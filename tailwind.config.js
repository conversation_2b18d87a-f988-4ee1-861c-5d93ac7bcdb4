/** @type {import('tailwindcss').Config} */
export default {
  darkMode: ['class'],
  content: ['./components/**/*.{ts,tsx}', './src/**/*.{ts,tsx}'],
  theme: {
    extend: {
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      colors: {
        grayScale: {
          10: 'hsla(240, 2%, 92%, 1)',
          20: 'hsla(240, 3%, 84%, 1)',
          30: 'hsla(210, 2%, 68%, 1)',
          40: '#777DA4',
          50: '#5D6384',
          60: '#313439',
          90: 'hsla(216, 9%, 10%, 1)',
          400: '#ABABB5',
          600: '#111111',
        },

        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        chart: {
          1: 'hsl(var(--chart-1))',
          2: 'hsl(var(--chart-2))',
          3: 'hsl(var(--chart-3))',
          4: 'hsl(var(--chart-4))',
          5: 'hsl(var(--chart-5))',
        },
        danger: {
          secondary: 'hsla(1, 84%, 52%, 1)',
        },
        text: {
          brand: {
            violet: {
              Default: 'hsla(262, 68%, 35%, 1)',
            },
          },
          neutral: {
            Default: 'hsla(0, 0%, 19%, 1)',
            tertiary: 'hsla(0, 0%, 46%, 1)',
          },
          Default: 'hsla(0,0%,12%,1)',
          tertiary: 'hsla(0, 0%, 70%, 1)',
          danger: 'hsla(1, 88%, 40%, 1)',
        },
        background: {
          brand: {
            violet: {
              Default: 'hsla(261, 68%, 47%, 1)',
              hover: 'hsla(261, 68%, 43%, 1)',
              tertiary: 'hsla(261, 63%, 95%, 1)',
            },
            teal: 'hsla(182, 96%, 39%, 1)',
          },
          secondary: {
            hover: '#E6E6E6',
          },
          tertiary: {
            neutral: 'hsla(0, 0%, 89%, 1)',
          },
          default: {
            hover: 'hsla(0, 0%, 96%, 1)',
          },
          danger: {
            default: 'hsla(1, 84%, 52%, 1)',
          },
        },
        border: {
          Default: 'hsla(0, 0%, 85%, 1)',
          brand: {
            violet: {
              Default: 'hsla(262, 68%, 35%, 1)',
            },
            teal: 'hsla(182, 96%, 39%, 1)',
          },
        },
        brand: {
          violet: 'hsla(262, 68%, 35%, 1)',
          teal: {
            Default: 'hsla(182, 96%, 39%, 1)',
            secondary: 'hsla(182, 96%, 19%, 1)',
          },
        },
        danger: 'hsla(1, 84%, 52%, 1)',
        tertiary: {
          neutral: 'hsla(0, 0%, 70%, 1)',
        },
      },
      keyframes: {
        'accordion-down': {
          from: {
            height: '0',
          },
          to: {
            height: 'var(--radix-accordion-content-height)',
          },
        },
        'accordion-up': {
          from: {
            height: 'var(--radix-accordion-content-height)',
          },
          to: {
            height: '0',
          },
        },
      },
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
      },
    },
  },
  plugins: [require('tailwindcss-animate')],
};
