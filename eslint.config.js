import js from '@eslint/js';
import globals from 'globals';
import reactHooks from 'eslint-plugin-react-hooks';
import reactRefresh from 'eslint-plugin-react-refresh';
// import react from "eslint-plugin-react";
import * as tseslint from 'typescript-eslint';
import prettierPlugin from 'eslint-plugin-prettier';

const baseConfig = {
  files: ['**/*.{js,jsx,ts,tsx}'],
  ...js.configs.recommended,
  languageOptions: {
    ecmaVersion: 2020,
    sourceType: 'module',
    globals: {
      ...globals.browser,
      ...globals.es2020,
    },
    parser: tseslint.parser,
    parserOptions: {
      ecmaVersion: 2020,
      sourceType: 'module',
    },
  },
  plugins: {
    prettier: prettierPlugin,
    '@typescript-eslint': tseslint.plugin,
    'react-hooks': reactHooks,
    'react-refresh': reactRefresh,
    // react: react,
  },
  rules: {
    ...reactHooks.configs.recommended.rules,
    'prettier/prettier': 'warn',
    'arrow-body-style': 'off',
    'prefer-arrow-callback': 'off',
    'no-console': 'warn',
    '@typescript-eslint/no-explicit-any': 'off',
    'no-unused-vars': 'off',
    '@typescript-eslint/no-unused-vars': 'off',
    '@typescript-eslint/no-empty-object-type': 'off',
    '@typescript-eslint/no-unused-expressions': 'off',
    'react-refresh/only-export-components': 'off',
    // "react-recommended/jsx-key": "error",
    '@typescript-eslint/explicit-function-return-type': [
      'off',
      {
        allowExpressions: true,
        allowConciseArrowFunctionExpressionsStartingWithVoid: true,
      },
    ],
    '@typescript-eslint/ban-ts-comment': 'off',
  },
};

export default tseslint.config(
  {
    ignores: ['dist/**', 'node_modules/**'],
  },
  baseConfig
);
