{"name": "party-track-app", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "host": "vite --host", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "prettier": "prettier --check '**/**/*.{js,jsx,ts,tsx,css}'", "prettier:fix": "prettier --write '**/**/*.{js,jsx,ts,tsx,css}'", "test": "vitest --run", "coverage": "vitest run --coverage", "testbuild": "npm run test && npm run build", "prepare": "husky install"}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx}": ["eslint .", "prettier --write"]}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.2", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.3", "@reduxjs/toolkit": "^2.2.8", "@tanstack/react-table": "^8.20.5", "@types/react-select": "^5.0.1", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "dayjs": "^1.11.13", "embla-carousel-react": "^8.3.0", "input-otp": "^1.2.4", "lodash": "^4.17.21", "lucide-react": "^0.447.0", "next-themes": "^0.3.0", "react": "^18.3.1", "react-day-picker": "^9.6.7", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.53.0", "react-idle-timer": "^5.7.2", "react-input-mask": "^2.0.4", "react-international-phone": "^4.3.0", "react-number-format": "^5.4.3", "react-redux": "^9.1.2", "react-resizable-panels": "^2.1.4", "react-router-dom": "^6.26.2", "react-select": "^5.8.3", "react-text-mask": "^5.5.0", "recharts": "^2.12.7", "sonner": "^1.5.0", "tailwind-merge": "^2.5.3", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/js": "^9.13.0", "@testing-library/jest-dom": "^6.5.0", "@testing-library/react": "^16.0.1", "@types/lodash": "^4.17.10", "@types/node": "^22.7.4", "@types/react": "^18.3.10", "@types/react-dom": "^18.3.0", "@types/react-helmet": "^6.1.11", "@types/react-input-mask": "^3.0.6", "@types/react-text-mask": "^5.4.14", "@vitejs/plugin-react-swc": "^3.5.0", "@vitest/coverage-v8": "^2.1.2", "autoprefixer": "^10.4.20", "eslint": "^9.13.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.37.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.12", "globals": "^15.9.0", "husky": "^8.0.0", "jsdom": "^25.0.1", "lint-staged": "^15.2.10", "postcss": "^8.4.47", "prettier": "^3.3.3", "tailwindcss": "^3.4.13", "typescript": "^5.6.3", "typescript-eslint": "^8.11.0", "vite": "^5.4.8", "vitest": "^2.1.2"}}