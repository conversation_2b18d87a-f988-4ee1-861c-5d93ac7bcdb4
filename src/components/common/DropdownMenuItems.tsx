import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';
import { ChevronDown } from 'lucide-react';
import React, { useState } from 'react';
import { Button } from '../ui/button';

interface DropdownMenuItemProps {
  label: string;
  onClick?: (id: number) => void;
  icon?: React.ReactNode;
  className?: string;
  disabled?: boolean;
}

interface DropdownMenuItemsProps {
  menuItems: DropdownMenuItemProps[];
  className?: string;
  icon?: React.ReactNode;
  label: string;
  triggerClassName?: string;
  disabled?: boolean;
}

const DropdownMenuItems = ({
  menuItems,
  icon,
  className,
  label,
  triggerClassName,
  disabled,
}: DropdownMenuItemsProps) => {
  const [isOpen, setIsOpen] = useState(false);
  return (
    <div className="w-full flex flex-row gap-x-2 justify-center items-center">
      <DropdownMenu onOpenChange={(open) => setIsOpen(open)}>
        <DropdownMenuTrigger
          asChild
          className={triggerClassName}
          disabled={disabled}
        >
          <Button
            variant={'outline'}
            className={cn(
              'text-md flex flex-row items-center gap-2 px-3',
              disabled ? 'bg-[#F5F5F5] hover:bg-[#F5F5F5]' : ''
            )}
            disabled={disabled}
          >
            {icon && icon}
            <span>{label}</span>
            <span
              className={cn(
                'transition-transform duration-100 mt-0.5',
                isOpen ? 'rotate-180' : 'rotate-0'
              )}
            >
              <ChevronDown className="w-4 h-4" />
            </span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          align="end"
          className={cn('rounded-xl z-10', className)}
        >
          {menuItems?.map((item, index) => (
            <DropdownMenuItem
              key={`${item.label}-${index}`}
              disabled={item.disabled}
              className={cn(item.className)}
              onClick={() => {
                if (item.onClick && !item.disabled) {
                  item.onClick(index);
                }
              }}
            >
              <div className="flex flex-row gap-3 items-center">
                {item.icon && item.icon}
                <span>{item.label}</span>
              </div>
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

export default DropdownMenuItems;
