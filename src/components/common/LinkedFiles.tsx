import EditPencilIcon from '@/assets/icons/EditPencilIcon';
import FileAttachmentIcon from '@/assets/icons/FileAttachmentIcon';
import AppButton from '@/components/common/app-button';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import DataTable from '@/components/common/data-tables';
import UploadFile from '@/components/common/UploadFile';
import { UseToast } from '@/components/ui/toast/ToastContainer';
import { ACCEPTED_FILE_TYPES, getFileIcon } from '@/constants/item-constants';
import { useDownloadFile } from '@/hooks/useDownloadFile';
import { formatDate } from '@/lib/utils';
import { LinkedFilesTypes } from '@/types/item.types';
import { ColumnDef } from '@tanstack/react-table';
import { CircleCheck, Download, Trash2 } from 'lucide-react';
import { useCallback, useMemo, useState } from 'react';
import ActionMenuItem, {
  MenuListTypes,
} from '../modules/items/item-details/linked-files/ActionColumn';

type ReuploadTypes = {
  id: number | null;
  state: boolean;
};

interface LinkedFilesProps {
  data: any;
  uploadUrl: string;
  downloadUrl: (id: number) => string;
  reuploadUrl?: string;
  deleteFile: (fileId: number) => any;
  defaultFile: (fileId: number) => any;
  isFetching: boolean;
  isDefaultLoading: boolean;
  isDeleteLoading: boolean;
  refetch: any;
  isReupload?: ReuploadTypes;
  isReuploadBtn?: boolean;
  setIsReupload?: React.Dispatch<React.SetStateAction<ReuploadTypes>>;
}

const LinkedFiles = ({
  data,
  deleteFile,
  defaultFile,
  isFetching,
  isDefaultLoading,
  isDeleteLoading,
  refetch,
  uploadUrl,
  reuploadUrl = '',
  downloadUrl,
  isReupload,
  setIsReupload,
  isReuploadBtn = true,
}: LinkedFilesProps) => {
  // states
  const [fileId, setFileId] = useState<number | null>(null);
  const [openDialog, setOpenDialog] = useState(false);

  const [openActionDialog, setOpenActionDialog] = useState({
    state: false,
    action: '',
  });

  // hooks
  const toast = UseToast();
  const { downloadFile, isLoading: isDownloading } = useDownloadFile();

  // Columns for the data table
  const columns: ColumnDef<LinkedFilesTypes>[] = [
    {
      accessorKey: 'icon',
      header: 'Icon',
      size: 50,
      cell: ({ row }) => {
        const fileName: string = row.original.fileName;
        return (
          <div className="flex justify-center">
            {getFileIcon(fileName ?? '')}
          </div>
        );
      },
    },
    {
      accessorKey: 'fileName',
      header: 'File Name',
      maxSize: 180,
    },
    {
      accessorKey: 'sequence',
      header: 'Order',
      size: 90,
      cell: ({ row }) => {
        return (
          <div>
            {row.original.sequence === 1 ? 'Default' : row.original.sequence}
          </div>
        );
      },
    },
    {
      accessorKey: 'createdDate',
      header: 'Date Created',
      size: 180,
      cell: ({ row }) =>
        formatDate(row?.original?.createdAt, 'MM/DD/YYYY hh:MM A'),
    },
    {
      accessorKey: 'fileSizeText',
      header: 'Size',
      size: 90,
    },
  ];

  // Open/Close Dialog handlers
  const onCloseDialog = useCallback(() => {
    setOpenActionDialog({ state: false, action: '' });
  }, []);

  const handleClose = () => {
    if (isReupload) {
      setIsReupload?.({ state: false, id: null });
    }
    setOpenDialog(false);
  };

  const handleFileAction = useCallback(
    (id: number, action: 'delete' | 'default') => {
      setFileId(id);
      setOpenActionDialog({ state: true, action });
    },
    []
  );

  // Action handling functions
  const handleAction = useCallback(
    async (action: 'delete' | 'default') => {
      if (!fileId) return;
      const actionFn = action === 'delete' ? deleteFile : defaultFile;
      try {
        const response = await actionFn(fileId).unwrap();
        toast.success(response.message);
        setFileId(null);
        onCloseDialog();
        refetch();
      } catch (error) {
        // Handle error
      }
    },
    [fileId, deleteFile, defaultFile, toast, onCloseDialog, refetch]
  );

  const handleDownload = useCallback(
    (id: number) => {
      const response = downloadFile({
        url: downloadUrl(id),
      });
      toast.promise(response, {
        loading: 'Downloading file...',
        success: 'File downloaded successfully.',
        error: 'Failed to download file',
      });
    },
    [downloadFile, downloadUrl, toast]
  );

  // Action column configuration
  const dropdownMenuItem = useMemo(() => {
    const menuList: MenuListTypes[] = [
      {
        label: 'Make Default',
        onClick: (id: number) => handleFileAction?.(id, 'default'),
        icon: <CircleCheck className="w-5 h-5 text-text-default" />,
        className: 'text-base text-text-default',
      },
      {
        label: 'Download',
        onClick: (id: number) => handleDownload(id),
        disabled: isDownloading,
        icon: <Download className="w-5 h-5 text-text-default" />,
        className: 'text-base text-text-default',
      },
      {
        label: 'Delete',
        onClick: (id: number) => handleFileAction?.(id, 'delete'),
        icon: <Trash2 className="w-5 h-5" />,
        className: 'text-base text-text-danger',
      },
    ];

    return menuList?.filter((item) => item?.onClick);
  }, [handleDownload, handleFileAction, isDownloading]);

  const ActionColumn: ColumnDef<LinkedFilesTypes> = useMemo(
    () => ({
      id: 'action',
      header: 'Actions',
      size: 80,
      cell: ({ row }) => {
        const value = row.original?.id;
        return (
          <ActionMenuItem
            customEdit={
              isReuploadBtn && (
                <AppButton
                  onClick={() => {
                    setIsReupload?.({ state: true, id: value });
                    setOpenDialog(true);
                  }}
                  variant="neutral"
                  label="Re-Upload"
                  icon={EditPencilIcon}
                />
              )
            }
            dropdownMenuItem={dropdownMenuItem}
            rowId={value}
          />
        );
      },
    }),
    [dropdownMenuItem, isReuploadBtn, setIsReupload]
  );

  return (
    <div>
      {/* Add File Button */}
      <div className="flex flex-row justify-end">
        <AppButton
          label="Add File"
          variant="neutral"
          className="bg-brand-teal-Default hover:bg-brand-teal-hover border-border-brand-teal-Default text-white mb-2"
          iconClassName="text-white"
          icon={FileAttachmentIcon}
          onClick={() => setOpenDialog(true)}
        />
      </div>
      <div className="grid grid-cols-1">
        <DataTable
          columns={[...columns, ActionColumn]}
          data={data ?? []}
          enablePagination={false}
          isLoading={isFetching}
          tableClassName="max-h-[70vh] overflow-auto"
        />
      </div>
      {/* Confirmation Modal for actions */}
      <AppConfirmationModal
        title={openActionDialog.action === 'delete' ? 'Delete' : 'Make Default'}
        description={
          openActionDialog.action === 'delete'
            ? 'Are you sure you want to delete this file?'
            : 'Are you sure you want to set this file as default?'
        }
        open={openActionDialog.state}
        onOpenChange={onCloseDialog}
        handleCancel={onCloseDialog}
        disabled={isDeleteLoading || isDefaultLoading}
        isLoading={isDeleteLoading || isDefaultLoading}
        handleSubmit={() =>
          handleAction(openActionDialog.action as 'delete' | 'default')
        }
      />

      {/* File Upload Component */}
      <UploadFile
        acceptedFileTypes={ACCEPTED_FILE_TYPES}
        onUpload={refetch}
        isModalOpen={openDialog}
        onClose={handleClose}
        url={isReupload?.state && isReupload?.id ? reuploadUrl : uploadUrl}
        dragDropText="Drag & Drop or upload to add a document as a Linked File to this item."
      />
    </div>
  );
};

export default LinkedFiles;
