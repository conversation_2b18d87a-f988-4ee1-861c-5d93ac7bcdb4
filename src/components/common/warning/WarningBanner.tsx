import { cn } from '@/lib/utils';
import { AlertTriangle } from 'lucide-react';

const WarningBanner = ({
  title = 'Warning:',
  message,
  titleClassName,
  messageClassName,
}: {
  title?: string;
  message: string;
  titleClassName?: string;
  messageClassName?: string;
}) => {
  return (
    <div className="w-full bg-yellow-100 border-l-4 border-yellow-500 p-4 flex items-center">
      <div className="mr-4">
        <AlertTriangle className="text-yellow-600 w-6 h-6" />
      </div>
      <div className="flex gap-x-2">
        <p className={cn('text-yellow-800 font-semibold', titleClassName)}>
          {title}
        </p>
        <p className={cn('text-yellow-700 text-sm mt-1', messageClassName)}>
          {message}
        </p>
      </div>
    </div>
  );
};

export default WarningBanner;
