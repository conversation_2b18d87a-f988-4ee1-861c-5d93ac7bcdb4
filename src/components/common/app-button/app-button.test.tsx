import '@testing-library/jest-dom';
import { fireEvent, render, screen } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import AppButton from '.';

describe('AppButton', () => {
  const mockOnClick = vi.fn();

  it('renders without crashing', () => {
    render(<AppButton label="Test Button" />);
    expect(screen.getByText('Test Button')).toBeInTheDocument();
  });

  it('renders the label correctly', () => {
    render(<AppButton label="Click Me" />);
    expect(screen.getByText('Click Me')).toBeInTheDocument();
  });

  it('renders an icon before the label', () => {
    const MockIcon = () => <div data-testid="mock-icon">Icon</div>;
    render(
      <AppButton label="With Icon" icon={MockIcon} iconPosition="before" />
    );
    expect(screen.getByTestId('mock-icon')).toBeInTheDocument();
    expect(screen.getByText('With Icon')).toBeInTheDocument();
  });

  it('renders an icon after the label', () => {
    const MockIcon = () => <div data-testid="mock-icon">Icon</div>;
    render(<AppButton label="With Icon" icon={MockIcon} iconPosition="next" />);
    expect(screen.getByText('With Icon')).toBeInTheDocument();
    expect(screen.getByTestId('mock-icon')).toBeInTheDocument();
  });

  it('calls onClick when clicked', () => {
    render(<AppButton label="Click Me" onClick={mockOnClick} />);
    fireEvent.click(screen.getByText('Click Me'));
    expect(mockOnClick).toHaveBeenCalledTimes(1);
  });

  it('applies custom class names', () => {
    render(<AppButton label="Styled Button" className="custom-class" />);
    const button = screen.getByText('Styled Button').closest('button');
    expect(button).toHaveClass('custom-class');
  });
});
