import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import React, { ReactNode } from 'react';
import AppSpinner from '../app-spinner';
import TooltipWidget from '../tooltip-widget';

interface AppButtonProps {
  icon?: React.FC<React.SVGProps<SVGSVGElement>>;
  label: ReactNode;
  variant?: 'primary' | 'neutral' | 'danger';
  className?: string;
  onClick?: () => void;
  iconClassName?: string; // Added this prop for icon styling
  iconPosition?: 'before' | 'next';
  disabled?: boolean;
  isLoading?: boolean;
  spinnerClass?: string;
  type?: 'submit' | 'reset' | 'button';
  tooltip?: string;
  side?: 'top' | 'right' | 'bottom' | 'left';
}

const AppButton = ({
  icon: Icon,
  label,
  className = '',
  onClick,
  variant = 'primary',
  iconPosition = 'before',
  iconClassName = 'w-5 h-5', // Default empty string if no class is passed
  disabled = false,
  isLoading,
  spinnerClass,
  type = 'submit',
  tooltip,
  side,
}: AppButtonProps) => {
  const baseClasses = 'rounded-lg text-base';
  const variantClasses = {
    primary:
      'bg-background-brand-violet-Default text-white hover:bg-background-brand-violet-hover border-border-brand-violet-Default',
    neutral: 'bg-white text-text-Default border-border-Default border-[1px]',
    danger:
      'bg-background-danger-default p-3 hover:bg-background-danger-default text-white border-border-Default border-[1px]',
  };

  const disabledClasses = disabled
    ? 'bg-tertiary-neutral hover:bg-tertiary-neutral'
    : '';

  return (
    <TooltipWidget
      content={tooltip}
      side={side}
      children={
        <Button
          className={cn(
            baseClasses,
            variantClasses[variant],
            className,
            disabledClasses
          )}
          type={type}
          disabled={disabled || isLoading}
          onClick={onClick}
        >
          <div className="flex items-center gap-x-2">
            {Icon && iconPosition === 'before' && !isLoading && (
              <Icon className={cn(iconClassName)} />
            )}
            {isLoading && (
              <AppSpinner className={cn('h-4 w-4', spinnerClass)} />
            )}
            {label && label}
            {Icon && iconPosition === 'next' && !isLoading && (
              <Icon className={iconClassName} />
            )}
          </div>
        </Button>
      }
    />
  );
};

export default AppButton;
