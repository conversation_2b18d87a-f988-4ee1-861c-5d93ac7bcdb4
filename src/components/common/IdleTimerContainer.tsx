import { Progress } from '@/components/ui/progress';
import { ROUTES } from '@/constants/routes-constants';
import { getStorageValue } from '@/lib/utils';
import { logoutUser } from '@/redux/features/auth/authSlice';
import { useLogoutMutation } from '@/redux/features/tenant/tenant.api';
import { AlarmClock } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import { Helmet, HelmetProvider } from 'react-helmet-async';
import { useIdleTimer } from 'react-idle-timer';
import { useNavigate } from 'react-router-dom';
import AppButton from './app-button';
import AppDialog from './app-dialog';
import { useDispatch } from 'react-redux';

const IdleTimerContainer: React.FC = () => {
  const [isIdle, setIsIdle] = useState<boolean>(false);
  const [openDialog, setOpenDialog] = useState<boolean>(false);
  const [remainingTime, setRemainingTime] = useState<number>(60); // 60 seconds countdown
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const isTokenPresent: string | null = localStorage.getItem('accessToken');
  const [logout] = useLogoutMutation();

  const idleTimeout: number =
    Number(getStorageValue('idleLogoutMinutes')) || 120; // 120 min
  const userIdleTime = idleTimeout * 60 * 1000;
  const isproductionEnv = process.env.NODE_ENV === 'production';

  const handleOnIdle = () => {
    if (isproductionEnv) {
      setIsIdle(true);
      setOpenDialog(true);
    }
  };

  const handleContinue = useCallback(() => {
    const currentTimestamp = new Date().toISOString();
    localStorage.setItem('userIsActive', currentTimestamp);
    const switchEvent = new CustomEvent('userStaysActive', {
      detail: currentTimestamp,
    });
    window.dispatchEvent(switchEvent);
    setOpenDialog(false);
    setIsIdle(false);
    setRemainingTime(60);
  }, []);

  const handleLogout = useCallback(async () => {
    // redirect to login page
    setOpenDialog(false);
    setIsIdle(false);
    if (isTokenPresent) {
      await logout(null).unwrap();
      dispatch(logoutUser());
      navigate(ROUTES.LOGIN);
    }
  }, [dispatch, isTokenPresent, logout, navigate]);

  useEffect(() => {
    let intervalId: NodeJS.Timeout;
    if (openDialog) {
      // Start the countdown interval when the dialog is open
      intervalId = setInterval(() => {
        setRemainingTime((prevRemainingTime) => {
          if (prevRemainingTime === 0) {
            clearInterval(intervalId);
            handleLogout();
            return prevRemainingTime;
          }
          return prevRemainingTime - 1;
        });
      }, 1000);
    }

    return () => {
      // Clear the interval when the component unmounts or when the dialog is closed
      clearInterval(intervalId);
    };
  }, [handleLogout, openDialog]);

  useIdleTimer({
    timeout: userIdleTime,
    onIdle: handleOnIdle,
    debounce: 500,
    crossTab: true,
  });

  useEffect(() => {
    const handleActivity = () => {
      setOpenDialog(false);
      setIsIdle(false);
      setRemainingTime(60);
    };

    window.addEventListener('userStaysActive', handleActivity);
    window.addEventListener('storage', (event) => {
      if (event.key === 'userIsActive') {
        handleActivity();
      }
    });

    return () => {
      window.removeEventListener('userStaysActive', handleActivity);
      window.removeEventListener('storage', handleActivity);
    };
  }, [openDialog]);

  const progress: number = Math.round((remainingTime / 60) * 100); // Round progress to the nearest integer

  return (
    <HelmetProvider>
      <div>
        {/* Use Helmet to change the document title */}
        <Helmet>
          <title>
            {isIdle
              ? `${remainingTime} sec - Inactivity Warning`
              : 'Party Track'}
          </title>
        </Helmet>

        {isIdle && (
          <AppDialog
            open={openDialog}
            footer={
              <div className="text-center w-full flex gap-2 justify-center">
                <AppButton
                  label="Log Out"
                  variant="neutral"
                  onClick={handleLogout}
                />

                <AppButton label="Stay In" onClick={handleContinue} />
              </div>
            }
          >
            <>
              <div className="px-4 py-3">
                <div>
                  <div className="flex items-center pb-4 justify-center">
                    <AlarmClock
                      size={38}
                      className="text-primary text-center "
                    />
                  </div>
                  <h1 className="text-bold text-lg text-center text-gray-400">
                    Session Timeout Warning
                  </h1>
                  <p className="text-bold text-3xl text-center pb-3">
                    Are you still working?
                  </p>
                </div>
                <div className="py-3">
                  <Progress
                    value={progress}
                    className="w-[100%] h-2"
                    bgColor={
                      progress < 18
                        ? '#ff6347'
                        : progress < 50
                          ? '#ffd700'
                          : '#6127CA'
                    }
                    style={{
                      transition: 'width 0.3s ease-in-out',
                    }}
                  />
                </div>
                <div className="text-center italic text-sm">
                  You will be logged out in <strong>{remainingTime}</strong>{' '}
                  seconds
                </div>
              </div>
            </>
          </AppDialog>
        )}
      </div>
    </HelmetProvider>
  );
};

export default IdleTimerContainer;
