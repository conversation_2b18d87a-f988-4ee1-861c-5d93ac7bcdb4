import SearchIcon from '@/assets/icons/SearchIcon';
import Input<PERSON>ield from '@/components/forms/input-field';
import PhoneInputWidget from '@/components/forms/phone-input-mask';
import SelectDropDown from '@/components/forms/select-dropdown';
import { CUSTOMER_API_ROUTES } from '@/constants/api-constants';
import { CustomerSearchType, statusList } from '@/constants/common-constants';
import { cn, formatPhoneNumber } from '@/lib/utils';
import { CustomerDetailTypes } from '@/types/customer.types';
import { ColumnDef } from '@tanstack/react-table';
import { debounce } from 'lodash';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import AppButton from '../../app-button';
import AppDataTable from '../../app-data-table';
import AppTableContextProvider from '../../app-data-table/AppTableContext';
import CustomDialog from '../../dialog';

interface CustomerLookupTypes {
  handleOk: (value: CustomerDetailTypes) => void;
  className?: string;
  searchFilter?: string;
  disabled?: boolean;
  isOpen?: boolean;
  onCancel?: () => void;
  label?: string;
  tooltip?: string;
}
const CustomerLookup = ({
  handleOk,
  className,
  searchFilter,
  disabled,
  isOpen,
  onCancel,
  label = 'Customer',
  tooltip,
}: CustomerLookupTypes) => {
  const [openLookup, setOpenLookup] = useState<boolean>(false);
  const [selectedCustomer, setSelectedCustomer] = useState<
    CustomerDetailTypes[]
  >([]);
  const [filter, setFilter] = useState<any>();

  const form = useForm({
    defaultValues: { isActive: '', type: 'name', name: '', tel1: '' },
    mode: 'onChange',
  });
  const { errors } = form.formState;

  const isActive = form.watch('isActive');
  const name = form.watch('name');
  const phone = form.watch('tel1');
  const isPhone = form.watch('type') === 'tel1';

  // Create the debounced filter function
  const debouncedSearch = useMemo(
    () =>
      debounce(() => {
        const filterFileds = [
          { name: 'isactive', value: isActive, operator: 'equals' },
          {
            name: 'full_name',
            value: name,
            operator: 'contains',
          },
          {
            name: 'tel1',
            value: phone.replace(/^\+1/, ''),
            operator: 'contains',
          },
        ];
        if (!errors?.tel1?.type) {
          setFilter(filterFileds);
        }
      }, 1000),
    [errors, isActive, name, phone]
  );

  // Trigger debounced search whenever the filter parameters change
  useEffect(() => {
    debouncedSearch();
    // Cleanup the debounce on unmount or when parameters change
    return () => {
      debouncedSearch.cancel();
    };
  }, [isActive, debouncedSearch]);

  const toggleOpenLookup = useCallback(() => {
    setOpenLookup((prev) => !prev);
  }, []);

  useEffect(() => {
    if (isOpen) {
      setOpenLookup(true);
      form.setValue('name', searchFilter ?? '');
    }
  }, [form, isOpen, searchFilter]);

  // Memoized table columns
  const columns: ColumnDef<CustomerDetailTypes>[] = useMemo(
    () => [
      {
        accessorKey: 'full_name',
        header: 'Customer',
        size: 240,
        enableSorting: true,
      },
      {
        accessorKey: 'custtype',
        header: 'Customer Type',
        enableSorting: true,
        size: 200,
      },
      {
        accessorKey: 'tel1',
        header: 'Phone',
        size: 170,
        enableSorting: true,
        cell: (info) => {
          const contact = info?.getValue() as string;
          return contact ? formatPhoneNumber(contact) : '';
        },
      },
      { accessorKey: 'city', header: 'City', size: 130, enableSorting: true },
      { accessorKey: 'state', header: 'State', size: 110, enableSorting: true },
    ],
    []
  );

  const handleTypeChange = useCallback(
    (value: string) => {
      form.setValue('type', value);
      form.setValue('name', '');
      form.setValue('tel1', '');
    },
    [form]
  );

  // handle Cancel
  const handleCancel = useCallback(() => {
    form.reset();
    toggleOpenLookup();
    setSelectedCustomer([]);
    onCancel?.();
  }, [form, onCancel, toggleOpenLookup]);

  // handle on click OK
  const handleOnClickOk = useCallback(() => {
    handleOk(selectedCustomer[0]);
    setSelectedCustomer([]);
    toggleOpenLookup();
    form.reset();
  }, [form, handleOk, selectedCustomer, toggleOpenLookup]);

  return (
    <>
      <AppButton
        type="button"
        label={label}
        icon={SearchIcon}
        className={cn(
          'bg-white border-[1px] hover:bg-white text-text-Default border-border-Default',
          className
        )}
        onClick={toggleOpenLookup}
        disabled={disabled}
        tooltip={tooltip}
      />
      {openLookup && (
        <CustomDialog
          open={openLookup}
          onOpenChange={handleCancel}
          title="Customer Search"
          description=""
          className={cn('min-w-[80%] md:min-w-[80%] 2xl:min-w-[65%]')}
          contentClassName="h-[490px] 2xl:h-[570px] pb-0"
        >
          <div className="grid grid-cols-1 px-6">
            <div className="flex items-start justify-end gap-3 flex-wrap w-full">
              <SelectDropDown
                name="isActive"
                label="Status"
                form={form}
                optionsList={statusList}
                className="w-full md:w-40"
                placeholder="Status"
                allowClear
              />
              <SelectDropDown
                name="type"
                form={form}
                label="Type"
                optionsList={CustomerSearchType}
                className="w-full md:w-40"
                placeholder="Type"
                onChange={handleTypeChange}
              />

              {isPhone ? (
                <PhoneInputWidget
                  label="Phone"
                  className="w-48"
                  form={form}
                  name="tel1"
                />
              ) : (
                <InputField
                  name="name"
                  label="Name"
                  form={form}
                  className="w-48"
                  placeholder="Name"
                />
              )}
            </div>
            <AppTableContextProvider
              defaultSort={[{ id: 'first_name', desc: true }]}
            >
              <AppDataTable
                heading=" "
                columns={columns}
                url={CUSTOMER_API_ROUTES.ALL}
                enableSearch={false}
                enablePagination
                filter={filter}
                enableRowSelection
                onRowsSelected={setSelectedCustomer}
                tableClassName="md:max-h-[280px] 2xl:max-h-[350px] overflow-y-auto"
                bindingKey="customer_id"
                loaderRows={8}
              />
            </AppTableContextProvider>

            <div className="flex justify-end gap-4 fixed bottom-0 right-6 bg-white pb-4">
              <AppButton
                onClick={handleOnClickOk}
                label="OK"
                disabled={!selectedCustomer?.length}
                className="w-28"
              />

              <AppButton
                onClick={handleCancel}
                label="Cancel"
                className="w-28"
                variant="neutral"
              />
            </div>
          </div>
        </CustomDialog>
      )}
    </>
  );
};

export default CustomerLookup;
