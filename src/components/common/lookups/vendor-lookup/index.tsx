import { VendorListTypes } from '@/types/vendor.types';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import CustomDialog from '../../dialog';
import AppButton from '../../app-button';
import { cn, formatPhoneNumber } from '@/lib/utils';
import SearchIcon from '@/assets/icons/SearchIcon';
import SelectDropDown from '@/components/forms/select-dropdown';
import { statusList, VendorSearchType } from '@/constants/common-constants';
import AppTableContextProvider from '../../app-data-table/AppTableContext';
import AppDataTable from '../../app-data-table';
import { ColumnDef } from '@tanstack/react-table';
import { VENDORS_API_ROUTES } from '@/constants/api-constants';
import debounce from 'lodash/debounce';
import PhoneInputWidget from '@/components/forms/phone-input-mask';
import InputField from '@/components/forms/input-field';

interface VendorLookupBase {
  handleOk: (value: VendorListTypes) => void;
}

type VendorLookupTypes = VendorLookupBase &
  Partial<{
    className: string;
    searchFilter: string;
    disabled: boolean;
    isOpen: boolean;
    onCancel: () => void;
  }>;

const VendorLookup = ({
  handleOk,
  className,
  searchFilter,
  disabled,
  isOpen,
  onCancel,
}: VendorLookupTypes) => {
  const [openLookup, setOpenLookup] = useState<boolean>(false);
  const [selectedVendor, setSelectedVendor] = useState<VendorListTypes[]>([]);
  const [filter, setFilter] = useState<any>();

  const form = useForm({
    defaultValues: { isActive: '', type: 'name', name: '', tel1: '' },
    mode: 'onChange',
  });
  const { errors } = form.formState;

  const isActive = form.watch('isActive');
  const name = form.watch('name');
  const phone = form.watch('tel1');
  const type = form.watch('type');
  const isPhone = type === 'tel1';

  const toggleOpenLookup = useCallback(() => {
    setOpenLookup((prev) => !prev);
  }, []);

  const handleCancel = useCallback(() => {
    form.reset();
    setSelectedVendor([]);
    toggleOpenLookup();
    onCancel?.();
  }, [form, onCancel, toggleOpenLookup]);

  const handleTypeChange = useCallback(
    (value: string) => {
      form.setValue('type', value);
      form.setValue('name', '');
      form.setValue('tel1', '');
    },
    [form]
  );

  const handleOnClickOk = useCallback(() => {
    handleOk(selectedVendor[0]);
    setSelectedVendor([]);
    toggleOpenLookup();
    form.reset();
  }, [form, handleOk, selectedVendor, toggleOpenLookup]);

  // Create the debounced filter function
  const debouncedSearch = useMemo(
    () =>
      debounce(() => {
        const filterFileds = [
          { name: 'isactive', value: isActive, operator: 'equals' },
          {
            name: 'vendorName',
            value: name,
            operator: 'contains',
          },
          {
            name: 'tel1',
            value: phone.replace(/^\+1/, ''),
            operator: 'contains',
          },
        ];
        if (!errors?.tel1?.type) {
          setFilter(filterFileds);
        }
      }, 1000),
    [errors, isActive, name, phone]
  );

  // Trigger debounced search whenever the filter parameters change
  useEffect(() => {
    debouncedSearch();
    // Cleanup the debounce on unmount or when parameters change
    return () => {
      debouncedSearch.cancel();
    };
  }, [debouncedSearch]);

  useEffect(() => {
    if (isOpen) {
      setOpenLookup(true);
      form.setValue('name', searchFilter ?? '');
    }
  }, [form, isOpen, searchFilter]);

  // Memoized table columns
  const columns: ColumnDef<VendorListTypes>[] = useMemo(
    () => [
      {
        accessorKey: 'vendorName',
        header: 'Vendor',
        size: 240,
        enableSorting: true,
      },
      {
        accessorKey: 'tel1',
        header: 'Phone',
        size: 170,
        enableSorting: true,
        cell: (info) => {
          const contact = info?.getValue() as string;
          return contact ? formatPhoneNumber(contact) : '';
        },
      },
      { accessorKey: 'city', header: 'City', size: 130, enableSorting: true },
      { accessorKey: 'state', header: 'State', size: 110, enableSorting: true },
    ],
    []
  );

  return (
    <>
      <AppButton
        label={''}
        icon={SearchIcon}
        className={cn(
          'bg-white border-[1px] hover:bg-white text-text-Default border-border-Default',
          className
        )}
        onClick={toggleOpenLookup}
        disabled={disabled}
      />
      {openLookup && (
        <CustomDialog
          open={openLookup}
          onOpenChange={handleCancel}
          title="Vendor Search"
          description=""
          className={cn('min-w-[80%] md:min-w-[80%] 2xl:min-w-[65%]')}
          contentClassName="h-[490px] 2xl:h-[570px] pb-0"
        >
          <>
            <div className="grid grid-cols-1 px-6">
              <div className="flex items-start justify-end gap-3 flex-wrap w-full">
                <SelectDropDown
                  name="isActive"
                  label="Status"
                  form={form}
                  optionsList={statusList}
                  className="w-full md:w-40"
                  placeholder="Status"
                  allowClear
                />
                <SelectDropDown
                  name="type"
                  form={form}
                  label="Type"
                  optionsList={VendorSearchType}
                  className="w-full md:w-40"
                  placeholder="Type"
                  onChange={handleTypeChange}
                  allowClear={false}
                />
                {isPhone ? (
                  <PhoneInputWidget
                    label="Phone"
                    className="w-48"
                    form={form}
                    name="tel1"
                  />
                ) : (
                  <InputField
                    name="name"
                    label="Name"
                    form={form}
                    className="w-48"
                    placeholder="Name"
                  />
                )}
              </div>
              <AppTableContextProvider
                defaultSort={[{ id: 'vendorName', desc: true }]}
              >
                <AppDataTable
                  heading=" "
                  columns={columns}
                  url={VENDORS_API_ROUTES.ALL}
                  enableSearch={false}
                  enablePagination
                  filter={filter}
                  enableRowSelection
                  onRowsSelected={setSelectedVendor}
                  tableClassName="md:max-h-[280px] 2xl:max-h-[350px] overflow-y-auto"
                  bindingKey="id"
                  loaderRows={8}
                />
              </AppTableContextProvider>
            </div>
            <div className="flex justify-end gap-4 fixed bottom-0 right-6 bg-white pb-4">
              <AppButton
                onClick={handleOnClickOk}
                label="OK"
                disabled={!selectedVendor?.length}
                className="w-28"
              />

              <AppButton
                onClick={handleCancel}
                label="Cancel"
                className="w-28"
                variant="neutral"
              />
            </div>
          </>
        </CustomDialog>
      )}
    </>
  );
};

export default VendorLookup;
