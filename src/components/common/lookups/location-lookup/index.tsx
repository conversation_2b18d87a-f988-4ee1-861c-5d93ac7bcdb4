import SearchIcon from '@/assets/icons/SearchIcon';
import Input<PERSON>ield from '@/components/forms/input-field';
import PhoneInputWidget from '@/components/forms/phone-input-mask';
import { DELIVERY_LOCATION_API_ROUTES } from '@/constants/api-constants';
import { cn, formatPhoneNumber } from '@/lib/utils';
import { DeliveryLocationFormType } from '@/types/list.types';
import { ColumnDef } from '@tanstack/react-table';
import { debounce } from 'lodash';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import AppButton from '../../app-button';
import AppDataTable from '../../app-data-table';
import AppTableContextProvider from '../../app-data-table/AppTableContext';
import CustomDialog from '../../dialog';

interface LocationLookupTyps {
  handleOk: (value: DeliveryLocationFormType) => void;
  className?: string;
  disabled: boolean;
}
const LocationLookup = ({
  handleOk,
  className,
  disabled,
}: LocationLookupTyps) => {
  const [openLookup, setOpenLookup] = useState<boolean>(false);
  const [selectedLocation, setSelectedLocation] = useState<
    DeliveryLocationFormType[]
  >([]);
  const [filter, setFilter] = useState<any>();

  const form = useForm({
    defaultValues: { location: '', town: '', phone: '' },
    mode: 'onChange',
  });
  const { errors } = form.formState;

  const location = form.watch('location');
  const town = form.watch('town');
  const phone = form.watch('phone');

  // Create the debounced filter function
  const debouncedSearch = useMemo(
    () =>
      debounce(() => {
        const filterFileds = [
          { name: 'town', value: town, operator: 'Contains' },
          {
            name: 'location',
            value: location,
            operator: 'Contains',
          },
          {
            name: 'phone',
            value: phone.replace(/^\+1/, ''),
            operator: 'Contains',
          },
        ];
        if (!errors?.phone?.type) {
          setFilter(filterFileds);
        }
      }, 500),
    [errors?.phone?.type, location, phone, town]
  );

  useEffect(() => {
    debouncedSearch();
    return () => {
      debouncedSearch.cancel();
    };
  }, [debouncedSearch]);

  const toggleOpenLookup = useCallback(() => {
    setOpenLookup((prev) => !prev);
  }, []);

  const columns: ColumnDef<DeliveryLocationFormType>[] = useMemo(
    () => [
      {
        accessorKey: 'location',
        header: 'Delivery Location',
        size: 200,
        enableSorting: true,
      },
      { accessorKey: 'town', header: 'Town', size: 130, enableSorting: true },
      {
        accessorKey: 'phone',
        header: 'Phone',
        size: 150,
        enableSorting: true,
        cell: ({ row }) => {
          const phone = row?.original?.phone;
          return phone ? formatPhoneNumber(phone) : '';
        },
      },
    ],
    []
  );

  // handle Cancel
  const handleCancel = useCallback(() => {
    toggleOpenLookup();
    form.reset();
    setSelectedLocation([]);
  }, [form, toggleOpenLookup]);

  // handle on click OK
  const handleOnClickOk = useCallback(() => {
    handleOk(selectedLocation[0]);
    toggleOpenLookup();
    setSelectedLocation([]);
    form.reset();
  }, [form, handleOk, selectedLocation, toggleOpenLookup]);

  return (
    <>
      <AppButton
        label={'Location'}
        icon={SearchIcon}
        className={cn(
          'bg-white border-[1px] hover:bg-white text-text-Default border-border-Default',
          className
        )}
        onClick={toggleOpenLookup}
        disabled={disabled}
      />
      {openLookup && (
        <CustomDialog
          open={openLookup}
          onOpenChange={handleCancel}
          title="Location Search"
          description=""
          className={cn('min-w-[70%] 2xl:min-w-[50%]')}
          contentClassName="h-[500px]  2xl:h-[620px]"
        >
          <div className="grid grid-cols-1 px-6">
            <div className="flex items-start justify-end gap-3 flex-wrap w-full">
              <InputField
                name="location"
                label="Location"
                form={form}
                className="w-48"
                placeholder="Enter Location"
              />
              <InputField
                name="town"
                label="Town"
                form={form}
                className="w-48"
                placeholder="Enter Town"
              />
              <PhoneInputWidget
                className="w-48"
                form={form}
                label="Phone"
                name="phone"
              />
            </div>
            <AppTableContextProvider
              defaultSort={[{ id: 'location', desc: true }]}
            >
              <AppDataTable
                heading=" "
                columns={columns}
                url={DELIVERY_LOCATION_API_ROUTES.ALL}
                enableSearch={false}
                enablePagination
                filter={filter}
                enableRowSelection
                onRowsSelected={setSelectedLocation}
                tableClassName="h-[200px] md:h-[280px] 2xl:h-[400px] overflow-auto min-w-[60%]"
                bindingKey="id"
              />
            </AppTableContextProvider>
            <div className="flex justify-end gap-4 fixed bottom-0 right-6 bg-white pb-4">
              <AppButton
                onClick={handleOnClickOk}
                label="OK"
                disabled={!selectedLocation?.length}
                className="w-28"
              />

              <AppButton
                onClick={handleCancel}
                label="Cancel"
                className="w-28"
                variant="neutral"
              />
            </div>
          </div>
        </CustomDialog>
      )}
    </>
  );
};

export default LocationLookup;
