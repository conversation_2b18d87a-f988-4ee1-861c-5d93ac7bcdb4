import { Calendar } from '@/components/ui/calendar';
import { cn } from '@/lib/utils';
import { AvailabilityEntry } from '@/types/common.types';
import dayjs from 'dayjs';
import { useState } from 'react';
import { Matcher } from 'react-day-picker';
import AppSpinner from '../app-spinner';

interface CalendarViewProps {
  data: AvailabilityEntry[];
  value: Date;
  onMonthChange: (value: Date) => void;
  onSelect: (day: Date | undefined | null) => void;
  className?: string;
  isLoading?: boolean;
  disabled?: Matcher | Matcher[] | undefined;
  dateKey?: string;
  labelKey?: string;
  timeZone?: string;
}
const CalendarView = ({
  data,
  value,
  onMonthChange,
  onSelect,
  className,
  isLoading,
  disabled,
  dateKey,
  labelKey,
}: CalendarViewProps) => {
  const [date, setDate] = useState<Date | null>(null);
  const startYear = new Date(1960, 0);
  const endYear = new Date(new Date().getFullYear() + 10, 0);

  const handleDayClick = (value: Date) => {
    const isSame = dayjs(date).isSame(value, 'day');
    setDate(value);
    !isSame && onSelect(value);
  };

  return (
    <div className="relative">
      <Calendar
        slotData={data}
        isCustomDay={true}
        showOutsideDays={false}
        month={value}
        selected={value}
        selectedValue={value}
        onDaySelect={handleDayClick}
        onMonthChange={onMonthChange}
        startMonth={startYear}
        endMonth={endYear}
        disabled={disabled}
        labelKey={labelKey}
        dateKey={dateKey}
        classNames={{
          weekdays:
            'flex border border-b border-border-Default rounded-t-sm py-0.5',
          weekday:
            'text-muted-foreground rounded-md full w-full text-sm py-0.5',
          week: 'flex w-full border-x border-b  border-border-Default',
          day: 'h-full w-full border-x border-border-Default',
        }}
        className={cn(
          'border rounded-md border-border-Default w-full p-2 xl:p-3',
          className
        )}
      />
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-white/50 backdrop-blur-[2px] z-10">
          <AppSpinner
            className="h-8 w-8 text-brand-teal-Default"
            isLoading={isLoading}
          />
        </div>
      )}
    </div>
  );
};

export default CalendarView;
