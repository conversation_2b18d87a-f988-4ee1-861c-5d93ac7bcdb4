import AppButton from '../app-button';
import AppDialog from '../app-dialog';

interface AppDeleteModalTypes {
  open?: boolean;
  title?: JSX.Element | string;
  description?: JSX.Element | string;
  submitLabel?: string;
  cancelLabel?: string;
  submitName?: string;
  onOpenChange?: () => void;
  handleSubmit?: () => any;
  handleCancel?: () => void;
  disabled?: boolean;
  isLoading?: boolean;
  cancelLoading?: boolean;
  className?: string;
}
const AppConfirmationModal = (props: AppDeleteModalTypes) => {
  const {
    title,
    description,
    open,
    onOpenChange,
    handleSubmit,
    handleCancel,
    disabled,
    cancelLabel,
    isLoading,
    cancelLoading,
    className,
    submitLabel = 'Yes',
  } = props;
  return (
    <AppDialog
      className={className}
      title={title ?? 'Delete'}
      description={description}
      open={open}
      onOpenChange={onOpenChange}
      footer={
        <div className="flex justify-end gap-x-2">
          {handleSubmit && (
            <AppButton
              className="text-md"
              label={submitLabel || 'Yes'}
              isLoading={isLoading}
              onClick={handleSubmit}
              disabled={disabled}
            />
          )}
          {handleCancel && (
            <AppButton
              label={cancelLabel || 'No'}
              variant="neutral"
              className="text-md text-primary border border-primary"
              onClick={handleCancel}
              disabled={isLoading}
              isLoading={cancelLoading}
            />
          )}
        </div>
      }
    />
  );
};
export default AppConfirmationModal;
