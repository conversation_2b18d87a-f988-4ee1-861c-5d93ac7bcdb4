import { render, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import AppConfirmationModal from './index';

describe('AppConfirmationModal Component', () => {
  it('renders the modal correctly and handles submit and cancel actions', () => {
    const handleSubmit = vi.fn();
    const handleCancel = vi.fn();
    const onOpenChange = vi.fn();

    const { getByText, queryByText } = render(
      <AppConfirmationModal
        open={true}
        title="Delete Item"
        description="Are you sure you want to delete this item?"
        handleSubmit={handleSubmit}
        handleCancel={handleCancel}
        onOpenChange={onOpenChange}
        submitLabel="Yes"
        cancelLabel="No"
        isLoading={false}
        disabled={false}
      />
    );
    expect(getByText('Delete Item')).toBeInTheDocument();
    expect(
      getByText('Are you sure you want to delete this item?')
    ).toBeInTheDocument();
    const submitButton = getByText('Yes');
    fireEvent.click(submitButton);
    expect(handleSubmit).toHaveBeenCalledOnce();

    const cancelButton = getByText('No');
    fireEvent.click(cancelButton);
    expect(handleCancel).toHaveBeenCalledOnce();

    expect(queryByText('Delete Item')).toBeInTheDocument();
  });

  it('does not render when open is false', () => {
    const { queryByText } = render(
      <AppConfirmationModal
        open={false}
        title="Delete Item"
        description="Are you sure?"
      />
    );

    expect(queryByText('Delete Item')).not.toBeInTheDocument();
  });
});
