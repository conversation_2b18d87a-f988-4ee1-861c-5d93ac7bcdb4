import { <PERSON>arm<PERSON><PERSON> } from 'lucide-react';
import { useCallback, useEffect, useRef, useState } from 'react';
import { Helmet, HelmetProvider } from 'react-helmet-async';
import { useIdleTimer } from 'react-idle-timer';
import { useNavigate } from 'react-router-dom';

import { Progress } from '@/components/ui/progress';
import { ROUTES } from '@/constants/routes-constants';
import { getQueryParam, getStorageValue } from '@/lib/utils';
import { useLazyLockedOrderQuery } from '@/redux/features/orders/order.api';
import AppButton from './app-button';
import AppDialog from './app-dialog';

const ORDER_IDLE_MINUTES_KEY = 'idleOrderMinutes';
const ORDER_ACTIVE_KEY = 'orderIsActive';
const DEFAULT_IDLE_MINUTES = 10;
const COUNTDOWN_SECONDS = 60;

const OrderSessionTimeout: React.FC = () => {
  const orderId = getQueryParam('id') as string;
  const [isIdle, setIsIdle] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [remainingTime, setRemainingTime] = useState(COUNTDOWN_SECONDS);

  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const navigate = useNavigate();
  const isProduction = process.env.NODE_ENV === 'production';

  const [lockedOrder] = useLazyLockedOrderQuery();

  const idleTimeoutMs =
    Number(getStorageValue(ORDER_IDLE_MINUTES_KEY)) * 60_000 ||
    DEFAULT_IDLE_MINUTES * 60_000;

  const handleUserActive = useCallback(() => {
    setIsIdle(false);
    setOpenDialog(false);
    setRemainingTime(COUNTDOWN_SECONDS);
  }, []);

  const handleOnIdle = () => {
    if (isProduction) {
      setIsIdle(true);
      setOpenDialog(true);
    }
  };

  const handleContinue = useCallback(() => {
    // lock order
    lockedOrder(orderId);
    const now = new Date().toISOString();
    localStorage.setItem(ORDER_ACTIVE_KEY, now);

    window.dispatchEvent(new CustomEvent('orderStaysActive', { detail: now }));
    handleUserActive();
  }, [handleUserActive, lockedOrder, orderId]);

  const handleRedirectToOrders = useCallback(() => {
    localStorage.removeItem(ORDER_ACTIVE_KEY);
    navigate(ROUTES.ORDERS);
    handleUserActive();
  }, [navigate, handleUserActive]);

  // Idle timer hook
  useIdleTimer({
    timeout: idleTimeoutMs,
    onIdle: handleOnIdle,
    debounce: 500,
    crossTab: true,
  });

  // Countdown effect
  useEffect(() => {
    if (openDialog) {
      intervalRef.current = setInterval(() => {
        setRemainingTime((prev) => {
          if (prev <= 1) {
            clearInterval(intervalRef.current!);
            handleRedirectToOrders();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }

    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current);
    };
  }, [openDialog, handleRedirectToOrders]);

  // Cross-tab sync
  useEffect(() => {
    const handleStorageEvent = (event: StorageEvent) => {
      if (event.key === ORDER_ACTIVE_KEY) handleUserActive();
    };

    window.addEventListener('orderStaysActive', handleUserActive);
    window.addEventListener('storage', handleStorageEvent);

    return () => {
      window.removeEventListener('orderStaysActive', handleUserActive);
      window.removeEventListener('storage', handleStorageEvent);
    };
  }, [handleUserActive]);

  const progress = Math.round((remainingTime / COUNTDOWN_SECONDS) * 100);

  return (
    <HelmetProvider>
      <Helmet>
        <title>
          {isIdle ? `${remainingTime} sec - Inactivity Warning` : 'Party Track'}
        </title>
      </Helmet>

      {isIdle && (
        <AppDialog
          open={openDialog}
          footer={
            <div className="flex justify-center gap-2 w-full">
              <AppButton
                label="Back to Orders"
                variant="neutral"
                onClick={handleRedirectToOrders}
              />
              <AppButton label="Stay In" onClick={handleContinue} />
            </div>
          }
        >
          <div className="px-4 py-3">
            <div className="flex justify-center pb-4">
              <AlarmClock size={38} className="text-primary" />
            </div>
            <h1 className="text-lg font-semibold text-center text-gray-400">
              Order Session Expiring
            </h1>
            <p className="text-3xl font-semibold text-center pb-3">
              Are you still working?
            </p>

            <div className="py-3">
              <Progress
                value={progress}
                className="w-full h-2"
                bgColor={
                  progress < 18
                    ? '#ff6347'
                    : progress < 50
                      ? '#ffd700'
                      : '#6127CA'
                }
                style={{ transition: 'width 0.3s ease-in-out' }}
              />
            </div>

            <p className="text-center italic text-sm">
              You will be redirected to the order page in{' '}
              <strong>{remainingTime}</strong> seconds.
            </p>
          </div>
        </AppDialog>
      )}
    </HelmetProvider>
  );
};

export default OrderSessionTimeout;
