import { Loader2 } from 'lucide-react';

const WorkInProgress = () => {
  return (
    <div className="min-h-[90vh] bg-gray-50 flex items-center justify-center p-4">
      <div className="text-center space-y-4">
        <Loader2 className="h-12 w-12 text-gray-600 animate-spin mx-auto" />
        <h1 className="text-2xl font-semibold text-gray-800">
          Work in Progress
        </h1>
        <p className="text-gray-600">Coming soon</p>
      </div>
    </div>
  );
};

export default WorkInProgress;
