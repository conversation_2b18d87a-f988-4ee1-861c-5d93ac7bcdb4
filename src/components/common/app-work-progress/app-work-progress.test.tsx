import { render, screen } from '@testing-library/react';
import { describe, expect, it } from 'vitest';
import WorkInProgress from './index';

describe('WorkInProgress Component', () => {
  it('should render the loader icon with correct classes', () => {
    const isRendered = render(<WorkInProgress />);
    expect(isRendered);
  });

  it('should render the correct title and subtitle', () => {
    render(<WorkInProgress />);

    const title = screen.getByText('Work in Progress');
    expect(title).toBeInTheDocument();
    expect(title).toHaveClass('text-2xl', 'font-semibold', 'text-gray-800');
    const subtitle = screen.getByText('Coming soon');
    expect(subtitle).toBeInTheDocument();
    expect(subtitle).toHaveClass('text-gray-600');
  });

  it('should render everything correctly with the correct styling', () => {
    render(<WorkInProgress />);

    const container = screen.getByText('Coming soon');
    expect(container);
  });
});
