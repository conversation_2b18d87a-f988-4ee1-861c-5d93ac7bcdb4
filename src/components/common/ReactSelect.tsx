import { cn, generateLabelValuePairs } from '@/lib/utils';
import { useGetItemLookupQuery } from '@/redux/features/orders/item-details.api';
import debounce from 'lodash/debounce';
import { useEffect, useMemo, useState } from 'react';
import {
  Controller,
  FieldValues,
  Path,
  RegisterOptions,
  UseFormReturn,
} from 'react-hook-form';
import Select from 'react-select';

type Option = {
  label: string;
  value: string;
};

interface ServerSideSelectProps<T extends FieldValues> {
  placeholder?: string;
  name: Path<T>;
  form: UseFormReturn<T>;
  onSelectChange?: (selected: Option) => void;
  url: string;
  className?: string;
  fieldName?: string;
  labelKey: string;
  valueKey: string;
  sortBy?: string;
  operator?: string;
  maxMenuHeight?: number;
  disabled?: boolean;
  validation?: RegisterOptions<T, Path<T>>;
  menuPosition?: 'absolute' | 'fixed';
  extraFilters?: { field: string; value: string; operator?: string }[];
}

const ReactSelect = <T extends FieldValues>({
  placeholder,
  name,
  form,
  onSelectChange,
  url,
  className,
  sortBy,
  labelKey,
  fieldName,
  operator = 'Contains',
  disabled,
  maxMenuHeight,
  validation,
  menuPosition = 'fixed',
  extraFilters = [],
}: ServerSideSelectProps<T>) => {
  const [search, setSearch] = useState('');
  const [totalCount, setTotalCount] = useState(10);
  const [isTablet, setIsTablet] = useState(window?.innerWidth >= 768);

  const { data, isFetching } = useGetItemLookupQuery(
    {
      url,
      body: {
        pageSize: totalCount,
        pageNumber: 1,
        sortBy: sortBy ?? labelKey,
        filters: [
          { field: fieldName ?? labelKey, value: search, operator },
          ...extraFilters.map((filter) => ({
            field: filter.field,
            value: filter.value,
            operator: filter.operator ?? 'Equals', // default fallback
          })),
        ],
        sortAscending: true,
      },
    }
    // { skip: totalCount === 10 && search === '' }
  );

  // Set up validation options, including the required field message if needed
  const isRequired = validation?.required ? 'Required' : undefined;
  const requiredMessage =
    validation && typeof validation.required === 'string'
      ? validation.required
      : isRequired;

  const validationOptions = validation
    ? {
        ...validation,
        required: requiredMessage,
      }
    : {};

  const options = useMemo(() => {
    const newOptions = generateLabelValuePairs({
      data: data?.data,
      labelKey: 'itemId',
      valueKey: 'id',
    });
    return newOptions;
  }, [data?.data]);

  // Effect hook to track screen resizing
  useEffect(() => {
    const handleResize = () => {
      setIsTablet(window?.innerWidth >= 768);
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const debouncedSearch = debounce((searchTerm: string) => {
    setSearch(searchTerm);
    setTotalCount(10);
  }, 500);

  useEffect(() => {
    return () => debouncedSearch.cancel();
  }, [debouncedSearch]);

  const handleMenuClose = () => {
    setTotalCount(10);
    setSearch('');
  };

  const handleScroll = () => {
    if (!isFetching && options.length < (data?.pagination?.totalCount ?? 0)) {
      setTotalCount((prev) => prev + 10);
    }
  };

  // const customStyles = {
  //   control: (base: any) => ({
  //     ...base,
  //     color: '#1E1E1E',
  //     height: '40px',
  //     zIndex: 99999,
  //     pointerEvents: 'all',
  //     ...(isTablet && { overflow: 'hidden', whiteSpace: 'nowrap' }), // For tablet screen sizes
  //   }),
  //   option: (styles: any, { isSelected }: any) => ({
  //     ...styles,
  //     background: isSelected ? '#F3F3F3' : undefined,
  //     color: isSelected ? '#491d96' : 'black',
  //     ':hover': {
  //       backgroundColor: '#F1F1F1',
  //       color: '#491d96',
  //     },
  //   }),
  // };

  return (
    <Controller
      name={name}
      control={form.control}
      rules={validationOptions}
      render={({ field: { onChange, value }, fieldState: { error } }) => {
        return (
          <div>
            <Select
              placeholder={placeholder}
              options={options as any}
              isLoading={isFetching}
              onInputChange={debouncedSearch}
              onMenuScrollToBottom={handleScroll}
              onMenuClose={handleMenuClose}
              isDisabled={disabled}
              value={value && value.label && value.value ? value : null}
              onChange={(newValue) => {
                onChange(newValue);
                if (newValue) {
                  onSelectChange?.(newValue as Option);
                }
              }}
              components={{
                IndicatorSeparator: () => null,
              }}
              className={cn('h-10 sm:text-base md:text-sm', className)}
              menuPortalTarget={document.body}
              menuPosition={menuPosition}
              menuShouldBlockScroll={true}
              // styles={customStyles}
              styles={{
                menuPortal: (base) => ({
                  ...base,
                  zIndex: 99999,
                  pointerEvents: 'all',
                }),
                control: (base: any) => ({
                  ...base,
                  color: '#1E1E1E',
                  height: '40px',
                  // zIndex: 99999,
                  // pointerEvents: 'all',
                  ...(isTablet && { overflow: 'hidden', whiteSpace: 'nowrap' }), // For tablet screen sizes
                }),
                option: (styles: any, { isSelected }: any) => ({
                  ...styles,
                  background: isSelected ? '#F3F3F3' : undefined,
                  color: isSelected ? '#491d96' : 'black',
                  ':hover': {
                    backgroundColor: '#F1F1F1',
                    color: '#491d96',
                  },
                }),
              }}
              theme={(theme) => ({
                ...theme,
                colors: {
                  ...theme.colors,
                  primary25: '#F3F3F3', // Hover color for options
                  primary: '#491d96', // Primary color for select
                },
              })}
              maxMenuHeight={maxMenuHeight}
            />
            {error && (
              <p className="text-sm font-normal text-danger pt-1">
                {error?.message}
              </p>
            )}
          </div>
        );
      }}
    />
  );
};

export default ReactSelect;
