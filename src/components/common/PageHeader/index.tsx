import CheveronLeft from '@/assets/icons/CheveronLeft';
import { cn } from '@/lib/utils';
import { SaveIcon, X } from 'lucide-react';
import AppButton from '../app-button';
import IconButton from '../icon-button';

import { DropdownMenuListType } from '@/types/common.types';
import type { FC, ReactNode, SVGProps } from 'react';
import { useNavigate } from 'react-router-dom';
import ActionColumnMenu from '../data-tables/ActionColumn';

type RequiredProps = {
  title: string;
};

type OptionalProps = Partial<{
  label: string;
  subtitle: string;
  className: string;
  cancelPath?: string;
  icon?: FC<SVGProps<SVGSVGElement>>;
  handleSubmit: () => void;
  isLoading: boolean;
  disabled: boolean;
  cancelDisabled: boolean;
  enableCancel: boolean;
  dropdownMenu?: DropdownMenuListType[];
  dropdownClassName?: string;
  customContent?: ReactNode;
  titleClassName?: string;
  labelClassName?: string;
}>;

type PageHeaderProps = RequiredProps & OptionalProps;

export const PageHeader: React.FC<PageHeaderProps> = ({
  title,
  subtitle,
  cancelPath,
  className,
  label = 'Save Detail',
  icon = SaveIcon,
  handleSubmit,
  isLoading,
  disabled,
  cancelDisabled,
  enableCancel = true,
  dropdownMenu,
  dropdownClassName,
  customContent,
  titleClassName,
  labelClassName,
}) => {
  const navigate = useNavigate();

  const onBack = () => {
    cancelPath && navigate(cancelPath);
  };

  return (
    <div
      className={cn(
        'flex justify-between gap-x-4 items-center py-4 sticky top-0',
        className
      )}
    >
      <div className="flex gap-x-4 items-center">
        {onBack && (
          <IconButton onClick={onBack}>
            <CheveronLeft />
          </IconButton>
        )}
        <h1
          className={cn(
            'text-2xl text-text-tertiary font-semibold',
            subtitle && 'cursor-pointer',
            titleClassName
          )}
          onClick={subtitle ? onBack : undefined}
        >
          {title}
        </h1>
        {subtitle && (
          <div className="flex items-center gap-3">
            <span className="text-2xl font-semibold text-text-tertiary">
              {' / '}
            </span>
            <p className="text-2xl capitalize font-semibold">{subtitle}</p>
          </div>
        )}
      </div>
      <div className="flex flex-wrap justify-end items-center gap-3">
        {customContent && customContent}
        {handleSubmit && (
          <AppButton
            label={label}
            icon={icon}
            onClick={handleSubmit}
            iconClassName="w-4 h-4"
            isLoading={isLoading}
            disabled={disabled}
            className={labelClassName}
          />
        )}

        {enableCancel && (
          <AppButton
            label="Cancel"
            onClick={onBack}
            variant="neutral"
            icon={X}
            iconClassName="w-5 h-5"
            disabled={isLoading || cancelDisabled}
          />
        )}
        {dropdownMenu && (
          <ActionColumnMenu
            triggerClassName="border h-10"
            dropdownMenuList={dropdownMenu}
            contentClassName={cn('p-3 w-full', dropdownClassName)}
          />
        )}
      </div>
    </div>
  );
};
