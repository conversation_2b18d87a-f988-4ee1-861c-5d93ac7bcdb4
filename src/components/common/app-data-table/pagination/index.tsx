import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { cn } from '@/lib/utils';
import { Table } from '@tanstack/react-table';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import React, { useContext } from 'react';
import { AppTableContext } from '../AppTableContext';
import { RowPerPage } from '@/constants/common-constants';

interface PaginationProps<TData> {
  table: Table<TData>;
  totalItems: number;
}

const Pagination = <TData,>({ table, totalItems }: PaginationProps<TData>) => {
  // Get pagination state and setter from context
  const { pagination, setPagination } = useContext(AppTableContext);

  const currentPage = pagination.pageIndex + 1; // +1 because pageIndex is 0-based
  const totalPages = table.getPageCount();

  const getPageNumbers = (): (number | string)[] => {
    const pageNumbers: (number | string)[] = [];

    if (totalPages <= 5) {
      pageNumbers.push(...Array.from({ length: totalPages }, (_, i) => i + 1));
    } else {
      const startPage = Math.max(1, currentPage - 1);
      const endPage = Math.min(totalPages, currentPage + 1);
      if (currentPage > 2) pageNumbers.push(1);

      if (currentPage > 3) {
        pageNumbers.push('...');
      }

      for (let i = startPage; i <= endPage; i++) {
        pageNumbers.push(i);
      }

      if (currentPage < totalPages - 2) {
        pageNumbers.push('...');
      }
      if (currentPage < totalPages - 1) pageNumbers.push(totalPages);
    }

    return pageNumbers;
  };

  const handlePageChange = (page: number) => {
    setPagination((prev) => ({ ...prev, pageIndex: page - 1 })); // Update the pageIndex in context
    table.setPageIndex(page - 1); // Update the page index of the table as well
  };

  return (
    <div className="flex justify-between items-center">
      <div className="flex items-center space-x-2">
        <Button
          variant="outline"
          aria-label="previous-page"
          size="icon"
          className="rounded-full bg-white"
          onClick={() => table.previousPage()}
          disabled={!table.getCanPreviousPage()}
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>

        {getPageNumbers().map((number, index) => (
          <React.Fragment key={index}>
            {number === '...' ? (
              <span className="px-2">...</span>
            ) : (
              <div
                className={cn(
                  currentPage === number
                    ? 'bg-background-brand-violet-Default hover:bg-background-brand-violet-hover rounded-full text-white cursor-not-allowed'
                    : 'hover:bg-background-secondary-hover rounded-md cursor-pointer',
                  'border-0 w-10 h-10 p-2 text-center'
                )}
                onClick={() =>
                  currentPage === number
                    ? undefined
                    : handlePageChange(number as number)
                }
              >
                {number}
              </div>
            )}
          </React.Fragment>
        ))}

        <Button
          variant="outline"
          aria-label="next-page"
          size="icon"
          onClick={() => table.nextPage()}
          className="rounded-full bg-white"
          disabled={!table.getCanNextPage()}
        >
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>

      <div className="flex items-center gap-2 text-sm">
        <div className="flex items-center gap-2 text-sm">
          <span>Row per page</span>
          <Select
            onValueChange={(value) => {
              const newPageSize = Number(value);
              setPagination((prev) => ({
                ...prev,
                pageSize: newPageSize,
                pageIndex: 0, // Reset to the first page when changing page size
              }));
              table.setPageSize(newPageSize);
            }}
            value={pagination.pageSize.toString()}
          >
            <SelectTrigger className="w-fit">
              <SelectValue placeholder="Select row" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                {RowPerPage?.map((item, index: number) => (
                  <SelectItem value={item?.toString()} key={`${item}-${index}`}>
                    {item}
                  </SelectItem>
                ))}
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>
        <span className="font-semibold">Total Count : {totalItems}</span>
      </div>
    </div>
  );
};

export default Pagination;
