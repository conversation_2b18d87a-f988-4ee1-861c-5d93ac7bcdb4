import { render, fireEvent, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import Pagination from './index';
import { AppTableContext } from '../AppTableContext';
import { Table } from '@tanstack/react-table';

const mockTable = {
  getPageCount: vi.fn(() => 10),
  setPageIndex: vi.fn(),
  getCanPreviousPage: vi.fn(() => true),
  getCanNextPage: vi.fn(() => true),
  previousPage: vi.fn(),
  nextPage: vi.fn(),
  setPageSize: vi.fn(),
} as any;

const mockSetPagination = vi.fn();
const mockSetSorting = vi.fn();

describe('Pagination Component', () => {
  it('renders pagination correctly', () => {
    render(
      <AppTableContext.Provider
        value={{
          pagination: { pageIndex: 0, pageSize: 10 },
          setPagination: mockSetPagination,
          sorting: [],
          setSorting: mockSetSorting,
          rowSelection: { '1': true },
          setRowSelection: vi.fn(),
        }}
      >
        <Pagination table={mockTable as Table<any>} totalItems={100} />
      </AppTableContext.Provider>
    );

    expect(screen.getByLabelText('previous-page')).toBeInTheDocument();
    expect(screen.getByLabelText('previous-page')).not.toBeDisabled();

    expect(screen.getByLabelText('next-page')).toBeInTheDocument();
    expect(screen.getByLabelText('next-page')).not.toBeDisabled();

    expect(screen.getByText('1')).toBeInTheDocument();
    expect(screen.getByText('2')).toBeInTheDocument();
    expect(screen.getByText('...')).toBeInTheDocument();
  });

  it('handles page number click', () => {
    render(
      <AppTableContext.Provider
        value={{
          pagination: { pageIndex: 0, pageSize: 10 },
          setPagination: mockSetPagination,
          sorting: [],
          setSorting: mockSetSorting,
          rowSelection: { '1': true },
          setRowSelection: vi.fn(),
        }}
      >
        <Pagination table={mockTable as Table<any>} totalItems={100} />
      </AppTableContext.Provider>
    );

    fireEvent.click(screen.getByText('2'));

    expect(mockSetPagination);
    expect(mockTable.setPageIndex).toHaveBeenCalledWith(1);
  });

  it('disables previous and next buttons when applicable', () => {
    mockTable.getCanPreviousPage = vi.fn(() => false);
    mockTable.getCanNextPage = vi.fn(() => false);

    render(
      <AppTableContext.Provider
        value={{
          pagination: { pageIndex: 0, pageSize: 10 },
          setPagination: mockSetPagination,
          sorting: [],
          setSorting: mockSetSorting,
          rowSelection: { '1': true },
          setRowSelection: vi.fn(),
        }}
      >
        <Pagination table={mockTable as Table<any>} totalItems={100} />
      </AppTableContext.Provider>
    );

    expect(screen.getByLabelText('previous-page')).toBeDisabled();
    expect(screen.getByLabelText('next-page')).toBeDisabled();
  });

  it('handles row per page change', () => {
    render(
      <AppTableContext.Provider
        value={{
          pagination: { pageIndex: 0, pageSize: 10 },
          setPagination: mockSetPagination,
          sorting: [],
          setSorting: mockSetSorting,
          rowSelection: { '1': true },
          setRowSelection: vi.fn(),
        }}
      >
        <Pagination table={mockTable as Table<any>} totalItems={100} />
      </AppTableContext.Provider>
    );
    expect(mockSetPagination);
    expect(mockTable.setPageSize);
  });
});
