import CancelIcon from '@/assets/icons/CancelIcon';
import FilterIcon from '@/assets/icons/FilterIcon';
import SearchIcon from '@/assets/icons/SearchIcon';
import { Input } from '@/components/ui/input';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { DropdownMenuListType } from '@/types/common.types';
import debounce from 'lodash/debounce';
import React, { useCallback, useEffect, useState } from 'react';
import ActionColumnMenu from '../../data-tables/ActionColumn';
import IconButton from '../../icon-button';

interface ToolbarProps<TFilter = Record<string, any>> {
  heading?: React.ReactNode;
  search: string;
  enableSearch?: boolean;
  setSearch?: (search: string) => void;
  customToolBar?: React.ReactNode;
  enableFilter?: boolean;
  isFilterOpen?: boolean;
  setIsFilterOpen?: React.Dispatch<React.SetStateAction<boolean>>;
  filterContent?: React.ReactNode;
  dropdownMenus?: DropdownMenuListType[];
  filter?: TFilter;
  dropdownContentClassName?: string;
  setSearchParams?: (search: string) => void;
  handleClearFilter?: (filterKey: string) => void;
  filterClassName?: string;
}

const Toolbar = <TFilter = Record<string, any>,>({
  heading,
  enableSearch,
  search,
  setSearch,
  customToolBar,
  enableFilter = false,
  isFilterOpen = false,
  setIsFilterOpen,
  filterContent,
  dropdownMenus,
  filterClassName,
  dropdownContentClassName,
  setSearchParams,
}: ToolbarProps<TFilter>) => {
  const [debouncedSearch, setDebouncedSearch] = useState(search);

  const handleFilterToggle = () => {
    if (setIsFilterOpen) {
      setIsFilterOpen((prev) => !prev);
    }
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setDebouncedSearch(event.target.value); // Set the input value
  };

  const handleClearSearch = () => {
    setSearch?.('');
    // Clear the search input immediately
    setSearchParams?.('');
    setDebouncedSearch(''); // Clear the debounced search
  };

  // Debounced search handler
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedSearchHandler = useCallback(
    debounce((newSearch: string) => {
      setSearch?.(newSearch); // Update the search state after debounce
      setSearchParams?.(newSearch);
    }, 500), // Debounce delay of 500ms
    [] // Only create this function once
  );

  // Effect to update debouncedSearch when input changes
  useEffect(() => {
    debouncedSearchHandler(debouncedSearch); // Trigger debounced function
    // Cleanup the debounced function on component unmount
    return () => {
      debouncedSearchHandler.cancel();
    };
  }, [debouncedSearch, debouncedSearchHandler]);
  return (
    <div className="flex justify-between items-center w-full gap-1">
      {heading && (
        <h1 className="text-2xl font-semibold text-[#181A1D]">{heading}</h1>
      )}

      <div className="flex gap-x-2 items-center justify-end flex-wrap gap-2">
        {customToolBar}

        {enableSearch && (
          <div className="flex relative">
            <Input
              value={debouncedSearch} // Controlled input
              placeholder="Search"
              onChange={handleSearchChange} // Call the search handler
              className="text-base  placeholder:text-[#B3B3B3] w-[239px]"
            />
            {search?.length > 0 ? (
              <CancelIcon
                className="text-[#c7ccea] absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer"
                onClick={handleClearSearch}
              />
            ) : (
              <SearchIcon className="text-[#c7ccea] absolute right-3 top-1/2 transform -translate-y-1/2" />
            )}
          </div>
        )}

        {enableFilter && (
          <Popover open={isFilterOpen} onOpenChange={setIsFilterOpen}>
            <PopoverTrigger asChild>
              <IconButton onClick={handleFilterToggle}>
                <FilterIcon />
              </IconButton>
            </PopoverTrigger>
            <PopoverContent
              className={cn(
                'flex flex-col border-border-Default w-full p-2  max-h-[78vh] overflow-auto',
                filterClassName
              )}
              align="end"
            >
              {filterContent}
            </PopoverContent>
          </Popover>
        )}
        {dropdownMenus?.length && (
          <ActionColumnMenu
            dropdownMenuList={dropdownMenus}
            triggerClassName="border h-11 rounded-md"
            contentClassName={dropdownContentClassName}
          />
        )}
      </div>
    </div>
  );
};

export default Toolbar;
