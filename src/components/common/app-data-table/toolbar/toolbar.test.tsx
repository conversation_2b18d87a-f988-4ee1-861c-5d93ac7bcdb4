import '@testing-library/jest-dom';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import Toolbar from '.';

describe('Toolbar', () => {
  const mockSetSearch = vi.fn();
  const mockSetIsFilterOpen = vi.fn();

  const defaultProps = {
    heading: 'Test Toolbar',
    search: '',
    setSearch: mockSetSearch,
    enableSearch: true,
    enableFilter: true,
    isFilterOpen: false,
    setIsFilterOpen: mockSetIsFilterOpen,
    filterContent: <div>Filter Content</div>,
  };

  const renderToolbar = (props = {}) => {
    return render(<Toolbar {...defaultProps} {...props} />);
  };

  it('renders without crashing', () => {
    renderToolbar();
    expect(screen.getByText('Test Toolbar')).toBeInTheDocument();
  });

  it('renders search input when enableSearch is true', () => {
    renderToolbar();
    expect(screen.getByPlaceholderText('Search')).toBeInTheDocument();
  });

  it('calls setSearch when input value changes', async () => {
    renderToolbar();
    const input = screen.getByPlaceholderText('Search');

    fireEvent.change(input, { target: { value: 'New search term' } });

    // Wait for the debounced function to be called
    await waitFor(
      () => {
        expect(mockSetSearch).toHaveBeenCalledWith('New search term');
      },
      { timeout: 2000 }
    );
  });

  it('clears search input and calls setSearch with empty string', () => {
    renderToolbar({ search: 'Some search term' });
    const cancelIcon = screen.getByLabelText(/cancel icon/i);
    fireEvent.click(cancelIcon);
    expect(mockSetSearch).toHaveBeenCalledWith('');
  });

  it('toggles filter when filter icon is clicked', () => {
    renderToolbar({ enableFilter: true, setIsFilterOpen: mockSetIsFilterOpen });

    // Find the IconButton that contains the FilterIcon
    const filterButton = screen.getByRole('button');

    // Click the filter button
    fireEvent.click(filterButton);

    // Check if setIsFilterOpen was called with a function
    expect(mockSetIsFilterOpen).toHaveBeenCalledWith(expect.any(Function));
  });

  it('renders filter content when filter is open', () => {
    renderToolbar({ isFilterOpen: true });
    expect(screen.getByText('Filter Content')).toBeInTheDocument();
  });

  it('renders custom toolbar when provided', () => {
    const customContent = (
      <div data-testid="custom-toolbar">Custom Toolbar</div>
    );
    renderToolbar({ customToolBar: customContent });
    expect(screen.getByTestId('custom-toolbar')).toBeInTheDocument();
  });

  it('renders column visibility icon when enableColumnVisibility is true', () => {
    const isRendered = renderToolbar({ enableColumnVisibility: true });
    expect(isRendered);
  });
});
