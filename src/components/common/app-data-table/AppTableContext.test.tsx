import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import { AppTableContext } from './AppTableContext';
import AppTableContextProvider from './AppTableContext';

describe('AppTableContextProvider Component', () => {
  it('provides pagination and sorting context values', () => {
    render(
      <AppTableContextProvider>
        <AppTableContext.Consumer>
          {(value: any) => (
            <>
              <div data-testid="pagination">
                {JSON.stringify(value.pagination)}
              </div>
              <div data-testid="sorting">{JSON.stringify(value.sorting)}</div>
            </>
          )}
        </AppTableContext.Consumer>
      </AppTableContextProvider>
    );

    expect(screen.getByTestId('pagination'));
    expect(screen.getByTestId('sorting'));
  });

  it('can update pagination and sorting values', () => {
    render(
      <AppTableContextProvider defaultSort={[{ id: 'name', desc: false }]}>
        <AppTableContext.Consumer>
          {(value: any) => (
            <>
              <div
                data-testid="pagination"
                onClick={() =>
                  value.setPagination({ pageIndex: 1, pageSize: 20 })
                }
              >
                {JSON.stringify(value.pagination)}
              </div>
              <div
                data-testid="sorting"
                onClick={() => value.setSorting([{ id: 'date', desc: true }])}
              >
                {JSON.stringify(value.sorting)}
              </div>
            </>
          )}
        </AppTableContext.Consumer>
      </AppTableContextProvider>
    );

    expect(screen.getByTestId('pagination'));
    expect(screen.getByTestId('sorting'));
    fireEvent.click(screen.getByTestId('pagination'));
    expect(screen.getByTestId('pagination')).toHaveTextContent(
      JSON.stringify({ pageIndex: 1, pageSize: 20 })
    );
    fireEvent.click(screen.getByTestId('sorting'));
    expect(screen.getByTestId('sorting')).toHaveTextContent(
      '[{"id":"date","desc":true}]'
    );
  });
});
