'use client';
import { defaultDataTablePageSize } from '@/constants/common-constants';
import { PaginationType, SortingStateType } from '@/types/common.types';
import { RowSelectionState } from '@tanstack/react-table';
import { Dispatch, SetStateAction, createContext, useState } from 'react';

type CreateAppTableContextProviderProps = {
  children: React.ReactNode;
  defaultSort?: SortingStateType[];
};

export const initialPagination = {
  pageIndex: 0,
  pageSize: defaultDataTablePageSize,
};

interface CreateAppTableContextModel {
  pagination: PaginationType;
  setPagination: Dispatch<SetStateAction<PaginationType>>;
  sorting: SortingStateType[];
  setSorting: React.Dispatch<React.SetStateAction<SortingStateType[]>>;
  rowSelection: RowSelectionState;
  setRowSelection: React.Dispatch<React.SetStateAction<RowSelectionState>>;
}

export const AppTableContext = createContext<CreateAppTableContextModel>({
  pagination: initialPagination,
  setPagination: () => {},
  sorting: [],
  setSorting: () => {},
  rowSelection: {},
  setRowSelection: () => {},
});

const AppTableContextProvider = ({
  children,
  defaultSort,
}: CreateAppTableContextProviderProps) => {
  const [pagination, setPagination] = useState<PaginationType>({
    pageSize: 10,
    pageIndex: 0,
  });
  const [sorting, setSorting] = useState<SortingStateType[]>(defaultSort || []);
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});

  return (
    <AppTableContext.Provider
      value={{
        pagination,
        setPagination,
        sorting,
        setSorting,
        rowSelection,
        setRowSelection,
      }}
    >
      {children}
    </AppTableContext.Provider>
  );
};

export default AppTableContextProvider;
