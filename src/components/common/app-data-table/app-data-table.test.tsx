import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { useGetAllMutation } from '@/redux/features/common-api/common.api';
import { ColumnDef } from '@tanstack/react-table';
import AppDataTable from './index';
import { AppTableContext } from './AppTableContext';

// Mock dependencies
vi.mock('@/redux/features/common-api/common.api', () => ({
  useGetAllMutation: vi.fn(),
}));

vi.mock('./AppTableContext', async () => {
  const actual = await import('./AppTableContext');
  return {
    ...actual,
    AppTableContext: {
      ...actual.AppTableContext,
      Provider: ({ children, value }: any) => (
        <actual.AppTableContext.Provider value={value}>
          {children}
        </actual.AppTableContext.Provider>
      ),
    },
  };
});

describe('AppDataTable Component', () => {
  const mockGetAllDataTable = vi.fn();
  const mockData = {
    pagination: { totalCount: 1 },
    data: [{ id: 1, name: 'John Doe', age: 30 }],
  };

  const defaultContextValue = {
    pagination: { pageIndex: 0, pageSize: 10 },
    setPagination: vi.fn(),
    sorting: [],
    setSorting: vi.fn(),
    rowSelection: { '1': true },
    setRowSelection: vi.fn(),
  };

  const dummyColumns: ColumnDef<any, any>[] = [
    { header: 'ID', accessorKey: 'id' },
    { header: 'Name', accessorKey: 'name' },
    { header: 'Age', accessorKey: 'age' },
  ];

  beforeEach(() => {
    (useGetAllMutation as any).mockReturnValue([
      mockGetAllDataTable,
      { data: mockData, isLoading: false },
    ]);
  });

  it('renders table with columns and data', () => {
    render(
      <AppTableContext.Provider value={defaultContextValue}>
        <AppDataTable
          url="/api/data"
          columns={dummyColumns}
          enablePagination
          heading="User Data Table"
          noDataPlaceholder="No users found."
        />
      </AppTableContext.Provider>
    );

    expect(screen.getByText('User Data Table')).toBeInTheDocument();
    expect(screen.getByText('ID')).toBeInTheDocument();
    expect(screen.getByText('Name')).toBeInTheDocument();
    expect(screen.getByText('Age')).toBeInTheDocument();
  });

  it('displays no data placeholder when no data', () => {
    (useGetAllMutation as any).mockReturnValueOnce([
      mockGetAllDataTable,
      { data: { data: [], pagination: { totalCount: 0 } }, isLoading: false },
    ]);

    render(
      <AppTableContext.Provider value={defaultContextValue}>
        <AppDataTable
          url="/api/data"
          columns={dummyColumns}
          enablePagination
          heading="User Data Table"
          noDataPlaceholder="No users found."
        />
      </AppTableContext.Provider>
    );

    expect(screen.getByText('No users found.')).toBeInTheDocument();
  });
});
