import CheckIcon from '@/assets/icons/CheckIcon';
import MinusIcon from '@/assets/icons/MinusIcon';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { cn, getPaginationObject } from '@/lib/utils';
import { useGetAllMutation } from '@/redux/features/common-api/common.api';
import { DropdownMenuListType } from '@/types/common.types';
import {
  ColumnDef,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  OnChangeFn,
  Table as ReactTable,
  Row,
  RowSelectionState,
  useReactTable,
} from '@tanstack/react-table';
import { ChevronDown, ChevronRight } from 'lucide-react';
import React, {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';
import CustomDataTable from '../data-tables/table';
import { AppTableContext, initialPagination } from './AppTableContext';
import Pagination from './pagination';
import RenderFilters from './render-filters';
import Toolbar from './toolbar';

interface DataTableProps<TData, TValue, TFilter = Record<string, any>> {
  columns: (ColumnDef<TData, TValue> & {
    className?: string | ((row: Row<TData>) => string);
  })[];
  url: string;
  onRowSelectionChange?: OnChangeFn<RowSelectionState>;
  enableRowSelection?: boolean;
  enableMultiRowSelection?: boolean;
  enableSearch?: boolean;
  heading?: React.ReactNode;
  customToolBar?: React.ReactNode;
  enableFilter?: boolean;
  filterContent?: React.ReactNode;
  dropdownMenus?: DropdownMenuListType[];
  dropdownContentClassName?: string;
  isFilterOpen?: boolean;
  filter?: TFilter;
  filterClassName?: string;
  noDataPlaceholder?: string;
  enablePagination?: boolean;
  searchKey?: string;
  loaderRows?: number;
  refreshList?: boolean;
  onRowsSelected?: (selectedRows: TData[]) => void;
  handleClearFilter?: (filterKey: string) => void;
  setIsFilterOpen?: React.Dispatch<React.SetStateAction<boolean>>;
  tableClassName?: string;
  setSearchParams?: (search: string) => void;
  bindingKey?: string;
  sortBy?: string;
  isRowExpanded?: boolean;
  renderSubComponent?: (props: { row: Row<TData> }) => React.ReactElement;
  headerClassName?: string;
  highlightKey?: string;
  highlightClassName?: string;
  selectedHighlightClassName?: string;
  searchOperator?: string;
  isLoading?: boolean;
}

function AppDataTable<
  TData,
  TValue,
  TFilter extends Record<string, any> = Record<string, any>,
>({
  url,
  heading,
  columns,
  enableRowSelection = false,
  enableMultiRowSelection = false,
  enableSearch = false,
  enableFilter = false,
  enablePagination = true,
  filterContent,
  dropdownMenus,
  dropdownContentClassName,
  customToolBar,
  filterClassName,
  loaderRows = 10,
  onRowsSelected,
  searchKey,
  refreshList = false,
  filter,
  isFilterOpen,
  setIsFilterOpen,
  handleClearFilter,
  tableClassName,
  noDataPlaceholder = 'No data found',
  onRowSelectionChange,
  setSearchParams,
  bindingKey,
  sortBy = '',
  isRowExpanded = false,
  renderSubComponent,
  headerClassName,
  highlightKey,
  highlightClassName,
  selectedHighlightClassName,
  searchOperator,
  isLoading = false,
}: DataTableProps<TData, TValue, TFilter>) {
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const [search, setSearch] = useState<string>('');
  const {
    pagination = initialPagination,
    setPagination,
    sorting: sortingData,
    setSorting,
  } = useContext(AppTableContext) || {}; // Use pagination from context

  const sorting = useMemo(() => {
    return sortBy ? [{ id: sortBy, desc: true }] : sortingData;
  }, [sortBy, sortingData]);

  const [getAllDataTable, { data: getAllData, isLoading: isTableLoading }] =
    useGetAllMutation();

  const totalItems = getAllData?.pagination?.totalCount;
  const data = useMemo(() => getAllData?.data || [], [getAllData?.data]);

  useEffect(() => {
    // Reset pagination when the search value changes
    if (pagination.pageSize !== 0 && search !== '') {
      setPagination({
        pageSize: 10,
        pageIndex: 0,
      });
    }
  }, [pagination.pageSize, search, setPagination]);

  const getTableData = useCallback(async () => {
    const normalizedFilters = Array.isArray(filter)
      ? filter.map((f: any) => ({
          field: f.name || f.label,
          value: f.value,
          operator: f.operator,
        }))
      : []; // If filter is not an array, default to an empty array

    const payload = getPaginationObject({
      pagination,
      sorting,
      filters:
        enableSearch && searchKey && search
          ? [
              ...normalizedFilters,
              {
                field: searchKey,
                value: search,
                operator: searchOperator ? searchOperator : 'Contains',
              },
            ]
          : normalizedFilters,
    });

    await getAllDataTable({ url, type: 'POST', body: payload });
  }, [
    enableSearch,
    filter,
    getAllDataTable,
    pagination,
    search,
    searchKey,
    sorting,
    url,
    searchOperator,
  ]);

  useEffect(() => {
    url && getTableData();
  }, [getTableData, url]);

  useEffect(() => {
    const normalizedFilters = Array.isArray(filter)
      ? filter.map((f: any) => ({
          field: f.name || f.label,
          value: f.value,
          operator: f.operator,
        }))
      : []; // If filter is not an array, default to an empty array

    const payload = getPaginationObject({
      pagination,
      sorting,
      filters:
        enableSearch && searchKey && search
          ? [
              ...normalizedFilters,
              { field: searchKey, value: search, operator: 'Contains' },
            ]
          : normalizedFilters,
    });

    if (refreshList && url)
      getAllDataTable({ url, type: 'POST', body: payload });
  }, [
    enableSearch,
    filter,
    getAllDataTable,
    pagination,
    refreshList,
    search,
    searchKey,
    sorting,
    url,
  ]);

  const SelectionColumn: ColumnDef<TData> = useMemo(
    () => ({
      id: 'select',
      size: 50,
      header: ({ table }: { table: ReactTable<TData> }) => {
        const isAllRowSelected =
          totalItems === Object.values(rowSelection)?.length;
        const isSomeRowSelected = Object.values(rowSelection)?.length > 0;
        return (
          enableMultiRowSelection && (
            <Checkbox
              // checked={
              //   table.getIsAllPageRowsSelected() ||
              //   (table.getIsSomePageRowsSelected() && 'indeterminate')
              // }
              checked={
                isAllRowSelected || (isSomeRowSelected && 'indeterminate')
              }
              onCheckedChange={() => {
                const isAllPageRowsSelected = table.getIsAllPageRowsSelected();
                table.toggleAllPageRowsSelected(
                  isAllPageRowsSelected ? false : true
                );
                table.getToggleAllPageRowsSelectedHandler();
              }}
              aria-label="Select all"
              className={cn(
                isAllRowSelected
                  ? 'data-[state=checked]:bg-black data-[state=checked]:text-primary-foreground'
                  : isSomeRowSelected
                    ? 'bg-grayScale-600 text-white'
                    : 'border-grayScale-400 border-2',
                'w-5 h-5 ml-1'
              )}
            >
              {isAllRowSelected ? <CheckIcon /> : <MinusIcon />}
            </Checkbox>
          )
        );
      },
      cell: ({ row }: { row: Row<TData> }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          className={cn(
            row.getIsSelected()
              ? 'data-[state=checked]:bg-grayScale-600 data-[state=checked]:text-primary-foreground'
              : 'border-grayScale-400 border-2',
            'w-5 h-5'
          )}
        >
          <CheckIcon />
        </Checkbox>
      ),
      enableSorting: false,
      enableHiding: false,
    }),
    [enableMultiRowSelection, rowSelection, totalItems]
  );

  const tableColumns = useMemo(() => {
    let cols = enableRowSelection
      ? [SelectionColumn, ...columns]
      : [...columns];

    if (isRowExpanded) {
      // Add extra column for expand/collapse if nested table is enabled
      cols = [
        {
          id: 'expander',
          header: () => null,
          size: 40,
          cell: ({ row }: any) => {
            return (
              row.getCanExpand() && (
                <Button
                  {...{ onClick: row?.getToggleExpandedHandler() }}
                  className="cursor-pointer p-[2px] m-0 h-fit text-center"
                  variant="ghost"
                >
                  {row?.getIsExpanded() ? (
                    <ChevronDown className="w-5 h-5 text-primary" />
                  ) : (
                    <ChevronRight className="w-5 h-5" />
                  )}
                </Button>
              )
            );
          },
        } as ColumnDef<TData, TValue>,
        ...cols,
      ];
    }

    return cols;
  }, [enableRowSelection, SelectionColumn, columns, isRowExpanded]);

  const table = useReactTable<TData>({
    data,
    columns: tableColumns,
    sortDescFirst: true,
    enableSortingRemoval: false,
    enableMultiRowSelection: enableMultiRowSelection,
    getCoreRowModel: getCoreRowModel(),
    onRowSelectionChange: (updater) => {
      const newSelection =
        typeof updater === 'function' ? updater(rowSelection) : updater;
      setRowSelection(newSelection);
      onRowSelectionChange?.(updater);

      onRowsSelected?.(
        data?.filter((item: any) =>
          Object.keys(newSelection)?.includes(
            item[bindingKey || 'id']?.toString()
          )
        )
      );
    },
    manualExpanding: true,
    getRowCanExpand: () => isRowExpanded,
    getSortedRowModel: getSortedRowModel(), //provide a sorting row model
    getPaginationRowModel: getPaginationRowModel(),
    manualPagination: true,
    manualSorting: true,
    onPaginationChange: setPagination,
    pageCount: Math.ceil(totalItems / pagination.pageSize),
    state: {
      rowSelection,
      sorting,
      pagination,
      columnPinning: { right: ['action'], left: ['expander'] },
    },
    getRowId: (row, index) => {
      // Fallback to index if the key doesn't exist
      return String(
        (row as Record<string, any>)[bindingKey ?? 'index'] ?? index
      );
    },
    onSortingChange: setSorting,
    enableColumnPinning: true,
    enableSorting: true,
  });

  return (
    <div className="flex flex-col gap-2">
      <Toolbar
        heading={heading}
        search={search}
        setSearch={setSearch}
        setSearchParams={setSearchParams}
        customToolBar={customToolBar}
        enableFilter={enableFilter}
        isFilterOpen={isFilterOpen}
        setIsFilterOpen={setIsFilterOpen}
        filterContent={filterContent}
        dropdownMenus={dropdownMenus}
        dropdownContentClassName={dropdownContentClassName}
        filter={filter}
        enableSearch={enableSearch}
        filterClassName={filterClassName}
        handleClearFilter={handleClearFilter}
      />

      {enableFilter && filter && (
        <RenderFilters
          filter={filter as any}
          handleClearFilter={handleClearFilter}
        />
      )}
      <CustomDataTable
        table={table}
        totalItems={totalItems}
        loaderRows={loaderRows}
        tableColumnsLenght={tableColumns?.length}
        noDataPlaceholder={noDataPlaceholder}
        isLoading={isTableLoading || isLoading}
        tableClassName={tableClassName}
        headerClassName={headerClassName}
        highlightKey={highlightKey}
        highlightClassName={highlightClassName}
        selectedRowHighlightClassName={selectedHighlightClassName}
        isRowExpanded={isRowExpanded}
        renderSubComponent={renderSubComponent}
      />
      {!isTableLoading && enablePagination && data?.length ? (
        <Pagination table={table} totalItems={totalItems} />
      ) : null}
    </div>
  );
}

export default AppDataTable;
