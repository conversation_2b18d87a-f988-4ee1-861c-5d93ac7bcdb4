import { render, screen, fireEvent } from '@testing-library/react';
import { beforeEach, it, vi } from 'vitest';
import UploadFile from './UploadFile';
import { UseToast } from '@/components/ui/toast/ToastContainer';
import { useUploadLinkedFilesMutation } from '@/redux/features/items/linkedFiles.api';
import { describe } from 'vitest';
import { expect } from 'vitest';

vi.mock('@/redux/features/items/linkedFiles.api', () => ({
  useUploadLinkedFilesMutation: vi.fn(),
}));

vi.mock('@/components/ui/toast/ToastContainer', () => ({
  UseToast: vi.fn(),
}));

describe('UploadFile Component', () => {
  const mockOnClose = vi.fn();
  const mockOnUpload = vi.fn();
  const mockToast = { error: vi.fn(), success: vi.fn() };

  beforeEach(() => {
    (UseToast as any).mockReturnValue(mockToast);
    (useUploadLinkedFilesMutation as any).mockReturnValue([
      vi.fn(),
      { isLoading: false },
    ]);
  });

  it('should render the dialog with the upload area', () => {
    render(
      <UploadFile isModalOpen={true} onClose={mockOnClose} url="test-url" />
    );

    expect(
      screen.getByText('Drag & Drop or click to upload a document')
    ).toBeInTheDocument();
  });

  it('should show file preview and enable the upload button when a valid file is selected', async () => {
    new File(['test'], 'test.pdf', { type: 'application/pdf' });
    render(
      <UploadFile isModalOpen={true} onClose={mockOnClose} url="test-url" />
    );

    const input = screen.getByText('Upload Document');
    expect(input);
  });

  it('should show error message when an invalid file type is selected', async () => {
    const invalidFile = new File(['test'], 'test.exe', {
      type: 'application/x-msdownload',
    });
    render(
      <UploadFile isModalOpen={true} onClose={mockOnClose} url="test-url" />
    );

    const input = screen.getByTestId('upload-file');
    fireEvent.change(input, { target: { files: [invalidFile] } });
    expect(mockToast.error);
  });

  it('should call the onUpload function and close the dialog when the file is successfully uploaded', async () => {
    const file = new File(['test'], 'test.pdf', { type: 'application/pdf' });
    (useUploadLinkedFilesMutation as any).mockReturnValue([
      vi.fn().mockResolvedValue({ message: 'Upload Successful' }),
      { isLoading: false },
    ]);

    render(
      <UploadFile
        isModalOpen={true}
        onClose={mockOnClose}
        url="test-url"
        onUpload={mockOnUpload}
      />
    );

    const input = screen.getByTestId('upload-file');
    fireEvent.change(input, { target: { files: [file] } });

    expect(screen.getByText(file.name)).toBeInTheDocument();
  });
});
