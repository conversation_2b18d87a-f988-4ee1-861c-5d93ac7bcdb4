import { render, screen, fireEvent } from '@testing-library/react';
import CustomDialog from './index';
import { describe, expect, it, vi } from 'vitest';

describe('CustomDialog Component', () => {
  it('should render with title, description, and footer', () => {
    const title = 'Test Title';
    const description = 'This is a description for the dialog.';
    const footer = <button>Close</button>;

    render(
      <CustomDialog
        open={true}
        title={title}
        description={description}
        footer={footer}
      />
    );

    expect(screen.getByText(title)).toBeInTheDocument();
    expect(screen.getByText(description)).toBeInTheDocument();
  });

  it('should not close if outsideClose is false', () => {
    const mockOnOpenChange = vi.fn();

    render(
      <CustomDialog
        open={true}
        onOpenChange={mockOnOpenChange}
        outsideClose={false}
      />
    );

    fireEvent.pointerDown(document);
    expect(mockOnOpenChange).not.toHaveBeenCalled();
  });

  it('should render with the provided icon', () => {
    const title = 'Test Title';
    const icon = <span data-testid="icon">🎉</span>;

    render(<CustomDialog open={true} title={title} icon={icon} />);

    expect(screen.getByTestId('icon')).toBeInTheDocument();
    expect(screen.getByText(title)).toBeInTheDocument();
  });
});
