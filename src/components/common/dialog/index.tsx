import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { cn } from '@/lib/utils';

interface CustomDialogPropTypes {
  children?: JSX.Element;
  title?: JSX.Element | string;
  actionTitle?: string;
  footer?: JSX.Element;
  icon?: JSX.Element;
  open?: boolean;
  onOpenChange?: (open?: boolean) => void;
  className?: string;
  footerClassName?: string;
  description?: string;
  outsideClose?: boolean;
  contentClassName?: string;
  ariaDescribedby?: string;
  autoFocus?: boolean;
}

const CustomDialog = ({
  open,
  onOpenChange,
  title,
  children,
  footer,
  footerClassName,
  className,
  icon,
  description = '',
  outsideClose = true,
  contentClassName,
  ariaDescribedby,
  autoFocus = true,
}: CustomDialogPropTypes) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className={cn('bg-white p-0 rounded-lg', className)}
        onPointerDownOutside={(event) =>
          outsideClose && event?.preventDefault()
        }
        aria-describedby={ariaDescribedby}
        onOpenAutoFocus={(event) => !autoFocus && event.preventDefault()} // <-- This disables auto focus
      >
        <DialogHeader className="pl-6 pr-6 pb-3 pt-3 bg-background-default-hover rounded-t-md">
          <DialogTitle className="text-text-Default font-normal text-xl text-start">
            {icon && icon}
            {title && title}
          </DialogTitle>
          {description && <DialogDescription>{description}</DialogDescription>}
        </DialogHeader>
        {children && (
          <div className={cn('pb-3', contentClassName)}>{children}</div>
        )}
        {footer && (
          <DialogFooter className={cn(footerClassName)}>{footer}</DialogFooter>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default CustomDialog;
