import Header from '@/components/common/app-header';
import Sidebar from '@/components/common/app-siderbar';
import { cn, getStorageValue } from '@/lib/utils';
import { setProfile } from '@/redux/features/user-options/loginProfileSlice';
import { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { Outlet } from 'react-router-dom';
import IdleTimerContainer from '../IdleTimerContainer';
import { STORAGE_KEYS } from '@/constants/storageKeys';

const AppLayout = () => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const dispatch = useDispatch();

  useEffect(() => {
    const userId = getStorageValue(STORAGE_KEYS.USER_ID);
    const firstName = getStorageValue(STORAGE_KEYS.FIRST_NAME);
    const lastName = getStorageValue(STORAGE_KEYS.LAST_NAME);
    const defaultLocationId = getStorageValue(STORAGE_KEYS.DEFAULT_LOCATION_ID);
    const idleLogoutMinutes = getStorageValue(STORAGE_KEYS.IDLE_LOGOUT_MINUTES);
    const profileImage = getStorageValue(STORAGE_KEYS.PROFILE_IMAGE);

    const user: any =
      userId && firstName
        ? {
            userId,
            firstName,
            lastName,
            defaultLocationId,
            idleLogoutMinutes,
            profileImage,
          }
        : null;

    if (user) {
      dispatch(setProfile(user));
    }
  }, [dispatch]);

  return (
    <div className="flex flex-col">
      <Header />

      <div
        className={cn(
          'bg-gradient-to-b from-[#55B9C0] to-[#5A2AC2] fixed left-0 top-16 bottom-0 hidden lg:block p-4 overflow-y-auto transition-all ease-in-out duration-500',
          isCollapsed ? 'w-[72px]' : 'w-[285px]'
        )}
      >
        <Sidebar isCollapsed={isCollapsed} setIsCollapsed={setIsCollapsed} />
      </div>

      {/* Main content */}
      <main
        className={cn(
          'flex-1 bg-white mt-16 transition-all ease-in-out duration-500',
          isCollapsed ? 'lg:ml-[72px]' : 'lg:ml-[285px]'
        )}
      >
        <Outlet />
      </main>
      <IdleTimerContainer />
    </div>
  );
};

export default AppLayout;
