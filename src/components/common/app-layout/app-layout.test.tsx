import { render, screen, fireEvent } from '@testing-library/react';
import { vi, describe, test, expect, beforeEach } from 'vitest';
import AppLayout from '.';
import { useDispatch } from 'react-redux';
import * as utils from '@/lib/utils';
import { STORAGE_KEYS } from '@/constants/storageKeys';

vi.mock('react-redux', () => ({
  useDispatch: vi.fn(),
}));

vi.mock('@/lib/utils', () => ({
  getStorageValue: vi.fn(),
  cn: vi.fn(),
}));

vi.mock('@/constants/storageKeys', () => ({
  STORAGE_KEYS: {
    USER_ID: 'userId',
    FIRST_NAME: 'firstName',
    LAST_NAME: 'lastName',
    DEFAULT_LOCATION_ID: 'defaultLocationId',
    IDLE_LOGOUT_MINUTES: 'idleLogoutMinutes',
    PROFILE_IMAGE: 'profileImage',
  },
}));

vi.mock('@/components/common/app-siderbar', () => ({
  __esModule: true,
  default: ({ isCollapsed, setIsCollapsed }: any) => (
    <div data-testid="sidebar">
      <button onClick={() => setIsCollapsed(!isCollapsed)}>
        Toggle Collapse
      </button>
    </div>
  ),
}));

vi.mock('@/components/common/app-header', () => ({
  __esModule: true,
  default: () => <div data-testid="header">Header</div>,
}));

vi.mock('../IdleTimerContainer', () => ({
  __esModule: true,
  default: () => (
    <div data-testid="idle-timer-container">Idle Timer Container</div>
  ),
}));

describe('AppLayout Component', () => {
  let dispatchMock: any;

  beforeEach(() => {
    dispatchMock = vi.fn();
    (useDispatch as any).mockReturnValue(dispatchMock);
  });

  test('renders Header, Sidebar, and IdleTimerContainer', async () => {
    (utils.getStorageValue as any).mockImplementation((key: string) => {
      switch (key) {
        case STORAGE_KEYS.USER_ID:
          return '123';
        case STORAGE_KEYS.FIRST_NAME:
          return 'John';
        case STORAGE_KEYS.LAST_NAME:
          return 'Doe';
        case STORAGE_KEYS.DEFAULT_LOCATION_ID:
          return 'location1';
        case STORAGE_KEYS.IDLE_LOGOUT_MINUTES:
          return '15';
        case STORAGE_KEYS.PROFILE_IMAGE:
          return 'image.jpg';
        default:
          return null;
      }
    });

    render(<AppLayout />);

    expect(screen.getByTestId('header')).toBeInTheDocument();
    expect(screen.getByTestId('idle-timer-container')).toBeInTheDocument();
    expect(screen.getByTestId('sidebar')).toBeInTheDocument();
  });

  test('dispatches setProfile with correct user data', async () => {
    // Mock localStorage values
    (utils.getStorageValue as any).mockImplementation((key: string) => {
      switch (key) {
        case STORAGE_KEYS.USER_ID:
          return '123';
        case STORAGE_KEYS.FIRST_NAME:
          return 'John';
        case STORAGE_KEYS.LAST_NAME:
          return 'Doe';
        case STORAGE_KEYS.DEFAULT_LOCATION_ID:
          return 'location1';
        case STORAGE_KEYS.IDLE_LOGOUT_MINUTES:
          return '15';
        case STORAGE_KEYS.PROFILE_IMAGE:
          return 'image.jpg';
        default:
          return null;
      }
    });

    const isRendered = render(<AppLayout />);
    expect(isRendered);
  });

  test('toggles Sidebar collapse state when button is clicked', async () => {
    (utils.getStorageValue as any).mockImplementation((key: string) => {
      switch (key) {
        case STORAGE_KEYS.USER_ID:
          return '123';
        case STORAGE_KEYS.FIRST_NAME:
          return 'John';
        case STORAGE_KEYS.LAST_NAME:
          return 'Doe';
        case STORAGE_KEYS.DEFAULT_LOCATION_ID:
          return 'location1';
        case STORAGE_KEYS.IDLE_LOGOUT_MINUTES:
          return '15';
        case STORAGE_KEYS.PROFILE_IMAGE:
          return 'image.jpg';
        default:
          return null;
      }
    });

    // Render the component
    render(<AppLayout />);

    const toggleButton = screen.getByText('Toggle Collapse');
    expect(screen.getByTestId('sidebar'));

    fireEvent.click(toggleButton);
    expect(screen.getByTestId('sidebar'));

    fireEvent.click(toggleButton);
    expect(screen.getByTestId('sidebar'));
  });
});
