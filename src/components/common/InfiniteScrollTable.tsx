import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { cn } from '@/lib/utils';
import { PaginationType } from '@/types/common.types';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  OnChangeFn,
  RowSelectionState,
  SortingState,
  useReactTable,
  Table as ReactTable,
  Row,
} from '@tanstack/react-table';
import { ChevronDown, ChevronsUpDown, ChevronUp } from 'lucide-react';
import React, { useEffect, useMemo, useRef } from 'react';
import RenderFilters from './data-tables/render-filters';
import Toolbar from './data-tables/toolbar';
import TooltipWidget from './tooltip-widget';
import { Checkbox } from '../ui/checkbox';
import CheckIcon from '@/assets/icons/CheckIcon';
import MinusIcon from '@/assets/icons/MinusIcon';

interface InfiniteScrollTableProps<
  TData,
  TValue,
  TFilter = Record<string, any>,
> {
  data: any;
  columns: ColumnDef<TData, TValue>[];
  search?: string;
  setSearch?: React.Dispatch<React.SetStateAction<string>>;
  onRowSelectionChange?: OnChangeFn<RowSelectionState>;
  enableRowSelection?: boolean;
  enableSearch?: boolean;
  heading?: React.ReactNode;
  customToolBar?: React.ReactNode;
  enableColumnVisibility?: boolean;
  enableFilter?: boolean;
  filterContent?: React.ReactNode;
  isFilterOpen?: boolean;
  filter?: TFilter;
  handleClearFilter?: (filterKey: string) => void;
  setIsFilterOpen?: React.Dispatch<React.SetStateAction<boolean>>;
  isLoading?: boolean;
  pagination?: PaginationType;
  setPagination?: React.Dispatch<React.SetStateAction<PaginationType>>;
  totalItems?: number;
  filterClassName?: string;
  filterSide?: 'top' | 'right' | 'bottom' | 'left';
  noDataPlaceholder?: string;
  enablePagination?: boolean;
  sorting: SortingState;
  setSorting: OnChangeFn<SortingState>;
  loaderRows?: number;
  handleColumnVisibility?: () => void;
  rowSelection?: any;
  tableClassName?: string;
  enableMultiRowSelection?: boolean;
  bindingKey?: string;
  truncate?: boolean;
}

function InfiniteScrollTable<
  TData,
  TValue,
  TFilter extends Record<string, any> = Record<string, any>,
>({
  data,
  columns,
  search = '',
  setSearch,
  enableRowSelection = false,
  enableSearch = false,
  heading,
  customToolBar,
  enableColumnVisibility = false,
  enableFilter = false,
  filterContent,
  isFilterOpen,
  setIsFilterOpen,
  filter,
  filterSide,
  handleClearFilter,
  isLoading = false,
  pagination = { pageIndex: 0, pageSize: 10 },
  setPagination,
  filterClassName,
  noDataPlaceholder = 'No data found',
  sorting,
  setSorting,
  handleColumnVisibility,
  rowSelection = {},
  onRowSelectionChange,
  tableClassName,
  enableMultiRowSelection = false,
  bindingKey,
}: InfiniteScrollTableProps<TData, TValue, TFilter>) {
  const tableContainerRef = useRef<HTMLTableElement>(null);

  const SelectionColumn: ColumnDef<TData> = useMemo(
    () => ({
      id: 'select',
      size: 50,
      header: ({ table }: { table: ReactTable<TData> }) => {
        return (
          enableMultiRowSelection && (
            <Checkbox
              disabled={!data?.length}
              checked={
                table.getIsAllPageRowsSelected() ||
                (table.getIsSomePageRowsSelected() && 'indeterminate')
              }
              onCheckedChange={(value) =>
                table.toggleAllPageRowsSelected(!!value)
              }
              aria-label="Select all"
              className={cn(
                table.getIsAllPageRowsSelected()
                  ? 'data-[state=checked]:bg-black data-[state=checked]:text-primary-foreground'
                  : table.getIsSomePageRowsSelected()
                    ? 'bg-grayScale-600 text-white'
                    : 'border-grayScale-400 border-2',
                'w-5 h-5'
              )}
            >
              {table.getIsSomePageRowsSelected() ? (
                <MinusIcon />
              ) : (
                <CheckIcon />
              )}
            </Checkbox>
          )
        );
      },
      cell: ({ row }: { row: Row<TData> }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          className={cn(
            row.getIsSelected()
              ? 'data-[state=checked]:bg-grayScale-600 data-[state=checked]:text-primary-foreground'
              : 'border-grayScale-400 border-2',
            'w-5 h-5'
          )}
        >
          <CheckIcon />
        </Checkbox>
      ),
      enableSorting: false,
      enableHiding: false,
    }),
    [data?.length, enableMultiRowSelection]
  );

  const tableColumns = useMemo(() => {
    const cols = enableRowSelection
      ? [SelectionColumn, ...columns]
      : [...columns];

    return cols;
  }, [enableRowSelection, columns, SelectionColumn]);
  // Initialize table
  const table = useReactTable({
    data: data?.data || [],
    columns: tableColumns,
    getCoreRowModel: getCoreRowModel(),
    onRowSelectionChange: onRowSelectionChange,
    manualSorting: true,
    manualPagination: true,
    pageCount: Math.ceil(
      data?.pagination?.totalCount / data?.pagination?.pageSize
    ),

    state: { sorting, rowSelection, columnPinning: { right: ['action'] } },
    onSortingChange: (value) => {
      setPagination?.({ ...pagination, pageIndex: 0 });
      setSorting(value);
    }, // Ensure sorting state is updated
    getRowId: (row, index) => {
      // Fallback to index if the key doesn't exist
      return String(
        (row as Record<string, any>)[bindingKey ?? 'index'] ?? index
      );
    },
  });

  // // Reset page when search or sorting changes
  // useEffect(() => {
  //   console.log('called');
  //   setPagination?.({ ...pagination, pageIndex: 0 });
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [sorting, search]);

  // Infinite scroll logic
  useEffect(() => {
    const container = tableContainerRef.current;
    if (!container) return;

    const handleScroll = () => {
      const { scrollTop, scrollHeight, clientHeight } = container;
      if (
        scrollTop + clientHeight >= scrollHeight - 50 &&
        !isLoading &&
        data?.pagination?.totalCount > data?.data?.length
      ) {
        setPagination?.({ ...pagination, pageIndex: pagination.pageIndex + 1 });
      }
    };

    container.addEventListener('scroll', handleScroll);
    return () => container.removeEventListener('scroll', handleScroll);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isLoading]);

  return (
    <div className="flex flex-col gap-4">
      {customToolBar && (
        <Toolbar
          heading={heading}
          search={search}
          setSearch={setSearch}
          customToolBar={customToolBar}
          enableFilter={enableFilter}
          isFilterOpen={isFilterOpen}
          setIsFilterOpen={setIsFilterOpen}
          filterContent={filterContent}
          filter={filter}
          filterSide={filterSide}
          enableSearch={enableSearch}
          filterClassName={filterClassName}
          handleClearFilter={handleClearFilter}
          enableColumnVisibility={enableColumnVisibility}
          handleColumnVisibility={handleColumnVisibility}
        />
      )}

      {enableFilter && filter && (
        <RenderFilters
          filter={filter as any}
          handleClearFilter={handleClearFilter}
        />
      )}

      <Table
        className="p-0 "
        tableClassName={tableClassName}
        ref={tableContainerRef}
      >
        <TableHeader className="bg-grayScale-10 sticky top-0 z-10">
          {table?.getHeaderGroups()?.map((headerGroup) => (
            <TableRow key={headerGroup.id}>
              {headerGroup?.headers?.map((header) => {
                const enableSorting =
                  data?.pagination?.totalCount > 1 &&
                  header.column.columnDef.enableSorting;
                const isPinned = header.column.getIsPinned();
                return (
                  <TableHead
                    key={header.id}
                    className={cn(
                      'text-base font-medium text-grayScale-90 border-l-[1px] border-r-[1px] border-grayScale-20',
                      isPinned
                        ? 'drop-shadow-[0_3px_5px_rgba(0,0,0,0.1)] sticky right-0 bg-[#eaeaeb] z-[1]'
                        : 'relative'
                    )}
                    style={{
                      minWidth:
                        !header.column.getIsPinned() ||
                        header.column.id !== 'select'
                          ? `${header.column.getSize()}px`
                          : '',
                      width:
                        header.column.getIsPinned() ||
                        header.column.id === 'select'
                          ? `${header.column.getSize()}px`
                          : '',
                      maxWidth: header.column.columnDef.maxSize
                        ? `${header.column.columnDef.maxSize}px`
                        : '',
                    }}
                  >
                    <div className="flex items-center gap-2">
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                      <div
                        onClick={
                          enableSorting
                            ? header?.column?.getToggleSortingHandler()
                            : undefined
                        }
                        className={cn(
                          'flex items-center gap-1',
                          enableSorting ? 'cursor-pointer' : ''
                        )}
                        title={
                          enableSorting && header.column.getCanSort()
                            ? header.column.getNextSortingOrder() === 'asc'
                              ? 'Sort descending'
                              : 'Sort ascending'
                            : undefined
                        }
                      >
                        {{
                          asc: <ChevronDown size={16} />,
                          desc: <ChevronUp size={16} />,
                        }[header.column.getIsSorted() as string] ??
                          (enableSorting && <ChevronsUpDown size={16} />)}
                      </div>
                    </div>
                  </TableHead>
                );
              })}
            </TableRow>
          ))}
        </TableHeader>
        <TableBody>
          {table.getRowModel().rows.map((row) => (
            <TableRow
              key={row.id}
              data-state={row.getIsSelected() ? 'selected' : undefined}
            >
              {row.getVisibleCells().map((cell) => {
                const isPinned = cell.column.getIsPinned();
                return (
                  <TableCell
                    key={cell.id}
                    className={cn(
                      'text-base text-grayScale-60 pt-2 pb-2 border-[1px] border-grayScale-20',
                      isPinned
                        ? 'drop-shadow-[0_3px_5px_rgba(0,0,0,0.1)] bg-white z-[1] right-0 sticky'
                        : 'bg-transparent relative'
                    )}
                    style={{
                      minWidth:
                        !cell.column.getIsPinned() ||
                        cell.column.id !== 'select'
                          ? `${cell.column.getSize()}px`
                          : '',
                      width:
                        cell.column.getIsPinned() || cell.column.id === 'select'
                          ? `${cell.column.getSize()}px`
                          : '',
                      maxWidth: cell.column.columnDef.maxSize
                        ? `${cell.column.columnDef.maxSize}px`
                        : '',
                    }}
                  >
                    {String(cell.getValue())?.length > 30 ? (
                      <TooltipWidget
                        className="p-3 text-black max-w-80 max-h-80 overflow-y-auto break-words"
                        tooltip={String(cell.getValue()) ?? ''}
                        side="top"
                        align="start"
                      >
                        <div className="truncate">
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </div>
                      </TooltipWidget>
                    ) : (
                      flexRender(cell.column.columnDef.cell, cell.getContext())
                    )}
                  </TableCell>
                );
              })}
            </TableRow>
          ))}
          {!isLoading &&
            (!table?.getRowModel()?.rows?.length ||
              table?.getRowModel()?.rows?.length === 0) && (
              <TableRow>
                <TableCell
                  colSpan={tableColumns?.length}
                  className="h-16 text-center"
                >
                  {noDataPlaceholder}
                </TableCell>
              </TableRow>
            )}
          {isLoading && (
            <TableRow>
              <TableCell
                colSpan={columns.length}
                className="text-center border-1"
              >
                Loading more data...
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
}

export default InfiniteScrollTable;
