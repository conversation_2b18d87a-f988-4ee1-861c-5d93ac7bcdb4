import '@testing-library/jest-dom';
import { fireEvent, render, screen } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import HamburgerMenu from './';

// Mock the dependencies
vi.mock('lucide-react', () => ({
  Menu: () => <div data-testid="mock-menu-icon">Menu Icon</div>,
}));

// Updated mock for Sheet components
vi.mock('../../ui/sheet', () => ({
  Sheet: ({
    children,
    open,
    onOpenChange,
  }: {
    children: React.ReactNode;
    open: boolean;
    onOpenChange: (openMenu: boolean) => void;
  }) => (
    <div data-testid="mock-sheet" onClick={() => onOpenChange(!open)}>
      {children}
    </div>
  ),
  SheetContent: ({
    children,
    side,
    className,
  }: {
    children: React.ReactNode;
    className?: string;
    side?: 'left' | 'top' | 'bottom' | 'right';
  }) => (
    <div
      data-testid="mock-sheet-content"
      data-side={side}
      className={className}
    >
      {children}
    </div>
  ),
  SheetTrigger: ({ children, ...props }: { children: React.ReactNode }) => (
    <div data-testid="mock-sheet-trigger" {...props}>
      {children}
    </div>
  ),
}));

describe('HamburgerMenu', () => {
  const mockSetOpenMenu = vi.fn();
  const defaultProps = {
    openMenu: false,
    setOpenMenu: mockSetOpenMenu,
    content: <div>Menu Content</div>,
  };

  it('renders without crashing', () => {
    render(<HamburgerMenu {...defaultProps} />);
    expect(screen.getByTestId('mock-sheet')).toBeInTheDocument();
  });

  it('renders default trigger when no triggerComponent is provided', () => {
    render(<HamburgerMenu {...defaultProps} />);
    expect(screen.getByTestId('mock-menu-icon')).toBeInTheDocument();
  });

  it('renders custom trigger when triggerComponent is provided', () => {
    const customTrigger = <button>Custom Trigger</button>;
    render(
      <HamburgerMenu {...defaultProps} triggerComponent={customTrigger} />
    );
    expect(screen.getByText('Custom Trigger')).toBeInTheDocument();
  });

  it('renders content in SheetContent', () => {
    render(<HamburgerMenu {...defaultProps} />);
    expect(screen.getByText('Menu Content')).toBeInTheDocument();
  });

  it('calls setOpenMenu when Sheet is opened or closed', () => {
    render(<HamburgerMenu {...defaultProps} />);
    fireEvent.click(screen.getByTestId('mock-sheet'));
    expect(mockSetOpenMenu).toHaveBeenCalledWith(true);
  });

  it('applies custom className to SheetContent', () => {
    const customClass = 'custom-class';
    render(<HamburgerMenu {...defaultProps} className={customClass} />);
    expect(screen.getByTestId('mock-sheet-content').className).toContain(
      customClass
    );
  });

  it('sets correct side prop on SheetContent', () => {
    const { rerender } = render(<HamburgerMenu {...defaultProps} />);
    expect(screen.getByTestId('mock-sheet-content')).toHaveAttribute(
      'data-side',
      'left'
    );

    rerender(<HamburgerMenu {...defaultProps} side="right" />);
    expect(screen.getByTestId('mock-sheet-content')).toHaveAttribute(
      'data-side',
      'right'
    );
  });

  it('has accessible label for the menu trigger', () => {
    render(<HamburgerMenu {...defaultProps} />);
    expect(screen.getByLabelText('Toggle menu')).toBeInTheDocument();
  });
});
