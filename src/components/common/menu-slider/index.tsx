import { cn } from '@/lib/utils';
import { Menu } from 'lucide-react';
import React from 'react';
import { Sheet, SheetContent, SheetTrigger } from '../../ui/sheet';

interface HamburgerMenuProps {
  triggerComponent?: React.ReactNode;
  openMenu: boolean;
  setOpenMenu: (openMenu: boolean) => void;
  content: React.ReactNode;
  className?: string;
  side?: 'left' | 'top' | 'bottom' | 'right';
}

const HamburgerMenu = ({
  triggerComponent,
  openMenu,
  setOpenMenu,
  content,
  className,
  side = 'left',
}: HamburgerMenuProps) => {
  return (
    <Sheet open={openMenu} onOpenChange={setOpenMenu}>
      <SheetTrigger className="w-8 h-8" aria-label="Toggle menu">
        {triggerComponent || (
          <Menu className="w-8 h-8 ml-2 text-grayScale-300" />
        )}
      </SheetTrigger>
      <SheetContent
        side={side}
        className={cn(
          'w-[85%] bg-gradient-to-b from-[#55B9C0] to-[#5A2AC2] text-secondary rounded-r-md p-2 pt-12',
          className
        )}
        closeBtnClass={'text-white h-6 w-6'}
      >
        {content}
      </SheetContent>
    </Sheet>
  );
};

export default HamburgerMenu;
