import { cn } from '@/lib/utils';
import React, { ReactNode } from 'react';

interface Tab {
  label: string;
  value: string;
  content: React.ReactNode;
}

interface AppTabsVerticalProps {
  tabs: Tab[];
  activeTab: string;
  onTabChange: (value: string | any) => void;
  showTabMenu?: boolean;
  className?: string;
  tabsBottomContent?: ReactNode;
  contentClassName?: string;
}

const AppTabsVertical: React.FC<AppTabsVerticalProps> = ({
  tabs,
  activeTab,
  onTabChange,
  showTabMenu = true,
  className,
  tabsBottomContent,
  contentClassName,
}) => {
  const tabsMap = new Map(tabs?.map((tab) => [tab?.value, tab?.content]));
  const tabContent = tabsMap?.get(activeTab);
  return (
    <div className={cn('flex flex-row gap-6 w-full ', className)}>
      {showTabMenu && (
        <div className="w-1/5">
          <div className={cn('border-[1px] rounded-lg overflow-hidden h-fit')}>
            {tabs?.map((tab, index) => (
              <div key={index}>
                <button
                  type="button"
                  className={cn(
                    'w-full text-text-neutral-tertiary text-left p-3 transition-colors duration-200',
                    activeTab === tab.value
                      ? 'border-b-text-brand-violet-Default border-b-[2px] text-brand-violet font-normal text-base bg-background-brand-violet-tertiary'
                      : 'border-b-[2px]'
                  )}
                  onClick={() => onTabChange(tab?.value)}
                >
                  {tab?.label}
                </button>
              </div>
            ))}
          </div>
          {tabsBottomContent && tabsBottomContent}
        </div>
      )}
      {/* Tab Content */}
      <div
        className={cn(
          'w-4/5 p-6 bg-white rounded-lg border h-fit',
          showTabMenu ? '' : 'w-full',
          contentClassName
        )}
      >
        {tabContent}
      </div>
    </div>
  );
};

export default AppTabsVertical;
