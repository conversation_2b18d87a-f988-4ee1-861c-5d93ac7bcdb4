import { render, screen, fireEvent } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import AppTabsVertical from './index';

describe('AppTabsVertical Component', () => {
  const tabs = [
    { label: 'Tab 1', value: 'tab1', content: <div>Content for Tab 1</div> },
    { label: 'Tab 2', value: 'tab2', content: <div>Content for Tab 2</div> },
    { label: 'Tab 3', value: 'tab3', content: <div>Content for Tab 3</div> },
  ];

  it('should render the correct tab content based on the active tab', () => {
    render(
      <AppTabsVertical tabs={tabs} activeTab="tab1" onTabChange={vi.fn()} />
    );
    expect(screen.getByText('Content for Tab 1')).toBeInTheDocument();
    expect(screen.queryByText('Content for Tab 3')).not.toBeInTheDocument();
  });

  it('should switch tab content when a new tab is clicked', () => {
    const handleTabChange = vi.fn();
    render(
      <AppTabsVertical
        tabs={tabs}
        activeTab="tab1"
        onTabChange={handleTabChange}
      />
    );

    fireEvent.click(screen.getByText('Tab 2'));
    expect(handleTabChange).toHaveBeenCalledWith('tab2');
    expect(screen.queryByText('Content for Tab 1')).toBeInTheDocument();
  });

  it('should apply active class to the active tab', () => {
    render(
      <AppTabsVertical tabs={tabs} activeTab="tab2" onTabChange={vi.fn()} />
    );

    const activeTabButton = screen.getByText('Tab 2');
    expect(activeTabButton).toHaveClass(
      'border-b-text-brand-violet-Default border-b-[2px] text-brand-violet font-normal text-base bg-background-brand-violet-tertiary'
    );

    screen.getByText('Tab 1');
    expect(screen.queryByText('Content for Tab 1')).not.toBeInTheDocument();
  });

  it('should render tab menu only when showTabMenu is true', () => {
    const { rerender } = render(
      <AppTabsVertical
        tabs={tabs}
        activeTab="tab1"
        onTabChange={vi.fn()}
        showTabMenu={true}
      />
    );

    expect(screen.getByText('Tab 1')).toBeInTheDocument();
    expect(screen.getByText('Tab 2')).toBeInTheDocument();
    expect(screen.getByText('Tab 3')).toBeInTheDocument();

    rerender(
      <AppTabsVertical
        tabs={tabs}
        activeTab="tab1"
        onTabChange={vi.fn()}
        showTabMenu={false}
      />
    );

    expect(screen.queryByText('Tab 1')).not.toBeInTheDocument();
    expect(screen.queryByText('Tab 2')).not.toBeInTheDocument();
    expect(screen.queryByText('Tab 3')).not.toBeInTheDocument();
  });

  it('should apply custom className to the container', () => {
    const isClicked = render(
      <AppTabsVertical
        tabs={tabs}
        activeTab="tab1"
        onTabChange={vi.fn()}
        className="custom-class"
      />
    );
    expect(isClicked);
  });
});
