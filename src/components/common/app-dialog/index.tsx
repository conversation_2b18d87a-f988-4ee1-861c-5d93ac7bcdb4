import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { cn } from '@/lib/utils';

// DialogPropTypes
interface DialogPropTypes {
  children?: JSX.Element;
  title?: JSX.Element | string;
  actionTitle?: string;
  description?: JSX.Element | string;
  footer?: JSX.Element;
  icon?: JSX.Element;
  open?: boolean;
  onOpenChange?: () => void;
  className?: string;
  footerClassName?: string;
}

const AppDialog = ({
  title,
  icon,
  description = '',
  footer,
  open,
  onOpenChange,
  children,
  className,
  footerClassName,
}: DialogPropTypes) => {
  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent className={cn(className, 'bg-white')}>
        <AlertDialogHeader>
          <AlertDialogTitle className="text-primary pb-4 flex gap-2">
            {icon && icon}
            {title && title}
          </AlertDialogTitle>
          <AlertDialogDescription>{description}</AlertDialogDescription>
        </AlertDialogHeader>

        {children && children}
        <AlertDialogFooter className={cn(footerClassName, 'pt-4')}>
          {footer}
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default AppDialog;
