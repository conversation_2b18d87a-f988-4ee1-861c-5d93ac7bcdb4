import { render, screen } from '@testing-library/react';
import AppDialog from '.';
import { describe, expect, it, vi } from 'vitest';

describe('AppDialog Component', () => {
  it('should render with the correct title and description', () => {
    const title = 'Test Title';
    const description = 'Test Description';

    render(
      <AppDialog
        title={title}
        description={description}
        open={true}
        onOpenChange={vi.fn()}
      />
    );

    expect(screen.getByText(title)).toBeInTheDocument();
    expect(screen.getByText(description)).toBeInTheDocument();
  });

  it('should render the icon when passed as a prop', () => {
    const icon = <span data-testid="icon">🔔</span>;
    render(
      <AppDialog title="Test" icon={icon} open={true} onOpenChange={vi.fn()} />
    );

    expect(screen.getByTestId('icon')).toBeInTheDocument();
  });

  it('should render children inside the dialog', () => {
    const childContent = <div data-testid="child">Child Component</div>;

    render(
      <AppDialog open={true} onOpenChange={vi.fn()}>
        {childContent}
      </AppDialog>
    );

    expect(screen.getByTestId('child')).toBeInTheDocument();
  });

  it('should conditionally render footer if passed', () => {
    const footerContent = <div data-testid="footer">Footer</div>;

    render(
      <AppDialog open={true} onOpenChange={vi.fn()} footer={footerContent}>
        <p>Dialog content</p>
      </AppDialog>
    );

    expect(screen.getByTestId('footer')).toBeInTheDocument();
  });

  it('should not render footer if not passed', () => {
    render(<AppDialog open={true} onOpenChange={vi.fn()} />);

    expect(screen.queryByTestId('footer')).not.toBeInTheDocument();
  });

  it('should render with custom className and footerClassName', () => {
    const customClass = 'custom-class';
    const customFooterClass = 'custom-footer-class';

    render(
      <AppDialog
        title="Custom Classes"
        open={true}
        onOpenChange={vi.fn()}
        className={customClass}
        footerClassName={customFooterClass}
      />
    );

    expect(screen.getByRole('alertdialog')).toHaveClass(customClass);
  });
});
