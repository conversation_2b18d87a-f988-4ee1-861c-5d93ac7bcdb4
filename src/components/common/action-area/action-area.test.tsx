import { render, screen, fireEvent } from '@testing-library/react';
import { describe, expect, it } from 'vitest';
import ActionArea from '.';

describe('ActionArea Component', () => {
  const tabs = [
    { value: 'tab1', label: 'Tab 1', content: <div>Content 1</div> },
    { value: 'tab2', label: 'Tab 2', content: <div>Content 2</div> },
    { value: 'tab3', label: 'Tab 3', content: <div>Content 3</div> },
  ];

  it('renders all tabs with labels', () => {
    render(<ActionArea tabs={tabs} defaultValue="tab1" />);

    tabs.forEach(({ label }) => {
      expect(screen.getByText(label)).toBeInTheDocument();
    });
  });

  it('renders the correct content for the default active tab', () => {
    render(<ActionArea tabs={tabs} defaultValue="tab1" />);

    expect(screen.getByText('Content 1')).toBeInTheDocument();
    expect(screen.queryByText('Content 2')).not.toBeInTheDocument();
    expect(screen.queryByText('Content 3')).not.toBeInTheDocument();
  });

  it('highlights the active tab correctly', () => {
    render(<ActionArea tabs={tabs} defaultValue="tab1" />);

    // Check if the first tab is active
    const activeTab = screen.getByText('Tab 1');
    expect(activeTab).toHaveClass('data-[state=active]:bg-brand-teal-Default');

    // Click the second tab and check again
    fireEvent.click(screen.getByText('Tab 2'));
    const newActiveTab = screen.getByText('Tab 2');
    expect(newActiveTab).toHaveClass(
      'data-[state=active]:bg-brand-teal-Default'
    );
  });
});
