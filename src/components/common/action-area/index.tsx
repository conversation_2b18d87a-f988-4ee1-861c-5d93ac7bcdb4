import { <PERSON>roll<PERSON><PERSON>, ScrollBar } from '@/components/ui/scroll-area';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { cn } from '@/lib/utils';
import { TabType } from '@/types/order.types';
import React from 'react';
import AppSpinner from '../app-spinner';

type Tab = {
  value: string;
  label: React.ReactNode;
  content: React.ReactNode;
};

interface ActionAreaTabsProps {
  tabs: Tab[];
  defaultValue: string;
  className?: string;
  tabsContentClassName?: string;
  onValueChange?: (value: TabType | string) => void;
  isLoading?: boolean;
  disabled?: boolean;
}

const ActionArea: React.FC<ActionAreaTabsProps> = ({
  tabs,
  defaultValue,
  className,
  tabsContentClassName,
  onValueChange,
  isLoading,
  disabled,
}) => {
  return (
    <div className="relative">
      <Tabs
        defaultValue={defaultValue}
        className="w-full p-0 m-0 relative"
        onValueChange={onValueChange}
        value={defaultValue}
      >
        <ScrollArea>
          <div className="p-3 border-border border-[1px] w-full relative rounded-lg bg-[#f5f5f5]">
            <TabsList className={cn('flex p-0 justify-between', className)}>
              {tabs.map(({ value, label }) => (
                <TabsTrigger
                  key={value}
                  value={value}
                  disabled={disabled}
                  className="w-full p-3 rounded-lg text-base font-normal text-text-Default
                 data-[state=active]:bg-brand-teal-Default
                 data-[state=active]:text-white
                 data-[state=active]:font-normal [&[data-state=active]_svg]:text-[#f5f5f5]"
                >
                  {label}
                </TabsTrigger>
              ))}
            </TabsList>
          </div>
          <ScrollBar orientation="horizontal" />
        </ScrollArea>

        {tabs.map(({ value, content }) => (
          <TabsContent
            key={value}
            value={value}
            className={tabsContentClassName}
          >
            {content}
          </TabsContent>
        ))}
      </Tabs>
      {/* Loader Overlay */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-white/50 backdrop-blur-[2px] z-10">
          <AppSpinner
            className="h-8 w-8 text-brand-teal-Default"
            isLoading={isLoading}
          />
        </div>
      )}
    </div>
  );
};

export default ActionArea;
