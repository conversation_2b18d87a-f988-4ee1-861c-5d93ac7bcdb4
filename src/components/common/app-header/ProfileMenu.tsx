import CompanyIcon from '@/assets/icons/CompanyIcon';
import LogoutIcon from '@/assets/icons/SignoutIcon';
import StoreIcon from '@/assets/icons/Store';
import UserEditIcon from '@/assets/icons/UserEditIcon';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ROLES } from '@/constants/common-constants';
import { Permissions } from '@/constants/permissions';
import { ROUTES } from '@/constants/routes-constants';
import { logoutUser } from '@/redux/features/auth/authSlice';
import { useLogoutMutation } from '@/redux/features/tenant/tenant.api';
import { removeProfile } from '@/redux/features/user-options/loginProfileSlice';
import { RootState } from '@/redux/store';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useLocation } from 'react-router-dom';
import AppSpinner from '../app-spinner';
import { resetAllApiStates } from '@/lib/resetAllApiStates';

const ProfileMenu = () => {
  const profile = useSelector((state: RootState) => state.loginProfile.profile);
  const location = useLocation();
  const dispatch = useDispatch();
  const role = localStorage.getItem('role') || sessionStorage.getItem('role');
  const userPermissions = JSON.parse(
    localStorage.getItem('permissions') || '[]'
  );

  const [logout, { isLoading }] = useLogoutMutation();
  const handleLogout = () => {
    logout(null).unwrap();
    dispatch(logoutUser());
    dispatch(removeProfile());
    resetAllApiStates(dispatch);
  };

  // Generate initials from the first and last name
  const getInitials = (firstName: string, lastName?: string) => {
    const firstInitial = firstName?.charAt(0)?.toUpperCase();
    const lastInitial = lastName ? lastName?.charAt(0)?.toUpperCase() : '';
    return lastInitial ? `${firstInitial}${lastInitial}` : firstInitial;
  };

  // Function to check if the user has a specific permission
  const hasPermission = (permission: string) => {
    return userPermissions.some(
      (item: any) => item.permission === permission && item.enabled
    );
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Avatar className="w-12 cursor-pointer h-12">
            <AvatarImage
              src={profile?.profileImage}
              alt="profile Image"
              className="object-cover"
            />
            <AvatarFallback className="bg-[#04AAAF] text-white cursor-pointer">
              {getInitials(profile?.firstName ?? '', profile?.lastName)}
            </AvatarFallback>
          </Avatar>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          align="end"
          className="w-[300px] p-2 rounded-lg z-30"
        >
          {role !== ROLES.SUPER_ADMIN && (
            <Link
              to={ROUTES.USER_OPTIONS}
              state={{ referrer: location.pathname }}
            >
              <DropdownMenuItem className="pl-4 pr-4 pt-3 pb-3 text-base text-text-Default">
                <div className="flex flex-row gap-3 items-center">
                  <UserEditIcon className="text-text-Default" />
                  <span>User Options</span>
                </div>
              </DropdownMenuItem>
            </Link>
          )}

          {role !== ROLES.SUPER_ADMIN &&
            hasPermission(Permissions.AccessAllOptions) && (
              <Link to={ROUTES.COMPANY} state={{ referrer: location.pathname }}>
                <DropdownMenuItem className="pl-4 pr-4 pt-3 pb-3 text-base text-text-Default">
                  <div className="flex flex-row gap-3 items-center">
                    <CompanyIcon />
                    <span>Company Options</span>
                  </div>
                </DropdownMenuItem>
              </Link>
            )}

          {role !== ROLES.SUPER_ADMIN &&
            hasPermission(Permissions.AccessAllOptions) && (
              <Link to={ROUTES.STORE_OPTIONS}>
                <DropdownMenuItem className="pl-4 pr-4 pt-3 pb-3 text-base text-text-Default">
                  <div className="flex flex-row gap-3 items-center">
                    <StoreIcon />
                    <span>Store Options</span>
                  </div>
                </DropdownMenuItem>
              </Link>
            )}

          <DropdownMenuItem
            className="pl-4 pr-4 pt-3 pb-3 text-base text-text-Default"
            onClick={handleLogout}
          >
            <div className="flex flex-row gap-3 items-center text-text-danger">
              <LogoutIcon />
              <span>Sign Out</span>
            </div>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      <AppSpinner overlay isLoading={isLoading} />
    </>
  );
};

export default ProfileMenu;
