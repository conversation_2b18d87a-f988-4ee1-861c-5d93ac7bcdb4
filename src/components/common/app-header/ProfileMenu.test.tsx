import { render, screen } from '@testing-library/react';
import { Provider } from 'react-redux';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { vi, describe, test, expect, beforeEach } from 'vitest';
import ProfileMenu from './ProfileMenu';
import { ROLES } from '@/constants/common-constants';
import { configureStore, createSlice } from '@reduxjs/toolkit';

vi.mock('@/assets/icons/CompanyIcon', () => ({
  __esModule: true,
  default: () => <div data-testid="company-icon">CompanyIcon</div>,
}));

vi.mock('@/assets/icons/SignoutIcon', () => ({
  __esModule: true,
  default: () => <div data-testid="logout-icon">LogoutIcon</div>,
}));

vi.mock('@/assets/icons/UserEditIcon', () => ({
  __esModule: true,
  default: () => <div data-testid="user-edit-icon">UserEditIcon</div>,
}));

vi.mock('@/assets/icons/Store', () => ({
  __esModule: true,
  default: () => <div data-testid="store-icon">StoreIcon</div>,
}));

vi.mock('@/components/ui/avatar', () => ({
  Avatar: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="avatar">{children}</div>
  ),
  AvatarImage: ({ src }: { src: string }) => (
    <img data-testid="avatar-image" src={src} />
  ),
  AvatarFallback: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="avatar-fallback">{children}</div>
  ),
}));

vi.mock('@/components/ui/dropdown-menu', () => ({
  DropdownMenu: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="dropdown-menu">{children}</div>
  ),
  DropdownMenuContent: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="dropdown-content">{children}</div>
  ),
  DropdownMenuItem: ({
    children,
    onClick,
  }: {
    children: React.ReactNode;
    onClick: () => void;
  }) => (
    <div data-testid="dropdown-item" onClick={onClick}>
      {children}
    </div>
  ),
  DropdownMenuTrigger: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="dropdown-trigger">{children}</div>
  ),
}));

vi.mock('@/redux/features/auth/authSlice', () => ({
  logoutUser: vi.fn(),
}));

vi.mock('@/redux/features/tenant/tenant.api', () => ({
  useLogoutMutation: () => [vi.fn(), { isLoading: false }],
}));

vi.mock('@/redux/features/user-options/loginProfileSlice', () => ({
  removeProfile: vi.fn(),
}));

interface Profile {
  firstName: string;
  lastName: string;
  profileImage: string;
}

interface LoginProfileState {
  profile: Profile;
}

describe('ProfileMenu Component', () => {
  const mockProfile: Profile = {
    firstName: 'John',
    lastName: 'Doe',
    profileImage: 'https://example.com/image.jpg',
  };

  const loginProfileSlice = createSlice({
    name: 'loginProfile',
    initialState: { profile: mockProfile } as LoginProfileState,
    reducers: {},
  });

  const setupStore = (preloadedState?: { loginProfile: LoginProfileState }) => {
    return configureStore({
      reducer: {
        loginProfile: loginProfileSlice.reducer,
      },
      preloadedState,
    });
  };

  const setup = (
    role: string,
    preloadedState?: { loginProfile: LoginProfileState }
  ) => {
    localStorage.setItem('role', role);
    const store = setupStore(preloadedState);

    return render(
      <Provider store={store}>
        <BrowserRouter>
          <ProfileMenu />
        </BrowserRouter>
      </Provider>
    );
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('renders the profile avatar with initials as fallback', () => {
    setup(ROLES.NORMAL_USER);

    const avatarImage = screen.getByTestId('avatar-image');
    expect(avatarImage).toHaveAttribute('src', mockProfile.profileImage);

    const avatarFallback = screen.getByTestId('avatar-fallback');
    expect(avatarFallback).toHaveTextContent('JD');
  });

  test('renders the profile avatar with initials as fallback', () => {
    setup(ROLES.NORMAL_USER, { loginProfile: { profile: mockProfile } });

    const avatarImage = screen.getByTestId('avatar-image');
    expect(avatarImage).toHaveAttribute('src', mockProfile.profileImage);

    const avatarFallback = screen.getByTestId('avatar-fallback');
    expect(avatarFallback).toHaveTextContent('JD');
  });
});
