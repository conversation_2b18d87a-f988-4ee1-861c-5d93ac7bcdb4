import Logo from '@/assets/icons/Logo';
import { Menu } from 'lucide-react';
import { useState } from 'react';
import Sidebar from '../app-siderbar';
import HamburgerMenu from '../menu-slider';
import ProfileMenu from './ProfileMenu';

const Header = () => {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <header className="flex justify-between items-center h-16 fixed top-0 left-0 p-4 bg-brand-teal-Default right-0 z-[20]">
      {/* Hamburger Menu for Tablets and Mobile */}
      <div className="lg:hidden ">
        <HamburgerMenu
          side="left"
          openMenu={isExpanded}
          setOpenMenu={setIsExpanded}
          triggerComponent={<Menu className="text-white w-6 h-6" />}
          content={<Sidebar setOpenMenu={setIsExpanded} />}
        />
      </div>

      {/* Left Side: Logo and Title for larger screens */}
      <Logo />
      <div className="flex items-center">
        <ProfileMenu />
        {/* <HelpIcon />
        <MoonIcon />
        <SettingIcon /> */}
      </div>
    </header>
  );
};

export default Header;
