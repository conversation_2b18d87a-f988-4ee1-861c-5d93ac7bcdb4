import { render, screen, fireEvent } from '@testing-library/react';
import { vi, describe, test, expect, beforeEach } from 'vitest';
import Header from './index';

// Mock components
vi.mock('@/assets/icons/Logo', () => ({
  __esModule: true,
  default: () => <div data-testid="logo">Logo</div>,
}));

vi.mock('../menu-slider', () => ({
  __esModule: true,
  default: ({ openMenu, triggerComponent, content }: any) => (
    <div>
      <div data-testid="hamburger-menu">{triggerComponent}</div>
      {openMenu && <div data-testid="sidebar">{content}</div>}
    </div>
  ),
}));

vi.mock('./ProfileMenu', () => ({
  __esModule: true,
  default: () => <div data-testid="profile-menu">Profile Menu</div>,
}));

describe('Header Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('renders Logo, HamburgerMenu, and ProfileMenu', () => {
    render(<Header />);

    expect(screen.getByTestId('logo')).toBeInTheDocument();
    expect(screen.getByTestId('hamburger-menu')).toBeInTheDocument();
    expect(screen.getByTestId('profile-menu')).toBeInTheDocument();
  });

  test('toggles sidebar when hamburger menu is clicked', () => {
    render(<Header />);

    expect(screen.queryByTestId('sidebar')).not.toBeInTheDocument();
    const isClicked = fireEvent.click(screen.getByTestId('hamburger-menu'));
    expect(isClicked).toBe(true);
  });
});
