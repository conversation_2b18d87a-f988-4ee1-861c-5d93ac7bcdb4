import UploadIcon from '@/assets/icons/UploadIcon';
import AppButton from '@/components/common/app-button';
import { CircleProgress } from '@/components/ui/circular-progress';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { UseToast } from '@/components/ui/toast/ToastContainer';
import {
  DEFAULT_ACCEPTED_TYPES,
  DEFAULT_MAX_FILE_SIZE,
} from '@/constants/common-constants';
import { cn } from '@/lib/utils';
import { useUploadLinkedFilesMutation } from '@/redux/features/items/linkedFiles.api';
import React, { useCallback, useState } from 'react';
import TooltipWidget from './tooltip-widget';

// Types
interface FileUploadProps {
  isModalOpen: boolean;
  onClose: () => void;
  title?: string;
  maxFileSize?: number;
  acceptedFileTypes?: string[];
  uploadButtonLabel?: string;
  dragDropText?: string;
  url: string;
  payload?: Record<string, any>;
  onUpload?: () => void;
}

// Validation result type
interface FileValidationResult {
  isValid: boolean;
  error?: string;
}

const UploadFile = ({
  isModalOpen,
  onClose,
  title = 'Upload a Document',
  maxFileSize = DEFAULT_MAX_FILE_SIZE,
  acceptedFileTypes = DEFAULT_ACCEPTED_TYPES,
  uploadButtonLabel = 'Upload Document',
  dragDropText = 'Drag & Drop or click to upload a document',
  url,
  payload = {},
  onUpload,
}: FileUploadProps) => {
  // States
  const [isDragging, setIsDragging] = useState(false);
  const [file, setFile] = useState<File | null>(null);

  // Hooks
  const toast = UseToast();
  const [uploadFile, { isLoading }] = useUploadLinkedFilesMutation();

  // Utility functions
  const validateFile = useCallback(
    (file: File): FileValidationResult => {
      if (file.size > maxFileSize) {
        return {
          isValid: false,
          error: `File ${file.name} exceeds the size limit of ${(maxFileSize / (1024 * 1024)).toFixed(1)} MB.`,
        };
      }

      if (
        acceptedFileTypes[0] !== '*/*' &&
        !acceptedFileTypes.includes(file.type)
      ) {
        return {
          isValid: false,
          error: `File type ${file.type} is not supported.`,
        };
      }

      return { isValid: true };
    },
    [maxFileSize, acceptedFileTypes]
  );

  // Handle file selection
  const handleFileSelection = useCallback(
    (selectedFile: File) => {
      const validation = validateFile(selectedFile);

      if (!validation.isValid && validation.error) {
        toast.error(validation.error);
        return;
      }

      setFile(selectedFile);
    },
    [validateFile, toast]
  );

  // File input handler (for file input element)
  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) handleFileSelection(selectedFile);
  };

  // Drag and drop handlers
  const handleDrag = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragging(event.type === 'dragenter' || event.type === 'dragover');
  }, []);

  const handleDrop = useCallback(
    (event: React.DragEvent<HTMLDivElement>) => {
      event.preventDefault();
      event.stopPropagation();
      setIsDragging(false);

      const droppedFiles = Array.from(event.dataTransfer.files);
      if (droppedFiles.length > 1) {
        toast.error('Please drop only one file.');
        return;
      }
      handleFileSelection(droppedFiles[0]);
    },
    [handleFileSelection, toast]
  );

  // UI action handlers
  const removeFile = useCallback(() => {
    setFile(null);
  }, []);

  const handleDialogClose = useCallback(() => {
    onClose();
    setFile(null);
  }, [onClose]);

  const handleUpload = useCallback(async () => {
    if (!file) {
      toast.error('No file selected.');
      return;
    }

    const formData = new FormData();
    formData.append('file', file);

    Object.entries(payload).forEach(([key, value]) => {
      formData.append(key, value);
    });

    try {
      const response = await uploadFile({ url, body: formData }).unwrap();
      toast.success(response.message);
      onUpload?.();
      handleDialogClose();
    } catch (error) {
      // Handle error if needed
    } finally {
      setFile(null);
    }
  }, [file, payload, toast, uploadFile, url, onUpload, handleDialogClose]);

  // Render helper components
  const renderDropZone = () => (
    <div
      onDragEnter={handleDrag}
      onDragOver={handleDrag}
      onDragLeave={handleDrag}
      onDrop={handleDrop}
      onClick={() => document.getElementById('file-upload')?.click()}
      className={cn(
        'border-2 border-dashed flex flex-col justify-center items-center cursor-pointer rounded-lg text-center p-16 bg-background-default-hover',
        isDragging ? 'border-purple-500 bg-purple-50' : 'border-gray-300'
      )}
    >
      <UploadIcon />
      <span className="mt-2 text-text-tertiary font-normal text-base">
        {dragDropText}
      </span>
      <input
        type="file"
        className="hidden"
        data-testid="upload-file"
        id="file-upload"
        accept={acceptedFileTypes.join(',')}
        onChange={handleFileInput}
      />
    </div>
  );

  const renderFilePreview = () =>
    file && (
      <div className="flex flex-col items-center justify-center gap-4 p-6 rounded border border-dashed border-blue-300 bg-[#F5F5F5] w-full">
        <CircleProgress value={0} />

        <TooltipWidget content={file.name} className="max-w-80 break-words">
          <p className="text-base text-text-Default font-semibold truncate text-center max-w-[400px]">
            {file.name}
          </p>
        </TooltipWidget>

        <span className="text-sm text-gray-500">
          {`${(file.size / 1024).toFixed(2)} KB`}
        </span>
        <AppButton
          label="Remove"
          className="w-full"
          variant="danger"
          onClick={removeFile}
          disabled={isLoading}
        />
      </div>
    );

  return (
    <Dialog open={isModalOpen} onOpenChange={handleDialogClose}>
      <DialogContent
        className="bg-white p-0 max-w-[570px] border-0"
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
      >
        <DialogHeader className="pl-6 pr-6 pb-3 pt-3 bg-background-default-hover rounded-t-md">
          <DialogTitle className="text-text-Default font-normal text-xl">
            {title}
          </DialogTitle>
        </DialogHeader>

        <div className="flex flex-col gap-6 p-6 w-full">
          {!file && renderDropZone()}
          {file && renderFilePreview()}

          <AppButton
            label={uploadButtonLabel}
            className="w-full"
            isLoading={isLoading}
            disabled={!file}
            onClick={handleUpload}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default UploadFile;
