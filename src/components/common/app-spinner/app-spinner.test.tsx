import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import AppSpinner from './';

describe('AppSpinner', () => {
  it('renders correctly and takes up full screen', () => {
    render(<AppSpinner />);

    // Get the main container div
    const container = screen.getByTestId('spinner-container');

    // Check if the container has the correct classes for full screen and centering
    expect(container.classList.contains('flex')).toBe(true);
    expect(container.classList.contains('items-center')).toBe(true);
    expect(container.classList.contains('justify-center')).toBe(true);
    expect(container.classList.contains('w-full')).toBe(true);
    expect(container.classList.contains('h-screen')).toBe(true);
  });

  it('renders the spinner with correct styling', () => {
    render(<AppSpinner />);

    // Get the spinner div
    const spinner = screen.getByTestId('spinner');

    // Check spinner dimensions
    expect(spinner.classList.contains('h-12')).toBe(true);
    expect(spinner.classList.contains('w-12')).toBe(true);

    // Check spinner border styling
    expect(spinner.classList.contains('rounded-full')).toBe(true);
    expect(spinner.classList.contains('border-2')).toBe(true);
    expect(spinner.classList.contains('border-solid')).toBe(true);
    expect(spinner.classList.contains('border-[#07BCC2]')).toBe(true);
    expect(spinner.classList.contains('border-t-transparent')).toBe(true);

    // Check animation
    expect(spinner.classList.contains('animate-spin')).toBe(true);
  });

  it('is accessible', () => {
    render(<AppSpinner />);

    // Check for role attribute
    const spinner = screen.getByRole('status');
    expect(spinner).toBeTruthy();

    // Check for aria-label
    expect(spinner.getAttribute('aria-label')).toBe('Loading');
  });
});
