import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { cn } from '@/lib/utils';

interface AppSpinnerProps {
  className?: string;
  overlay?: boolean;
  message?: string;
  isLoading?: boolean;
}
const AppSpinner = ({
  isLoading,
  overlay,
  className,
  message,
}: AppSpinnerProps) => {
  if (overlay) {
    return (
      <AlertDialog open={isLoading}>
        <AlertDialogContent className="p-0 w-auto border-0 shadow-none">
          <AlertDialogHeader
            className={cn('p-4 flex items-center justify-center')}
          >
            <AlertDialogTitle className="border-0">
              <div
                data-testid="spinner"
                role="status"
                aria-label="Loading"
                className={cn(
                  'h-12 w-12 rounded-full border-2 border-solid border-[#07BCC2] border-t-transparent animate-spin',
                  className
                )}
              ></div>
            </AlertDialogTitle>
            <AlertDialogDescription>{message}</AlertDialogDescription>
          </AlertDialogHeader>
        </AlertDialogContent>
      </AlertDialog>
    );
  }

  return (
    <div
      data-testid="spinner-container"
      className={cn(
        'flex items-center justify-center w-full h-screen',
        className
      )}
    >
      <div
        data-testid="spinner"
        role="status"
        aria-label="Loading"
        className={cn(
          'h-12 w-12 rounded-full border-2 border-solid border-[#07BCC2] border-t-transparent animate-spin',
          className
        )}
      ></div>
    </div>
  );
};

export default AppSpinner;
