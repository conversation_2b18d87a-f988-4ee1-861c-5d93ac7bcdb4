import {
  Bread<PERSON>rumb,
  Bread<PERSON>rumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { cn } from '@/lib/utils';
import { BreadcrumbType } from '@/types/common.types';
import React from 'react';

type BreadcrumbWithCustomSeparatorProps = {
  breadcrumbs: BreadcrumbType[];
  goBack: (value: string) => void;
  activeValue?: string; // Optional prop to specify the active breadcrumb
};

const BreadcrumbWithCustomSeparator: React.FC<
  BreadcrumbWithCustomSeparatorProps
> = ({ breadcrumbs, goBack, activeValue }) => (
  <Breadcrumb>
    <BreadcrumbList>
      {breadcrumbs.map(({ value, label }, index) => (
        <React.Fragment key={value}>
          <BreadcrumbItem>
            <BreadcrumbLink
              onClick={() => goBack(value)}
              className={cn(
                'cursor-pointer',
                value === activeValue
                  ? 'font-bold text-foreground'
                  : 'text-muted-foreground hover:text-foreground'
              )}
            >
              {label}
            </BreadcrumbLink>
          </BreadcrumbItem>
          {index < breadcrumbs.length - 1 && (
            <BreadcrumbSeparator>/</BreadcrumbSeparator>
          )}
        </React.Fragment>
      ))}
    </BreadcrumbList>
  </Breadcrumb>
);

export default BreadcrumbWithCustomSeparator;
