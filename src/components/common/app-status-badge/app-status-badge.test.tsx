import { render, screen } from '@testing-library/react';
import StatusBadge from './index';
import { describe, expect, it } from 'vitest';

describe('StatusBadge Component', () => {
  it('should display active text when status is true', () => {
    render(
      <StatusBadge status={true} activeText="Active" inactiveText="Inactive" />
    );
    expect(screen.getByText('Active')).toBeInTheDocument();
  });

  it('should display inactive text when status is false', () => {
    render(
      <StatusBadge status={false} activeText="Active" inactiveText="Inactive" />
    );
    expect(screen.getByText('Inactive')).toBeInTheDocument();
  });

  it('should apply the active color when status is true', () => {
    render(<StatusBadge status={true} />);
    const button = screen.getByRole('button');
    expect(button).toHaveClass('bg-green-500');
  });

  it('should apply the inactive color when status is false', () => {
    render(<StatusBadge status={false} />);
    const button = screen.getByRole('button');
    expect(button).toHaveClass('bg-red-500');
  });

  it('should apply custom active color when passed as prop', () => {
    render(<StatusBadge status={true} activeColor="bg-blue-500" />);
    const button = screen.getByRole('button');
    expect(button).toHaveClass('bg-blue-500');
  });

  it('should apply custom inactive color when passed as prop', () => {
    render(<StatusBadge status={false} inactiveColor="bg-yellow-500" />);
    const button = screen.getByRole('button');
    expect(button).toHaveClass('bg-yellow-500');
  });

  it('should apply custom className when passed as prop', () => {
    render(<StatusBadge status={true} className="custom-class" />);
    const button = screen.getByRole('button');
    expect(button).toHaveClass('custom-class');
  });

  it('should apply the correct hover effect based on status', () => {
    const { rerender } = render(<StatusBadge status={true} />);
    const button = screen.getByRole('button');
    expect(button).toHaveClass('hover:bg-green-500');

    rerender(<StatusBadge status={false} />);
    expect(button).toHaveClass('hover:bg-red-500');
  });
});
