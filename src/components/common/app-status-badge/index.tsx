import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface StatusBadgeProps {
  status: boolean;
  activeText?: string;
  inactiveText?: string;
  activeColor?: string;
  inactiveColor?: string;
  className?: string;
}
const StatusBadge = ({
  status,
  activeText = 'Active',
  inactiveText = 'Inactive',
  activeColor = 'bg-green-500',
  inactiveColor = 'bg-red-500',
  className,
}: StatusBadgeProps) => {
  return (
    <Button
      className={cn(
        `h-8 w-20 px-4 py-0 rounded-full hover:${status ? activeColor : inactiveColor}`,
        status ? activeColor : inactiveColor,
        className
      )}
    >
      {status ? activeText : inactiveText}
    </Button>
  );
};

export default StatusBadge;
