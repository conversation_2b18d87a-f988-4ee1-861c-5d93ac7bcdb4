import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import { useForm, FormProvider } from 'react-hook-form';

interface TestFormValues {
  acceptTerms: boolean;
}

interface TestFormProps {
  defaultValues?: TestFormValues;
  onSubmit?: (data: TestFormValues) => void;
  children?: React.ReactNode;
}

const TestForm = ({
  defaultValues = { acceptTerms: false },
  onSubmit,
  children,
}: TestFormProps) => {
  const methods = useForm<TestFormValues>({ defaultValues });
  return (
    <FormProvider {...methods}>
      <form onSubmit={methods.handleSubmit(onSubmit || (() => {}))}>
        {children}
        <button type="submit">Submit</button>
      </form>
    </FormProvider>
  );
};

describe('AppCheckbox Component', () => {
  it('renders the checkbox with a label', () => {
    render(<TestForm />);

    expect(screen.getByText('Submit')).toBeInTheDocument();
  });

  it('displays validation error when required and not checked', async () => {
    render(<TestForm />);

    const isSubmitted = fireEvent.submit(screen.getByText('Submit'));
    expect(isSubmitted);
  });
});
