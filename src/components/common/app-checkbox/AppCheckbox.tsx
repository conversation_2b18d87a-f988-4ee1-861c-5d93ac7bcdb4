import * as CheckboxPrimitive from '@radix-ui/react-checkbox';
import { Check } from 'lucide-react';
import {
  useController,
  type Control,
  type FieldValues,
  type Path,
} from 'react-hook-form';

interface CheckboxProps<T extends FieldValues> {
  name: Path<T>;
  control: Control<T>;
  label?: string;
  defaultChecked?: boolean;
  disabled?: boolean;
  className?: string;
  required?: boolean;
  onChange?: (checked: boolean) => void;
}

const AppCheckbox = <T extends FieldValues>({
  name,
  control,
  label,
  defaultChecked = false,
  disabled = false,
  className = '',
  required = false,
  onChange,
}: CheckboxProps<T>) => {
  const {
    field: { value, onChange: fieldOnChange },
    fieldState: { error },
  } = useController({
    name,
    control,
    defaultValue: defaultChecked as any,
    rules: { required: required ? 'Required' : false },
  });

  const handleChange = (checked: boolean) => {
    fieldOnChange(checked);
    onChange?.(checked);
  };

  return (
    <div className="flex items-center space-x-2">
      <CheckboxPrimitive.Root
        id={name}
        checked={value}
        onCheckedChange={handleChange}
        disabled={disabled}
        className={`
          peer h-4 w-4 shrink-0 rounded border border-primary shadow
          focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2
          disabled:cursor-not-allowed disabled:opacity-50
          ${className}
          ${error ? 'border-red-500' : ''}
        `}
      >
        <CheckboxPrimitive.Indicator className="flex items-center justify-center text-current">
          <Check className="h-3 w-3" />
        </CheckboxPrimitive.Indicator>
      </CheckboxPrimitive.Root>
      {label && (
        <label
          htmlFor={name}
          className={`text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70
            ${error ? 'text-red-500' : 'text-gray-900'}
          `}
        >
          {label}
        </label>
      )}
      {error && (
        <span className="text-xs text-red-500 mt-1">{error.message}</span>
      )}
    </div>
  );
};

export default AppCheckbox;
