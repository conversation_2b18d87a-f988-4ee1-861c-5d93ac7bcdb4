import { render, screen, fireEvent } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import Tag from '.';

const TAG_STYLES = {
  variants: {
    primary: 'bg-brand-teal-Default text-white hover:bg-[#04AAAF]',
    secondary: 'bg-white text-[#012F31]',
    positive: 'bg-[#14AE5C] text-[#EBFFEE]',
    danger: 'bg-[#EC221F] text-[#FEE9E7]',
    warning: 'bg-[#E8B931] text-[#401B01]',
    neutral: 'bg-[#D9D9D9] text-[#1E1E1E]',
  },
};

type Variant =
  | 'primary'
  | 'secondary'
  | 'positive'
  | 'danger'
  | 'warning'
  | 'neutral';

describe('Tag Component', () => {
  it('renders the tag with children', () => {
    render(<Tag>Test Tag</Tag>);

    expect(screen.getByText('Test Tag')).toBeInTheDocument();
  });

  it('renders with the correct variant styles', () => {
    const { rerender } = render(<Tag>Primary Tag</Tag>);

    // Check default variant (primary)
    expect(screen.getByText('Primary Tag')).toHaveClass(
      'bg-brand-teal-Default'
    );

    // Check other variants
    const variants: Variant[] = [
      'secondary',
      'positive',
      'danger',
      'warning',
      'neutral',
    ];
    variants.forEach((variant) => {
      rerender(<Tag variant={variant}>{`${variant} Tag`}</Tag>);
      expect(screen.getByText(`${variant} Tag`)).toHaveClass(
        TAG_STYLES.variants[variant]
      );
    });
  });

  it('calls onClick when the remove icon is clicked', () => {
    const handleClick = vi.fn();
    render(<Tag onClick={handleClick}>Removable Tag</Tag>);

    const cancelIcon = screen.getByLabelText(/cancel icon/i);
    // Assuming the icon has a role of button
    fireEvent.click(cancelIcon);

    expect(handleClick).toHaveBeenCalled();
  });

  it('does not render remove icon if remove is false', () => {
    render(<Tag remove={false}>Non-removable Tag</Tag>);

    expect(screen.queryByRole('button')).not.toBeInTheDocument();
  });
});
