import CancelIcon from '@/assets/icons/CancelIcon';
import { cn } from '@/lib/utils';

interface TagProps {
  onClick?: () => void;
  children: React.ReactNode;
  variant?:
    | 'primary'
    | 'secondary'
    | 'positive'
    | 'danger'
    | 'neutral'
    | 'warning';

  remove?: boolean;
}

const TAG_STYLES = {
  base: 'border-[1px] border-brand-teal p-2 rounded-[8px] flex items-center gap-x-2 cursor-pointer',
  variants: {
    primary: 'bg-brand-teal-Default text-white hover:bg-[#04AAAF]',
    secondary: 'bg-white text-[#012F31]',
    positive: 'bg-[#14AE5C] text-[#EBFFEE]',
    danger: 'bg-[#EC221F] text-[#FEE9E7]',
    warning: 'bg-[#E8B931] text-[#401B01]',
    neutral: 'bg-[#D9D9D9] text-[#1E1E1E]',
  },
  iconColors: {
    primary: '#FFFFFF',
    secondary: '#04AAAF',
    positive: '#EBFFEE',
    danger: '#FEE9E7',
    warning: '#401B01',
    neutral: '#1E1E1E',
  },
};

const Tag: React.FC<TagProps> = ({
  onClick,
  children,
  variant = 'primary',
  remove = true,
}) => {
  const variantStyles =
    TAG_STYLES.variants[variant] || TAG_STYLES.variants.primary;
  const iconColor =
    TAG_STYLES.iconColors[variant] || TAG_STYLES.iconColors.primary;

  return (
    <div className={cn(TAG_STYLES.base, variantStyles)}>
      {children}
      {remove && (
        <CancelIcon
          iconColor={iconColor}
          className="cursor-pointer"
          onClick={onClick}
        />
      )}
    </div>
  );
};

export default Tag;
