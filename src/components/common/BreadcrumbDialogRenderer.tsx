import React, { useMemo } from 'react';
import CustomDialog from './dialog';
import BreadcrumbWithCustomSeparator from './BreadCrumbWithSeparator';

// Type for each list item in the dialog
interface ListItem {
  label: string;
  value: string;
  content: React.ReactNode;
}

// Type for the breadcrumb structure
export type Breadcrumb = {
  label: string;
  value: string;
};

// Props for the BreadcrumbDialogRenderer component
interface BreadcrumbDialogRendererProps {
  listItems: ListItem[];
  activeTab: string;
  className?: string;
  contentClassName?: string;
  isOpen: boolean;
  onOpenChange: (isOpen?: boolean) => void;
  setActiveTab: (tabValue: string) => void;
  onBreadcrumbClick?: (value: string) => void;
  autoFocus?: boolean;
}

// Main dialog component
const BreadcrumbDialogRenderer = ({
  listItems,
  isOpen,
  onOpenChange,
  contentClassName,
  className,
  activeTab,
  setActiveTab,
  autoFocus = true,
}: BreadcrumbDialogRendererProps) => {
  // Map the list items to create a dictionary for quick lookup of content
  const contentMap = new Map(
    listItems.map((item) => [item.value, item.content])
  );

  // Get the content for the active tab
  const activeTabContent = contentMap.get(activeTab);

  // Get the active item to retrieve the label (tab name)
  const activeTabValue = listItems.find((item) => item.value === activeTab);

  // If active tab exists, get its label, otherwise fallback to empty string
  const dialogTitle = activeTabValue ? activeTabValue.label : '';

  // Handler for navigating back to a previous tab
  const handleGoBack = (tabValue: string) => {
    setActiveTab(tabValue);
  };

  // Memoized breadcrumbs for navigation
  const breadcrumbs = useMemo(() => {
    const activeTabIndex = listItems.findIndex(
      (item) => item.value === activeTab
    );
    // Create breadcrumbs up to the current active tab
    return activeTabIndex >= 0
      ? listItems
          .slice(0, activeTabIndex + 1)
          .map(({ label, value }) => ({ label, value }))
      : [];
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeTab]);

  return (
    <CustomDialog
      onOpenChange={onOpenChange}
      description=""
      open={isOpen}
      className={className}
      contentClassName={contentClassName}
      title={dialogTitle}
      autoFocus={autoFocus}
    >
      <div className="pl-6 pr-6 flex flex-col gap-4 h-full w-full">
        {/* Breadcrumb navigation */}
        <BreadcrumbWithCustomSeparator
          breadcrumbs={breadcrumbs.length > 1 ? breadcrumbs : []}
          goBack={handleGoBack}
          activeValue={activeTab} // Pass the active tab value
        />

        {/* Content of the active tab */}
        <div className="grid grid-cols-1">{activeTabContent}</div>
      </div>
    </CustomDialog>
  );
};

export default BreadcrumbDialogRenderer;
