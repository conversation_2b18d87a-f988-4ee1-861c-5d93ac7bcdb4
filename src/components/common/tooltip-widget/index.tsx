import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { ReactNode } from 'react';
import { Portal as RadixTooltipPortal } from '@radix-ui/react-tooltip';

type TooltipWidgetProps = {
  children: ReactNode;
  tooltip?: React.ReactNode | string;
  content?: React.ReactNode | string;
  className?: string;
  side?: 'left' | 'right' | 'top' | 'bottom';
  align?: 'center' | 'end' | 'start';
};

const TooltipWidget = ({
  children,
  tooltip,
  content,
  className,
  side,
  align,
}: TooltipWidgetProps) => {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>{children}</TooltipTrigger>
        {(content || tooltip) && (
          <RadixTooltipPortal>
            <TooltipContent
              side={side}
              className={cn('bg-card text-black', className)}
              align={align}
            >
              {content && content}
              {tooltip && (
                <ul
                  style={{
                    listStyleType: 'disc',
                    fontSize: '14px',
                    color: 'black',
                  }}
                >
                  {tooltip}
                </ul>
              )}
            </TooltipContent>
          </RadixTooltipPortal>
        )}
      </Tooltip>
    </TooltipProvider>
  );
};

export default TooltipWidget;
