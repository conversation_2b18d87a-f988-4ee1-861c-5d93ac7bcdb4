import { render, screen, fireEvent } from '@testing-library/react';
import TooltipWidget from './index';
import { describe, expect, it } from 'vitest';

describe('TooltipWidget Component', () => {
  it('should render with tooltip content', () => {
    const tooltipContent = 'This is a tooltip';
    const children = <button>Hover me</button>;

    render(
      <TooltipWidget tooltip={tooltipContent} content="Additional content">
        {children}
      </TooltipWidget>
    );

    expect(screen.queryByText(tooltipContent)).not.toBeInTheDocument();
    fireEvent.mouseOver(screen.getByText('Hover me'));
  });

  it('should render with provided content', () => {
    const content = 'This is additional content';
    const children = <button>Hover me</button>;

    render(
      <TooltipWidget content={content} tooltip="Tooltip">
        {children}
      </TooltipWidget>
    );

    const isMouseHover = fireEvent.mouseOver(screen.getByText('Hover me'));
    expect(isMouseHover);
  });

  it('should apply the custom className to the tooltip content', () => {
    const children = <button>Hover me</button>;
    const className = 'custom-tooltip';

    render(
      <TooltipWidget
        tooltip="Tooltip"
        content="Additional content"
        className={className}
      >
        {children}
      </TooltipWidget>
    );

    const isHover = fireEvent.mouseOver(screen.getByText('Hover me'));
    expect(isHover);
  });
});
