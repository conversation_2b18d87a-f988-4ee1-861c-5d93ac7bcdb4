import { render, screen, fireEvent } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import AccessDenied from './AccessDenied';
import { describe, expect, it, vi } from 'vitest';

describe('AccessDenied Component', () => {
  it('should render AccessDenied page correctly', () => {
    render(
      <MemoryRouter>
        <AccessDenied />
      </MemoryRouter>
    );

    expect(screen.getByText('Access Denied')).toBeInTheDocument();
    expect(
      screen.getByText(
        "Sorry, but you don't have permission to access this page. Please contact your administrator."
      )
    ).toBeInTheDocument();

    expect(screen.getByRole('button', { name: 'Go Back' })).toBeInTheDocument();
  });

  it('should navigate to the home route when "Go Back" button is clicked', () => {
    const mockNavigate = vi.fn();
    render(
      <MemoryRouter>
        <AccessDenied />
      </MemoryRouter>
    );

    const navigateButton = screen.getByRole('button', { name: 'Go Back' });
    fireEvent.click(navigateButton);
    expect(mockNavigate);
  });
});
