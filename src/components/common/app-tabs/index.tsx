import React from 'react';
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import '../../../index.css';
import { cn } from '@/lib/utils';

type Tab = {
  value: string;
  label: React.ReactNode;
  content: React.ReactNode;
};

interface AppTabsProps {
  tabs: Tab[];
  className?: string;
  activeTab?: string;
  handleTabClick?: (value: string) => void | undefined;
  header?: React.ReactNode;
  tabClassName?: string;
}

const AppTabs: React.FC<AppTabsProps> = ({
  tabs,
  className,
  activeTab,
  handleTabClick,
  header,
  tabClassName,
}) => {
  return (
    <Tabs
      value={activeTab}
      onValueChange={handleTabClick}
      className={'p-0 m-0 flex flex-col gap-4'}
    >
      <div className={cn('flex flex-row gap-0 items-center', tabClassName)}>
        {/* Scrollable tab list section */}
        <div className="flex-1 overflow-x-auto">
          <TabsList
            className={cn(
              className,
              'p-0 rounded-lg flex flex-row justify-stretch bg-transparent'
            )}
          >
            {/* Static header section */}
            {header}

            {tabs.map(({ value, label }) => (
              <TabsTrigger
                key={value}
                value={value}
                className="w-full pr-5 pl-5 h-full border-b-2  text-text-neutral-tertiary rounded-none data-[state=active]:border-b-2 data-[state=active]:border-text-brand-violet-Default data-[state=active]:bg-background-brand-violet-tertiary data-[state=active]:hover:bg-white data-[state=active]:text-brand-violet font-normal text-base [&[data-state=active]_svg]:text-text-brand-violet-Default"
              >
                {label}
              </TabsTrigger>
            ))}
          </TabsList>
        </div>
      </div>

      {/* Tab content */}
      {tabs.map(({ value, content }) => (
        <TabsContent key={value} value={value}>
          {content}
        </TabsContent>
      ))}
    </Tabs>
  );
};

export default AppTabs;
