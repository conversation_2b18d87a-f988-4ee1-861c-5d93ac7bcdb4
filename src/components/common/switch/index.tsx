import Labels from '@/components/forms/Label';
import { Switch } from '@/components/ui/switch';
import { cn } from '@/lib/utils';
import { Controller, FieldValues, Path, UseFormReturn } from 'react-hook-form';

interface SwitchFieldProps<T extends FieldValues> {
  name: Path<T>;
  form: UseFormReturn<T>;
  label?: string;
  labelEnd?: string;
  className?: string;
  description?: string;
  onChange?: (value: boolean) => void;
  disabled?: boolean;
  switchClassName?: string;
}

const SwitchField = <T extends FieldValues>({
  label,
  labelEnd,
  className,
  description,
  form,
  name,
  onChange,
  disabled,
  switchClassName,
}: SwitchFieldProps<T>) => {
  return (
    <div className={cn('flex flex-col gap-2', className)}>
      <div
        className={cn(
          'flex justify-between items-center w-full gap-x-3',
          switchClassName
        )}
      >
        {label && <Labels label={label} htmlFor={name} />}
        <Controller
          name={name}
          control={form.control}
          render={({ field: { value, onChange: fieldControllerOnChange } }) => (
            <Switch
              id={name}
              disabled={disabled}
              checked={value}
              onCheckedChange={(checked) => {
                fieldControllerOnChange(checked);
                if (onChange) {
                  onChange(checked);
                }
              }}
              className="data-[state=checked]:bg-brand-teal-Default data-[state=unchecked]:bg-background data-[state=unchecked]:border-[#B2B2B2]"
              knobClassName="data-[state=unchecked]:bg-[#B3B3B3] data-[state=checked]:bg-white"
            />
          )}
        />
        {labelEnd && <Labels label={labelEnd} htmlFor={name} />}
      </div>
      {description && (
        <div className="text-base font-normal text-[#757575] w-[90%]">
          {description}
        </div>
      )}
    </div>
  );
};

export default SwitchField;
