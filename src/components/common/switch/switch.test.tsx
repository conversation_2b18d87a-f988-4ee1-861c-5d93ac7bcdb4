import React from 'react';
import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import {
  useForm,
  FormProvider,
  UseFormReturn,
  FieldValues,
  UseFormProps,
} from 'react-hook-form';
import SwitchField from '.';
import { beforeAll } from 'vitest';

// Mock the cn function
vi.mock('@/lib/utils', () => ({
  cn: (...args: any[]) => args.filter(Boolean).join(' '),
}));

// Test wrapper component to provide form context
interface TestWrapperProps<TFieldValues extends FieldValues = FieldValues> {
  children: (form: UseFormReturn<TFieldValues>) => React.ReactElement;
  formProps?: UseFormProps<TFieldValues>;
}

function TestWrapper<TFieldValues extends FieldValues = FieldValues>({
  children,
  formProps,
}: TestWrapperProps<TFieldValues>) {
  const methods = useForm<TFieldValues>(formProps);
  return <FormProvider {...methods}>{children(methods)}</FormProvider>;
}

describe('SwitchField Component', () => {
  beforeAll(() => {
    window.HTMLElement.prototype.scrollIntoView = function () {};

    global.ResizeObserver = class ResizeObserver {
      observe() {
        // do nothing
      }
      unobserve() {
        // do nothing
      }
      disconnect() {
        // do nothing
      }
    };
  });
  it('renders with label correctly', () => {
    render(
      <TestWrapper<{ testSwitch: boolean }>>
        {(form) => (
          <SwitchField name="testSwitch" form={form} label="Test Label" />
        )}
      </TestWrapper>
    );

    expect(screen.getByText('Test Label')).toBeInTheDocument();
    expect(screen.getByRole('switch')).toBeInTheDocument();
  });

  it('renders with description correctly', () => {
    render(
      <TestWrapper<{ testSwitch: boolean }>>
        {(form) => (
          <SwitchField
            name="testSwitch"
            form={form}
            label="Test Label"
            description="Test Description"
          />
        )}
      </TestWrapper>
    );

    expect(screen.getByText('Test Description')).toBeInTheDocument();
  });

  it('handles form registration and value changes correctly', async () => {
    const onSubmit = vi.fn();
    let formInstance: UseFormReturn<{ testSwitch: boolean }>;

    render(
      <TestWrapper<{ testSwitch: boolean }>
        formProps={{
          defaultValues: { testSwitch: false },
        }}
      >
        {(form) => {
          formInstance = form;
          return (
            <form onSubmit={form.handleSubmit(onSubmit)} data-testid="form">
              <SwitchField name="testSwitch" form={form} label="Test Switch" />
              <button type="submit">Submit</button>
            </form>
          );
        }}
      </TestWrapper>
    );

    const switchElement = screen.getByRole('switch');
    const form = screen.getByTestId('form');

    // Click the switch to change its value
    fireEvent.click(switchElement);

    // Wait for form values to update
    await waitFor(() => {
      expect(formInstance.getValues().testSwitch).toBe(true);
    });

    // Submit the form
    fireEvent.submit(form);

    // Verify the form submission
    await waitFor(() => {
      expect(onSubmit).toHaveBeenCalledWith(
        { testSwitch: true },
        expect.anything()
      );
    });
  });

  it('applies correct styling classes', () => {
    render(
      <TestWrapper<{ testSwitch: boolean }>>
        {(form) => <SwitchField name="testSwitch" form={form} />}
      </TestWrapper>
    );

    const switchElement = screen.getByRole('switch');
    expect(switchElement).toHaveClass('data-[state=unchecked]:bg-background');
  });

  it('renders with both label and description', () => {
    render(
      <TestWrapper<{ testSwitch: boolean }>>
        {(form) => (
          <SwitchField
            name="testSwitch"
            form={form}
            label="Test Label"
            description="Test Description"
          />
        )}
      </TestWrapper>
    );

    expect(screen.getByText('Test Label')).toBeInTheDocument();
    expect(screen.getByText('Test Description')).toBeInTheDocument();
    expect(screen.getByRole('switch')).toBeInTheDocument();
  });

  it('works without optional props', () => {
    render(
      <TestWrapper<{ testSwitch: boolean }>>
        {(form) => <SwitchField name="testSwitch" form={form} />}
      </TestWrapper>
    );

    expect(screen.getByRole('switch')).toBeInTheDocument();
    expect(screen.queryByText('Test Label')).not.toBeInTheDocument();
    expect(screen.queryByText('Test Description')).not.toBeInTheDocument();
  });
});
