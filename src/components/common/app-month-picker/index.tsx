// import { But<PERSON> } from '@/components/ui/button';
// import {
//   Popover,
//   PopoverContent,
//   PopoverTrigger,
// } from '@/components/ui/popover';
// import { cn, formatDate } from '@/lib/utils';
// import { CalendarIcon } from 'lucide-react';
// import { memo, useState } from 'react';
// import { Controller, FieldValues, Path, UseFormReturn } from 'react-hook-form';
// import { MonthPicker } from './monthpicker';
// import Labels from '@/components/forms/Label';

// interface MonthPickerInfoProps<T extends FieldValues> {
//   name: Path<T>;
//   form: UseFormReturn | any;
//   label?: string;
//   placeholder?: string;
//   disabled?: boolean;
//   className?: string;
//   onMonthChange?: (date: Date | string | undefined) => void;
// }

// const MonthPickerInfo = <T extends FieldValues>({
//   name,
//   form,
//   label,
//   placeholder = 'Select Month',
//   disabled = false,
//   className,
//   onMonthChange,
// }: MonthPickerInfoProps<T>) => {
//   const [open, setOpen] = useState<boolean>(false);
//   const [errorText, setErrorText] = useState<string>('');

//   return (
//     <div className={cn('flex flex-col gap-2', className)}>
//       {label && <Labels htmlFor={name} label={label} />}
//       <Controller
//         control={form.control}
//         name={name}
//         render={({ field: { value, onChange } }) => {
//           return (
//             <Popover open={open} onOpenChange={setOpen}>
//               <PopoverTrigger asChild>
//                 <Button
//                   variant={'outline'}
//                   disabled={disabled}
//                   className={cn(
//                     'w-full flex justify-start font-normal border-border-Default placeholder:text-[#B3B3B3]',
//                     className
//                   )}
//                 >
//                   <CalendarIcon className="mr-2 h-4 w-4" />
//                   {value ? formatDate(value, 'MMM YYYY') : placeholder}
//                 </Button>
//               </PopoverTrigger>

//               <PopoverContent className="w-auto p-0">
//                 <MonthPicker
//                   onMonthSelect={(newDate) => {
//                     onChange(newDate);
//                     onMonthChange?.(newDate);
//                     setErrorText('');
//                     setOpen(false);
//                   }}
//                   selectedMonth={value}
//                 />
//               </PopoverContent>
//             </Popover>
//           );
//         }}
//       />
//       {errorText && (
//         <p className="text-sm font-normal text-danger">{errorText}</p>
//       )}
//     </div>
//   );
// };

// export default memo(MonthPickerInfo);

import Labels from '@/components/forms/Label';
import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn, formatDate } from '@/lib/utils';
import dayjs from 'dayjs';
import { CalendarIcon } from 'lucide-react';
import { memo, useState } from 'react';
import { Controller, FieldValues, Path, UseFormReturn } from 'react-hook-form';
import { PatternFormat } from 'react-number-format';
import { MonthPicker } from './monthpicker';
export const validateMonthInput = (value: string) => {
  const regex = /^(0[1-9]|1[0-2])\/\d{4}$/;
  return regex.test(value);
};

interface MonthPickerInfoProps<T extends FieldValues> {
  name: Path<T>;
  form: UseFormReturn<T> | any;
  label?: string;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  onMonthChange?: (date: Date | string | undefined) => void;
  fromYear?: number;
  toYear?: number;
  enableInput?: boolean;
}

const MonthPickerInfo = <T extends FieldValues>({
  name,
  form,
  label,
  placeholder = 'MM/YYYY',
  disabled = false,
  className,
  onMonthChange,
  fromYear = 1960,
  toYear = 10,
  enableInput = false,
}: MonthPickerInfoProps<T>) => {
  const [open, setOpen] = useState<boolean>(false);
  const [errorText, setErrorText] = useState<string>('');
  const startYear = fromYear;
  const endYear = new Date().getFullYear() + toYear;

  return (
    <div className={cn('flex flex-col gap-2', className)}>
      {label && <Labels htmlFor={name} label={label} />}
      <Controller
        control={form.control}
        name={name}
        render={({ field: { value, onChange } }) => {
          const parsedDate =
            value && dayjs(value).isValid() ? dayjs(value).toDate() : undefined;

          return (
            <Popover open={open} onOpenChange={setOpen}>
              {enableInput ? (
                <div className="relative w-full">
                  <PatternFormat
                    format="##/####"
                    allowEmptyFormatting
                    mask="_"
                    className={cn(
                      'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:bg-[#F5F5F5]',
                      errorText ? 'border-danger' : ''
                    )}
                    isAllowed={({ formattedValue }) => {
                      const [mmStr, yyyyStr] = formattedValue.split('/');
                      const mm = Number(mmStr);
                      const yyyy = Number(yyyyStr);

                      if (mmStr && (mm < 1 || mm > 12)) return false;
                      if (yyyyStr && (yyyy < startYear || yyyy > endYear))
                        return false;

                      return true;
                    }}
                    onValueChange={({ formattedValue }) => {
                      const value = formattedValue?.replace(/[^0-9+]/g, '');
                      if (!value) return;

                      const [mmStr, yyyyStr] = formattedValue.split('/');
                      const mm = Number(mmStr);
                      const yyyy = Number(yyyyStr);

                      if (
                        mmStr.length === 2 &&
                        yyyyStr.length === 4 &&
                        mm >= 1 &&
                        mm <= 12 &&
                        yyyy >= startYear &&
                        yyyy <= endYear
                      ) {
                        const date = dayjs(`${yyyy}-${mm}-01`).toDate();
                        onChange(date);
                        onMonthChange?.(date);
                        setErrorText('');
                      } else if (formattedValue.length === 7) {
                        setErrorText('Invalid Month');
                      }
                    }}
                    value={value ? dayjs(value).format('MM/YYYY') : ''}
                    disabled={disabled}
                  />

                  <PopoverTrigger asChild>
                    <Button
                      disabled={disabled}
                      variant="outline"
                      className="absolute right-0 top-1/2 -translate-y-1/2 rounded-l-none p-2 bg-muted h-9 me-[1px]"
                    >
                      <CalendarIcon className="w-4 h-4" />
                    </Button>
                  </PopoverTrigger>
                </div>
              ) : (
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    disabled={disabled}
                    className={cn(
                      'w-full flex justify-start font-normal border-border-Default placeholder:text-[#B3B3B3]',
                      errorText ? 'border-danger' : '',
                      className
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {parsedDate
                      ? formatDate(parsedDate, 'MMM YYYY')
                      : placeholder}
                  </Button>
                </PopoverTrigger>
              )}

              <PopoverContent className="w-auto p-0">
                <MonthPicker
                  onMonthSelect={(newDate) => {
                    onChange(newDate);
                    onMonthChange?.(newDate);
                    setErrorText('');
                    setOpen(false);
                  }}
                  selectedMonth={parsedDate}
                />
              </PopoverContent>
            </Popover>
          );
        }}
      />
      {errorText && (
        <p className="text-sm font-normal text-danger">{errorText}</p>
      )}
    </div>
  );
};

export default memo(MonthPickerInfo);
