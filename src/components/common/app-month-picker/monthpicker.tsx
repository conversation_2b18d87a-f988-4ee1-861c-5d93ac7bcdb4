import * as React from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { buttonVariants } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

type Month = {
  number: number;
  name: string;
};

const MONTHS: Month[][] = [
  [
    { number: 0, name: 'Jan' },
    { number: 1, name: 'Feb' },
    { number: 2, name: 'Mar' },
    { number: 3, name: 'Apr' },
  ],
  [
    { number: 4, name: 'May' },
    { number: 5, name: 'Jun' },
    { number: 6, name: 'Jul' },
    { number: 7, name: 'Aug' },
  ],
  [
    { number: 8, name: 'Sep' },
    { number: 9, name: 'Oct' },
    { number: 10, name: 'Nov' },
    { number: 11, name: 'Dec' },
  ],
];

type MonthCalProps = {
  selectedMonth?: Date;
  onMonthSelect?: (date: Date) => void;
  onYearForward?: () => void;
  onYearBackward?: () => void;
  callbacks?: {
    yearLabel?: (year: number) => string;
    monthLabel?: (month: Month) => string;
  };
  variant?: {
    calendar?: {
      main?: ButtonVariant;
      selected?: ButtonVariant;
    };
    chevrons?: ButtonVariant;
  };
  minDate?: Date;
  maxDate?: Date;
  disabledDates?: Date[];
};

type ButtonVariant =
  | 'default'
  | 'outline'
  | 'ghost'
  | 'link'
  | 'destructive'
  | 'secondary'
  | null
  | undefined;

function MonthPicker({
  onMonthSelect,
  selectedMonth,
  minDate,
  maxDate,
  disabledDates,
  callbacks,
  onYearBackward,
  onYearForward,
  variant,
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement> & MonthCalProps) {
  return (
    <div className={cn('min-w-[200px] w-[280px] p-3', className)} {...props}>
      <div className="flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0">
        <div className="space-y-4 w-full">
          <MonthCal
            onMonthSelect={onMonthSelect}
            callbacks={callbacks}
            selectedMonth={selectedMonth}
            onYearBackward={onYearBackward}
            onYearForward={onYearForward}
            variant={variant}
            minDate={minDate}
            maxDate={maxDate}
            disabledDates={disabledDates}
          />
        </div>
      </div>
    </div>
  );
}

function MonthCal({
  selectedMonth,
  onMonthSelect,
  callbacks,
  variant,
  minDate,
  maxDate,
  disabledDates,
  onYearBackward,
  onYearForward,
}: MonthCalProps) {
  const currentYear = new Date().getFullYear();
  const minYear = 1960;
  const maxYear = currentYear + 10; // Allow up to 10 years ahead

  const safeSelectedMonth = selectedMonth
    ? new Date(selectedMonth)
    : new Date();

  const [year, setYear] = React.useState<number>(
    safeSelectedMonth.getFullYear()
  );
  const [month, setMonth] = React.useState<number>(
    safeSelectedMonth.getMonth()
  );
  const [menuYear, setMenuYear] = React.useState<number>(year);

  if (minDate && maxDate && minDate > maxDate) minDate = maxDate;

  const disabledDatesMapped = disabledDates?.map((d) => ({
    year: d.getFullYear(),
    month: d.getMonth(),
  }));

  return (
    <>
      <div className="flex justify-center pt-1 relative items-center">
        {/* Left Chevron (Disabled when menuYear ≤ minYear) */}
        <button
          onClick={() => {
            if (menuYear > minYear) {
              setMenuYear(menuYear - 1);
              if (onYearBackward) onYearBackward();
            }
          }}
          disabled={menuYear <= minYear}
          className={cn(
            buttonVariants({ variant: variant?.chevrons ?? 'outline' }),
            'inline-flex items-center justify-center h-7 w-7 p-0 absolute left-1',
            menuYear <= minYear ? 'opacity-50 cursor-not-allowed' : ''
          )}
        >
          <ChevronLeft className="h-4 w-4" />
        </button>

        {/* Year Dropdown */}
        <Select
          value={menuYear.toString()}
          onValueChange={(value) => {
            setMenuYear(Number(value));
          }}
        >
          <SelectTrigger className="w-[120px]">
            <SelectValue />
          </SelectTrigger>
          <SelectContent className="h-72">
            <SelectGroup>
              {Array.from(
                { length: maxYear - minYear + 1 },
                (_, index) => minYear + index
              ) // Generates ascending order
                .map((year) => (
                  <SelectItem key={year} value={year.toString()}>
                    {year}
                  </SelectItem>
                ))}
            </SelectGroup>
          </SelectContent>
        </Select>

        {/* Right Chevron (Disabled when menuYear ≥ maxYear) */}
        <button
          onClick={() => {
            if (menuYear < maxYear) {
              setMenuYear(menuYear + 1);
              if (onYearForward) onYearForward();
            }
          }}
          disabled={menuYear >= maxYear}
          className={cn(
            buttonVariants({ variant: variant?.chevrons ?? 'outline' }),
            'inline-flex items-center justify-center h-7 w-7 p-0 absolute right-1',
            menuYear >= maxYear ? 'opacity-50 cursor-not-allowed' : ''
          )}
        >
          <ChevronRight className="h-4 w-4" />
        </button>
      </div>

      {/* Month Selection Table */}
      <table className="w-full border-collapse space-y-1">
        <tbody>
          {MONTHS.map((monthRow, a) => (
            <tr key={'row-' + a} className="flex w-full mt-2">
              {monthRow.map((m) => (
                <td
                  key={m.number}
                  className="h-10 w-1/4 text-center text-sm p-0"
                >
                  <button
                    onClick={() => {
                      setMonth(m.number);
                      setYear(menuYear);
                      if (onMonthSelect)
                        onMonthSelect(new Date(menuYear, m.number));
                    }}
                    disabled={
                      (maxDate &&
                        (menuYear > maxDate.getFullYear() ||
                          (menuYear == maxDate.getFullYear() &&
                            m.number > maxDate.getMonth()))) ||
                      (minDate &&
                        (menuYear < minDate.getFullYear() ||
                          (menuYear == minDate.getFullYear() &&
                            m.number < minDate.getMonth()))) ||
                      (disabledDatesMapped &&
                        disabledDatesMapped.some(
                          (d) => d.year == menuYear && d.month == m.number
                        ))
                    }
                    className={cn(
                      buttonVariants({
                        variant:
                          month == m.number && menuYear == year
                            ? (variant?.calendar?.selected ?? 'default')
                            : (variant?.calendar?.main ?? 'ghost'),
                      }),
                      'h-full w-full p-0 font-normal'
                    )}
                  >
                    {callbacks?.monthLabel ? callbacks.monthLabel(m) : m.name}
                  </button>
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </>
  );
}

MonthPicker.displayName = 'MonthPicker';

export { MonthPicker };
