import AppButton from '@/components/common/app-button';
import { cn } from '@/lib/utils';
import { memo } from 'react';

interface FormActionButtonsProps {
  onSubmit: () => void;
  onCancel: () => void;
  isLoading?: boolean;
  submitLabel?: string;
  cancelLabel?: string;
  className?: string;
  submitIcon?: React.FC<React.SVGProps<SVGSVGElement>>;
  cancelIcon?: React.FC<React.SVGProps<SVGSVGElement>>;
  disabledSubmitButton?: boolean;
  disabledCancelButton?: boolean;
}

const FormActionButtons: React.FC<FormActionButtonsProps> = ({
  onSubmit,
  onCancel,
  isLoading,
  submitLabel = 'Submit',
  cancelLabel = 'Cancel',
  className,
  submitIcon,
  cancelIcon,
  disabledSubmitButton,
  disabledCancelButton,
}) => {
  return (
    <div className={cn('flex gap-x-2', className)}>
      <AppButton
        label={submitLabel}
        className="w-full col-span-2 mt-4"
        onClick={onSubmit}
        isLoading={isLoading}
        icon={submitIcon}
        disabled={disabledSubmitButton}
        iconClassName="w-5 h-5"
      />
      <AppButton
        label={cancelLabel}
        variant="neutral"
        className="w-full col-span-2 mt-4"
        onClick={onCancel}
        icon={cancelIcon}
        iconClassName="w-5 h-5"
        disabled={isLoading || disabledCancelButton}
      />
    </div>
  );
};

export default memo(FormActionButtons);
