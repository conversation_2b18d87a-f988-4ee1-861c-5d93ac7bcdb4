import MultiCheckboxDropdown from '@/components/forms/MulitCheckbox';
import NumberInputField from '@/components/forms/number-input-field';
import { CATEGORY_API_ROUTES } from '@/constants/api-constants';
import useOptionList from '@/hooks/useOptionList';
import { cn, convertToFloat, getPaginationObject } from '@/lib/utils';
import { useGetItemLookupQuery } from '@/redux/features/items/item.api';
import { PaginationType, SortingStateType } from '@/types/common.types';
import { Disc2Icon, SearchIcon } from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { SubmitHandler, useFieldArray, useForm } from 'react-hook-form';
import AppButton from '../app-button';
import DataTable from '../data-tables';
import CustomDialog from '../dialog';

interface ItemLookupListTypes {
  id: number;
  itemId: string;
  description: string;
  quantity: number | string;
  unitPrice?: number | string;
}

interface ItemLookupListFormTypes {
  id: number | null;
  itemId: {
    label: string;
    value: string;
  };
  quantity: number;
  description: string;
  childItemId?: number;
}

interface ItemLookupTypes {
  onClick?: (value: ItemLookupListFormTypes[]) => void;
  className?: string;
  btnLabel?: string;
  url: string;
  heading?: string;
  isPriceEditable?: boolean;
  disabled?: boolean;
}

const ItemLookup = ({
  onClick,
  className,
  btnLabel = 'Add to Package List',
  url,
  heading = 'Select one or more Item to add to the package list',
  isPriceEditable,
  disabled,
}: ItemLookupTypes) => {
  const [openLookup, setOpenLookup] = useState<boolean>(false);
  const [search, setSearch] = useState<string>('');
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [pagination, setPagination] = useState<PaginationType>({
    pageSize: 10,
    pageIndex: 0,
  });
  const [sorting, setSorting] = useState<SortingStateType[]>([
    { id: 'itemId', desc: true },
  ]);
  const [isFetchingMore, setIsFetchingMore] = useState<boolean>(false);

  const payload = useMemo(() => {
    return getPaginationObject({
      pagination: pagination,
      sorting: sorting,
      filters: [
        { field: 'filter', value: search, operator: 'Contains' },
        {
          field: 'categoryId',
          value: selectedCategories.join(','),
          operator: 'Equals',
        },
      ],
    });
  }, [pagination, search, selectedCategories, sorting]);

  const {
    data,
    isFetching: isLoading,
    isFetching,
    isSuccess,
  } = useGetItemLookupQuery(
    {
      url,
      body: payload,
    },
    { skip: !openLookup }
  );

  const ItemList = useMemo(
    () => (isSuccess && data?.data ? data?.data : []),
    [data?.data, isSuccess]
  );

  const { options: categoryList, optionLoading } = useOptionList({
    url: CATEGORY_API_ROUTES.ALL,
    labelKey: 'catDesc',
    valueKey: 'id',
    sortBy: 'catDesc',
  });

  const defaultValues = useMemo(() => {
    return {
      items:
        ItemList?.map((item) => ({
          id: item?.id,
          itemId: item?.itemId,
          quantity: '',
          description: item?.description,
          unitPrice: item?.unitPrice,
        })) || [],
    };
  }, [ItemList]); // Ensure it updates when `data?.data` changes

  const categoryForm = useForm();

  // Form initialization
  const form = useForm<{ items: ItemLookupListTypes[] }>({
    defaultValues,
    mode: 'onChange',
  });
  // Handle form reset when new data arrives
  useEffect(() => {
    if (defaultValues) {
      form.reset(defaultValues);
    }
  }, [defaultValues, form]);

  const { fields } = useFieldArray({
    control: form.control,
    name: 'items',
  });

  const handleReset = useCallback(() => {
    form.reset(defaultValues);
  }, [defaultValues, form]);

  const toggleOpenLookup = useCallback(() => {
    handleReset();
    categoryForm.setValue('category', '');
    setSelectedCategories([]);
    setSearch('');
    setOpenLookup((prev) => !prev);
  }, [categoryForm, handleReset]);

  const onSubmit: SubmitHandler<{ items: ItemLookupListTypes[] }> = useCallback(
    (formData) => {
      const payload = formData.items
        .filter((item) => item?.quantity)
        .map((value) => {
          return {
            id: null,
            childItemId: value.id,
            description: value.description,
            quantity: Number(value.quantity),
            ...(isPriceEditable && { unitPrice: Number(value?.unitPrice) }),
            itemId: {
              label: value?.itemId,
              value: value.id?.toString(),
            },
          };
        });

      if (onClick) {
        onClick?.(payload);
        toggleOpenLookup(); // Close dialog after submit
      }
    },
    [isPriceEditable, onClick, toggleOpenLookup]
  );

  // Table columns
  const columns = useMemo(() => {
    return [
      {
        accessorKey: 'itemId',
        header: 'Item ID',
        size: 80,
        enableSorting: true,
      },
      {
        accessorKey: 'description',
        header: 'Item Description',
        maxSize: 250,
        enableSorting: true,
      },
      {
        accessorKey: 'quantity',
        header: 'Quantity',
        size: 100,
        maxSize: 100,
        cell: ({ row }: any) => (
          <div className="p-1">
            <NumberInputField
              form={form}
              placeholder="Quantity"
              name={`items.${row?.index}.quantity`}
              className="w-full h-8"
              maxLength={5}
              decimalScale={0}
            />
          </div>
        ),
      },
      {
        accessorKey: 'unitPrice',
        header: 'Price',
        size: isPriceEditable ? 110 : 80,
        maxSize: 120,
        enableSorting: true,
        cell: ({ row }: any) =>
          isPriceEditable ? (
            <NumberInputField
              form={form}
              placeholder="Unit Price"
              name={`items.${row?.index}.unitPrice`}
              className="w-full h-8"
              maxLength={14}
              thousandSeparator=","
              pClassName="p-1"
              prefix="$"
              fixedDecimalScale
            />
          ) : (
            convertToFloat({ value: row.original.unitPrice, prefix: '$' })
          ),
      },
    ];
  }, [form, isPriceEditable]);

  const modifiedItemLookups = form.watch('items');
  const hasQuantity = modifiedItemLookups.some(
    (item) => item.quantity !== undefined && Number(item?.quantity) > 0
  );

  // Add/Reset buttons
  const AddResetItemLookup = useCallback(
    () => (
      <div className="flex absolute bottom-3 right-6 gap-3">
        <AppButton
          label={btnLabel}
          icon={Disc2Icon}
          onClick={form.handleSubmit(onSubmit)}
          iconClassName="w-4 h-4"
          variant="primary"
          disabled={!hasQuantity}
        />
        <AppButton
          label="Reset"
          onClick={handleReset}
          variant="neutral"
          disabled={!hasQuantity}
          className="w-32"
        />
      </div>
    ),
    [btnLabel, form, handleReset, hasQuantity, onSubmit]
  );

  // Custom toolbar for category selection
  const CustomToolbar = (
    <MultiCheckboxDropdown
      name="category"
      form={categoryForm}
      optionsList={categoryList ?? []}
      placeholder={'Select Categories'}
      onChange={(value) => setSelectedCategories(value)}
      isLoading={optionLoading}
    />
  );

  const fetchMore = useCallback(() => {
    if (isFetchingMore) return; // Avoid duplicate calls

    setIsFetchingMore(true); // Set flag to prevent further calls
    setPagination((prev) => ({ ...prev, pageSize: prev.pageSize + 10 }));
  }, [isFetchingMore]);

  // Reset `isFetchingMore` only after new data arrives
  useEffect(() => {
    if (!isFetching) {
      setIsFetchingMore(false);
    }
  }, [isFetching]);

  return (
    <div className="w-full">
      <div className="flex justify-end">
        <AppButton
          label="Item Lookup"
          variant="primary"
          icon={SearchIcon}
          iconClassName="w-5 h-5"
          className={cn(
            'bg-brand-teal-Default hover:bg-brand-teal-Default',
            className
          )}
          onClick={toggleOpenLookup}
          disabled={disabled}
        />
      </div>

      <CustomDialog
        title="Item Lookup"
        onOpenChange={toggleOpenLookup}
        description=""
        open={openLookup}
        className={cn('min-w-[70%] 2xl:min-w-[55%]', className)}
        contentClassName="h-[450px] 2xl:h-[550px] overflow-y-auto"
      >
        <div className="px-4 py-2">
          <DataTable
            data={fields ?? []}
            columns={columns}
            isLoading={isLoading}
            totalItems={data?.pagination?.totalCount}
            search={search}
            setSearch={setSearch}
            enableSearch
            sorting={sorting}
            setSorting={setSorting}
            customToolBar={CustomToolbar}
            heading={<div className="font-normal text-base">{heading}</div>}
            enablePagination={false}
            tableClassName="max-h-[300px] 2xl:max-h-[400px] overflow-auto"
            isInfiniteScroll
            fetchMore={fetchMore}
          />
          <AddResetItemLookup />
        </div>
      </CustomDialog>
    </div>
  );
};

export default ItemLookup;
