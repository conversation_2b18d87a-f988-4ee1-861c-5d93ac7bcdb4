import ColumnOrdering from '@/assets/icons/ColumnOrdering';
import AppButton from '@/components/common/app-button';
import IconButton from '@/components/common/icon-button';
import SwitchField from '@/components/common/switch';
import Labels from '@/components/forms/Label';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import { memo, useEffect, useMemo } from 'react';
import { Controller, SubmitHandler, useForm } from 'react-hook-form';

interface ColumnReOrderingProps {
  isOpen: boolean;
  handleOrderingColumn: (formData: any) => void;
  tableColumns: ColumnOption[];
  setOpenColumnOrdering: (open: boolean) => void;
  className?: string;
}

interface ColumnOption {
  accessorKey: string;
  header: string;
  enableSorting: boolean;
  size: number;
  enabled: boolean;
  allowDisable: boolean;
}

const ColumnReOrdering: React.FC<ColumnReOrderingProps> = ({
  isOpen,
  handleOrderingColumn,
  tableColumns,
  setOpenColumnOrdering,
  className,
}) => {
  // Default values
  const defaultValues = useMemo(
    () =>
      tableColumns.reduce<Record<string, boolean>>(
        (acc, column) => ({ ...acc, [column.accessorKey]: column.enabled }),
        {}
      ),
    [tableColumns]
  );

  // useForm hook
  const form = useForm<any>({
    defaultValues,
    mode: 'onChange',
  });

  const { handleSubmit, setValue, watch, control } = form;
  const formValues = watch(); // Watch all form values
  const columnValues = Object.keys(formValues).filter(
    (key) => key !== 'selectAll'
  );
  const isSelectAll = columnValues.every((key) => formValues[key]);

  useEffect(() => {
    form.setValue('selectAll', isSelectAll);
  }, [form, isSelectAll]);

  // Handle Select All Toggle
  const handleSelectAllToggle = (checked: boolean) => {
    tableColumns.forEach(
      (col) => col.allowDisable && setValue(col.accessorKey, checked)
    );
  };

  // Form submit function
  const onSubmit: SubmitHandler<any> = (formData) => {
    handleOrderingColumn(formData);
  };

  return (
    <Popover open={isOpen} onOpenChange={setOpenColumnOrdering}>
      <PopoverTrigger asChild>
        <IconButton className="bg-inherit">
          <ColumnOrdering />
        </IconButton>
      </PopoverTrigger>
      <PopoverContent
        className={cn(
          'flex flex-col border-border-Default w-full p-2 h-[38vh] overflow-auto',
          className
        )}
        alignOffset={-15}
        align="start"
      >
        <div className="flex justify-between gap-3 mx-2 my-2">
          <Labels label="Columns" />
          <AppButton
            className="h-7"
            label="Save"
            onClick={handleSubmit(onSubmit)}
          />
        </div>

        <Separator className="h-[1px] bg-border-Default mb-2" />

        {/* Select All Toggle */}
        <div className="flex items-center justify-between p-2 bg-gray-100 rounded-md">
          <span className="text-sm font-medium text-gray-800">Select All</span>
          <Controller
            name="selectAll"
            control={control}
            render={({ field: { onChange: fieldOnChange } }) => (
              <SwitchField
                label=""
                form={form}
                name="selectAll"
                className="mx-2"
                onChange={(checked) => {
                  fieldOnChange(checked);
                  handleSelectAllToggle(checked);
                }}
              />
            )}
          />
        </div>

        <Separator className="h-[1px] bg-border-Default my-2" />

        {/* Column Switches */}
        <div className="h-[50vh] overflow-auto space-y-2">
          {tableColumns.map((option) => (
            <div
              key={option.accessorKey}
              className="flex items-center justify-between px-2 py-1 hover:bg-gray-100 rounded-md"
            >
              <span className="text-sm font-medium text-gray-800">
                {option.header}
              </span>
              <SwitchField
                label=""
                form={form}
                name={option.accessorKey}
                className="ml-2"
                disabled={!option.allowDisable}
              />
            </div>
          ))}
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default memo(ColumnReOrdering);
