import { render } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import { Provider } from 'react-redux';
import { createStore } from '@reduxjs/toolkit';
import IdleTimerContainer from './IdleTimerContainer';
import { useIdleTimer } from 'react-idle-timer';
import { beforeEach, describe, vi, it, expect } from 'vitest';

vi.mock('react-idle-timer', () => ({
  useIdleTimer: vi.fn(),
}));

vi.mock('react-router-dom', async () => {
  const actual = await import('react-router-dom');
  return {
    ...actual,
    useNavigate: vi.fn(),
    MemoryRouter: actual.MemoryRouter,
  };
});

const mockStore = createStore(() => ({}));

describe('IdleTimerContainer Component', () => {
  beforeEach(() => {
    (useIdleTimer as any).mockImplementation(({ onIdle }: any) => {
      onIdle();
      return {
        reset: vi.fn(),
        clear: vi.fn(),
      };
    });
  });

  it('should render the idle dialog when the user is idle', async () => {
    const isRendered = render(
      <Provider store={mockStore}>
        <MemoryRouter>
          <IdleTimerContainer />
        </MemoryRouter>
      </Provider>
    );

    expect(isRendered);
  });
});
