import { render, screen } from '@testing-library/react';
import { Provider } from 'react-redux';
import { vi, describe, it, expect } from 'vitest';
import { createStore } from 'redux';
import Sidebar from './SidebarItem';
import { ROLES } from '@/constants/common-constants';

const mockStore = {
  auth: {
    role: ROLES.SUPER_ADMIN,
  },
};

const mockHandlePinClick = vi.fn();

vi.mock('@/hooks/useSidebarCollapse', () => ({
  useSidebarCollapse: vi.fn().mockReturnValue({
    isPinned: false,
    handleMouseEnter: vi.fn(),
    handleMouseLeave: vi.fn(),
    handlePinClick: mockHandlePinClick,
  }),
}));

// Mock SidebarItem to simplify the test
vi.mock('./SidebarItem', () => ({
  __esModule: true,
  default: vi.fn(() => <div>SidebarItem</div>),
}));

// Create a mock store with redux
const renderWithStore = (store = mockStore) => {
  return render(
    <Provider store={createStore(() => store)}>
      <Sidebar
        label={'Slider'}
        to={'Navigate'}
        isCollapsed={false}
        onItemClick={vi.fn()}
        children={<div>SidebarItem</div>}
      />
    </Provider>
  );
};

describe('Sidebar', () => {
  it('renders SidebarItem components based on the role', () => {
    renderWithStore();
    expect(screen.getByText('SidebarItem')).toBeInTheDocument();
  });

  it('renders ScrollArea component correctly', () => {
    const isRendered = renderWithStore();
    expect(isRendered);
  });

  it('correctly displays menu based on role', () => {
    renderWithStore({
      auth: {
        role: ROLES.ADMIN,
      },
    });

    expect(screen.getByText('SidebarItem')).toBeInTheDocument();
  });
});
