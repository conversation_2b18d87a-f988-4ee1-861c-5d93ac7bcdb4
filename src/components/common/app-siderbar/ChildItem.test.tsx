import { render, screen, fireEvent } from '@testing-library/react';
import { vi, describe, test, expect, beforeEach } from 'vitest';
import { useNavigate } from 'react-router-dom';
import ChildItem from './ChildItem';

vi.mock('react-router-dom', () => ({
  useNavigate: vi.fn(),
}));

describe('ChildItem Component', () => {
  let setToggleStatesMock: React.Dispatch<
    React.SetStateAction<{ [key: string]: boolean }>
  >;

  beforeEach(() => {
    setToggleStatesMock = vi.fn();
  });

  test('renders correctly with label and icon', () => {
    render(
      <ChildItem
        label="Test Item"
        to="/test"
        icon={() => <div>Icon</div>}
        toggleStates={{}}
        setToggleStates={setToggleStatesMock}
      />
    );

    expect(screen.getByText('Test Item')).toBeInTheDocument();
    expect(screen.getByText('Icon')).toBeInTheDocument();
  });

  test('handles item click and navigation', () => {
    const mockNavigate = vi.fn();
    (useNavigate as any).mockReturnValue(mockNavigate);

    render(
      <ChildItem
        label="Test Item"
        to="/test"
        toggleStates={{}}
        setToggleStates={setToggleStatesMock}
      />
    );

    fireEvent.click(screen.getByText('Test Item'));

    expect(mockNavigate).toHaveBeenCalledWith('/test');
  });

  test('does not render children when collapsed', () => {
    const toggleStates = {
      'test-to-Test Item': false,
    };

    render(
      <ChildItem
        label="Test Item"
        to="/test"
        children={[{ label: 'Child Item', to: '/child' }]}
        toggleStates={toggleStates}
        setToggleStates={setToggleStatesMock}
      />
    );

    expect(screen.queryByText('Child Item')).not.toBeInTheDocument();
  });
});
