import { render, screen } from '@testing-library/react';
import { BrowserRouter as Router } from 'react-router-dom';
import { vi, describe, it, expect } from 'vitest';
import SidebarItem from './SidebarItem';
import { ROUTES } from '@/constants/routes-constants';

vi.mock('@/hooks/useSidebarNavigation', () => ({
  useSidebarNavigation: vi.fn().mockReturnValue({ isActive: false }),
}));

vi.mock('react-router-dom', async () => ({
  ...(await import('react-router-dom')),
  useNavigate: vi.fn(),
}));

describe('SidebarItem', () => {
  const mockOnItemClick = vi.fn();

  const defaultProps = {
    label: 'System Users',
    to: ROUTES.SYSTEM_USERS,
    isCollapsed: false,
    onItemClick: mockOnItemClick,
    children: [
      {
        label: 'Child Item 1',
        to: '/child-item-1',
      },
      {
        label: 'Child Item 2',
        to: '/child-item-2',
      },
    ],
  };

  it('renders correctly when collapsed and not collapsed', () => {
    render(
      <Router>
        <SidebarItem {...defaultProps} isCollapsed={false} />
      </Router>
    );

    expect(screen.getByText('System Users')).toBeInTheDocument();
  });
});
