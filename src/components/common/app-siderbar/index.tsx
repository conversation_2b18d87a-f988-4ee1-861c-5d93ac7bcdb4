import DoubleCheveronIcon from '@/assets/icons/DoubleCheveronIcon';
import PinIcon from '@/assets/icons/PinIcon';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  ROLES,
  superAdminMenu,
  tenantMenu,
  userMenu,
} from '@/constants/common-constants';
import { useSidebarCollapse } from '@/hooks/useSidebarCollapse';
import { cn } from '@/lib/utils';
import type { RootState } from '@/redux/store';
import { SidebarProps } from '@/types/sidebar.types';
import { lazy, memo } from 'react';
import { useSelector } from 'react-redux';
const SidebarItem = lazy(() => import('./SidebarItem'));

// Function to get the menu by role
const getMenuByRole = (role: string | null) => {
  switch (role) {
    case ROLES.SUPER_ADMIN:
      return superAdminMenu;
    case ROLES.ADMIN:
      return tenantMenu;
    case ROLES.NORMAL_USER:
      return tenantMenu;
    default:
      return userMenu;
  }
};

// Function to check if the user has permission
const hasPermission = (userPermissions: any[], permission: string) => {
  return userPermissions.some(
    (item) => item.permission === permission && item.enabled
  );
};

const Sidebar = ({
  isCollapsed,
  setOpenMenu,
  setIsCollapsed,
}: SidebarProps) => {
  const { role } = useSelector((state: RootState) => state.auth);
  const { isPinned, handleMouseEnter, handleMouseLeave, handlePinClick } =
    useSidebarCollapse(setIsCollapsed);

  const handleItemClick = () => {
    setOpenMenu?.(false);
  };

  // Retrieve permissions from localStorage (assuming it's stored under 'userPermissions' key)
  const userPermissions = JSON.parse(
    localStorage.getItem('permissions') || '[]'
  );

  const menuList = getMenuByRole(role);

  // Filter the menu items based on the user's permissions
  const filteredMenuList = menuList?.filter((item) => {
    if (item && item?.permission) {
      return hasPermission(userPermissions, item?.permission); // Check if the user has the required permission
    }
    return true; // If no specific permission is required, show the item
  });

  return (
    <ScrollArea
      className="h-full"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <div className="flex flex-col gap-4">
        <div className="hidden lg:flex justify-between items-center">
          <div
            className="transition-transform duration-200"
            style={{
              transform: isCollapsed ? 'rotate(180deg)' : 'rotate(0deg)',
            }}
          >
            <DoubleCheveronIcon />
          </div>
          <div
            onClick={handlePinClick}
            className={cn(
              'cursor-pointer p-3',
              isPinned ? 'bg-white rounded-md' : ''
            )}
          >
            <PinIcon iconColor={isPinned ? '#1E1E1E' : '#F2F4F7'} />
          </div>
        </div>

        {filteredMenuList.map((item, index) => (
          <SidebarItem
            key={index}
            {...item}
            isCollapsed={isCollapsed}
            onItemClick={handleItemClick}
          />
        ))}
      </div>
    </ScrollArea>
  );
};

export default memo(Sidebar);
