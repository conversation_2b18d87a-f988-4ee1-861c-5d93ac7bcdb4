import { useNavigate } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { ChevronDown } from 'lucide-react';
import { memo } from 'react';

export interface ChildItemProps {
  icon?: any;
  parentIcon?: any;
  label: string;
  to: string;
  isActive?: boolean;
  onItemClick?: () => void;
  children?: any[];
  toggleStates: { [key: string]: boolean };
  setToggleStates: React.Dispatch<
    React.SetStateAction<{ [key: string]: boolean }>
  >;
  iconClassName?: string;
}

const ChildItem = ({
  icon: Icon,
  parentIcon: ParentIcon,
  label,
  to,
  isActive,
  onItemClick,
  children = [],
  toggleStates,
  setToggleStates,
  iconClassName,
}: ChildItemProps) => {
  const navigate = useNavigate();
  const hasChildren = children?.length > 0;
  const itemKey = `${to}-${label}`;
  const isExpanded = toggleStates[itemKey];

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();

    if (hasChildren) {
      setToggleStates((prev) => ({
        ...prev,
        [itemKey]: !prev[itemKey],
      }));
    }

    if (to) {
      navigate(to);
      onItemClick?.();
    }
  };

  const handleChevronClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setToggleStates((prev) => ({
      ...prev,
      [itemKey]: !prev[itemKey],
    }));
  };

  return (
    <div className="flex flex-col">
      <div
        className={cn(
          'flex items-center justify-between py-2 px-3 rounded-md transition-colors duration-200 cursor-pointer',
          isActive
            ? 'bg-white/10 text-white'
            : 'text-white/60 hover:text-white hover:bg-white/5'
        )}
        onClick={handleClick}
      >
        <div className="flex items-center">
          {Icon ? (
            <Icon className={cn('w-4 h-4 mr-2', iconClassName)} />
          ) : (
            ParentIcon && <ParentIcon className="w-4 h-4 mr-2 opacity-50" />
          )}
          <span className="font-semibold text-sm ml-2">{label}</span>
        </div>
        {hasChildren && (
          <div onClick={handleChevronClick}>
            <ChevronDown
              className={cn(
                'h-4 w-4 transition-transform duration-200',
                isExpanded ? 'rotate-180' : ''
              )}
            />
          </div>
        )}
      </div>

      {hasChildren && isExpanded && (
        <div className="ml-4 mt-1 flex flex-col gap-1 animate-slideDown">
          {children.map((child, index) => (
            <ChildItem
              key={`${child.to}-${index}`}
              {...child}
              parentIcon={Icon || ParentIcon}
              onItemClick={onItemClick}
              toggleStates={toggleStates}
              setToggleStates={setToggleStates}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default memo(ChildItem);
