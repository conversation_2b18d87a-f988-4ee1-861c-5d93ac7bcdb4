import { useCallback, useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { ChevronDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { SidebarItemProps } from '@/types/sidebar.types';
import { useSidebarNavigation } from '@/hooks/useSidebarNavigation';
import ChildItem, { ChildItemProps } from './ChildItem';
import { ROUTES } from '@/constants/routes-constants';

type ToggleState = {
  [key: string]: boolean;
};

const SidebarItem = ({
  icon: Icon,
  label,
  to,
  isCollapsed,
  onItemClick,
  children,
}: SidebarItemProps) => {
  const [toggleStates, setToggleStates] = useState<ToggleState>({});
  const { isActive } = useSidebarNavigation(to, children);
  const location = useLocation();
  const navigate = useNavigate();
  const hasChildren = children?.length > 0;
  const itemKey = `${to}`;

  const isChildActive = children?.some((child: any) => {
    if (child.to === location.pathname) return true;
    return child.children?.some(
      (subChild: any) => subChild.to === location.pathname
    );
  });

  const isExpanded = toggleStates[itemKey];

  const toggleSidebarItem = useCallback(() => {
    setToggleStates((prev) => ({
      ...prev,
      [itemKey]: !prev[itemKey],
    }));
  }, [itemKey]);

  const handleItemClick = (to: string) => {
    if (
      hasChildren &&
      (itemKey === ROUTES.SYSTEM_USERS || itemKey === ROUTES.ADJUSTMENTS)
    ) {
      toggleSidebarItem();
    } else if (hasChildren && itemKey === ROUTES.WAREHOUSE) {
      toggleSidebarItem();
    }
    navigate(to);
    onItemClick?.();
  };

  const handleChevronClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    toggleSidebarItem();
  };

  useEffect(() => {
    if (
      (itemKey === ROUTES.SYSTEM_USERS || itemKey === ROUTES.ADJUSTMENTS) &&
      isActive
    ) {
      setToggleStates((prev) => ({
        ...prev,
        [itemKey]: true,
      }));
    } else if (itemKey === ROUTES.WAREHOUSE && isActive) {
      setToggleStates((prev) => ({
        ...prev,
        [itemKey]: true,
      }));
    }
  }, [isActive, itemKey]);

  return (
    <div className="flex flex-col">
      <div
        className={cn(
          'flex items-center transition-all ease-in-out duration-300 rounded-md font-semibold cursor-pointer',
          isActive || isChildActive
            ? 'bg-white text-[#303030]'
            : 'text-white hover:bg-white/10',
          isCollapsed
            ? 'justify-center w-10 h-10'
            : 'justify-between h-10 w-full p-[10px]'
        )}
        onClick={() => handleItemClick(to)}
        aria-label={`link-${label}`}
      >
        <div className="flex items-center">
          {Icon && (
            <Icon
              iconColor={isActive || isChildActive ? '#303030' : '#F3F3F3'}
            />
          )}
          {!isCollapsed && (
            <span className="ml-2 transition-transform ease-in-out duration-300">
              {label}
            </span>
          )}
        </div>

        {!isCollapsed && hasChildren && (
          <div onClick={handleChevronClick}>
            <ChevronDown
              className={cn(
                'h-4 w-4 transition-transform duration-200 cursor-pointer',
                isExpanded ? 'rotate-180' : ''
              )}
            />
          </div>
        )}
      </div>

      {!isCollapsed && hasChildren && isExpanded && (
        <div className="ml-4 mt-1 flex flex-col gap-2 animate-slideDown">
          {children.map((child: ChildItemProps, index: number) => (
            <ChildItem
              key={`${child.to}-${index}`}
              {...child}
              parentIcon={Icon}
              onItemClick={onItemClick}
              isActive={location.pathname.includes(child.to)}
              toggleStates={toggleStates}
              setToggleStates={setToggleStates}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default SidebarItem;
