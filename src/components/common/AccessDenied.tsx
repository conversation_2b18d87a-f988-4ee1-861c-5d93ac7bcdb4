import { ROUTES } from '@/constants/routes-constants';
import { ArrowLeft, ShieldX } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import AppButton from './app-button';

const AccessDenied = () => {
  const navigate = useNavigate();

  const redirectToLogin = async () => {
    navigate(ROUTES.HOME);
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-[#55B9C0] to-[#5A2AC2] flex flex-col justify-center items-center px-6">
      <div className="w-[30%] bg-white/10 rounded-2xl  shadow-xl p-16 text-center">
        <div className="mb-8 flex justify-center">
          <ShieldX className="h-20 w-20 text-white" />
        </div>

        <h1 className="text-4xl font-bold text-white mb-4">Access Denied</h1>

        <p className="text-white mb-8">
          Sorry, but you don't have permission to access this page. Please
          contact your administrator.
        </p>

        <div className="flex flex-col gap-4">
          <AppButton
            icon={ArrowLeft}
            label="Go Back"
            onClick={redirectToLogin}
          />
        </div>
      </div>
    </div>
  );
};

export default AccessDenied;
