import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import React from 'react';
import AppSpinner from '../app-spinner';
import { useNavigate } from 'react-router-dom';

interface AppButtonProps {
  icon?: React.FC<React.SVGProps<SVGSVGElement>>;
  cancelIcon?: React.FC<React.SVGProps<SVGSVGElement>>;
  buttonLabel?: string;
  cancelButtonLabel?: string;
  variant?: 'primary' | 'neutral' | 'danger';
  cancelVariant?: 'primary' | 'neutral' | 'danger';
  className?: string;
  navigateTo?: string;
  onClick?: () => void;
  cancelOnClick?: () => void;
  iconClassName?: string; // Added this prop for icon styling
  cancelIconClassName?: string;
  iconPosition?: 'before' | 'next';
  disabled?: boolean;
  isLoading?: boolean;
  spinnerClass?: string;
  type?: 'submit' | 'reset' | 'button';
}

const AppButtonActions = ({
  icon: Icon,
  cancelIcon: CancelIcon,
  buttonLabel = 'Save',
  cancelButtonLabel = 'Cancel',
  className = '',
  navigateTo,
  onClick,
  cancelOnClick,
  variant = 'primary',
  cancelVariant = 'neutral',
  iconPosition = 'before',
  iconClassName = '', // Default empty string if no class is passed
  cancelIconClassName = '',
  disabled = false,
  isLoading,
  spinnerClass,
  type = 'submit',
}: AppButtonProps) => {
  const navigation = useNavigate();

  const baseClasses = 'rounded-lg text-base';
  const variantClasses = {
    primary:
      'bg-background-brand-violet-Default text-white hover:bg-background-brand-violet-hover border-border-brand-violet-Default',
    neutral: 'bg-white text-text-Default border-border-Default border-[1px]',
    danger:
      'bg-background-danger-default p-3 hover:bg-background-danger-default text-white border-border-Default border-[1px]',
  };

  const getButtonClasses = (variant: 'primary' | 'neutral' | 'danger') =>
    cn(
      baseClasses,
      variantClasses[variant],
      disabled && 'bg-tertiary-neutral',
      className
    );

  const renderIcon = (
    Icon: React.FC<React.SVGProps<SVGSVGElement>> | undefined,
    position: string,
    className: string
  ) =>
    Icon &&
    position === iconPosition &&
    !isLoading && <Icon className={className} />;

  const disabledClasses = disabled ? 'bg-tertiary-neutral' : '';

  return (
    <div className="mt-4 flex justify-end gap-4">
      <div className="w-[30%] flex justify-end gap-4">
        <Button
          className={getButtonClasses(variant)}
          type={type}
          disabled={disabled || isLoading}
          onClick={onClick}
        >
          <div className="flex items-center gap-x-2">
            {renderIcon(Icon, 'before', iconClassName)}
            {isLoading && (
              <AppSpinner className={cn('h-4 w-4', spinnerClass)} />
            )}
            {buttonLabel}
            {renderIcon(Icon, 'next', iconClassName)}
          </div>
        </Button>
        <Button
          className={cn(
            baseClasses,
            variantClasses[cancelVariant],
            disabledClasses,
            className
          )}
          type={type}
          disabled={disabled || isLoading}
          onClick={() => {
            cancelOnClick?.();
            navigation(navigateTo || '/customers');
          }}
        >
          <div className="flex items-center gap-x-2">
            {CancelIcon && iconPosition === 'before' && !isLoading && (
              <CancelIcon className={cancelIconClassName} />
            )}
            {isLoading && (
              <AppSpinner className={cn('h-4 w-4', spinnerClass)} />
            )}
            {cancelButtonLabel}
            {CancelIcon && iconPosition === 'next' && !isLoading && (
              <CancelIcon className={cancelIconClassName} />
            )}
          </div>
        </Button>
      </div>
    </div>
  );
};

export default AppButtonActions;
