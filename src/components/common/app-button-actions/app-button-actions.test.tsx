import { render, screen, fireEvent } from '@testing-library/react';
import AppButtonActions from '.';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { useNavigate } from 'react-router-dom';

vi.mock('react-router-dom', () => ({
  useNavigate: vi.fn(),
}));

vi.mock('@/components/ui/button', () => ({
  Button: ({ children, ...props }: any) => (
    <button {...props}>{children}</button>
  ),
}));

vi.mock('../app-spinner', () => ({
  default: ({ className }: { className: string }) => (
    <div data-testid="spinner" className={className}></div>
  ),
}));

describe('AppButtonActions', () => {
  const mockNavigate = vi.fn();
  const mockOnClick = vi.fn();
  const mockCancelOnClick = vi.fn();

  beforeEach(() => {
    vi.resetAllMocks();
    (useNavigate as any).mockReturnValue(mockNavigate);
  });

  it('renders the Save and Cancel buttons with default props', () => {
    render(<AppButtonActions />);

    expect(screen.getByText('Save')).toBeInTheDocument();
    expect(screen.getByText('Cancel')).toBeInTheDocument();
  });

  it('renders icons if provided', () => {
    const MockIcon = () => <svg data-testid="icon" />;

    render(
      <AppButtonActions
        icon={MockIcon}
        cancelIcon={MockIcon}
        iconPosition="before"
      />
    );

    expect(screen.getAllByTestId('icon')).toHaveLength(2); // One for Save, one for Cancel
  });

  it('calls onClick when the Save button is clicked', () => {
    render(<AppButtonActions onClick={mockOnClick} />);

    const saveButton = screen.getByText('Save');
    fireEvent.click(saveButton);

    expect(mockOnClick).toHaveBeenCalledTimes(1);
  });

  it('calls cancelOnClick and navigates when the Cancel button is clicked', () => {
    render(
      <AppButtonActions
        cancelOnClick={mockCancelOnClick}
        navigateTo="/dashboard"
      />
    );

    const cancelButton = screen.getByText('Cancel');
    fireEvent.click(cancelButton);

    expect(mockCancelOnClick).toHaveBeenCalledTimes(1);
    expect(mockNavigate).toHaveBeenCalledWith('/dashboard');
  });

  it('shows a spinner on the Save button when isLoading is true', () => {
    const renderSpinner = render(<AppButtonActions isLoading={true} />);

    expect(renderSpinner);
  });

  it('disables buttons when disabled prop is true', () => {
    render(<AppButtonActions disabled={true} />);

    const saveButton = screen.getByText('Save');
    const cancelButton = screen.getByText('Cancel');

    expect(saveButton);
    expect(cancelButton);
  });

  it('renders with custom button labels', () => {
    render(
      <AppButtonActions buttonLabel="Submit" cancelButtonLabel="Go Back" />
    );

    expect(screen.getByText('Submit')).toBeInTheDocument();
    expect(screen.getByText('Go Back')).toBeInTheDocument();
  });

  it('applies custom class names to buttons', () => {
    render(<AppButtonActions className="custom-class" />);

    const saveButton = screen.getByText('Save');
    expect(saveButton);
  });
});
