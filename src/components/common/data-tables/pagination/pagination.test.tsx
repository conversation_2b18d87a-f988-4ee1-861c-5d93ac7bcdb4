import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, vi } from 'vitest';
import Pagination from './index';
import { AppTableContext } from '../../app-data-table/AppTableContext';
import { Table } from '@tanstack/react-table';
import '@testing-library/jest-dom';
import { expect } from 'vitest';

describe('Pagination Component', () => {
  const mockTable = {
    getPageCount: vi.fn(() => 10),
    setPageIndex: vi.fn(),
    getCanPreviousPage: vi.fn(() => true),
    getCanNextPage: vi.fn(() => true),
    previousPage: vi.fn(),
    nextPage: vi.fn(),
    setPageSize: vi.fn(),
  } as any;

  const mockSetPagination = vi.fn();
  const mockSetSorting = vi.fn();

  const renderPagination = (paginationState: any) => {
    render(
      <AppTableContext.Provider
        value={{
          pagination: paginationState,
          setPagination: mockSetPagination,
          sorting: [],
          setSorting: mockSetSorting,
          rowSelection: { '1': true },
          setRowSelection: vi.fn(),
        }}
      >
        <Pagination
          table={mockTable as Table<any>}
          totalItems={100}
          pagination={paginationState}
          setPagination={mockSetPagination}
        />
      </AppTableContext.Provider>
    );
  };

  it('should render pagination buttons correctly', () => {
    const paginationState = { pageIndex: 1, pageSize: 10 };
    renderPagination(paginationState);
    const previousButton = screen.getByLabelText('previous-page');
    const nextButton = screen.getByLabelText('next-page');

    expect(previousButton).toBeInTheDocument();
    expect(nextButton).toBeInTheDocument();
  });

  it('should render correct page numbers and handle page change', () => {
    const paginationState = { pageIndex: 1, pageSize: 10 };
    renderPagination(paginationState);
    const pageNumbers = screen.getAllByRole('button');
    expect(pageNumbers.length).toBeGreaterThan(0);
    const page2Button = screen.getByText('2');
    fireEvent.click(page2Button);
    expect(mockSetPagination);
  });

  it('should disable previous and next buttons when there are no pages to navigate', () => {
    const paginationState = { pageIndex: 0, pageSize: 10 };
    mockTable.getCanPreviousPage.mockReturnValue(false);
    mockTable.getCanNextPage.mockReturnValue(false);
    renderPagination(paginationState);

    const previousButton = screen.getByLabelText('previous-page');
    const nextButton = screen.getByLabelText('next-page');

    expect(previousButton).toBeDisabled();
    expect(nextButton).toBeDisabled();
  });

  it('should update rows per page and reset to the first page', () => {
    const paginationState = { pageIndex: 0, pageSize: 10 };
    renderPagination(paginationState);

    const rowPerPageSelect = screen.getByRole('combobox');
    fireEvent.change(rowPerPageSelect, { target: { value: '20' } });

    expect(mockSetPagination);
    expect(mockTable.setPageSize);
  });

  it('should display the total count correctly', () => {
    const paginationState = { pageIndex: 1, pageSize: 10 };
    renderPagination(paginationState);
    const totalCount = screen.getByText(/Total Count : 100/);
    expect(totalCount).toBeInTheDocument();
  });
});
