import CheckIcon from '@/assets/icons/CheckIcon';
import MinusIcon from '@/assets/icons/MinusIcon';
import { Checkbox } from '@/components/ui/checkbox';

import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { PaginationType } from '@/types/common.types';
import {
  ColumnDef,
  ExpandedState,
  getCoreRowModel,
  getExpandedRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  OnChangeFn,
  Table as ReactTable,
  Row,
  RowSelectionState,
  SortingState,
  useReactTable,
} from '@tanstack/react-table';
import { ChevronDown, ChevronRight } from 'lucide-react';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import Pagination from './pagination';
import RenderFilters from './render-filters';
import CustomDataTable from './table';
import Toolbar from './toolbar';

interface RowData {
  [key: string]: any; // Allows dynamic access to properties by string keys
  overbooked?: boolean; // Example specific key for overbooked
}

interface DataTableProps<
  TData extends RowData,
  TValue,
  TFilter = Record<string, any>,
> {
  columns: (ColumnDef<TData, TValue> & {
    className?: string | ((row: Row<TData>) => string);
  })[];
  data: TData[];
  search?: string;
  setSearch?: React.Dispatch<React.SetStateAction<string>>;
  onRowSelectionChange?: OnChangeFn<RowSelectionState>;
  enableRowSelection?: boolean;
  enableSearch?: boolean;
  heading?: React.ReactNode;
  customToolBar?: React.ReactNode;
  enableColumnVisibility?: boolean;
  enableFilter?: boolean;
  filterContent?: React.ReactNode;
  isFilterOpen?: boolean;
  filter?: TFilter;
  handleClearFilter?: (filterKey: string) => void;
  setIsFilterOpen?: React.Dispatch<React.SetStateAction<boolean>>;
  isLoading?: boolean;
  pagination?: PaginationType;
  setPagination?: React.Dispatch<React.SetStateAction<PaginationType>>;
  totalItems?: number;
  filterClassName?: string;
  filterSide?: 'top' | 'right' | 'bottom' | 'left';
  noDataPlaceholder?: string;
  enablePagination?: boolean;
  sorting?: SortingState;
  setSorting?: OnChangeFn<SortingState>;
  loaderRows?: number;
  handleColumnVisibility?: () => void;
  rowSelection?: any;
  tableClassName?: string;
  enableMultiRowSelection?: boolean;
  bindingKey?: string;
  truncate?: boolean;
  footer?: React.ReactNode;
  highlightKey?: string;
  highlightClassName?: string;
  selectedHighlightClassName?: string;
  disableSelectAllCheckbox?: boolean;
  onScrollRef?: any;
  manualSorting?: boolean;
  isInfiniteScroll?: boolean;
  fetchMore?: () => void;
  onRowsSelected?: (selectedRows: TData[]) => void;
  isRowExpanded?: boolean;
  isExpandAllRow?: boolean;
  renderSubComponent?: (props: { row: Row<TData> }) => React.ReactElement;
  headerClassName?: string;
  getRowClassName?: (row: Row<TData>, index: number) => string;
}

function DataTable<
  TData extends RowData,
  TValue,
  TFilter extends Record<string, any> = Record<string, any>,
>({
  columns,
  data,
  search = '',
  setSearch,
  enableRowSelection = false,
  enableSearch = false,
  heading,
  customToolBar,
  enableColumnVisibility = false,
  enableFilter = false,
  filterContent,
  isFilterOpen,
  setIsFilterOpen,
  filter,
  filterSide,
  handleClearFilter,
  isLoading = false,
  pagination = { pageIndex: 0, pageSize: 10 },
  setPagination,
  totalItems = 0,
  filterClassName,
  noDataPlaceholder = 'No data found',
  enablePagination = true,
  sorting,
  setSorting,
  loaderRows = 10,
  handleColumnVisibility,
  rowSelection = {},
  onRowSelectionChange,
  tableClassName,
  enableMultiRowSelection = false,
  bindingKey,
  footer,
  highlightKey,
  highlightClassName,
  selectedHighlightClassName,
  disableSelectAllCheckbox,
  manualSorting = true,
  isInfiniteScroll = false,
  fetchMore,
  onScrollRef,
  onRowsSelected,
  isRowExpanded = false,
  isExpandAllRow,
  renderSubComponent,
  headerClassName,
  getRowClassName,
}: DataTableProps<TData, TValue, TFilter>) {
  const [expanded, setExpanded] = useState<ExpandedState>({});

  const SelectionColumn: ColumnDef<TData>[] = useMemo(
    () => [
      ...(isRowExpanded
        ? [
            {
              id: 'expander',
              header: () => null,
              size: 40,
              cell: ({ row }: any) => {
                return (
                  row.getCanExpand() && (
                    <Button
                      {...{ onClick: row?.getToggleExpandedHandler() }}
                      className="cursor-pointer p-[2px] m-0 h-fit text-center"
                      variant="ghost"
                    >
                      {row?.getIsExpanded() ? (
                        <ChevronDown className="w-5 h-5 text-primary" />
                      ) : (
                        <ChevronRight className="w-5 h-5" />
                      )}
                    </Button>
                  )
                );
              },
            },
          ]
        : []),
      ...(enableRowSelection
        ? [
            {
              id: 'select',
              size: 50,
              header: ({ table }: { table: ReactTable<TData> }) => {
                return (
                  enableMultiRowSelection && (
                    <Checkbox
                      disabled={!data?.length}
                      checked={
                        table.getIsAllPageRowsSelected() ||
                        (table.getIsSomePageRowsSelected() && 'indeterminate')
                      }
                      onCheckedChange={(value) =>
                        table.toggleAllPageRowsSelected(!!value)
                      }
                      aria-label="Select all"
                      className={cn(
                        table.getIsAllPageRowsSelected()
                          ? 'data-[state=checked]:bg-black data-[state=checked]:text-primary-foreground'
                          : table.getIsSomePageRowsSelected()
                            ? 'bg-grayScale-600 text-white'
                            : 'border-grayScale-400 border-2',
                        'w-5 h-5',
                        disableSelectAllCheckbox && 'hidden'
                      )}
                    >
                      {table.getIsSomePageRowsSelected() ? (
                        <MinusIcon />
                      ) : (
                        <CheckIcon />
                      )}
                    </Checkbox>
                  )
                );
              },
              cell: ({ row }: { row: Row<TData> }) => {
                return (
                  <Checkbox
                    checked={row.getIsSelected()}
                    onCheckedChange={(value) => row.toggleSelected(!!value)}
                    disabled={row.original?.disabledCheckBox}
                    className={cn(
                      row.getIsSelected()
                        ? 'data-[state=checked]:bg-grayScale-600 data-[state=checked]:text-primary-foreground'
                        : 'border-grayScale-400 border-2',
                      'w-5 h-5'
                    )}
                  >
                    <CheckIcon />
                  </Checkbox>
                );
              },
              enableSorting: false,
              enableHiding: false,
            },
          ]
        : []),
    ],
    [
      data?.length,
      disableSelectAllCheckbox,
      enableMultiRowSelection,
      enableRowSelection,
      isRowExpanded,
    ]
  );

  const tableColumns = useMemo(() => {
    return [...SelectionColumn, ...columns];
  }, [columns, SelectionColumn]);

  const table = useReactTable<TData>({
    data,
    columns: tableColumns,
    getCoreRowModel: getCoreRowModel(),
    onRowSelectionChange: (updater) => {
      const newSelection =
        typeof updater === 'function' ? updater(rowSelection) : updater;
      onRowSelectionChange?.(newSelection);

      onRowsSelected?.(
        data?.filter((item: any) =>
          Object.keys(newSelection)?.includes(
            item[bindingKey || 'id']?.toString()
          )
        )
      );
    },
    getSortedRowModel: getSortedRowModel(), //provide a sorting row model
    getExpandedRowModel: getExpandedRowModel(),
    sortingFns: {
      //add a custom sorting function
      myCustomSortingFn: (rowA, rowB, columnId) => {
        return rowA.original[columnId] > rowB.original[columnId]
          ? 1
          : rowA.original[columnId] < rowB.original[columnId]
            ? -1
            : 0;
      },
    },
    getPaginationRowModel: getPaginationRowModel(),
    manualPagination: true,
    manualSorting: manualSorting,
    enableSortingRemoval: false,
    sortDescFirst: true,
    enableMultiRowSelection: enableMultiRowSelection,
    onPaginationChange: setPagination,
    manualExpanding: true,
    onExpandedChange: setExpanded,
    getRowCanExpand: () => isRowExpanded,
    pageCount: Math.ceil(totalItems / pagination.pageSize),
    state: {
      rowSelection,
      sorting,
      pagination,
      columnPinning: { right: ['action'], left: ['expander', 'select'] },
      expanded,
    },
    getRowId: (row, index) => {
      // Fallback to index if the key doesn't exist
      return String(
        (row as Record<string, any>)[bindingKey ?? 'index'] ?? index
      );
    },
    onSortingChange: setSorting,
    enableColumnPinning: true,
    enableSorting: true,
  });

  // Toggles all rows' expansion based on `isExpandAllRow`, avoiding redundant updates.
  useEffect(() => {
    if (!table) return;

    table.getRowModel()?.rows?.forEach((row) => {
      if (row.getIsExpanded() !== isExpandAllRow) {
        row.toggleExpanded(isExpandAllRow);
      }
    });
  }, [isExpandAllRow, table]);

  const hasMore = isInfiniteScroll && data?.length < totalItems;
  const handleOnScroll = useCallback(
    (event: any) => {
      if (!isInfiniteScroll || !hasMore) return; // Stop if infinite scroll is disabled or no more data

      const target = event?.target;
      if (!target) return;

      requestAnimationFrame(() => {
        const { scrollTop, scrollHeight, clientHeight } = target;

        // Trigger when scrolled past 99% of total height
        const isAtBottom = (scrollTop + clientHeight) / scrollHeight >= 0.99;

        if (isAtBottom) {
          fetchMore?.();
        }
      });
    },
    [fetchMore, hasMore, isInfiniteScroll]
  );

  return (
    <div className="flex flex-col gap-2">
      {customToolBar && (
        <Toolbar
          heading={heading}
          search={search}
          setSearch={setSearch}
          customToolBar={customToolBar}
          enableFilter={enableFilter}
          isFilterOpen={isFilterOpen}
          setIsFilterOpen={setIsFilterOpen}
          filterContent={filterContent}
          filter={filter}
          filterSide={filterSide}
          enableSearch={enableSearch}
          filterClassName={filterClassName}
          handleClearFilter={handleClearFilter}
          enableColumnVisibility={enableColumnVisibility}
          handleColumnVisibility={handleColumnVisibility}
        />
      )}

      {enableFilter && filter && (
        <RenderFilters
          filter={filter as any}
          handleClearFilter={handleClearFilter}
        />
      )}
      <CustomDataTable
        table={table}
        totalItems={totalItems}
        loaderRows={loaderRows}
        tableColumnsLenght={tableColumns?.length}
        noDataPlaceholder={noDataPlaceholder}
        isLoading={isLoading}
        onScrollRef={onScrollRef}
        tableClassName={tableClassName}
        handleOnScroll={handleOnScroll}
        headerClassName={headerClassName}
        highlightKey={highlightKey}
        highlightClassName={highlightClassName}
        selectedRowHighlightClassName={selectedHighlightClassName}
        isRowExpanded={isRowExpanded}
        renderSubComponent={renderSubComponent}
        getRowClassName={getRowClassName}
      />

      {/* <div className="border-[1px]">
        <Table
          id="scrollableTableDiv"
          ref={onScrollRef}
          className="p-0"
          tableClassName={tableClassName}
          onScroll={isInfiniteScroll ? handleOnScroll : undefined}
        >
          <TableHeader
            className={cn('bg-grayScale-10 sticky top-0 z-10', headerClassName)}
          >
            {table?.getHeaderGroups()?.map((headerGroup) => (
              <TableRow
                key={headerGroup.id}
                className="bg-grayScale-10 sticky top-0 z-10"
              >
                {headerGroup?.headers?.map((header) => {
                  const enableSorting =
                    totalItems > 1 && header.column.columnDef.enableSorting;
                  const isPinned = header.column.getIsPinned();
                  return (
                    <TableHead
                      key={header.id}
                      className={cn(
                        'text-base font-medium text-grayScale-90   ',
                        isPinned
                          ? 'drop-shadow-[0_2px_5px_rgba(0,0,0,0.1)]  sticky left-0 right-0 bg-[#eaeaeb] z-[1]'
                          : 'relative border-r-[1px] border-grayScale-20'
                      )}
                      style={{
                        minWidth:
                          !header.column.getIsPinned() ||
                          header.column.id !== 'select'
                            ? `${header.column.getSize()}px`
                            : '',
                        width:
                          header.column.getIsPinned() ||
                          header.column.id === 'select'
                            ? `${header.column.getSize()}px`
                            : '',
                        maxWidth: header.column.columnDef.maxSize
                          ? `${header.column.columnDef.maxSize}px`
                          : '',
                      }}
                    >
                      <div className="flex items-center">
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                        <div
                          onClick={
                            enableSorting
                              ? header?.column?.getToggleSortingHandler()
                              : undefined
                          }
                          className={cn(
                            'flex items-center gap-1',
                            enableSorting ? 'cursor-pointer' : ''
                          )}
                          title={
                            enableSorting && header.column.getCanSort()
                              ? header.column.getNextSortingOrder() === 'asc'
                                ? 'Sort descending'
                                : 'Sort ascending'
                              : undefined
                          }
                        >
                          {{
                            asc: <ChevronDown size={16} />,
                            desc: <ChevronUp size={16} />,
                          }[header.column.getIsSorted() as string] ??
                            (enableSorting && <ChevronsUpDown size={16} />)}
                        </div>
                      </div>
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>

          <TableBody className=" border-grayScale-20">
            {isLoading
              ? [...Array(loaderRows)].map(() =>
                  table?.getHeaderGroups()?.map((headerGroup) => (
                    <TableRow key={headerGroup?.id}>
                      {headerGroup?.headers?.map((header) => {
                        return (
                          <TableCell key={header?.id} className="py-2">
                            <Skeleton key={header?.id} className="h-7 w-full" />
                          </TableCell>
                        );
                      })}
                    </TableRow>
                  ))
                )
              : table.getRowModel().rows.map((row, index) => {
                  const isOverbooked =
                    overbookedKey && row.original?.[overbookedKey] === true;
                  return (
                    <React.Fragment key={`${row.id}-${index}`}>
                      <TableRow
                        key={row.id}
                        data-state={
                          row.getIsSelected() ? 'selected' : undefined
                        }
                        className={cn(
                          'border-grayScale-20',
                          isOverbooked ? 'bg-red-100 hover:bg-red-100' : '', // Apply red if overbooked

                          row.getIsSelected() && isOverbooked
                            ? 'data-[state=selected]:bg-red-100'
                            : '' // Apply red if both selected and overbooked
                        )}
                      >
                        {row.getVisibleCells().map((cell) => {
                          const isPinned = cell.column.getIsPinned();
                          const columnDef = cell.column.columnDef as {
                            className?: string | ((row: Row<TData>) => string);
                          };
                          const className =
                            typeof columnDef.className === 'function'
                              ? columnDef.className(row)
                              : columnDef.className;
                          return (
                            <TableCell
                              key={cell.id}
                              className={cn(
                                'text-base  text-grayScale-60 pt-1.5 pb-1.5 border-grayScale-20 [&:has([role=checkbox])]:pr-2',
                                isPinned
                                  ? 'drop-shadow-[0_3px_5px_rgba(0,0,0,0.1)] bg-white z-[1] left-0 right-0 sticky '
                                  : 'bg-transparent relative border-r-[1px]',
                                isOverbooked ? 'bg-red-100' : '',
                                className
                              )}
                              style={{
                                minWidth:
                                  !cell.column.getIsPinned() ||
                                  cell.column.id !== 'select'
                                    ? `${cell.column.getSize()}px`
                                    : '',
                                width:
                                  cell.column.getIsPinned() ||
                                  cell.column.id === 'select'
                                    ? `${cell.column.getSize()}px`
                                    : '',
                                maxWidth:
                                  String(cell.getValue())?.length > 30
                                    ? '80px'
                                    : cell.column.columnDef.maxSize
                                      ? `${cell.column.columnDef.maxSize}px`
                                      : '',
                              }}
                            >
                              {String(cell.getValue())?.length > 30 ? (
                                <TooltipWidget
                                  className="p-3 text-black max-w-80 max-h-80 overflow-y-auto break-words"
                                  tooltip={String(cell.getValue()) ?? ''}
                                  side="top"
                                  align="start"
                                >
                                  <div className="truncate">
                                    {flexRender(
                                      cell.column.columnDef.cell,
                                      cell.getContext()
                                    )}
                                  </div>
                                </TooltipWidget>
                              ) : (
                                flexRender(
                                  cell.column.columnDef.cell,
                                  cell.getContext()
                                )
                              )}
                            </TableCell>
                          );
                        })}
                      </TableRow>
                      {row?.getIsExpanded() && (
                        <TableRow>
                          Render the custom sub-component when the row is expanded 
                          <TableCell></TableCell>
                          <TableCell
                            colSpan={row?.getVisibleCells()?.length}
                            className="p-0"
                          >
                            {renderSubComponent?.({ row })}
                          </TableCell>
                        </TableRow>
                      )}
                    </React.Fragment>
                  );
                })}
            {!isLoading &&
              (!table?.getRowModel()?.rows?.length ||
                table?.getRowModel()?.rows?.length === 0) && (
                <TableRow>
                  <TableCell
                    colSpan={tableColumns?.length}
                    className="h-16 text-center"
                  >
                    {noDataPlaceholder}
                  </TableCell>
                </TableRow>
              )}
          </TableBody>
        </Table>
      </div> */}

      {data?.length > 0 && enablePagination && (
        <Pagination
          table={table}
          totalItems={totalItems}
          pagination={pagination}
          setPagination={setPagination}
        />
      )}

      {footer && footer}
    </div>
  );
}

export default DataTable;
