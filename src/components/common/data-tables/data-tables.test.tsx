import { render, screen } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import DataTable from './index';

const columns = [
  { id: 'name', header: 'Name', accessor: 'name' },
  { id: 'email', header: 'Email', accessor: 'email' },
];

const data = [
  { name: '<PERSON>', email: '<EMAIL>' },
  { name: '<PERSON>', email: '<EMAIL>' },
];

describe('DataTable Component', () => {
  it('renders the table with data and handles row selection and pagination', async () => {
    const setPagination = vi.fn();
    const setSorting = vi.fn();

    const renderedData = render(
      <DataTable
        columns={columns}
        data={data}
        isLoading={false}
        pagination={{ pageIndex: 0, pageSize: 10 }}
        setPagination={setPagination}
        sorting={[]}
        setSorting={setSorting}
        totalItems={data.length}
        enablePagination={true}
      />
    );
    expect(renderedData);
  });

  it('shows a loading skeleton when data is loading', () => {
    const isRennderProgressBar = render(
      <DataTable
        columns={columns}
        data={[]}
        isLoading={true}
        pagination={{ pageIndex: 0, pageSize: 10 }}
        setPagination={() => {}}
        sorting={[]}
        setSorting={() => {}}
        totalItems={0}
        enablePagination={false}
      />
    );
    expect(isRennderProgressBar);
  });

  it('shows no data message when no data is available', () => {
    render(
      <DataTable
        columns={columns}
        data={[]}
        isLoading={false}
        pagination={{ pageIndex: 0, pageSize: 10 }}
        setPagination={() => {}}
        sorting={[]}
        setSorting={() => {}}
        totalItems={0}
        enablePagination={false}
      />
    );
    expect(screen.getByText('No data found')).toBeInTheDocument();
  });
});
