import { Skeleton } from '@/components/ui/skeleton';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { cn } from '@/lib/utils';
import { flexRender, Row, RowData } from '@tanstack/react-table';
import { ChevronDown, ChevronsUpDown, ChevronUp } from 'lucide-react';
import React from 'react';
import TooltipWidget from '../../tooltip-widget';

interface CustomTableProps<TData extends RowData> {
  table: any;
  totalItems: number;
  isLoading: boolean;
  onScrollRef?: string;
  tableClassName?: string;
  handleOnScroll?: (event: React.UIEvent<HTMLTableElement>) => void | undefined;
  headerClassName?: string;
  loaderRows: number;
  highlightKey?: string;
  highlightClassName?: string;
  selectedRowHighlightClassName?: string;
  tableColumnsLenght: number;
  noDataPlaceholder: string;
  renderSubComponent?: (props: { row: Row<TData> }) => React.ReactElement;
  isRowExpanded?: boolean;
  getRowClassName?: (row: Row<TData>, index: number) => string;
}
const CustomDataTable = <TData extends RowData>({
  table,
  totalItems = 0,
  isLoading,
  onScrollRef,
  tableClassName,
  handleOnScroll,
  headerClassName,
  loaderRows,
  highlightKey,
  highlightClassName,
  selectedRowHighlightClassName,
  tableColumnsLenght,
  noDataPlaceholder,
  renderSubComponent,
  isRowExpanded = false,
  getRowClassName,
}: CustomTableProps<TData>) => {
  return (
    <div className="border-[1px]">
      <Table
        id="scrollableTableDiv"
        ref={onScrollRef}
        className="p-0"
        tableClassName={tableClassName}
        onScroll={handleOnScroll}
      >
        <TableHeader
          className={cn('bg-grayScale-10 sticky top-0 z-10', headerClassName)}
        >
          {table
            ?.getHeaderGroups()
            ?.map(
              (headerGroup: {
                id: React.Key | null | undefined;
                headers: any[];
              }) => (
                <TableRow
                  key={headerGroup.id}
                  className="bg-grayScale-10 sticky top-0 z-10"
                >
                  {headerGroup?.headers?.map((header) => {
                    const enableSorting =
                      totalItems > 1 && header.column.columnDef.enableSorting;
                    const isPinned = header.column.getIsPinned();
                    return (
                      <TableHead
                        key={header.id}
                        className={cn(
                          'text-base font-medium text-grayScale-90   ',
                          isPinned
                            ? 'drop-shadow-[0_2px_5px_rgba(0,0,0,0.1)]  sticky left-0 right-0 bg-[#eaeaeb] z-[1]'
                            : 'relative border-r-[1px] border-grayScale-20'
                        )}
                        style={{
                          minWidth:
                            !header.column.getIsPinned() ||
                            header.column.id !== 'select'
                              ? `${header.column.getSize()}px`
                              : '',
                          width:
                            header.column.getIsPinned() ||
                            header.column.id === 'select'
                              ? `${header.column.getSize()}px`
                              : '',
                          maxWidth: header.column.columnDef.maxSize
                            ? `${header.column.columnDef.maxSize}px`
                            : '',
                        }}
                      >
                        <div className="flex items-center">
                          {header.isPlaceholder
                            ? null
                            : flexRender(
                                header.column.columnDef.header,
                                header.getContext()
                              )}
                          <div
                            onClick={
                              enableSorting
                                ? header?.column?.getToggleSortingHandler()
                                : undefined
                            }
                            className={cn(
                              'flex items-center gap-1',
                              enableSorting ? 'cursor-pointer' : ''
                            )}
                            title={
                              enableSorting && header.column.getCanSort()
                                ? header.column.getNextSortingOrder() === 'asc'
                                  ? 'Sort descending'
                                  : 'Sort ascending'
                                : undefined
                            }
                          >
                            {{
                              asc: <ChevronDown size={16} />,
                              desc: <ChevronUp size={16} />,
                            }[header.column.getIsSorted() as string] ??
                              (enableSorting && <ChevronsUpDown size={16} />)}
                          </div>
                        </div>
                      </TableHead>
                    );
                  })}
                </TableRow>
              )
            )}
        </TableHeader>

        <TableBody className=" border-grayScale-20">
          {isLoading
            ? [...Array(loaderRows)].map(() =>
                table
                  ?.getHeaderGroups()
                  ?.map(
                    (headerGroup: {
                      id: React.Key | null | undefined;
                      headers: any[];
                    }) => (
                      <TableRow key={headerGroup?.id}>
                        {headerGroup?.headers?.map((header) => {
                          return (
                            <TableCell key={header?.id} className="py-2">
                              <Skeleton
                                key={header?.id}
                                className="h-7 w-full"
                              />
                            </TableCell>
                          );
                        })}
                      </TableRow>
                    )
                  )
              )
            : table.getRowModel().rows.map((row: Row<any>, index: number) => {
                const isHighlight =
                  highlightKey && row.original?.[highlightKey] === true;
                return (
                  <React.Fragment key={`${row.id}-${index}`}>
                    <TableRow
                      key={row.id}
                      data-state={row.getIsSelected() ? 'selected' : undefined}
                      className={cn(
                        'border-grayScale-20',
                        isHighlight ? highlightClassName : '',

                        row.getIsSelected() && isHighlight
                          ? selectedRowHighlightClassName
                          : '',
                        getRowClassName?.(row, index)
                      )}
                    >
                      {row.getVisibleCells().map((cell) => {
                        const isPinned = cell.column.getIsPinned();
                        const columnDef = cell.column.columnDef as {
                          className?: string | ((row: Row<any>) => string);
                        };
                        const className =
                          typeof columnDef.className === 'function'
                            ? columnDef.className(row)
                            : columnDef.className;
                        return (
                          <TableCell
                            key={cell.id}
                            className={cn(
                              'text-base  text-grayScale-60 py-0 border-grayScale-20 [&:has([role=checkbox])]:pr-2',
                              isPinned
                                ? 'drop-shadow-[0_3px_5px_rgba(0,0,0,0.1)] bg-white z-[1] left-0 right-0 sticky '
                                : 'bg-transparent relative border-r-[1px]',
                              isHighlight ? highlightClassName : '',
                              className
                            )}
                            style={{
                              minWidth:
                                !cell.column.getIsPinned() ||
                                cell.column.id !== 'select'
                                  ? `${cell.column.getSize()}px`
                                  : '',
                              width:
                                cell.column.getIsPinned() ||
                                cell.column.id === 'select'
                                  ? `${cell.column.getSize()}px`
                                  : '',
                              maxWidth:
                                String(cell.getValue())?.length > 25
                                  ? '80px'
                                  : cell.column.columnDef.maxSize
                                    ? `${cell.column.columnDef.maxSize}px`
                                    : '',
                            }}
                          >
                            {String(cell.getValue())?.length > 25 ? (
                              <TooltipWidget
                                className="p-3 text-black max-w-80 max-h-80 overflow-y-auto break-words"
                                tooltip={String(cell.getValue()) ?? ''}
                                side="top"
                                align="start"
                              >
                                <div className="truncate p-1">
                                  {flexRender(
                                    cell.column.columnDef.cell,
                                    cell.getContext()
                                  )}
                                </div>
                              </TooltipWidget>
                            ) : (
                              <div className="truncate p-1">
                                {flexRender(
                                  cell.column.columnDef.cell,
                                  cell.getContext()
                                )}
                              </div>
                            )}
                          </TableCell>
                        );
                      })}
                    </TableRow>
                    {isRowExpanded && row?.getIsExpanded() && (
                      <TableRow>
                        {/*  Render the custom sub-component when the row is expanded */}
                        {isRowExpanded && <TableCell></TableCell>}
                        <TableCell
                          colSpan={row?.getVisibleCells()?.length}
                          className="p-0"
                        >
                          {renderSubComponent?.({ row })}
                        </TableCell>
                      </TableRow>
                    )}
                  </React.Fragment>
                );
              })}
          {!isLoading &&
            (!table?.getRowModel()?.rows?.length ||
              table?.getRowModel()?.rows?.length === 0) && (
              <TableRow>
                <TableCell
                  colSpan={tableColumnsLenght}
                  className="h-16 text-center"
                >
                  {noDataPlaceholder}
                </TableCell>
              </TableRow>
            )}
        </TableBody>
      </Table>
    </div>
  );
};

export default CustomDataTable;
