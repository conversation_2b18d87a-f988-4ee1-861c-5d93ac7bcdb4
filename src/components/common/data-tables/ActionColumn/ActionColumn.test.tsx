import { render, fireEvent, screen } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import ActionColumnMenu from './index';

describe('ActionColumnMenu Component', () => {
  it('renders the ActionColumnMenu component with proper actions', async () => {
    const onEditMock = vi.fn();
    const onDeleteMock = vi.fn();
    const onCopyMock = vi.fn();
    const onClickMenuMock = vi.fn();

    render(
      <ActionColumnMenu
        onEdit={onEditMock}
        onDelete={onDeleteMock}
        onCopy={onCopyMock}
        onClickMenu={onClickMenuMock}
        menuLabel="Custom Menu"
        menuIcon={<></>}
      />
    );

    const viewDetailsButton = screen.getByText('View details');
    expect(viewDetailsButton).toBeInTheDocument();

    fireEvent.click(viewDetailsButton);
    expect(onEditMock).toHaveBeenCalledTimes(1);
  });

  it('does not render menu items that do not have onClick function', async () => {
    const onDeleteMock = vi.fn();

    const isDelete = render(<ActionColumnMenu onDelete={onDeleteMock} />);
    expect(isDelete);
  });
});
