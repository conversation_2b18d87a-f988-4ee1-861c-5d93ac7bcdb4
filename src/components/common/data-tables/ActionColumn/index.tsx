import DotIcon from '@/assets/icons/DotIcon';
import TrashIcon from '@/assets/icons/TrashIcon';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuPortal,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';
import { DropdownMenuListType } from '@/types/common.types';
import { Copy, SquareArrowOutUpRight } from 'lucide-react';
import { ReactNode, useMemo } from 'react';
import AppButton from '../../app-button';

interface ActionColumnProps {
  onEdit?: () => void;
  customEdit?: ReactNode;
  onDelete?: () => void | undefined;
  onCopy?: () => Promise<void> | void;
  menuLabel?: string;
  onClickMenu?: () => Promise<void> | void;
  menuIcon?: ReactNode;
  menuClassName?: string;
  contentClassName?: string;
  dropdownMenuList?: DropdownMenuListType[];
  triggerClassName?: string;
  label?: string;
  icon?: React.FC<React.SVGProps<SVGSVGElement>>;
  editClassName?: string;
  triggerContent?: ReactNode;
  disabled?: boolean;
  subClassName?: string;
}
const ActionColumnMenu = ({
  onEdit,
  customEdit,
  onDelete,
  onCopy,
  menuLabel,
  onClickMenu,
  menuIcon,
  menuClassName,
  contentClassName,
  dropdownMenuList = [],
  triggerClassName,
  editClassName,
  label = 'View details',
  icon = SquareArrowOutUpRight,
  triggerContent = <DotIcon />,
  disabled,
  subClassName,
}: ActionColumnProps) => {
  const dropdownMenuItem = useMemo(() => {
    const menuList: DropdownMenuListType[] = [
      ...(dropdownMenuList || []),
      {
        label: 'Delete',
        onClick: onDelete,
        icon: <TrashIcon />,
        className: 'text-base text-text-danger',
      },
      {
        label: 'Clone',
        onClick: onCopy,
        icon: <Copy />,
        className: 'text-base text-text-brand-violet-Default',
      },
      {
        ...(menuLabel && {
          label: menuLabel,
          onClick: onClickMenu,
          icon: menuIcon,
          className: menuClassName,
        }),
      },
    ];

    return menuList?.filter((item) => item?.onClick || item?.subMenu?.length);
  }, [
    dropdownMenuList,
    menuClassName,
    menuIcon,
    menuLabel,
    onClickMenu,
    onCopy,
    onDelete,
  ]);

  return (
    <div className="flex flex-row gap-x-4 justify-center items-center">
      {customEdit ||
        (onEdit && (
          <AppButton
            label={label}
            variant="neutral"
            className={editClassName}
            icon={icon}
            iconClassName="w-4 h-4"
            onClick={onEdit}
          />
        ))}
      <DropdownMenu>
        {(onDelete ||
          onCopy ||
          onClickMenu ||
          dropdownMenuList?.length !== 0) && (
          <DropdownMenuTrigger asChild disabled={disabled}>
            <Button
              variant="ghost"
              className={cn('h-8 w-8 p-0', triggerClassName)}
            >
              {triggerContent}
            </Button>
          </DropdownMenuTrigger>
        )}
        <DropdownMenuContent
          align="end"
          className={cn('w-[200px] rounded-xl z-[100]', contentClassName)}
        >
          {dropdownMenuItem?.map((item, index: number) => {
            const isSubMenu = item?.subMenu?.length;
            return (
              <DropdownMenuGroup key={`${item?.label}-${index}`}>
                {!isSubMenu ? (
                  <DropdownMenuItem
                    className={cn(item?.className)}
                    onClick={item?.disabled ? undefined : item?.onClick}
                    disabled={item?.disabled}
                  >
                    <div className="flex flex-row gap-3 items-center">
                      {item?.icon}
                      <span>{item?.label}</span>
                    </div>
                  </DropdownMenuItem>
                ) : (
                  <DropdownMenuSub>
                    <DropdownMenuSubTrigger
                      className={cn(
                        'flex flex-row gap-3 items-center',
                        item?.className
                      )}
                    >
                      {item?.icon}
                      <span>{item?.label}</span>
                    </DropdownMenuSubTrigger>
                    <DropdownMenuPortal>
                      <DropdownMenuSubContent className={cn(subClassName)}>
                        {item?.subMenu?.map((subMenu, index) => {
                          return (
                            <DropdownMenuItem
                              key={`${subMenu?.label}-${index}`}
                              onClick={subMenu?.onClick}
                              className={cn(
                                'flex flex-row gap-3 items-center',
                                subMenu?.className
                              )}
                            >
                              {subMenu?.icon}
                              <span>{subMenu?.label}</span>
                            </DropdownMenuItem>
                          );
                        })}
                      </DropdownMenuSubContent>
                    </DropdownMenuPortal>
                  </DropdownMenuSub>
                )}
              </DropdownMenuGroup>
            );
          })}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

export default ActionColumnMenu;
