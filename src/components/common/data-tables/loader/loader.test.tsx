import '@testing-library/jest-dom';
import { render } from '@testing-library/react';
import { describe, expect, it } from 'vitest';
import { ColumnDef } from '@tanstack/react-table';
import Loader from '.';

describe('Loader', () => {
  const mockColumns: ColumnDef<any>[] = [
    { accessorKey: 'column1', header: 'Column 1' },
    { accessorKey: 'column2', header: 'Column 2' },
    { accessorKey: 'column3', header: 'Column 3' },
  ];

  it('renders the correct number of skeletons based on columns', () => {
    const { container } = render(<Loader columns={mockColumns} />);

    // Check the total number of Skeleton elements
    const skeletons = container.querySelectorAll('.h-10.w-full'); // Assuming Skeleton has these classes
    const expectedSkeletons = mockColumns.length * 10; // 10 rows of skeletons

    expect(skeletons).toHaveLength(expectedSkeletons);
  });

  it('renders skeletons in the correct layout', () => {
    const { container } = render(<Loader columns={mockColumns} />);

    // Check the first row for the correct number of skeletons
    const firstRowSkeletons = container.querySelectorAll(
      '.flex.space-x-4:first-child .h-10'
    );
    expect(firstRowSkeletons.length).toBe(mockColumns.length);

    // Check total number of rows
    const rows = container.querySelectorAll('.flex.space-x-4');
    expect(rows.length).toBe(10); // Ensure there are 11 rows
  });
});
