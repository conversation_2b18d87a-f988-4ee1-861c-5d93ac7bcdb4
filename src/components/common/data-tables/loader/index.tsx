import { Skeleton } from '@/components/ui/skeleton';
import { ColumnDef } from '@tanstack/react-table';

interface LoaderProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  rowPerTable?: number;
}
const Loader = <TData, TValue>({
  columns,
  rowPerTable = 10,
}: LoaderProps<TData, TValue>) => {
  return (
    <div className="space-y-4">
      {[...Array(rowPerTable)].map((_, index) => (
        <div key={index} className="flex space-x-4">
          {columns.map((_column, colIndex) => (
            <Skeleton key={colIndex} className="h-10 w-full" />
          ))}
        </div>
      ))}
    </div>
  );
};

export default Loader;
