import CancelIcon from '@/assets/icons/CancelIcon';
import DotIcon from '@/assets/icons/DotIcon';
import FilterIcon from '@/assets/icons/FilterIcon';
import SearchIcon from '@/assets/icons/SearchIcon';
import { Input } from '@/components/ui/input';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import debounce from 'lodash/debounce';
import React, { useEffect, useState } from 'react';
import IconButton from '../../icon-button';

interface ToolbarProps<TFilter = Record<string, any>> {
  heading?: React.ReactNode;
  search: string;
  enableSearch?: boolean;
  setSearch?: (search: string) => void;
  customToolBar?: React.ReactNode;
  enableFilter?: boolean;
  isFilterOpen?: boolean;
  setIsFilterOpen?: React.Dispatch<React.SetStateAction<boolean>>;
  filterContent?: React.ReactNode;
  filter?: TFilter;
  enableColumnVisibility?: boolean;
  handleClearFilter?: (filterKey: string) => void;
  filterClassName?: string;
  handleColumnVisibility?: () => void;
  filterSide?: 'top' | 'right' | 'bottom' | 'left';
}

const Toolbar = <TFilter = Record<string, any>,>({
  heading,
  enableSearch,
  search,
  setSearch,
  customToolBar,
  enableFilter = false,
  isFilterOpen = false,
  setIsFilterOpen,
  filterContent,
  enableColumnVisibility,
  filterClassName,
  handleColumnVisibility,
  filterSide = 'bottom',
}: ToolbarProps<TFilter>) => {
  const [debouncedSearch, setDebouncedSearch] = useState(search);

  const handleFilterToggle = () => {
    if (setIsFilterOpen) {
      setIsFilterOpen((prev) => !prev);
    }
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setDebouncedSearch(event.target.value); // Set the input value
  };

  const handleClearSearch = () => {
    if (setSearch) {
      setSearch(''); // Clear the search input immediately
    }
    setDebouncedSearch(''); // Clear the debounced search
  };

  // Debounced search handler
  const debouncedSearchHandler = debounce((newSearch: string) => {
    setSearch?.(newSearch);
  }, 500);

  // Effect to update debouncedSearch when input changes
  useEffect(() => {
    debouncedSearchHandler(debouncedSearch); // Trigger debounced function
    // Cleanup the debounced function on component unmount
    return () => {
      debouncedSearchHandler.cancel();
    };
  }, [debouncedSearch, debouncedSearchHandler]);

  return (
    <div className="flex gap-x-4 justify-between items-center w-full">
      {heading && (
        <h1 className="text-2xl font-semibold text-[#181A1D] w-full">
          {heading}
        </h1>
      )}

      <div className="flex gap-x-2 items-center justify-end w-full">
        {customToolBar}

        {enableSearch && (
          <div className="flex relative">
            <Input
              value={debouncedSearch} // Controlled input
              placeholder="Search"
              onChange={handleSearchChange} // Call the search handler
              className="text-base  placeholder:text-[#B3B3B3] w-[239px]"
            />
            {search?.length > 0 ? (
              <CancelIcon
                className="text-[#c7ccea] absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer"
                onClick={handleClearSearch}
              />
            ) : (
              <SearchIcon className="text-[#c7ccea] absolute right-3 top-1/2 transform -translate-y-1/2" />
            )}
          </div>
        )}

        {enableFilter && (
          <Popover open={isFilterOpen} onOpenChange={setIsFilterOpen}>
            <PopoverTrigger asChild>
              <IconButton onClick={handleFilterToggle}>
                <FilterIcon />
              </IconButton>
            </PopoverTrigger>
            <PopoverContent
              className={cn(
                'flex flex-col border-border-Default w-full p-2  max-h-[78vh] overflow-auto',
                filterClassName
              )}
              align="end"
              side={filterSide}
            >
              {filterContent}
            </PopoverContent>
          </Popover>
        )}
        {enableColumnVisibility && (
          <IconButton onClick={handleColumnVisibility}>
            <DotIcon />
          </IconButton>
        )}
      </div>
    </div>
  );
};

export default Toolbar;
