import { render, screen, fireEvent } from '@testing-library/react';
import RenderFilters from '.';
import { vi, describe, test, expect } from 'vitest';

vi.mock('../../tag', () => ({
  __esModule: true,
  default: ({
    children,
    onClick,
  }: {
    children: React.ReactNode;
    onClick: () => void;
  }) => (
    <div data-testid="tag" onClick={onClick}>
      {children}
    </div>
  ),
}));

describe('RenderFilters Component', () => {
  const mockFilters = [
    { label: 'Status', name: 'status', value: 'active', tagValue: 'Active' },
    { label: 'Type', name: 'type', value: 'premium', tagValue: 'Premium' },
    { label: 'Category', name: 'category', value: '', tagValue: 'Unused' },
  ];

  test('renders filter tags with valid values', () => {
    render(<RenderFilters filter={mockFilters} />);

    // Verify that tags with valid values are rendered
    expect(screen.getByText('Status: Active')).toBeInTheDocument();
    expect(screen.getByText('Type: Premium')).toBeInTheDocument();

    // Verify that tags with empty values are not rendered
    expect(screen.queryByText('Category: Unused')).not.toBeInTheDocument();
  });

  test('calls handleClearFilter when a tag is clicked', () => {
    const mockHandleClearFilter = vi.fn();
    render(
      <RenderFilters
        filter={mockFilters}
        handleClearFilter={mockHandleClearFilter}
      />
    );

    // Find all rendered tags
    const tags = screen.getAllByTestId('tag');
    expect(tags).toHaveLength(2); // Only two valid tags should render

    // Simulate clicking the first tag
    fireEvent.click(tags[0]);

    // Verify that the click handler is called with the correct argument
    expect(mockHandleClearFilter).toHaveBeenCalledWith('status');
  });

  test('renders nothing when no filters have valid values', () => {
    const emptyFilters = [
      { label: 'Status', name: 'status', value: '', tagValue: 'Active' },
      { label: 'Type', name: 'type', value: '', tagValue: 'Premium' },
    ];

    render(<RenderFilters filter={emptyFilters} />);

    // Verify that no tags are rendered
    expect(screen.queryByTestId('tag')).not.toBeInTheDocument();
  });
});
