import Tag from '../../tag';
import TooltipWidget from '../../tooltip-widget';

interface RenderFiltersProps {
  filter: any;
  handleClearFilter?: (filterKey: string) => void;
}

const RenderFilters = ({ filter, handleClearFilter }: RenderFiltersProps) => {
  return (
    <div className="flex gap-1 flex-wrap">
      {filter?.map((item: any) => {
        // Check if the item.value has a non-empty value
        if (item?.value) {
          return (
            <Tag
              key={item.label}
              onClick={() => handleClearFilter?.(item?.name)}
            >
              {`${item?.label}:`}{' '}
              {item?.tagValue?.length > 50 ? (
                <TooltipWidget
                  tooltip={item?.tagValue}
                  className="max-w-64 max-h-64 overflow-y-auto"
                >
                  <span className="truncate max-w-[350px]">
                    {item?.tagValue}
                  </span>
                </TooltipWidget>
              ) : (
                item?.tagValue
              )}
            </Tag>
          );
        }
        return null; // Return nothing if there's no value
      })}
    </div>
  );
};

export default RenderFilters;
