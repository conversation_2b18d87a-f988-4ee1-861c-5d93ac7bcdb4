import React, { forwardRef } from 'react';
import { cn } from '@/lib/utils';

interface IconButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  variant?: 'primary' | 'neutral';
}

// Define the styles for the button
const TAG_STYLES = {
  base: 'border-[1px] p-3 rounded-[8px] cursor-pointer hover:bg-background-secondary-hover',
  variants: {
    primary:
      'bg-background-brand-violet-default text-white border-border-brand-violet-Default',
    neutral: 'bg-white text-[#012F31] border-border',
  },
};

// Use forwardRef to allow refs to be passed to the component
const IconButton = forwardRef<HTMLButtonElement, IconButtonProps>(
  ({ onClick, children, variant = 'neutral', className, ...props }, ref) => {
    const variantStyles =
      TAG_STYLES.variants[variant] || TAG_STYLES.variants.neutral;

    return (
      <button
        ref={ref}
        className={cn(TAG_STYLES.base, variantStyles, className)}
        onClick={onClick}
        {...props}
      >
        {children}
      </button>
    );
  }
);

IconButton.displayName = 'IconButton';

export default IconButton;
