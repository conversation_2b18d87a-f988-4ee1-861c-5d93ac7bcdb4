import { render, screen, fireEvent } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import IconButton from '.';

describe('IconButton Component', () => {
  it('renders the button with children', () => {
    render(<IconButton>Click Me</IconButton>);

    expect(screen.getByText('Click Me')).toBeInTheDocument();
  });

  it('applies default neutral styles', () => {
    render(<IconButton>Neutral Button</IconButton>);

    const button = screen.getByText('Neutral Button');
    expect(button).toHaveClass('bg-white');
    expect(button).toHaveClass('text-[#012F31]');
  });

  it('applies primary variant styles', () => {
    render(<IconButton variant="primary">Primary Button</IconButton>);

    const button = screen.getByText('Primary Button');
    expect(button).toHaveClass('bg-background-brand-violet-default');
    expect(button).toHaveClass('text-white');
  });

  it('calls onClick when the button is clicked', () => {
    const handleClick = vi.fn();
    render(<IconButton onClick={handleClick}>Clickable Button</IconButton>);

    const button = screen.getByText('Clickable Button');
    fireEvent.click(button);

    expect(handleClick).toHaveBeenCalled();
  });

  it('applies custom className', () => {
    render(
      <IconButton className="custom-class">Custom Class Button</IconButton>
    );

    const button = screen.getByText('Custom Class Button');
    expect(button).toHaveClass('custom-class');
  });
});
