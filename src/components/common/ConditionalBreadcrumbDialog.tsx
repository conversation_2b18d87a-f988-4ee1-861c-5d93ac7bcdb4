import React from 'react';
import CustomDialog from './dialog';
import BreadcrumbWithCustomSeparator from './BreadCrumbWithSeparator';

interface ListItem {
  label: string;
  value: string;
  content: React.ReactNode;
}

export type BreadcrumbType = {
  label: string;
  value: string;
};

interface ConditionalBreadcrumbDialogProps {
  list: ListItem[];
  active: string;
  showTabMenu?: boolean;
  className?: string;
  contentClassName?: string;
  open: boolean;
  onOpenChange: (open?: boolean) => void;
  breadcrumbs: BreadcrumbType[];
  onBreadcrumbClick?: (value: string) => void;
}

const ConditionalBreadcrumbDialog = ({
  list,
  open,
  onOpenChange,
  contentClassName,
  className,
  active,
  breadcrumbs,
  onBreadcrumbClick,
}: ConditionalBreadcrumbDialogProps) => {
  const listMap = new Map(list.map((tab) => [tab.value, tab.content]));
  const listContent = listMap.get(active);

  const goBack = (value: string) => {
    onBreadcrumbClick?.(value);
  };

  return (
    <CustomDialog
      onOpenChange={onOpenChange}
      description=""
      open={open}
      className={className}
      contentClassName={contentClassName}
      title=""
    >
      <div className="pl-6 pr-6 flex flex-col gap-4 h-full">
        <BreadcrumbWithCustomSeparator
          breadcrumbs={breadcrumbs}
          goBack={goBack}
        />
        {listContent}
      </div>
    </CustomDialog>
  );
};

export default ConditionalBreadcrumbDialog;
