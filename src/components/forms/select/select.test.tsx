import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect } from 'vitest';
import { useForm } from 'react-hook-form';
import SelectWidget, { ListType } from './index';
import { TEXT_VALIDATION_RULE } from '@/constants/validation-constants';

describe('SelectWidget Component', () => {
  const mockOptions: ListType[] = [
    { value: '1', label: 'Option 1' },
    { value: '2', label: 'Option 2', disabled: false },
    { value: '3', label: 'Option 3', isInactive: true },
  ];

  const renderWithForm = (props = {}) => {
    const Wrapper = () => {
      const form = useForm();

      const onSubmit = vi.fn();

      return (
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <SelectWidget
            name="testSelect"
            label="Test Select"
            optionsList={mockOptions}
            form={form}
            validation={TEXT_VALIDATION_RULE}
            {...props}
          />
          <button type="submit">Submit</button>
        </form>
      );
    };

    return render(<Wrapper />);
  };

  it('renders the component with label and placeholder', () => {
    renderWithForm({ placeholder: 'Select an option' });

    expect(screen.getByText('Test Select')).toBeInTheDocument();
    expect(screen.getByText('Select an option')).toBeInTheDocument();
  });

  it('allows selecting an option', async () => {
    renderWithForm();

    const selectInput = screen.getByRole('combobox');

    fireEvent.keyDown(selectInput, { key: 'ArrowDown' });
    fireEvent.click(screen.getByText('Option 1'));

    await waitFor(() => {
      expect(screen.queryByText('Option 1')).toBeInTheDocument();
    });
  });

  it('calls onSelectChange callback when option is selected', async () => {
    const onSelectChange = vi.fn();
    renderWithForm({ onSelectChange });

    const selectInput = screen.getByRole('combobox');

    fireEvent.keyDown(selectInput, { key: 'ArrowDown' });
    fireEvent.click(screen.getByText('Option 1'));

    await waitFor(() => {
      expect(onSelectChange).toHaveBeenCalledWith('1');
    });
  });
});
