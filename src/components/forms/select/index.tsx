import { cn } from '@/lib/utils';
import get from 'lodash/get';
import { useEffect, useState } from 'react';
import {
  Controller,
  FieldValues,
  Path,
  RegisterOptions,
  UseFormReturn,
} from 'react-hook-form';
import Select from 'react-select';
import CreatableSelect from 'react-select/creatable';
import Labels from '../Label';

export interface ListType {
  value: string | number;
  label: string;
  id?: string | number;
  disabled?: boolean;
  isInactive?: boolean;
  isInActiveDisabled?: boolean;
  icon?: React.FC<{ iconColor?: string }>;
}

export interface SelectDropdownTyps<T extends FieldValues> {
  name: Path<T>;
  value?: string;
  label?: string;
  isClearable?: boolean;
  optionsList: ListType[];
  form: UseFormReturn<T>;
  defaultValue?: T[Path<T>];
  validation?: RegisterOptions<T, Path<T>>;
  enableSearch?: boolean;
  className?: string;
  optionClassName?: string;
  placeholder?: string;
  searchPlaceholder?: string;
  selectedValue?: string;
  hasSelectAll?: boolean;
  isLoading?: boolean;
  disabled?: boolean;
  disableSearch?: boolean;
  emptyOptionMsg?: string;
  isMulti?: boolean;
  isSearchable?: boolean;
  labelClassName?: string;
  menuPlacement?: 'top' | 'bottom' | 'auto';
  parentClassName?: string;
  onSelectChange?: (selected: string) => void;
  onChange?: (selected: string) => void;
  handleOnChange?: (selected: string | any[] | null) => void;
  dropdownHeight?: string;
  keepSelectedInList?: boolean;
  closeOnSelect?: boolean;
  textWrap?: string;
  maxMenuHeight?: number;
  menuPosition?: 'fixed' | 'absolute';
  returnOptionAsObject?: boolean;
  allowCustomEntry?: boolean;
  numericOnly?: boolean;
  formatCreateLabel?: (value: string) => void;
}

const SelectWidget = <T extends FieldValues>({
  name,
  label,
  optionsList,
  form,
  defaultValue,
  validation,
  enableSearch,
  placeholder,
  disabled,
  isMulti,
  isSearchable,
  className,
  isClearable = true,
  menuPlacement = 'auto',
  parentClassName,
  onSelectChange,
  isLoading,
  labelClassName,
  maxMenuHeight,
  menuPosition = 'fixed',
  returnOptionAsObject,
  allowCustomEntry,
  numericOnly,
  formatCreateLabel,
}: SelectDropdownTyps<T>) => {
  // State to handle screen resizing and tablet detection
  const [isTablet, setIsTablet] = useState(window?.innerWidth >= 768);

  // Check if the field has any validation errors
  const hasError = !!get(form.formState.errors, name);
  const errorMessage = get(form.formState.errors, name)?.message?.toString();

  // Set up validation options, including the required field message if needed
  const isRequired = validation?.required ? 'Required' : undefined;
  const requiredMessage =
    validation && typeof validation.required === 'string'
      ? validation.required
      : isRequired;

  const validationOptions = validation
    ? {
        ...validation,
        required: requiredMessage,
      }
    : {};

  // Effect hook to track screen resizing
  useEffect(() => {
    const handleResize = () => {
      setIsTablet(window?.innerWidth >= 768);
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const onBlurWorkaround = (event: React.FocusEvent<HTMLInputElement>) => {
    const element = event.relatedTarget;
    if (
      element &&
      (element.tagName === 'A' ||
        element.tagName === 'BUTTON' ||
        element.tagName === 'INPUT')
    ) {
      (element as HTMLElement).focus();
    }
  };

  // Custom styles for the select input and options
  const customStyles = {
    control: (base: any, state: any) => ({
      ...base,
      color: '#1E1E1E',
      height: '40px',
      backgroundColor: disabled ? '#F5F5F5' : 'white',
      border:
        errorMessage && state.isFocused
          ? '1px solid #491d96' // Purple border when focused with error
          : errorMessage
            ? '1px solid red' // Red border if there's an error
            : '',
      borderWidth: errorMessage ? '1px' : '1px',
      ...(isTablet && { overflow: 'hidden', whiteSpace: 'nowrap' }), // For tablet screen sizes
      '&:hover': {
        border:
          errorMessage && state.isFocused
            ? '1px solid #491d96' // Purple border on hover with error
            : errorMessage
              ? '1px solid red' // Red border on hover if error
              : '',
      },
    }),
    option: (styles: any, { isSelected }: any) => ({
      ...styles,
      minHeight: '30px',
      background: isSelected ? '#F3F3F3' : undefined,
      color: isSelected ? '#491d96' : 'black',
      ':hover': {
        backgroundColor: '#F1F1F1',
        color: '#491d96',
      },
    }),
  };

  // Choose Select type based on custom entry permission
  const SelectComponent = allowCustomEntry ? CreatableSelect : Select;

  return (
    <Controller
      name={name}
      control={form.control}
      defaultValue={defaultValue}
      rules={validationOptions}
      render={({ field: { onChange, value } }) => (
        <div className={cn('w-full flex flex-col gap-2', parentClassName)}>
          {/* Render label if provided */}
          {label && (
            <Labels
              htmlFor={name}
              label={label}
              validation={validation}
              className={labelClassName}
            />
          )}
          {/* Render the select  */}
          <SelectComponent
            isLoading={isLoading}
            value={
              isMulti || returnOptionAsObject
                ? value
                : optionsList?.filter((option) => option?.value === value)
            }
            onBlur={onBlurWorkaround}
            onChange={(newValue: any) => {
              // Handle change for multi or single select
              onChange(
                isMulti || returnOptionAsObject
                  ? newValue
                  : newValue?.value || ''
              );
              onSelectChange?.(
                isMulti || returnOptionAsObject ? newValue : newValue?.value
              ); // Call onSelectChange if provided
            }}
            onInputChange={(inputValue, { action }) => {
              if (action === 'input-change') {
                if (numericOnly) {
                  return inputValue.replace(/[^0-9]/g, ''); // Only numbers
                }
                return inputValue; // Allow all characters
              }
              return inputValue;
            }}
            options={optionsList}
            placeholder={
              <div className="text-[#b3b3b3] truncate">{placeholder}</div>
            } // Custom placeholder
            isMulti={isMulti}
            defaultValue={defaultValue}
            isSearchable={enableSearch || isSearchable}
            isDisabled={disabled}
            isClearable={!disabled && isClearable}
            formatCreateLabel={(inputValue) =>
              `Add "${formatCreateLabel ? formatCreateLabel(inputValue) : inputValue}"`
            }
            components={{
              IndicatorSeparator: () => null,
              // Option: CustomOption,
            }} // Custom dropdown option rendering
            maxMenuHeight={maxMenuHeight}
            isOptionDisabled={(option) => option.isInActiveDisabled}
            // menuPortalTarget={
            //   typeof window !== 'undefined' ? document?.body : undefined
            // }
            styles={{
              menuPortal: (base) => ({
                ...base,
                zIndex: 99999,
                pointerEvents: 'all',
              }),
              ...customStyles,
            }}
            className={cn('h-10 sm:text-base md:text-sm', className)}
            menuPlacement={menuPlacement}
            menuPosition={menuPosition}
            theme={(theme) => ({
              ...theme,
              colors: {
                ...theme.colors,
                primary25: '#F3F3F3', // Hover color for options
                primary: '#491d96', // Primary color for select
              },
            })}
          />
          {/* Show error message if there are validation errors */}
          {hasError && (
            <p className={cn('text-sm font-normal text-danger')}>
              {errorMessage}
            </p>
          )}
        </div>
      )}
    />
  );
};

export default SelectWidget;
