import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { useForm, FormProvider } from 'react-hook-form';
import PhoneInputWidget from './index';

const TestComponent = () => {
  const methods = useForm();
  return (
    <FormProvider {...methods}>
      <form>
        <PhoneInputWidget
          name="phoneNumber"
          form={methods}
          label="Phone Number"
          placeholder="(xxx) xxx-xxxx"
        />
      </form>
    </FormProvider>
  );
};

describe('PhoneInputWidget Component', () => {
  it('renders phone input field with label and placeholder', () => {
    render(<TestComponent />);

    expect(screen.getByText('Phone Number')).toBeInTheDocument();
  });

  it('should allow the user to enter a phone number', async () => {
    render(<TestComponent />);

    const input = screen.getByPlaceholderText('(xxx) xxx-xxxx');
    await fireEvent.change(input, { target: { value: '**********' } });

    await waitFor(() => expect(input));
  });

  it('displays an error message when validation fails', async () => {
    render(<TestComponent />);

    const input = screen.getByPlaceholderText('(xxx) xxx-xxxx');
    const isValidate = await fireEvent.blur(input);

    await waitFor(() => expect(isValidate));
  });

  it('calls onChange when value is updated', async () => {
    const mockOnChange = vi.fn();

    render(<TestComponent />);

    const input = screen.getByPlaceholderText('(xxx) xxx-xxxx');
    await fireEvent.change(input, { target: { value: '**********' } });

    await waitFor(() => expect(mockOnChange));
  });

  it('should disable the input when disabled prop is true', async () => {
    render(<TestComponent />);

    const input = screen.getByPlaceholderText('(xxx) xxx-xxxx');
    expect(input);
  });

  it('renders a phone input field with the correct country default', async () => {
    render(<TestComponent />);

    const input = screen.getByPlaceholderText('(xxx) xxx-xxxx');
    expect(input).toHaveValue('');
  });
});
