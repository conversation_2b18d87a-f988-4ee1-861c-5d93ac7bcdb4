// import {
//   FAX_VALIDATION_RULE,
//   PHONE_VALIDATION_RULE,
// } from '@/constants/validation-constants';
// import { cn } from '@/lib/utils';
// import get from 'lodash/get';
// import { useState } from 'react';
// import {
//   Controller,
//   FieldValues,
//   Path,
//   RegisterOptions,
//   UseFormReturn,
// } from 'react-hook-form';
// import { PhoneInput } from 'react-international-phone';
// import 'react-international-phone/style.css';
// import Labels from '../Label';

// // Define the types for the PhoneInputWidget props
// interface PhoneInputWidgetProps<T extends FieldValues> {
//   name: Path<T>;
//   label?: string;
//   form: UseFormReturn<T>;
//   placeholder?: string;
//   className?: string;
//   inputClassName?: string;
//   labelClassName?: string;
//   onChange?: (value: string) => void;
//   onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
//   disabled?: boolean; // Whether the input is disabled
//   readOnly?: boolean; // Whether the input is read-only
//   error?: any; // Error object to display error message
//   helperText?: string; // Helper text to be displayed below input
//   helperTextClassName?: string; // Custom class for helper text
//   errorMessageClassName?: string; // Custom class for error message
//   requiredClassName?: string; // Custom class for required field
//   defaultCountry?: string; // Default country code for the phone number input
//   disablePrefix?: boolean; // Disable the prefix (dial code)
//   hideDropdown?: boolean; // Whether to hide the country dropdown
//   validation?: RegisterOptions<T, Path<T>>; // Validation rules for React Hook Form
//   isFax?: boolean;
//   isValidation?: boolean;
//   errorDisplayMode?: boolean;
//   autoComplete?: string;
// }

// const PhoneInputWidget = <T extends FieldValues>({
//   name,
//   label,
//   form,
//   placeholder = '(xxx) xxx-xxxx',
//   className,
//   inputClassName,
//   onChange,
//   disabled,
//   defaultCountry = 'us',
//   validation,
//   disablePrefix = true,
//   hideDropdown,
//   isFax,
//   labelClassName,
//   isValidation = true,
//   errorDisplayMode,
//   autoComplete = 'tel',
// }: PhoneInputWidgetProps<T>) => {
//   // State to handle focus state of the input field
//   const [isFocused, setIsFocused] = useState(false);

//   // Determine if the field is required based on validation rules
//   const isRequired = validation?.required ? 'Required' : undefined;
//   const requiredMessage =
//     validation && typeof validation.required === 'string'
//       ? validation.required
//       : isRequired;

//   // Prepare the validation options
//   const validationOptions = validation
//     ? { ...validation, required: requiredMessage }
//     : {};

//   // Handle focus and blur to manage the border color
//   const handleFocus = () => setIsFocused(true);
//   const handleBlur = () => setIsFocused(false);

//   // Check if there is any error on the form field
//   // const hasError = !!form.formState.errors[name];
//   const hasError = !!get(form.formState.errors, name);
//   const errorMessage = get(form.formState.errors, name)?.message?.toString();

//   // Render the PhoneInput component
//   const renderPhoneInput = (field: any = {}) => (
//     <PhoneInput
//       defaultCountry={defaultCountry}
//       value={field?.value ?? ''}
//       onChange={(phone) => {
//         if (field?.onChange) field?.onChange(phone); // Update react-hook-form state
//         if (onChange) onChange(phone); // Custom onChange handler
//       }}
//       flags={[]}
//       disableCountryGuess={true}
//       forceDialCode={true}
//       onFocus={handleFocus}
//       onBlur={handleBlur}
//       disableDialCodeAndPrefix={disablePrefix}
//       hideDropdown={hideDropdown}
//       inputProps={{ autoComplete: autoComplete }}
//       inputClassName={cn(
//         `flex h-10 w-full rounded-md placeholder:text-[#B3B3B3]
//        focus:border-0 bg-background px-3 py-2 text-sm ring-offset-background
//        focus-visible:ring-2 focus-visible:border-0 focus-visible:ring-ring
//       disabled:cursor-not-allowed disabled:bg-[#F5F5F5] focus:border-0`,
//         errorDisplayMode && hasError
//           ? 'focus-visible:ring-offset-[0.5px] focus-visible:ring-[0.5px] border-danger border-[1px]'
//           : '',
//         inputClassName
//       )}
//       inputStyle={{
//         height: '40px',
//         border: hasError && !isFocused ? '1px solid red' : '', // Apply red border if error exists
//         borderRadius: '6px',
//       }}
//       disabled={disabled}
//       placeholder={placeholder}
//     />
//   );

//   return (
//     <div className={cn('flex flex-col gap-2 relative', className)}>
//       {/* Render label if available */}
//       {label && (
//         <Labels
//           htmlFor={name}
//           label={label}
//           validation={validation}
//           className={labelClassName}
//         />
//       )}
//       <div
//         className={
//           hasError && errorDisplayMode
//             ? 'bg-[#C00F0C] rounded-md'
//             : 'flex flex-col gap-2 relative'
//         }
//       >
//         {/* Render the Controller from react-hook-form */}
//         {form ? (
//           <Controller
//             name={name}
//             control={form.control}
//             render={({ field }) => renderPhoneInput(field)} // Pass field data to renderPhoneInput
//             rules={{
//               ...validationOptions, // External validation rules
//               ...(isValidation
//                 ? isFax
//                   ? FAX_VALIDATION_RULE // Use PHONE_VALIDATION_RULE if isFax is true
//                   : PHONE_VALIDATION_RULE
//                 : ''), // Use FAX_VALIDATION_RULE if isFax is false
//             }}
//           />
//         ) : (
//           renderPhoneInput() // If no form provided, render phone input directly
//         )}
//         {/* Display error message if any */}

//         {hasError && errorDisplayMode ? (
//           <p className="text-sm font-normal text-danger bg-[#C00F0C] text-white pt-1 pb-1 pl-3 pr-3 rounded-md">
//             {errorMessage}
//           </p>
//         ) : errorMessage ? (
//           <p className="text-sm font-normal text-danger">{errorMessage}</p>
//         ) : (
//           hasError && (
//             <p className="text-sm font-normal text-danger">{errorMessage}</p>
//           )
//         )}
//       </div>
//     </div>
//   );
// };

// export default PhoneInputWidget;

import {
  FAX_VALIDATION_RULE,
  PHONE_VALIDATION_RULE,
} from '@/constants/validation-constants';
import { cn } from '@/lib/utils';
import get from 'lodash/get';
import React, { HTMLInputAutoCompleteAttribute } from 'react';
import {
  Controller,
  FieldValues,
  Path,
  RegisterOptions,
  UseFormReturn,
} from 'react-hook-form';
import { PatternFormat } from 'react-number-format';
import Labels from '../Label';

// Define the types for the PhoneInputWidget props
interface PhoneInputWidgetProps<T extends FieldValues> {
  name: Path<T>;
  label?: string;
  form: UseFormReturn<T>;
  placeholder?: string;
  className?: string;
  inputClassName?: string;
  labelClassName?: string;
  onChange?: (value: string) => void;
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  disabled?: boolean; // Whether the input is disabled
  readOnly?: boolean; // Whether the input is read-only
  error?: any; // Error object to display error message
  helperText?: string; // Helper text to be displayed below input
  helperTextClassName?: string; // Custom class for helper text
  errorMessageClassName?: string; // Custom class for error message
  requiredClassName?: string; // Custom class for required field
  validation?: RegisterOptions<T, Path<T>>; // Validation rules for React Hook Form
  isFax?: boolean;
  isValidation?: boolean;
  errorDisplayMode?: boolean;
  countryCode?: string;
  hasExt?: boolean; // enabled phone extension
  autoComplete?: HTMLInputAutoCompleteAttribute;
}

export const normalizePhoneValue = (
  value: string = '',
  countryCode: string = '+1'
): string => {
  const [numberPart, extPart] = String(value || '')?.split('x'); // Safe now because value defaults to ''

  const trimmedNumber = numberPart?.replace(/[^0-9+]/g, '').slice(0, 10);
  const trimmedExt = extPart?.replace(/[^0-9]/g, '');
  const number = trimmedExt ? `${trimmedNumber} x${trimmedExt}` : trimmedNumber;

  return number ? `${countryCode}${number}` : '';
};

const removeCountryCodePrefix = (value: string = '', countryCode: string) => {
  return value?.startsWith(countryCode)
    ? value?.slice(countryCode?.length)
    : value;
};

const PhoneInputWidget = <T extends FieldValues>({
  name,
  label,
  form,
  hasExt = true,
  placeholder = hasExt ? '(____) ___-___ x_____' : '(____) ___-___',
  className,
  inputClassName,
  onChange,
  disabled,
  validation,
  isFax,
  labelClassName,
  isValidation = true,
  errorDisplayMode,
  countryCode = '+1',
  autoComplete = 'tel',
}: PhoneInputWidgetProps<T>) => {
  const isRequired = validation?.required ? 'Required' : undefined;
  const requiredMessage =
    validation && typeof validation.required === 'string'
      ? validation.required
      : isRequired;

  // Prepare the validation options
  const validationOptions = validation
    ? { ...validation, required: requiredMessage }
    : {};

  // Error management
  // Check if there is any error on the form field
  // const hasError = !!form.formState.errors[name];
  const hasError = !!get(form.formState.errors, name);
  const errorMessage = get(form.formState.errors, name)?.message?.toString();

  const renderPhoneInput = (field: any = {}) => (
    <PatternFormat
      type="tel"
      format={hasExt ? '(###) ###-#### x#####' : '(###) ###-####'}
      mask="_"
      value={removeCountryCodePrefix(field?.value, countryCode) ?? ''}
      onValueChange={({ formattedValue }) => {
        const value = normalizePhoneValue(formattedValue, countryCode);
        field?.onChange?.(value); // Update react-hook-form state
        onChange?.(value); // Custom onChange handler
      }}
      disabled={disabled}
      placeholder={placeholder}
      autoComplete={autoComplete}
      className={cn(
        'flex h-10 w-full rounded-md border bg-background px-3 py-2 text-sm placeholder:text-[#B3B3B3] ring-offset-background',
        'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary',
        ((errorDisplayMode && hasError) || errorMessage) && !disabled
          ? 'border-danger focus-visible:border-none'
          : 'border-input',
        disabled && 'cursor-not-allowed bg-[#F5F5F5]',
        inputClassName
      )}
    />
  );

  return (
    <div className={cn('flex flex-col gap-2 relative', className)}>
      {/* Render label if available */}
      {label && (
        <Labels
          htmlFor={name}
          label={label}
          validation={validation}
          className={labelClassName}
        />
      )}
      <div
        className={
          hasError && errorDisplayMode
            ? 'bg-[#C00F0C] rounded-md'
            : 'flex flex-col gap-2 relative'
        }
      >
        {/* Render Controller if form is provided */}
        {form ? (
          <Controller
            name={name}
            control={form.control}
            render={({ field }) => renderPhoneInput(field)} // Pass field data to renderPhoneInput
            rules={{
              ...validationOptions, // External validation rules
              ...(isValidation
                ? isFax
                  ? FAX_VALIDATION_RULE // Use FAX_VALIDATION_RULE if isFax is true
                  : PHONE_VALIDATION_RULE
                : ''), // Use PHONE_VALIDATION_RULE if isFax is false
            }}
          />
        ) : (
          renderPhoneInput() // If no form provided, render phone input directly
        )}

        {/* Display error message if any */}
        {hasError && errorDisplayMode ? (
          <p className="text-sm font-normal text-danger bg-[#C00F0C] text-white pt-1 pb-1 pl-3 pr-3 rounded-md">
            {errorMessage}
          </p>
        ) : errorMessage ? (
          <p className="text-sm font-normal text-danger">{errorMessage}</p>
        ) : (
          hasError && (
            <p className="text-sm font-normal text-danger">{errorMessage}</p>
          )
        )}
      </div>
    </div>
  );
};

export default PhoneInputWidget;
