import TooltipWidget from '@/components/common/tooltip-widget';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Controller } from 'react-hook-form';
import Select, {
  CSSObjectWithLabel,
  OptionProps,
  ValueContainerProps,
  components,
} from 'react-select';
import Labels from '../Label';

export interface SelectDropdownTyps {
  name: string;
  value?: string | string[];
  label?: string;
  extraLabel?: React.ReactNode;
  labelClassName?: string;
  optionsList: {
    label: string;
    value: string;
    isInactive?: boolean;
    isInActiveDisabled?: boolean;
  }[];
  control: any; // Adjust this type based on your form control
  defaultValue?: any;
  required?: string;
  selectedValue?: { label: string; value: string }[];
  pattern?: RegExp;
  patternMessage?: string;
  helperText?: any;
  enableSearch?: boolean;
  placeholder?: string;
  disabled?: boolean;
  isMulti?: boolean;
  isSearchable?: boolean;
  className?: string;
  isClearable?: boolean;
  menuPlacement?: 'auto' | 'top' | 'bottom';
  hideRequiredIcon?: boolean;
  parentClassName?: string;
  handleOnChange?: (value: { label: string; value: string }[] | any[]) => void;
  isLoading?: boolean;
  hasSelectAll?: boolean;
  menuPosition?: 'absolute' | 'fixed';
}

const MultiSelectCheckboxDropdown = ({
  name,
  label,
  optionsList,
  control,
  required,
  pattern,
  patternMessage,
  helperText,
  enableSearch,
  placeholder,
  disabled,
  isSearchable,
  className,
  isClearable = true,
  menuPlacement,
  parentClassName,
  handleOnChange,
  isLoading,
  extraLabel,
  labelClassName,
  menuPosition = 'fixed',
}: SelectDropdownTyps) => {
  const [isOpen, setOpen] = useState(false);
  const selectRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement | null>(null);

  const allOptionsList = optionsList?.length
    ? [{ label: 'Select All', value: 'selectAll' }, ...optionsList]
    : optionsList;

  const customStyles = {
    control: (base: CSSObjectWithLabel): CSSObjectWithLabel => ({
      ...base,
      borderRadius: '6px',
      fontSize: '14px',
    }),
    option: (
      styles: CSSObjectWithLabel,
      { isSelected }: any
    ): CSSObjectWithLabel => ({
      ...styles,
      background: isSelected ? '#F3F3F3' : undefined,
      color: isSelected ? '#02245F' : 'black',
      ':hover': {
        backgroundColor: '#F1F1F1',
        color: '#02245F',
      },
    }),
  };

  const CustomOption = (optionProps: OptionProps<any>) => {
    const { data } = optionProps;
    const labelColor =
      data.isInactive !== undefined ? !data?.isInactive && 'text-red-500' : '';
    return (
      <components.Option {...optionProps} className="flex items-center">
        {optionProps.data.value !== 'selectAll' && (
          <input
            type="checkbox"
            checked={optionProps.isSelected}
            readOnly
            className="accent-primary"
          />
        )}
        <Label className={cn(labelColor, 'px-1')}>{optionProps.label}</Label>
      </components.Option>
    );
  };

  const ValueContainer = ({
    children,
    placeholder,
    ...props
  }: ValueContainerProps<any> & { placeholder?: string }) => {
    const [values, input] = children as any[];
    let displayValues = values;
    let toolTips = [];
    if (Array.isArray(values)) {
      displayValues = values.length > 2 ? `${values.length} selected` : values;

      if (values.length > 0) {
        toolTips = values.map((tooltip) => tooltip.key);
      }
    }

    // const title = displayValues ? displayValues : '';
    const title =
      Array.isArray(values) && values.length === 0
        ? placeholder
        : displayValues;

    return (
      <components.ValueContainer {...props}>
        <>
          {toolTips?.length ? (
            <TooltipWidget tooltip={toolTips.join(' , ')}>
              <div className="flex">{title}</div>
            </TooltipWidget>
          ) : (
            <div className="flex">{title}</div>
          )}
          {isOpen && (
            <div>
              <div className="text-[12px] truncate w-24 ml-[3px]">
                {React.cloneElement(input, {
                  placeholder: placeholder,
                  className: 'multiSelect-drp-search',
                  onKeyDown: (e: React.KeyboardEvent<HTMLInputElement>) => {
                    if (e.key === 'Backspace' && !input.props.value) {
                      e.stopPropagation();
                    }
                  },
                })}
              </div>
            </div>
          )}
        </>
      </components.ValueContainer>
    );
  };

  const handleClickOutside = (event: any) => {
    if (selectRef.current && !selectRef.current.contains(event.target)) {
      if (inputRef.current && inputRef.current.value) {
        setOpen(true);
      } else {
        setOpen(false);
      }
    }
  };

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleOnClickSelect = useCallback(() => {
    if (!disabled) {
      setOpen(true);
    }
  }, [disabled]);

  return (
    <Controller
      name={name}
      control={control}
      rules={{
        required: required ? 'Required' : '',
        pattern: pattern
          ? { value: pattern, message: patternMessage || '' }
          : undefined,
      }}
      render={({ field: { onChange, value } }) => {
        const handleMultiSelectChange = (newValue: any) => {
          if (Array.isArray(newValue)) {
            if (newValue.some((option) => option.value === 'selectAll')) {
              let allSelected;
              let newOptionList;
              if (optionsList.some((option) => option.isInActiveDisabled)) {
                newOptionList = optionsList.filter(
                  (option) => !option.isInActiveDisabled
                );
                allSelected = value?.length === newOptionList.length;
              } else {
                allSelected = value?.length === optionsList.length;
                newOptionList = optionsList;
              }

              const allOptions = allSelected ? [] : newOptionList;
              onChange(allOptions);
              handleOnChange?.(allOptions);
            } else {
              const selectedValues = newValue.filter(
                (option: any) => option.value !== 'selectAll'
              );
              onChange(selectedValues);
              handleOnChange?.(selectedValues);
            }
          }
        };

        const selectedValues = value
          ? value.map((v: any) =>
              optionsList?.find((option) => option.value === v.value)
            )
          : [];

        return (
          <div className={parentClassName || 'w-full mb-4'} ref={selectRef}>
            <div className={labelClassName}>
              {label && <Labels htmlFor={name} label={label} />}
              {extraLabel && extraLabel}
            </div>

            <div
              onClick={handleOnClickSelect}
              onTouchStart={handleOnClickSelect}
            >
              <Select
                menuIsOpen={isOpen}
                isLoading={isLoading}
                value={selectedValues}
                onChange={handleMultiSelectChange}
                options={allOptionsList}
                placeholder={placeholder}
                hideSelectedOptions={false}
                isMulti={true}
                closeMenuOnSelect={false}
                isSearchable={enableSearch || isSearchable}
                isDisabled={disabled}
                isClearable={!disabled && isClearable}
                isOptionDisabled={(option) => option.isInActiveDisabled}
                components={{
                  IndicatorSeparator: () => null,
                  Option: CustomOption,
                  Input: (props) => (
                    <input
                      {...props}
                      ref={(input: HTMLInputElement | null) => {
                        if (input) {
                          inputRef.current = input;
                        }
                      }}
                    />
                  ),
                  ValueContainer: (props) => (
                    <ValueContainer placeholder={'Search...'} {...props} />
                  ),
                }}
                styles={customStyles}
                className={cn(
                  'mt-1 h-10',
                  className,
                  disabled ? 'cursor-not-allowed' : ''
                )}
                menuPlacement={menuPlacement || 'auto'}
                menuPosition={menuPosition}
                theme={(theme) => ({
                  ...theme,
                  borderRadius: 0,
                  colors: {
                    ...theme.colors,
                    primary25: '#F3F3F3',
                    primary: '#02245F',
                  },
                })}
              />
            </div>
            {helperText && (
              <Label className="text-xs block text-red-600 mt-1">
                {helperText}
              </Label>
            )}
          </div>
        );
      }}
    />
  );
};

export default MultiSelectCheckboxDropdown;
