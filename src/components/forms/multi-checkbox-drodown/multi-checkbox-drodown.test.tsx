import { render, fireEvent, screen } from '@testing-library/react';
import { describe, vi, it, expect } from 'vitest';
import { useForm } from 'react-hook-form';
import MultiSelectCheckboxDropdown from './index';

const TestComponent = () => {
  const { control, handleSubmit } = useForm();
  const onSubmit = vi.fn();

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <MultiSelectCheckboxDropdown
        name="testSelect"
        label="Test Label"
        optionsList={[
          { label: 'Option 1', value: '1' },
          { label: 'Option 2', value: '2' },
        ]}
        control={control}
        required="This field is required"
        isMulti={true}
        isSearchable={true}
        placeholder="Select options"
        handleOnChange={onSubmit}
      />
      <button type="submit">Submit</button>
    </form>
  );
};

describe('MultiSelectCheckboxDropdown Component', () => {
  it('should render label and options correctly', () => {
    render(<TestComponent />);

    expect(screen.getByText('Test Label')).toBeInTheDocument();
    fireEvent.click(screen.getByText('Select options'));
    expect(screen.getByText('Option 1')).toBeInTheDocument();
    expect(screen.getByText('Option 2')).toBeInTheDocument();
  });

  it('should handle multi-select functionality', async () => {
    render(<TestComponent />);

    fireEvent.click(screen.getByText('Select options'));
    const isClicked = fireEvent.click(screen.getByText('Option 1'));
    expect(isClicked).toBe(true);
  });

  it('should call handleOnChange with selected values', async () => {
    const handleOnChange = vi.fn();

    render(<TestComponent />);

    fireEvent.click(screen.getByText('Select options'));
    fireEvent.click(screen.getByText('Option 1'));
    fireEvent.click(screen.getByText('Option 2'));
    fireEvent.click(screen.getByText('Submit'));
    expect(handleOnChange);
  });

  it('should be disabled when disabled prop is true', () => {
    render(<TestComponent />);
    fireEvent.click(screen.getByText('Select options'));
    expect(screen.getByText('Select options'));
  });
});
