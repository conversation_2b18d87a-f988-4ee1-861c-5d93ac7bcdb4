import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import { useForm, FormProvider } from 'react-hook-form';
import NumberInputField from './index';

const mockOnValueChange = vi.fn();

const TestComponent = () => {
  const methods = useForm();
  return (
    <FormProvider {...methods}>
      <form>
        <NumberInputField
          name="amount"
          form={methods}
          label="Amount"
          placeholder="Enter amount"
          onValueChange={mockOnValueChange}
        />
      </form>
    </FormProvider>
  );
};

describe('NumberInputField Component', () => {
  it('renders input with label and placeholder', () => {
    render(<TestComponent />);
    expect(screen.getByText('Amount')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Enter amount')).toBeInTheDocument();
  });

  it('should allow user to enter a value', async () => {
    render(<TestComponent />);
    const input = screen.getByPlaceholderText('Enter amount');
    await fireEvent.change(input, { target: { value: '1000.99' } });
    await waitFor(() => expect(input).toHaveValue('1000.99'));
  });

  it('calls onValueChange when value is updated', async () => {
    render(<TestComponent />);
    const input = screen.getByPlaceholderText('Enter amount');
    await fireEvent.change(input, {
      target: { value: '1000.99' },
    });
    await waitFor(() =>
      expect(mockOnValueChange).toHaveBeenCalledWith('1000.99')
    );
  });

  it('handles maxLength properly', async () => {
    render(<TestComponent />);
    const input = screen.getByPlaceholderText('Enter amount');
    await fireEvent.change(input, { target: { value: '123456' } });
    await waitFor(() =>
      expect(mockOnValueChange).not.toHaveBeenCalledWith('12345')
    );
  });

  it('formats the number input properly', async () => {
    render(<TestComponent />);
    const input = screen.getByPlaceholderText('Enter amount');
    await fireEvent.change(input, { target: { value: '1000.50' } });
    await waitFor(() =>
      expect(mockOnValueChange).not.toHaveBeenCalledWith('1,000.50')
    );
  });
});
