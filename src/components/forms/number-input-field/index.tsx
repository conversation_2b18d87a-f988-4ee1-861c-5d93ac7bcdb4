import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import get from 'lodash/get';
import {
  Controller,
  FieldValues,
  Path,
  RegisterOptions,
  UseFormReturn,
} from 'react-hook-form';
import { NumericFormat } from 'react-number-format';
import Labels from '../Label';
import { ReactNode } from 'react';

interface CurrencyInputFieldProps<T extends FieldValues> {
  name: Path<T>;
  form: UseFormReturn<T | any>;
  label?: string;
  extraLabel?: ReactNode;
  placeholder?: string;
  className?: string;
  pClassName?: string;
  disabled?: boolean;
  validation?: RegisterOptions<T, Path<T>>;
  decimalScale?: number;
  fixedDecimalScale?: boolean;
  allowNegative?: boolean;
  prefix?: string;
  suffix?: string;
  onValueChange?: (value: string) => void;
  labelClassName?: string;
  maxLength?: number;
  maxValue?: number;
  decimalSeparator?: string;
  thousandSeparator?: string;
  onBlur?: (event: any) => void;
  defaultValue?: number | string;
}

const NumberInputField = <T extends FieldValues>({
  name,
  label,
  extraLabel,
  placeholder,
  form,
  disabled,
  className,
  pClassName,
  validation,
  decimalScale = 2,
  fixedDecimalScale = false,
  allowNegative = false,
  prefix,
  suffix,
  onValueChange,
  labelClassName,
  maxLength,
  maxValue,
  decimalSeparator,
  thousandSeparator,
  onBlur,
  defaultValue,
}: CurrencyInputFieldProps<T>) => {
  // Generate required validation message
  // Check if the field has any validation errors
  const hasError = !!get(form.formState.errors, name);
  const errorMessage = get(form.formState.errors, name)?.message?.toString();

  // Set up validation options, including the required field message if needed
  const isRequired = validation?.required ? 'Required' : undefined;
  const requiredMessage =
    validation && typeof validation.required === 'string'
      ? validation.required
      : isRequired;

  const validationOptions = validation
    ? {
        ...validation,
        required: requiredMessage,
      }
    : {};

  return (
    <>
      <Controller
        name={name}
        control={form.control}
        rules={validationOptions}
        render={({ field: { onChange, value } }) => (
          <div className={cn('w-full flex flex-col gap-2', pClassName)}>
            {/* Render label if provided */}
            {(label || extraLabel) && (
              <div className="flex flex-row gap-x-2">
                {label && (
                  <Labels
                    htmlFor={name}
                    label={label}
                    validation={validation}
                    className={labelClassName}
                  />
                )}
                {extraLabel && extraLabel}
              </div>
            )}

            <NumericFormat
              value={value} // Controlled value
              defaultValue={defaultValue}
              disabled={disabled}
              placeholder={placeholder}
              decimalScale={decimalScale}
              fixedDecimalScale={fixedDecimalScale}
              allowNegative={allowNegative}
              // isAllowed={
              //   maxLength
              //     ? ({ formattedValue }) => formattedValue.length <= maxLength
              //     : undefined
              // }
              isAllowed={({ formattedValue, floatValue }) => {
                const lengthCheck = maxLength
                  ? formattedValue.length <= maxLength
                  : true;
                const valueCheck =
                  maxValue !== undefined ? (floatValue ?? 0) <= maxValue : true;
                return lengthCheck && valueCheck;
              }}
              // maxLength={maxLength}
              prefix={prefix}
              suffix={suffix}
              decimalSeparator={decimalSeparator}
              thousandSeparator={thousandSeparator}
              onValueChange={({ value }) => {
                onChange(value);
                onValueChange?.(value);
              }}
              onBlur={onBlur}
              customInput={Input} // Use the custom Input component
              className={cn(
                'border-border-Default placeholder:text-[#B3B3B3] focus-visible:border-0',
                hasError || errorMessage ? 'border-danger border' : '',
                disabled ? 'bg-[#F5F5F5]' : '',
                className
              )}
            />

            {/* Show error message if there are validation errors */}
            {hasError && (
              <p className={cn('text-sm font-normal text-danger')}>
                {errorMessage}
              </p>
            )}
          </div>
        )}
      />
    </>
  );
};

export default NumberInputField;
