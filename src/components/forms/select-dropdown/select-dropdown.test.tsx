import React from 'react';
import { describe, it, expect, vi, beforeAll } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import {
  useForm,
  FormProvider,
  UseFormReturn,
  FieldValues,
  UseFormProps,
} from 'react-hook-form';
import SelectDropDown from '.';

// Mock the cn function
vi.mock('@/lib/utils', () => ({
  cn: (...args: any[]) => args.filter(Boolean).join(' '),
}));

interface TestWrapperProps<TFieldValues extends FieldValues = FieldValues> {
  children: (form: UseFormReturn<TFieldValues>) => React.ReactElement;
  formProps?: UseFormProps<TFieldValues>;
}

function TestWrapper<TFieldValues extends FieldValues = FieldValues>({
  children,
  formProps,
}: TestWrapperProps<TFieldValues>) {
  const methods = useForm<TFieldValues>(formProps);
  return <FormProvider {...methods}>{children(methods)}</FormProvider>;
}

const mockOptions = [
  { label: 'Option 1', value: 'option1' },
  { label: 'Option 2', value: 'option2' },
  { label: 'Option 3', value: 'option3' },
];

describe('SelectDropDown', () => {
  beforeAll(() => {
    window.HTMLElement.prototype.scrollIntoView = function () {};

    global.ResizeObserver = class ResizeObserver {
      observe() {
        // do nothing
      }
      unobserve() {
        // do nothing
      }
      disconnect() {
        // do nothing
      }
    };
  });
  it('renders with label and placeholder', () => {
    render(
      <TestWrapper<{ testSelect: string }>>
        {(form) => (
          <SelectDropDown
            name="testSelect"
            form={form}
            label="Test Label"
            placeholder="Select an option"
            optionsList={mockOptions}
          />
        )}
      </TestWrapper>
    );

    expect(screen.getByText('Test Label')).toBeInTheDocument();
    expect(screen.getByText('Select an option')).toBeInTheDocument();
  });

  it('renders options list correctly', async () => {
    render(
      <TestWrapper<{ testSelect: string }>>
        {(form) => (
          <SelectDropDown
            name="testSelect"
            form={form}
            optionsList={mockOptions}
          />
        )}
      </TestWrapper>
    );

    // Open the select dropdown
    const trigger = screen.getByRole('combobox');
    fireEvent.click(trigger);

    // Check if all options are rendered
    await waitFor(() => {
      mockOptions.forEach((option) => {
        expect(screen.getByText(option.label)).toBeInTheDocument();
      });
    });
  });

  it('displays loading state correctly', async () => {
    render(
      <TestWrapper<{ testSelect: string }>>
        {(form) => (
          <SelectDropDown
            name="testSelect"
            form={form}
            optionsList={[]}
            isLoading={true}
          />
        )}
      </TestWrapper>
    );

    // Open the select dropdown
    const trigger = screen.getByRole('combobox');
    fireEvent.click(trigger);

    await waitFor(() => {
      expect(screen.getByText('Loading...')).toBeInTheDocument();
    });
  });

  it('shows empty message when no options are available', async () => {
    render(
      <TestWrapper<{ testSelect: string }>>
        {(form) => (
          <SelectDropDown
            name="testSelect"
            form={form}
            optionsList={[]}
            emptyOptionMsg="No options available"
          />
        )}
      </TestWrapper>
    );

    // Open the select dropdown
    const trigger = screen.getByRole('combobox');
    fireEvent.click(trigger);

    await waitFor(() => {
      expect(screen.getByText('No options available')).toBeInTheDocument();
    });
  });

  it('handles value change correctly', async () => {
    const onChangeMock = vi.fn();

    render(
      <TestWrapper<{ testSelect: string }>>
        {(form) => (
          <SelectDropDown
            name="testSelect"
            form={form}
            optionsList={mockOptions}
            onChange={onChangeMock}
          />
        )}
      </TestWrapper>
    );

    // Open the select dropdown
    const trigger = screen.getByRole('combobox');
    fireEvent.click(trigger);

    // Select an option
    await waitFor(() => {
      const option = screen.getByText('Option 1');
      fireEvent.click(option);
    });

    expect(onChangeMock).toHaveBeenCalledWith('option1');
  });

  it('displays error message when validation fails', async () => {
    const submitHandler = vi.fn();

    render(
      <TestWrapper<{ testSelect: string }>
        formProps={{
          mode: 'onSubmit',
          defaultValues: { testSelect: '' },
        }}
      >
        {(form) => (
          <form onSubmit={form.handleSubmit(submitHandler)}>
            <SelectDropDown
              name="testSelect"
              form={form}
              optionsList={mockOptions}
              validation={{ required: 'Required' }}
            />
            <button type="submit">Submit</button>
          </form>
        )}
      </TestWrapper>
    );

    // Submit the form without selecting any option
    const submitButton = screen.getByText('Submit');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Required')).toBeInTheDocument();
    });
    expect(submitHandler).not.toHaveBeenCalled();
  });

  it('applies custom className to select trigger', () => {
    const customClass = 'custom-select-class';

    render(
      <TestWrapper<{ testSelect: string }>>
        {(form) => (
          <SelectDropDown
            name="testSelect"
            form={form}
            optionsList={mockOptions}
            className={customClass}
          />
        )}
      </TestWrapper>
    );

    const trigger = screen.getByRole('combobox');
    expect(trigger.classList.contains(customClass)).toBe(true);
  });

  it('renders with description', () => {
    render(
      <TestWrapper<{ testSelect: string }>>
        {(form) => (
          <SelectDropDown
            name="testSelect"
            form={form}
            optionsList={mockOptions}
            description="This is a description"
          />
        )}
      </TestWrapper>
    );

    expect(screen.getByText('This is a description')).toBeInTheDocument();
  });

  it('disables select when disabled prop is true', () => {
    render(
      <TestWrapper<{ testSelect: string }>>
        {(form) => (
          <SelectDropDown
            name="testSelect"
            form={form}
            optionsList={mockOptions}
            disabled={true}
          />
        )}
      </TestWrapper>
    );

    const trigger = screen.getByRole('combobox');
    expect(trigger).toBeDisabled();
  });

  it('sets default value correctly', () => {
    render(
      <TestWrapper<{ testSelect: string }>
        formProps={{
          defaultValues: { testSelect: 'option2' },
        }}
      >
        {(form) => (
          <SelectDropDown
            name="testSelect"
            form={form}
            optionsList={mockOptions}
            defaultValue="option2"
          />
        )}
      </TestWrapper>
    );

    const trigger = screen.getByRole('combobox');
    expect(trigger).toHaveTextContent('Option 2');
  });
});
