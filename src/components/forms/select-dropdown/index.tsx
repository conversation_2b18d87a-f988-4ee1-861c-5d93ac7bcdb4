import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { cn } from '@/lib/utils';
import { OptionsListTypes } from '@/types/common.types';
import { X } from 'lucide-react';
import {
  Controller,
  FieldValues,
  Path,
  RegisterOptions,
} from 'react-hook-form';
import { Label } from '../../ui/label';
import Labels from '../Label';
import get from 'lodash/get';

interface SelectDropDownProps<T extends FieldValues> {
  name: Path<T>;
  form: any;
  label?: string;
  validation?: RegisterOptions<T, Path<T>>;
  placeholder?: string;
  className?: string;
  optionsList: OptionsListTypes[];
  optionClassName?: string;
  emptyOptionMsg?: string;
  isLoading?: boolean;
  disabled?: boolean;
  allowClear?: boolean;
  defaultValue?: string;
  onChange?: (selected: string) => void;
  description?: string;
  labelClassName?: string;
  errorDisplayMode?: boolean;
  itemClassName?: string;
}

const SelectDropDown = <T extends FieldValues>({
  name,
  label,
  validation,
  placeholder,
  form,
  className,
  optionsList,
  optionClassName,
  emptyOptionMsg = 'Nothing to select',
  isLoading = false,
  disabled = false,
  allowClear = true,
  onChange,
  description,
  labelClassName,
  errorDisplayMode,
  itemClassName,
}: SelectDropDownProps<T>) => {
  const validationOptions = validation
    ? {
        ...validation,
        required:
          typeof validation.required === 'string'
            ? validation.required
            : validation.required === true
              ? 'Required'
              : undefined,
      }
    : {};

  const hasError = !!get(form.formState.errors, name);
  const errorMessage = get(form.formState.errors, name)?.message?.toString();

  return (
    <Controller
      name={name}
      control={form.control}
      rules={validationOptions}
      render={({ field: { value, onChange: fieldControllerOnChange } }) => {
        const handleValueChange = (value: string | null) => {
          if (value === '_clear' || value === '' || value === null) {
            fieldControllerOnChange(null); // keep form value as null
            onChange?.(''); // notify parent component
          } else {
            fieldControllerOnChange(value);
            onChange?.(value);
          }
        };
        const handleClear = (
          e: React.MouseEvent<HTMLButtonElement, MouseEvent>
        ) => {
          e.stopPropagation();
          fieldControllerOnChange(null);
          onChange?.('');
        };

        return (
          <div className="flex flex-col gap-2">
            {label && (
              <Labels
                label={label}
                validation={validation}
                htmlFor={name}
                className={labelClassName}
              />
            )}
            {description && (
              <Label className="text-text-tertiary text-base font-normal">
                {description}
              </Label>
            )}

            <div
              className={
                hasError && errorDisplayMode
                  ? 'bg-[#C00F0C] text-white rounded-md'
                  : 'flex flex-col gap-2'
              }
            >
              <Select
                disabled={disabled}
                onValueChange={handleValueChange}
                value={value ?? ''}
              >
                <div className="relative w-full">
                  <SelectTrigger
                    id={name}
                    className={cn(
                      'w-full truncate border-border-Default bg-white',
                      'box-border',
                      'focus-visible:ring-2 focus-visible:ring-offset-0',
                      'focus:outline-none focus:border-0',
                      hasError ? 'border-danger border' : 'border',
                      disabled && 'bg-[#F5F5F5]',
                      errorDisplayMode && hasError
                        ? 'focus:ring-offset-[0.5px] focus:ring-[0.5px]'
                        : '',
                      // errorDisplayMode && hasError
                      //   ? 'rounded-t-md rounded-b-none'
                      //   : 'rounded-md',
                      className
                    )}
                  >
                    <SelectValue
                      placeholder={
                        <span className="text-text-tertiary">
                          {placeholder}
                        </span>
                      }
                    />
                  </SelectTrigger>
                  {allowClear && !disabled && !!value && (
                    <button
                      type="button"
                      className="absolute right-6 lg:right-10 top-1/2 -translate-y-1/2 p-0"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleClear(e);
                      }}
                    >
                      <X className="h-3 w-3 lg:h-4 md:w-4 text-gray-500" />
                    </button>
                  )}

                  <SelectContent
                    className={cn(
                      'max-h-64 overflow-y-auto scroll-smooth',
                      'scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent',
                      'hover:scrollbar-thumb-gray-400',
                      optionClassName
                    )}
                  >
                    <SelectGroup className="max-h-full">
                      {isLoading ? (
                        <SelectItem value="_isloading" disabled>
                          Loading...
                        </SelectItem>
                      ) : optionsList?.length === 0 ? (
                        <SelectItem value="_empty" disabled>
                          {emptyOptionMsg}
                        </SelectItem>
                      ) : (
                        optionsList?.map((item, index: number) => (
                          <SelectItem
                            value={item?.value as string}
                            key={`${item.value}-${index}`}
                            className={cn(
                              'hover:bg-gray-100 cursor-pointer',
                              itemClassName
                            )}
                          >
                            {item?.label}
                          </SelectItem>
                        ))
                      )}
                    </SelectGroup>
                  </SelectContent>
                </div>
              </Select>
              {errorDisplayMode && hasError && (
                <p className="text-sm font-normal text-danger bg-[#C00F0C] text-white pt-1 pb-1 pl-3 pr-3 rounded-md">
                  {errorMessage}
                </p>
              )}
            </div>
            {!errorDisplayMode && hasError && (
              <p className="text-sm font-normal text-danger">{errorMessage}</p>
            )}
          </div>
        );
      }}
    />
  );
};

export default SelectDropDown;
