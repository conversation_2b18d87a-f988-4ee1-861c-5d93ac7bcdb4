import { render, screen } from '@testing-library/react';
import Labels from './Label';
import { describe, expect, it } from 'vitest';

describe('Labels Component', () => {
  it('should render the label with the correct text', () => {
    render(<Labels label="Test Label" />);

    const labelElement = screen.getByText('Test Label');
    expect(labelElement).toBeInTheDocument();
  });

  it('should render a red asterisk (*) if the field is required', () => {
    render(<Labels label="Test Label" validation={{ required: true }} />);

    const asteriskElement = screen.getByText('*');
    expect(asteriskElement).toBeInTheDocument();
    expect(asteriskElement).toHaveClass('text-red-500');
  });

  it('should not render the asterisk if the field is not required', () => {
    render(<Labels label="Test Label" validation={{ required: false }} />);

    const asteriskElement = screen.queryByText('*');
    expect(asteriskElement).not.toBeInTheDocument();
  });

  it('should render the children content passed', () => {
    render(
      <Labels label="Test Label">
        <span>Additional content</span>
      </Labels>
    );

    const childrenContent = screen.getByText('Additional content');
    expect(childrenContent).toBeInTheDocument();
  });

  it('should apply the custom className to the label', () => {
    render(<Labels label="Test Label" className="text-xl" />);

    const labelElement = screen.getByText('Test Label');
    expect(labelElement).toHaveClass('text-xl');
  });
});
