import React from 'react';
import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import {
  useForm,
  FormProvider,
  UseFormReturn,
  FieldValues,
  UseFormProps,
} from 'react-hook-form';
import Text<PERSON>reaField from '.';

// Mock the cn function
vi.mock('@/lib/utils', () => ({
  cn: (...args: any[]) => args.filter(Boolean).join(' '),
}));

interface TestWrapperProps<TFieldValues extends FieldValues = FieldValues> {
  children: (form: UseFormReturn<TFieldValues>) => React.ReactElement;
  formProps?: UseFormProps<TFieldValues>;
}

function TestWrapper<TFieldValues extends FieldValues = FieldValues>({
  children,
  formProps,
}: TestWrapperProps<TFieldValues>) {
  const methods = useForm<TFieldValues>(formProps);
  return <FormProvider {...methods}>{children(methods)}</FormProvider>;
}

describe('TextAreaField', () => {
  it('renders with label and placeholder', () => {
    render(
      <TestWrapper<{ testArea: string }>>
        {(form) => (
          <TextAreaField
            name="testArea"
            form={form}
            label="Test Label"
            placeholder="Enter text here"
          />
        )}
      </TestWrapper>
    );

    expect(screen.getByLabelText('Test Label')).toBeTruthy();
    expect(screen.getByPlaceholderText('Enter text here')).toBeTruthy();
  });

  it('handles text input correctly', () => {
    render(
      <TestWrapper<{ testArea: string }>>
        {(form) => (
          <TextAreaField name="testArea" form={form} label="Test TextArea" />
        )}
      </TestWrapper>
    );

    const textarea = screen.getByLabelText('Test TextArea');
    fireEvent.change(textarea, { target: { value: 'test content' } });
    expect(textarea).toHaveValue('test content');
  });

  it('displays error message when validation fails', async () => {
    render(
      <TestWrapper<{ testArea: string }>
        formProps={{
          mode: 'onBlur',
          defaultValues: { testArea: '' },
        }}
      >
        {(form) => (
          <TextAreaField
            name="testArea"
            form={form}
            label="Test TextArea"
            validation={{ required: 'Required' }}
          />
        )}
      </TestWrapper>
    );

    const textarea = screen.getByLabelText('Test TextArea');
    fireEvent.blur(textarea);

    const errorMessage = await screen.findByText(/Required/i);
    expect(errorMessage).toBeInTheDocument();
  });

  it('respects readonly attribute', () => {
    render(
      <TestWrapper<{ testArea: string }>>
        {(form) => (
          <TextAreaField
            name="testArea"
            form={form}
            label="Test TextArea"
            readonly={true}
          />
        )}
      </TestWrapper>
    );

    const textarea = screen.getByLabelText('Test TextArea');
    expect(textarea).toHaveAttribute('readonly');
  });

  it('applies custom className', () => {
    const customClass = 'custom-textarea-class';
    render(
      <TestWrapper<{ testArea: string }>>
        {(form) => (
          <TextAreaField
            name="testArea"
            form={form}
            label="Test TextArea"
            className={customClass}
          />
        )}
      </TestWrapper>
    );

    const textarea = screen.getByLabelText('Test TextArea');
    expect(textarea.classList.contains(customClass)).toBe(true);
  });

  it('renders with custom number of rows', () => {
    render(
      <TestWrapper<{ testArea: string }>>
        {(form) => (
          <TextAreaField
            name="testArea"
            form={form}
            label="Test TextArea"
            rows={5}
          />
        )}
      </TestWrapper>
    );

    const textarea = screen.getByLabelText('Test TextArea');
    expect(textarea).toHaveAttribute('rows', '5');
  });

  it('handles string type required validation message', async () => {
    render(
      <TestWrapper<{ testArea: string }>
        formProps={{
          mode: 'onBlur',
          defaultValues: { testArea: '' },
        }}
      >
        {(form) => (
          <TextAreaField
            name="testArea"
            form={form}
            label="Test TextArea"
            validation={{ required: 'Custom required message' }}
          />
        )}
      </TestWrapper>
    );

    const textarea = screen.getByLabelText('Test TextArea');
    fireEvent.blur(textarea);

    const errorMessage = await screen.findByText('Custom required message');
    expect(errorMessage).toBeInTheDocument();
  });

  it('handles boolean type required validation', async () => {
    render(
      <TestWrapper<{ testArea: string }>
        formProps={{
          mode: 'onBlur',
          defaultValues: { testArea: '' },
        }}
      >
        {(form) => (
          <TextAreaField
            name="testArea"
            form={form}
            label="Test TextArea"
            validation={{ required: true }}
          />
        )}
      </TestWrapper>
    );

    const textarea = screen.getByLabelText('Test TextArea');
    fireEvent.blur(textarea);

    const errorMessage = await screen.findByText('Required');
    expect(errorMessage).toBeInTheDocument();
  });
});
