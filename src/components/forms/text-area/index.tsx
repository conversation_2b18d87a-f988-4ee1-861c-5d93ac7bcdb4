import { cn } from '@/lib/utils';
import { ChangeEvent } from 'react';
import {
  Controller,
  FieldValues,
  Path,
  RegisterOptions,
  UseFormReturn,
} from 'react-hook-form';
import { Textarea } from '../../ui/textarea';
import Labels from '../Label';

interface TextAreaFieldProps<T extends FieldValues> {
  name: Path<T>;
  form: UseFormReturn<T>;
  label?: string;
  validation?: RegisterOptions<T, Path<T>>;
  placeholder?: string;
  readonly?: boolean;
  className?: string;
  pClassName?: string;
  onChange?: (event: ChangeEvent<HTMLTextAreaElement>) => void;
  rows?: number;
  maxLength?: number;
  disabled?: boolean;
  extraLabel?: React.ReactNode;
}

const TextAreaField = <T extends FieldValues>({
  name,
  label,
  validation,
  placeholder,
  form,
  readonly,
  pClassName,
  className,
  rows,
  onChange,
  maxLength,
  disabled = false,
  extraLabel,
}: TextAreaFieldProps<T>) => {
  // Set up validation options, including handling required field messages
  const isRequired = validation?.required ? 'Required' : undefined;
  const requiredMessage =
    validation && typeof validation.required === 'string'
      ? validation.required
      : isRequired;

  const validationOptions = validation
    ? {
        ...validation,
        required: requiredMessage,
      }
    : {};

  const hasError = !!form.formState.errors[name];

  return (
    <div className={cn('flex flex-col gap-2', pClassName)}>
      {/* Label section with required indicator */}
      <div className="flex flex-row gap-x-2">
        {label && (
          <Labels htmlFor={name} label={label} validation={validation} />
        )}
        {extraLabel && extraLabel}
      </div>

      {/* Using Controller component for better form control */}
      <Controller
        name={name}
        control={form.control}
        rules={validationOptions}
        render={({ field }) => (
          <Textarea
            id={name}
            readOnly={readonly}
            placeholder={placeholder}
            value={field.value || ''}
            onChange={(e) => {
              field.onChange(e);
              onChange?.(e);
            }}
            maxLength={maxLength ?? undefined}
            onBlur={field.onBlur}
            className={cn(
              'border-border-Default placeholder:text-[#B3B3B3] focus-visible:border-0',
              hasError ? 'border-danger border-2' : '',
              className
            )}
            rows={rows}
            disabled={disabled}
          />
        )}
      />

      {/* Display error message if validation fails */}
      {hasError && (
        <p className="text-base font-normal text-danger">
          {form.formState.errors[name]?.message?.toString()}
        </p>
      )}
    </div>
  );
};

export default TextAreaField;
