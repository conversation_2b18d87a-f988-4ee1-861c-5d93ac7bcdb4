import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { expect, it, vi } from 'vitest';
import { useForm } from 'react-hook-form';
import ZipCodeInput from './index';
import { formatZipCode } from '@/lib/utils';
import { describe } from 'vitest';

vi.mock('@/lib/utils', () => ({
  formatZipCode: vi.fn(),
  validateZipCode: vi.fn(),
  cn: vi.fn(),
}));

describe('ZipCodeInput Component', () => {
  const mockValidation = {
    required: true,
    validate: (value: string) => value.length === 5 || 'Invalid Zip Code',
  };

  const renderComponent = (props: any) => {
    const Wrapper = () => {
      const form = useForm();
      return <ZipCodeInput {...props} form={form} />;
    };
    render(<Wrapper />);
  };

  it('should render the input with correct placeholder and handle value change for USA zip code', async () => {
    const props = {
      name: 'zipCode',
      form: { control: vi.fn() },
      label: 'Zip Code',
      isUSA: true,
      validation: mockValidation,
      errorDisplayMode: true,
    };

    renderComponent(props);

    const inputElement = screen.getByPlaceholderText('12345 or 12345-6789');
    expect(inputElement).toBeInTheDocument();

    fireEvent.change(inputElement, { target: { value: '12345' } });

    await waitFor(() => {
      expect(formatZipCode).toHaveBeenCalledWith('12345', true);
    });
  });

  it('should display error message when validation fails', async () => {
    const props = {
      name: 'zipCode',
      form: {
        control: vi.fn(),
        formState: { errors: { zipCode: { message: 'Invalid Zip Code' } } },
      },
      label: 'Zip Code',
      isUSA: true,
      validation: mockValidation,
      errorDisplayMode: true,
    };

    renderComponent(props);

    const inputElement = screen.getByPlaceholderText('12345 or 12345-6789');
    const errorElement = fireEvent.change(inputElement, {
      target: { value: '123' },
    });
    expect(errorElement);
  });

  it('should handle Canadian zip code format correctly', async () => {
    const props = {
      name: 'zipCode',
      form: { control: vi.fn() },
      label: 'Zip Code',
      isUSA: false,
      validation: mockValidation,
      errorDisplayMode: true,
    };

    renderComponent(props);

    const inputElement = screen.getByPlaceholderText('A1B 2C3');
    fireEvent.change(inputElement, { target: { value: 'A1B 2C3' } });
    expect(formatZipCode).toHaveBeenCalledWith('A1B 2C3', false);
  });

  it('should apply error class if there is an error', async () => {
    const props = {
      name: 'zipCode',
      form: {
        control: vi.fn(),
        formState: { errors: { zipCode: { message: 'Invalid Zip Code' } } },
      },
      label: 'Zip Code',
      isUSA: true,
      validation: mockValidation,
      errorDisplayMode: true,
    };

    renderComponent(props);

    const inputElement = screen.getByPlaceholderText('12345 or 12345-6789');
    expect(inputElement);
  });

  it('should display label with validation class if provided', () => {
    const props = {
      name: 'zipCode',
      form: { control: vi.fn() },
      label: 'Zip Code',
      isUSA: true,
      validation: mockValidation,
      labelClassName: 'font-bold',
      errorDisplayMode: false,
    };

    renderComponent(props);

    const labelElement = screen.getByText('Zip Code');
    expect(labelElement);
  });
});
