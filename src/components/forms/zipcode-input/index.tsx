import { Input } from '@/components/ui/input';
import { cn, formatZipCode, validateZipCode } from '@/lib/utils';
import {
  Controller,
  FieldValues,
  Path,
  RegisterOptions,
  UseFormReturn,
} from 'react-hook-form';
import Labels from '../Label';
import get from 'lodash/get';

interface ZipCodeInputProps<T extends FieldValues> {
  name: Path<T>;
  form: UseFormReturn<T>;
  label?: string;
  validation?: RegisterOptions<T, Path<T>>;
  disabled?: boolean;
  labelClassName?: string;
  isUSA: boolean;
  className?: string;
  required?: boolean;
  errorDisplayMode?: boolean;
  autoComplete?: string;
}

const ZipCodeInput = <T extends FieldValues>({
  label,
  validation,
  labelClassName,
  name,
  isUSA,
  form,
  disabled = false,
  className,
  required,
  errorDisplayMode,
  autoComplete = 'postal-code',
}: ZipCodeInputProps<T>) => {
  // const hasError = !!form.formState.errors[name];
  const hasError = !!get(form.formState.errors, name);
  const errorMessage = get(form.formState.errors, name)?.message?.toString();

  return (
    <Controller
      name={name}
      control={form.control}
      rules={{
        required: required ? 'Required' : false,
        validate: (value) => validateZipCode(value, isUSA),
        ...validation,
      }}
      render={({ field }) => (
        <div className="flex flex-col gap-2">
          {label && (
            <Labels
              label={label}
              validation={validation}
              className={labelClassName}
            />
          )}
          <div
            className={
              hasError && errorDisplayMode
                ? 'bg-[#C00F0C] rounded-md'
                : 'flex flex-col gap-2'
            }
          >
            <Input
              className={cn(
                'border-border-Default placeholder:text-[#B3B3B3] focus-visible:border-0',
                hasError ? 'border-danger border-[1px]' : '',
                errorDisplayMode && hasError
                  ? 'focus-visible:ring-offset-[0.5px] focus-visible:ring-[0.5px]'
                  : '',
                className
              )}
              value={field.value ?? ''}
              onChange={(e) => {
                const formatted = formatZipCode(e.target.value, isUSA);
                field.onChange(formatted);
              }}
              placeholder={isUSA ? '12345 or 12345-6789' : 'A1B 2C3'}
              maxLength={isUSA ? 10 : 7}
              disabled={disabled}
              autoComplete={autoComplete}
            />
            {errorDisplayMode && hasError ? (
              <p className="text-sm font-normal bg-[#C00F0C] text-white pt-1 pb-1 pl-3 pr-3 rounded-md">
                {errorMessage}
              </p>
            ) : (
              <p className="text-sm font-normal text-danger">{errorMessage}</p>
            )}
          </div>
        </div>
      )}
    />
  );
};

export default ZipCodeInput;
