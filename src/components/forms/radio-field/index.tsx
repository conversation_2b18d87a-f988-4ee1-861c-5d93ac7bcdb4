import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { cn } from '@/lib/utils';
import { ReactNode } from 'react';
import {
  Controller,
  type FieldValues,
  type Path,
  type UseFormReturn,
} from 'react-hook-form';

interface Option {
  label: string;
  value: string;
  content?: ReactNode;
  disabled?: boolean;
}

interface RadioFieldProps<T extends FieldValues> {
  name: Path<T>;
  form: UseFormReturn<T>;
  label?: string;
  options: Option[];
  optionsPerRow?: number;
  disabled?: boolean;
  required?: boolean;
  onChange?: (value: string) => void;
  className?: string;
  pClassName?: string;
  rowClassName?: string;
  radioItemClassName?: string;
}

const RadioField = <T extends FieldValues>({
  name,
  form,
  label,
  options,
  optionsPerRow = 3,
  disabled = false,
  required = false,
  onChange,
  pClassName,
  className = '',
  rowClassName,
  radioItemClassName,
}: RadioFieldProps<T>) => {
  // Calculate rows of options
  const rows: Option[][] = [];
  for (let i = 0; i < options?.length; i += optionsPerRow) {
    rows.push(options?.slice(i, i + optionsPerRow));
  }

  return (
    <div className={cn('space-y-3', pClassName)}>
      {label && (
        <div className="flex items-center gap-1">
          <Label htmlFor={name} className="text-sm font-medium text-gray-700">
            {label}
          </Label>
          {required && (
            <span className="text-red-500" aria-hidden="true">
              *
            </span>
          )}
        </div>
      )}

      <Controller
        name={name}
        control={form.control}
        rules={{ required: required ? 'Required' : false }}
        render={({ field }) => (
          <RadioGroup
            onValueChange={(value) => {
              field.onChange(value);
              onChange?.(value);
            }}
            value={field.value}
            className={cn('space-y-4', className)}
          >
            {rows.map((row, rowIndex) => (
              <div
                key={rowIndex}
                data-testid={`radio-row-${rowIndex}`}
                className={cn(
                  'flex items-center gap-8 flex-nowrap',
                  rowClassName
                )}
                role="radiogroup"
                aria-label={`${label} options row ${rowIndex + 1}`}
              >
                {row?.map((option) => (
                  <div
                    key={option.value}
                    className={cn(
                      'flex items-center gap-2',
                      radioItemClassName
                    )}
                  >
                    <RadioGroupItem
                      id={`${name}-${option.value}`}
                      value={option.value}
                      disabled={disabled || option?.disabled}
                      className="border-brand-teal-Default"
                    />
                    <Label
                      htmlFor={`${name}-${option.value}`}
                      className="text-sm text-gray-700"
                    >
                      {option?.label}
                    </Label>
                    {option?.content && option?.content}
                  </div>
                ))}
              </div>
            ))}
          </RadioGroup>
        )}
      />

      {form.formState.errors[name] && (
        <p className="text-sm text-red-500" role="alert">
          {form.formState.errors[name]?.message?.toString()}
        </p>
      )}
    </div>
  );
};

export default RadioField;
