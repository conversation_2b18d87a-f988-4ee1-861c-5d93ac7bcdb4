import React from 'react';
import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import {
  useForm,
  FormProvider,
  UseFormReturn,
  FieldValues,
  UseFormProps,
} from 'react-hook-form';
import RadioField from '.';
import { beforeAll } from 'vitest';

// Mock the cn function if needed
vi.mock('@/lib/utils', () => ({
  cn: (...args: any[]) => args.filter(Boolean).join(' '),
}));

// Test wrapper component to provide form context
interface TestWrapperProps<TFieldValues extends FieldValues = FieldValues> {
  children: (form: UseFormReturn<TFieldValues>) => React.ReactElement;
  formProps?: UseFormProps<TFieldValues>;
}

function TestWrapper<TFieldValues extends FieldValues = FieldValues>({
  children,
  formProps,
}: TestWrapperProps<TFieldValues>) {
  const methods = useForm<TFieldValues>(formProps);
  return <FormProvider {...methods}>{children(methods)}</FormProvider>;
}

describe('RadioField Component', () => {
  beforeAll(() => {
    window.HTMLElement.prototype.scrollIntoView = function () {};

    global.ResizeObserver = class ResizeObserver {
      observe() {
        // do nothing
      }
      unobserve() {
        // do nothing
      }
      disconnect() {
        // do nothing
      }
    };
  });
  const defaultOptions = [
    { label: 'Option 1', value: 'option1' },
    { label: 'Option 2', value: 'option2' },
    { label: 'Option 3', value: 'option3' },
  ];

  it('renders with label and options correctly', () => {
    render(
      <TestWrapper<{ test: string }>>
        {(form) => (
          <RadioField
            name="test"
            form={form}
            label="Test Label"
            options={defaultOptions}
          />
        )}
      </TestWrapper>
    );

    expect(screen.getByText('Test Label')).toBeInTheDocument();
    defaultOptions.forEach((option) => {
      expect(screen.getByText(option.label)).toBeInTheDocument();
      expect(
        screen.getByRole('radio', { name: option.label })
      ).toBeInTheDocument();
    });
  });

  it('shows required asterisk when required is true', () => {
    render(
      <TestWrapper<{ test: string }>>
        {(form) => (
          <RadioField
            name="test"
            form={form}
            label="Test Label"
            options={defaultOptions}
            required
          />
        )}
      </TestWrapper>
    );

    expect(screen.getByText('*')).toBeInTheDocument();
  });

  it('displays error message when required field is not filled', async () => {
    render(
      <TestWrapper<{ test: string }>
        formProps={{
          mode: 'onChange',
        }}
      >
        {(form) => (
          <form onSubmit={form.handleSubmit(() => {})}>
            <RadioField
              name="test"
              form={form}
              label="Test Label"
              options={defaultOptions}
              required
            />
            <button type="submit">Submit</button>
          </form>
        )}
      </TestWrapper>
    );

    fireEvent.click(screen.getByText('Submit'));

    await waitFor(() => {
      expect(screen.getByRole('alert')).toHaveTextContent('Required');
    });
  });

  it('handles option selection and onChange callback', async () => {
    const onChangeMock = vi.fn();
    let formInstance: UseFormReturn<{ test: string }>;

    render(
      <TestWrapper<{ test: string }>>
        {(form) => {
          formInstance = form;
          return (
            <RadioField
              name="test"
              form={form}
              label="Test Label"
              options={defaultOptions}
              onChange={onChangeMock}
            />
          );
        }}
      </TestWrapper>
    );

    const radioOption = screen.getByRole('radio', { name: 'Option 1' });
    fireEvent.click(radioOption);

    await waitFor(() => {
      expect(onChangeMock).toHaveBeenCalledWith('option1');
      expect(formInstance.getValues().test).toBe('option1');
    });
  });

  it('disables all radio buttons when disabled prop is true', () => {
    render(
      <TestWrapper<{ test: string }>>
        {(form) => (
          <RadioField
            name="test"
            form={form}
            label="Test Label"
            options={defaultOptions}
            disabled
          />
        )}
      </TestWrapper>
    );

    const radioButtons = screen.getAllByRole('radio');
    radioButtons.forEach((radio) => {
      expect(radio).toBeDisabled();
    });
  });

  it('applies custom className correctly', () => {
    render(
      <TestWrapper<{ test: string }>>
        {(form) => (
          <RadioField
            name="test"
            form={form}
            label="Test Label"
            options={defaultOptions}
            className="custom-class"
          />
        )}
      </TestWrapper>
    );

    const container = screen.getByText('Test Label').closest('div');
    expect(container?.parentElement).toHaveClass('space-y-3');
  });

  it('updates form value correctly when selection changes', async () => {
    let formInstance: UseFormReturn<{ test: string }>;

    render(
      <TestWrapper<{ test: string }>>
        {(form) => {
          formInstance = form;
          return (
            <RadioField
              name="test"
              form={form}
              label="Test Label"
              options={defaultOptions}
            />
          );
        }}
      </TestWrapper>
    );

    const radioOption1 = screen.getByRole('radio', { name: 'Option 1' });
    const radioOption2 = screen.getByRole('radio', { name: 'Option 2' });

    fireEvent.click(radioOption1);
    await waitFor(() => {
      expect(formInstance.getValues().test).toBe('option1');
    });

    fireEvent.click(radioOption2);
    await waitFor(() => {
      expect(formInstance.getValues().test).toBe('option2');
    });
  });
});
