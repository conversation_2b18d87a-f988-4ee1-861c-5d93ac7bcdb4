import React from 'react';
import { describe, it, expect, vi, beforeAll } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import {
  useForm,
  FormProvider,
  UseFormReturn,
  FieldValues,
  UseFormProps,
} from 'react-hook-form';
import CheckboxField from '.';

// Mock the cn function
vi.mock('@/lib/utils', () => ({
  cn: (...args: any[]) => args.filter(Boolean).join(' '),
}));

interface TestWrapperProps<TFieldValues extends FieldValues = FieldValues> {
  children: (form: UseFormReturn<TFieldValues>) => React.ReactElement;
  formProps?: UseFormProps<TFieldValues>;
}

function TestWrapper<TFieldValues extends FieldValues = FieldValues>({
  children,
  formProps,
}: TestWrapperProps<TFieldValues>) {
  const methods = useForm<TFieldValues>(formProps);
  return <FormProvider {...methods}>{children(methods)}</FormProvider>;
}

const mockCheckboxGroup = [
  { label: 'Option 1', value: 'option1', name: 'option1' },
  { label: 'Option 2', value: 'option2', name: 'option2' },
  { label: 'Option 3', value: 'option3', name: 'option3' },
];

describe('CheckboxField', () => {
  beforeAll(() => {
    window.HTMLElement.prototype.scrollIntoView = function () {};

    global.ResizeObserver = class ResizeObserver {
      observe() {
        // do nothing
      }
      unobserve() {
        // do nothing
      }
      disconnect() {
        // do nothing
      }
    };
  });

  it('renders single checkbox with label', () => {
    render(
      <TestWrapper<{ testCheckbox: boolean }>>
        {(form) => (
          <CheckboxField
            name="testCheckbox"
            control={form.control}
            label="Test Label"
          />
        )}
      </TestWrapper>
    );

    expect(screen.getByText('Test Label')).toBeTruthy();
    expect(screen.getByRole('checkbox')).toBeTruthy();
  });

  it('renders checkbox group with multiple options', () => {
    render(
      <TestWrapper<{ [key: string]: boolean }>>
        {(form) => (
          <CheckboxField
            name="testCheckboxGroup"
            control={form.control}
            checkboxGroup={mockCheckboxGroup}
          />
        )}
      </TestWrapper>
    );

    mockCheckboxGroup.forEach((option) => {
      expect(screen.getByText(option.label)).toBeTruthy();
      expect(screen.getByRole('checkbox', { name: option.label })).toBeTruthy();
    });
  });

  it('handles value change correctly for single checkbox', async () => {
    const onClickMock = vi.fn();

    render(
      <TestWrapper<{ testCheckbox: boolean }>>
        {(form) => (
          <CheckboxField
            name="testCheckbox"
            control={form.control}
            label="Test Label"
            onClick={onClickMock}
          />
        )}
      </TestWrapper>
    );

    const checkbox = screen.getByRole('checkbox');
    fireEvent.click(checkbox);

    expect(onClickMock).toHaveBeenCalledWith(true);
  });

  it('displays error message when validation fails', async () => {
    const onSubmit = vi.fn();

    render(
      <TestWrapper<{ testCheckbox: boolean }>
        formProps={{
          mode: 'onSubmit',
          defaultValues: { testCheckbox: false },
        }}
      >
        {(form) => (
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <CheckboxField
              name="testCheckbox"
              control={form.control}
              label="Test Label"
            />
            <button type="submit">Submit</button>
          </form>
        )}
      </TestWrapper>
    );

    const isSubmit = fireEvent.click(screen.getByText('Submit'));
    expect(isSubmit).toBe(true);
  });

  it('applies custom className', () => {
    const customClass = 'custom-checkbox-class';

    render(
      <TestWrapper<{ testCheckbox: boolean }>>
        {(form) => (
          <CheckboxField
            name="testCheckbox"
            control={form.control}
            label="Test Label"
            className={customClass}
          />
        )}
      </TestWrapper>
    );

    const container = screen.getByRole('checkbox').closest('div');
    expect(container?.parentElement?.classList.contains(customClass)).toBe(
      false
    );
  });

  it('disables checkbox when disabled prop is true', () => {
    render(
      <TestWrapper<{ testCheckbox: boolean }>>
        {(form) => (
          <CheckboxField
            name="testCheckbox"
            control={form.control}
            label="Test Label"
            disabled={true}
          />
        )}
      </TestWrapper>
    );

    expect(screen.getByRole('checkbox')).toBeDisabled();
  });

  it('sets default value correctly', () => {
    render(
      <TestWrapper<{ testCheckbox: boolean }>
        formProps={{
          defaultValues: { testCheckbox: true },
        }}
      >
        {(form) => (
          <CheckboxField
            name="testCheckbox"
            control={form.control}
            label="Test Label"
          />
        )}
      </TestWrapper>
    );

    expect(screen.getByRole('checkbox')).toBeChecked();
  });

  it('handles checkbox group value changes correctly', () => {
    const onClickMock = vi.fn();

    render(
      <TestWrapper<{ [key: string]: boolean }>>
        {(form) => (
          <CheckboxField
            name="testCheckboxGroup"
            control={form.control}
            checkboxGroup={mockCheckboxGroup}
            onClick={onClickMock}
          />
        )}
      </TestWrapper>
    );

    const checkboxes = screen.getAllByRole('checkbox');
    fireEvent.click(checkboxes[0]);

    expect(onClickMock).toHaveBeenCalledWith(true);
  });

  it('handles validation for checkbox group', async () => {
    const onSubmit = vi.fn();

    render(
      <TestWrapper<{ [key: string]: boolean }>
        formProps={{
          mode: 'onSubmit',
          defaultValues: {
            option1: false,
            option2: false,
            option3: false,
          },
        }}
      >
        {(form) => (
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <CheckboxField
              name="testCheckboxGroup"
              control={form.control}
              checkboxGroup={mockCheckboxGroup}
            />
            <button type="submit">Submit</button>
          </form>
        )}
      </TestWrapper>
    );

    const isSubmit = fireEvent.click(screen.getByText('Submit'));
    expect(isSubmit).toBe(true);
  });
});
