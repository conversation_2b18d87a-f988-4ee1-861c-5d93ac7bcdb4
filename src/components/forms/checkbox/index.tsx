import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { CheckedState } from '@radix-ui/react-checkbox';
import { Controller } from 'react-hook-form';

interface CheckboxListType {
  name: string;
  label: string;
  value?: string;
}

export interface CheckboxFieldTyps {
  control: any;
  name?: string;
  checkboxGroup?: CheckboxListType[];
  value?: string;
  label?: string;
  helperText?: string;
  required?: string;
  pattern?: RegExp;
  patternMessage?: string;
  className?: string;
  pClassName?: string;
  defaultValue?: any;
  disabled?: boolean;
  onClick?: (checked: CheckedState | boolean) => void | any;
}

const CheckboxField = (props: CheckboxFieldTyps) => {
  const {
    label,
    name,
    helperText,
    required,
    control,
    pattern,
    patternMessage,
    checkboxGroup,
    className,
    defaultValue,
    disabled,
    onClick,
    pClassName,
  } = props;

  const itemsPerRow = 2;
  return (
    <div className={`my-0`}>
      <div
        className={`flex flex-wrap justify content-center my-2 ${pClassName}`}
      >
        {checkboxGroup ? (
          checkboxGroup?.map((list, index) => {
            if (index % itemsPerRow === 0) {
              return (
                <Controller
                  key={`${list.name}-${index}`}
                  name={list.name}
                  control={control}
                  render={({ field }) => (
                    <div className="flex  items-center space-x-1 mb-3 pr-1  w-1/2">
                      <Checkbox
                        className={className}
                        checked={field.value}
                        onCheckedChange={(checked) => {
                          field.onChange(checked ? checked : false);
                          onClick && onClick?.(checked);
                        }}
                        id={`${list.name}-${list.label}`}
                        disabled={disabled}
                      />
                      <Label
                        htmlFor={`${list.name}-${list.label}`}
                        className={`block text-base  text-[#1E1E1E] font-normal`}
                      >
                        {list.label}
                      </Label>
                    </div>
                  )}
                />
              );
            } else {
              return (
                <Controller
                  key={`${list.name}-${index}`}
                  name={list.name}
                  control={control}
                  render={({ field }) => (
                    <div className="flex items-center space-x-1 mb-3 pr-1">
                      <Checkbox
                        className="w-5 h-5"
                        checked={field.value}
                        onCheckedChange={(checked) => {
                          field.onChange(checked ? checked : false);
                          onClick && onClick?.(checked);
                        }}
                        id={`${list.name}-${list.label}`}
                        disabled={disabled}
                      />
                      <Label
                        htmlFor={`${list.name}-${list.label}`}
                        className={`block text-base  text-[#1E1E1E] font-normal`}
                      >
                        {list.label}
                      </Label>
                    </div>
                  )}
                />
              );
            }
          })
        ) : (
          <>
            <Controller
              name={name || ''}
              rules={{
                required: required,
                pattern: pattern
                  ? { value: pattern, message: patternMessage || '' }
                  : undefined,
              }}
              defaultValue={defaultValue}
              control={control}
              render={({ field }) => (
                <div className="flex items-center space-x-3">
                  <Checkbox
                    className={className}
                    checked={field.value}
                    onCheckedChange={(checked) => {
                      field.onChange(checked ? checked : false);
                      onClick && onClick?.(checked);
                    }}
                    id={`${name}-${label}`}
                    disabled={disabled}
                  />
                  {label && (
                    <Label
                      htmlFor={`${name}-${label}`}
                      className={`block text-base  text-[#1E1E1E] font-normal`}
                    >
                      {label}
                    </Label>
                  )}
                </div>
              )}
            />
            {helperText && (
              <Label className="text-xs text-red-600">{helperText}</Label>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default CheckboxField;
