import { cn, convertToFloat } from '@/lib/utils';
import { Eye, EyeOff } from 'lucide-react';
import { ChangeEvent, useState } from 'react';
import {
  FieldValues,
  Path,
  RegisterOptions,
  UseFormReturn,
} from 'react-hook-form';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import Labels from './Label';

interface PasswordInputFieldProps<T extends FieldValues> {
  name: Path<T>;
  form: UseFormReturn<T>;
  label?: string;
  validation?: RegisterOptions<T, Path<T>>;
  placeholder?: string;
  readonly?: boolean;
  onChange?: (event: ChangeEvent<HTMLInputElement>) => void;
  className?: string;
  description?: string;
  disabled?: boolean;
  extraLabel?: React.ReactNode;
  isShowError?: boolean;
  maxLength?: number;
  toFloat?: boolean;
  prefix?: string;
  labelClassName?: string;
  onBlur?: () => void;
  errorDisplayMode?: boolean;
}

const PasswordInputField = <T extends FieldValues>({
  name,
  label,
  validation,
  placeholder,
  form,
  disabled = false,
  onChange,
  className,
  description,
  extraLabel,
  isShowError = true,
  maxLength,
  toFloat,
  labelClassName,
  onBlur,
  errorDisplayMode,
}: PasswordInputFieldProps<T>) => {
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);

  const togglePasswordVisibility = () => {
    setIsPasswordVisible(!isPasswordVisible);
  };

  const isRequired = validation?.required ? 'Required' : undefined;
  const requiredMessage =
    validation && typeof validation.required === 'string'
      ? validation.required
      : isRequired;

  const validationOptions = validation
    ? {
        ...validation,
        required: requiredMessage,
      }
    : {};

  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    const { value } = event.target;
    if (maxLength && value.length > maxLength) {
      event.target.value = value.slice(0, maxLength);
    }
    form.register(name, validationOptions).onChange(event);
    if (onChange) {
      onChange(event);
    }
  };

  const handleOnBlur = (event: React.FocusEvent<HTMLInputElement>) => {
    const { value } = event.target;
    if (toFloat) {
      event.target.value = value ? convertToFloat({ value }) : value;
    }
    onBlur && onBlur();
    form.register(name, validationOptions).onChange(event);
    if (onChange) {
      onChange(event);
    }
  };

  const hasError = !!form.formState.errors[name];

  return (
    <div className="relative flex flex-col gap-2">
      {(extraLabel || label) && (
        <div className="flex flex-row gap-x-2">
          {label && (
            <Labels
              htmlFor={name}
              label={label}
              validation={validation}
              className={labelClassName}
            />
          )}
          {extraLabel && extraLabel}
        </div>
      )}
      {description && (
        <Label className="text-text-tertiary text-base font-normal">
          {description}
        </Label>
      )}
      <div className="relative">
        <Input
          className={cn(
            'border-border-Default placeholder:text-[#B3B3B3] focus-visible:border-0',
            hasError ? 'border-danger border-[1px]' : '',
            errorDisplayMode && hasError
              ? 'rounded-t-md rounded-b-none focus-visible:ring-offset-1 focus-visible:ring-1'
              : '',
            className
          )}
          id={name}
          type={isPasswordVisible ? 'text' : 'password'}
          disabled={disabled}
          placeholder={placeholder}
          autoComplete="off"
          {...form.register(name, validationOptions)}
          maxLength={maxLength ?? undefined}
          onChange={handleChange}
          onBlur={handleOnBlur}
        />
        <button
          type="button"
          onClick={togglePasswordVisibility}
          className={cn(
            'absolute right-3 top-[10px] text-gray-600',
            disabled ? 'cursor-not-allowed' : ''
          )}
          disabled={disabled}
        >
          {isPasswordVisible ? (
            <Eye className="w-5 h-5" />
          ) : (
            <EyeOff className="w-5 h-5" />
          )}
        </button>
      </div>
      {errorDisplayMode && hasError ? (
        <p
          className={cn(
            'text-xs font-normal text-white bg-red-500 p-1 pl-2 w-full rounded-b-md z-0 -mt-[8px]'
          )}
        >
          {form.formState.errors[name]?.message?.toString()}
        </p>
      ) : isShowError && hasError ? (
        <p className={cn('text-sm font-normal text-danger')}>
          {form.formState.errors[name]?.message?.toString()}
        </p>
      ) : null}
    </div>
  );
};

export default PasswordInputField;
