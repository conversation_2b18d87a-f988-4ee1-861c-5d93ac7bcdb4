import { cn } from '@/lib/utils';
import { useGetItemLookupDropDownQuery } from '@/redux/features/items/item.api';
import debounce from 'lodash/debounce';
import get from 'lodash/get';
import { useCallback, useEffect, useMemo, useState } from 'react';

import {
  Controller,
  FieldValues,
  Path,
  RegisterOptions,
  UseFormReturn,
} from 'react-hook-form';
import Select, { MenuPlacement, components } from 'react-select';
import CreatableSelect from 'react-select/creatable';

import Labels from '../Label';

interface Options<T> {
  label: string;
  value: string;
  item?: T | any;
}

interface AutoCompleteDropdownProps<T extends FieldValues> {
  url: string;
  name: Path<T>;
  form: UseFormReturn<T>;
  placeholder?: string;
  onSelectChange?: (selected: Options<T>, value?: string) => void;
  fieldName?: string;
  labelKey: string;
  valueKey: string;
  label?: string;
  sortBy?: string;
  operator?: string;
  validation?: RegisterOptions<T, Path<T>>;
  className?: string;
  showItem?: boolean; // to add the item to the option list
  formatLabel?: (label: string) => string;
  labelComponent?: (value: T | any, item?: T | any) => any;
  enableSearch?: boolean;
  disabled?: boolean;
  isClearable?: boolean;
  isRefetch?: boolean;
  menuPosition?: 'fixed' | 'absolute';
  menuPlacement?: MenuPlacement;
  allowCustomEntry?: boolean;
  filterBy?: { field: string; value: string; operator: string }[];
  acceptAlphanumeric?: boolean;
  searchType?: 'startsWith' | 'contains';
  formatSearch?: (value: string) => string;
}

const AutoCompleteDropdown = <T extends FieldValues>({
  placeholder,
  name,
  form,
  label,
  onSelectChange,
  url,
  sortBy = '',
  fieldName,
  labelKey,
  valueKey,
  showItem,
  operator = 'Contains',
  formatSearch,
  formatLabel,
  labelComponent,
  validation,
  className,
  enableSearch,
  disabled,
  isClearable,
  isRefetch,
  menuPosition,
  menuPlacement = 'auto',
  allowCustomEntry,
  filterBy,
  acceptAlphanumeric = true,
  searchType,
}: AutoCompleteDropdownProps<T>) => {
  const [search, setSearch] = useState('');
  const [totalCount, setTotalCount] = useState(10);
  const [isTablet, setIsTablet] = useState(window?.innerWidth >= 768);
  // get the option list
  const {
    data: optionData,
    isFetching,
    refetch,
  } = useGetItemLookupDropDownQuery(
    {
      url: url,
      body: {
        pageSize: totalCount,
        pageNumber: 1,
        sortBy: sortBy ?? labelKey,
        filters: [
          {
            field: fieldName ?? labelKey,
            value: formatSearch ? formatSearch(search) : search,
            operator,
          },
          ...(filterBy ?? []),
        ],
        sortAscending: true,
      },
    },
    {
      skip: !url,
      refetchOnMountOrArgChange: true,
    }
  );

  useEffect(() => {
    if (isRefetch) {
      refetch();
    }
  }, [isRefetch, refetch]);

  const options = useMemo(() => {
    const optionList = optionData?.data?.map((item: any) => ({
      label: formatLabel
        ? formatLabel(item[labelKey])
        : (item[labelKey] as string),
      value: item[valueKey] as string,
    }));
    return optionList?.filter((item: Options<T>) => item?.label) || [];
  }, [optionData?.data, formatLabel, labelKey, valueKey]);

  // Effect hook to track screen resizing
  useEffect(() => {
    const handleResize = () => {
      setIsTablet(window?.innerWidth >= 768);
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // search
  const debouncedSearch = debounce((searchTerm: string) => {
    setSearch(searchTerm);
    setTotalCount(10);
  }, 500);

  // useEffect(() => {
  //   return () => debouncedSearch.cancel();
  // }, [debouncedSearch]);

  // handle house scroll
  const handleScroll = useCallback(() => {
    if (
      !isFetching &&
      options?.length < (optionData?.pagination?.totalCount ?? 0)
    ) {
      setTotalCount((prev) => prev + 10);
    }
  }, [optionData?.pagination?.totalCount, isFetching, options?.length]);

  // Set up validation options, including the required field message if needed
  const isRequired = validation?.required ? 'Required' : undefined;
  const requiredMessage =
    validation && typeof validation.required === 'string'
      ? validation.required
      : isRequired;

  const validationOptions = validation
    ? {
        ...validation,
        required: requiredMessage,
      }
    : {};

  // const hasError = !!get(form.formState.errors, name);
  const errorMessage = get(form.formState.errors, name)?.message?.toString();

  const customStyles = {
    control: (base: any, state: any) => ({
      ...base,
      color: '#1E1E1E',
      height: '40px',
      ...(isTablet && { overflow: 'hidden', whiteSpace: 'nowrap' }), // For tablet screen sizes
      border:
        errorMessage && state.isFocused
          ? '1px solid #491d96' // Purple border when focused with error
          : errorMessage
            ? '1px solid red' // Red border if there's an error
            : '',
      borderWidth: '1px',
      borderRadius: '6px',
      '&:hover': {
        border:
          errorMessage && state.isFocused
            ? '1px solid #491d96' // Purple border on hover with error
            : errorMessage
              ? '1px solid red' // Red border on hover if error
              : '',
      },
    }),
    option: (styles: any, { isSelected }: any) => ({
      ...styles,
      background: isSelected ? '#F3F3F3' : undefined,
      color: isSelected ? '#491d96' : 'black',
      ':hover': {
        backgroundColor: '#F1F1F1',
        color: '#491d96',
      },
    }),
  };

  const onBlurWorkaround = (event: React.FocusEvent<HTMLInputElement>) => {
    const element = event.relatedTarget;
    if (
      element &&
      (element.tagName === 'A' ||
        element.tagName === 'BUTTON' ||
        element.tagName === 'INPUT')
    ) {
      (element as HTMLElement).focus();
    }
  };

  const CustomOption = (props: any) => {
    const item: any = optionData?.data?.find(
      (i: any) => i[valueKey] === props?.data?.value
    );
    const label = props?.data?.label;
    return (
      <components.Option {...props}>
        {labelComponent ? labelComponent(label, item) : label}
      </components.Option>
    );
  };

  const SelectComponent = allowCustomEntry ? CreatableSelect : Select;

  return (
    <Controller
      name={name}
      control={form.control}
      rules={validationOptions}
      render={({ field: { onChange }, fieldState: { error } }) => {
        const value = form.getValues(name);
        return (
          <div className={cn('w-full')}>
            {label && (
              <div className="pb-2">
                <Labels htmlFor={name} label={label} validation={validation} />
              </div>
            )}
            <SelectComponent
              placeholder={
                <div className="text-[#b3b3b3] truncate">{placeholder}</div>
              }
              value={value && value?.label && value?.value ? value : null}
              options={options as any}
              isLoading={isFetching}
              isSearchable={enableSearch}
              // onInputChange={debouncedSearch}
              onInputChange={(inputValue, { action }) => {
                if (action === 'input-change') {
                  if (!acceptAlphanumeric) {
                    const cleaned = inputValue.replace(/[^0-9]/g, '');
                    debouncedSearch(cleaned);
                    return cleaned;
                  }
                  debouncedSearch(inputValue);
                }
                return inputValue;
              }}
              filterOption={
                searchType === 'startsWith'
                  ? (option, inputValue) =>
                      option?.label
                        ?.toLowerCase()
                        ?.startsWith(inputValue?.toLowerCase())
                  : undefined
              }
              onMenuClose={() => debouncedSearch('')}
              onMenuScrollToBottom={handleScroll}
              menuPosition={menuPosition}
              menuPlacement={menuPlacement}
              // onMenuClose={handleMenuClose}
              onBlur={onBlurWorkaround}
              onChange={(newValue: any) => {
                onChange(newValue);
                const selectedNewValue = showItem
                  ? {
                      item: optionData?.data?.find(
                        (item: any) =>
                          (item[valueKey] as string) === newValue?.value
                      ),
                    }
                  : (newValue as Options<T>);

                onSelectChange?.(
                  selectedNewValue as Options<T>,
                  newValue?.value
                );
              }}
              isDisabled={disabled}
              isClearable={!disabled && isClearable}
              components={{
                IndicatorSeparator: () => null,
                Option: CustomOption,
              }}
              className={cn('h-10 sm:text-base md:text-sm w-full', className)}
              formatCreateLabel={(inputValue) => `Add "${inputValue}"`}
              // menuPortalTarget={document.body}
              styles={customStyles}
              theme={(theme) => ({
                ...theme,
                colors: {
                  ...theme.colors,
                  primary25: '#F3F3F3',
                  primary: '#491d96',
                },
              })}
            />
            {error && (
              <p className="text-sm font-normal text-danger pt-1">
                {error?.message}
              </p>
            )}
          </div>
        );
      }}
    />
  );
};

export default AutoCompleteDropdown;
