import { FieldValues, Path, RegisterOptions } from 'react-hook-form';
import { Label } from '../ui/label';

interface LabelProps<T extends FieldValues> {
  htmlFor?: string;
  label: string;
  className?: string;
  children?: React.ReactNode;
  validation?: RegisterOptions<T, Path<T>>;
}

const Labels = <T extends FieldValues>({
  htmlFor,
  label,
  className,
  children,
  validation,
}: LabelProps<T>) => {
  return (
    <div className="flex items-center space-x-1">
      <Label
        htmlFor={htmlFor}
        className={`block text-base  text-[#1E1E1E] font-normal ${className}`}
      >
        {label}
      </Label>
      {validation?.required && <div className="text-red-500">*</div>}
      {children}
    </div>
  );
};

export default Labels;
