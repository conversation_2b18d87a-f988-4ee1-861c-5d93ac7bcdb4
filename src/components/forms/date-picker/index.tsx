import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Label } from '@/components/ui/label';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn, dayJsFormat, formatDate, validateDateInput } from '@/lib/utils';
import get from 'lodash/get';
import { CalendarIcon } from 'lucide-react';
import React, { memo, useEffect, useRef, useState } from 'react';
import { Controller, RegisterOptions, UseFormReturn } from 'react-hook-form';
import { PatternFormat } from 'react-number-format';
import Labels from '../Label';

interface DatePickerInputProps {
  name: string;
  form: UseFormReturn | any;
  label?: string;
  validation?: RegisterOptions;
  placeholder?: string;
  readonly?: boolean;
  onDateChange?: (date: Date | string | undefined) => void;
  className?: string;
  description?: string;
  disabled?: boolean;
  extraLabel?: React.ReactNode;
  isShowError?: boolean;
  maxLength?: number;
  fromYear?: number;
  toYear?: number;
  disablePastDate?: any;
  format?: string;
  enableInput?: boolean;
  inputClassName?: string;
  pClassName?: string;
  isRenderFirst?: boolean;
  includeWeekends?: boolean;
  handleBlur?: (date: Date | string | undefined) => void;
}

const DatePicker = ({
  name,
  label,
  extraLabel,
  placeholder = 'Select',
  onDateChange,
  className,
  validation,
  form,
  disabled = false,
  fromYear = 1960,
  toYear = 10,
  disablePastDate,
  description,
  isShowError = true,
  format = 'YYYY-MM-DDTHH:mm:ss.SSSSSS',
  enableInput = false,
  inputClassName,
  pClassName,
  isRenderFirst = false,
  includeWeekends,
  handleBlur,
}: DatePickerInputProps) => {
  const [open, setOpen] = useState<boolean>(false);
  const [isFocused, setIsFocused] = useState<boolean>(false); // Track focus state
  const [errorText, setErrorText] = useState<string>(''); // Track focus state
  const isFirstRender = useRef(true);
  const [hasMounted, setHasMounted] = useState(false);

  const isWeekend = (date: Date) => {
    const day = date.getDay();
    return day === 0 || day === 6; // 0 = Sunday, 6 = Saturday
  };

  const startMonth = new Date(fromYear, 0);
  const endMonth = new Date(new Date().getFullYear() + toYear, 0);

  useEffect(() => {
    if (isRenderFirst) {
      setHasMounted(true);
    }
  }, [isRenderFirst]);

  // const hasError = !!form.formState.errors[name];

  const hasError = !!get(form.formState.errors, name);
  const errorMessage = get(form.formState.errors, name)?.message?.toString();

  return (
    <div className={cn('flex flex-col gap-2', pClassName)}>
      {(extraLabel || label) && (
        <div className="flex flex-row gap-x-2 md:truncate" title={label}>
          {label && (
            <Labels htmlFor={name} label={label} validation={validation} />
          )}
          {extraLabel && extraLabel}
        </div>
      )}

      {description && (
        <Label className="text-text-tertiary text-base font-normal">
          {description}
        </Label>
      )}

      <Controller
        control={form.control}
        name={name}
        rules={validation}
        render={({ field: { value, onChange } }) => {
          const parsedDate = new Date(value);
          return (
            <Popover open={open} onOpenChange={setOpen}>
              {enableInput ? (
                <div className={cn('relative w-full', inputClassName)}>
                  <PatternFormat
                    format="##/##/####"
                    allowEmptyFormatting
                    mask={'_'}
                    className={cn(
                      'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground  focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-70 disabled:bg-[#F5F5F5] focus-visible:border-0',
                      hasError || errorText ? 'border-danger border' : ''
                    )}
                    onFocus={() => {
                      setIsFocused(true);
                    }}
                    onBlur={(event) => {
                      setIsFocused(false);
                      const newvalue = event.target.value;
                      handleBlur?.(newvalue);
                    }}
                    disabled={disabled}
                    value={
                      parsedDate.toString() === 'Invalid Date'
                        ? value
                        : formatDate(value)
                    }
                    isAllowed={(values) => {
                      return validateDateInput(values.formattedValue);
                    }}
                    onValueChange={(values) => {
                      if (isRenderFirst && !hasMounted) return;
                      if (isFirstRender.current && isRenderFirst) {
                        isFirstRender.current = false;
                        return;
                      }
                      const inputValue = values.formattedValue;
                      const parsedDate = new Date(inputValue);

                      if (
                        parsedDate.toString() === 'Invalid Date' &&
                        values.value.length !== 0
                      ) {
                        setErrorText('Invalid Date');
                        onDateChange?.(inputValue);
                      } else {
                        // PTR-2723, Sprint:14 Cosmetic Issues
                        const year = parsedDate.getFullYear();
                        const currentYear = new Date().getFullYear();
                        const maxYear = currentYear + toYear;

                        if (
                          (year < fromYear || year > maxYear) &&
                          values.value.length !== 0
                        ) {
                          setErrorText('Invalid Date');
                          return;
                        }
                        // End PTR-2723, Sprint:14 Cosmetic Issues

                        const enteredDate = dayJsFormat(
                          parsedDate,
                          'YYYY-MM-DD'
                        );
                        const allowedDisablePastDate = disablePastDate
                          ? dayJsFormat(new Date(disablePastDate), 'YYYY-MM-DD')
                          : null;

                        if (
                          allowedDisablePastDate &&
                          enteredDate < allowedDisablePastDate &&
                          values.value.length !== 0
                        ) {
                          setErrorText('Date out of range');
                          return;
                        }

                        // Add weekend validation check here
                        if (includeWeekends && isWeekend(parsedDate)) {
                          setErrorText('Weekend date not allowed');
                          return;
                        }

                        // Valid date
                        const formattedDate = dayJsFormat(parsedDate, format);
                        setErrorText('');
                        onChange(formattedDate);
                        onDateChange?.(formattedDate);
                      }
                    }}
                  />

                  <PopoverTrigger asChild>
                    <Button
                      disabled={disabled}
                      variant={'outline'}
                      className={cn(
                        'font-normal absolute right-0 translate-y-[-50%] top-[50%] rounded-l-none p-1 lg:p-2 bg-muted',
                        !value && 'text-muted-foreground',
                        (hasError || errorText) && !isFocused
                          ? 'border-danger border'
                          : '',
                        disabled ? 'disabled:bg-[#F0F0F0]' : ''
                      )}
                    >
                      <CalendarIcon className="w-4 h-4" />
                    </Button>
                  </PopoverTrigger>
                </div>
              ) : (
                <PopoverTrigger asChild>
                  <Button
                    variant={'outline'}
                    disabled={disabled}
                    className={cn(
                      'w-full flex justify-start font-normal border-border-Default placeholder:text-[#B3B3B3] focus-visible:border-0',
                      hasError ? 'border-danger border' : '',
                      disabled ? 'disabled:bg-[#F0F0F0]' : '',
                      className
                    )}
                  >
                    {value ? (
                      formatDate(value)
                    ) : (
                      <span className="text-muted-foreground">
                        {placeholder}
                      </span>
                    )}
                  </Button>
                </PopoverTrigger>
              )}

              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  defaultMonth={value ? new Date(value) : new Date()}
                  selected={value ? new Date(value) : undefined}
                  onSelect={(date) => {
                    if (!date) return;
                    if (value && dayJsFormat(date, format) === value) {
                      return;
                    }
                    const formattedDate = dayJsFormat(date, format);
                    onChange(formattedDate);
                    onDateChange?.(date);
                    handleBlur?.(date);
                    setErrorText('');
                    setOpen(false);
                  }}
                  startMonth={disablePastDate ? disablePastDate : startMonth}
                  endMonth={endMonth}
                  disabled={(date) =>
                    (includeWeekends ? isWeekend(date) : false) || disabled
                  }
                />
              </PopoverContent>
            </Popover>
          );
        }}
      />

      {errorText && (
        <p className="text-sm font-normal text-danger">{errorText}</p>
      )}

      {!errorText && isShowError && hasError && (
        <p className="text-sm font-normal text-danger">{errorMessage}</p>
      )}
    </div>
  );
};

export default memo(DatePicker);
