import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { useForm } from 'react-hook-form';
import DatePicker from './index';

vi.mock('@/components/ui/button', () => ({
  Button: ({ children, ...props }: any) => (
    <button {...props}>{children}</button>
  ),
}));
vi.mock('@/components/ui/popover', () => ({
  Popover: ({ children }: any) => <div>{children}</div>,
  PopoverTrigger: ({ children }: any) => <div>{children}</div>,
  PopoverContent: ({ children }: any) => <div>{children}</div>,
}));
vi.mock('@/components/ui/calendar', () => ({
  Calendar: ({ onSelect }: any) => (
    <div>
      <button
        data-testid="calendar-select"
        onClick={() => onSelect(new Date())}
      >
        Select Date
      </button>
    </div>
  ),
}));
vi.mock('@/components/ui/label', () => ({
  Label: ({ children }: any) => <label>{children}</label>,
}));
vi.mock('@/lib/utils', () => ({
  cn: (...args: any[]) => args.filter(Boolean).join(' '),
  dayJsFormat: (date: Date) => date.toISOString(),
  formatDate: (date: Date) => date.toDateString(),
}));

const TestWrapper = ({ children }: any) => {
  const form = useForm();
  return children(form);
};

describe('DatePicker Component', () => {
  it('renders correctly with placeholder text', () => {
    render(
      <TestWrapper>
        {(form: any) => (
          <DatePicker name="testDate" form={form} placeholder="Select a date" />
        )}
      </TestWrapper>
    );

    expect(screen.getByText('Select a date')).toBeInTheDocument();
  });

  it('opens the calendar on button click', () => {
    render(
      <TestWrapper>
        {(form: any) => (
          <DatePicker name="testDate" form={form} placeholder="Select a date" />
        )}
      </TestWrapper>
    );

    const button = screen.getByText('Select a date');
    fireEvent.click(button);

    expect(screen.getByTestId('calendar-select')).toBeInTheDocument();
  });

  it('calls onDateChange when a date is selected', () => {
    const onDateChangeMock = vi.fn();

    render(
      <TestWrapper>
        {(form: any) => (
          <DatePicker
            name="testDate"
            form={form}
            placeholder="Select a date"
            onDateChange={onDateChangeMock}
          />
        )}
      </TestWrapper>
    );

    const button = screen.getByText('Select a date');
    fireEvent.click(button);
  });

  it('displays validation error message when invalid', () => {
    render(
      <TestWrapper>
        {(form: any) => (
          <>
            <DatePicker
              name="testDate"
              form={form}
              placeholder="Select a date"
            />
            <button
              onClick={() => form.trigger('testDate', { shouldFocus: false })}
            >
              Submit
            </button>
          </>
        )}
      </TestWrapper>
    );

    const submitButton = screen.getByText('Submit');
    const isRequired = fireEvent.click(submitButton);
    expect(isRequired);
  });

  it('renders label and description correctly', () => {
    render(
      <TestWrapper>
        {(form: any) => (
          <DatePicker
            name="testDate"
            form={form}
            label="Date Label"
            description="This is a description."
          />
        )}
      </TestWrapper>
    );

    expect(screen.getByText('Date Label')).toBeInTheDocument();
    expect(screen.getByText('This is a description.')).toBeInTheDocument();
  });

  it('disables the button when disabled prop is true', () => {
    render(
      <TestWrapper>
        {(form: any) => (
          <DatePicker name="testDate" form={form} disabled={true} />
        )}
      </TestWrapper>
    );

    const button = screen.getByTestId('calendar-select');
    expect(button);
  });
});
