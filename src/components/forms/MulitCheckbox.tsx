import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { CommandList } from 'cmdk';
import { ChevronsUpDown } from 'lucide-react';
import { useState } from 'react';
import { Controller, FieldValues, Path, UseFormReturn } from 'react-hook-form';
import { Checkbox } from '../ui/checkbox';
import { Label } from '../ui/label';

// Type for the framework object
interface Options {
  value: string;
  label: string;
}

interface MultiCheckboxDropdownProps<T extends FieldValues> {
  placeholder: string;
  optionsList: Options[]; // List of frameworks passed as a prop
  name: Path<T>;
  form: UseFormReturn<T>;
  onChange?: (selected: string[]) => void; // Expecting a string array of selected values
  className?: string;
  label?: string;
  noDataPlaceholder?: string;
  isLoading?: boolean;
  disabled?: boolean;
}

const MultiCheckboxDropdown = <T extends FieldValues>({
  name,
  placeholder,
  optionsList,
  form,
  onChange,
  className,
  label,
  isLoading,
  noDataPlaceholder = 'No record found.',
  disabled,
}: MultiCheckboxDropdownProps<T>) => {
  // Explicitly type the state as an array of strings
  const [open, setOpen] = useState(false);

  return (
    <Controller
      name={name}
      control={form.control}
      render={({ field }) => {
        const isAllSelected = optionsList.length === (field.value?.length || 0);

        // Update onChange when the field value changes
        const handleChange = (updatedSelection: string[]) => {
          field.onChange(updatedSelection); // Update form field value
          if (onChange) onChange(updatedSelection); // Call the passed onChange prop
        };

        return (
          <div>
            {label && (
              <Label
                className={` block text-base  text-[#1E1E1E] font-normal mb-2`}
              >
                {label}
              </Label>
            )}
            <Popover open={open} onOpenChange={setOpen}>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  role="combobox"
                  aria-expanded={open}
                  className={cn(
                    'w-[300px] justify-between relative border-[1px] border-red-600 border-border-Default',
                    className,
                    disabled && 'bg-[#F5F5F5]'
                  )}
                  disabled={disabled}
                >
                  <Controller
                    name={name}
                    control={form.control}
                    render={({ field }) => {
                      const selectedCount = field?.value?.length;
                      return (
                        <>
                          {selectedCount > 0 ? (
                            <> {selectedCount} selected</>
                          ) : (
                            <div className="text-[#B3B3B3] text-left w-full">
                              {placeholder}
                            </div>
                          )}
                        </>
                      );
                    }}
                  />

                  <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-90 absolute right-1 bg-white" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-[300px] p-0  ">
                <Command>
                  <CommandInput placeholder="Search" />
                  <CommandList>
                    <CommandEmpty>{noDataPlaceholder}</CommandEmpty>
                    <CommandGroup className="max-h-60 overflow-y-auto">
                      {/* Loading  */}
                      {isLoading && (
                        <CommandItem key="loading" className="text-center">
                          Loading...
                        </CommandItem>
                      )}
                      {/* Select All Option */}
                      {optionsList?.length > 0 && (
                        <CommandItem
                          key="select-all"
                          onSelect={() => {
                            const updatedSelection = isAllSelected
                              ? [] // Deselect all
                              : optionsList.map((option) => option.value); // Select all
                            handleChange(updatedSelection); // Update and call onChange
                          }}
                          className="cursor-pointer"
                        >
                          <Checkbox
                            checked={isAllSelected}
                            onCheckedChange={() => {
                              const updatedSelection = isAllSelected
                                ? [] // Deselect all
                                : optionsList.map((option) => option.value); // Select all
                              handleChange(updatedSelection); // Update and call onChange
                            }}
                            className="mr-2 border-primary"
                          />
                          Select All
                        </CommandItem>
                      )}
                      {optionsList.map((option) => {
                        const isSelected =
                          field.value?.includes(option.value) || false;
                        //   console.log('field', field);
                        return (
                          <CommandItem
                            key={option.value}
                            onSelect={() => {
                              const updatedSelection = isSelected
                                ? field.value.filter(
                                    (item: string) => item !== option.value
                                  )
                                : [...(field.value || []), option.value];
                              handleChange(updatedSelection); // Update and call onChange
                            }}
                            className="cursor-pointer"
                          >
                            <Checkbox
                              checked={isSelected}
                              onCheckedChange={() => {
                                const updatedSelection = isSelected
                                  ? field.value.filter(
                                      (item: string) => item !== option.value
                                    )
                                  : [...(field.value || []), option.value];
                                handleChange(updatedSelection); // Update and call onChange
                              }}
                              className="mr-2 border-primary"
                            />
                            {option.label}
                          </CommandItem>
                        );
                      })}
                    </CommandGroup>
                  </CommandList>
                </Command>
              </PopoverContent>
            </Popover>
          </div>
        );
      }}
    />
  );
};

export default MultiCheckboxDropdown;
