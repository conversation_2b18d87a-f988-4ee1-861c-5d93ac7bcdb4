import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { cn } from '@/lib/utils';
import { Controller } from 'react-hook-form';

export interface ListType {
  value: string;
  label: string;
  id?: string | number;
  disabled?: boolean;
  isInactive?: boolean;
  isInActiveDisabled?: boolean;
}

interface SelectDropdownTyps {
  name: string;
  value?: string;
  label?: string;
  isClearable?: boolean;
  optionsList: ListType[];
  form: any;
  defaultValue?: string;
  required?: string;
  pattern?: RegExp;
  patternMessage?: string;
  helperText?: any;
  enableSearch?: boolean;
  className?: string;
  optionClassName?: string;
  placeholder?: string;
  searchPlaceholder?: string;
  allowClear?: boolean;
  selectedValue?: string;
  ClearIcon?: boolean;
  hasSelectAll?: boolean;
  isLoading?: boolean;
  disabled?: boolean;
  disableSearch?: boolean;
  emptyOptionMsg?: string;
  isMulti?: boolean;
  isSearchable?: boolean;
  LabelClassName?: string;
  menuPlacement?: 'top' | 'bottom' | 'auto';
  // for hide the required icon like *
  hideRequiredIcon?: boolean;
  parentClassName?: string;
  onSelectChange?: (selected: string) => void;
  onChange?: (selected: string) => void;
  handleOnChange?: (selected: string | any[] | null) => void;
  dropdownHeight?: string;
  keepSelectedInList?: boolean;
  closeOnSelect?: boolean;
  textWrap?: string;
}

const ShadcnSelect = (props: SelectDropdownTyps) => {
  const {
    name,
    label,
    optionsList,
    form,
    defaultValue,
    required,
    pattern,
    patternMessage,
    placeholder,
    allowClear,
    optionClassName,
    emptyOptionMsg,
    disabled,
    onChange,
    isLoading,
    parentClassName,
  } = props;

  const hasError = !!form.formState.errors[name];

  return (
    <Controller
      name={name}
      control={form.control}
      rules={{
        required: required ? 'Required' : '',
        pattern: pattern
          ? { value: pattern, message: patternMessage || '' }
          : undefined,
      }}
      render={({ field: { value, onChange: fieldControllerOnChange } }) => (
        <div className={cn('w-full flex flex-col gap-2', parentClassName)}>
          {label && (
            <Label className={`block text-base  text-[#1E1E1E] font-normal`}>
              {label}{' '}
              {label && required && required?.length > 1 && (
                <span className="text-red-600">*</span>
              )}
            </Label>
          )}
          <Select
            disabled={disabled}
            onValueChange={(selectedValue) => {
              if (selectedValue === '_clear') {
                // Set value to empty when clear is selected
                fieldControllerOnChange('');
                if (onChange) {
                  onChange('');
                }
              } else {
                // Otherwise use the standard onChange behavior
                fieldControllerOnChange(selectedValue);
                if (onChange) {
                  onChange(selectedValue);
                }
              }
            }}
            value={value}
          >
            <SelectTrigger
              defaultValue={defaultValue}
              className={cn(
                'w-full truncate',
                hasError ? 'border-danger border-[1px]' : ''
              )}
            >
              <SelectValue placeholder={placeholder} />
            </SelectTrigger>
            <SelectContent className={cn(optionClassName)}>
              <SelectGroup>
                {allowClear && (
                  <SelectItem
                    value="_clear"
                    key={`${placeholder}-clear-option`}
                    className="text-gray-500"
                  >
                    {placeholder}
                  </SelectItem>
                )}
                {isLoading ? (
                  <Label
                    className={cn(
                      optionClassName,
                      'flex justify-center items-center p-1'
                    )}
                  >
                    Loading...
                  </Label>
                ) : !optionsList || optionsList?.length === 0 ? (
                  <Label
                    className={cn(
                      optionClassName,
                      'flex justify-center items-center'
                    )}
                  >
                    {emptyOptionMsg ?? 'Nothing to select'}
                  </Label>
                ) : (
                  optionsList?.map((item, index) => (
                    <SelectItem
                      disabled={item?.isInActiveDisabled}
                      value={item?.value}
                      key={`${item.value}-${index}`}
                    >
                      {item?.label}
                    </SelectItem>
                  ))
                )}
              </SelectGroup>
            </SelectContent>
          </Select>
          {hasError && (
            <p className="text-sm font-normal text-danger">
              {form.formState.errors[name]?.message?.toString()}
            </p>
          )}
        </div>
      )}
    />
  );
};

export default ShadcnSelect;
