import { render, screen, fireEvent } from '@testing-library/react';
import { useForm } from 'react-hook-form';
import InputField from './index';
import { describe, expect, test, vi } from 'vitest';

describe('InputField Component', () => {
  const TestComponent = (props: any) => {
    const form = useForm();
    return <InputField form={form} {...props} />;
  };

  test('renders input field with label', () => {
    render(<TestComponent name="test" label="Test Label" />);
    expect(screen.getByLabelText('Test Label')).toBeInTheDocument();
  });

  test('displays error message when validation fails', async () => {
    render(
      <TestComponent
        name="test"
        label="Test Label"
        validation={{ required: 'This field is required' }}
      />
    );

    const input = screen.getByLabelText('Test Label') as HTMLInputElement;
    const isValidate = fireEvent.blur(input);

    expect(isValidate);
  });

  test('calls onChange when input value changes', () => {
    const handleChange = vi.fn();
    render(
      <TestComponent name="test" label="Test Label" onChange={handleChange} />
    );

    const input = screen.getByLabelText('Test Label') as HTMLInputElement;
    fireEvent.change(input, { target: { value: 'New Value' } });

    expect(handleChange).toHaveBeenCalled();
  });

  test('handles maxLength property', () => {
    render(<TestComponent name="test" label="Test Label" maxLength={5} />);

    const input = screen.getByLabelText('Test Label') as HTMLInputElement;
    fireEvent.change(input, { target: { value: '123456' } });

    expect(input.value).toBe('123456');
  });

  test('handles number type with negative value restriction', () => {
    render(<TestComponent name="test" type="number" label="Test Label" />);

    const input = screen.getByLabelText('Test Label') as HTMLInputElement;
    fireEvent.change(input, { target: { value: '-123' } });

    expect(input.value).toBe('-123');
  });

  test('renders prefix when provided', () => {
    render(<TestComponent name="test" label="Test Label" prefix="$" />);
    expect(screen.getByText('$')).toBeInTheDocument();
  });
});
