import { cn, convertToFloat } from '@/lib/utils';
import get from 'lodash/get';
import { ChangeEvent, HTMLInputAutoCompleteAttribute } from 'react';
import {
  Controller,
  FieldValues,
  Path,
  RegisterOptions,
  UseFormReturn,
} from 'react-hook-form';
import { Input } from '../../ui/input';
import { Label } from '../../ui/label';
import Labels from '../Label';

interface InputFieldProps<T extends FieldValues> {
  name: Path<T>;
  form: UseFormReturn<T>;
  type?: 'text' | 'email' | 'password' | 'number' | 'url' | 'time';
  label?: string;
  validation?: RegisterOptions<T, Path<T>>;
  placeholder?: string;
  readonly?: boolean;
  onChange?: (event: ChangeEvent<HTMLInputElement>) => void;
  className?: string;
  description?: string;
  disabled?: boolean;
  extraLabel?: React.ReactNode;
  isShowError?: boolean;
  maxLength?: number;
  toFloat?: boolean;
  prefix?: string;
  labelClassName?: string;
  onBlur?: () => void;
  errorDisplayMode?: boolean;
  pClassName?: string;
  autoComplete?: HTMLInputAutoCompleteAttribute;
  callOnChangeOnBlur?: boolean;
}

const InputField = <T extends FieldValues>({
  name,
  type = 'text',
  label,
  validation,
  placeholder,
  form,
  disabled = false,
  onChange,
  className,
  description,
  extraLabel,
  isShowError = true,
  maxLength,
  toFloat,
  prefix,
  labelClassName,
  onBlur,
  errorDisplayMode,
  pClassName,
  autoComplete = 'off',
  callOnChangeOnBlur = true,
}: InputFieldProps<T>) => {
  const isRequired = validation?.required ? 'Required' : undefined;
  const requiredMessage =
    validation && typeof validation.required === 'string'
      ? validation.required
      : isRequired;

  const validationOptions = validation
    ? {
        ...validation,
        required: requiredMessage,
      }
    : {};

  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    const { value } = event.target;
    if (type === 'number') {
      if (value && value?.startsWith('-')) {
        event.target.value = value.slice(1);
        return;
      }
    }
    // If maxLength is provided and type is text, trim the value
    if (maxLength && value.length > maxLength) {
      event.target.value = value.slice(0, maxLength);
    }
    form.register(name, validationOptions).onChange(event);
    if (onChange) {
      onChange(event);
    }
  };

  const handleOnBlur = (event: React.FocusEvent<HTMLInputElement>) => {
    const { value } = event.target;
    if (toFloat) {
      event.target.value = value ? convertToFloat({ value }) : value;
    }
    onBlur && onBlur();

    form.register(name, validationOptions).onChange(event);
    if (onChange && callOnChangeOnBlur) {
      onChange(event);
    }
  };

  // const hasError = !!form.formState.errors[name];
  const hasError = !!get(form.formState.errors, name);
  const errorMessage = get(form.formState.errors, name)?.message?.toString();

  return (
    <div className={cn('relative flex flex-col gap-2', pClassName)}>
      {(extraLabel || label) && (
        <div className="flex flex-row gap-x-2">
          {label && (
            <Labels
              htmlFor={name}
              label={label}
              validation={validation}
              className={labelClassName}
            />
          )}
          {extraLabel && extraLabel}
        </div>
      )}
      {description && (
        <Label className="text-text-tertiary text-base font-normal">
          {description}
        </Label>
      )}

      <div
        className={
          hasError && errorDisplayMode
            ? 'bg-[#C00F0C] rounded-md relative'
            : ' flex flex-col gap-2 '
        }
      >
        <div className="relative">
          <Controller
            control={form.control}
            name={name}
            rules={validationOptions}
            render={({ field }) => (
              <Input
                className={cn(
                  'border-border-Default placeholder:text-[#B3B3B3]',
                  errorDisplayMode && hasError
                    ? 'focus-visible:ring-offset-[0.5px] focus-visible:ring-[0.5px] border-danger border-[1px]'
                    : hasError
                      ? 'border-danger border-[1px]'
                      : '',
                  className
                )}
                value={field.value ?? ''}
                // {...field}
                id={name}
                type={type}
                disabled={disabled}
                placeholder={placeholder}
                autoComplete={autoComplete}
                maxLength={maxLength ?? undefined}
                onChange={(e) => {
                  field.onChange(e);
                  handleChange(e);
                }}
                onBlur={(e) => {
                  field.onBlur();
                  handleOnBlur(e);
                }}
                style={{
                  paddingLeft: prefix ? '1.5rem' : '',
                }}
              />
            )}
          />
          {prefix && (
            <span className="absolute left-2  top-[8px]  text-[#1E1E1E]">
              {prefix}
            </span>
          )}
        </div>
        {errorDisplayMode && hasError ? (
          <p className="text-sm font-normal text-danger bg-[#C00F0C] text-white pt-1 pb-1 pl-3 pr-3 rounded-md">
            {errorMessage}
          </p>
        ) : isShowError && hasError ? (
          <p className={cn('text-sm font-normal text-danger')}>
            {errorMessage}
          </p>
        ) : null}
      </div>
    </div>
  );
};

export default InputField;
