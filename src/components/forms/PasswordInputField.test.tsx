import { render, screen, fireEvent } from '@testing-library/react';
import { FormProvider, useForm } from 'react-hook-form';
import PasswordInputField from './PasswordInputField';
import { describe, expect, it } from 'vitest';

const TestComponent = () => {
  const methods = useForm();
  return (
    <FormProvider {...methods}>
      <form>
        <PasswordInputField
          name="password"
          form={methods}
          label="Password"
          description="Enter your password"
        />
      </form>
    </FormProvider>
  );
};

describe('PasswordInputField Component', () => {
  it('should render the label and description', () => {
    render(<TestComponent />);

    const labelElement = screen.getByText('Password');
    const descriptionElement = screen.getByText('Enter your password');

    expect(labelElement).toBeInTheDocument();
    expect(descriptionElement).toBeInTheDocument();
  });

  it('should toggle password visibility when the eye icon is clicked', () => {
    render(<TestComponent />);

    const eyeIcon = screen.getByRole('button');
    const input = screen.getByText('Enter your password');
    fireEvent.click(eyeIcon);
    expect(input);
  });

  it('should trigger onChange event handler', () => {
    render(<TestComponent />);

    const input = screen.getByText('Enter your password');
    expect(input);
  });

  it('should display error message if validation fails', () => {
    render(<TestComponent />);

    const input = screen.getByText('Enter your password');
    const errorMessage = fireEvent.blur(input);
    expect(errorMessage);
  });

  it('should apply custom className to the input field', () => {
    render(<TestComponent />);

    const input = screen.getByText('Enter your password');
    expect(input);
  });

  it('should handle maxLength properly', () => {
    render(<TestComponent />);

    const input = screen.getByText('Enter your password');
    expect(input);
  });
});
