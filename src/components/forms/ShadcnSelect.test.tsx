import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { useForm } from 'react-hook-form';
import ShadcnSelect, { ListType } from './ShadcnSelect';

describe('ShadcnSelect Component', () => {
  const mockOnChange = vi.fn();

  const optionsList: ListType[] = [
    { value: 'option1', label: 'Option 1' },
    { value: 'option2', label: 'Option 2' },
    { value: 'option3', label: 'Option 3' },
  ];

  const TestForm = ({ isLoading = false }: { isLoading?: boolean }) => {
    const {
      control,
      handleSubmit,
      formState: { errors },
    } = useForm({
      defaultValues: { selectField: '' },
    });

    const onSubmit = () => {};

    return (
      <form onSubmit={handleSubmit(onSubmit)}>
        <ShadcnSelect
          name="selectField"
          form={{ control, formState: { errors } }}
          label="Select Option"
          optionsList={isLoading ? [] : optionsList}
          onChange={mockOnChange}
          required="This field is required"
          isLoading={isLoading}
        />
        <button type="submit">Submit</button>
      </form>
    );
  };

  it('renders correctly with options', async () => {
    render(<TestForm />);

    expect(screen.getByText('Select Option')).toBeInTheDocument();
  });

  it('calls onChange function when an option is selected', async () => {
    render(<TestForm />);

    fireEvent.click(screen.getByText('Select Option'));
    fireEvent.click(screen.getByText('Option 2'));

    expect(mockOnChange);
  });
});
