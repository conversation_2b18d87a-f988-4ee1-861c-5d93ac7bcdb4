import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { defaultDataTablePageSize } from '@/constants/common-constants';
import { cn, generateLabelValuePairs } from '@/lib/utils';
import { useGetItemLookupDropDownQuery } from '@/redux/features/items/item.api';
import { CommandList } from 'cmdk';
import { debounce } from 'lodash';
import { ChevronsUpDown } from 'lucide-react';
import React, { useEffect, useRef, useState } from 'react';
import { Controller, FieldValues, Path, UseFormReturn } from 'react-hook-form';
import { Button } from '../ui/button';

interface Options {
  label: string;
  value: string;
}

interface ServerSideSelectProps<T extends FieldValues> {
  placeholder?: string;
  name: Path<T>;
  form: UseFormReturn<T>;
  onChange?: (selected: any) => void;
  url: string;
  className?: string;
  contentClassName?: string;
}

const ServerSideSelect = <T extends FieldValues>({
  placeholder,
  name,
  form,
  onChange,
  url,
  className,
  contentClassName,
}: ServerSideSelectProps<T>) => {
  const [open, setOpen] = useState(false);
  const [search, setSearch] = useState('');
  const [page, setPage] = useState(1);
  const [options, setOptions] = useState<Options[]>([]);
  const commandListRef = useRef<HTMLDivElement>(null);

  const { data, isLoading, isFetching } = useGetItemLookupDropDownQuery(
    {
      url: url,
      body: {
        pageSize: defaultDataTablePageSize,
        pageNumber: page,
        sortBy: 'itemId',
        filters: [{ field: 'itemId', value: search, operator: 'Contains' }],
        sortAscending: true,
      },
    },
    {
      skip: search === '' && page === 1,
    }
  );

  // Create debounced search function
  const debouncedSetSearch = debounce((newSearch: string) => {
    setSearch?.(newSearch);

    if (newSearch === '') {
      setOptions([]);
    }
  }, 500);

  // Cleanup debounce on unmount
  useEffect(() => {
    return () => {
      debouncedSetSearch.cancel();
    };
  }, [debouncedSetSearch]);

  useEffect(() => {
    if (data?.data) {
      if (page === 1) {
        const newOptions = generateLabelValuePairs({
          data: data?.data,
          labelKey: 'itemId',
          valueKey: 'id',
        });
        setOptions(newOptions); // Replace options if it's the first page
      } else {
        const newOptions = generateLabelValuePairs({
          data: data?.data,
          labelKey: 'itemId',
          valueKey: 'id',
        });
        setOptions((prev) => [...prev, ...newOptions]); // Append options on pagination
      }
    }
  }, [data, page]);

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const element = e.currentTarget;
    if (
      element.scrollHeight - element.scrollTop === element.clientHeight &&
      !isFetching &&
      options.length < (data?.pagination?.totalCount ?? 0)
    ) {
      setPage((prev) => prev + 1);
    }
  };

  return (
    <Controller
      name={name}
      control={form.control}
      render={({ field }) => (
        <Popover
          open={open}
          onOpenChange={(isOpen) => {
            setOpen(isOpen);
            if (!isOpen) {
              setOptions([]);
            }
          }}
        >
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              role="combobox"
              aria-expanded={open}
              className={cn('w-[300px] justify-between', className)}
            >
              {field.value?.label || placeholder}
              <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className={cn('w-[300px] p-0', contentClassName)}>
            <Command>
              <CommandInput
                onValueChange={(value) => {
                  debouncedSetSearch(value);
                }}
                placeholder={'Search...'}
              />
              <CommandList
                ref={commandListRef}
                onScroll={handleScroll}
                className="max-h-[300px] overflow-auto"
              >
                <CommandEmpty>
                  {isLoading || isFetching ? 'Loading...' : 'No results'}
                </CommandEmpty>
                <CommandGroup>
                  {isLoading && page === 1 ? (
                    <div className="text-center p-2">Loading...</div>
                  ) : (
                    options.map((option, index) => (
                      <CommandItem
                        key={index}
                        value={option.value}
                        onSelect={() => {
                          const valueToPass = {
                            label: option?.label,
                            value: option?.value,
                          };
                          field.onChange(valueToPass);
                          onChange?.(valueToPass);
                          setOpen(false);
                          setOptions([]);
                        }}
                      >
                        {option.label}
                      </CommandItem>
                    ))
                  )}
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>
      )}
    />
  );
};

export default ServerSideSelect;
