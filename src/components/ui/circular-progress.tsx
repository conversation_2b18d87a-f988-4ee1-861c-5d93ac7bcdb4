import * as React from 'react';
import * as ProgressPrimitive from '@radix-ui/react-progress';

import { cn } from '@/lib/utils';
import UploadFileProgressIcon from '../../assets/icons/UploadFileProgressIcon';

interface CircleProgressProps
  extends React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root> {
  progressColor?: string; // Hex code for the progress color
  backgroundColor?: string; // Hex code for the background color
}

const CircleProgress = React.forwardRef<
  React.ElementRef<typeof ProgressPrimitive.Root>,
  CircleProgressProps
>(
  (
    {
      className,
      value,
      progressColor = '#04BCC2',
      backgroundColor = '#D4F1F2',
      ...props
    },
    ref
  ) => (
    <ProgressPrimitive.Root
      ref={ref}
      className={cn(
        `relative h-[70px] w-[70px] overflow-hidden rounded-full bg-primary/20 flex justify-center items-center`,
        className
      )}
      {...props}
      style={{
        background: `radial-gradient(closest-side, white 65%, transparent 70% 100%), conic-gradient(${progressColor} ${value || 0}%, ${backgroundColor} 0)`,
      }}
    >
      <UploadFileProgressIcon />
    </ProgressPrimitive.Root>
  )
);

export { CircleProgress };
