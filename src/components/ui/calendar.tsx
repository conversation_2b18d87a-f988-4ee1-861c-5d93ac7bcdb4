import { ChevronLeftIcon, ChevronRightIcon } from 'lucide-react';
import * as React from 'react';
import { DayPicker, DayProps, DropdownProps } from 'react-day-picker';

import { buttonVariants } from '@/components/ui/button';
import { formatDateWithTimezone } from '@/lib/formatDateWithTimezone';
import { cn, getStorageValue } from '@/lib/utils';
import { AvailabilityEntry } from '@/types/common.types';
import dayjs from 'dayjs';
import { ScrollArea } from './scroll-area';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from './select';

export type CalendarProps = React.ComponentProps<typeof DayPicker> & {
  slotData?: AvailabilityEntry[];
  dateKey?: string;
  labelKey?: string;
  isCustomDay?: boolean;
  onDaySelect?: (date: Date) => void;
  selectedValue?: Date;
};
function Calendar({
  className,
  classNames,
  showOutsideDays = true,
  slotData,
  dateKey = 'date',
  labelKey = 'label',
  isCustomDay = false,
  onDaySelect,
  selectedValue,
  ...props
}: CalendarProps) {
  const tz = getStorageValue('timeZoneOffset') || '';
  const today = formatDateWithTimezone({ tz });

  // custome day components
  const CustomDayCell = ({ modifiers, ...buttonProps }: DayProps) => {
    const date = buttonProps?.day?.date;
    const disabled = modifiers?.disabled;
    const isSelected = dayjs(selectedValue)?.isSame(date, 'day');
    const match = slotData?.find((item: AvailabilityEntry) =>
      dayjs(item[dateKey] as Date)?.isSame(date, 'day')
    );
    const label = (match && match[labelKey]?.toString()) ?? '';
    const {
      className = '',
      labelClassName = '',
      selectedClassName = '',
    } = match ?? {};

    return (
      <button
        onClick={() => !disabled && onDaySelect?.(date)}
        type="button"
        className={cn(
          'text-sm flex flex-col items-center justify-center',
          disabled ? '' : 'hover:bg-muted hover:rounded-md',
          modifiers?.outside && 'text-muted-foreground opacity-50',
          buttonProps?.className,
          isSelected &&
            cn(
              'bg-text-brand-violet-Default hover:bg-text-brand-violet-Default rounded-md text-white over:text-white',
              selectedClassName
            ),
          'h-full w-full',
          modifiers?.today &&
            'border border-text-brand-violet-Default rounded-md',
          className
        )}
      >
        <span className={cn('text-base pt-0.5', isSelected && 'text-white')}>
          {date?.getDate()}
        </span>
        <span
          className={cn(
            modifiers?.today ? '' : 'mt-0.5',
            'text-base text-text-brand-violet-Default font-medium leading-none h-7',
            isSelected && 'text-white',
            labelClassName
          )}
        >
          {label}
        </span>
      </button>
    );
  };

  return (
    <DayPicker
      showOutsideDays={showOutsideDays}
      className={cn('p-3', className)}
      today={dayjs(today)?.toDate()}
      {...props}
      classNames={{
        month: 'space-y-4',
        months: 'flex flex-col',
        month_caption: 'flex justify-center pt-1 relative items-center',
        month_grid: 'w-full border-collapse space-y-1',
        caption_label: 'text-sm font-medium',
        nav: 'flex items-center justify-between absolute inset-x-4',
        dropdowns: 'flex gap-2 mt-[-3px]',
        button_previous: cn(
          buttonVariants({ variant: 'outline' }),
          'h-10 w-7 bg-transparent p-0 opacity-50 hover:opacity-100 z-10'
        ),
        button_next: cn(
          buttonVariants({ variant: 'outline' }),
          'h-10 w-7 bg-transparent p-0 opacity-50 hover:opacity-100 z-10'
        ),
        weeks: 'w-full border-collapse space-y-full',
        weekdays: 'flex',
        weekday:
          'text-muted-foreground rounded-md w-full font-normal text-[0.8rem]',
        week: 'flex w-full mt-2',
        day_button:
          'h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20',
        day: cn(
          buttonVariants({ variant: 'ghost' }),
          'h-9 w-9 p-0 font-normal aria-selected:opacity-100'
        ),
        range_end: 'day-range-end',
        selected:
          'bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground',
        today: 'bg-accent text-accent-foreground',
        outside:
          'day-outside text-muted-foreground opacity-50 aria-selected:bg-accent/50 aria-selected:text-muted-foreground aria-selected:opacity-30',
        disabled: 'text-muted-foreground opacity-50 cursor-default',
        range_middle:
          'aria-selected:bg-accent aria-selected:text-accent-foreground',
        hidden: 'invisible',
        ...classNames,
      }}
      captionLayout="dropdown"
      components={{
        Dropdown: (props: DropdownProps) => {
          const options: { value: number; label: string }[] =
            props?.options || [];
          const selected = options?.find(
            (option) => option?.value === props?.value
          );
          const handleChange = (value: string) => {
            const changeEvent = {
              target: { value },
            } as React.ChangeEvent<HTMLSelectElement>;
            props?.onChange?.(changeEvent);
          };
          return (
            <Select
              value={props?.value?.toString() ?? ''}
              onValueChange={handleChange}
            >
              <SelectTrigger className="focus:ring-0">
                <SelectValue>{selected?.label}</SelectValue>
              </SelectTrigger>
              <SelectContent position="popper">
                <ScrollArea className="h-80">
                  {options?.map((option, index: number) => (
                    <SelectItem
                      key={`${option?.value}-${index}`}
                      value={option?.value?.toString() ?? ''}
                    >
                      {option?.label}
                    </SelectItem>
                  ))}
                </ScrollArea>
              </SelectContent>
            </Select>
          );
        },
        Chevron: ({ orientation }) => {
          const Icon =
            orientation === 'left' ? ChevronLeftIcon : ChevronRightIcon;
          return <Icon className="h-4 w-4 z-10" />;
        },
        ...(isCustomDay && { Day: CustomDayCell }),
      }}
    />
  );
}
Calendar.displayName = 'Calendar';

export { Calendar };
