import { toast, Toaster } from 'sonner';
import {
  CheckIcon,
  AlertCircleIcon,
  InfoIcon,
  AlertTriangleIcon,
  Loader2Icon,
} from 'lucide-react';
import { cn } from '@/lib/utils';

type ToastType = 'success' | 'error' | 'info' | 'warning' | 'loading';
type ToastPosition =
  | 'top-right'
  | 'top-left'
  | 'bottom-right'
  | 'bottom-left'
  | 'top-center'
  | 'bottom-center';

interface ToastOptions {
  position?: ToastPosition;
  duration?: number;
  description?: string;
}

interface PromiseOptions<T> extends ToastOptions {
  loading: string;
  success?: string | ((data: T) => string);
  error?: string | ((error: any) => string);
}

const defaultOptions: ToastOptions = {
  position: 'top-right',
};

export const ToastContainer = () => {
  return (
    <Toaster
      position={defaultOptions.position}
      richColors
      closeButton
      toastOptions={{
        style: { margin: 0, padding: 0 },
      }}
    />
  );
};

export const UseToast = () => {
  const showToast = (
    type: ToastType,
    title: string | undefined,
    options?: ToastOptions
  ) => {
    const mergedOptions = { ...defaultOptions, ...options };
    const toastConfig = {
      duration: mergedOptions.duration,
      description: mergedOptions.description,
    };

    const commonIconStyle =
      'w-5 h-5 mr-2.5 bg-white p-0.5 rounded-full font-semibold';
    const commonStyleText = 'text-white font-semibold';

    const iconMap: Record<ToastType, React.ReactNode> = {
      success: <CheckIcon className={cn(commonIconStyle, 'text-green-500')} />,
      error: (
        <AlertCircleIcon className={cn(commonIconStyle, 'text-red-500')} />
      ),
      warning: (
        <AlertTriangleIcon className={cn(commonIconStyle, 'text-yellow-500')} />
      ),
      info: <InfoIcon className={cn(commonIconStyle, 'text-blue-500')} />,
      loading: (
        <Loader2Icon
          className={cn(commonIconStyle, 'text-blue-500 animate-spin')}
        />
      ),
    };

    const toastStyles = {
      success: `bg-green-500 ${commonStyleText}`,
      error: `bg-red-500 ${commonStyleText}`,
      warning: `bg-yellow-500 ${commonStyleText}`,
      info: `bg-blue-500 ${commonStyleText}`,
      loading: `bg-blue-500 ${commonStyleText}`,
    };

    return toast[type](
      <span
        className={`flex items-center w-full h-full ${toastStyles[type]} p-3 rounded-md`}
      >
        <span className="mr-2">{iconMap[type]}</span>
        <span className="text-sm">{title}</span>
      </span>,
      toastConfig
    );
  };

  // const promise = <T,>(promise: Promise<T>, options: PromiseOptions<T>) => {
  //   return toast.promise(promise, {
  //     loading: (
  //       <span className="flex items-center w-full h-full bg-blue-500 text-white font-semibold p-3 rounded-md">
  //         <Loader2Icon className="w-5 h-5 mr-2.5 bg-white p-0.5 rounded-full animate-spin text-blue-500" />
  //         <span className="text-sm">{options.loading}</span>
  //       </span>
  //     ),
  //     success: (data) => (
  //       <span className="flex items-center w-full h-full bg-green-500 text-white font-semibold p-3 rounded-md">
  //         <CheckIcon className="w-5 h-5 mr-2.5 bg-white p-0.5 rounded-full text-green-500" />
  //         <span className="text-sm">
  //           {typeof options.success === 'function'
  //             ? options.success(data)
  //             : options.success}
  //         </span>
  //       </span>
  //     ),
  //     error: (error) => (
  //       <span className="flex items-center w-full h-full bg-red-500 text-white font-semibold p-3 rounded-md">
  //         <AlertCircleIcon className="w-5 h-5 mr-2.5 bg-white p-0.5 rounded-full text-red-500" />
  //         <span className="text-sm">
  //           {typeof options.error === 'function'
  //             ? options.error(error)
  //             : options.error}
  //         </span>
  //       </span>
  //     ),
  //     duration: options.duration,
  //     position: options.position || defaultOptions.position,
  //     closeButton: false,
  //   });
  // };

  const promise = <T,>(promise: Promise<T>, options: PromiseOptions<T>) => {
    const toastConfig: Parameters<typeof toast.promise<T>>[1] = {
      loading: (
        <span className="flex items-center w-full h-full bg-blue-500 text-white font-semibold p-3 rounded-md">
          <Loader2Icon className="w-5 h-5 mr-2.5 bg-white p-0.5 rounded-full animate-spin text-blue-500" />
          <span className="text-sm">{options.loading}</span>
        </span>
      ),
      duration: options.duration,
      position: options.position || defaultOptions.position,
      closeButton: false,
    };

    // Conditionally add success if provided
    if (options.success) {
      toastConfig.success = (data) => {
        const successText =
          typeof options.success === 'function'
            ? options.success(data)
            : options.success;

        return (
          <span className="flex items-center w-full h-full bg-green-500 text-white font-semibold p-3 rounded-md">
            <CheckIcon className="w-5 h-5 mr-2.5 bg-white p-0.5 rounded-full text-green-500" />
            <span className="text-sm">{successText}</span>
          </span>
        );
      };
    }

    // Conditionally add error if provided
    if (options.error) {
      toastConfig.error = (error) => {
        const errorText =
          typeof options.error === 'function'
            ? options.error(error)
            : options.error;

        return (
          <span className="flex items-center w-full h-full bg-red-500 text-white font-semibold p-3 rounded-md">
            <AlertCircleIcon className="w-5 h-5 mr-2.5 bg-white p-0.5 rounded-full text-red-500" />
            <span className="text-sm">{errorText}</span>
          </span>
        );
      };
    }

    return toast.promise(promise, toastConfig);
  };

  return {
    success: (title: string, options?: ToastOptions) =>
      showToast('success', title, options),
    error: (title: string, options?: ToastOptions) =>
      showToast('error', title, options),
    warning: (title: string, options?: ToastOptions) =>
      showToast('warning', title, options),
    info: (title: string, options?: ToastOptions) =>
      showToast('info', title, options),
    loading: (title: string, options?: ToastOptions) =>
      showToast('loading', title, options),
    promise,
  };
};
