import { X } from 'lucide-react';
import { memo, useCallback, useEffect, useMemo } from 'react';
import { useForm, useFormContext } from 'react-hook-form';

import AppButton from '@/components/common/app-button';
import CustomDialog from '@/components/common/dialog';
import InputField from '@/components/forms/input-field';
import Text<PERSON>reaField from '@/components/forms/text-area';
import { cn, DEFAULT_FORMAT, formatDate, getQueryParam } from '@/lib/utils';
import { useDeletePurchaseOrderMutation } from '@/redux/features/purchase-order/purchaseOrder.api';

import {
  DeletePurchaseOrderType,
  POInformationTypes,
  PurchaseOrderListingTypes,
} from '@/types/purchase-order.types';

interface DeleteOrderProps {
  open: boolean;
  onOpenChange: (success?: boolean) => void;
  POData?: PurchaseOrderListingTypes | null;
  isDeleted: boolean;
}

const DeletePurchaseOrder = ({
  open,
  onOpenChange,
  POData,
  isDeleted,
}: DeleteOrderProps) => {
  const poForm = useFormContext<POInformationTypes>();
  const queryParamPOId = getQueryParam('id') as string;
  const poId = queryParamPOId ?? POData?.id;
  const poDetails = queryParamPOId ? poForm.watch() : POData;

  const defaultValues = useMemo<Partial<DeletePurchaseOrderType>>(
    () => ({
      cancelBy: poDetails?.cancelBy ?? '',
      cancelDate: formatDate(
        poDetails?.cancelDate ?? new Date(),
        DEFAULT_FORMAT
      ),
      cancelReason: poDetails?.cancelReason ?? '',
    }),
    [poDetails?.cancelBy, poDetails?.cancelDate, poDetails?.cancelReason]
  );

  const form = useForm<DeletePurchaseOrderType>({
    defaultValues,
    mode: 'onChange',
  });

  const { handleSubmit, reset } = form;

  useEffect(() => {
    if (defaultValues) {
      reset(defaultValues);
    }
  }, [defaultValues, reset]);

  const [deletePO, { isLoading: deleteLoading }] =
    useDeletePurchaseOrderMutation();

  const onSubmit = useCallback(
    async (formData: DeletePurchaseOrderType) => {
      try {
        const payload = {
          id: Number(poId),
          reasonOfDeletion: formData?.cancelReason,
          deletedBy: formData?.cancelBy,
        };
        const { data }: any = await deletePO({ data: payload });

        if (data?.statusCode === 200) {
          onOpenChange(data?.success);
        }
      } catch (error) {}
    },
    [deletePO, onOpenChange, poId]
  );

  const handleCancel = useCallback(() => {
    onOpenChange();
    reset();
  }, [onOpenChange, reset]);

  return (
    <CustomDialog
      open={open}
      onOpenChange={handleCancel}
      description=""
      className={cn('max-w-[90%] w-[80%] md:w-[50%] 2xl:w-[35%]')}
      contentClassName="max-h-[410px] overflow-y-auto px-6 pb-0"
      title={'Deletion Info'}
    >
      <div className="w-full">
        <div className="border rounded-lg p-4 my-3">
          <div className="grid grid-cols-2 gap-4">
            <InputField
              name="cancelBy"
              form={form}
              label="Deleted By"
              placeholder="Deleted By"
              disabled={isDeleted}
              pClassName="min-w-fit"
              maxLength={50}
            />
            <InputField
              name="cancelDate"
              form={form}
              label="Date"
              placeholder="Date"
              disabled
            />
          </div>

          <TextAreaField
            name="cancelReason"
            form={form}
            label="Reason for Deletion"
            placeholder="Reason for Deletion"
            rows={10}
            disabled={isDeleted}
            maxLength={200}
            pClassName="pt-4"
            className="max-h-[100px]"
          />
        </div>

        {!isDeleted && (
          <div className="flex items-center justify-end sticky bottom-0 bg-white gap-4 py-3 mt-2">
            <AppButton
              label="Delete"
              icon={X}
              className="w-28"
              onClick={handleSubmit(onSubmit)}
              isLoading={deleteLoading}
            />
            <AppButton
              label="Cancel"
              variant="neutral"
              onClick={handleCancel}
              className="w-28"
            />
          </div>
        )}
      </div>
    </CustomDialog>
  );
};

export default memo(DeletePurchaseOrder);
