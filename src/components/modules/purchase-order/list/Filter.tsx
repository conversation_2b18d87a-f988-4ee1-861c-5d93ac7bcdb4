import AppButton from '@/components/common/app-button';
import Input<PERSON>ield from '@/components/forms/input-field';
import SelectDropDown from '@/components/forms/select-dropdown';
import { Separator } from '@/components/ui/separator';
import {
  FilterList,
  OperatorListFor1,
  OperatorListFor2,
  RangeList,
  SearchByList,
} from '@/constants/purchaseOrder-constant';
import { toCapitalize } from '@/lib/utils';
import {
  clearAllPurchaseOrderFilters,
  setPurchaseOrderFilter,
  updatePurchaseOrderFormValues,
} from '@/redux/features/purchase-order/purchaseOrderSlice';
import { RootState } from '@/redux/store';
import { memo, useEffect } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';

// Define the interface for filter data
interface FilterFormValues {
  [key: string]: string | null | number;
}

const defaultValues: FilterFormValues = {
  range: '',
  type: '',
  itemDesc: '',
  operator: '',
  searchBy: '',
  value: '',
};

interface FilterProps {
  setIsFilterOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const Filter: React.FC<FilterProps> = ({ setIsFilterOpen }) => {
  const dispatch = useDispatch();
  const formValuesFromRedux = useSelector(
    (state: RootState) => state.purchaseOrder.formValues
  );

  const form = useForm<FilterFormValues>({
    defaultValues: formValuesFromRedux,
    mode: 'onChange',
  });

  const isSelectedSearchBy = form.watch('searchBy');

  // Watch form changes and update Redux state
  useEffect(() => {
    const subscription = form.watch((values) => {
      dispatch(updatePurchaseOrderFormValues(values));
    });

    const defaultValues = {
      operator: 'CONTAINS',
    };

    Object.entries(defaultValues).forEach(([key, defaultValue]) => {
      if (!form.getValues(key)) {
        form.setValue(key, defaultValue);
      }
    });

    return () => subscription.unsubscribe();
  }, [form, dispatch, isSelectedSearchBy]);

  const handleClear = () => {
    form.reset(defaultValues); // Reset form to default values
    dispatch(clearAllPurchaseOrderFilters()); // Clear all filters in Redux
    setIsFilterOpen(false); // Close the filter form
  };

  // Optional helper to convert field names to user-friendly labels
  const toLabel = (field: string): string => {
    switch (field) {
      case 'range':
        return 'Range';
      case 'type':
        return 'Type';
      case 'itemDesc':
        return 'Item Desc';
      case 'searchBy':
        return 'Search By';
      case 'JOBNO':
        return 'Job #';
      case 'VENDOR':
        return 'Vendor';
      case 'PONO':
        return 'PO #';
      case 'TOTAL':
        return 'Total';
      case 'value':
        return 'Value';
      default:
        return field;
    }
  };

  const onSubmit: SubmitHandler<FilterFormValues> = (data) => {
    const filterFields = ['range', 'type'] as const;

    const newFilterData: any[] = [];

    if (data.itemDesc) {
      newFilterData.push({
        label: 'Item Desc',
        name: 'itemDesc',
        value: data.itemDesc,
        tagValue: data.itemDesc,
        operator: 'LESSTHAN',
      });
    }

    // Handle searchBy + value field
    if (data.searchBy && data.value) {
      newFilterData.push({
        label: toLabel(data.searchBy?.toString() ?? ''),
        name: data.searchBy,
        value: data.value,
        tagValue: data.value,
        operator: data.operator,
      });
    }

    // Handle other fields like range, type
    filterFields.forEach((field) => {
      const value = data[field];
      if (value) {
        newFilterData.push({
          label: toLabel(field),
          value,
          name: field,
          tagValue:
            value === 'current-future'
              ? 'Current / Future'
              : toCapitalize(value?.toString() ?? ''),
          operator: data.operator,
        });
      }
    });

    dispatch(setPurchaseOrderFilter(newFilterData));
    setIsFilterOpen(false);
  };

  const handleSearchByChange = (value: string) => {
    if (value === 'VENDOR' || value === 'JOBNO') {
      form.setValue('operator', 'CONTAINS');
    } else {
      form.setValue('operator', 'EQUALS');
    }
  };

  // Track if form values are different from the default values
  const isFormModified =
    JSON.stringify(form.watch()) !== JSON.stringify(defaultValues);

  return (
    <div className="p-3">
      <div className="text-normal py-2  font-semibold">Filters</div>
      <Separator
        orientation="horizontal"
        className="h-[1px] bg-border-Default mb-2"
      />
      <div className="grid col-span-1 grid-cols-1 gap-3">
        <SelectDropDown
          name="type"
          label="Purchase Order Type"
          placeholder="Select Purchase Order Type"
          optionsList={FilterList}
          form={form}
          allowClear={false}
        />
        <SelectDropDown
          name="range"
          label="Range"
          placeholder="Select Range"
          optionsList={RangeList}
          form={form}
          allowClear={false}
        />
        <InputField
          form={form}
          name="itemDesc"
          label="Item Desc"
          placeholder="Enter Value"
        />
        <SelectDropDown
          name="searchBy"
          label="Search By"
          placeholder="Select Search By"
          onChange={handleSearchByChange}
          optionsList={SearchByList ?? []}
          form={form}
          allowClear={false}
        />
        <SelectDropDown
          name="operator"
          label="Operator"
          placeholder="Select Operator"
          optionsList={
            isSelectedSearchBy === 'VENDOR' || isSelectedSearchBy === 'JOBNO'
              ? OperatorListFor1
              : isSelectedSearchBy === 'TOTAL' || isSelectedSearchBy === 'PONO'
                ? OperatorListFor2
                : OperatorListFor1
          }
          form={form}
          allowClear={false}
        />
        <InputField
          form={form}
          name="value"
          label="Value"
          placeholder="Enter Value"
          disabled={!form.watch('searchBy')}
        />
      </div>
      <div className="w-full h-full flex justify-between gap-3 mt-3 sticky bottom-0 bg-white z-30">
        <AppButton
          className="w-full"
          onClick={form.handleSubmit(onSubmit)}
          label="Apply"
          disabled={!isFormModified}
        />
        <AppButton
          className="w-full"
          label="Clear"
          variant="neutral"
          onClick={handleClear}
          disabled={!isFormModified}
        />
      </div>
    </div>
  );
};

export default memo(Filter);
