import EditIcon from '@/assets/icons/EditIcon';
import ActionColumnMenu from '@/components/common/data-tables/ActionColumn';
import { CircleAlert, EyeIcon, TrashIcon } from 'lucide-react';
import { memo } from 'react';

interface ActionCellProps {
  row: any;
  toggleDelete: (data?: any, success?: boolean) => void;
  handleCompletionInfo: (status: string, id: number, data?: any) => void;
}

const ActionCell = ({
  row,
  toggleDelete,
  handleCompletionInfo,
}: ActionCellProps) => {
  const isDeleted = row.original?.isDeleted;

  return (
    <ActionColumnMenu
      customEdit={
        <button
          onClick={() =>
            handleCompletionInfo(
              row.original?.status,
              row.original?.id,
              row.original
            )
          }
          className="flex items-center justify-center gap-2 border border-text-tertiary rounded-md px-3 py-1"
        >
          {isDeleted ? <EyeIcon className="w-5 h-5" /> : <EditIcon />}
          <span className="w-24">{isDeleted ? 'View' : 'Edit'} details</span>
        </button>
      }
      dropdownMenuList={[
        isDeleted
          ? {
              label: 'Deletion Info',
              onClick: () => toggleDelete(row.original),
              icon: <CircleAlert className="h-5 w-5" />,
              className: 'text-base',
            }
          : {
              label: 'Delete',
              onClick: () => toggleDelete(row.original),
              icon: <TrashIcon className="h-5 w-5" />,
              className: 'text-base text-text-danger',
            },
      ]}
    />
  );
};

export default memo(ActionCell);
