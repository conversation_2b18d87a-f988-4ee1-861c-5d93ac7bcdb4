import { Row } from '@tanstack/react-table';
import { Plus<PERSON><PERSON>, Printer } from 'lucide-react';
import React, { useCallback, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';

import AppButton from '@/components/common/app-button';
import AppDataTable from '@/components/common/app-data-table';
import AppSpinner from '@/components/common/app-spinner';
import StatusBadge from '@/components/common/app-status-badge';
import ItemColumnOrdering from '@/components/modules/items/item-details/column-ordering';
import CompletionInfo from '../CompletionInfo';
import DeletePurchaseOrder from '../DeletePurchaseOrder';
import ActionCell from './ActionCell';
import Filter from './Filter';

import { PURCHASE_API_ROUTES } from '@/constants/api-constants';
import { POStatusColors } from '@/constants/purchaseOrder-constant';
import { ROUTES } from '@/constants/routes-constants';
import { convertToFloat } from '@/lib/utils';
import { useAddNewItemMutation } from '@/redux/features/common-api/common.api';
import { useGetListQuery } from '@/redux/features/list/category/list.api';
import { clearPurchaseOrderFilter } from '@/redux/features/purchase-order/purchaseOrderSlice';
import { RootState } from '@/redux/store';
import {
  FilterItemDTO,
  PurchaseOrderListingTypes,
  RawFilterItem,
} from '@/types/purchase-order.types';

interface DeleteState {
  open: boolean;
  data: PurchaseOrderListingTypes | null;
}

interface CompletionState {
  open: boolean;
  id: number | null;
  data: PurchaseOrderListingTypes | null;
}

const PurchaseOrders = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  // Redux selectors
  const filters = useSelector(
    (state: RootState) => state.purchaseOrder.filters
  );

  // Local state
  const [openColumnOrdering, setOpenColumnOrdering] = useState<boolean>(false);
  const [isFilterOpen, setFilterOpen] = useState<boolean>(false);
  const [refreshList, setRefreshList] = useState<boolean>(false);
  const [deleteState, setDeleteState] = useState<DeleteState>({
    open: false,
    data: null,
  });
  const [completionState, setCompletionState] = useState<CompletionState>({
    open: false,
    id: null,
    data: null,
  });

  const [addColumnOrder, { isLoading: ordering }] = useAddNewItemMutation();
  const {
    data: columnData,
    refetch,
    isFetching,
  } = useGetListQuery({ url: PURCHASE_API_ROUTES.GET_COLUMN });

  // Memoized rawColumns to stabilize dependencies
  const rawColumns = useMemo(() => columnData?.data ?? [], [columnData?.data]);

  const toggleDelete = useCallback(
    (data?: PurchaseOrderListingTypes | null, success = false) => {
      if (success) {
        setRefreshList(true);
        setTimeout(() => setRefreshList(false), 500);
      }
      setDeleteState((state) => ({ open: !state.open, data: data || null }));
    },
    []
  );

  const handleClearFilter = useCallback(
    (key: string) => {
      dispatch(clearPurchaseOrderFilter(key));
      setFilterOpen(false);
    },
    [dispatch]
  );

  const handleColumnOrder = useCallback(
    async (formValues: Record<string, boolean>) => {
      if (!columnData) return;
      const updated = columnData.data.map((col: any) => ({
        ...col,
        enabled: !!formValues[col.accessorKey],
      }));
      await addColumnOrder({
        url: PURCHASE_API_ROUTES.UPDATE_COLUMN,
        data: updated,
      }).unwrap();
      setOpenColumnOrdering(false);
      setRefreshList(true);
      setTimeout(() => setRefreshList(false), 500);
      refetch();
    },
    [addColumnOrder, columnData, refetch]
  );

  const handleCompletion = useCallback(
    (status: string, id: number, data?: PurchaseOrderListingTypes) => {
      if (status === 'PAID') {
        setCompletionState({ open: true, id, data: data || null });
      } else {
        navigate(`${ROUTES.EDIT_PURCHASE_ORDER}?id=${id}&tab=information`);
      }
    },
    [navigate]
  );

  const CustomToolbar = (
    <AppButton
      icon={PlusIcon}
      iconClassName="w-5 h-5"
      label="New Purchase Order"
      onClick={() =>
        navigate(`${ROUTES.ADD_NEW_PURCHASE_ORDER}?tab=information`)
      }
    />
  );

  // Columns
  const tableColumns = useMemo(() => {
    return columnData?.data;
  }, [columnData?.data]);

  // Map components
  const componentMap: Record<string, (value: any) => React.ReactNode> = useMemo(
    () => ({
      StatusBadge: (value: any) => <StatusBadge status={value} />,
      LabelDollar: (value: any) => convertToFloat({ value, prefix: '$' }),
    }),
    []
  );

  const dropdownItems = useMemo(() => {
    return [
      {
        label: 'Print Purchase Order List',
        onClick: () => {},
        icon: <Printer />,
      },
    ];
  }, []);

  const columns = useMemo(() => {
    if (!tableColumns?.length) return [];

    const base = tableColumns
      .filter((column: any) => column.enabled)
      .map((col: any) => ({
        accessorKey: col.accessorKey,
        header: col.header,
        size: col.size || 200,
        enableSorting: col.enableSorting,
        className:
          col.accessorKey === 'statusLabel'
            ? (row: Row<any>) => {
                const v = row.original?.status;
                return row.original?.isDeleted
                  ? 'bg-red-300'
                  : POStatusColors[v];
              }
            : undefined,
        cell: ({ row }: any) => {
          const compFn = componentMap[col.component as string];
          const val = row.original[col.accessorKey];
          return compFn ? compFn(val) : val;
        },
      }));

    return [
      {
        accessorKey: 'ordering',
        header: () => (
          <ItemColumnOrdering
            isOpen={openColumnOrdering}
            handleOrderingColumn={handleColumnOrder}
            tableColumns={rawColumns}
            setOpenColumnOrdering={setOpenColumnOrdering}
          />
        ),
        size: 50,
      },
      ...base,
      {
        id: 'action',
        header: 'Actions',
        size: 110,
        cell: ({ row }: any) => (
          <ActionCell
            row={row}
            toggleDelete={toggleDelete}
            handleCompletionInfo={handleCompletion}
          />
        ),
      },
    ];
  }, [
    tableColumns,
    componentMap,
    openColumnOrdering,
    handleColumnOrder,
    rawColumns,
    toggleDelete,
    handleCompletion,
  ]);

  const updatedFilter: FilterItemDTO[] = useMemo(() => {
    return (filters as RawFilterItem[]).map(
      (item): FilterItemDTO => ({
        field: item.name ?? null,
        label: item?.label,
        operator: item.operator,
        name: item.name,
        tagValue: item.tagValue ?? null,
        value: item.value,
      })
    );
  }, [filters]);

  return (
    <div className="flex flex-col p-4 gap-6">
      <AppDataTable
        url={PURCHASE_API_ROUTES.ALL}
        columns={columns}
        enableSearch={true}
        searchKey="PONO"
        searchOperator="Equals"
        heading="Purchase Orders"
        enableFilter
        filter={updatedFilter}
        handleClearFilter={handleClearFilter}
        filterClassName="w-[473px]"
        filterContent={<Filter setIsFilterOpen={setFilterOpen} />}
        setIsFilterOpen={setFilterOpen}
        isFilterOpen={isFilterOpen}
        customToolBar={CustomToolbar}
        enablePagination={true}
        refreshList={refreshList}
        tableClassName="max-h-[580px] overflow-auto"
        dropdownMenus={dropdownItems}
        dropdownContentClassName="p-4 flex flex-col gap-2 w-full"
      />

      <DeletePurchaseOrder
        open={deleteState.open}
        onOpenChange={(success) => toggleDelete(null, success)}
        POData={deleteState.data}
        isDeleted={!!deleteState.data?.isDeleted}
      />

      <CompletionInfo
        open={completionState.open}
        onOpenChange={() => {
          if (completionState.id) {
            navigate(
              `${ROUTES.EDIT_PURCHASE_ORDER}?id=${completionState.id}&tab=information`
            );
          }
          setCompletionState({ open: false, id: null, data: null });
        }}
        POData={completionState.data}
      />
      <AppSpinner overlay isLoading={ordering || isFetching} />
    </div>
  );
};

export default PurchaseOrders;
