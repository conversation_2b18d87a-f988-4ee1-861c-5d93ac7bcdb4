import DataTable from '@/components/common/data-tables';
import { ColumnDef } from '@tanstack/react-table';
import { useEffect, useRef } from 'react';

interface ItemListTableProps {
  fields: any[];
  columns: ColumnDef<any>[];
  isLoading?: boolean;
}

const ItemListTable = ({ fields, columns, isLoading }: ItemListTableProps) => {
  const onScrollRef = useRef<HTMLTableElement>(null);

  useEffect(() => {
    if (onScrollRef.current) {
      // Scroll to the bottom of the table when the fields change
      onScrollRef.current.scrollTop = onScrollRef.current.scrollHeight;
    }
  }, [fields]); // Trigger scroll when fields change

  return (
    <DataTable
      onScrollRef={onScrollRef}
      data={fields || []}
      columns={columns}
      enablePagination={false}
      tableClassName="max-h-[580px] overflow-auto"
      enableRowSelection={false}
      isLoading={isLoading}
      highlightKey="isOverbooked"
      highlightClassName="bg-red-200 hover:bg-red-200"
      selectedHighlightClassName="data-[state=selected]:bg-red-100"
    />
  );
};

export default ItemListTable;
