import NumberInputField from '@/components/forms/number-input-field';
import SelectWidget from '@/components/forms/select';
import {
  QUANTITY_VALIDATION_RULE,
  TEXT_VALIDATION_RULE,
} from '@/constants/validation-constants';
import {
  POInformationFormDataTypes,
  ReceivedItemsList,
} from '@/types/purchase-order.types';
import { useMemo } from 'react';
import { useFormContext, UseFormReturn } from 'react-hook-form';

interface UseItemListColumnsProps {
  form: UseFormReturn<ReceivedItemsList>;
  optionLoading: boolean;
  storeLocationList: any;
}

export const useReceviedItemColumns = ({
  form,
  optionLoading,
  storeLocationList,
}: UseItemListColumnsProps) => {
  // Memoized columns configuration to prevent unnecessary re-renders

  const informationForm = useFormContext<POInformationFormDataTypes>();
  const isDeleted = informationForm.watch('isDeleted');

  return useMemo(
    () => [
      {
        accessorKey: 'itemIdLabel',
        header: 'Item ID',
        size: 120,
        enableSorting: true,
      },
      {
        accessorKey: 'quantity',
        header: 'Quantity',
        size: 80,
        enableSorting: true,
      },
      {
        accessorKey: 'description',
        header: 'Item Description',
        size: 250,
        enableSorting: true,
      },
      {
        accessorKey: 'location',
        header: 'Location',
        maxSize: 220,
        enableSorting: true,
        cell: ({ row }: any) => {
          return (
            <div className="p-1">
              <SelectWidget
                placeholder="Location"
                className="w-40"
                name={`items.${row.index}.storeLocationId`}
                form={form}
                optionsList={storeLocationList}
                isClearable={false}
                validation={TEXT_VALIDATION_RULE}
                isLoading={optionLoading}
                disabled={isDeleted}
              />
            </div>
          );
        },
      },
      {
        accessorKey: 'quantityReceived',
        header: 'Qty Rcvd',
        maxSize: 150,
        enableSorting: true,
        cell: ({ row }: any) => {
          return (
            <NumberInputField
              form={form}
              placeholder="Quantity"
              name={`items.${row?.index}.quantityReceived`}
              className="w-24 h-8 mt-1"
              maxLength={5}
              decimalScale={0}
              validation={QUANTITY_VALIDATION_RULE}
              maxValue={Number(form.watch(`items.${row?.index}.quantity`))}
              disabled={isDeleted}
            />
          );
        },
      },
      {
        accessorKey: 'totalQuantityReceived',
        header: 'Total Qty Rcvd',
        maxSize: 180,
        enableSorting: true,
      },
    ],
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [form, optionLoading]
  );
};
