import AppButton from '@/components/common/app-button';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import { STORE_OPTIONS_API_ROUTES } from '@/constants/api-constants';
import useOptionList from '@/hooks/useOptionList';
import { getQueryParam } from '@/lib/utils';
import { useGetCompanyQuery } from '@/redux/features/company/company.api';
import {
  useGetPOItemListQuery,
  useReceiveAllMutation,
} from '@/redux/features/purchase-order/purchaseOrder.api';
import { useGetUserDefaultStoreQuery } from '@/redux/features/store/store.api';
import {
  POInformationFormDataTypes,
  POItemDetailsTypes,
  ReceivedItemsList,
} from '@/types/purchase-order.types';
import { WineIcon } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';
import { useFieldArray, useFormContext, UseFormReturn } from 'react-hook-form';
import ItemListTable from '../ItemListTable';
import { useReceviedItemColumns } from './useReceviedItemColumns';

interface ReceivedItemType {
  form: UseFormReturn<ReceivedItemsList>;
  onReceivedDefaultValuesLoaded?: (defaults: ReceivedItemsList) => void;
}

const mapItemsToFormValues = (
  items: POItemDetailsTypes[],
  defaultStoreLocation: number
): ReceivedItemsList => ({
  items:
    items?.map((item) => ({
      id: item.id,
      itemId: item.itemId,
      itemIdLabel: item.itemIdLabel,
      description: item.description || '',
      quantity: item.quantity || '',
      storeLocationId: defaultStoreLocation || null,
      quantityReceived: item.quantityReceived ?? '',
      totalQuantityReceived: item.totalQuantityReceived,
    })) || [],
});

const ReceivedItems = ({
  form,
  onReceivedDefaultValuesLoaded,
}: ReceivedItemType) => {
  // State and Hooks

  const id = getQueryParam('id') as string;
  const informationForm = useFormContext<POInformationFormDataTypes>();
  const { data, isLoading } = useGetPOItemListQuery(id);
  const [recieveAll, { isLoading: recievedItemsLoading }] =
    useReceiveAllMutation();
  const { data: companyData } = useGetCompanyQuery();
  const { data: storeLocations } = useGetUserDefaultStoreQuery();

  const [isModalOpen, setIsModalOpen] = useState(false);

  const isDeleted = informationForm.watch('isDeleted');

  const { options: storeLocationList, optionLoading } = useOptionList({
    url: STORE_OPTIONS_API_ROUTES.ALL,
    labelKey: 'locationName',
    valueKey: 'id',
    sortBy: 'locationName',
    extraKey: 'isDefault',
  });

  const defaultStoreLocation = useMemo(() => {
    return storeLocationList?.find((opt: any) => opt.extraKey);
  }, [storeLocationList]);

  const { fields } = useFieldArray({
    control: form.control,
    name: 'items',
  });

  const defaultValues = useMemo(
    () =>
      mapItemsToFormValues(
        data?.data as POItemDetailsTypes[],
        !companyData?.data?.useMultiloc
          ? defaultStoreLocation?.value
          : storeLocations?.data?.id
      ),
    [
      companyData?.data?.useMultiloc,
      data?.data,
      defaultStoreLocation?.value,
      storeLocations?.data?.id,
    ]
  );

  // Columns configuration for data table
  const columns = useReceviedItemColumns({
    form,
    optionLoading,
    storeLocationList,
  });

  useEffect(() => {
    if (defaultValues) {
      onReceivedDefaultValuesLoaded?.(defaultValues as ReceivedItemsList);
      form.reset(defaultValues);
    }
  }, [defaultValues, form, onReceivedDefaultValuesLoaded]);

  // Handler for "All Items Received"
  const handleAllItemsReceived = () => {
    setIsModalOpen(true);
  };

  // Confirm action
  const handleConfirmSubmit = async () => {
    try {
      const response = await recieveAll(id).unwrap();
      if (response?.success) {
        setIsModalOpen(false);
      }
    } catch (error) {}
  };

  return (
    <div className="flex flex-col gap-2">
      <div className="flex justify-end">
        <AppButton
          disabled={isDeleted}
          label="All Items Received"
          onClick={handleAllItemsReceived}
          icon={WineIcon}
          className="w-fit bg-brand-teal-Default hover:bg-brand-teal-Default/85"
        />
      </div>

      <ItemListTable fields={fields} columns={columns} isLoading={isLoading} />
      <AppConfirmationModal
        title="Confirmation"
        description={
          <div>Do you want to make all items as fully received ?</div>
        }
        open={isModalOpen}
        handleCancel={() => setIsModalOpen(false)}
        handleSubmit={handleConfirmSubmit}
        disabled={recievedItemsLoading}
        isLoading={recievedItemsLoading}
      />
    </div>
  );
};

export default ReceivedItems;
