import CheveronLeft from '@/assets/icons/CheveronLeft';
import isEqual from 'lodash/isEqual';
import { CircleAlert, CopyIcon, SaveIcon, TrashIcon } from 'lucide-react';
import { useCallback, useMemo, useState } from 'react';
import { Form<PERSON>rovider, SubmitHandler, useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';

import AppButton from '@/components/common/app-button';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import AppTabsVertical from '@/components/common/app-tabs-vertical';
import ActionColumnMenu from '@/components/common/data-tables/ActionColumn';
import IconButton from '@/components/common/icon-button';
import TooltipWidget from '@/components/common/tooltip-widget';
import DeletePurchaseOrder from '../DeletePurchaseOrder';

import {
  useCopyPOMutation,
  useSavePOItemDetailsMutation,
  useSavePurchaseOrderMutation,
  useSaveRecievedItemDetailsMutation,
} from '@/redux/features/purchase-order/purchaseOrder.api';

import {
  purchaseOrderTabList,
  TAB_NAMES,
} from '@/constants/purchaseOrder-constant';
import { ROUTES } from '@/constants/routes-constants';
import { getModifiedItems } from '@/lib/getModifiedItems';
import { getQueryParam, updateQueryParam } from '@/lib/utils';

import {
  transformItemPayload,
  transformPurchaseOrderPayload,
  transformReceivedPayload,
} from '@/lib/poUtils';
import {
  ActiveTabType,
  POInformationFormDataTypes,
  POItemDetailsList,
  ReceivedItemsList,
} from '@/types/purchase-order.types';

const AddEditPurchaseOrder = () => {
  const id = getQueryParam('id') as string;
  const navigate = useNavigate();
  const tabName = getQueryParam('tab') || TAB_NAMES.INFORMATION;
  const [activeTab, setActiveTab] = useState(tabName);
  const [copyModalState, setCopyModalState] = useState({
    open: false,
    copyItems: false,
  });
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);

  const [copyPO, { isLoading: copyPOLoading }] = useCopyPOMutation();
  const [addUpdateItem, { isLoading: updateLoading }] =
    useSavePurchaseOrderMutation();
  const [saveItemDetails, { isLoading: itemDetailsLoading }] =
    useSavePOItemDetailsMutation();
  const [saveRecievedItems, { isLoading: recievedItemLoading }] =
    useSaveRecievedItemDetailsMutation();

  const informatioForm = useForm<POInformationFormDataTypes>();
  const itemDetailsForm = useForm<POItemDetailsList>();
  const receivedForm = useForm<ReceivedItemsList>();

  const [itemDefaultValues, setitemDefaultValues] =
    useState<POItemDetailsList | null>(null);
  const onDefaultValuesLoaded = useCallback((defaults: POItemDetailsList) => {
    setitemDefaultValues(defaults);
  }, []);

  const [receivedItemDefaultValues, setReceivedDefaultValues] =
    useState<ReceivedItemsList | null>(null);
  const onReceivedDefaultValuesLoaded = useCallback(
    (defaults: ReceivedItemsList) => {
      setReceivedDefaultValues(defaults);
    },
    []
  );
  const currentItems = itemDetailsForm?.watch('items')?.map((item) => {
    return {
      ...item,
      quantity: Number(item?.quantity),
      price: Number(item?.price),
    };
  });

  const currentRecievedItems = receivedForm?.watch('items')?.map((item) => {
    return {
      ...item,
      quantityReceived: item?.quantityReceived ?? '',
    };
  });

  const isItemModified = useMemo(() => {
    return isEqual(currentItems, itemDefaultValues?.items);
  }, [currentItems, itemDefaultValues?.items]);

  const isReceviedItemModified = useMemo(() => {
    return isEqual(currentRecievedItems, receivedItemDefaultValues?.items);
  }, [currentRecievedItems, receivedItemDefaultValues?.items]);

  const isDeleted = informatioForm?.watch('isDeleted');
  const isItemTab = activeTab === TAB_NAMES.ITEM_DETAILS;
  const isReceviedTab = activeTab === TAB_NAMES.RECEIVED_ITEMS;
  const isSaveDisabled = isItemTab
    ? isItemModified
    : isReceviedTab
      ? isReceviedItemModified
      : false;

  const onSubmit: SubmitHandler<POInformationFormDataTypes> = useCallback(
    async (data) => {
      const payload = transformPurchaseOrderPayload(data, id);
      const res = await addUpdateItem({ body: payload });
      const newId = res?.data?.data?.id;
      if (!id && newId) updateQueryParam(newId);
    },
    [addUpdateItem, id]
  );

  const onSubmitItems: SubmitHandler<POItemDetailsList> = async (data) => {
    const modified = getModifiedItems(
      data.items,
      itemDefaultValues?.items || []
    );
    const payload = transformItemPayload(modified);
    if (payload.length) await saveItemDetails({ id, body: payload });
  };

  const onSubmitReceived: SubmitHandler<ReceivedItemsList> = async (data) => {
    const modified = getModifiedItems(
      data.items,
      receivedItemDefaultValues?.items || []
    );
    const payload = transformReceivedPayload(modified);
    if (payload.length) await saveRecievedItems({ id, body: payload });
  };

  // Helper function to trigger the correct submit handler based on tab
  const triggerSubmitHandler = () => {
    switch (activeTab) {
      case TAB_NAMES.INFORMATION:
        informatioForm.handleSubmit(onSubmit)();
        break;
      case TAB_NAMES.ITEM_DETAILS:
        itemDetailsForm.handleSubmit(onSubmitItems)();
        break;
      case TAB_NAMES.RECEIVED_ITEMS:
        receivedForm.handleSubmit(onSubmitReceived)();
        break;
      default:
        break;
    }
  };

  const handleTabChange = useCallback((value: ActiveTabType) => {
    setActiveTab(value);
    updateQueryParam(value, 'tab');
  }, []);

  const navigateToItem = useCallback(() => {
    navigate(ROUTES.PURCHASE_ORDERS);
  }, [navigate]);

  const toggleModal = useCallback(
    (success?: boolean) => {
      setOpenDeleteDialog((prev) => !prev);
      if (success) {
        navigate(ROUTES.PURCHASE_ORDERS);
      }
    },
    [navigate]
  );
  const handleCopy = useCallback(async () => {
    if (id)
      try {
        const { data } = await copyPO({
          id,
          copyItems: copyModalState.copyItems,
        }).unwrap();
        const copyPOId = data?.id;
        copyPOId && updateQueryParam(copyPOId);
      } catch (err) {
      } finally {
        setCopyModalState({ open: false, copyItems: false });
      }
  }, [copyPO, id, copyModalState.copyItems]);

  const toggleCopy = useCallback((copyItems: boolean = false) => {
    setCopyModalState((prev) => ({
      open: !prev.open,
      copyItems,
    }));
  }, []);

  // Order Drop down menu
  const PODropdownMenu = useMemo(
    () => [
      {
        label: 'Copy PO',
        icon: <CopyIcon className="h-5 w-5" />,
        subMenu: [
          {
            label: 'PO Info Only',
            onClick: () => toggleCopy(),
            icon: <CopyIcon className="h-5 w-5" />,
          },
          {
            label: 'PO Info & Items',
            onClick: () => toggleCopy(true),
            icon: <CopyIcon className="h-5 w-5" />,
          },
        ],
      },

      isDeleted
        ? {
            label: 'Deletion Info',
            onClick: () => toggleModal(),
            icon: <CircleAlert className="h-5 w-5" />,
          }
        : {
            label: 'Delete PO',
            onClick: () => toggleModal(),
            icon: <TrashIcon className="h-5 w-5" />,
            className: 'text-base text-text-danger',
          },
    ],
    [isDeleted, toggleCopy, toggleModal]
  );

  return (
    <>
      <FormProvider {...informatioForm}>
        <div className="flex flex-col gap-6 p-4">
          <div className="flex justify-between items-center px-4 sticky top-16 z-[10] bg-white py-2">
            <div className="flex gap-x-4 items-center">
              <IconButton onClick={navigateToItem}>
                <CheveronLeft />
              </IconButton>
              <h1
                className="text-2xl text-text-tertiary font-semibold hover:cursor-pointer"
                onClick={navigateToItem}
              >
                PO
              </h1>

              <div className="flex items-center gap-3">
                <span className="text-2xl font-semibold text-text-tertiary">
                  {' / '}
                </span>
                <p className="text-2xl capitalize font-semibold">
                  {id ? id : 'New Purchase Order'}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              {(isItemTab || isReceviedTab) && isSaveDisabled ? (
                <TooltipWidget content="Please add a new item or update an existing item">
                  <div>
                    <AppButton
                      label="Save Detail"
                      icon={SaveIcon}
                      onClick={triggerSubmitHandler}
                      iconClassName="w-4 h-4"
                      isLoading={
                        updateLoading ||
                        itemDetailsLoading ||
                        recievedItemLoading
                      }
                      disabled={isSaveDisabled || isDeleted}
                    />
                  </div>
                </TooltipWidget>
              ) : (
                <AppButton
                  label="Save Detail"
                  icon={SaveIcon}
                  onClick={triggerSubmitHandler}
                  iconClassName="w-4 h-4"
                  disabled={isDeleted}
                  isLoading={
                    updateLoading || itemDetailsLoading || recievedItemLoading
                  }
                />
              )}
              <AppButton
                label="Cancel"
                onClick={navigateToItem}
                variant="neutral"
              />
              {activeTab === TAB_NAMES.INFORMATION && (
                <ActionColumnMenu
                  triggerClassName="border h-10"
                  dropdownMenuList={PODropdownMenu}
                  contentClassName="p-4 flex flex-col gap-2 w-full"
                  subClassName="p-2 mr-5"
                  disabled={!id}
                />
              )}
            </div>
          </div>
          <AppTabsVertical
            tabs={purchaseOrderTabList({
              itemDetailsForm,
              receivedForm,
              onDefaultValuesLoaded,
              onReceivedDefaultValuesLoaded,
            })}
            activeTab={activeTab}
            onTabChange={handleTabChange}
            showTabMenu={!!id}
            className="px-4 pb-3"
          />
        </div>
        <DeletePurchaseOrder
          open={openDeleteDialog}
          onOpenChange={(success) => toggleModal(success)}
          isDeleted={isDeleted}
        />
      </FormProvider>
      <AppConfirmationModal
        title="Confirmation - Copy PO info"
        description={
          <div>
            Are you sure you want to create a copy of this Purchase Order?
          </div>
        }
        open={copyModalState.open}
        onOpenChange={() => toggleCopy(copyModalState.copyItems)}
        handleCancel={() => toggleCopy(copyModalState.copyItems)}
        handleSubmit={handleCopy}
        isLoading={copyPOLoading}
      />
    </>
  );
};

export default AddEditPurchaseOrder;
