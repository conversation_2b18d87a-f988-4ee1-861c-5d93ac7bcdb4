import { use<PERSON><PERSON>back, useEffect, useMemo } from 'react';
import { <PERSON>mit<PERSON><PERSON><PERSON>, useForm } from 'react-hook-form';

import CustomDialog from '@/components/common/dialog';
import FormActionButtons from '@/components/common/FormActionButtons';
import Input<PERSON>ield from '@/components/forms/input-field';
import PhoneInputWidget from '@/components/forms/phone-input-mask';
import SelectDropDown from '@/components/forms/select-dropdown';
import ZipCodeInput from '@/components/forms/zipcode-input';

import {
  useGetCountryListQuery,
  useGetStateByCountryQuery,
} from '@/redux/features/country/country.api';
import { useAddUpdateVendorInfoMutation } from '@/redux/features/vendors-api/vendors.api';

import { cn, generateLabelValuePairs } from '@/lib/utils';
import { VENDORS_API_ROUTES } from '@/constants/api-constants';
import { statusList } from '@/constants/common-constants';
import {
  EMAIL_VALIDATION_RULE,
  TEXT_VALIDATION_RULE,
} from '@/constants/validation-constants';
import { CountryType } from '@/types/customer.types';
import { BusinessInfoTypes, VendorListTypes } from '@/types/vendor.types';

interface NewVendorProps {
  handleOk?: (value: VendorListTypes) => void;
  open: boolean;
  onOpenChange: () => void;
}

const defaultFormValues: Partial<BusinessInfoTypes> = {
  isactive: 'true',
  country: 1,
};

const NewVendor = ({ handleOk, open, onOpenChange }: NewVendorProps) => {
  // Form setup
  const form = useForm<BusinessInfoTypes>();
  const { handleSubmit, reset, watch } = form;
  const countryId = watch('country');

  // Fetch countries and states
  const { data: statesData, isFetching: stateIsLoading } =
    useGetStateByCountryQuery(
      {
        countryId: Number(countryId),
      },
      {
        skip: !countryId,
      }
    );
  const { data: countryData = [] } = useGetCountryListQuery();

  // Mutation
  const [addVendorInfo, { isLoading: isSubmitting }] =
    useAddUpdateVendorInfoMutation();

  // Generate dropdown options
  const countryList = useMemo(
    () =>
      generateLabelValuePairs({
        data: countryData as CountryType[],
        labelKey: 'name',
        valueKey: 'country_id',
      }),
    [countryData]
  );

  // states list
  const stateList = generateLabelValuePairs({
    data: statesData,
    labelKey: 'code',
    valueKey: 'state_id',
  });

  // Reset form with default values on mount
  useEffect(() => {
    reset(defaultFormValues);
  }, [reset]);

  const handleCancel = useCallback(() => {
    reset();
    onOpenChange();
  }, [reset, onOpenChange]);

  const preparePayload = (formData: BusinessInfoTypes) => ({
    ...formData,
    id: null,
    isactive: Boolean(formData?.isactive),
  });

  const onSubmit: SubmitHandler<BusinessInfoTypes> = async (formData) => {
    const payload = preparePayload(formData);
    const response = await addVendorInfo({
      url: VENDORS_API_ROUTES.ADD,
      data: payload,
      method: 'POST',
    });

    if (response?.data?.success) {
      handleOk?.(response.data.data);
      onOpenChange();
    }
  };

  return (
    <CustomDialog
      open={open}
      onOpenChange={onOpenChange}
      title="New Vendor"
      className={cn('min-w-[80%] md:min-w-[80%] 2xl:min-w-[50%]')}
      contentClassName="h-[490px] 2xl:h-[570px] pb-0 px-2"
    >
      <div className="relative flex flex-col h-full">
        {/* Content */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4 mb-20">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <InputField
              name="vendorName"
              form={form}
              label="Vendor"
              placeholder="Vendor Name"
              validation={TEXT_VALIDATION_RULE}
              maxLength={64}
            />
            <SelectDropDown
              form={form}
              name="isactive"
              label="Status"
              placeholder="Select Status"
              optionsList={statusList}
              allowClear={false}
            />
            <InputField
              name="address1"
              form={form}
              label="Address Line 1"
              placeholder="Address Line 1"
              maxLength={64}
            />
            <InputField
              name="address2"
              form={form}
              label="Address Line 2"
              placeholder="Address Line 2"
              maxLength={64}
            />
            <InputField
              name="city"
              form={form}
              label="City"
              placeholder="City"
              maxLength={32}
            />
            <SelectDropDown
              name="state"
              form={form}
              label="State"
              placeholder="Select State"
              optionsList={stateList}
              isLoading={stateIsLoading}
              validation={TEXT_VALIDATION_RULE}
            />
            <SelectDropDown
              form={form}
              name="country"
              label="Country"
              optionsList={countryList}
              placeholder="Select Country"
              allowClear={false}
            />
            <ZipCodeInput
              name="zipCode"
              form={form}
              label="Zip Code"
              isUSA={Number(countryId) === 1}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <PhoneInputWidget form={form} name="tel1" label="Phone" />
            <PhoneInputWidget form={form} name="tel2" label="Phone 2" />
            <PhoneInputWidget form={form} name="telfax" isFax label="Fax" />
            <InputField
              name="emailaddress"
              form={form}
              label="E-mail"
              placeholder="E-mail"
              validation={EMAIL_VALIDATION_RULE}
            />
          </div>
        </div>

        {/* Footer */}
        <FormActionButtons
          className="fixed bottom-0 left-[60%] right-0 bg-white px-4 py-2 pt-0"
          onSubmit={handleSubmit(onSubmit)}
          submitLabel="Save"
          onCancel={handleCancel}
          isLoading={isSubmitting}
          cancelLabel="Cancel"
        />
      </div>
    </CustomDialog>
  );
};

export default NewVendor;
