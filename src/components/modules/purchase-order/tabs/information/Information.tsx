import { use<PERSON>allback, useEffect, useMemo, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import dayjs from 'dayjs';

import {
  useGetCountryListQuery,
  useGetStateByCountryQuery,
} from '@/redux/features/country/country.api';
import { useSalesPersonQuery } from '@/redux/features/customers/choices.api';
import { useGetEnumsListQuery } from '@/redux/features/enums-api/enums-api';
import { useGetPurchaseOrderByIdQuery } from '@/redux/features/purchase-order/purchaseOrder.api';

import AppButton from '@/components/common/app-button';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import AppSpinner from '@/components/common/app-spinner';

import VendorLookup from '@/components/common/lookups/vendor-lookup';
import AutoCompleteDropdown from '@/components/forms/auto-complete-dropdown';
import DatePicker from '@/components/forms/date-picker';
import InputField from '@/components/forms/input-field';
import NumberInputField from '@/components/forms/number-input-field';
import PhoneInputWidget from '@/components/forms/phone-input-mask';
import SelectWidget from '@/components/forms/select';
import ZipCodeInput from '@/components/forms/zipcode-input';
import { PlusIcon } from 'lucide-react';

import { VENDORS_API_ROUTES } from '@/constants/api-constants';
import { ROUTES } from '@/constants/routes-constants';
import { TEXT_VALIDATION_RULE } from '@/constants/validation-constants';
import {
  formatPhoneNumber,
  generateLabelValuePairs,
  getQueryParam,
} from '@/lib/utils';

import { POInformationFormDataTypes } from '@/types/purchase-order.types';
import CompletionInfo from '../../CompletionInfo';
import DeletePurchaseOrder from '../../DeletePurchaseOrder';
import NewVendor from './NewVendor';

const Information = () => {
  const form = useFormContext<POInformationFormDataTypes>();
  const navigate = useNavigate();
  const id = getQueryParam('id') as string;
  const { reset, watch, setValue, clearErrors, formState } = form;
  const { errors } = formState;

  // Local state
  const [modals, setModals] = useState({
    vendor: false,
    deleteConfirm: false,
    completionInfo: false,
    deleteDialog: false,
  });
  const [statusState, setStatusState] = useState({ previous: '', pending: '' });
  const [showPastDateWarningModal, setShowPastDateWarningModal] =
    useState<boolean>(false);

  const [warningModal, setWarningModal] = useState<{
    message: string;
    isOpen: boolean;
  }>({ message: '', isOpen: false });
  const [isRefetchNewCustInfo, setIsRefetchNewCustInfo] =
    useState<boolean>(false);

  const shipCountryId = watch('shipCountryId');
  const isDelete = watch('isDeleted');
  const isUSA = shipCountryId === 1;
  const currentStatus = watch('status');

  // API calls
  const { data: poResponse, isLoading: poLoading } =
    useGetPurchaseOrderByIdQuery(id, { skip: !id });
  const { data: countryData = [] } = useGetCountryListQuery();
  const { data: statesData = [], isFetching: stateLoading } =
    useGetStateByCountryQuery({ countryId: Number(shipCountryId || 1) });
  const { data: salesResp, isLoading: salesLoading } = useSalesPersonQuery();
  const { data: typeResp, isLoading: typeLoading } = useGetEnumsListQuery({
    name: 'PurchaseOrderType',
  });
  const { data: statusResp, isLoading: statusLoading } = useGetEnumsListQuery({
    name: 'PurchaseOrderStatus',
  });

  const statusList = statusResp?.data?.filter(
    (status: any) => status?.value !== 'DELETED'
  );

  // Derived & memoized data
  const defaultValues = useMemo(() => {
    const data = poResponse?.data;
    if (!data) {
      return {
        shipCountryId: 1,
        type: 'PURCHASE_ORDER',
        status: 'ORDERED',
        orderDate: !id ? new Date() : '',
        anticipatedDeliveryDate: new Date(),
        subTotal: 0,
        total: 0,
      };
    }
    return {
      ...data,
      orderBy: data?.orderBy
        ? { label: data?.orderBy, value: data?.orderBy }
        : '',
      vendorId: { label: data?.vendorName, value: data?.vendorId.toString() },
      vendorPhone: {
        label: formatPhoneNumber(data?.vendorPhone ?? ''),
        value: data?.vendorPhone ?? '',
      },
    };
  }, [id, poResponse?.data]);

  const countryList = useMemo(
    () =>
      generateLabelValuePairs({
        data: countryData,
        labelKey: 'name',
        valueKey: 'country_id',
      }),
    [countryData]
  );
  const stateList = useMemo(
    () =>
      generateLabelValuePairs({
        data: statesData,
        labelKey: 'code',
        valueKey: 'state_id',
      }),
    [statesData]
  );
  const salesList = useMemo(
    () =>
      generateLabelValuePairs({
        data: salesResp?.data,
        labelKey: 'name',
        valueKey: 'name',
      }),
    [salesResp]
  );

  // Reset form when data loads
  useEffect(() => {
    reset(defaultValues);
  }, [defaultValues, reset]);

  // Handlers - Vendor
  const toggleVendorModal = useCallback(
    () => setModals((prev) => ({ ...prev, vendor: !prev.vendor })),
    []
  );

  const toggleCompetionModal = useCallback(
    () =>
      setModals((prev) => ({ ...prev, completionInfo: !prev.completionInfo })),
    []
  );

  const handleChangeVendor = useCallback(
    (vendor: any) => {
      if (vendor) {
        const {
          id,
          vendorName,
          address1,
          address2,
          city,
          stateId,
          countryId,
          zipCode,
          telfax,
          tel1,
        } = vendor;
        setValue('vendorId', { label: vendorName, value: id });
        setValue('vendorPhone', {
          label: formatPhoneNumber(tel1),
          value: tel1,
        });
        setValue('vendorCity', city);
        setValue('vendorAddress1', address1);
        setValue('vendorAddress2', address2);
        setValue('vendorStateId', stateId);
        setValue('vendorCountryId', countryId);
        setValue('vendorZipCode', zipCode);
        setValue('vendorFax', telfax);
      }
      if (isRefetchNewCustInfo) {
        setIsRefetchNewCustInfo(false);
      }
    },
    [setValue, isRefetchNewCustInfo]
  );

  // Handlers - Country change
  const handleCountryChange = useCallback(
    (value: string) => {
      setValue('shipCountryId', value);
      if (shipCountryId !== value) {
        setValue('shipStateId', '');
        setValue('shipZipCode', '');
        clearErrors('shipZipCode');
      }
    },
    [shipCountryId, setValue, clearErrors]
  );

  // Handlers - Charges
  const handleCustomCharge = (value: string) => {
    const { subTotal, freight } = watch();
    const total = Number(subTotal) + Number(freight ?? 0) + Number(value);
    setValue('customCharge', Number(value));
    setValue('total', total);
  };

  const handleFreightChange = (value: string) => {
    const { subTotal, customCharge } = watch();
    const total = Number(subTotal) + Number(customCharge ?? 0) + Number(value);
    setValue('freight', Number(value));
    setValue('total', total);
  };

  // Handlers - Status changes
  const handleStatusChange = (value: string) => {
    if (value === 'PAID') {
      setModals((prev) => ({ ...prev, completionInfo: true }));
      setValue('status', value);
    } else if (value === 'DELETED') {
      setStatusState({ previous: currentStatus, pending: value });
      setModals((prev) => ({ ...prev, deleteConfirm: true }));
    } else {
      setValue('status', value);
    }
  };

  const handleDeleteCancel = () => {
    setValue('status', statusState.previous);
    setStatusState({ previous: '', pending: '' });
    setModals((prev) => ({ ...prev, deleteConfirm: false }));
  };

  const toggleDelete = useCallback(
    (success?: boolean) => {
      setModals((prev) => ({ ...prev, deleteDialog: !prev.deleteDialog }));
      if (modals.deleteDialog) {
        form.setValue('status', statusState.previous);
      }
      if (success) navigate(ROUTES.PURCHASE_ORDERS);
    },
    [form, modals.deleteDialog, navigate, statusState.previous]
  );

  const handleToggleWarningModal = useCallback(async () => {
    setShowPastDateWarningModal((prevState) => !prevState);
  }, []);

  const orderDate = form.watch('orderDate');
  const anticipatedDeliveryDateWatch = form.watch('anticipatedDeliveryDate');

  // Date change logic
  const onDateChange = useCallback(
    (
      date: Date | string | undefined,
      fieldName: 'orderDate' | 'anticipatedDeliveryDate'
    ) => {
      const selectDate = new Date(date as string);
      const today = new Date();
      today.setHours(0, 0, 0, 0); // Reset time for an accurate comparison
      if (selectDate < today && fieldName === 'orderDate') {
        setShowPastDateWarningModal(true);
        return;
      }
      if (!date) return;
      const selectedDate = dayjs(date);
      const anticipatedDeliveryDate = dayjs(
        form.watch('anticipatedDeliveryDate')
      );
      if (!selectedDate.isValid() || !anticipatedDeliveryDate.isValid()) return;
      let message = '';
      if (
        fieldName === 'orderDate' &&
        selectedDate.isAfter(anticipatedDeliveryDate, 'day') &&
        !showPastDateWarningModal
      ) {
        form.setValue('orderDate', orderDate);
        message =
          'The Order Date cannot be later than the Anticipated Delivery Date.';
      }
      if (
        fieldName === 'anticipatedDeliveryDate' &&
        selectedDate.isBefore(form.watch('orderDate'), 'day')
      ) {
        form.setValue('anticipatedDeliveryDate', anticipatedDeliveryDateWatch);
        message =
          'The Anticipated Delivery Date cannot be earlier than the Order Date.';
      }
      if (message) {
        setWarningModal({ message, isOpen: true });
      }
    },
    [anticipatedDeliveryDateWatch, form, orderDate, showPastDateWarningModal]
  );

  return (
    <>
      <div>
        <div className="flex flex-col gap-6 justify-between h-full">
          {/* INFO DETAILS */}
          <div className="flex items-center justify-between gap-3">
            <p className="text-2xl font-semibold text-[#181A1D]">Info</p>
          </div>
          <div className="p-5 gap-8 border border-border-Default rounded-lg w-full">
            <div className="grid 2xl:grid-cols-3 md:grid-cols-2 gap-4 grid-cols-1">
              <NumberInputField
                form={form}
                name="id"
                label="PO #"
                placeholder="Enter Purchase Order #"
                disabled
              />
              <SelectWidget
                form={form}
                optionsList={typeResp?.data ?? []}
                label="PO Type"
                name="type"
                placeholder="Purchase Order Type"
                isLoading={typeLoading}
                isClearable={false}
                disabled={isDelete}
              />
              <SelectWidget
                form={form}
                optionsList={id ? statusResp?.data : (statusList ?? [])}
                label="Status"
                name="status"
                placeholder="Purchase Order Status"
                isLoading={statusLoading}
                isClearable={false}
                validation={TEXT_VALIDATION_RULE}
                onSelectChange={handleStatusChange}
                disabled={isDelete}
              />
              <DatePicker
                form={form}
                name="orderDate"
                label="Order Date"
                placeholder="MM/DD/YYYY"
                enableInput
                validation={TEXT_VALIDATION_RULE}
                disabled={isDelete}
                isRenderFirst={true}
                onDateChange={(date) => onDateChange(date, 'orderDate')}
              />
              <DatePicker
                form={form}
                name="anticipatedDeliveryDate"
                label="Anticipated Delivery Date"
                placeholder="MM/DD/YYYY"
                enableInput
                isRenderFirst={true}
                onDateChange={(date) =>
                  onDateChange(date, 'anticipatedDeliveryDate')
                }
                disabled={isDelete}
              />
              <InputField
                name="job"
                form={form}
                label="Job"
                placeholder="Enter Job"
                maxLength={16}
                disabled={isDelete}
              />
              <InputField
                name="contact"
                form={form}
                label="Contact"
                placeholder="Enter Contact"
                maxLength={30}
                disabled={isDelete}
              />
              <InputField
                name="shipVia"
                form={form}
                label="Ship Via"
                placeholder="Enter Ship Via"
                maxLength={15}
                disabled={isDelete}
              />
              <InputField
                name="fob"
                form={form}
                label="F.O.B."
                placeholder="Enter F.O.B."
                maxLength={15}
                disabled={isDelete}
              />
              <InputField
                name="terms"
                form={form}
                label="Terms"
                placeholder="Enter Terms"
                maxLength={15}
                disabled={isDelete}
              />
            </div>
          </div>
        </div>
      </div>
      <div>
        <div className="flex flex-col gap-6 justify-between h-full mt-6">
          {/* VENDOR DETAILS */}
          <div className="flex items-center justify-between">
            <p className="text-2xl font-semibold text-[#181A1D]">Vendor</p>

            <AppButton
              label={'New Vendor'}
              icon={PlusIcon}
              iconClassName="w-4"
              spinnerClass={
                'border-white-500  border-t-transparent animate-spin'
              }
              className="bg-brand-teal-Default hover:bg-brand-teal-hover border-border-brand-teal-Default mx-1"
              onClick={toggleVendorModal}
              disabled={isDelete}
            />
          </div>
          <div className="p-5 gap-8 border border-border-Default rounded-lg w-full">
            <div className="grid 2xl:grid-cols-3 md:grid-cols-2 gap-4 grid-cols-1">
              <div className="flex items-center gap-x-2">
                <AutoCompleteDropdown
                  label="Vendor Name (Lookup)"
                  placeholder="Select Vendor Name"
                  name="vendorId"
                  form={form}
                  onSelectChange={(option) => handleChangeVendor(option.item)}
                  url={VENDORS_API_ROUTES.ALL}
                  labelKey="vendorName"
                  valueKey="id"
                  sortBy="vendorName"
                  validation={TEXT_VALIDATION_RULE}
                  showItem
                  isRefetch={isRefetchNewCustInfo}
                  disabled={isDelete}
                />
                <VendorLookup
                  handleOk={(vendor) => handleChangeVendor(vendor)}
                  className={errors?.vendorId?.message ? 'mt-2' : 'mt-8'}
                  disabled={isDelete}
                />
              </div>

              <AutoCompleteDropdown
                label="Phone (Lookup)"
                placeholder="Select Phone"
                name="vendorPhone"
                form={form}
                onSelectChange={(option) => handleChangeVendor(option?.item)}
                showItem
                url={VENDORS_API_ROUTES.ALL}
                labelKey="tel1"
                valueKey="tel1"
                sortBy="vendorName"
                formatLabel={formatPhoneNumber}
                isRefetch={isRefetchNewCustInfo}
                disabled={isDelete}
                acceptAlphanumeric={false}
              />

              <PhoneInputWidget
                form={form}
                name="vendorFax"
                label="Fax"
                disabled
              />
              <SelectWidget
                form={form}
                optionsList={salesList}
                label="Ordered By"
                name="orderBy"
                placeholder="Ordered By"
                isLoading={salesLoading}
                allowCustomEntry
                returnOptionAsObject
                disabled={isDelete}
              />
              <InputField
                name="vendorAddress1"
                form={form}
                label="Address 1"
                placeholder="Enter Address 1"
                maxLength={4000}
                disabled
              />
              <InputField
                name="vendorAddress2"
                form={form}
                label="Address 2"
                placeholder="Enter Address 2"
                maxLength={4000}
                disabled
              />
              <InputField
                name="vendorCity"
                form={form}
                label="City"
                placeholder="Enter City"
                maxLength={4000}
                disabled
              />

              <SelectWidget
                name="vendorStateId"
                form={form}
                placeholder="Select State"
                label="State"
                isClearable={false}
                optionsList={stateList}
                isLoading={stateLoading}
                disabled
              />
              <SelectWidget
                form={form}
                name="vendorCountryId"
                label="Country"
                optionsList={countryList}
                disabled
              />
              <InputField
                name="vendorZipCode"
                form={form}
                label="Zip Code"
                placeholder="Enter Zip Code"
                maxLength={15}
                disabled
              />
            </div>
          </div>
        </div>
      </div>
      <div>
        <div className="flex flex-col gap-6 justify-between h-full mt-6">
          {/* SHIP TO */}
          <p className="text-2xl font-semibold text-[#181A1D]">Ship To</p>
          <div className="p-5 gap-8 border border-border-Default rounded-lg w-full">
            <div className="grid xl:grid-cols-3 md:grid-cols-2 gap-4 grid-cols-1">
              <InputField
                name="shipLocation"
                form={form}
                label="Location"
                placeholder="Enter Location"
                maxLength={4000}
                disabled={isDelete}
              />
              <PhoneInputWidget
                form={form}
                name="shipPhone"
                label="Phone"
                disabled={isDelete}
              />
              <InputField
                name="shipContact"
                form={form}
                label="Contact"
                placeholder="Enter Contact"
                maxLength={4000}
                disabled={isDelete}
              />
              <InputField
                name="shipAddress1"
                form={form}
                label="Address 1"
                placeholder="Enter Address 1"
                maxLength={4000}
                disabled={isDelete}
              />
              <InputField
                name="shipAddress2"
                form={form}
                label="Address 2"
                placeholder="Enter Address 2"
                maxLength={4000}
                disabled={isDelete}
              />
              <InputField
                name="shipCity"
                form={form}
                label="City"
                placeholder="Enter City"
                disabled={isDelete}
                maxLength={4000}
              />

              <SelectWidget
                name="shipStateId"
                form={form}
                placeholder="Select State"
                label="State"
                isClearable={false}
                optionsList={stateList}
                isLoading={stateLoading}
                disabled={isDelete}
              />

              <SelectWidget
                enableSearch={true}
                form={form}
                name="shipCountryId"
                label="Country"
                placeholder="Select Country"
                optionsList={countryList}
                onSelectChange={handleCountryChange}
                isClearable={false}
                disabled={isDelete}
              />

              <ZipCodeInput
                name="shipZipCode"
                isUSA={isUSA}
                form={form}
                label="Zip Code"
                disabled={isDelete}
              />
              <div></div>
              <div></div>
              <NumberInputField
                form={form}
                name="subTotal"
                label="Sub-Total"
                placeholder="$______.__"
                prefix="$"
                fixedDecimalScale
                thousandSeparator=","
                disabled
              />
              <div></div>
              <InputField
                name="customDescription"
                form={form}
                label="Custom Description"
                placeholder="Enter Custom Description"
                maxLength={4000}
                disabled={isDelete}
              />
              <NumberInputField
                form={form}
                name="customCharge"
                label="Custom Charge"
                placeholder="$______.__"
                prefix="$"
                maxLength={11}
                fixedDecimalScale
                thousandSeparator=","
                onValueChange={handleCustomCharge}
                disabled={isDelete}
              />
              <div></div>
              <div></div>

              <NumberInputField
                form={form}
                name="freight"
                label="Freight Charge"
                placeholder="$______.__"
                prefix="$"
                maxLength={11}
                fixedDecimalScale
                thousandSeparator=","
                onValueChange={handleFreightChange}
                disabled={isDelete}
              />
              <div></div>
              <div></div>
              <NumberInputField
                form={form}
                name="total"
                label="Total Charge"
                placeholder="$______.__"
                prefix="$"
                fixedDecimalScale
                thousandSeparator=","
                disabled
              />
            </div>
          </div>
        </div>
      </div>

      <NewVendor
        open={modals.vendor as boolean}
        onOpenChange={toggleVendorModal}
        handleOk={(value) => {
          setIsRefetchNewCustInfo(true);
          handleChangeVendor(value);
        }}
      />
      <AppConfirmationModal
        title={'Warning'}
        open={showPastDateWarningModal}
        description={
          <div>
            The Date of Use just Entered is prior to today. Please be make sure
            <span className="font-bold"> this is the correct date</span>
          </div>
        }
        handleSubmit={handleToggleWarningModal}
        submitLabel="Ok"
      />

      <AppConfirmationModal
        title="Confirmation"
        open={modals.deleteConfirm}
        description={
          <div>
            This will delete this purchase order. Do you want to continue?
          </div>
        }
        handleCancel={handleDeleteCancel}
        handleSubmit={() =>
          setModals((prev) => ({
            ...prev,
            deleteConfirm: false,
            deleteDialog: true,
          }))
        }
      />

      <AppConfirmationModal
        title={'Warning'}
        description={<div>{warningModal.message}</div>}
        open={warningModal.isOpen}
        handleCancel={() => setWarningModal({ message: '', isOpen: false })}
        cancelLabel={'Ok'}
      />

      <CompletionInfo
        open={modals.completionInfo}
        onOpenChange={toggleCompetionModal}
      />

      <DeletePurchaseOrder
        isDeleted={isDelete}
        open={modals.deleteDialog}
        onOpenChange={(success) => toggleDelete(success)}
      />

      <AppSpinner overlay isLoading={poLoading} />
    </>
  );
};

export default Information;
