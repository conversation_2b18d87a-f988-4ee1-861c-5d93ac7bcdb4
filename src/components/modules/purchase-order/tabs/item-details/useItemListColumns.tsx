import { useMemo } from 'react';
import { useFormContext, UseFormReturn } from 'react-hook-form';

// Components
import ActionColumnMenu from '@/components/common/data-tables/ActionColumn';
import InputField from '@/components/forms/input-field';
import NumberInputField from '@/components/forms/number-input-field';

// Constants and Utils
import { PURCHASE_API_ROUTES } from '@/constants/api-constants';
import { convertToFloat } from '@/lib/utils';

// API Hooks
import ReactSelect from '@/components/common/ReactSelect';
import {
  QUANTITY_VALIDATION_RULE,
  TEXT_VALIDATION_RULE,
} from '@/constants/validation-constants';
import {
  POInformationFormDataTypes,
  POItemDetailsList,
} from '@/types/purchase-order.types';

interface UseItemListColumnsProps {
  form: UseFormReturn<POItemDetailsList>;
  handleItemIdChange: (
    value: { value: string },
    rowIndex: number
  ) => Promise<void>;
  handlePriceChange: (price: string | number, index: number) => void;
  handleQuantityChange: (quantity: string | number, index: number) => void;
  toggleDelete: (id: number | null, index: number | null) => void;
}

export const useItemListColumns = ({
  form,
  handleItemIdChange,
  handlePriceChange,
  handleQuantityChange,
  toggleDelete,
}: UseItemListColumnsProps) => {
  const informationForm = useFormContext<POInformationFormDataTypes>();
  const isDeleted = informationForm.watch('isDeleted');

  return useMemo(
    () => [
      {
        accessorKey: 'itemId',
        header: 'Item ID',
        size: 120,
        enableSorting: true,
        cell: ({ row }: any) => {
          return (
            <ReactSelect
              placeholder="Select Item ID"
              className="w-44 bg-white "
              name={`items.${row?.index}.itemId`}
              form={form}
              onSelectChange={(value) => handleItemIdChange(value, row.index)}
              url={PURCHASE_API_ROUTES.GENERIC_ITEM_LOOKUP}
              labelKey="itemId"
              valueKey="id"
              maxMenuHeight={250}
              fieldName="itemId"
              operator="Equals"
              extraFilters={[{ field: 'isKitAllowded', value: 'false' }]}
              disabled={isDeleted}
              validation={TEXT_VALIDATION_RULE}
            />
          );
        },
      },
      {
        accessorKey: 'quantity',
        header: 'Qty',
        size: 80,
        cell: ({ row }: any) => {
          const itemId = form.watch(`items.${row.index}.itemId`);
          const validation = itemId?.value ? QUANTITY_VALIDATION_RULE : {};

          return (
            <NumberInputField
              form={form}
              placeholder="Quantity"
              name={`items.${row.index}.quantity`}
              className="w-24 h-8"
              maxLength={5}
              decimalScale={0}
              onValueChange={(value) => handleQuantityChange(value, row.index)}
              disabled={!itemId?.value || isDeleted}
              validation={validation}
            />
          );
        },
      },
      {
        accessorKey: 'description',
        header: 'Item Description',
        maxSize: 300,
        enableSorting: true,
        cell: ({ row }: any) => {
          const disabled = form.watch(`items.${row?.index}.itemId`);
          return (
            <div className="w-full truncate">
              <InputField
                form={form}
                placeholder="Description"
                className="w-48 h-8"
                name={`items.${row?.index}.description`}
                disabled={!disabled || isDeleted}
                pClassName="p-1"
              />
            </div>
          );
        },
      },
      {
        accessorKey: 'price',
        header: 'Price',
        size: 120,
        cell: ({ row }: any) => {
          const itemId = form.watch(`items.${row.index}.itemId`);
          const validation = itemId?.value
            ? {
                validate: (val: any) =>
                  (val !== null && val !== '') || 'Required',
              }
            : {};
          return (
            <NumberInputField
              form={form}
              name={`items.${row.index}.price`}
              placeholder=""
              maxLength={9}
              prefix="$"
              className="w-28 h-8"
              fixedDecimalScale
              thousandSeparator=","
              disabled={!itemId?.value || isDeleted}
              onValueChange={(value) => handlePriceChange(value, row.index)}
              validation={validation}
            />
          );
        },
      },
      {
        accessorKey: 'total',
        header: 'Total',
        size: 120,
        cell: ({ row }: any) =>
          convertToFloat({
            value: form.watch(`items.${row.index}.total`) ?? 0,
            prefix: '$',
          }),
      },
      {
        accessorKey: 'action',
        header: 'Actions',
        size: 80,
        cell: ({ row }: any) => (
          <ActionColumnMenu
            contentClassName="w-fit"
            onDelete={() => toggleDelete(row.original.listId, row.index)}
            disabled={isDeleted}
          />
        ),
      },
    ],
    [
      form,
      handleItemIdChange,
      handlePriceChange,
      handleQuantityChange,
      isDeleted,
      toggleDelete,
    ]
  );
};
