import { useCallback, useEffect, useMemo, useState } from 'react';
import { useFieldArray, useFormContext, UseFormReturn } from 'react-hook-form';

// Components
import AppButton from '@/components/common/app-button';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';

// Constants and Utils
import { PURCHASE_API_ROUTES } from '@/constants/api-constants';
import { getQueryParam } from '@/lib/utils';

// API Hooks
import ItemLookup from '@/components/modules/orders/item-details/item-list/ItemLookup';
import { useGetItemBriefMutation } from '@/redux/features/items/item.api';
import {
  useDeletePOItemsMutation,
  useGetPOItemListQuery,
} from '@/redux/features/purchase-order/purchaseOrder.api';
import {
  POInformationFormDataTypes,
  POItemDetailsList,
  POItemDetailsTypes,
} from '@/types/purchase-order.types';
import ItemListTable from '../ItemListTable';
import { useItemListColumns } from './useItemListColumns';

// Types
interface DeleteDialogState {
  isOpen: boolean;
  itemId: number | null;
  index: number | null;
}

const initialDeleteDialogState: DeleteDialogState = {
  isOpen: false,
  itemId: null,
  index: null,
};
interface ItemDetailsType {
  form: UseFormReturn<POItemDetailsList>;
  onDefaultValuesLoaded?: (defaults: POItemDetailsList) => void;
}

const mapItemsToFormValues = (
  items: POItemDetailsTypes[]
): POItemDetailsList => ({
  items:
    items?.map((item) => ({
      listId: item.id,
      itemId: {
        label: item.itemIdLabel || '',
        value: item.itemId?.toString() || '',
      },
      description: item.description || '',
      quantity: item.quantity || '',
      price: item.price || '0',
      total: item?.total,
    })) || [],
});

const ItemDetails = ({ form, onDefaultValuesLoaded }: ItemDetailsType) => {
  // State and Hooks
  const informationForm = useFormContext<POInformationFormDataTypes>();
  const [deleteDialogState, setDeleteDialogState] = useState<DeleteDialogState>(
    initialDeleteDialogState
  );
  const id = getQueryParam('id') as string;
  const isDeleted = informationForm.watch('isDeleted');
  const { data, isLoading } = useGetPOItemListQuery(id, {
    refetchOnMountOrArgChange: true,
  });
  const [getitemBrief] = useGetItemBriefMutation();
  const [deleteItem, { isLoading: isDeleteLoading }] =
    useDeletePOItemsMutation();

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'items',
  });

  const defaultValues = useMemo(
    () => mapItemsToFormValues(data?.data as POItemDetailsTypes[]),
    [data?.data]
  );

  useEffect(() => {
    form.reset(defaultValues);
    onDefaultValuesLoaded?.(defaultValues as POItemDetailsList);
  }, [defaultValues, form, onDefaultValuesLoaded]);

  const handleAddNew = useCallback(() => {
    const lastItem =
      form.getValues('items')[form.getValues('items').length - 1];

    if (lastItem?.itemId?.value || fields?.length === 0) {
      append({
        listId: null,
        itemId: null,
        description: '',
        quantity: '',
        price: '0',
        total: 0,
        disabledCheckBox: true,
      });
    }
  }, [append, fields?.length, form]);

  // toggle delete
  const toggleDelete = useCallback(
    (id: number | null, index: number | null) => {
      setDeleteDialogState((prev) => ({
        isOpen: !prev.isOpen,
        itemId: id,
        index,
      }));
    },
    []
  );

  const handleDeleteItem = async () => {
    if (deleteDialogState.itemId) {
      await deleteItem({ poId: id, poItemId: deleteDialogState.itemId });
    } else if (deleteDialogState.index !== null) {
      remove(deleteDialogState.index);
    }
    setDeleteDialogState(initialDeleteDialogState);
  };

  const handleAddItemLookupData = useCallback(
    (selectedItems: any[]) => {
      const transformedItems = selectedItems.map((item) => ({
        ...item,
        disabledCheckBox: true,
        ...(item.price &&
          item.quantity && {
            total: Number(item.price) * Number(item.quantity),
          }),
      }));

      append(transformedItems);
    },
    [append]
  );

  const handlePriceChange = useCallback(
    (price: string | number, index: number) => {
      form.clearErrors(`items[${index}].price` as any);
      const quantity = form.getValues(`items.${index}.quantity`);
      const priceNumber = Number(price);
      const quantityNumber = Number(quantity);
      const total =
        !isNaN(priceNumber) && !isNaN(quantityNumber)
          ? priceNumber * quantityNumber
          : 0;
      form.setValue(`items.${index}.total`, Number(total));
      form.setValue(`items.${index}.price`, price);
    },
    [form]
  );

  const handleItemIdChange = useCallback(
    async (value: { value: string }, rowIndex: number) => {
      const itemData = await getitemBrief(value?.value);
      if (itemData?.data) {
        form.setValue(`items.${rowIndex}.itemId`, {
          label: itemData?.data?.data?.itemId,
          value: itemData?.data?.data?.id?.toString(),
        });
        form.setValue(
          `items.${rowIndex}.price`,
          itemData?.data?.data?.unitPrice?.toString()
        );
        form.setValue(
          `items.${rowIndex}.description`,
          itemData?.data?.data?.description ?? ''
        );
      }
    },
    [getitemBrief, form]
  );

  const handleQuantityChange = useCallback(
    (quantity: string | number, index: number) => {
      form.clearErrors(`items[${index}].quantity` as any);
      const price = form.getValues(`items.${index}.price`);
      const quantityNumber = Number(quantity);
      const priceNumber = Number(price);
      const total =
        !isNaN(quantityNumber) && !isNaN(priceNumber)
          ? quantityNumber * priceNumber
          : 0;
      form.setValue(`items.${index}.total`, Number(total));
    },
    [form]
  );

  const columns = useItemListColumns({
    form,
    handleItemIdChange,
    handlePriceChange,
    handleQuantityChange,
    toggleDelete,
  });

  return (
    <div className="flex flex-col gap-2 ">
      <ItemLookup
        className="w-fit"
        onClick={handleAddItemLookupData}
        url={PURCHASE_API_ROUTES.GENERIC_ITEM_LOOKUP}
        heading="Select one or more Item to add to the PO Item list"
        disabled={isDeleted}
        extraFilters={[{ field: 'isKitAllowded', value: 'false' }]}
      />
      <ItemListTable fields={fields} columns={columns} isLoading={isLoading} />
      <AppButton
        disabled={isDeleted}
        label="+ Add New"
        onClick={handleAddNew}
        className="w-[110px] bg-brand-teal-Default hover:bg-brand-teal-Default/85"
      />
      <AppConfirmationModal
        description={<div>Are you sure you want to delete ?</div>}
        open={deleteDialogState.isOpen}
        handleCancel={() => setDeleteDialogState(initialDeleteDialogState)}
        handleSubmit={handleDeleteItem}
        disabled={isDeleteLoading}
        isLoading={isDeleteLoading}
      />
    </div>
  );
};

export default ItemDetails;
