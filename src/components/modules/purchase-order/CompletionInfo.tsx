import AppButton from '@/components/common/app-button';
import CustomDialog from '@/components/common/dialog';
import InputField from '@/components/forms/input-field';
import { getQueryParam } from '@/lib/utils';
import {
  CompletionInfoType,
  POInformationTypes,
  PurchaseOrderListingTypes,
} from '@/types/purchase-order.types';
import { X } from 'lucide-react';
import { useCallback, useEffect, useMemo } from 'react';
import { useForm, useFormContext } from 'react-hook-form';

interface CompletionInfoProps {
  open: boolean;
  onOpenChange: (success?: boolean) => void;
  POData?: PurchaseOrderListingTypes | null;
}

const CompletionInfo = ({
  open,
  onOpenChange,
  POData,
}: CompletionInfoProps) => {
  const poForm = useFormContext<POInformationTypes>();
  const queryParamPOId = getQueryParam('id') as string;
  const poDetails = queryParamPOId ? poForm.watch() : POData;

  const defaultValues = useMemo<Partial<CompletionInfoType>>(
    () => ({
      completeNote1: poDetails?.completeNote1 ?? '',
      completeNote2: poDetails?.completeNote2 ?? '',
      completeNote3: poDetails?.completeNote3 ?? '',
    }),
    [
      poDetails?.completeNote1,
      poDetails?.completeNote2,
      poDetails?.completeNote3,
    ]
  );

  const form = useForm<CompletionInfoType>({
    defaultValues,
    mode: 'onChange',
  });

  const { reset } = form;

  useEffect(() => {
    if (defaultValues) {
      reset(defaultValues);
    }
  }, [defaultValues, reset]);

  const handleNoteChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = event.target;
    if (
      id === 'completeNote1' ||
      id === 'completeNote2' ||
      id === 'completeNote3'
    ) {
      form?.setValue(id, value);
      poForm?.setValue(id, value);
    }
  };

  const handleCancel = useCallback(() => {
    onOpenChange();
  }, [onOpenChange]);

  return (
    <CustomDialog
      title={'Completion Info'}
      open={open}
      onOpenChange={handleCancel}
      className={'max-w-[90%] w-[80%] md:w-[40%] 2xl:w-[30%]'}
      contentClassName="max-h-[410px] overflow-y-auto px-6 pb-0"
    >
      <div className="w-full">
        <div className="border rounded-lg p-4 ">
          <div className="py-2 flex flex-col gap-4">
            <InputField
              name="completeNote1"
              form={form}
              label="Note 1"
              onChange={handleNoteChange}
              disabled={!!POData}
            />
            <InputField
              name="completeNote2"
              form={form}
              label="Note 2"
              onChange={handleNoteChange}
              disabled={!!POData}
            />
            <InputField
              name="completeNote3"
              form={form}
              label="Note 3"
              onChange={handleNoteChange}
              disabled={!!POData}
            />
          </div>
        </div>
        <div className="flex items-center justify-end sticky bottom-0 bg-white gap-4 py-3 mt-2">
          <AppButton
            label="Close"
            onClick={handleCancel}
            className="w-28"
            icon={X}
          />
        </div>
      </div>
    </CustomDialog>
  );
};

export default CompletionInfo;
