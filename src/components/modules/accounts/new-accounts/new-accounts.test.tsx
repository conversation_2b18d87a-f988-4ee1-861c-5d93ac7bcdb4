import { describe, expect, vi, beforeEach, afterEach, test } from 'vitest';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import NewAccountInformation from './';
import { FormProvider, useForm } from 'react-hook-form';
import * as countryApi from '@/redux/features/country/country.api';
import * as timezoneApi from '@/redux/features/timezone/timezone.api';
import * as tenantApi from '@/redux/features/tenant/tenant.api';
import { MemoryRouter } from 'react-router-dom';
import { it } from 'vitest';

// Mock the API hooks
vi.mock('@/redux/features/country/country.api');
vi.mock('@/redux/features/timezone/timezone.api');
vi.mock('@/redux/features/tenant/tenant.api');

// Mock data
const mockCountryData = [
  { country_id: 1, name: 'United States' },
  { country_id: 2, name: 'Canada' },
];

const mockTimeZoneData = [
  { id: 1, displayName: 'UTC-5 Eastern Time' },
  { id: 2, displayName: 'UTC-8 Pacific Time' },
];

const mockStatesData = [
  { code: 'NY', name: 'New York' },
  { code: 'CA', name: 'California' },
];

const FormProviderWrapper = ({ children }: { children: React.ReactNode }) => {
  const methods = useForm({
    defaultValues: {
      clientName: 'Test Client',
      schemaName: 'test-schema',
      timezoneId: '1',
      isActive: 'true',
      contactName: 'John Doe',
      contactEmail: '<EMAIL>',
      contactPhone: '*************',
      location: 'New York',
      website: 'www.example.com',
      addressLine1: '123 Main St',
      addressLine2: 'Suite 100',
      city: 'New York City',
      state: 'NY',
      zipcode: '10001',
      country: 'United States',
    },
  });

  return (
    <MemoryRouter>
      <FormProvider {...methods}>{children}</FormProvider>
    </MemoryRouter>
  );
};

describe('New Account Info Component', () => {
  beforeEach(() => {
    // Mock API responses
    vi.spyOn(countryApi, 'useGetCountryListQuery').mockReturnValue({
      data: mockCountryData,
      isLoading: false,
      error: null,
    } as any);

    vi.spyOn(timezoneApi, 'useGetTimeZoneListQuery').mockReturnValue({
      data: mockTimeZoneData,
      isLoading: false,
      error: null,
    } as any);

    vi.spyOn(countryApi, 'useGetStateByCountryQuery').mockReturnValue({
      data: mockStatesData,
      isLoading: false,
      error: null,
    } as any);

    vi.spyOn(tenantApi, 'useActivateAccountMutation').mockReturnValue([
      vi.fn(),
      { isLoading: false },
    ] as any);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  test('should render all form fields with initial values', async () => {
    render(
      <FormProviderWrapper>
        <NewAccountInformation />
      </FormProviderWrapper>
    );

    expect(screen.getByLabelText('Client Name')).toBeInTheDocument();
    expect(screen.getByLabelText('Schema Name')).toBeInTheDocument();
    expect(screen.getByLabelText('Timezone')).toBeInTheDocument();
    expect(screen.getByLabelText('Status')).toBeInTheDocument();
    expect(screen.getByLabelText('Contact Name')).toBeInTheDocument();
    expect(screen.getByLabelText('Contact Email')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Enter Contact Phone'));
  });

  test('should handle country selection and update states', async () => {
    render(
      <FormProviderWrapper>
        <NewAccountInformation />
      </FormProviderWrapper>
    );

    const countrySelect = screen.getByLabelText('Country');
    await fireEvent.change(countrySelect, { target: { value: 'Canada' } });

    await waitFor(() => {
      const stateSelect = screen.getByLabelText('State');
      expect(stateSelect).toBeInTheDocument();
    });
  });

  const setup = () => {
    render(
      <FormProviderWrapper>
        <NewAccountInformation />
      </FormProviderWrapper>
    );
    return {
      getEmailInput: () => screen.getByLabelText('Contact Email'),
      getPhoneInput: () => screen.getByPlaceholderText('Enter Contact Phone'),
    };
  };

  describe('Email Validation', () => {
    it('should show error for invalid email format', async () => {
      const validateEmail = (email: string) => {
        const pattern = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$/;
        return pattern.test(email) ? null : 'Enter a valid Contact Email';
      };

      const invalidEmails = [
        'invalid-email',
        'test@',
        '@example.com',
        'test@example',
        'test.com',
      ];

      for (const invalidEmail of invalidEmails) {
        const validationError = validateEmail(invalidEmail);
        expect(validationError).toBe('Enter a valid Contact Email');
      }
    });

    it('should accept valid email formats', async () => {
      const validateEmail = (email: string) => {
        const pattern = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$/;
        return pattern.test(email) ? null : 'Enter a valid Contact Email';
      };

      const invalidEmails = [
        'invalid-email',
        'test@',
        '@example.com',
        'test@example',
        'test.com',
      ];

      for (const invalidEmail of invalidEmails) {
        const validationError = validateEmail(invalidEmail);
        expect(validationError).toBe('Enter a valid Contact Email');
      }
    });

    it('should show required error when email is empty', async () => {
      const { getEmailInput } = setup();
      const emailInput = getEmailInput();

      fireEvent.change(emailInput, { target: { value: '' } });
      const isBlured = fireEvent.blur(emailInput);
      expect(isBlured).toBe(true);
    });

    it('should handle disabled state correctly', () => {
      const { getEmailInput } = setup();
      const emailInput = getEmailInput();

      expect(emailInput).toBeDisabled();
    });
  });

  describe('Phone Validation', () => {
    it('should show error for invalid phone number format', async () => {
      const validatePhone = (phoneNumber: string) => {
        if (phoneNumber?.length > 10 || phoneNumber?.length < 10) {
          return 'Enter a valid Phone Number';
        } else {
          const pattern = /^[+]*[0-9]{1,4}[ ]?([0-9]{10,15})$/;
          return pattern.test(phoneNumber)
            ? null
            : 'Enter a valid Phone Number';
        }
      };

      const invalidPhoneNumbers = [
        '6767666767666',
        '**********0666',
        '12345678',
        'abcg',
      ];

      for (const invalidnumber of invalidPhoneNumbers) {
        const validationError = validatePhone(invalidnumber);
        expect(validationError).toBe('Enter a valid Phone Number');
      }
    });

    it('should accept valid phone number formats', async () => {
      const validatePhone = (phoneNumber: string) => {
        if (phoneNumber?.length > 10 || phoneNumber?.length < 10) {
          return 'Enter a valid Phone Number';
        } else {
          const pattern = /^[+]*[0-9]{1,4}[ ]?([0-9]{10,15})$/;
          return pattern.test(phoneNumber)
            ? null
            : 'Enter a valid Phone Number';
        }
      };

      const invalidPhoneNumbers = [
        '6767666767666',
        '**********0666',
        '12345678',
      ];

      for (const invalidnumber of invalidPhoneNumbers) {
        const validationError = validatePhone(invalidnumber);
        expect(validationError).toBe('Enter a valid Phone Number');
      }
    });

    it('should show required error when phone is empty', async () => {
      const { getPhoneInput } = setup();
      const phoneInput = getPhoneInput();

      fireEvent.change(phoneInput, { target: { value: '' } });
      const isBlured = fireEvent.blur(phoneInput);
      expect(isBlured).toBe(true);
    });
  });
});
