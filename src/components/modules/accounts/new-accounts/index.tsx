import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import AppSpinner from '@/components/common/app-spinner';
import TooltipWidget from '@/components/common/tooltip-widget';
import InputField from '@/components/forms/input-field';
import PhoneInputWidget from '@/components/forms/phone-input-mask';
import SelectDropDown from '@/components/forms/select-dropdown';
import { Separator } from '@/components/ui/separator';
import { statusList } from '@/constants/common-constants';
import { generateLabelValuePairs } from '@/lib/utils';
import {
  useGetCountryListQuery,
  useGetStateByCountryQuery,
} from '@/redux/features/country/country.api';
import { useActivateAccountMutation } from '@/redux/features/tenant/tenant.api';
import { useGetTimeZoneListQuery } from '@/redux/features/timezone/timezone.api';
import { CountryType, TimeZoneType } from '@/types/customer.types';
import { TenantType } from '@/types/tenant.types';
import debounce from 'lodash/debounce';
import { HelpCircleIcon } from 'lucide-react';
import React, { memo, useCallback, useMemo, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { useParams } from 'react-router-dom';

const FormItemWrapper = memo(({ children }: { children: React.ReactNode }) => {
  return <div className="col-span-2">{children}</div>;
});

const NewAccountInformation = () => {
  const form = useFormContext<TenantType>();
  const { data: countryData } = useGetCountryListQuery();
  const { data: timeZoneData, isLoading: timeZoneLoader } =
    useGetTimeZoneListQuery();
  const [selectedCountry, setSelectedCountry] = useState(1);
  const { data: statesData, isLoading: statesLoading } =
    useGetStateByCountryQuery({
      countryId: selectedCountry,
    });
  const [activateAccount, { isLoading }] = useActivateAccountMutation();
  const [openDeleteDialog, setOpenDeleteDialog] = useState<boolean>(false);

  const onOpenChange = useCallback(() => {
    setOpenDeleteDialog((prevState) => !prevState);
  }, [setOpenDeleteDialog]);

  const { clientId } = useParams<{ clientId?: string }>();

  const countryList = generateLabelValuePairs({
    data: countryData as CountryType[],
    labelKey: 'name',
    valueKey: 'name',
  });

  const timeZoneList = generateLabelValuePairs({
    data: timeZoneData as TimeZoneType[],
    labelKey: 'displayName',
    valueKey: 'id',
  });

  const stateList = useMemo(() => {
    return generateLabelValuePairs({
      data: statesData,
      labelKey: 'code',
      valueKey: 'name',
    });
  }, [statesData]);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  useCallback(
    debounce((countryId) => {
      setSelectedCountry(countryId);
    }, 300),
    []
  );

  const handleActivation = async () => {
    let payload = `${clientId}/${form.getValues('isActive') === 'false' ? 'activate' : 'deactivate'}`;
    await activateAccount(payload)
      .unwrap()
      .then(() => {
        form.setValue(
          'isActive',
          form.getValues('isActive') === 'true' ? 'false' : 'true'
        );
      })
      .catch((error) => error)
      .finally(() => {
        onOpenChange();
      });
  };
  const handleActivationCancel = async () => {
    await onOpenChange();
    form.setValue('isActive', form.getValues('isActive'));
  };
  return (
    <div className="col-span-12 md:col-span-12 border p-8 rounded-lg bg-white">
      <h3
        id="client-information-title"
        className="text-xl font-normal text-text-Default mb-2"
      >
        Client Information
      </h3>
      <Separator
        orientation="horizontal"
        className="h-[1px] bg-border-Default mb-4"
      />
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-x-4 gap-y-6">
        {/* Client Name */}
        <FormItemWrapper>
          <InputField
            form={form}
            name="clientName"
            label="Client Name"
            placeholder="Enter Client Name"
            validation={{ required: 'Required' }}
            disabled
            aria-describedby="clientName-helper"
          />
        </FormItemWrapper>

        {/* Schema Name */}
        <FormItemWrapper>
          <InputField
            form={form}
            name="schemaName"
            label="Schema Name"
            placeholder="Enter Schema Name"
            validation={{
              required: 'Required',
              pattern: {
                value: /^(?=[a-z]*[a-z])[a-z0-9-]+$/,
                message: 'Invalid Schema Name',
              },
            }}
            extraLabel={
              <TooltipWidget
                tooltip={
                  <ul style={{ listStyleType: 'disc' }} className="p-4">
                    <li>Should start with lowercase letter(a-z)</li>
                    <li>Only hyphen(-) is allowed as special character</li>
                    <li>Space is not allowed</li>
                  </ul>
                }
              >
                <HelpCircleIcon
                  size={22}
                  className="text-white"
                  fill="#5d26d2"
                />
              </TooltipWidget>
            }
            disabled
            aria-describedby="schemaName-helper"
          />
        </FormItemWrapper>

        {/* Timezone */}
        <FormItemWrapper>
          <SelectDropDown
            form={form}
            name="timezoneId"
            label="Timezone"
            optionsList={timeZoneList}
            placeholder="Select Timezone"
            validation={{ required: 'Required' }}
            disabled
            aria-describedby="timezoneId-helper"
          />
        </FormItemWrapper>

        {/* Status */}
        <FormItemWrapper>
          <SelectDropDown
            form={form}
            name="isActive"
            label="Status"
            placeholder="Select Status"
            optionsList={statusList}
            allowClear={false}
            onChange={(value) => {
              if (clientId) {
                onOpenChange();
              } else {
                form.setValue('isActive', value);
              }
            }}
            validation={{ required: 'Required' }}
            aria-describedby="isActive-helper"
          />
        </FormItemWrapper>

        {/* Contact Name */}
        <FormItemWrapper>
          <InputField
            form={form}
            name="contactName"
            label="Contact Name"
            placeholder="Enter Contact Name"
            validation={{
              required: 'Required',
            }}
            disabled
            aria-describedby="contactName-helper"
          />
        </FormItemWrapper>

        {/* Contact Email */}
        <FormItemWrapper>
          <InputField
            form={form}
            name="contactEmail"
            label="Contact Email"
            placeholder="Enter Contact Email"
            validation={{
              required: 'Required',
              pattern: {
                value: /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$/,
                message: 'Enter a valid Contact Email',
              },
            }}
            disabled
            aria-describedby="contactEmail-helper"
          />
        </FormItemWrapper>

        {/* Contact Phone */}
        <FormItemWrapper>
          <PhoneInputWidget
            aria-labelledby="contact-phone-label"
            name="contactPhone"
            label="Contact Phone"
            placeholder="Enter Contact Phone"
            form={form}
            error={form.formState.errors}
            validation={{
              required: 'Required',
              pattern: {
                value: /^[+]*[0-9]{1,4}[ ]?([0-9]{10,15})$/,
                message: 'Enter a valid Phone Number',
              },
            }}
            disabled
            aria-describedby="contactPhone-helper"
          />
        </FormItemWrapper>
        {/* Location */}
        <FormItemWrapper>
          <InputField
            form={form}
            name="location"
            label="Location"
            placeholder="Enter Location"
            disabled
            aria-describedby="location-helper"
          />
        </FormItemWrapper>
        {/* Website */}
        <FormItemWrapper>
          <InputField
            form={form}
            name="website"
            label="Website"
            placeholder="Enter Website"
            disabled
            aria-describedby="website-helper"
          />
        </FormItemWrapper>

        {/* Address Line 1 */}
        <FormItemWrapper>
          <InputField
            form={form}
            name="addressLine1"
            label="Address Line 1"
            placeholder="Enter Address Line 1"
            disabled
            aria-describedby="addressLine1-helper"
          />
        </FormItemWrapper>

        {/* Address Line 2 */}
        <FormItemWrapper>
          <InputField
            form={form}
            name="addressLine2"
            label="Address Line 2"
            placeholder="Enter Address Line 2"
            disabled
            aria-describedby="addressLine2-helper"
          />
        </FormItemWrapper>

        {/* City */}
        <FormItemWrapper>
          <InputField
            form={form}
            name="city"
            label="City"
            placeholder="Enter City"
            disabled
            aria-describedby="city-helper"
          />
        </FormItemWrapper>

        {/* State */}
        <FormItemWrapper>
          <SelectDropDown
            form={form}
            name="state"
            label="State"
            optionsList={stateList}
            placeholder="Select State"
            validation={{ required: 'Required' }}
            disabled
            aria-describedby="state-helper"
          />
        </FormItemWrapper>

        {/* Zip Code */}
        <FormItemWrapper>
          <InputField
            form={form}
            name="zipcode"
            label="Zip Code"
            placeholder="Enter Zip Code"
            disabled
            aria-describedby="zipcode-helper"
          />
        </FormItemWrapper>

        {/* Country */}
        <FormItemWrapper>
          <SelectDropDown
            form={form}
            name="country"
            label="Country"
            optionsList={countryList}
            placeholder="Select Country"
            onChange={(value) => {
              form.setValue('country', value);
              const newCountry = countryData?.find(
                (element) => element.name == value
              );
              form.setValue('state', '');
              setSelectedCountry(newCountry?.country_id as number);
            }}
            validation={{ required: 'Required' }}
            disabled
            aria-labelledby="country-label"
            aria-describedby="country-helper"
          />
        </FormItemWrapper>
      </div>
      <AppConfirmationModal
        title="Confirmation"
        description={
          <div>
            Are you sure you want to
            <span className="font-semibold text-base text-text-Default">
              {' '}
              {form.getValues()?.isActive === 'true'
                ? ' Deactivate'
                : ' Activate'}{' '}
              {form.getValues()?.clientName}
            </span>
            &nbsp;?
          </div>
        }
        open={openDeleteDialog}
        onOpenChange={onOpenChange}
        handleCancel={handleActivationCancel}
        handleSubmit={handleActivation}
        aria-labelledby="confirmation-modal-title"
        aria-describedby="confirmation-modal-description"
      />

      <AppSpinner
        isLoading={isLoading || timeZoneLoader || statesLoading}
        aria-live="polite"
        overlay
      />
    </div>
  );
};

export default NewAccountInformation;
