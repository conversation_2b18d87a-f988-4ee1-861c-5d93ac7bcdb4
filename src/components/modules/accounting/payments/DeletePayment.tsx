import AppButton from '@/components/common/app-button';
import FormActionButtons from '@/components/common/FormActionButtons';
import { InputField, NumberInputField } from '@/components/forms';
import { useDeletePaymentMutation } from '@/redux/features/accounting/payments.api';
import { DeletePaymentFormTypes } from '@/types/accounting.types';
import { CreditCardIcon, Trash2 } from 'lucide-react';
import { useState } from 'react';
import { UseFormReturn } from 'react-hook-form';

interface DeletePaymentProps {
  voidDeletePaymentForm: UseFormReturn<DeletePaymentFormTypes>;
  handleCancel: () => void;
}

const DeletePayment = ({
  voidDeletePaymentForm,
  handleCancel,
}: DeletePaymentProps) => {
  const [actionType, setActionType] = useState<'void' | 'delete' | null>(null);
  const [removePayment, { isLoading }] = useDeletePaymentMutation();

  const handleDeletePayment = (type: 'void' | 'delete') => {
    return async (formData: DeletePaymentFormTypes) => {
      try {
        const payload = {
          paymentId: formData?.id ?? 0,
          voidTransaction: type === 'void',
        };
        await removePayment(payload).unwrap();
        handleCancel();
      } catch (error) {
        // Optional error handling
      } finally {
        setActionType(null);
      }
    };
  };

  return (
    <>
      <div className="px-6 flex flex-col gap-4">
        <div className="flex items-center justify-between">
          <div className=" border rounded-lg p-4">
            <p className="text-sm font-medium">
              Click "Process" to void this transaction
            </p>
          </div>
          <AppButton
            onClick={() => {
              setActionType('void');
              voidDeletePaymentForm.handleSubmit(handleDeletePayment('void'))();
            }}
            label="Process"
            className="w-28"
            variant="primary"
            isLoading={isLoading && actionType === 'void'}
            icon={CreditCardIcon}
          />
        </div>
        <div className="grid grid-cols-2 gap-4">
          <InputField
            name="date"
            form={voidDeletePaymentForm}
            label="Date"
            disabled
          />
          <InputField
            name="type"
            form={voidDeletePaymentForm}
            label="Type"
            disabled
          />
          <NumberInputField
            name="amount"
            form={voidDeletePaymentForm}
            label="Amount"
            disabled
            prefix="$"
            fixedDecimalScale
          />
          <InputField
            name="originalAccount"
            form={voidDeletePaymentForm}
            label="Original Account"
            className="tracking-wide text-base font-sans"
            disabled
          />
          <InputField
            name="emailReceipt"
            form={voidDeletePaymentForm}
            label="E-mail Receipt"
          />
        </div>
      </div>

      <FormActionButtons
        className="sticky bottom-0 bg-white border-t px-6 gap-5"
        onSubmit={() => {
          setActionType('delete');
          voidDeletePaymentForm.handleSubmit(handleDeletePayment('delete'))();
        }}
        submitLabel={'Delete Payment Without Void'}
        onCancel={handleCancel}
        isLoading={isLoading && actionType === 'delete'}
        cancelLabel="Close"
        submitIcon={Trash2}
      />
    </>
  );
};

export default DeletePayment;
