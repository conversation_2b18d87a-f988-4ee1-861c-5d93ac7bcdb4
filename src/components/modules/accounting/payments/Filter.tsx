import { memo, useEffect } from 'react';
import { Submit<PERSON>and<PERSON>, useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';

import { useGetInquiryColumsQuery } from '@/redux/features/accounting/Inquiry.api';
import { RootState } from '@/redux/store';
import {
  clearAllFilters,
  setFilter,
  updateFormValues,
} from '@/redux/features/accounting/accountingCustomersSlice';

import { InputField, SelectDropDown } from '@/components/forms';
import { Separator } from '@/components/ui/separator';
import AppButton from '@/components/common/app-button';

import { generateLabelValuePairs, toCapitalize } from '@/lib/utils';
import {
  OperatorTypeOptions,
  statusListWithAll,
} from '@/constants/common-constants';

interface FilterFormValues {
  isactive: string;
  searchBy: string;
  operator: string;
  searchValue: string;
}

interface FilterProps {
  setIsFilterOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const Filter = ({ setIsFilterOpen }: FilterProps) => {
  const dispatch = useDispatch();
  const formValuesFromRedux = useSelector(
    (state: RootState) => state.accountingCustomers.formValues
  );

  const { data, isLoading } = useGetInquiryColumsQuery();
  const searchByList = generateLabelValuePairs({
    data: data?.data,
    labelKey: 'header',
    valueKey: 'accessorKey',
  })?.filter((item) => item?.value !== 'custtype');

  const form = useForm<FilterFormValues>({
    defaultValues: formValuesFromRedux,
  });

  useEffect(() => {
    const subscription = form.watch((values) => {
      dispatch(updateFormValues(values));
    });

    return () => subscription.unsubscribe();
  }, [form, dispatch]);

  const onSubmit: SubmitHandler<FilterFormValues> = (data) => {
    const key: string = data.searchBy;
    const filterLabel = searchByList?.find(({ value }) => value == key)?.label;

    const newFilterData: any = [
      {
        label: 'Status',
        value: data?.isactive === 'all' ? '' : data?.isactive,
        name: 'isactive',
        tagValue: data?.isactive === 'true' ? 'Active' : 'Inactive',
        operator: 'Equals',
      },
      {
        label: toCapitalize(filterLabel),
        value: key === 'tel1' ? `+1${data.searchValue}` : data.searchValue,
        name: key,
        tagValue: data.searchValue,
        operator: data.operator,
      },
    ];
    dispatch(setFilter(newFilterData));
    setIsFilterOpen(false);
  };

  const handleClear = () => {
    dispatch(clearAllFilters());
    setIsFilterOpen(false);
  };

  const searchBy = form.watch('searchBy');

  return (
    <div className="px-3">
      <div className="text-normal py-2 font-semibold">Filters</div>
      <Separator
        orientation="horizontal"
        className="h-[1px] bg-border-Default mb-2"
      />
      <div className="grid grid-cols-2 gap-3">
        <div className="col-span-2">
          <SelectDropDown
            form={form}
            name="isactive"
            label="Status"
            placeholder="Select Status"
            optionsList={statusListWithAll}
            allowClear={false}
          />
        </div>
        <SelectDropDown
          form={form}
          name="searchBy"
          label="Search By"
          placeholder="Search By"
          optionsList={searchByList}
          isLoading={isLoading}
          onChange={(value) => {
            !value && form.setValue('searchValue', '');
          }}
        />
        <SelectDropDown
          form={form}
          name="operator"
          label="Operator"
          allowClear={false}
          placeholder="Operator"
          optionsList={OperatorTypeOptions}
        />
        <InputField
          form={form}
          name="searchValue"
          disabled={!searchBy}
          label="Value"
          placeholder="Search"
          pClassName="col-span-2"
        />
      </div>
      <div className="flex gap-3 mt-7 sticky bottom-2 bg-white">
        <AppButton
          className="w-full"
          onClick={form.handleSubmit(onSubmit)}
          label="Apply"
        />
        <AppButton
          className="w-full"
          label="Clear"
          variant="neutral"
          onClick={handleClear}
        />
      </div>
    </div>
  );
};

export default memo(Filter);
