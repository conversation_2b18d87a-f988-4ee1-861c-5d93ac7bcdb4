import { PlusIcon } from 'lucide-react';
import { use<PERSON><PERSON>back, useContext, useMemo, useState } from 'react';
import { useNavigate } from 'react-router-dom';

// Components
import AppSpinner from '@/components/common/app-spinner';
import ColumnReOrdering from '@/components/common/column-reording';
import DataTable from '@/components/common/data-tables';
import ActionColumnMenu from '@/components/common/data-tables/ActionColumn';
import { PageHeader } from '@/components/common/PageHeader';

// Constants
import { componentMap } from '@/constants/common-constants';
import { ROUTES } from '@/constants/routes-constants';

// Utils & APIs
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import {
  AppTableContext,
  initialPagination,
} from '@/components/common/app-data-table/AppTableContext';
import { DEFAULT_FORMAT, formatDate, getQueryParam } from '@/lib/utils';
import {
  useDeletePaymentMutation,
  useGetPaymentsColumsQuery,
  useGetPaymentsListQuery,
  useUpdatePaymentColumsMutation,
} from '@/redux/features/accounting/payments.api';
import { DeletePaymentFormTypes } from '@/types/accounting.types';
import { ExtendedPaymentInfoDTO } from '@/types/order.types';
import { Row } from '@tanstack/react-table';
import { useForm } from 'react-hook-form';
import Modal from '../adjustments/add-edit-adjustments/Modal';
import DeletePayment from './DeletePayment';

// Types
interface FilterItem {
  field: string;
  value: string;
  operator: string;
}

interface DeleteState {
  open: boolean;
  id: number | null;
  type: 'confirmation' | 'void' | null;
}

const TABLE_MAX_HEIGHT = 'max-h-[580px] overflow-auto';

const PaymentsList = () => {
  const navigate = useNavigate();

  // Extract query parameters
  const referenceNumber = getQueryParam('referenceNo');
  const customerId = getQueryParam('customerId');

  // Memoized filters based on query parameters
  const appliedFilters = useMemo((): FilterItem[] => {
    const filters: FilterItem[] = [
      {
        field: 'isDeleted',
        value: 'false',
        operator: 'Equals',
      },
    ];

    if (customerId) {
      filters.push({
        field: 'customerId',
        value: customerId,
        operator: 'Equals',
      });
    }

    if (referenceNumber) {
      filters.push({
        field: 'referenceNo',
        value: referenceNumber,
        operator: 'StartsWith',
      });
    }

    return filters;
  }, [customerId, referenceNumber]);

  // Local state
  const [isColumnOrderingOpen, setIsColumnOrderingOpen] =
    useState<boolean>(false);

  const {
    pagination = initialPagination,
    setPagination,
    sorting,
    setSorting,
  } = useContext(AppTableContext) || {};

  const [deleteState, setDeleteState] = useState<DeleteState>({
    open: false,
    id: null,
    type: null,
  });
  const voidDeletePaymentForm = useForm<DeletePaymentFormTypes>();

  // get reorganize columns
  const { data: columnData, isFetching: isFetchingColumns } =
    useGetPaymentsColumsQuery();

  // update reorganize columns
  const [updateColumnOrder, { isLoading: isUpdatingColumnOrder }] =
    useUpdatePaymentColumsMutation();

  // get payment list
  const { data, isLoading } = useGetPaymentsListQuery(
    {
      body: {
        pageNumber: pagination.pageIndex + 1,
        pageSize: pagination.pageSize,
        sortBy: sorting[0].id,
        sortAscending: sorting[0].desc,
        filters: appliedFilters,
      },
    },
    { refetchOnMountOrArgChange: true }
  );

  // delete payment
  const [deletePayment, { isLoading: isDeleteLoading }] =
    useDeletePaymentMutation();

  // Extract table columns from API response
  const tableColumns = useMemo(() => {
    return columnData?.data || [];
  }, [columnData?.data]);

  // Handle column reordering
  const handleColumnReorder = useCallback(
    async (formValues: Record<string, boolean>): Promise<void> => {
      if (!columnData?.data) return;

      try {
        const updatedColumns = columnData?.data.map((column: any) => ({
          ...column,
          enabled: Boolean(formValues[column.accessorKey]),
        }));

        await updateColumnOrder(updatedColumns).unwrap();

        setIsColumnOrderingOpen(false);
      } catch (error) {}
    },
    [columnData?.data, updateColumnOrder]
  );

  // toggle delete
  const toggleConfirmationDelete = useCallback((id: number) => {
    setDeleteState({ open: true, id, type: 'confirmation' });
  }, []);

  const toggleVoidDelete = useCallback((id: number) => {
    setDeleteState({ open: true, id, type: 'void' });
  }, []);

  const handleDelete = useCallback(
    (data: ExtendedPaymentInfoDTO) => {
      if (data.isOnlinePayment && data.transactionOperation === 'PAYMENT') {
        voidDeletePaymentForm.setValue(
          'date',
          formatDate(data?.paymentDate, DEFAULT_FORMAT)
        );
        voidDeletePaymentForm.setValue('type', data?.cardType);
        voidDeletePaymentForm.setValue('amount', data?.amount);
        voidDeletePaymentForm.setValue('originalAccount', data?.cardNumber);
        voidDeletePaymentForm.setValue('emailReceipt', data?.email);
        voidDeletePaymentForm.setValue('id', data?.id);

        toggleVoidDelete(data?.id ?? 0);
      } else {
        toggleConfirmationDelete(data?.id ?? 0);
      }
    },
    [toggleConfirmationDelete, toggleVoidDelete, voidDeletePaymentForm]
  );

  // Navigate to payment form (add/edit)
  const navigateToPaymentForm = useCallback(
    (id?: number): void => {
      const params = new URLSearchParams();

      if (id) params.append('paymentId', id?.toString());
      if (customerId) params.append('customerId', customerId?.toString());
      if (referenceNumber) params.append('referenceNo', referenceNumber);

      const baseRoute = id
        ? ROUTES.ACCOUNTING_EDIT_PAYMENT
        : ROUTES.ACCOUNTING_ADD_PAYMENT;

      navigate(`${baseRoute}?${params?.toString()}`);
    },
    [customerId, navigate, referenceNumber]
  );

  // columns
  const memoizedColumns = useMemo(() => {
    if (!tableColumns?.length) return [];

    const formattedColumns = tableColumns
      .filter((column: any) => column?.enabled)
      .map((column: any) => ({
        accessorKey: column?.accessorKey,
        header: column?.header,
        enableSorting: column?.enableSorting || false,
        size: column?.size || 200,
        cell: ({ row }: any) => {
          if (column?.accessorKey === 'cardType') {
            return row?.original?.cardType == null
              ? row?.original?.paymentMethodName
              : row?.original?.cardType;
          }
          const cellValue = row?.original?.[column?.accessorKey];

          if (typeof cellValue === 'string' && cellValue?.includes('*')) {
            return (
              <span className="tracking-wide text-base font-sans">
                {cellValue}
              </span>
            );
          }

          const Component = componentMap[column.component];
          return Component ? <Component value={cellValue} /> : cellValue;
        },
      }));

    return [
      {
        accessorKey: 'ordering',
        header: () => (
          <ColumnReOrdering
            isOpen={isColumnOrderingOpen}
            handleOrderingColumn={handleColumnReorder}
            tableColumns={tableColumns}
            setOpenColumnOrdering={setIsColumnOrderingOpen}
          />
        ),
        size: 50,
      },
      ...formattedColumns,
      {
        id: 'action',
        size: 110,
        header: 'Actions',
        cell: ({ row }: { row: Row<ExtendedPaymentInfoDTO> }) => {
          return (
            <ActionColumnMenu
              onEdit={() => navigateToPaymentForm(row?.original?.id)}
              onDelete={() => handleDelete(row?.original)}
              contentClassName="w-fit"
            />
          );
        },
      },
    ];
  }, [
    tableColumns,
    isColumnOrderingOpen,
    handleColumnReorder,
    navigateToPaymentForm,
    handleDelete,
  ]);

  const handleCancel = () =>
    setDeleteState({ open: false, id: null, type: null });

  const handleDeletePayment = async () => {
    try {
      await deletePayment({
        paymentId: deleteState?.id ?? 0,
        voidTransaction: false,
      }).unwrap();
      handleCancel();
    } catch (error) {}
  };

  return (
    <div className="flex flex-col gap-2 px-6 gap-x-6">
      {/* Page Header */}
      <PageHeader
        title="Payments"
        cancelPath="/accounting/customers"
        handleSubmit={() => navigateToPaymentForm()}
        icon={PlusIcon}
        label="New payment"
        enableCancel={false}
        titleClassName="text-text-Default"
        className="pb-0"
        disabled={Boolean(referenceNumber)}
      />
      <DataTable
        columns={memoizedColumns}
        data={data?.data ?? []}
        enablePagination={true}
        pagination={pagination}
        setPagination={setPagination}
        sorting={sorting}
        setSorting={setSorting}
        tableClassName={TABLE_MAX_HEIGHT}
        isLoading={isFetchingColumns || isLoading}
        totalItems={data?.pagination?.totalCount}
      />

      <AppSpinner
        overlay
        isLoading={isFetchingColumns || isUpdatingColumnOrder}
      />

      <AppConfirmationModal
        description={
          <div>
            Are you sure you want to delete this payment and its distribution ?
          </div>
        }
        open={deleteState.open && deleteState.type === 'confirmation'}
        handleCancel={handleCancel}
        disabled={isDeleteLoading}
        isLoading={isDeleteLoading}
        handleSubmit={handleDeletePayment}
      />

      <Modal
        open={deleteState.open && deleteState.type === 'void'}
        onOpenChange={handleCancel}
        title="Process Void"
        size="lg"
        height="auto"
        showHeader={true}
        className="p-0"
        contentClassName="md:px-0"
      >
        <DeletePayment
          voidDeletePaymentForm={voidDeletePaymentForm}
          handleCancel={handleCancel}
        />
      </Modal>
    </div>
  );
};

export default PaymentsList;
