import AppButton from '@/components/common/app-button';
import { InputField } from '@/components/forms';
import Labels from '@/components/forms/Label';
import RadioField from '@/components/forms/radio-field';
import { RefundConvFeeTypes } from '@/types/accounting.types';
import { CheckCircle, X } from 'lucide-react';
import { SubmitHandler, UseFormReturn } from 'react-hook-form';

interface RefundConvFeeProps {
  handleRefundConvFee: (data: RefundConvFeeTypes) => void;
  onOpenChange: () => void;
  refundForm: UseFormReturn<RefundConvFeeTypes>;
  isLoading: boolean;
}

const RefundConvFee = ({
  handleRefundConvFee,
  onOpenChange,
  refundForm,
  isLoading,
}: RefundConvFeeProps) => {
  const onSubmit: SubmitHandler<RefundConvFeeTypes> = (formData) => {
    handleRefundConvFee(formData);
  };

  return (
    <div className="flex flex-col gap-6">
      <div className="flex flex-col gap-2">
        <Labels label="Order #" />
        <InputField name="orderNo" form={refundForm} disabled />
      </div>
      <div className="flex flex-col gap-2">
        <p className="text-base text-text-Default font-medium">
          Do you want to refund the convenience fee for this order?
        </p>{' '}
        <RadioField
          name="refundAmountType"
          form={refundForm}
          radioItemClassName="flex items-start space-x-3 p-4 rounded-xl border-2 border-dashed border-gray-400 hover:border-blue-300 hover:bg-blue-50/30 transition-all duration-200 cursor-pointer"
          options={[
            {
              label: `Refund including convenience fee: $ ${refundForm.watch('refundIncludingConvFee')?.toFixed(2) ?? 0.0}`,
              value: 'refundIncludingConvFee',
            },
            {
              label: `Refund for the original amount only: $ ${refundForm.watch('refundOriginalAmount')?.toFixed(2) ?? 0.0}`,
              value: 'refundOriginalAmount',
            },
          ]}
          optionsPerRow={1}
        />
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end space-x-3 mt-6 pt-4">
        <AppButton
          label="OK"
          icon={CheckCircle}
          iconClassName="w-4 h-4"
          className="w-28"
          onClick={refundForm.handleSubmit(onSubmit)}
          isLoading={isLoading}
        />
        <AppButton
          label="Cancel"
          icon={X}
          variant="neutral"
          iconClassName="w-4 h-4"
          className="w-28"
          onClick={onOpenChange}
        />
      </div>
    </div>
  );
};

export default RefundConvFee;
