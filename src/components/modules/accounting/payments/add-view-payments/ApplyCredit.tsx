import { useCallback, useEffect, useMemo } from 'react';
import { <PERSON>mitHandler, UseFormReturn } from 'react-hook-form';

import AppButton from '@/components/common/app-button';
import DataTable from '@/components/common/data-tables';
import Labels from '@/components/forms/Label';
import NumberInputField from '@/components/forms/number-input-field';

import { convertToFloat, getQueryParam } from '@/lib/utils';
import { useGetPaymentDetailsQuery } from '@/redux/features/accounting/payments.api';
import { ApplyCreditForm } from '@/types/accounting.types';

interface ApplyCreditProps {
  onOpenChange: () => void;
  onConfirm: (data: ApplyCreditForm) => void;
  applyCreditForm: UseFormReturn<ApplyCreditForm>;
  applyPayment: any;
}

const useColumns = ({ applyCreditForm, handleAmountOrder }: any) => {
  return useMemo(
    () => [
      {
        accessorKey: 'orderNo',
        header: 'Order #',
        size: 120,
      },
      {
        accessorKey: 'total',
        header: 'Total',
        size: 100,
        maxSize: 120,
        cell: ({ row }: any) =>
          convertToFloat({ value: row.original.total, prefix: '$' }),
      },
      {
        accessorKey: 'balance',
        header: 'Balance',
        size: 100,
        maxSize: 120,
        cell: ({ row }: any) =>
          convertToFloat({ value: row.original.balance, prefix: '$' }),
      },
      {
        accessorKey: 'amountOrder',
        header: 'Amount',
        size: 100,
        cell: ({ row }: any) => (
          <NumberInputField
            name={`payments.${row?.index}.amountOrder`}
            form={applyCreditForm}
            pClassName="p-1"
            className="h-8 w-28"
            placeholder="$0.00"
            onBlur={handleAmountOrder}
            prefix="$"
            fixedDecimalScale
            maxValue={applyCreditForm.watch('amount')}
          />
        ),
      },
    ],
    [applyCreditForm, handleAmountOrder]
  );
};

const ApplyCredit = ({
  onOpenChange,
  onConfirm,
  applyCreditForm,
  applyPayment,
}: ApplyCreditProps) => {
  const paymentId = getQueryParam('paymentId') ?? '';
  const customerId = getQueryParam('customerId') ?? '';
  const { data: paymentData, isLoading: paymentLoading } =
    useGetPaymentDetailsQuery(
      { paymentId, customerId },
      {
        refetchOnMountOrArgChange: true,
      }
    );

  const defaultFormValues = useMemo(
    () => ({
      payments:
        paymentData?.data?.payments
          ?.filter?.(
            (item: any) =>
              item?.orderNo && item?.orderId !== applyPayment?.orderId
          )
          ?.map((item: any) => ({ ...item })) ?? [],
    }),
    [applyPayment?.orderId, paymentData?.data?.payments]
  );

  const { handleSubmit, watch, reset, setValue } = applyCreditForm;

  const watchedPayments = watch('payments');

  const handleAmountOrder = useCallback(() => {
    const calculatedAmount = watchedPayments?.reduce(
      (sum, p) => sum + Number(p?.amountOrder || 0),
      0
    );
    setValue('amount', Number(applyPayment?.amount) - calculatedAmount);
  }, [applyPayment, setValue, watchedPayments]);

  const columns = useColumns({ applyCreditForm, handleAmountOrder });

  const handleCancel = () => {
    reset();
    onOpenChange();
  };
  const onSubmit: SubmitHandler<ApplyCreditForm> = (formData) => {
    onConfirm(formData);
    handleCancel();
  };

  useEffect(() => {
    if (paymentData?.data) {
      applyCreditForm?.reset(defaultFormValues);
    }
  }, [defaultFormValues, applyCreditForm, paymentData?.data]);

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col h-full">
      {/* Header */}
      <div className="px-6  border-b border-gray-200 bg-gray-50">
        <div className="flex gap-2 ">
          <Labels label="Amount" />
          <NumberInputField
            name="amount"
            form={applyCreditForm}
            label=""
            placeholder="$__.__"
            prefix="$"
            disabled
            fixedDecimalScale
            pClassName="w-40"
            className="bg-white"
            allowNegative
          />
        </div>
      </div>

      {/* Table */}
      <div className="flex-1 py-4">
        <DataTable
          data={defaultFormValues.payments}
          columns={columns}
          tableClassName="max-h-[320px] 2xl:max-h-[400px] overflow-auto"
          enablePagination={false}
          isLoading={paymentLoading}
        />
      </div>

      {/* Footer */}
      <div className="px-6 py-2 border-t border-gray-200 bg-gray-50">
        <div className="flex flex-col sm:flex-row gap-3 sm:justify-end">
          <AppButton
            label={'OK'}
            type="submit"
            className=" w-full sm:w-28"
            disabled={!defaultFormValues.payments.length}
          />
          <AppButton
            label="Cancel"
            type="button"
            onClick={handleCancel}
            variant="neutral"
            className=" w-full sm:w-28"
          />
        </div>
      </div>
    </form>
  );
};

export default ApplyCredit;
