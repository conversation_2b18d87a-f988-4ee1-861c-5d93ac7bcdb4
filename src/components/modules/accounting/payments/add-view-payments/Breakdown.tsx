import AppButton from '@/components/common/app-button';
import CustomDialog from '@/components/common/dialog';
import NumberInputField from '@/components/forms/number-input-field';
import { cn, getQueryParam } from '@/lib/utils';
import { PaymentFormTypes } from '@/types/accounting.types';
import { CircleAlert } from 'lucide-react';
import { memo, useState } from 'react';
import { UseFormReturn } from 'react-hook-form';

const Breakdown = ({
  form,
  onChargeChange,
}: {
  form: UseFormReturn<PaymentFormTypes>;
  onChargeChange: (event: React.FocusEvent<HTMLInputElement>) => void;
}) => {
  const paymentId = getQueryParam('paymentId') ?? '';

  const [open, setOpen] = useState<boolean>(false);
  const toggleOpen = () => {
    setOpen((prev) => !prev);
  };

  return (
    <>
      <AppButton
        label=""
        icon={CircleAlert}
        spinnerClass="border-white border-t-transparent animate-spin"
        className="p-2 bg-white border hover:bg-white text-text-Default border-border-Default mt-8"
        iconClassName="w-5"
        onClick={toggleOpen}
        tooltip="Breakdown"
        disabled={!!paymentId}
      />
      <CustomDialog
        onOpenChange={toggleOpen}
        description=""
        open={open}
        className={cn(
          'max-h-[96%] overflow-auto md:max-w-[30%] 2xl:max-w-[20%]'
        )}
        title={'Breakdown'}
        autoFocus={false}
      >
        <div className="px-6">
          <div className="grid grid-cols-1 gap-4">
            <NumberInputField
              name="breakdown.charge"
              form={form}
              label="Charge"
              fixedDecimalScale
              placeholder="$______.__"
              prefix="$"
              onBlur={onChargeChange}
              disabled={!!paymentId}
              allowNegative
            />
            <NumberInputField
              name="breakdown.convenienceFee"
              form={form}
              label="Convenience Fee"
              fixedDecimalScale
              placeholder="$______.__"
              prefix="$"
              disabled
            />
            <NumberInputField
              name="breakdown.tax"
              form={form}
              label="Tax"
              fixedDecimalScale
              placeholder="$______.__"
              prefix="$"
              disabled
            />
            <NumberInputField
              name="breakdown.totalCharge"
              form={form}
              label="Total Charge"
              fixedDecimalScale
              placeholder="$______.__"
              prefix="$"
              disabled
              allowNegative
            />
          </div>

          <div className="w-full h-full justify-end flex py-1 sticky bottom-0 pt-5 bg-white z-30">
            <AppButton
              className="w-28"
              label="Close"
              onClick={toggleOpen}
              variant="neutral"
            />
          </div>
        </div>
      </CustomDialog>
    </>
  );
};

export default memo(Breakdown);
