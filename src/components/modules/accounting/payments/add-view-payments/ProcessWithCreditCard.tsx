import AppButton from '@/components/common/app-button';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import AppSpinner from '@/components/common/app-spinner';
import SwitchField from '@/components/common/switch';
import { SelectWidget } from '@/components/forms';
import DatePicker from '@/components/forms/date-picker';
import InputField from '@/components/forms/input-field';
import NumberInputField from '@/components/forms/number-input-field';
import SelectDropDown from '@/components/forms/select-dropdown';
import ZipCodeInput from '@/components/forms/zipcode-input';
import { EMAIL_VALIDATION_RULEs } from '@/constants/auth-constants';
import { REQUIRED_TEXT } from '@/constants/validation-constants';
import {
  DATE_FORMAT_YYYYMMDD,
  formatDate,
  generateLabelValuePairs,
} from '@/lib/utils';
import { useGetNewPaymentLinkMutation } from '@/redux/features/accounting/payments.api';
import { useGetStateByCountryQuery } from '@/redux/features/country/country.api';
import { useGetCustomerDetailsQuery } from '@/redux/features/customers/customer.api';
import {
  useGetCustomerCardInfoQuery,
  useRemoveCustomerCardMutation,
} from '@/redux/features/orders/order.api';
import {
  PaymentProcessWithCreditCardProps,
  PaymentProcessWithCreditCardTypes,
} from '@/types/accounting.types';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { SubmitHandler, useForm, useWatch } from 'react-hook-form';

const ProcessWithCreditCard = ({
  setOpen,
  data,
  refetchPaymentDetails,
  paymentTypeList,
}: PaymentProcessWithCreditCardProps) => {
  const form = useForm<PaymentProcessWithCreditCardTypes>();
  const [openDelete, setOpenDelete] = useState<boolean>(false);

  // customer details by id
  const customerId = data?.customerId;
  const { data: customerData, isLoading: customerLoading } =
    useGetCustomerDetailsQuery(customerId, {
      skip: !customerId,
    });
  const customerDetails = customerData?.data;
  const countryId = customerDetails?.countryId;

  const defaultValues = useMemo(() => {
    const billingName = [customerDetails?.firstName, customerDetails?.lastName]
      .filter(Boolean)
      .join(' ')
      .trim();

    const billingAddress = [
      customerDetails?.address1,
      customerDetails?.address2,
    ]
      .filter(Boolean)
      .join(' ')
      .trim();
    return {
      date: data?.date || '',
      paymentTypeId: data?.type || '',
      amount: Number(data?.amount) || 0,
      billingName: billingName ?? '',
      reference: data?.reference ?? '',
      email: customerDetails?.emailaddress ?? null,
      billingAddress: billingAddress ?? '',
      billingCity: customerDetails?.city ?? '',
      stateId: customerDetails?.stateId ?? '',
      billingZipCode: customerDetails?.zipcode ?? '',
      updateCustomer: false,
      reuseCC: false,
      cardExpiry: '',
      bankAccountId: data?.bankAccountId || null,
      creditAmount: Number(data?.creditAmount) ?? null,
      convFee: data?.convFee ?? 0,
      convFeeTax: data?.convFeeTax ?? 0,
      paymentRequestList: data?.paymentRequestList,
    };
  }, [
    customerDetails?.address1,
    customerDetails?.address2,
    customerDetails?.city,
    customerDetails?.emailaddress,
    customerDetails?.firstName,
    customerDetails?.lastName,
    customerDetails?.stateId,
    customerDetails?.zipcode,
    data?.amount,
    data?.bankAccountId,
    data?.convFee,
    data?.convFeeTax,
    data?.creditAmount,
    data?.date,
    data?.paymentRequestList,
    data?.reference,
    data?.type,
  ]);

  useEffect(() => {
    if (defaultValues) {
      form.reset(defaultValues);
    }
  }, [defaultValues, form]);

  const selectedCardId = form.watch('reuseOption');

  const isReuseCCChecked = useWatch({
    control: form.control,
    name: 'reuseCC',
  });

  const isAchType = data?.type == '13'; // If type is ACH then disable the reuse CC/ACH checkbox

  const handleProcess = () => {
    setOpen({ state: false, action: '' });
  };

  // Customer card info API
  const { data: customerCardInfoData } = useGetCustomerCardInfoQuery(
    { customerId },
    {
      skip: !customerId,
    }
  );
  const customerCardList = generateLabelValuePairs({
    data: customerCardInfoData?.data,
    labelKey: 'cardNumber',
    valueKey: 'id',
  });
  const [RemoveCustomerCard, { isLoading: deleteLoading }] =
    useRemoveCustomerCardMutation();

  const toggleDelete = useCallback(() => {
    setOpenDelete((prev) => !prev);
  }, []);

  const handleDelete = useCallback(async () => {
    try {
      await RemoveCustomerCard(selectedCardId).unwrap();
      toggleDelete();
      form.setValue('reuseOption', null);
    } catch (err) {}
  }, [RemoveCustomerCard, form, selectedCardId, toggleDelete]);

  // get payment link API
  const [getNewPaymentLink, { isLoading: isLoadingPaymentLink }] =
    useGetNewPaymentLinkMutation();

  // state data
  const { data: statesData, isFetching: stateIsLoading } =
    useGetStateByCountryQuery(
      {
        countryId: countryId,
      },
      { skip: !countryId }
    );
  // states list
  const stateList = generateLabelValuePairs({
    data: statesData,
    labelKey: 'code',
    valueKey: 'state_id',
  });

  const onProcessSubmit: SubmitHandler<
    PaymentProcessWithCreditCardTypes
  > = async (formData) => {
    const { paymentRequestList, cardExpiry, ...paymentData } = formData;
    const formFields = {
      paymentRequest: {
        ...paymentData,
        amount: Number(data?.charge),
        date: formatDate(formData.date, DATE_FORMAT_YYYYMMDD),
        stateId: paymentData?.stateId ?? null,
        customerCardDetailId: formData?.reuseOption || null,
        customerId: Number(customerId),
        distributionId: data?.distributionId,
      },
      paymentRequestList: paymentRequestList,
    };

    try {
      if (isReuseCCChecked) {
        const responsePaymentLink =
          await getNewPaymentLink(formFields).unwrap();
        if (responsePaymentLink && responsePaymentLink?.data) {
          handleProcess();
        }
      } else {
        const responsePaymentLink =
          await getNewPaymentLink(formFields).unwrap();
        if (responsePaymentLink && responsePaymentLink?.data) {
          handleProcess();
          const url = responsePaymentLink.data;
          const newTab = window.open(url, '_blank');

          if (newTab) {
            let hasRefetched = false;

            const checkTabClosed = setInterval(() => {
              if (newTab.closed && !hasRefetched) {
                hasRefetched = true;
                clearInterval(checkTabClosed);
                refetchPaymentDetails();
              }
            }, 500);
          }
        }
      }
    } catch (error) {}
  };

  const onChangeReuseOption = (value: string) => {
    const selectedCard = customerCardInfoData?.data?.find(
      (card: any) => card?.id === value
    );
    const expiry = selectedCard?.cardExpiry || '';
    const month = expiry?.slice(0, 2);
    const year = expiry?.slice(-2);
    form.setValue('cardExpiry', expiry ? `${month}/${year}` : '');
  };

  return (
    <>
      <div className="space-y-3 md:space-y-4 px-6">
        {/* Date, Type, Amount */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 border p-4 rounded-lg">
          <NumberInputField
            form={form}
            name="amount"
            label="Amount"
            placeholder="Amount"
            prefix="$"
            fixedDecimalScale
            disabled
            allowNegative
          />
          <DatePicker
            name="date"
            form={form}
            enableInput
            label="Date"
            disabled
          />
          <SelectDropDown
            form={form}
            optionsList={paymentTypeList ?? []}
            name="paymentTypeId"
            label="Type"
            placeholder="Select Type"
            disabled
          />
        </div>

        {/* Cardholder Info */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 border rounded-lg p-4">
          <div className="col-span-2 gap-2 md:flex md:gap-2 items-center">
            <h3 className="text-lg font-semibold">Cardholder Info</h3>
            <div className="text-sm">
              ( * Missing info may result in higher fees )
            </div>
          </div>
          <InputField
            name="billingName"
            label="Name"
            form={form}
            placeholder="Name"
          />
          <InputField
            name="email"
            label="E-mail Receipt"
            form={form}
            placeholder="Email"
            validation={EMAIL_VALIDATION_RULEs}
          />
          <InputField
            name="billingAddress"
            label="Address"
            form={form}
            placeholder="Address"
          />
          <InputField
            name="billingCity"
            label="City"
            form={form}
            placeholder="City"
          />
          <SelectWidget
            name="stateId"
            form={form}
            placeholder="Select State"
            label="State"
            optionsList={stateList}
            isLoading={stateIsLoading}
            menuPosition="absolute"
          />
          <ZipCodeInput
            name="billingZipCode"
            label="Zip Code"
            form={form}
            isUSA={countryId === 1}
          />
          <div className="col-span-2 md:flex items-center md:gap-2">
            <SwitchField
              labelEnd="Update Customer Record"
              name="updateCustomer"
              form={form}
              switchClassName="justify-start"
            />

            <div className="text-sm ps-12 md:p-0">
              ( * Does not update customer name)
            </div>
          </div>
        </div>

        {/* Reuse CC/ACH section */}
        <div className="grid grid-cols-1 items-start md:flex gap-4 border rounded-lg p-4">
          <SwitchField
            labelEnd="Reuse CC/ACH"
            name="reuseCC"
            form={form}
            disabled={isAchType}
            className="mt-2"
            switchClassName="justify-start w-[170px]"
            onChange={(value) => !value && form.clearErrors('reuseOption')}
          />
          <SelectWidget
            form={form}
            name="reuseOption"
            optionsList={customerCardList ?? []}
            placeholder="Select Option"
            disabled={!isReuseCCChecked}
            onSelectChange={onChangeReuseOption}
            menuPosition="absolute"
            numericOnly
            validation={{ required: isReuseCCChecked ? REQUIRED_TEXT : '' }}
            className="tracking-wide text-base font-sans"
          />
          <div className="flex items-center gap-3 w-full">
            <InputField
              name="cardExpiry"
              label=""
              form={form}
              placeholder="Card Expiry"
              disabled
              pClassName="w-full"
            />
            <AppButton
              label="Remove"
              onClick={toggleDelete}
              variant="neutral"
              disabled={!isReuseCCChecked || !selectedCardId}
            />
          </div>
        </div>
      </div>
      {/* Footer Buttons */}
      <div className="mt-3 md:mt-4 sticky bottom-0 bg-white py-3 border-t flex justify-between md:justify-end items-center gap-4 w-full px-6">
        <AppButton
          label="Process"
          onClick={form.handleSubmit(onProcessSubmit)}
          className="w-32"
          isLoading={isLoadingPaymentLink}
        />
        <AppButton
          label="Close"
          onClick={handleProcess}
          className="w-32"
          variant="neutral"
          disabled={isLoadingPaymentLink}
        />
      </div>
      <AppConfirmationModal
        title={'Confirmation'}
        description={<div>Are you sure you want to remove ? </div>}
        open={openDelete}
        onOpenChange={toggleDelete}
        handleCancel={toggleDelete}
        handleSubmit={handleDelete}
        isLoading={deleteLoading}
      />
      <AppSpinner overlay isLoading={customerLoading} />
    </>
  );
};

export default ProcessWithCreditCard;
