import {
  calculateConvenienceFeeByType,
  getUnappliedAmount,
} from '@/lib/paymentCalculations';
import { normalizeToPositive } from '@/lib/utils';
import {
  ApplyCreditForm,
  ModalKeys,
  PaymentFormTypes,
  TransactionType,
} from '@/types/accounting.types';
import { UseFormReturn } from 'react-hook-form';

export const createHandlers = ({
  form,
  applyCreditForm,
  toggleModal,
  setApplyPayment,
  calculateConvenienceFee,
  recalculateTotals,
  previousValueRef,
}: {
  form: UseFormReturn<PaymentFormTypes>;
  applyCreditForm: UseFormReturn<ApplyCreditForm>;
  toggleModal: (key?: ModalKeys) => void;
  setApplyPayment: any;
  calculateConvenienceFee: (
    payment: string,
    orderId: string,
    index: number
  ) => void;
  recalculateTotals: (charge: number) => void;
  previousValueRef: React.MutableRefObject<Record<number, string>>;
}) => {
  const handlePayment = (event: any, row: any) => {
    const key = row.index;
    const orderId = row?.original?.orderId;
    const adjustmentId = row?.original?.adjustmentId;
    const balance = row?.original?.balance;
    const paymentType = form.watch('paymentTypeLabel');
    const adjustmentType = form.watch(`payments.${key}.adjustmentType`);
    const storePaymentConfig = form.watch('storePaymentConfig');
    const charge = Number(form.watch('breakdown.charge')) || 0;

    const rawValue =
      event?.target?.value?.replace('$', '').replace(/,/g, '') || '';
    const newValue = Number(rawValue);
    const lastCalledValue = previousValueRef?.current[key] ?? '';

    // If no orderId or value hasn't changed, do nothing
    if ((!orderId && !adjustmentId) || rawValue === lastCalledValue) return;

    // Save last value to avoid redundant processing
    previousValueRef.current[key] = rawValue;

    if (
      (adjustmentId && balance > 0 && newValue > balance) ||
      (balance < 0 && newValue < balance)
    ) {
      toggleModal('adjustmentOverBalance');
      form.setValue(`payments.${key}.payment`, '');
      form.setValue(`payments.${key}.ccConvFee`, '');
      form.setValue(`payments.${key}.tax`, '');
      recalculateTotals(charge);
      return;
    }

    // Handle manual convenience fee for adjustment payments
    else if (adjustmentId && newValue > 0) {
      const isOverpayment = adjustmentType === TransactionType.OVERPAYMENT;
      const fee = !isOverpayment
        ? calculateConvenienceFeeByType(
            paymentType,
            newValue,
            storePaymentConfig
          )
        : '';

      form.setValue(`payments.${key}.ccConvFee`, fee);
      form.setValue(`payments.${key}.tax`, 0);
      recalculateTotals(charge);
      return;
    }

    // For regular order payments
    calculateConvenienceFee(rawValue, orderId, key);

    // Handle negative value: apply credit flow
    if (newValue < 0) {
      toggleModal('applyCredit');
      const amount = normalizeToPositive(newValue);
      applyCreditForm.setValue('amount', amount);

      setApplyPayment({
        amount,
        orderId: row.original.orderId,
        adjustmentId: row?.original.adjustmentId,
      });

      const payments = form.getValues('payments');
      const children = payments[key]?.appliedCreditChildren || [];

      if (children.length) {
        // Build a map of orderId to their index for quick lookup
        const creditMap = new Map(payments.map((p, i) => [p.orderId, i]));

        // Revert applied credit from child rows
        children.forEach(({ orderId: childOrderId, creditAmount }) => {
          const matchIndex = creditMap.get(childOrderId);

          if (matchIndex !== undefined) {
            const currentCredit = Number(
              payments[matchIndex]?.creditAmount || 0
            );
            const updatedCredit = currentCredit - Number(creditAmount);
            form.setValue(
              `payments.${matchIndex}.creditAmount`,
              updatedCredit === 0 ? '' : updatedCredit
            );
          }
        });
      }

      // Clear applied credit children after reversion
      form.setValue(`payments.${key}.appliedCreditChildren`, []);
    }
  };

  const handleApplyCredit = (event: any, row: any) => {
    const key = row.index;
    const orderId = row?.original?.orderId;
    const newValue = event?.target?.value?.replace('$', '');
    const lastCalledValue = previousValueRef?.current[key] ?? '';

    // If no orderId or value hasn't changed, do nothing
    if (!orderId || newValue === lastCalledValue) return;

    // Store the new value in the ref
    previousValueRef.current[key] = newValue;

    const payments = form.watch('payments') || [];

    const amount = Number(form.getValues('breakdown.charge')) || 0;
    const creditAmount = Number(form.getValues('creditAmount')) || 0;
    const totalPayment = Number(form.getValues('totalPayment')) || 0;
    const totalAppliedCredit = payments?.reduce((sum, row) => {
      return sum + Number(row.creditAmount || 0);
    }, 0);

    const newUnapplied = getUnappliedAmount({
      amount,
      payments: totalPayment,
      creditAmount,
      applyCredit: totalAppliedCredit,
    });
    form.setValue('unapplied', newUnapplied);
    form.setValue('totalAppliedCredit', totalAppliedCredit);
  };

  return {
    handlePayment,
    handleApplyCredit,
  };
};
