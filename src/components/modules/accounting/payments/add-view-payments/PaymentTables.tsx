import AppSpinner from '@/components/common/app-spinner';
import DataTable from '@/components/common/data-tables';
import { getUnappliedAmount } from '@/lib/paymentCalculations';
import { getQueryParam } from '@/lib/utils';
import { useCalculateConvFeeTaxMutation } from '@/redux/features/orders/order.api';
import {
  ApplyCreditForm,
  ModalKeys,
  PaymentFormTypes,
} from '@/types/accounting.types';
import { useMemo, useRef } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { createHandlers } from './createHandlers';
import { getOrderTableColumns } from './getOrderTableColumns';

interface ItemDetailsProps {
  form: UseFormReturn<PaymentFormTypes>;
  isLoading: boolean;
  toggleModal: (key?: ModalKeys) => void;
  applyCreditForm: UseFormReturn<ApplyCreditForm>;
  setApplyPayment: any;
}

const OrderTable = ({
  form,
  isLoading,
  toggleModal,
  applyCreditForm,
  setApplyPayment,
}: ItemDetailsProps) => {
  const batchId = getQueryParam('batchId');
  const previousValueRef = useRef<Record<number, string>>({});

  // Determines whether the given row type indicates a sticky row.
  const isStickyRow = (rowType?: string): boolean => rowType === 'STICKY';

  // calculate convence fees and tax
  const [calculateConvFeeTax, { isLoading: convLoading }] =
    useCalculateConvFeeTaxMutation();

  const recalculateTotals = (charge: number) => {
    const payments = form.watch('payments') || [];

    const { payment, ccConvFee, tax } = payments.reduce(
      (acc, item) => ({
        payment: acc.payment + Number(item.payment || 0),
        ccConvFee: acc.ccConvFee + Number(item.ccConvFee || 0),
        tax: acc.tax + Number(item.tax || 0),
      }),
      { payment: 0, ccConvFee: 0, tax: 0 }
    );

    const totalCharge = charge + ccConvFee + tax;
    const creditAmount = Number(form.watch('creditAmount') || 0);
    const totalAppliedCredit = Number(form.watch('totalAppliedCredit') || 0);
    const unapplied = getUnappliedAmount({
      amount: charge,
      payments: payment,
      creditAmount,
      applyCredit: totalAppliedCredit,
    });

    form.setValue('breakdown', {
      charge,
      convenienceFee: ccConvFee,
      tax,
      totalCharge,
    });
    form.setValue('amount', totalCharge);
    form.setValue('unapplied', unapplied);
    form.setValue('totalPayment', payment);
    form.setValue('totalconvenienceFee', ccConvFee);
    form.setValue('totalTax', tax);
  };

  const calculateConvenienceFee = async (
    payment: string,
    orderId: string,
    index: number
  ) => {
    const paymentAmount = Number(payment);
    const paymentTypeId = form.watch('type');
    const charge = Number(form.watch('breakdown.charge')) || 0;

    // Step 1: If payment is positive, calculate conv fee + tax via API
    if (paymentAmount > 0) {
      try {
        const { data } = await calculateConvFeeTax({
          orderId,
          paymentTypeId: paymentTypeId,
          amount: paymentAmount,
        }).unwrap();
        form.setValue(`payments.${index}.ccConvFee`, data?.convFee ?? 0);
        form.setValue(`payments.${index}.tax`, data?.convFeeTax ?? 0);
      } catch (error) {
        form.setValue(`payments.${index}.ccConvFee`, 0);
        form.setValue(`payments.${index}.tax`, 0);
      }
    } else {
      // Reset values to zero for negative/zero payment
      form.setValue(`payments.${index}.ccConvFee`, 0);
      form.setValue(`payments.${index}.tax`, 0);
    }

    recalculateTotals(charge);
  };

  const { handlePayment, handleApplyCredit } = createHandlers({
    form,
    applyCreditForm,
    toggleModal,
    setApplyPayment,
    calculateConvenienceFee,
    recalculateTotals,
    previousValueRef,
  });

  const columns = useMemo(
    () =>
      getOrderTableColumns({
        form,
        handlePayment,
        handleApplyCredit,
      }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [form, batchId]
  );

  // get total
  const getTotals: any = (data: any) => {
    if (!data?.length) return [];
    const excludeKeys = ['order', 'dateOfUse'];

    const keysToSum = Object.keys(data[0])?.filter(
      (key) => !excludeKeys?.includes(key)
    );

    const totals = keysToSum?.reduce((acc: Record<string, number>, key) => {
      acc[key] = 0;
      return acc;
    }, {});

    data?.forEach((item: Record<string, any>) => {
      keysToSum?.forEach((key) => {
        totals[key] += parseFloat(item[key]) || 0;
      });
    });
    return [
      ...data,
      { ...totals, orderNo: '', adjustmentNo: null, stickyId: 'STICKY' },
    ];
  };

  const data = form.watch('payments');
  const total = getTotals(data);

  return (
    <>
      <DataTable
        data={total}
        columns={columns}
        enablePagination={false}
        tableClassName="max-h-[450px] 2xl:max-h-[80dvh] overflow-auto"
        isLoading={isLoading}
        highlightKey="isPostedOrder"
        highlightClassName="bg-green-200 hover:bg-green-200"
        getRowClassName={(row) =>
          isStickyRow(row.original.stickyId)
            ? 'sticky bottom-0 bg-grayScale-10 hover:bg-grayScale-10 font-semibold'
            : row.original.orderId
              ? 'bg-green-200 hover:bg-green-200'
              : ''
        }
      />
      <AppSpinner isLoading={convLoading} overlay />
    </>
  );
};

export default OrderTable;
