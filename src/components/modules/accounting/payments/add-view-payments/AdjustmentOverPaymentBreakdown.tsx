import AppButton from '@/components/common/app-button';
import { NumberInputField } from '@/components/forms';
import Labels from '@/components/forms/Label';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { PaymentsItemTypes } from '@/types/accounting.types';
import { CircleAlert } from 'lucide-react';
import { useForm } from 'react-hook-form';

interface AdjustmentBreakdownProps {
  row: PaymentsItemTypes;
}

const AdjustmentOverPaymentBreakdown = ({ row }: AdjustmentBreakdownProps) => {
  const form = useForm();
  if (row?.adjustmentType !== 'OVERPAYMENT') return null;

  // list
  const breakdownItems = [
    { label: 'Sub Total', value: Number(row?.amount || 0) },
    { label: 'Convenience Fee', value: Number(row?.ccConvFee || 0) },
    {
      label: 'Total',
      value: Number(row?.amount || 0) + Number(row?.ccConvFee || 0),
    },
    { label: 'To Be Applied', value: Number(row?.amount || 0) },
  ];

  return (
    <Popover>
      <PopoverTrigger>
        <AppButton
          label=""
          icon={CircleAlert}
          className="border-none p-0 cursor-auto"
          iconClassName="w-5 h-5"
          variant="neutral"
          tooltip="Breakdown"
        />
      </PopoverTrigger>

      <PopoverContent
        className="w-[350px] border-none"
        side="top"
        hideWhenDetached={false}
      >
        <div className="text-sm border rounded-lg p-3 space-y-3">
          {breakdownItems?.map(({ label, value }, index) => (
            <div
              key={`${label}-${index}`}
              className="grid grid-cols-2 items-center gap-4"
            >
              <Labels label={label} className="font-medium" />
              <NumberInputField
                form={form}
                name={`breakdown-${label}`}
                defaultValue={value}
                prefix="$"
                className="h-8"
                fixedDecimalScale
                thousandSeparator=","
                allowNegative
                disabled
              />
            </div>
          ))}
        </div>
      </PopoverContent>
    </Popover>
  );
};
export default AdjustmentOverPaymentBreakdown;
