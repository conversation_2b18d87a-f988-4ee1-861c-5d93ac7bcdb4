import NumberInputField from '@/components/forms/number-input-field';
import { cn, convertToFloat, formatDate, getQueryParam } from '@/lib/utils';
import { PaymentFormTypes, PaymentsItemTypes } from '@/types/accounting.types';
import { ColumnDef } from '@tanstack/react-table';
import { UseFormReturn } from 'react-hook-form';

interface UsePaymentListColumnsProps {
  form: UseFormReturn<PaymentFormTypes>;
  handlePayment: (event: any, index: any) => void;
  handleApplyCredit: (event: any, index: any) => void;
}

export const getOrderTableColumns = ({
  form,
  handlePayment,
  handleApplyCredit,
}: UsePaymentListColumnsProps): ColumnDef<PaymentsItemTypes>[] => {
  const paymentId = getQueryParam('paymentId');
  const isStickyRow = (rowType?: string): boolean => rowType === 'STICKY';

  return [
    {
      accessorKey: 'orderNo',
      header: 'Order #',
      size: 100,
      cell: ({ row }) =>
        row?.original?.adjustmentNo
          ? row?.original?.adjustmentNo
          : row?.original?.orderNo,
    },
    {
      accessorKey: 'dateOfUse',
      header: 'Date of Use',
      size: 130,
      cell: ({ row }) => formatDate(row?.original?.dateOfUse),
    },
    {
      accessorKey: 'total',
      header: 'Total',
      size: 120,
      cell: ({ row }) => (
        <div className={cn(Number(row?.original?.total) >= 0 && 'pl-2')}>
          {convertToFloat({
            value: row?.original?.total,
            prefix: '$',
          })}
        </div>
      ),
    },
    {
      accessorKey: 'balance',
      header: 'Balance',
      size: 120,
      cell: ({ row }) => (
        <div className={cn(Number(row?.original?.balance) >= 0 && 'pl-2')}>
          {convertToFloat({
            value: row?.original?.balance,
            prefix: '$',
          })}
          {/* <AdjustmentOverPaymentBreakdown row={row?.original} /> */}
        </div>
      ),
    },
    {
      accessorKey: 'payment',
      header: 'Payment',
      size: 120,
      cell: ({ row }) =>
        isStickyRow(row.original.stickyId) || !!paymentId ? (
          <div className="ps-4">
            {convertToFloat({
              value: row?.original?.payment,
              prefix: '$',
            })}
          </div>
        ) : (
          <NumberInputField
            form={form}
            name={`payments.${row.index}.payment`}
            placeholder="Payment"
            maxLength={11}
            prefix="$"
            className="w-28 h-8"
            fixedDecimalScale
            thousandSeparator=","
            pClassName="p-1"
            allowNegative
            disabled={!!paymentId}
            onBlur={(event) => {
              handlePayment(event, row);
            }}
          />
        ),
    },
    {
      accessorKey: 'creditAmount',
      header: 'Applied Credit',
      size: 150,
      cell: ({ row }) =>
        isStickyRow(row?.original?.stickyId) || !!paymentId ? (
          <div className="ps-4 py-1">
            {convertToFloat({
              value: row?.original?.creditAmount,
              prefix: '$',
            })}
          </div>
        ) : (
          <NumberInputField
            form={form}
            name={`payments.${row.index}.creditAmount`}
            placeholder="Applied Credit"
            maxLength={11}
            prefix="$"
            className="w-28 h-8"
            fixedDecimalScale
            thousandSeparator=","
            pClassName="p-1"
            allowNegative
            disabled={!!paymentId}
            onBlur={(event) => {
              handleApplyCredit(event, row);
            }}
          />
        ),
    },
    {
      accessorKey: 'ccConvFee',
      header: 'Conv Fee',
      size: 120,
      cell: ({ row }) => (
        <div
          className={cn(
            (Number(form.watch(`payments.${row.index}.ccConvFee`)) >= 0 ||
              Number(row.original.ccConvFee) >= 0) &&
              'pl-2'
          )}
        >
          {convertToFloat({
            value:
              form.watch(`payments.${row.index}.ccConvFee`) ||
              row.original.ccConvFee,
            prefix: '$',
          })}
        </div>
      ),
    },
    {
      accessorKey: 'tax',
      header: 'Tax',
      size: 80,
      cell: ({ row }) =>
        convertToFloat({
          value: form.watch(`payments.${row.index}.tax`) || row.original.tax,
          prefix: '$',
        }),
    },
    {
      accessorKey: 'discount',
      header: 'Discount',
      size: 100,
      cell: ({ row }) =>
        isStickyRow(row?.original?.stickyId) || !!paymentId ? (
          <div className="ps-4">
            {convertToFloat({
              value: row?.original?.discount,
              prefix: '$',
            })}
          </div>
        ) : (
          <NumberInputField
            form={form}
            name={`payments.${row.index}.discount`}
            placeholder="Discount"
            maxLength={11}
            prefix="$"
            className="w-28 h-8"
            fixedDecimalScale
            thousandSeparator=","
            pClassName="p-1"
            allowNegative
            disabled={!!paymentId}
          />
        ),
    },
  ];
};
