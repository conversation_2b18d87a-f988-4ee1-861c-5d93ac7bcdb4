import DataTable from '@/components/common/data-tables';
import { convertToFloat } from '@/lib/utils';
import { ColumnDef } from '@tanstack/react-table';
import { useMemo } from 'react';

interface CreditAmountType {
  creditMemo: string;
  creditAmount: number;
}

interface CreditMemoAmountTableProps {
  data: CreditAmountType[];
}

const CreditMemoAmountTable = ({ data }: CreditMemoAmountTableProps) => {
  const columns: ColumnDef<CreditAmountType>[] = useMemo(
    () => [
      {
        accessorKey: 'creditMemo',
        header: 'Credit Memo #',
        size: 80,
      },
      {
        accessorKey: 'creditAmount',
        header: 'Credit Amount',
        size: 80,
        cell: ({ row }) =>
          convertToFloat({
            value: row?.original?.creditAmount,
            prefix: '$',
          }),
      },
    ],
    []
  );

  return (
    <div className="col-span-2 pt-1">
      <DataTable
        data={data}
        columns={columns}
        enablePagination={false}
        tableClassName="max-h-[200px] overflow-auto"
      />
    </div>
  );
};

export default CreditMemoAmountTable;
