import AppButton from '@/components/common/app-button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { cn, convertToFloat, getQueryParam } from '@/lib/utils';
import { useLoadAdjustmentDistributionQuery } from '@/redux/features/accounting/adjustments.api';
import { LoadAdjustmentDistributionTypes } from '@/types/accounting.types';
import { CheckCircle, CheckIcon, X } from 'lucide-react';
import { useMemo } from 'react';

interface LoadAdjustmentDistributionProps {
  onOpenChange: () => void;
  onConfirm: (data: any) => void;
  selectedAdjustmentId: number | null;
  setSelectedAdjustmentId: React.Dispatch<React.SetStateAction<number | null>>;
}

type TableRowTypes = {
  id: number;
  adjustmentNo: string;
  adjustmentId: number;
  adjustmentAmount: number;
  orderNo: string;
  orderId: number;
  amount: number;
  rowSpan: number;
  isFirstRow: boolean;
};

// Skeleton component for table rows
const TableRowSkeleton = () => (
  <TableRow className="animate-pulse">
    <TableCell>
      <div className="w-5 h-5 bg-gray-200 rounded"></div>
    </TableCell>
    <TableCell>
      <div className="h-5 bg-gray-200 rounded w-20"></div>
    </TableCell>
    <TableCell>
      <div className="h-5 bg-gray-200 rounded w-24"></div>
    </TableCell>
    <TableCell>
      <div className="h-5 bg-gray-200 rounded w-24"></div>
    </TableCell>
    <TableCell>
      <div className="h-5 bg-gray-200 rounded w-20"></div>
    </TableCell>
  </TableRow>
);

function flattenAdjustments(
  data: LoadAdjustmentDistributionTypes[]
): TableRowTypes[] {
  const result: TableRowTypes[] = [];

  data?.forEach((adjustment) => {
    adjustment.distributions.forEach((dist, index) => {
      result.push({
        id: adjustment.id,
        adjustmentNo: adjustment.adjustmentNo,
        adjustmentId: adjustment.adjustmentId,
        adjustmentAmount: adjustment.adjustmentAmount,
        orderNo: dist.orderNo,
        orderId: dist.orderId,
        amount: dist.amount,
        rowSpan: index === 0 ? adjustment.distributions.length : 0,
        isFirstRow: index === 0,
      });
    });
  });

  return result;
}

const LoadAdjustmentDistribution = ({
  onOpenChange,
  onConfirm,
  selectedAdjustmentId,
  setSelectedAdjustmentId,
}: LoadAdjustmentDistributionProps) => {
  const customerId = getQueryParam('customerId') ?? '';

  const { data, isLoading } = useLoadAdjustmentDistributionQuery(customerId);

  const tableData = useMemo(() => {
    return flattenAdjustments(data?.data ?? []);
  }, [data?.data]);

  const handleAdjustmentSelection = (adjustmentId: number) => {
    setSelectedAdjustmentId((prev) =>
      prev === adjustmentId ? null : adjustmentId
    );
  };

  const handleOK = () => {
    const selectedData = data?.data?.filter(
      (adj) => adj.adjustmentId === selectedAdjustmentId
    );
    onConfirm(selectedData);
    onOpenChange();
  };

  return (
    <>
      <div className="border border-gray-200 rounded-lg shadow-sm">
        <Table className="w-full" tableClassName="max-h-96 overflow-auto">
          <TableHeader className="sticky top-0 z-10 bg-gray-100 shadow-sm">
            <TableRow>
              <TableHead className="w-12  bg-gray-100 sticky top-0 z-10 border-gray-300  border-r-1"></TableHead>
              <TableHead className="font-medium text-grayScale-90 sticky top-0 bg-gray-100 z-10 ">
                Adjustment #
              </TableHead>

              <TableHead className=" font-medium text-grayScale-90 sticky top-0 bg-gray-100 z-10">
                Applied to Order #
              </TableHead>
              <TableHead className="font-medium text-grayScale-90 sticky top-0 bg-gray-100 z-10">
                Distributed Amount
              </TableHead>
              <TableHead className=" font-medium text-grayScale-90 sticky top-0 bg-gray-100 z-10">
                Adjustment Amount
              </TableHead>
            </TableRow>
          </TableHeader>

          <TableBody className="bg-white divide-gray-200 py-0">
            {isLoading
              ? // Render skeleton rows while loading
                Array.from({ length: 5 }).map((_, index) => (
                  <TableRowSkeleton key={index} />
                ))
              : tableData?.map((item: any, index: number) => (
                  <TableRow
                    key={`${item.adjustmentId}-${index}`}
                    className={cn(
                      'hover:bg-muted transition-colors',
                      selectedAdjustmentId === item.adjustmentId &&
                        'bg-green-50'
                    )}
                  >
                    {item.isFirstRow && (
                      <TableCell rowSpan={item.rowSpan} className="py-2">
                        <Checkbox
                          checked={selectedAdjustmentId === item.adjustmentId}
                          onCheckedChange={() =>
                            handleAdjustmentSelection(item.adjustmentId)
                          }
                          className={cn(
                            selectedAdjustmentId === item.adjustmentId
                              ? 'data-[state=checked]:bg-grayScale-600 data-[state=checked]:text-primary-foreground'
                              : 'border-grayScale-400 border-2',
                            'w-5 h-5'
                          )}
                        >
                          <CheckIcon className="w-4 h-4" />
                        </Checkbox>
                      </TableCell>
                    )}

                    {item.isFirstRow && (
                      <TableCell
                        rowSpan={item.rowSpan}
                        className="text-sm border py-2"
                      >
                        {item.adjustmentNo}
                      </TableCell>
                    )}

                    <TableCell className="text-sm border py-2">
                      {item.orderNo}
                    </TableCell>
                    <TableCell className="text-sm border py-2">
                      {convertToFloat({
                        value: item?.amount?.toFixed(2),
                        prefix: '$',
                      })}
                    </TableCell>
                    {item.isFirstRow && (
                      <TableCell
                        rowSpan={item.rowSpan}
                        className="text-sm border py-2"
                      >
                        {convertToFloat({
                          value: item?.adjustmentAmount?.toFixed(2),
                          prefix: '$',
                        })}
                      </TableCell>
                    )}
                  </TableRow>
                ))}
          </TableBody>
        </Table>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end space-x-3 mt-6 pt-4">
        <AppButton
          label="OK"
          icon={CheckCircle}
          onClick={handleOK}
          iconClassName="w-4 h-4"
          className="w-28"
        />
        <AppButton
          label="Cancel"
          icon={X}
          variant="neutral"
          iconClassName="w-4 h-4"
          className="w-28"
          onClick={onOpenChange}
        />
      </div>
    </>
  );
};

export default LoadAdjustmentDistribution;
