import AppButton from '@/components/common/app-button';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import AppSpinner from '@/components/common/app-spinner';
import CustomDialog from '@/components/common/dialog';
import { PageHeader } from '@/components/common/PageHeader';
import { InputField, NumberInputField, SelectWidget } from '@/components/forms';
import DatePicker from '@/components/forms/date-picker';
import CreditCards from '@/components/modules/orders/total-payments/new-payment/CreditCards';
import { CARD_TYPE_MAPPING } from '@/components/modules/orders/total-payments/NewPayment';

import {
  BANK_ACCOUNTS_API_ROUTES,
  PAYMENT_TYPE_API_ROUTES,
} from '@/constants/api-constants';
import { TEXT_VALIDATION_RULE } from '@/constants/validation-constants';
import useOptionList, { OptionListType } from '@/hooks/useOptionList';
import { formatDateWithTimezone } from '@/lib/formatDateWithTimezone';
import {
  calculateConvenienceFeeByType,
  getUnappliedAmount,
  mapRefundFormValues,
} from '@/lib/paymentCalculations';
import {
  cn,
  DATE_FORMAT_YYYYMMDD,
  formatDate,
  getQueryParam,
} from '@/lib/utils';
import {
  useAddPaymentMutation,
  useCalculateConvFeeTaxAllMutation,
  useGetCardTypeMutation,
  useGetPaymentDetailsQuery,
} from '@/redux/features/accounting/payments.api';
import { useGetUserDefaultStoreQuery } from '@/redux/features/store/store.api';
import {
  ApplyCreditForm,
  LoadAdjustmentDistributionTypes,
  ModalKeys,
  ModalState,
  PaymentFormTypes,
  PaymentsItemTypes,
  PaymentTypeEnum,
  RefundConvFeeTypes,
} from '@/types/accounting.types';
import {
  BookCopy,
  CircleCheckBig,
  Info,
  Printer,
  TriangleAlert,
} from 'lucide-react';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import Modal from '../../adjustments/add-edit-adjustments/Modal';
import ApplyCredit from './ApplyCredit';
import Breakdown from './Breakdown';
import LoadAdjustmentDistribution from './LoadAdjustmentDistribution';
import OrderTable from './PaymentTables';
import ProcessWithCreditCard from './ProcessWithCreditCard';
import CreditMemoAmountTable from './CreditAmountTable';
import RefundConvFee from './RefundConvFee';

const allowedPaymentMethods = [
  1, // American Express,
  5, // Credit Card
  7, // Discover,
  8, // MasterCard
  11, //Visa
  13, // ACH
  14, // Refund
];

const AddViewPayment = () => {
  const navigate = useNavigate();
  const paymentId = getQueryParam('paymentId') ?? '';
  const customerId = getQueryParam('customerId') ?? '';
  const referenceNo = getQueryParam('referenceNo');
  const paymentPayloadRef = useRef<any>(null);
  const [getCardType, { isLoading: isCardTypeLoading }] =
    useGetCardTypeMutation();

  const [calculateConvFeeTaxAll, { isLoading: convLoading }] =
    useCalculateConvFeeTaxAllMutation();

  const params = new URLSearchParams();
  if (customerId) params.append('customerId', customerId.toString());
  if (referenceNo) params.append('referenceNo', referenceNo.toString());

  // navigate to customer payment
  const navigateBackURL = `/accounting/customers/payments${
    params?.toString() ? `?${params?.toString()}` : ''
  }`;
  const currentDate = formatDateWithTimezone({});
  const [applyPayment, setApplyPayment] = useState({
    amount: null,
    orderId: null,
  });

  const [modalState, setModalState] = useState<ModalState>({
    withoutGravityPayment: false,
    checkCardDetails: false,
    isFullPayment: false,
    processCreditCardACH: false,
    applyCredit: false,
    overPayment: false,
    loadDistribution: false,
    isDistributionPresent: false,
    adjustmentOverBalance: false,
    refundConvFee: false,
    refundWithGravity: false,
  });

  const [clearCA, setClearCA] = useState<boolean>(false);
  const previousAmountRef = useRef<number | null>(null);
  const [selectedAdjustmentId, setSelectedAdjustmentId] = useState<
    number | null
  >(null);
  const [refundQueue, setRefundQueue] = useState<PaymentsItemTypes[]>([]);

  // Disable all fields if the payment type ID is not provided
  const disabled = !!paymentId;

  // default store info
  const { data: storeLocationData, isLoading } = useGetUserDefaultStoreQuery(
    undefined,
    {
      refetchOnMountOrArgChange: true,
    }
  );

  // add new payment
  const [addNewPayment, { isLoading: newPaymentLoading }] =
    useAddPaymentMutation();

  // default store info
  const defaultStoreLocation = storeLocationData?.data;
  const paymentTypeId = defaultStoreLocation?.storePayment?.paymentTypeId;
  const { data: paymentData, isLoading: paymentLoading } =
    useGetPaymentDetailsQuery(
      { paymentId, customerId },
      {
        refetchOnMountOrArgChange: true,
      }
    );

  const defaultValues = useMemo(() => {
    const dataValue = paymentData?.data;
    return {
      ...dataValue,
      date: dataValue?.date ?? currentDate,
      type: paymentId ? dataValue?.paymentMethodTypeId : paymentTypeId,
      bankAccountId: dataValue?.bankAccountId,
      breakdown: {
        charge: 0,
        convenienceFee: 0,
        tax: 0,
        totalCharge: 0,
      },
      payments: dataValue?.payments ?? [],
      creditAmount: dataValue?.creditAmount ?? null,
      amount: paymentId ? dataValue?.amount : '',
      unapplied: 0,
      storePaymentConfig: defaultStoreLocation?.storePayment,
    };
  }, [
    paymentData?.data,
    currentDate,
    paymentId,
    paymentTypeId,
    defaultStoreLocation?.storePayment,
  ]);

  const form = useForm<PaymentFormTypes>({ defaultValues });
  const refundForm = useForm<RefundConvFeeTypes>();

  const unapplied = Number(form.watch('unapplied') || 0);
  const creditAmount = form.watch('creditAmount');

  const disabledApplyBal = unapplied === 0 && creditAmount == null;

  const applyCreditForm = useForm<ApplyCreditForm>();

  useEffect(() => {
    if (defaultValues) {
      form.reset(defaultValues);
    }
  }, [defaultValues, form]);

  const paymentInfo = form.watch();

  const toggleModal = useCallback(
    (key?: ModalKeys) => {
      setModalState((prev) => {
        const newState: ModalState = {
          withoutGravityPayment: false,
          checkCardDetails: false,
          isFullPayment: false,
          processCreditCardACH: false,
          applyCredit: false,
          overPayment: false,
          loadDistribution: false,
          isDistributionPresent: false,
          adjustmentOverBalance: false,
          refundConvFee: false,
          refundWithGravity: false,
        };

        if (key && key in prev) {
          newState[key] = true;
        }

        if (applyPayment.amount) {
          setApplyPayment({ amount: null, orderId: null });
        }

        return newState;
      });
    },
    [applyPayment]
  );

  // print option
  const dropdownMenu = [
    {
      label: 'Print',
      icon: <Printer className="h-5 w-5" />,
      onClick: () => {},
      disabled: true || disabled,
    },
    {
      label: 'Load Adjustment Distribution',
      icon: <BookCopy className="h-5 w-5" />,
      onClick: () => {
        paymentData?.data?.isDistributionPresent
          ? toggleModal('loadDistribution')
          : toggleModal('isDistributionPresent');
      },
      disabled: disabled,
    },
    {
      label: 'Check Card Details',
      icon: <Printer className="h-5 w-5" />,
      onClick: () => toggleModal('checkCardDetails'),
      disabled: disabled,
    },
  ];

  // Bank account list
  const { options: bankAccountList, optionLoading } = useOptionList({
    url: BANK_ACCOUNTS_API_ROUTES.ALL,
    labelKey: 'account',
    valueKey: 'id',
  });

  //Payment Type list
  const { options: paymentTypeList, optionLoading: typeOptionLoading } =
    useOptionList({
      url: PAYMENT_TYPE_API_ROUTES.ALL,
      labelKey: 'paymentMethod',
      valueKey: 'id',
      extraKey: 'bankAccountId',
      sortBy: 'paymentMethod',
    });

  const matchPayment = paymentTypeList?.find(
    (item: OptionListType) => item?.value === paymentTypeId
  );

  useEffect(() => {
    if (!paymentId) {
      form.setValue('bankAccountId', matchPayment?.extraKey ?? null);
      form.setValue('paymentTypeLabel', matchPayment?.label ?? null);
    }
  }, [form, paymentId, matchPayment?.extraKey, matchPayment?.label]);

  // base on the selected payment type enable the process credit card /ACH
  const isProcessCreditCard = allowedPaymentMethods?.includes(
    paymentInfo?.type
  );

  // handle Apply Balance as Credit & clear credit amount
  const handleApplyBalanceAsCredit = useCallback(() => {
    const unapplied = Number(paymentInfo?.unapplied || 0);
    const creditAmount = Number(form.watch('creditAmount') || 0);
    const totalPayment = Number(form.watch('totalPayment') || 0);
    const totalCharge = Number(form.watch('breakdown.charge') || 0);
    const totalAppliedCredit = Number(form.watch('totalAppliedCredit') || 0);

    if (creditAmount) {
      form.setValue('unapplied', creditAmount + unapplied);
      form.setValue('creditAmount', null);
    } else {
      const newUnapplied = getUnappliedAmount({
        amount: totalCharge,
        payments: totalPayment,
        creditAmount,
        applyCredit: totalAppliedCredit,
      });
      form.setValue('unapplied', 0);
      form.setValue('creditAmount', newUnapplied);
    }

    setClearCA((prev) => !prev);
  }, [form, paymentInfo?.unapplied]);

  const handleOnPaymentType = async (id: string, paymentDetails: any) => {
    const payments = form.getValues('payments');
    const charge = Number(form.getValues('breakdown.charge')) || 0;
    const storePaymentConfig = form.getValues('storePaymentConfig');

    const orderPayments = payments?.filter((p) => !!p?.payment && p?.orderId);
    const adjustmentPayments = payments?.filter(
      (p) => !!p?.payment && p?.adjustmentId
    );

    // --- Step 1: Get API-based fees for order payments ---
    const payload = orderPayments?.map((p) => ({
      amount: Number(p.payment),
      orderId: Number(p.orderId),
    }));

    let updatedOrderMap = new Map<number, any>();

    if (payload.length > 0) {
      const response = await calculateConvFeeTaxAll({
        type: id,
        body: payload,
      });

      if (response?.data?.success) {
        for (const item of response.data.data) {
          updatedOrderMap.set(item.orderId, item);
        }
      }
    }

    // --- Step 2: Set fees for each payment ---
    const adjustmentMap = new Map<number, any>();
    adjustmentPayments.forEach((p) => {
      adjustmentMap.set(Number(p.adjustmentId), p);
    });

    payments.forEach((payment, index) => {
      const paymentAmount = Number(payment.payment);

      if (payment.orderId) {
        const updated = updatedOrderMap.get(Number(payment.orderId));
        if (updated && paymentAmount === Number(updated.amount)) {
          form.setValue(
            `payments.${index}.ccConvFee`,
            Number(updated.convFee).toFixed(2)
          );
          form.setValue(
            `payments.${index}.tax`,
            Number(updated.convFeeTax).toFixed(2)
          );
        }
      }

      if (payment.adjustmentId && storePaymentConfig) {
        const adjusted = adjustmentMap.get(Number(payment.adjustmentId));
        if (adjusted && paymentAmount === Number(adjusted.payment)) {
          const manualFee = calculateConvenienceFeeByType(
            paymentDetails?.label,
            paymentAmount,
            storePaymentConfig
          );
          form.setValue(`payments.${index}.ccConvFee`, manualFee);
          form.setValue(`payments.${index}.tax`, 0);
        }
      }
    });

    // --- Step 3: Calculate and set totals -
    const paymentsWatch = form.watch('payments');

    const totals = paymentsWatch.reduce(
      (acc, item) => ({
        ccConvFee: acc.ccConvFee + Number(item.ccConvFee || 0),
        tax: acc.tax + Number(item.tax || 0),
      }),
      { ccConvFee: 0, tax: 0 }
    );

    const totalCharge = charge + totals.ccConvFee + totals.tax;

    form.setValue('breakdown', {
      charge,
      convenienceFee: totals.ccConvFee,
      tax: totals.tax,
      totalCharge,
    });

    form.setValue('amount', totalCharge);
    form.setValue('totalconvenienceFee', totals.ccConvFee);
    form.setValue('totalTax', totals.tax);
    form.setValue('bankAccountId', paymentDetails.extraKey);
  };

  // on type change
  const onTypeChange = async (id: string) => {
    const paymentDetails = paymentTypeList?.find(
      (item: OptionListType) => item?.value === id
    );

    if (!paymentDetails) return;
    form.setValue('paymentTypeLabel', paymentDetails?.label ?? null);

    handleOnPaymentType(id, paymentDetails);
  };

  const processWithCreditCardACHData = useMemo(() => {
    return {
      date: paymentInfo.date,
      type: paymentInfo?.type as unknown as string,
      amount: Number(paymentInfo?.amount),
      reference: paymentInfo?.referenceNo ?? '',
      bankAccountId: Number(paymentInfo?.bankAccountId) ?? null,
      charge: Number(paymentInfo?.breakdown?.charge),
      tax: paymentInfo.breakdown.tax,
      customerId: customerId,
      creditAmount: Number(paymentInfo?.creditAmount) ?? null,
      convFee: Number(paymentInfo?.totalconvenienceFee) ?? 0,
      convFeeTax: Number(paymentInfo?.totalTax) ?? 0,
      paymentRequestList:
        paymentInfo?.payments?.flatMap((item) => {
          const amount = Number(item?.payment);
          const discount = Number(item?.discount);
          const creditAmount = Number(item?.creditAmount);
          const convFee = Number(item?.ccConvFee);
          const convFeeTax = Number(item?.tax);

          const hasValidValue =
            (!isNaN(amount) && amount !== 0) ||
            (!isNaN(discount) && discount !== 0) ||
            (!isNaN(creditAmount) && creditAmount !== 0);

          return hasValidValue
            ? [
                {
                  orderId: item?.orderId ? Number(item?.orderId) : null,
                  adjustmentId: item?.adjustmentId
                    ? Number(item?.adjustmentId)
                    : null,
                  amount: !isNaN(amount) ? amount : 0,
                  creditAmount: !isNaN(creditAmount) ? creditAmount : 0,
                  discount: !isNaN(discount) ? discount : 0,
                  convFee: !isNaN(convFee) ? convFee : 0,
                  convFeeTax: !isNaN(convFeeTax) ? convFeeTax : 0,
                  refundConvFee: false,
                },
              ]
            : [];
        }) || [],
      distributionId: paymentInfo?.distributionId ?? null,
    };
  }, [
    customerId,
    paymentInfo?.amount,
    paymentInfo?.bankAccountId,
    paymentInfo.breakdown?.charge,
    paymentInfo.breakdown.tax,
    paymentInfo?.creditAmount,
    paymentInfo.date,
    paymentInfo?.distributionId,
    paymentInfo?.payments,
    paymentInfo?.referenceNo,
    paymentInfo?.totalTax,
    paymentInfo?.totalconvenienceFee,
    paymentInfo?.type,
  ]);

  const refundConvFee = useCallback(
    async (addPaymentPayload: any) => {
      const refundablePayments =
        paymentInfo?.payments?.filter(
          (item) =>
            item?.orderId &&
            Number(item.payment) < 0 &&
            Number(item.orderConvFee) > 0
        ) ?? [];

      const totalNegative = refundablePayments.reduce(
        (sum, item) => sum + Math.abs(Number(item.payment)),
        0
      );

      const cardType = await getCardType(
        addPaymentPayload?.paymentRequest?.paymentTypeId
      );

      const refundPopupLimit = Math.ceil(
        totalNegative / Math.abs(addPaymentPayload?.amount || 0)
      );

      const limitedRefunds = refundablePayments?.slice(0, refundPopupLimit);

      if (
        !refundablePayments.length ||
        !totalNegative ||
        !cardType?.data?.data ||
        !limitedRefunds?.length
      ) {
        await addNewPayment(addPaymentPayload).unwrap();
        toggleModal(); // close any open modals
        navigate(navigateBackURL);
        return;
      }

      paymentPayloadRef.current = addPaymentPayload;
      setRefundQueue(limitedRefunds);
      refundForm.reset(mapRefundFormValues(limitedRefunds[0]));
      toggleModal('refundConvFee');
    },
    [
      addNewPayment,
      getCardType,
      navigate,
      navigateBackURL,
      paymentInfo?.payments,
      refundForm,
      toggleModal,
    ]
  );

  const handleRefundConvFee = async (refundData: RefundConvFeeTypes) => {
    const currentOrderId = refundData?.orderId;
    const refundAmountType = refundData?.refundAmountType;

    const paymentRequestList =
      paymentPayloadRef.current?.paymentRequestList ?? [];

    const match = paymentRequestList.find(
      (entry: any) => entry.orderId === currentOrderId
    );

    if (match && refundAmountType === 'refundIncludingConvFee') {
      match.refundConvFee = true;
    }

    const remainingQueue = refundQueue?.slice(1);

    if (remainingQueue.length) {
      setRefundQueue(remainingQueue);
      refundForm.reset(mapRefundFormValues(remainingQueue[0]));
    } else {
      await addNewPayment(paymentPayloadRef.current).unwrap();
      toggleModal();
      navigate(navigateBackURL);
    }
  };

  const onSubmit = useCallback(
    (action?: 'withoutGravityPayment') =>
      async (formData: PaymentFormTypes) => {
        const {
          unapplied,
          breakdown,
          totalPayment,
          totalconvenienceFee,
          amount,
          totalTax,
        } = formData;
        const isWithoutGravityPayment = action === 'withoutGravityPayment';
        const isRefund = Number(formData.amount) < 0;

        const addPaymentPayload = {
          paymentRequest: {
            date: formatDate(formData?.date, DATE_FORMAT_YYYYMMDD),
            paymentTypeId: formData?.type ?? null,
            amount: Number(formData?.breakdown?.charge) || 0,
            reference: paymentInfo?.referenceNo,
            updateCustomer: false,
            reuseCC: false,
            bankAccountId: formData?.bankAccountId || null,
            creditAmount: Number(formData?.creditAmount) || null,
            customerId: Number(customerId),
            distributionId: formData?.distributionId ?? null,
            convFee: Number(totalconvenienceFee) ?? 0,
            convFeeTax: Number(totalTax) ?? 0,
          },
          paymentRequestList:
            paymentInfo?.payments?.flatMap((item) => {
              const amount = Number(item?.payment);
              const discount = Number(item?.discount);
              const creditAmount = Number(item?.creditAmount);
              const convFee = Number(item?.ccConvFee);
              const convFeeTax = Number(item?.tax);

              const hasValidValue =
                (!isNaN(amount) && amount !== 0) ||
                (!isNaN(discount) && discount !== 0) ||
                (!isNaN(creditAmount) && creditAmount !== 0);

              return hasValidValue
                ? [
                    {
                      orderId: item?.orderId ? Number(item?.orderId) : null,
                      adjustmentId: item?.adjustmentId
                        ? Number(item?.adjustmentId)
                        : null,
                      amount: !isNaN(amount) ? amount : 0,
                      creditAmount: !isNaN(creditAmount) ? creditAmount : 0,
                      discount: !isNaN(discount) ? discount : 0,
                      convFee: !isNaN(convFee) ? convFee : 0,
                      convFeeTax: !isNaN(convFeeTax) ? convFeeTax : 0,
                      refundConvFee: false,
                    },
                  ]
                : [];
            }) || [],
        };

        // Validation: Amount is less than distributed total
        const isInvalidDistribution =
          Math.abs(unapplied) ||
          (breakdown?.charge < 0 && totalPayment >= 0) ||
          (Number(amount) < 0 && Number(totalconvenienceFee > 0));
        if (isInvalidDistribution) {
          toggleModal('isFullPayment');
          return;
        }

        // 💳 WITHOUT GRAVITY PAYMENT
        if (isWithoutGravityPayment) {
          if (!modalState.withoutGravityPayment) {
            toggleModal('withoutGravityPayment');
            return;
          }

          const allowRefundConvFee =
            defaultStoreLocation?.storePayment?.allowRefundingConvenienceFees;

          if (allowRefundConvFee && isRefund) {
            refundConvFee(addPaymentPayload);
            return;
          }

          // Just submit normally
          await addNewPayment(addPaymentPayload).unwrap();
          toggleModal();
          navigate(navigateBackURL);
          return;
        }

        // ⚠️ OVERPAYMENT not allowed with Gravity
        if (Number(breakdown.charge) !== totalPayment) {
          toggleModal('overPayment');
          return;
        }

        // 🔄 Refund flow with Gravity
        if (isRefund) {
          toggleModal('refundWithGravity');
          return;
        }

        // ✅ Default success case
        toggleModal('processCreditCardACH');
      },
    [
      addNewPayment,
      customerId,
      defaultStoreLocation?.storePayment?.allowRefundingConvenienceFees,
      modalState.withoutGravityPayment,
      navigate,
      navigateBackURL,
      paymentInfo?.payments,
      refundConvFee,
      paymentInfo?.referenceNo,
      toggleModal,
    ]
  );

  // handle change amount
  const onChangeAmount = (event: React.FocusEvent<HTMLInputElement>) => {
    const enteredAmount = Number(event?.target?.value?.replace('$', '')) || 0;
    if (Number(previousAmountRef.current?.toFixed(2)) === enteredAmount) {
      return;
    }

    const totalPayment = Number(form.watch('totalPayment') || 0);
    const totalconvenienceFee = Number(form.watch('totalconvenienceFee') || 0);
    const totalTax = Number(form.watch('totalTax') || 0);
    const totalAppliedCredit = Number(form.watch('totalAppliedCredit') || 0);
    const creditAmount = Number(paymentInfo?.creditAmount || 0);

    const newUnapplied = getUnappliedAmount({
      amount: enteredAmount,
      payments: totalPayment,
      creditAmount,
      applyCredit: totalAppliedCredit,
    });

    form.setValue('unapplied', newUnapplied);

    //  Calculate and set the total amount including convenience fee and tax
    if (enteredAmount < 0) {
      form.setValue('breakdown.charge', enteredAmount);
      form.setValue('breakdown.convenienceFee', 0);
      form.setValue('breakdown.tax', 0);
      form.setValue('breakdown.totalCharge', enteredAmount);
      form.setValue('amount', enteredAmount);
      previousAmountRef.current = enteredAmount;
    } else {
      const newAmount = enteredAmount + totalconvenienceFee + totalTax;
      form.setValue('amount', newAmount);
      form.setValue('breakdown.convenienceFee', totalconvenienceFee);
      form.setValue('breakdown.tax', totalTax);
      form.setValue('breakdown.charge', enteredAmount);
      form.setValue('breakdown.totalCharge', newAmount);
      //  Store current value
      previousAmountRef.current = newAmount;
    }
  };

  const handleAdjustmentDistibution = (
    adjustmentDistributions: LoadAdjustmentDistributionTypes[]
  ) => {
    if (selectedAdjustmentId) {
      const payments = form.getValues('payments');
      const adjustment = adjustmentDistributions[0];

      // Create a Map for quick lookup by orderId
      const distributionMap = new Map<
        number,
        { amount: number; isAdjustment: boolean }
      >();

      adjustment.distributions.forEach((dist) => {
        distributionMap.set(dist.orderId, dist);
      });

      const paymentDetails = paymentTypeList?.find(
        (item: OptionListType) => item?.label === PaymentTypeEnum.ZERO_SUM
      );
      form.setValue('type', paymentDetails?.value);
      form.setValue('paymentTypeLabel', paymentDetails?.label ?? null);

      payments.forEach((payment, index) => {
        const { orderId, adjustmentId } = payment;
        // If the payment  matches the adjustment
        if (adjustment.adjustmentId === adjustmentId) {
          form.setValue(
            `payments.${index}.payment`,
            adjustment.adjustmentAmount
          );
        } else {
          // Try to find a matching distribution by orderId or adjustmentId
          let matchedDistribution;
          const distByAdjustmentId = distributionMap.get(Number(adjustmentId));
          if (distByAdjustmentId?.isAdjustment) {
            matchedDistribution = distByAdjustmentId;
          } else {
            matchedDistribution = distributionMap.get(Number(orderId));
          }

          if (matchedDistribution) {
            form.setValue(
              `payments.${index}.payment`,
              matchedDistribution.amount
            );
          } else {
            // No matching distribution found, clear payment
            form.setValue(`payments.${index}.payment`, '');
          }
        }
        form.setValue(`payments.${index}.ccConvFee`, '');
        form.setValue(`payments.${index}.tax`, '');
        form.setValue(`payments.${index}.creditAmount`, '');
        form.setValue(`payments.${index}.discount`, '');
      });
      form.setValue('breakdown', {
        charge: 0,
        convenienceFee: 0,
        tax: 0,
        totalCharge: 0,
      });
      form.setValue('amount', 0);
      form.setValue('unapplied', 0);
      form.setValue('totalPayment', 0);
      form.setValue('totalconvenienceFee', 0);
      form.setValue('totalTax', 0);
    }
    form.setValue('distributionId', selectedAdjustmentId);
  };

  const handleApplyCredit = (formData: ApplyCreditForm) => {
    // Step 1: Build a map of orderId -> amountOrder (as string for safety)
    const creditMap = new Map(
      formData.payments
        .filter((item) => item.amountOrder) // Only include valid credit entries
        .map((item) => [item.orderId, item.amountOrder?.toString()])
    );

    // Step 2: Get current payment rows from form
    const currentPayments = form.getValues('payments');

    currentPayments.forEach((row, index) => {
      const orderId = row.orderId;
      const creditValue = creditMap.get(orderId as number);
      const existingAppliedCredit = Number(
        form.getValues(`payments.${index}.creditAmount`) ?? 0
      );
      // Step 3: If credit is applicable for this row, add it to appliedCredit
      if (creditValue !== undefined) {
        form.setValue(
          `payments.${index}.creditAmount`,
          existingAppliedCredit + Number(creditValue)
        );
      }

      // Step 4: If this row matches the order for which credit is being applied,
      // store the full list of credit children (for potential reversal)
      if (applyPayment?.orderId === row?.orderId) {
        const children = Array.from(creditMap?.entries())?.map(
          ([childOrderId, value]) => ({
            orderId: childOrderId,
            creditAmount: Number(value),
          })
        );
        form.setValue(`payments.${index}.appliedCreditChildren`, children);
      }
    });

    // Sum up total applied credit
    const totalAppliedCredit = currentPayments.reduce((sum, row) => {
      return sum + Number(row.creditAmount || 0);
    }, 0);

    const totalCharge = Number(form.watch('breakdown.charge') || 0);
    const totalPayment = Number(form.watch('totalPayment') || 0);
    const creditAmount = Number(paymentInfo?.creditAmount || 0);
    const newUnapplied = getUnappliedAmount({
      amount: totalCharge,
      payments: totalPayment,
      creditAmount,
      applyCredit: totalAppliedCredit,
    });
    form.setValue('unapplied', newUnapplied);
    form.setValue('totalAppliedCredit', totalAppliedCredit);
  };

  const handleCreditCard = (cardType: string) => {
    const paymentDetails = paymentTypeList?.find(
      (item: OptionListType) => item?.label === CARD_TYPE_MAPPING[cardType]
    );
    form.setValue('type', paymentDetails?.value);
    form.setValue('paymentTypeLabel', paymentDetails?.label ?? null);

    handleOnPaymentType(paymentDetails?.value, paymentDetails);
  };

  return (
    <div className="px-6">
      <PageHeader
        title={paymentId ? 'View Payment' : 'New Payment'}
        cancelPath={navigateBackURL}
        label="Ok"
        disabled={disabled}
        icon={CircleCheckBig}
        titleClassName="text-text-Default"
        labelClassName="w-28"
        dropdownMenu={dropdownMenu}
        handleSubmit={form.handleSubmit(onSubmit('withoutGravityPayment'))}
      />

      <div className="w-full space-y-6">
        {/* Main Grid Container */}
        <div className="grid grid-cols-1 md:grid-cols-12 gap-6">
          <div className="md:col-span-5 2xl:col-span-4">
            <div className="border rounded-lg p-4 space-y-6">
              {/* Basic Payment Information */}
              <div className="space-y-4">
                {/* Date and Type Row */}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <DatePicker
                    label="Date"
                    name="date"
                    form={form}
                    enableInput
                    disabled={disabled}
                  />
                  <SelectWidget
                    name="type"
                    label="Type"
                    placeholder="Select Type"
                    form={form}
                    optionsList={paymentTypeList}
                    isLoading={typeOptionLoading}
                    isClearable={false}
                    onSelectChange={onTypeChange}
                    disabled={disabled}
                  />
                </div>

                {/* Amount and Reference Row */}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  {/* Amount with Breakdown Button */}
                  <div className="flex gap-2">
                    <NumberInputField
                      label="Amount"
                      name="amount"
                      placeholder="Amount"
                      form={form}
                      fixedDecimalScale
                      maxLength={10}
                      prefix="$"
                      disabled={disabled}
                      validation={TEXT_VALIDATION_RULE}
                      onBlur={onChangeAmount}
                      allowNegative
                    />
                    <Breakdown form={form} onChargeChange={onChangeAmount} />
                  </div>

                  <InputField
                    label="Reference #"
                    name="referenceNo"
                    placeholder="Reference #"
                    form={form}
                    maxLength={35}
                    disabled={disabled}
                  />
                </div>

                {/* Convenience Fee Notice */}
                {paymentInfo?.breakdown?.convenienceFee > 0 && (
                  <div className="bg-red-50 border border-red-200 rounded-md p-3">
                    <p className="text-sm text-red-600 flex items-center">
                      <span className="mr-2">⚠️</span>A convenience fee has been
                      applied to this payment
                    </p>
                  </div>
                )}

                {/* Bank Account Selection */}
                <div className="w-full">
                  <SelectWidget
                    label="Bank Account"
                    name="bankAccountId"
                    placeholder="Bank Account"
                    form={form}
                    optionsList={bankAccountList}
                    isLoading={optionLoading}
                    parentClassName="col-span-2"
                    numericOnly
                    disabled={disabled}
                  />
                </div>
              </div>

              {/* Credit Application Section */}
              <div className="space-y-4 border-t pt-4">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 items-end">
                  <NumberInputField
                    label="Unapplied"
                    name="unapplied"
                    placeholder="Unapplied"
                    form={form}
                    prefix="$"
                    fixedDecimalScale
                    disabled
                    allowNegative
                  />
                  <AppButton
                    label={
                      !clearCA ? 'Apply BAL as Credit' : 'Clear Credit Amount'
                    }
                    className="mt-8"
                    tooltip={
                      !clearCA
                        ? `Apply Balance as Credit`
                        : 'Clear Credit Amount'
                    }
                    disabled={disabled || disabledApplyBal}
                    onClick={handleApplyBalanceAsCredit}
                  />
                </div>

                {/* Credit Memo Table */}
                <div className="w-full">
                  <CreditMemoAmountTable
                    data={
                      paymentInfo?.creditAmount
                        ? [
                            {
                              creditAmount: Number(paymentInfo?.creditAmount),
                              creditMemo: '',
                            },
                          ]
                        : []
                    }
                  />
                </div>
              </div>

              {/* Action Button - Sticky at bottom on mobile */}
              <div className="sticky bottom-1 bg-white pt-3 border-t mt-6 z-20 hidden : md:block">
                <AppButton
                  label="Process Credit Card/ACH"
                  className="w-full py-3 text-base font-medium"
                  disabled={
                    disabled ||
                    !isProcessCreditCard ||
                    !paymentInfo.amount ||
                    Number(paymentInfo.amount) === 0
                  }
                  onClick={form.handleSubmit(onSubmit())}
                />
              </div>
            </div>
          </div>
          {/* Right Panel - Order Table */}
          <div className="md:col-span-7 2xl:col-span-8">
            <OrderTable
              form={form}
              isLoading={false}
              toggleModal={toggleModal}
              applyCreditForm={applyCreditForm}
              setApplyPayment={setApplyPayment}
            />
          </div>
          <div className="sticky bottom-0 bg-white py-3 border-t mt-6 z-20 md:hidden">
            <AppButton
              label="Process Credit Card/ACH"
              className="w-full py-3 text-base font-medium"
              disabled={disabled || !isProcessCreditCard}
              onClick={form.handleSubmit(onSubmit())}
            />
          </div>
        </div>
      </div>

      {/* Process Credit Card */}
      <CustomDialog
        onOpenChange={() => toggleModal()}
        description=""
        open={modalState.processCreditCardACH}
        className={cn(
          'max-h-[90%] overflow-auto md:max-w-[60%] 2xl:max-w-[40%]'
        )}
        title="Process With Credit Card/ACH"
        contentClassName="p-0"
      >
        <ProcessWithCreditCard
          setOpen={() => toggleModal()}
          toggleToNewPayment={toggleModal}
          data={processWithCreditCardACHData}
          paymentTypeList={paymentTypeList}
          refetchPaymentDetails={() => navigate(navigateBackURL)}
        />
      </CustomDialog>

      {/* Check Credit Card */}
      <CustomDialog
        onOpenChange={() => toggleModal()}
        description=""
        open={modalState.checkCardDetails}
        className={cn(
          ' md:max-w-[35%] 2xl:max-w-[28%] max-h-[96%] overflow-auto'
        )}
        title="Check Credit Card"
      >
        <CreditCards
          toggle={() => toggleModal()}
          onConfirm={handleCreditCard}
        />
      </CustomDialog>

      {/* Apply Credit */}
      <Modal
        open={modalState.applyCredit}
        onOpenChange={toggleModal}
        title="Apply Credit"
        size="lg"
        height="auto"
        showHeader={true}
      >
        <ApplyCredit
          onOpenChange={() => toggleModal()}
          onConfirm={handleApplyCredit}
          applyCreditForm={applyCreditForm}
          applyPayment={applyPayment}
        />
      </Modal>

      {/* Load Adjustment Distribution */}
      <Modal
        open={modalState.loadDistribution}
        onOpenChange={toggleModal}
        title="Load Adjustment Distribution"
        size="xl"
        height="auto"
        showHeader={true}
      >
        <LoadAdjustmentDistribution
          onOpenChange={() => toggleModal()}
          onConfirm={handleAdjustmentDistibution}
          selectedAdjustmentId={selectedAdjustmentId}
          setSelectedAdjustmentId={setSelectedAdjustmentId}
        />
      </Modal>

      {/* Refunc Conv Fee*/}
      <Modal
        open={modalState.refundConvFee}
        onOpenChange={() => toggleModal()}
        title="Refund Convenience Fee"
        size="md"
        height="auto"
        showHeader={true}
      >
        <RefundConvFee
          handleRefundConvFee={handleRefundConvFee}
          onOpenChange={() => toggleModal()}
          refundForm={refundForm}
          isLoading={newPaymentLoading}
        />
      </Modal>

      <AppConfirmationModal
        title="Confirmation"
        open={modalState.withoutGravityPayment}
        handleCancel={() => toggleModal()}
        handleSubmit={form.handleSubmit(onSubmit('withoutGravityPayment'))}
        isLoading={newPaymentLoading || isCardTypeLoading}
        description={
          <div>
            Are you sure you want to apply this credit card payment without
            processing through Gravity Payments?
          </div>
        }
      />
      <AppConfirmationModal
        title="Warning"
        open={
          modalState.isFullPayment ||
          modalState.overPayment ||
          modalState.adjustmentOverBalance
        }
        handleSubmit={() => toggleModal()}
        submitLabel="Ok"
        description={
          <div className="flex gap-2 items-center">
            <TriangleAlert className="text-primary w-12 h-12" />
            {modalState.isFullPayment
              ? ' Payment amount does not equal the amount distributed on the orders. Please apply this payment in full and try again.'
              : modalState.overPayment
                ? 'Overpayment credits cannot be processed as credit card transactions. Please fully apply this payment and try again.'
                : ' Adjustment can only be applied up to the remaining balance (minus any convenience fees)'}
          </div>
        }
      />
      <AppConfirmationModal
        title="Warning"
        open={modalState.isDistributionPresent || modalState.refundWithGravity}
        handleSubmit={() => toggleModal()}
        submitLabel="Ok"
        description={
          <div className="flex gap-2 items-center">
            <Info className="text-primary w-8 h-8" />
            {modalState.isDistributionPresent
              ? 'No adjustment distributions are saved for this customer'
              : 'Refund must be done within the order'}
          </div>
        }
      />

      <AppSpinner
        isLoading={isLoading || paymentLoading || convLoading}
        overlay
      />
    </div>
  );
};

export default AddViewPayment;
