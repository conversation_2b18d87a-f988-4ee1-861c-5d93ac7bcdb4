import { RootState } from '@/redux/store';
import { useCallback, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';

import AppButton from '@/components/common/app-button';
import AppDataTable from '@/components/common/app-data-table';
import AppSpinner from '@/components/common/app-spinner';
import ColumnReOrdering from '@/components/common/column-reording';
import ActionColumnMenu from '@/components/common/data-tables/ActionColumn';
import CustomDialog from '@/components/common/dialog';
import FormActionButtons from '@/components/common/FormActionButtons';
import { InputField } from '@/components/forms';

import { CUSTOMER_API_ROUTES } from '@/constants/api-constants';
import { componentMap } from '@/constants/common-constants';
import { ROUTES } from '@/constants/routes-constants';
import { TEXT_VALIDATION_RULE } from '@/constants/validation-constants';
import { clearFilter } from '@/redux/features/accounting/accountingCustomersSlice';
import {
  useGetAccountingCustomerColumsQuery,
  useUpdateAccountingCustomerColumsMutation,
} from '@/redux/features/accounting/payments.api';
import Filter from './Filter';

const AccountingCustomers = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const filters = useSelector(
    (state: RootState) => state.accountingCustomers.filters
  );

  const [openColumnOrdering, setOpenColumnOrdering] = useState<boolean>(false);
  const [isFilterOpen, setFilterOpen] = useState<boolean>(false);
  const [byReference, setByReference] = useState<boolean>(false);

  const form = useForm({
    defaultValues: { reference: '' },
  });

  // get reorganize columns
  const { data: columnData, isFetching } =
    useGetAccountingCustomerColumsQuery();

  // update reorganize columns
  const [addColumnOrder, { isLoading: ordering }] =
    useUpdateAccountingCustomerColumsMutation();

  const handleClearFilter = useCallback(
    (key: string) => {
      dispatch(clearFilter(key));
      setFilterOpen(false);
    },
    [dispatch]
  );

  const toggleByReference = () => {
    setByReference((prev) => !prev);
  };

  const handleColumnOrder = useCallback(
    async (formValues: Record<string, boolean>) => {
      if (!columnData) return;
      const updated = columnData?.data.map((col: any) => ({
        ...col,
        enabled: !!formValues[col?.accessorKey],
      }));
      await addColumnOrder(updated).unwrap();
      setOpenColumnOrdering(false);
    },
    [columnData, addColumnOrder]
  );

  const navigateToPayments = useCallback(
    (customerId: number) =>
      navigate(`${ROUTES.ACCOUNTING_PAYMENTS}?customerId=${customerId}`),
    [navigate]
  );

  const tableColumns = useMemo(
    () => columnData?.data ?? [],
    [columnData?.data]
  );

  const memoizedColumns = useMemo(() => {
    if (!tableColumns?.length) return [];

    const formattedColumns = tableColumns
      .filter((column: any) => column?.enabled)
      .map((column: any) => ({
        accessorKey: column?.accessorKey,
        header: column?.header,
        enableSorting: column?.enableSorting || false,
        size: column?.size || 200,
        cell: ({ row }: any) => {
          const Component = componentMap[column?.component];
          return Component ? (
            <Component value={row?.original?.[column?.accessorKey]} />
          ) : (
            row?.original?.[column?.accessorKey]
          );
        },
      }));

    return [
      {
        accessorKey: 'ordering',
        header: () => (
          <ColumnReOrdering
            isOpen={openColumnOrdering}
            handleOrderingColumn={handleColumnOrder}
            tableColumns={tableColumns}
            setOpenColumnOrdering={setOpenColumnOrdering}
          />
        ),
        size: 50,
      },
      ...formattedColumns,
      {
        id: 'action',
        size: 110,
        header: 'Actions',
        cell: ({ row }: any) => {
          return (
            <ActionColumnMenu
              triggerClassName="hidden"
              onEdit={() => navigateToPayments(row.original.customer_id)}
            />
          );
        },
      },
    ];
  }, [tableColumns, openColumnOrdering, handleColumnOrder, navigateToPayments]);

  const handleReferenceSearch = (data: { reference: string }) => {
    toggleByReference();
    navigate(`${ROUTES.ACCOUNTING_PAYMENTS}?referenceNo=${data?.reference}`);
  };

  const handleReferenceCancel = () => {
    toggleByReference();
    form.reset();
  };

  return (
    <div className="flex flex-col p-4 gap-6">
      <AppDataTable
        url={CUSTOMER_API_ROUTES.ALL}
        columns={memoizedColumns}
        heading="Payments"
        searchKey="full_name"
        customToolBar={
          <AppButton label="Search By Reference" onClick={toggleByReference} />
        }
        enableFilter
        filter={filters}
        handleClearFilter={handleClearFilter}
        filterClassName="w-[473px]"
        filterContent={<Filter setIsFilterOpen={setFilterOpen} />}
        setIsFilterOpen={setFilterOpen}
        isFilterOpen={isFilterOpen}
        enablePagination={true}
        tableClassName="max-h-[580px] overflow-auto"
        enableSearch
      />

      <CustomDialog
        open={byReference}
        onOpenChange={handleReferenceCancel}
        title="Search By Reference"
        description=""
        className="pb-3"
      >
        <div className="pl-6 pr-6 flex flex-col gap-4 h-full w-full">
          <InputField
            form={form}
            name="reference"
            label="Enter a Reference Number"
            placeholder="Enter a Reference Number"
            validation={TEXT_VALIDATION_RULE}
          />
          <FormActionButtons
            onSubmit={form.handleSubmit(handleReferenceSearch)}
            onCancel={handleReferenceCancel}
            className="gap-x-5"
          />
        </div>
      </CustomDialog>

      <AppSpinner overlay isLoading={ordering || isFetching} />
    </div>
  );
};

export default AccountingCustomers;
