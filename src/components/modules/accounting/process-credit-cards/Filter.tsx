import AppButton from '@/components/common/app-button';
import { CheckboxField } from '@/components/forms';
import DatePicker from '@/components/forms/date-picker';
import { Separator } from '@/components/ui/separator';
import { DATE_FORMAT_YYYYMMDD, DEFAULT_FORMAT, formatDate } from '@/lib/utils';
import { updateAdjustmentFormValues } from '@/redux/features/accounting/adjustmentsSlice';
import {
  clearAllCCProcessingFilters,
  setCCProcessingFilter,
} from '@/redux/features/accounting/ccProcessingSlice';
import { RootState } from '@/redux/store';
import { memo, useEffect } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';

// Define the interface for filter data
interface FilterFormValues {
  [key: string]: any;
}

const defaultValues: FilterFormValues = {
  dateOfUseFrom: new Date(),
  dateOfUseThru: new Date(),
  showAll: false,
};

interface FilterProps {
  setIsFilterOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const Filter: React.FC<FilterProps> = ({ setIsFilterOpen }) => {
  const formValuesFromRedux = useSelector(
    (state: RootState) => state.ccProcessing.formValues
  );
  const dispatch = useDispatch();

  // Initialize form with values from Redux
  const form = useForm<FilterFormValues>({
    defaultValues: formValuesFromRedux,
    mode: 'onChange',
  });

  // Watch form changes and update Redux state
  useEffect(() => {
    const subscription = form.watch((values) => {
      dispatch(updateAdjustmentFormValues(values));
    });

    return () => subscription.unsubscribe();
  }, [form, dispatch]);

  const onSubmit: SubmitHandler<FilterFormValues> = (data) => {
    const dateOfUseFrom = data?.dateOfUseFrom;
    const dateOfUseThru = data?.dateOfUseThru;
    const showAll = data?.showAll;

    const newFilterData = [
      {
        label: 'Date From',
        value: dateOfUseFrom
          ? formatDate(dateOfUseFrom, DATE_FORMAT_YYYYMMDD)
          : '',
        name: 'dateOfUseFrom',
        tagValue: dateOfUseFrom
          ? formatDate(dateOfUseFrom, DEFAULT_FORMAT)
          : '',
        operator: 'Equals',
      },
      {
        label: 'Date Thru',
        value: dateOfUseThru
          ? formatDate(dateOfUseThru, DATE_FORMAT_YYYYMMDD)
          : '',
        name: 'dateOfUseThru',
        tagValue: dateOfUseThru
          ? formatDate(dateOfUseThru, DEFAULT_FORMAT)
          : '',
        operator: 'Equals',
      },
      {
        label: 'Show All',
        value: showAll ? 'true' : 'false',
        name: 'showAll',
        tagValue: showAll ? 'Yes' : 'No',
        operator: 'Equals',
      },
    ].filter((item) => item.value);

    dispatch(setCCProcessingFilter(newFilterData));
    setIsFilterOpen(false);
  };

  const handleClear = () => {
    form.reset(defaultValues); // Reset form to default values
    dispatch(clearAllCCProcessingFilters()); // Clear all filters in Redux
    setIsFilterOpen(false); // Close the filter form
  };

  // Track if form values are different from the default values
  const isFormModified =
    JSON.stringify(form.watch()) !== JSON.stringify(defaultValues);

  return (
    <>
      <div className="px-3">
        <div className="text-normal py-2  font-semibold">Filters</div>
        <Separator
          orientation="horizontal"
          className="h-[1px] bg-border-Default mb-2"
        />

        <div className="grid grid-cols-2 gap-3">
          <DatePicker
            form={form}
            name="dateOfUseFrom"
            label="Date From"
            placeholder="Select Date"
            enableInput
          />

          <DatePicker
            form={form}
            name="dateOfUseThru"
            label="Date Thru"
            placeholder="Select Date"
            disabled={!form.watch('dateOfUseFrom')}
            enableInput
          />

          <CheckboxField
            name="showAll"
            control={form.control}
            label="Show All"
          />
        </div>
        <div className="w-full h-full flex justify-between gap-3 mt-3 sticky -bottom-2 pt-1 pb-3 bg-white z-30">
          <AppButton
            className="w-full"
            onClick={form.handleSubmit(onSubmit)}
            label="Apply"
            disabled={!isFormModified}
          />
          <AppButton
            className="w-full"
            label="Clear"
            variant="neutral"
            onClick={handleClear}
            disabled={!isFormModified}
          />
        </div>
      </div>
    </>
  );
};

export default memo(Filter);
