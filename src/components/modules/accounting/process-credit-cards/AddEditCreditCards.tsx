// import { ColumnDef, RowSelectionState } from '@tanstack/react-table';
// import { debounce } from 'lodash';
// import { useEffect, useMemo, useState } from 'react';
// import { SubmitHand<PERSON>, useForm, UseFormReturn } from 'react-hook-form';

// // Components
// import AppButton from '@/components/common/app-button';
// import AppSpinner from '@/components/common/app-spinner';
// import CheckboxField from '@/components/forms/checkbox';
// import InputField from '@/components/forms/input-field';
// import PhoneInputWidget from '@/components/forms/phone-input-mask';
// import TextAreaField from '@/components/forms/text-area';

// // Constants and Utilities
// import { ROUTES } from '@/constants/routes-constants';
// import { TEXT_VALIDATION_RULE } from '@/constants/validation-constants';
// import { formatPhoneNumber, getQueryParam, searchPayload } from '@/lib/utils';

// // Hooks and API
// import { useGetAllMutation } from '@/redux/features/common-api/common.api';
// import {
//   useAddNewAdjustmentsMutation,
//   useGetListQuery,
//   useUpdateAdjustmentsMutation,
// } from '@/redux/features/accounting/adjustments.api';

// // Types
// import Header from '@/components/modules/customers/new-customer/header';
// import { SortingStateType } from '@/types/common.types';
// import { CustomerDetailTypes } from '@/types/customer.types';
// import { AdjustmentsDetailTypes } from '@/types/accounting.types';
// import { useNavigate } from 'react-router-dom';
// import CustomerFilter from '@/components/modules/lists/delivery-locations/CustomFilter';
// import Selector from '@/components/modules/lists/delivery-locations/Selector';
// import DatePicker from '@/components/forms/date-picker';
// import NumberInputField from '@/components/forms/number-input-field';
// import SelectWidget from '@/components/forms/select';
// import { ADJUSTMENTS_API_ROUTES } from '@/constants/api-constants/accounting-api-constants';
// import { CUSTOMER_API_ROUTES } from '@/constants/api-constants';

// export const AddEditCreditCards = () => {
//   const url = CUSTOMER_API_ROUTES.ALL;
//   const id = getQueryParam('id');

//   const selectType = [{ label: '', value: '' }];

//   // State management
//   const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
//   const [filters, setFilters] = useState<any[]>([]);
//   const [isCustomDialogOpen, setIsCustomDialogOpen] = useState(false);
//   const [sorting] = useState<SortingStateType[]>([]);

//   // Queries and Mutations
//   const [getAllDataTable, { data: getAllData, isLoading: isCustomerLoading }] =
//     useGetAllMutation();
//   const { data, isFetching } = useGetListQuery(
//     { url: ADJUSTMENTS_API_ROUTES.GET(id) },
//     { skip: !id }
//   );

//   const [updateAdjustments, { isLoading }] = useUpdateAdjustmentsMutation();
//   const [addNewAdjustments, { isLoading: newAdjustmentsLoading }] =
//     useAddNewAdjustmentsMutation();
//   const navigate = useNavigate();
//   // Filter Form
//   const filterForm = useForm({
//     defaultValues: { isactive: '', filterName: 'name', name: '', tel1: '' },
//   });

//   const defaultValues = useMemo(() => {
//     if (!data?.data)
//       return {
//         ...data?.data,
//         contactPhone: data?.data?.contactPhone ?? '',
//         status: data?.data?.status ?? 'Active',
//         adjustments_id: data?.data?.adjustments_id ?? 0,
//         convenienceFee: data?.data?.convenienceFee ?? '0.00',
//         customerId: data?.data?.customerId ?? '',
//       };
//   }, [data]);

//   const form: UseFormReturn<AdjustmentsDetailTypes> =
//     useForm<AdjustmentsDetailTypes>({ defaultValues, mode: 'onChange' });

//   // Effect for search payload and data fetching
//   useEffect(() => {
//     const payload = searchPayload({
//       pageNumber: -1,
//       pageSize: -1,
//       sortBy: sorting?.[0]?.id || '',
//       sortAscending: sorting?.[0]?.desc,
//       filters:
//         filters.length > 0 ? filters : [{ field: '', value: '', operator: '' }],
//     });

//     getAllDataTable({ url, type: 'POST', body: payload });
//   }, [filters, getAllDataTable, sorting, url]);

//   // Effect to reset form when default values change
//   useEffect(() => {
//     if (defaultValues) form.reset(defaultValues);
//   }, [defaultValues, form]);

//   const onSubmit: SubmitHandler<AdjustmentsDetailTypes> = async (formData) => {
//     try {
//       if (id) {
//         await updateAdjustments({
//           url: ADJUSTMENTS_API_ROUTES.UPDATE(id),
//           data: {
//             ...formData,
//             customerId: Number(formData.customerId) ?? null,
//             adjustments_id: id,
//           },
//         }).unwrap();
//       } else {
//         await addNewAdjustments({
//           url: ADJUSTMENTS_API_ROUTES.CREATE,
//           data: {
//             ...formData,
//             customerId: Number(formData.customerId) ?? null,
//           },
//         }).unwrap();
//       }
//       navigate(ROUTES.ADJUSTMENTS);
//     } catch (error) {}
//   };

//   const debouncedSearch = useMemo(
//     () =>
//       debounce(
//         ({
//           isactive,
//           filterName,
//           value,
//         }: {
//           isactive: string;
//           filterName: string;
//           value: string;
//         }) => {
//           const filtersToApply = [];

//           // Filter for active/inactive status
//           if (isactive) {
//             filtersToApply.push({
//               field: 'isactive',
//               value: isactive,
//               operator: 'equals',
//             });
//           }

//           // Filter for name or phone number
//           if (value) {
//             if (filterName === 'tel1') {
//               const telValue = value.replace(/^\+1/, '');
//               if (telValue) {
//                 filtersToApply.push({
//                   field: 'tel1',
//                   value: value.replace(/^\+1/, ''),
//                   operator: 'contains',
//                 });
//               }
//               2;
//             } else if (filterName === 'name') {
//               filtersToApply.push({
//                 field: 'first_name',
//                 value: value,
//                 operator: 'contains',
//               });
//             }
//           }

//           setFilters(filtersToApply);
//         },
//         500
//       ),
//     []
//   );

//   // Memoized table columns
//   const columns: ColumnDef<CustomerDetailTypes>[] = useMemo(
//     () => [
//       {
//         accessorKey: 'first_name',
//         header: 'Name',
//         size: 240,
//         enableSorting: true,
//         cell: (info) => info.row.original.full_name,
//       },
//       {
//         accessorKey: 'custtype',
//         header: 'Customer Type',
//         enableSorting: true,
//         size: 200,
//       },
//       {
//         accessorKey: 'contact',
//         header: 'Phone',
//         size: 170,
//         enableSorting: true,
//         cell: (info) => {
//           const contact = info.getValue() as string;

//           // Check if the phone number is empty or has less than 10 digits (e.g., a 6-digit number)
//           if (!contact || contact.length < 10) {
//             return ''; // or return an empty string, null, or placeholder
//           }

//           return formatPhoneNumber(contact);
//         },
//       },
//       { accessorKey: 'city', header: 'City', size: 130, enableSorting: true },
//       { accessorKey: 'state', header: 'State', size: 110, enableSorting: true },
//     ],
//     []
//   );

//   // New handlers for "Ok" and "Cancel" buttons
//   const handleOk = () => {
//     const selectedCustomerId = Object.keys(rowSelection).find(
//       (key) => rowSelection[key]
//     );
//     if (selectedCustomerId) {
//       const selectedCustomer = getAllData?.data?.find(
//         (adjustments: any) =>
//           adjustments.customer_id === Number(selectedCustomerId)
//       );
//       if (selectedCustomer) {
//         form.setValue('customerId', Number(selectedCustomerId));
//         form.setValue(
//           'customerName',
//           `${selectedCustomer?.first_name} ${selectedCustomer?.last_name ?? ''}`
//         );
//       }
//     }
//     setRowSelection({});
//     setIsCustomDialogOpen(false);
//   };

//   const handleCancel = () => setIsCustomDialogOpen(false);

//   return (
//     <>
//       <Header
//         isLoading={newAdjustmentsLoading || isLoading}
//         navigateTo={ROUTES.ADJUSTMENTS}
//         onSave={() => form.handleSubmit(onSubmit)()}
//         headerTitle={`${id ? 'Edit' : 'Add'} Adjustment`}
//         className="sticky top-16 p-4 z-[20] bg-white"
//       />

//       <div className="pl-8 pr-8 w-4/5">
//         <form
//           onSubmit={form.handleSubmit(onSubmit)}
//           className="grid grid-cols-2 gap-4 mt-4"
//         >
//           {/* Form Fields */}
//           <InputField
//             form={form}
//             name="adjustmentId"
//             label="Adjustment #"
//             placeholder="Enter Adjustment"
//             disabled
//           />
//           <InputField
//             form={form}
//             name="status"
//             label="Status"
//             placeholder="Enter Status"
//             disabled
//           />
//           <SelectWidget
//             form={form}
//             name="type"
//             label="Type"
//             isClearable={false}
//             placeholder="Select Type"
//             optionsList={selectType}
//             disabled={!!id}
//           />
//           <DatePicker
//             form={form}
//             name="date"
//             label="Date"
//             placeholder="Date"
//             disabled={!!id}
//           />
//           <PhoneInputWidget
//             name="contactPhone"
//             label="Contact Phone"
//             placeholder="(xxx)-xxx-xxxx"
//             form={form}
//             disabled={!!id}
//           />
//           <NumberInputField
//             form={form}
//             name="taxableTotal"
//             label="Taxable Total"
//             placeholder="$______.__"
//             prefix="$"
//             maxLength={10}
//             disabled={!!id}
//           />
//           <NumberInputField
//             form={form}
//             name="nonTaxableTotal"
//             label="Non-Taxable Total"
//             placeholder="$______.__"
//             prefix="$"
//             maxLength={10}
//             disabled={!!id}
//           />
//           <NumberInputField
//             form={form}
//             name="convenienceFee"
//             label="Convenience Fee"
//             placeholder="$______.__"
//             prefix="$"
//             maxLength={10}
//             disabled
//           />
//           <SelectWidget
//             form={form}
//             name="taxCode"
//             label="Tax Code"
//             isClearable={false}
//             placeholder="Select Tax Code"
//             optionsList={selectType}
//             disabled={!!id}
//           />
//           <NumberInputField
//             form={form}
//             name="taxRate"
//             label="Tax Rate"
//             placeholder="__.__%"
//             suffix="%"
//             maxLength={7}
//             disabled
//           />
//           <NumberInputField
//             form={form}
//             name="tax"
//             label="Tax"
//             placeholder="$______.__"
//             prefix="$"
//             maxLength={10}
//             disabled
//           />
//           <NumberInputField
//             form={form}
//             name="total"
//             label="Total"
//             placeholder="$______.__"
//             prefix="$"
//             maxLength={10}
//             disabled
//           />

//           <div className="col-span-2 flex flex-row gap-x-4 items-center">
//             <InputField
//               form={form}
//               name="customerName"
//               label="Customer"
//               className="w-full"
//               placeholder="Customer"
//               validation={TEXT_VALIDATION_RULE}
//               disabled={!!id}
//             />
//             <AppButton
//               type="button"
//               onClick={() => setIsCustomDialogOpen(true)}
//               label="Select Customer"
//               className="mt-8"
//               disabled={!!id}
//             />
//           </div>
//           <CheckboxField
//             control={form.control}
//             name="isDefault"
//             label="This adjustment was used to write off a bad debt"
//             disabled={!!id}
//           />
//           <div className="col-span-2 mb-4">
//             <TextAreaField form={form} name="instructions" label="Note" />
//           </div>
//         </form>

//         <AppSpinner overlay isLoading={isFetching} />
//       </div>

//       {/* Customer Selection Dialog */}
//       <Selector
//         open={isCustomDialogOpen}
//         onOpenChange={() => setIsCustomDialogOpen((prev) => !prev)}
//         filter={
//           <CustomerFilter
//             filterForm={filterForm}
//             debouncedSearch={debouncedSearch}
//           />
//         }
//         rowSelection={rowSelection}
//         setRowSelection={setRowSelection}
//         columns={columns}
//         isLoading={isCustomerLoading}
//         data={getAllData?.data ?? []}
//         onOkClick={handleOk}
//         onCancelClick={handleCancel}
//         title="Customer Search"
//         bindingKey="customer_id"
//       />
//     </>
//   );
// };

const AddEditCreditCards = () => {
  return <div>AddEditCreditCards</div>;
};

export default AddEditCreditCards;
