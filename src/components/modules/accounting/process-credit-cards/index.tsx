import AppDataTable from '@/components/common/app-data-table';
import ActionColumnMenu from '@/components/common/data-tables/ActionColumn';

import Filter from '@/components/modules/accounting/process-credit-cards/Filter';
import { clearAdjustmentFilter } from '@/redux/features/accounting/adjustmentsSlice';
import { RootState } from '@/redux/store';
import { CCProcessingTypes } from '@/types/accounting.types';
import { ColumnDef } from '@tanstack/react-table';
import { EyeIcon, PrinterIcon } from 'lucide-react';
import { useCallback, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

const ProcessCreditCards = () => {
  const dispatch = useDispatch();
  const [isFilterOpen, setIsFilterOpen] = useState<boolean>(false);

  const filter = useSelector((state: RootState) => state.adjustment.filters);

  const columns: ColumnDef<CCProcessingTypes>[] = useMemo(
    () => [
      {
        accessorKey: 'customer',
        header: 'Customer',
        size: 200,
        enableSorting: true,
      },
      {
        accessorKey: 'orderNo',
        header: 'Order #',
        enableSorting: true,
        size: 150,
      },
      {
        accessorKey: 'dateOfUse',
        header: 'Date of Use',
        size: 150,
        enableSorting: true,
      },
      {
        accessorKey: 'total',
        header: 'Order Total',
        size: 150,
        enableSorting: true,
      },
      {
        accessorKey: 'balanceDue',
        header: 'Balance Due',
        size: 200,
        enableSorting: true,
      },

      {
        accessorKey: 'paymentType',
        header: 'Payment Type',
        size: 200,
        enableSorting: true,
      },
      {
        accessorKey: 'paymentAmount',
        header: 'Payment Amount',
        size: 200,
        enableSorting: true,
      },
      {
        accessorKey: 'paymentDiscount',
        header: 'Payment Discount',
        size: 200,
        enableSorting: true,
      },
      {
        accessorKey: 'paymentReference',
        header: 'Payment Reference',
        size: 200,
        enableSorting: true,
      },
      {
        accessorKey: 'creditCard',
        header: 'Credit Card #',
        size: 180,
        enableSorting: true,
      },
      {
        accessorKey: 'user',
        header: 'User',
        size: 130,
        enableSorting: true,
      },
      {
        id: 'action',
        size: 110,
        header: 'Actions',
        cell: () => (
          <ActionColumnMenu
            customEdit={
              <button
                disabled
                className="flex items-center justify-center gap-2 border border-text-tertiary rounded-md px-3 py-1"
              >
                <EyeIcon /> View Multi-Order Details
              </button>
            }
          />
        ),
      },
    ],
    []
  );

  const DropdownMenu = useMemo(() => {
    return [
      {
        label: 'Process Transactions',
        icon: <PrinterIcon />,
        onClick: () => {},
        className: 'w-80',
      },
    ];
  }, []);

  // handle Clear function
  const handleClearFilter = useCallback(
    (key: string) => {
      dispatch(clearAdjustmentFilter(key));
      setIsFilterOpen(false);
    },
    [dispatch]
  );

  return (
    <div className="flex flex-col p-4 gap-6">
      <AppDataTable
        url={''}
        columns={columns}
        enableSearch={true}
        searchKey="adjustment_ID"
        heading="Process Credit Cards"
        enableFilter
        filter={filter}
        handleClearFilter={handleClearFilter}
        filterClassName="w-[473px]"
        filterContent={<Filter setIsFilterOpen={setIsFilterOpen} />}
        setIsFilterOpen={setIsFilterOpen}
        isFilterOpen={isFilterOpen}
        enablePagination={true}
        tableClassName="max-h-[580px] overflow-auto"
        enableRowSelection
        enableMultiRowSelection
        dropdownMenus={DropdownMenu}
      />
    </div>
  );
};

export default ProcessCreditCards;
