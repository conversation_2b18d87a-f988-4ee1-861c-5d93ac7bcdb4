import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
// Filter Component
import Filter from './Filter';
import adjustmentReducer from '@/redux/features/accounting/adjustmentsSlice';
import { adjustmentsApi } from '@/redux/features/accounting/adjustments.api';

// Filter Component
// Mock API endpoint
const mockGetStoreLocations = vi.fn();
vi.mock('@/redux/api', () => ({
  api: {
    reducerPath: 'api',
    reducer: (state: any = {}) => state,
    middleware: (getDefaultMiddleware: any) => getDefaultMiddleware(),
    endpoints: {
      getStoreLocations: {
        query: mockGetStoreLocations,
      },
    },
    useGetStoreLocationsQuery: () => ({
      data: mockStoreLocations,
      isLoading: false,
      isError: false,
    }),
  },
}));

// Mock store locations data
// Mock data for the API call
const mockStoreLocations = [
  { id: 1, name: 'Location A' },
  { id: 2, name: 'Location B' },
];

// Mock components
vi.mock('@/components/common/app-button', () => ({
  default: ({ label, onClick, disabled }: any) => (
    <button onClick={onClick} disabled={disabled}>
      {label}
    </button>
  ),
}));

vi.mock('@/components/forms/input-field', () => ({
  default: ({ label, name, placeholder, form }: any) => (
    <input
      placeholder={placeholder}
      {...form.register(name)}
      aria-label={label}
    />
  ),
}));

vi.mock('@/components/forms/select-dropdown', () => ({
  default: ({ label, name, placeholder, form, optionsList }: any) => (
    <select {...form.register(name)} aria-label={label}>
      <option value="">{placeholder}</option>
      {optionsList?.map((option: any) => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  ),
}));

// Setup test with proper store configuration
const setupTest = (initialState = {}) => {
  // Configuring the store
  const store = configureStore({
    reducer: {
      adjustment: adjustmentReducer,
      [adjustmentsApi.reducerPath as string]: adjustmentsApi.reducer,
    } as any,
    middleware: (getDefaultMiddleware: any) =>
      getDefaultMiddleware().concat(adjustmentsApi.middleware),
    preloadedState: {
      adjustment: {
        formValues: initialState
          ? { ...initialState }
          : {
              items_ID: '',
              description: '',
              category: '',
              item_location: '',
              unit_price: '',
              replacement_charge: '',
              operator: '',
            },
        filters: [],
      },
      [adjustmentsApi.reducerPath]: {
        queries: {
          getStoreLocations: {
            status: 'fulfilled',
            data: mockStoreLocations,
          },
        },
      },
    },
  });

  // Mock function for setIsFilterOpen prop
  const setIsFilterOpen = vi.fn();

  // Render the Filter component with the store and mock function
  return {
    store,
    setIsFilterOpen,
    ...render(
      <Provider store={store}>
        <Filter setIsFilterOpen={setIsFilterOpen} />
      </Provider>
    ),
  };
};

describe('Filter Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders form fields correctly', () => {
    setupTest();

    expect(screen.getByText('Select Type')).toBeInTheDocument();
    expect(screen.getByText('All Types')).toBeInTheDocument();
    expect(screen.getByText('Deleted')).toBeInTheDocument();
  });

  it('handles form submission', () => {
    const { store } = setupTest();
    // Submit form
    fireEvent.click(screen.getByText('Apply'));
    // Verify state updates
    const state = store.getState();
    expect(state.adjustment.filters).toEqual([]);
  });

  it('handles clear action', () => {
    const { store } = setupTest({
      formValues: {
        items_ID: 'TEST123',
        description: 'Test Description',
      },
    });
    fireEvent.click(screen.getByText('Clear'));
    const state = store.getState();
    expect(state.adjustment.filters).toEqual([]);
  });

  it('updates form values in store', () => {
    const { store } = setupTest();

    const state = store.getState();
    expect(state.adjustment.formValues.items_ID);
  });
});
