// AdjustmentHeaderActions.tsx
import { FC } from 'react';
import AppButton from '@/components/common/app-button';
import { Banknote, Plus } from 'lucide-react';

interface Props {
  onCalculate: () => void;
  onNewCustomer: () => void;
  disabled?: boolean;
}

const AdjustmentHeaderActions: FC<Props> = ({
  onCalculate,
  onNewCustomer,
  disabled,
}) => {
  return (
    <div className="flex justify-end items-center gap-2">
      <AppButton
        label="Calculate Adjustment Amount"
        onClick={onCalculate}
        icon={Banknote}
        className="bg-brand-teal-Default hover:bg-brand-teal-hover border-border-brand-teal-Default text-white"
        variant="neutral"
        disabled={disabled}
      />
      <AppButton
        label="New Customer"
        icon={Plus}
        onClick={onNewCustomer}
        className="bg-brand-teal-Default hover:bg-brand-teal-hover border-border-brand-teal-Default text-white"
        variant="neutral"
        disabled={disabled}
      />
    </div>
  );
};

export default AdjustmentHeaderActions;
