import { useCallback, useEffect, useMemo } from 'react';
import { Submit<PERSON>and<PERSON>, useFieldArray, useForm } from 'react-hook-form';
import { useGetCalculatedAdjustmentQuery } from '@/redux/features/accounting/adjustments.api';

import AppButton from '@/components/common/app-button';
import DataTable from '@/components/common/data-tables';
import Labels from '@/components/forms/Label';
import NumberInputField from '@/components/forms/number-input-field';

import { convertToFloat, DEFAULT_FORMAT, formatDate } from '@/lib/utils';
import { CalculateAdjustmentForm } from '@/types/accounting.types';
import { CheckboxField } from '@/components/forms';

interface CalculateAdjustmentProps {
  open: boolean;
  onOpenChange: () => void;
  onConfirm: (data: CalculateAdjustmentForm) => void;
  customerId: string;
}

const useColumns = ({ form, handleAmountOrder }: any) => {
  return useMemo(
    () => [
      {
        accessorKey: 'orderNo',
        header: 'Order #',
        size: 120,
        cell: ({ row }: any) =>
          row?.original?.orderNo
            ? row?.original?.orderNo
            : row?.original?.adjustmentNo,
      },
      {
        accessorKey: 'dateOfUse',
        header: 'Date of Use',
        size: 130,
        cell: ({ row }: any) =>
          formatDate(row.original.dateOfUse, DEFAULT_FORMAT),
      },
      {
        accessorKey: 'total',
        header: 'Total',
        size: 100,
        maxSize: 120,
        cell: ({ row }: any) =>
          convertToFloat({ value: row.original.total, prefix: '$' }),
      },
      {
        accessorKey: 'balance',
        header: 'Balance',
        size: 100,
        maxSize: 120,
        cell: ({ row }: any) =>
          convertToFloat({ value: row.original.balance, prefix: '$' }),
      },
      {
        accessorKey: 'amountOrder',
        header: 'Amount',
        size: 100,
        cell: ({ row }: any) => (
          <NumberInputField
            name={`payments.${row?.index}.amountOrder`}
            form={form}
            pClassName="p-1"
            className="h-8 w-28"
            placeholder="$0.00"
            onValueChange={handleAmountOrder}
            prefix="$"
            fixedDecimalScale
          />
        ),
      },
    ],
    [form, handleAmountOrder]
  );
};

const CalculateAdjustment = ({
  open,
  onOpenChange,
  onConfirm,
  customerId,
}: CalculateAdjustmentProps) => {
  const { data, isLoading } = useGetCalculatedAdjustmentQuery(customerId, {
    skip: !customerId || !open,
  });

  const defaultFormValues = useMemo(
    () => ({
      amount: 0,
      payments: data?.data?.map((item) => ({ ...item })) ?? [],
      saveDistributionLater: true,
    }),
    [data]
  );

  const form = useForm<CalculateAdjustmentForm>();
  const { control, handleSubmit, watch, reset, setValue } = form;

  const { fields } = useFieldArray({
    control,
    name: 'payments',
  });

  const watchedPayments = watch('payments');

  const handleAmountOrder = useCallback(() => {
    const calculatedAmount = watchedPayments.reduce(
      (sum, p) => sum + Number(p.amountOrder || 0),
      0
    );
    setValue('amount', calculatedAmount);
  }, [setValue, watchedPayments]);

  const columns = useColumns({ form, handleAmountOrder });

  const handleCancel = () => {
    reset();
    onOpenChange();
  };
  const onSubmit: SubmitHandler<CalculateAdjustmentForm> = (formData) => {
    onConfirm(formData);
    handleCancel();
  };

  useEffect(() => {
    if (data?.data) {
      form.reset(defaultFormValues);
    }
  }, [data?.data, defaultFormValues, form]);

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col h-full">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
        <div className="grid grid-cols-2 lg:grid-cols-2 gap-6 items-end">
          <div className="space-y-2 flex gap-2">
            <Labels label="Amount" />
            <NumberInputField
              name="amount"
              form={form}
              label=""
              placeholder="$__.__"
              prefix="$"
              disabled
              fixedDecimalScale
              className="bg-white"
            />
          </div>
          <CheckboxField
            control={form?.control}
            name="saveDistributionLater"
            label="Save Distribution for Later"
          />
        </div>
      </div>

      {/* Table */}
      <div className="flex-1 px-6 py-4">
        <DataTable
          data={fields}
          columns={columns}
          tableClassName="max-h-[320px] 2xl:max-h-[400px] overflow-auto"
          enablePagination={false}
          isLoading={isLoading}
          getRowClassName={(row) => {
            return row.original.status !== 'POSTED' && row.original.orderId
              ? 'bg-green-100 hover:bg-green-100'
              : '';
          }}
        />
      </div>

      {/* Footer */}
      <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
        <div className="flex flex-col sm:flex-row gap-3 sm:justify-end">
          <AppButton
            label={'OK'}
            type="submit"
            className=" w-full sm:w-28"
            disabled={fields.length === 0}
          />
          <AppButton
            label="Cancel"
            type="button"
            onClick={handleCancel}
            variant="neutral"
            className=" w-full sm:w-28"
          />
        </div>
      </div>
    </form>
  );
};

export default CalculateAdjustment;
