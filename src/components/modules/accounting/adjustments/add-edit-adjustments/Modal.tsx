import CustomDialog from '@/components/common/dialog';
import { cn } from '@/lib/utils';
import { ReactNode } from 'react';

interface ModalProps {
  open: boolean;
  onOpenChange: () => void;
  children: ReactNode;
  title?: string;
  description?: string;
  className?: string;
  contentClassName?: string;
  headerClassName?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | 'full';
  height?: 'auto' | 'sm' | 'md' | 'lg' | 'xl' | 'full';
  showHeader?: boolean;
  preventOutsideClose?: boolean;
}

const Modal = ({
  open,
  onOpenChange,
  children,
  title = 'Modal',
  description = '',
  className,
  contentClassName,
  size = 'lg',
  height = 'lg',
  showHeader = true,
  preventOutsideClose = false,
}: ModalProps) => {
  // Responsive width classes based on size
  const sizeClasses = {
    sm: 'w-[90%] max-w-sm sm:w-full',
    md: 'w-[90%] max-w-md sm:w-full',
    lg: 'w-[90%] max-w-lg sm:w-full md:max-w-2xl',
    xl: 'w-[90%] max-w-xl sm:w-full md:max-w-3xl',
    '2xl': 'w-[90%] max-w-2xl sm:w-full md:max-w-4xl',
    '3xl': 'w-[90%] max-w-3xl sm:w-full md:max-w-5xl',
    full: 'w-[95%] max-w-none sm:w-[90%] md:w-[85%]',
  };

  // Responsive height classes
  const heightClasses = {
    auto: 'max-h-[90vh]',
    sm: 'h-[40vh] max-h-[40vh] sm:h-[45vh] sm:max-h-[45vh]',
    md: 'h-[60vh] max-h-[60vh] sm:h-[65vh] sm:max-h-[65vh]',
    lg: 'h-[75vh] max-h-[75vh] sm:h-[80vh] sm:max-h-[80vh]',
    xl: 'h-[85vh] max-h-[85vh] sm:h-[90vh] sm:max-h-[90vh]',
    full: 'h-[95vh] max-h-[95vh]',
  };

  // Content padding classes for different screen sizes
  const contentPaddingClasses =
    height === 'auto' ? 'p-2 sm:p-4 md:px-6' : 'p-0';

  return (
    <CustomDialog
      open={open}
      onOpenChange={preventOutsideClose ? undefined : onOpenChange}
      title={showHeader ? title : undefined}
      description={description}
      className={cn(
        // Base responsive classes
        sizeClasses[size],
        'mx-2 sm:mx-4',
        className
      )}
      contentClassName={cn(
        // Height and overflow classes
        heightClasses[height],
        'overflow-auto flex flex-col',
        contentPaddingClasses,
        contentClassName
      )}
    >
      {height === 'auto' ? (
        // Auto height: content determines size
        <div className="flex flex-col space-y-4">{children}</div>
      ) : (
        // Fixed height: scrollable content
        <div className="flex flex-col h-full">{children}</div>
      )}
    </CustomDialog>
  );
};

export default Modal;
