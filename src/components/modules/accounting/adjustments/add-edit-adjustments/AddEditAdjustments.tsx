import { useCallback, useEffect, useMemo, useState } from 'react';

// Components
import AppSpinner from '@/components/common/app-spinner';
import { PageHeader } from '@/components/common/PageHeader';
import Information from '@/components/modules/orders/customer-info/Information';
import CalculateAdjustment from './CalculateAdjustment';
import AdjustmentForm from './AdjustmentForm';
import AdjustmentHeaderActions from './AdjustmentHeaderActions';
import Modal from './Modal';

// Constants and Utilities
import { ROUTES } from '@/constants/routes-constants';
import {
  formatPhoneNumber,
  getQueryParam,
  normalizeToNegative,
  normalizeToPositive,
} from '@/lib/utils';

// Hooks and API
import { useAdjustmentForm } from './useAdjustmentForm';
import { useGetAdjustmentQuery } from '@/redux/features/accounting/adjustments.api';
import { useGetUserDefaultStoreQuery } from '@/redux/features/store/store.api';

// Types
import {
  AdjustmentDistributionTypes,
  AdjustmentsFormDataTypes,
  CalculateAdjustmentForm,
  OrderDistributionTypes,
  TransactionType,
} from '@/types/accounting.types';

const AddEditAdjustments = () => {
  const id = getQueryParam('id') as string;
  const { data: storeLocationData } = useGetUserDefaultStoreQuery();
  const { data: adjustmentData, isLoading } = useGetAdjustmentQuery(id, {
    skip: !id,
  });
  const [isRefetchNewCustInfo, setIsRefetchNewCustInfo] = useState(false);

  const {
    isSaving,
    isDistributionSaving,
    onSubmit,
    form,
    openModal,
    toggleModal,
    openCustomerDialog,
    toggleCustomerDialog,
  } = useAdjustmentForm();

  // Derived & memoized data
  const defaultValues = useMemo(() => {
    const data = adjustmentData?.data;

    if (!data)
      return {
        adjustmentType: TransactionType.CREDIT,
        adjustmentDate: new Date(),
        status: 'Active',
        salesTaxId:
          storeLocationData?.data?.storeMiscOrderSetting?.defaultSalesTaxCode,
        taxRate: storeLocationData?.data?.storeMiscOrderSetting?.rate,
        tax: 0,
        total: 0,
        convenienceFee: 0,
      };

    const defaultFormValues: AdjustmentsFormDataTypes = {
      ...data,
      customerId: {
        label: data?.customerName ?? '',
        value: data?.customerId.toString(),
      },
      phone: {
        label: formatPhoneNumber(data.phone ?? ''),
        value: data.phone ?? '',
      },
      adjustmentDistribution: [],
      orderDistribution: [],
    };

    return defaultFormValues;
  }, [
    adjustmentData?.data,
    storeLocationData?.data?.storeMiscOrderSetting?.defaultSalesTaxCode,
    storeLocationData?.data?.storeMiscOrderSetting?.rate,
  ]);

  const { reset, getValues, setValue } = form;

  // Effect to reset form when default values change
  useEffect(() => {
    if (defaultValues && storeLocationData?.data) {
      reset(defaultValues);
    }
  }, [defaultValues, reset, storeLocationData?.data]);

  const handleChangeCustomer = useCallback(
    (customer: any) => {
      const {
        first_name,
        last_name,
        customer_id,
        tel1,
        salestaxcode_id,
        salesTaxRate,
      } = customer;
      setValue('customerId', {
        label: `${first_name} ${last_name}`,
        value: customer_id,
      });
      setValue('phone', { label: formatPhoneNumber(tel1), value: tel1 });
      setValue('salesTaxId', salestaxcode_id);
      setValue('salesTaxRate', salesTaxRate ?? 0);
    },
    [setValue]
  );

  const toggleOpenCustomerDialog = useCallback(() => {
    toggleCustomerDialog();
    if (isRefetchNewCustInfo) {
      setIsRefetchNewCustInfo(false);
    }
  }, [isRefetchNewCustInfo, toggleCustomerDialog]);

  const handleAddUpdateCustomer = useCallback(
    (customer: any) => {
      setIsRefetchNewCustInfo(true);
      handleChangeCustomer?.(customer);
      toggleCustomerDialog();
    },
    [handleChangeCustomer, toggleCustomerDialog]
  );

  const handleCalculateAdjustment = (data: CalculateAdjustmentForm) => {
    const taxableTotal = Number(getValues('taxableTotal') ?? 0);
    const tax = Number(getValues('tax') ?? 0);
    const type = getValues('adjustmentType');
    const saveDistributionLater = data?.saveDistributionLater;
    const total =
      normalizeToPositive(taxableTotal) +
      normalizeToPositive(Number(data.amount)) +
      normalizeToPositive(tax);

    if (
      type === TransactionType.CREDIT ||
      type === TransactionType.OVERPAYMENT
    ) {
      setValue('nonTaxableTotal', normalizeToNegative(data?.amount));
      setValue('total', normalizeToNegative(total));
    } else {
      setValue('nonTaxableTotal', normalizeToPositive(Number(data?.amount)));
      setValue('total', normalizeToPositive(Number(total)));
    }

    if (saveDistributionLater) {
      const adjustmentDistribution: AdjustmentDistributionTypes[] = [];
      const orderDistribution: OrderDistributionTypes[] = [];
      data?.payments?.forEach((item) => {
        if (!item.amountOrder) return;

        const amount = Number(item.amountOrder);

        if (item.adjustmentId) {
          adjustmentDistribution.push({
            adjustmentId: item.adjustmentId,
            amount,
          });
        }

        if (item.orderId) {
          orderDistribution.push({
            orderId: item.orderId,
            amount,
          });
        }
      });
      form.setValue('adjustmentDistribution', adjustmentDistribution);
      form.setValue('orderDistribution', orderDistribution);
    }
  };

  const handleCalculateClick = async () => {
    const customer = form.getValues('customerId');

    if (!customer || !customer.value) {
      form.setError('customerId', {
        type: 'manual',
        message: 'Required',
      });
      return;
    }
    form.setValue('adjustmentDistribution', []);
    form.setValue('orderDistribution', []);
    toggleModal();
  };

  return (
    <div className="px-6 pb-4">
      <PageHeader
        title={`${id ? 'Edit' : 'Add'} Adjustment`}
        cancelPath={ROUTES.ADJUSTMENTS}
        handleSubmit={form.handleSubmit(onSubmit)}
        isLoading={isSaving || isDistributionSaving}
      />
      <AdjustmentHeaderActions
        onCalculate={handleCalculateClick}
        onNewCustomer={toggleOpenCustomerDialog}
        disabled={!!id}
      />
      <AdjustmentForm form={form} isRefetchNewCustInfo={isRefetchNewCustInfo} />
      <Modal
        open={openModal}
        onOpenChange={toggleModal}
        title="Calculate Adjustment Amount"
        size="xl"
        height="lg"
        showHeader={true}
      >
        <CalculateAdjustment
          open={openModal}
          onOpenChange={toggleModal}
          onConfirm={handleCalculateAdjustment}
          customerId={form.getValues('customerId.value')}
        />
      </Modal>

      <Modal
        open={openCustomerDialog}
        onOpenChange={toggleOpenCustomerDialog}
        title="New Customer"
        size="xl"
        height="lg"
        showHeader={true}
      >
        <div className="px-4 py-2">
          <Information
            handleOk={handleAddUpdateCustomer}
            handleCancel={toggleOpenCustomerDialog}
          />
        </div>
      </Modal>

      <AppSpinner overlay isLoading={isLoading} />
    </div>
  );
};

export default AddEditAdjustments;
