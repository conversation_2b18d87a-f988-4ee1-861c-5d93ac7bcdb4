import { useCallback, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import {
  useSaveAdjustmentMutation,
  useSaveDistributionMutation,
} from '@/redux/features/accounting/adjustments.api';

import { ROUTES } from '@/constants/routes-constants';
import { AdjustmentsFormDataTypes } from '@/types/accounting.types';
import {
  DATE_FORMAT_YYYYMMDD,
  formatDate,
  getQueryParam,
  updateQueryParam,
} from '@/lib/utils';

export const useAdjustmentForm = () => {
  const id = getQueryParam('id') as string;
  const navigate = useNavigate();
  const form = useForm<AdjustmentsFormDataTypes>();
  const [saveAdjustment, { isLoading: isSaving }] = useSaveAdjustmentMutation();
  const [saveDistribution, { isLoading: isDistributionSaving }] =
    useSaveDistributionMutation();

  const [openModal, setOpenModal] = useState(false);
  const [openCustomerDialog, setOpenCustomerDialog] = useState(false);

  const toggleModal = useCallback(() => setOpenModal((prev) => !prev), []);

  const toggleCustomerDialog = () => setOpenCustomerDialog((prev) => !prev);

  const onSubmit = async (formData: AdjustmentsFormDataTypes) => {
    try {
      const response = await saveAdjustment({
        ...convertFormData(formData, id),
      }).unwrap();

      if (!id && response?.data?.id && response?.success) {
        const newId = response?.data?.id;

        if (
          formData?.adjustmentDistribution?.length ||
          formData?.orderDistribution?.length
        ) {
          const distributionResponse = await saveDistribution({
            parentAdjustmentId: newId,
            adjustmentDistribution: formData?.adjustmentDistribution,
            orderDistribution: formData?.orderDistribution,
          });

          if (distributionResponse?.data?.success) {
            navigate(ROUTES.EDIT_ADJUSTMENTS);
            updateQueryParam(newId);
          }
        } else {
          navigate(ROUTES.EDIT_ADJUSTMENTS);
          updateQueryParam(newId);
        }
      }
    } catch (error) {}
  };

  return {
    id,
    form,
    isSaving,
    isDistributionSaving,
    onSubmit,
    openModal,
    toggleModal,
    openCustomerDialog,
    toggleCustomerDialog,
  };
};

function convertFormData(data: AdjustmentsFormDataTypes, id: string) {
  const {
    adjustmentDate,
    customerId,
    phone,
    taxableTotal,
    nonTaxableTotal,
    convenienceFees,
    salesTaxId,
    isBadDebt,
    notes,
    adjustmentType,
  } = data;
  return {
    id: id ? Number(id) : null,
    customerId: Number(customerId?.value) ?? 0,
    phone: phone?.value ? phone?.value : null,
    adjustmentDate: formatDate(adjustmentDate, DATE_FORMAT_YYYYMMDD),
    taxableTotal: Number(taxableTotal ?? 0),
    nonTaxableTotal: Number(nonTaxableTotal ?? 0),
    convenienceFees: Number(convenienceFees ?? 0),
    salesTaxId,
    isBadDebt: isBadDebt ?? false,
    notes,
    adjustmentType,
  };
}
