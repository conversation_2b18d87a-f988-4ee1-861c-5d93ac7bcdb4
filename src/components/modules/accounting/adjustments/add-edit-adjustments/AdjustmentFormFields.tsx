import { UseFormReturn } from 'react-hook-form';
import useOptionList from '@/hooks/useOptionList';
import { useGetEnumsListQuery } from '@/redux/features/enums-api/enums-api';
import CustomerLookup from '@/components/common/lookups/customer-lookup';
import {
  AutoCompleteDropdown,
  CheckboxField,
  DatePicker,
  InputField,
  NumberInputField,
  SelectWidget,
  TextAreaField,
} from '@/components/forms';
import {
  CUSTOMER_API_ROUTES,
  SALES_TAX_CODE_API_ROUTES,
} from '@/constants/api-constants';
import {
  REQUIRED_TEXT,
  TEXT_VALIDATION_RULE,
} from '@/constants/validation-constants';
import {
  formatPhoneNumber,
  getQueryParam,
  normalizeToNegative,
  normalizeToPositive,
  stripCountryCode,
} from '@/lib/utils';
import {
  AdjustmentsFormDataTypes,
  TransactionType,
} from '@/types/accounting.types';

interface AdjustmentFormFieldsProps {
  form: UseFormReturn<AdjustmentsFormDataTypes>;
  isRefetchNewCustInfo: boolean;
}

export const AdjustmentFormFields = ({
  form,
  isRefetchNewCustInfo,
}: AdjustmentFormFieldsProps) => {
  const id = getQueryParam('id') as string;
  const { setValue, getValues, formState } = form;
  const { data: adjustmentType, isLoading: adjustmentTypeLoading } =
    useGetEnumsListQuery({
      name: 'AdjustmentType',
    });
  const { options: salesTaxCodeList, optionLoading } = useOptionList({
    url: SALES_TAX_CODE_API_ROUTES.ALL,
    valueKey: 'salestaxcode_id',
    labelKey: 'salestaxcode',
    extraKey: 'salestaxrate',
    sortBy: 'salestaxcode',
  });

  const updateAmountValues = ({
    taxableTotal,
    nonTaxableTotal,
    tax,
    total,
    type,
  }: {
    taxableTotal: number | null;
    nonTaxableTotal: number | null;
    tax: number;
    total: number;
    type: string;
  }) => {
    if (
      type === TransactionType.CREDIT ||
      type === TransactionType.OVERPAYMENT
    ) {
      setValue(
        'taxableTotal',
        taxableTotal ? normalizeToNegative(taxableTotal) : null
      );
      setValue(
        'nonTaxableTotal',
        nonTaxableTotal ? normalizeToNegative(nonTaxableTotal) : null
      );
      setValue('tax', normalizeToNegative(tax));
      setValue('total', normalizeToNegative(total));
    } else {
      setValue(
        'taxableTotal',
        taxableTotal ? normalizeToPositive(taxableTotal) : null
      );
      setValue(
        'nonTaxableTotal',
        nonTaxableTotal ? normalizeToPositive(nonTaxableTotal) : null
      );
      setValue('tax', normalizeToPositive(tax));
      setValue('total', normalizeToPositive(total));
    }
  };

  const handleTypeChange = (value: string) => {
    const taxableTotal = Number(getValues('taxableTotal'));
    const nonTaxableTotal = Number(getValues('nonTaxableTotal'));
    const tax = Number(getValues('tax'));
    const total = Number(getValues('total'));

    updateAmountValues({
      type: value,
      taxableTotal,
      nonTaxableTotal,
      tax,
      total,
    });
  };

  const handleChangeCustomer = (customer: any) => {
    const { full_name, customer_id, tel1, salestaxcode_id, salesTaxRate } =
      customer;
    setValue('customerId', {
      label: full_name,
      value: customer_id,
    });
    setValue('phone', { label: formatPhoneNumber(tel1), value: tel1 });
    setValue('salesTaxId', salestaxcode_id);
    setValue('salesTaxRate', salesTaxRate ?? 0);

    form.clearErrors('customerId');
  };

  const handleOnBlur = () => {
    const taxableTotal = Number(getValues('taxableTotal'));
    const nonTaxableTotal = Number(getValues('nonTaxableTotal'));
    const taxRate = Number(getValues('salesTaxRate') ?? 0);
    const tax = normalizeToPositive(taxableTotal ?? 0) * (taxRate / 100);

    const total =
      normalizeToPositive(taxableTotal || 0) +
      normalizeToPositive(nonTaxableTotal || 0) +
      normalizeToPositive(tax);
    const type = getValues('adjustmentType');

    updateAmountValues({
      type,
      taxableTotal,
      nonTaxableTotal,
      tax,
      total,
    });
  };

  const handleTaxCode = (taxCode: string) => {
    const selectedTaxCode = salesTaxCodeList?.find(
      (code: any) => code.value === taxCode
    );

    if (selectedTaxCode) {
      setValue('salesTaxRate', selectedTaxCode.extraKey || 0);
    }
    handleOnBlur();
  };

  return (
    <>
      <InputField
        form={form}
        name="id"
        label="Adjustment #"
        placeholder="Adjustment #"
        disabled
      />
      <InputField
        form={form}
        name="status"
        label="Status"
        placeholder="Enter Status"
        disabled
      />
      <SelectWidget
        form={form}
        name="adjustmentType"
        label="Type"
        isClearable={false}
        placeholder="Select Type"
        optionsList={adjustmentType?.data}
        disabled={!!id}
        onSelectChange={handleTypeChange}
        isLoading={adjustmentTypeLoading}
      />
      <DatePicker
        form={form}
        name="adjustmentDate"
        label="Date"
        placeholder="Date"
        disabled={!!id}
        enableInput
        validation={{ required: REQUIRED_TEXT }}
      />
      <div className="flex items-center gap-x-2">
        <AutoCompleteDropdown
          label="Customer"
          placeholder="Select Customer Name"
          name="customerId"
          form={form}
          onSelectChange={(option) => handleChangeCustomer(option.item)}
          url={CUSTOMER_API_ROUTES.ALL}
          labelKey="full_name"
          valueKey="customer_id"
          sortBy="first_name"
          validation={TEXT_VALIDATION_RULE}
          showItem
          disabled={!!id}
          isRefetch={isRefetchNewCustInfo}
        />
        <CustomerLookup
          tooltip="Customer"
          label=""
          disabled={!!id}
          handleOk={(customer) => handleChangeCustomer(customer)}
          className={formState?.errors?.customerName?.message ? 'mt-2' : 'mt-8'}
        />
      </div>
      <AutoCompleteDropdown
        label="Phone"
        placeholder="Select Phone"
        name="phone"
        form={form}
        onSelectChange={(option) => handleChangeCustomer(option?.item)}
        showItem
        url={CUSTOMER_API_ROUTES.ALL}
        labelKey="tel1"
        valueKey="tel1"
        sortBy="first_name"
        operator="StartsWith"
        formatLabel={stripCountryCode}
        labelComponent={formatPhoneNumber}
        disabled={!!id}
        isRefetch={isRefetchNewCustInfo}
      />
      <NumberInputField
        form={form}
        name="taxableTotal"
        label="Taxable Total"
        placeholder="$______.__"
        prefix="$"
        maxLength={10}
        disabled={!!id}
        allowNegative
        onBlur={handleOnBlur}
        fixedDecimalScale
      />
      <div></div>
      <NumberInputField
        form={form}
        name="nonTaxableTotal"
        label="Non-Taxable Total"
        placeholder="$______.__"
        prefix="$"
        maxLength={10}
        allowNegative
        disabled={!!id}
        fixedDecimalScale
        onBlur={handleOnBlur}
      />
      <NumberInputField
        form={form}
        name="convenienceFee"
        label="Convenience Fee"
        placeholder="$______.__"
        prefix="$"
        maxLength={10}
        fixedDecimalScale
        disabled
      />
      <SelectWidget
        form={form}
        name="salesTaxId"
        label="Tax Code"
        isClearable={false}
        placeholder="Select Tax Code"
        isLoading={optionLoading}
        optionsList={salesTaxCodeList}
        onSelectChange={handleTaxCode}
        disabled={!!id}
      />
      <NumberInputField
        form={form}
        name="salesTaxRate"
        label="Tax Rate"
        placeholder="__.__%"
        suffix="%"
        maxLength={7}
        disabled
        fixedDecimalScale
      />
      <NumberInputField
        form={form}
        name="tax"
        label="Tax"
        placeholder="$______.__"
        prefix="$"
        maxLength={10}
        disabled
        allowNegative
        fixedDecimalScale
      />
      <NumberInputField
        form={form}
        name="total"
        label="Total"
        placeholder="$______.__"
        prefix="$"
        maxLength={10}
        disabled
        allowNegative
        fixedDecimalScale
      />
      <CheckboxField
        pClassName="col-span-2"
        control={form.control}
        name="isBadDebt"
        label="This adjustment was used to write off a bad debt"
        disabled={!!id}
      />
      <div className="col-span-2 mb-4">
        <TextAreaField form={form} name="notes" label="Note" />
      </div>
    </>
  );
};
