import { UseFormReturn } from 'react-hook-form';
import { AdjustmentsFormDataTypes } from '@/types/accounting.types';
import { AdjustmentFormFields } from './AdjustmentFormFields';

interface AdjustmentFormProps {
  form: UseFormReturn<AdjustmentsFormDataTypes>;
  isRefetchNewCustInfo: boolean;
}

const AdjustmentForm = ({
  form,
  isRefetchNewCustInfo,
}: AdjustmentFormProps) => {
  return (
    <form className="grid grid-cols-2 gap-4 mt-4">
      <AdjustmentFormFields
        form={form}
        isRefetchNewCustInfo={isRefetchNewCustInfo}
      />
    </form>
  );
};

export default AdjustmentForm;
