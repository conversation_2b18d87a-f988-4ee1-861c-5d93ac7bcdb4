import AppButton from '@/components/common/app-button';
import Input<PERSON>ield from '@/components/forms/input-field';
import SelectDropDown from '@/components/forms/select-dropdown';
import { Separator } from '@/components/ui/separator';
import {
  OperatorListFor1,
  OperatorListFor2,
} from '@/constants/purchaseOrder-constant';
import { getQueryParam, updateQueryParam } from '@/lib/utils';
import {
  clearAllAdjustmentFilters,
  setAdjustmentFilter,
  updateAdjustmentFormValues,
} from '@/redux/features/accounting/adjustmentsSlice';
import { RootState } from '@/redux/store';
import { memo, useEffect } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import { AdjustmnetType, searchByFilters } from './constants';

// Define the interface for filter data
interface FilterFormValues {
  [key: string]: string | null | number;
}

const defaultValues: FilterFormValues = {
  type: '',
  searchBy: '',
  operator: '',
  value: '',
};

interface FilterProps {
  setIsFilterOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const Filter: React.FC<FilterProps> = ({ setIsFilterOpen }) => {
  const search = getQueryParam('search') as string;
  const formValuesFromRedux = useSelector(
    (state: RootState) => state.adjustment.formValues
  );
  const dispatch = useDispatch();

  // Initialize form with values from Redux
  const form = useForm<FilterFormValues>({
    defaultValues: formValuesFromRedux,
    mode: 'onChange',
  });
  const isSelectedSearchBy = form.watch('searchBy');

  // Watch form changes and update Redux state
  useEffect(() => {
    const subscription = form.watch((values) => {
      dispatch(updateAdjustmentFormValues(values));
    });

    const defaultValues = {
      operator: 'EQUALS',
    };

    Object.entries(defaultValues).forEach(([key, defaultValue]) => {
      if (!form.getValues(key)) {
        form.setValue(key, defaultValue);
      }
    });

    return () => subscription.unsubscribe();
  }, [form, dispatch]);

  const toLabel = (field: string): string => {
    switch (field) {
      case 'customerName':
        return 'Customer Name';
      case 'adjustmentNo':
        return 'Adjustment #';
      case 'total':
        return 'Total';

      default:
        return field;
    }
  };

  const onSubmit: SubmitHandler<FilterFormValues> = (data) => {
    const newFilterData: any[] = [{}];

    // Handle searchBy + value field
    if (data.searchBy && data.value) {
      newFilterData.push({
        label: toLabel(data.searchBy?.toString() ?? ''),
        name: data.searchBy,
        value: data.value,
        tagValue: data.value,
        operator: data.operator,
      });
    }

    if (data?.type) {
      newFilterData.push({
        label: 'Type',
        name: 'status',
        value: data.type,
        tagValue: data.type === 'ACTIVE' ? 'All Types' : 'Deleted',
        operator: 'Contains',
      });
    }

    dispatch(setAdjustmentFilter(newFilterData));
    setIsFilterOpen(false);
  };

  const handleClear = () => {
    form.reset(defaultValues); // Reset form to default values
    dispatch(clearAllAdjustmentFilters()); // Clear all filters in Redux
    setIsFilterOpen(false); // Close the filter form
    if (search) {
      updateQueryParam(null, 'search');
    }
  };

  const handleSearchByChange = (value: string) => {
    if (value === 'customerName') {
      form.setValue('operator', 'CONTAINS');
    } else {
      form.setValue('operator', 'EQUALS');
    }
  };

  // Track if form values are different from the default values
  const isFormModified =
    JSON.stringify(form.watch()) !== JSON.stringify(defaultValues);

  useEffect(() => {
    if (!form.watch('searchBy')) {
      form.setValue('searchValue', '');
    }
  }, [form]);

  useEffect(() => {
    if (search) {
      form.setValue('searchBy', 'customer');
      form.setValue('searchValue', search);
      form.setValue('operator', 'equals');
    }
  }, [form, search]);

  return (
    <>
      <div className="px-3">
        <div className="text-normal py-2  font-semibold">Filters</div>
        <Separator
          orientation="horizontal"
          className="h-[1px] bg-border-Default mb-2"
        />

        <div className="grid col-span-1 md:grid-cols-1 gap-3">
          <SelectDropDown
            form={form}
            name="type"
            label="Type"
            placeholder="Select Type"
            optionsList={AdjustmnetType}
            allowClear={false}
          />
          <div className="grid grid-cols-2 gap-3">
            <SelectDropDown
              form={form}
              name="searchBy"
              label="Search By"
              placeholder="Select Search By"
              onChange={handleSearchByChange}
              optionsList={searchByFilters}
              allowClear={false}
            />
            <SelectDropDown
              form={form}
              name="operator"
              label="Operator"
              allowClear={false}
              placeholder="Select Operator"
              optionsList={
                isSelectedSearchBy === 'customerName'
                  ? OperatorListFor1
                  : isSelectedSearchBy === 'adjustmentNo' ||
                      isSelectedSearchBy === 'total'
                    ? OperatorListFor2
                    : OperatorListFor2
              }
            />
          </div>
          <InputField
            form={form}
            name="value"
            disabled={!form.watch('searchBy')}
            label="Value"
            placeholder="Enter Description"
          />
        </div>
        <div className="w-full h-full flex justify-between gap-3 mt-3 sticky -bottom-2 pt-1 pb-3 bg-white z-30">
          <AppButton
            className="w-full"
            onClick={form.handleSubmit(onSubmit)}
            label="Apply"
            disabled={!isFormModified}
          />
          <AppButton
            className="w-full"
            label="Clear"
            variant="neutral"
            onClick={handleClear}
            disabled={!isFormModified} // Disable button if no changes
          />
        </div>
      </div>
    </>
  );
};

export default memo(Filter);
