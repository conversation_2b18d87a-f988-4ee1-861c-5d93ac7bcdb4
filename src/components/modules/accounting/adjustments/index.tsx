import EditIcon from '@/assets/icons/EditIcon';
import AppButton from '@/components/common/app-button';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import AppDataTable from '@/components/common/app-data-table';
import AppSpinner from '@/components/common/app-spinner';
import ColumnReOrdering from '@/components/common/column-reording';
import ActionColumnMenu from '@/components/common/data-tables/ActionColumn';
import Filter from '@/components/modules/accounting/adjustments/Filter';
import { ADJUSTMENTS_API_ROUTES } from '@/constants/api-constants/accounting-api-constants';
import { ROUTES } from '@/constants/routes-constants';
import { convertToFloat } from '@/lib/utils';
import { useDeleteAdjustmentMutation } from '@/redux/features/accounting/adjustments.api';
import { clearAdjustmentFilter } from '@/redux/features/accounting/adjustmentsSlice';
import { useAddNewItemMutation } from '@/redux/features/common-api/common.api';
import { useGetListQuery } from '@/redux/features/user-options/profile-details.api';
import { RootState } from '@/redux/store';
import { FilterItemDTO, RawFilterItem } from '@/types/purchase-order.types';
import { Row } from '@tanstack/react-table';
import { EyeIcon, PlusIcon } from 'lucide-react';
import { useCallback, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useNavigate } from 'react-router-dom';

interface DeleteState {
  open: boolean;
  id: number | null;
}

const Adjustments = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  // Redux selectors
  const filters = useSelector((state: RootState) => state.adjustment.filters);

  // Local state
  const [openColumnOrdering, setOpenColumnOrdering] = useState<boolean>(false);
  const [isFilterOpen, setFilterOpen] = useState<boolean>(false);
  const [refreshList, setRefreshList] = useState<boolean>(false);
  const [deleteState, setDeleteState] = useState<DeleteState>({
    open: false,
    id: null,
  });

  const [addColumnOrder, { isLoading: ordering }] = useAddNewItemMutation();
  const {
    data: columnData,
    refetch,
    isFetching,
  } = useGetListQuery({ url: ADJUSTMENTS_API_ROUTES.GET_COLUMN });

  const [deleteAdjustment, { isLoading: isDeleteLoading }] =
    useDeleteAdjustmentMutation();

  // toggle delete
  const toggleDelete = useCallback((id: number) => {
    setDeleteState({ open: true, id });
  }, []);

  const handleCancel = () => setDeleteState({ open: false, id: null });

  const handleDelete = async () => {
    try {
      await deleteAdjustment(deleteState?.id ?? 0).unwrap();
      setDeleteState({ open: false, id: null });
      setRefreshList(true);
      setTimeout(() => setRefreshList(false), 500);
      refetch();
    } catch (error) {}
  };

  const handleClearFilter = useCallback(
    (key: string) => {
      dispatch(clearAdjustmentFilter(key));
      setFilterOpen(false);
    },
    [dispatch]
  );

  const handleColumnOrder = useCallback(
    async (formValues: Record<string, boolean>) => {
      if (!columnData) return;
      const updated = columnData.data.map((col: any) => ({
        ...col,
        enabled: !!formValues[col.accessorKey],
      }));
      await addColumnOrder({
        url: ADJUSTMENTS_API_ROUTES.UPDATE_COLUMN,
        data: updated,
      }).unwrap();
      setOpenColumnOrdering(false);
      setRefreshList(true);
      setTimeout(() => setRefreshList(false), 500);
      refetch();
    },
    [addColumnOrder, columnData, refetch]
  );

  const componentMap: any = useMemo(
    () => ({
      LabelDollar: ({ value }: any) => convertToFloat({ value, prefix: '$' }),
    }),
    []
  );

  const tableColumns = useMemo(() => {
    return columnData?.data;
  }, [columnData?.data]);

  const memoizedColumns = useMemo(() => {
    if (!tableColumns?.length) return [];

    const formattedColumns = tableColumns
      .filter((column: any) => column.enabled)
      .map((column: any) => ({
        accessorKey: column.accessorKey,
        header: column.header,
        enableSorting: column.enableSorting || false,
        size: column.size || 200,
        ...(column.accessorKey === 'total' && {
          className: (row: Row<any>) => {
            const value = row.original?.total;
            return Number(value) < 0 ? 'text-red-500' : 'pl-6';
          },
        }),
        cell: ({ row }: any) => {
          const Component = componentMap[column.component];
          return Component ? (
            <Component value={row?.original?.[column.accessorKey]} />
          ) : (
            row?.original?.[column.accessorKey]
          );
        },
      }));

    return [
      {
        accessorKey: 'ordering',
        header: () => (
          <ColumnReOrdering
            isOpen={openColumnOrdering}
            handleOrderingColumn={handleColumnOrder}
            tableColumns={tableColumns}
            setOpenColumnOrdering={setOpenColumnOrdering}
          />
        ),
        size: 50,
      },
      ...formattedColumns,
      {
        id: 'action',
        size: 110,
        header: 'Actions',
        cell: ({ row }: any) => {
          const isDeleted = row.original?.isDeleted;
          return (
            <ActionColumnMenu
              customEdit={
                <Link
                  to={`${ROUTES.EDIT_ADJUSTMENTS}?id=${row.original.id}`}
                  className="flex items-center justify-center gap-2 border border-text-tertiary rounded-md px-3 py-1"
                >
                  {isDeleted ? <EyeIcon className="w-5 h-5" /> : <EditIcon />}
                  <span className="w-24">
                    {isDeleted ? 'View' : 'Edit'} details
                  </span>
                </Link>
              }
              disabled={isDeleted}
              onDelete={() => toggleDelete(row?.original?.id)}
            />
          );
        },
      },
    ];
  }, [
    tableColumns,
    componentMap,
    openColumnOrdering,
    handleColumnOrder,
    toggleDelete,
  ]);

  // Custom toolbar
  const CustomToolbar = (
    <AppButton
      icon={PlusIcon}
      iconClassName="w-5 h-5"
      label="New Adjustment"
      onClick={() => navigate(ROUTES.ADD_ADJUSTMENTS)}
    />
  );
  const updatedFilter: FilterItemDTO[] = useMemo(() => {
    return (filters as RawFilterItem[]).map(
      (item): FilterItemDTO => ({
        field: item.name ?? null,
        label: item?.label,
        operator: item.operator,
        name: item.name,
        tagValue: item.tagValue ?? null,
        value: item.value,
      })
    );
  }, [filters]);

  return (
    <div className="flex flex-col p-4 gap-6">
      <AppDataTable
        url={ADJUSTMENTS_API_ROUTES.ALL}
        columns={memoizedColumns}
        heading="Adjustments"
        searchKey="filter"
        enableFilter
        filter={updatedFilter}
        handleClearFilter={handleClearFilter}
        filterClassName="w-[473px]"
        filterContent={<Filter setIsFilterOpen={setFilterOpen} />}
        setIsFilterOpen={setFilterOpen}
        isFilterOpen={isFilterOpen}
        customToolBar={CustomToolbar}
        enablePagination={true}
        refreshList={refreshList}
        tableClassName="max-h-[580px] overflow-auto"
        enableSearch
        searchOperator="equals"
      />
      <AppConfirmationModal
        description={<div>Are you sure you want to delete ?</div>}
        open={deleteState?.open}
        handleCancel={handleCancel}
        disabled={isDeleteLoading}
        isLoading={isDeleteLoading}
        handleSubmit={handleDelete}
      />
      <AppSpinner overlay isLoading={ordering || isFetching} />
    </div>
  );
};

export default Adjustments;
