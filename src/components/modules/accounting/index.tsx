import DataTable from '@/components/common/data-tables/index';
import { accountingData } from '@/mock-data/accounting-mock-data';
import { AccountingTypes } from '@/types/accounting.types';
import { ColumnDef } from '@tanstack/react-table';
import { useMemo } from 'react';
import { Link } from 'react-router-dom';

const Accountings = () => {
  // Memoized table columns
  const columns: ColumnDef<AccountingTypes>[] = useMemo(
    () => [
      {
        accessorKey: 'accountingName',
        header: 'Accounting Name',
        cell: (info) => {
          return (
            <Link to={info.row.original.to}>
              <div className="cursor-pointer">
                {(info.getValue() as string) ?? ''}
              </div>
            </Link>
          );
        },
      },
    ],
    []
  );

  return (
    <div className="flex flex-col p-6 gap-6">
      <DataTable
        data={accountingData}
        columns={columns}
        heading="Accountings"
        enablePagination={false}
      />
    </div>
  );
};

export default Accountings;
