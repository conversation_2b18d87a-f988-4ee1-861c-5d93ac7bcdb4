import AppButton from '@/components/common/app-button';
import AppSpinner from '@/components/common/app-spinner';
import CustomDialog from '@/components/common/dialog';
import CustomerLookup from '@/components/common/lookups/customer-lookup';
import { PageHeader } from '@/components/common/PageHeader';
import AutoCompleteDropdown from '@/components/forms/auto-complete-dropdown';
import DatePicker from '@/components/forms/date-picker';
import InputField from '@/components/forms/input-field';
import Labels from '@/components/forms/Label';
import NumberInputField from '@/components/forms/number-input-field';
import PhoneInputWidget from '@/components/forms/phone-input-mask';
import RadioField from '@/components/forms/radio-field';
import Notes from '@/components/modules/orders/customer-info/notes';
import { CUSTOMER_API_ROUTES } from '@/constants/api-constants';
import { ROUTES } from '@/constants/routes-constants';
import {
  DATE_FORMAT_YYYYMMDD,
  formatDate,
  formatPhoneNumber,
  getQueryParam,
  stripCountryCode,
  updateQueryParam,
} from '@/lib/utils';
import { useGetInquiryDetailsMutation } from '@/redux/features/accounting/Inquiry.api';
import { useGetEnumsListQuery } from '@/redux/features/enums-api/enums-api';
import { InquiryFormTypes } from '@/types/accounting.types';
import dayjs from 'dayjs';
import { Notebook, Printer } from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import InquiryListTable from './InquiryListTable';

const InquiryDetails = () => {
  const customerId = getQueryParam('customerId') as string;
  const [openNote, setOpenNote] = useState<boolean>(false);
  const [filterOption, setFilterOption] = useState<{
    displayEnum: string;
    balanceTransactionEnum: string;
    balanceTransactionVal: string;
  }>({
    displayEnum: 'CURRENT',
    balanceTransactionEnum: 'TODAY',
    balanceTransactionVal: '',
  });

  // toggle open note
  const toggleOpenNote = () => {
    setOpenNote((prev) => !prev);
  };

  // Inquiry details
  const [getInquiryDetails, { data, isLoading }] =
    useGetInquiryDetailsMutation();

  // display ENUM
  const { data: displayEnum } = useGetEnumsListQuery({
    name: 'InquiryDisplayEnum',
  });
  // Balances & Transactions of ENUM
  const { data: InquiryBalanceTransaction } = useGetEnumsListQuery({
    name: 'InquiryBalanceTransactionEnum',
  });

  const defaultValues = useMemo(() => {
    const inquiryData: InquiryFormTypes = data?.data;
    return {
      customerId: customerId ?? '',
      customer: {
        label: inquiryData?.customerFullName ?? '',
        value: inquiryData?.customerId,
      },
      phone: {
        label: formatPhoneNumber(inquiryData?.phone),
        value: inquiryData?.phone,
      },
      fax: inquiryData?.fax,
      creditHold: inquiryData?.creditHold ? 'Yes' : 'No',
      baseDiscount: inquiryData?.baseDiscount ?? 0,
      balance: inquiryData?.balance ?? 0,
      deposits: inquiryData?.deposits ?? 0,
      agedCurrent: inquiryData?.agedCurrent ?? 0,
      agedOver30: inquiryData?.agedOver30 ?? 0,
      agedOver60: inquiryData?.agedOver60 ?? 0,
      agedOver90: inquiryData?.agedOver90 ?? 0,
      displayEnum: filterOption?.displayEnum,
      balanceTransactionEnum: filterOption?.balanceTransactionEnum,
      balanceTransactionEnumVal: filterOption.balanceTransactionVal,
    };
  }, [customerId, data?.data, filterOption]);

  const form = useForm<InquiryFormTypes>({});

  useEffect(() => {
    if (defaultValues) {
      form.reset(defaultValues);
    }
  }, [defaultValues, form]);

  const fetchInquiryDetails = useCallback(() => {
    const inquiryDetail = form.watch();
    const payload = {
      customerID: inquiryDetail?.customerId,
      displayEnum: inquiryDetail?.displayEnum,
      balanceTransactionEnum: inquiryDetail?.balanceTransactionEnum,
      balanceTransactionEnumVal: formatDate(
        inquiryDetail?.balanceTransactionEnumVal,
        DATE_FORMAT_YYYYMMDD
      ),
    };
    getInquiryDetails(payload);
  }, [form, getInquiryDetails]);

  useEffect(() => {
    if (customerId) {
      fetchInquiryDetails();
    }
  }, [customerId, fetchInquiryDetails]);

  // print option
  const dropdownMenu = [
    {
      label: 'Print',
      icon: <Printer className="h-5 w-5" />,
      onClick: () => {},
      disabled: true,
    },
  ];

  const balanceTransactionEnum = form.watch('balanceTransactionEnum');

  // display list
  const displayList = displayEnum && [...displayEnum?.data].reverse();

  // Aged Balances View
  const AgedBalances = [
    { label: 'Current', name: 'agedCurrent' },
    { label: 'Over 30', name: 'agedOver30' },
    { label: 'Over 60', name: 'agedOver60' },
    { label: 'Over 90', name: 'agedOver90' },
  ];

  // Balances & Transactions of  List
  const InquiryBalanceTransactionList = InquiryBalanceTransaction?.data?.map(
    (item: any) => ({
      ...item,
      content: item?.value === 'ASOF' && (
        <DatePicker
          name="balanceTransactionEnumVal"
          form={form}
          placeholder="As of"
          enableInput
          disabled={balanceTransactionEnum !== 'ASOF'}
          inputClassName="w-36"
          onDateChange={(date) => {
            dayjs(date).isValid() && fetchInquiryDetails();

            setFilterOption((prev) => ({
              ...prev,
              balanceTransactionVal: formatDate(
                date ?? '',
                DATE_FORMAT_YYYYMMDD
              ),
            }));
          }}
        />
      ),
    })
  );

  // handle change customer
  const handleChangeCustomer = (customerId: string | number) => {
    updateQueryParam(customerId, 'customerId');
    form.setValue('customerId', customerId);
    fetchInquiryDetails();
  };

  const handleBalanceTransactionChange = (value: string) => {
    const isAsof = value !== 'ASOF';
    if (isAsof || (value === 'ASOF' && filterOption.balanceTransactionVal)) {
      fetchInquiryDetails();
    }
    setFilterOption((prev) => ({ ...prev, balanceTransactionEnum: value }));
  };

  return (
    <div className="px-6 pb-4">
      <PageHeader
        title="Inquiry"
        cancelPath={ROUTES.INQUIRY}
        dropdownMenu={dropdownMenu}
      />
      <div className="grid grid-cols-5 gap-x-4 gap-y-3">
        <div className="flex items-center gap-x-2">
          <AutoCompleteDropdown
            label="Customer"
            placeholder="Select Customer"
            name="customer"
            form={form}
            onSelectChange={(option) => handleChangeCustomer(option?.value)}
            url={CUSTOMER_API_ROUTES.ALL}
            labelKey="full_name"
            valueKey="customer_id"
            sortBy="full_name"
          />
          <CustomerLookup
            label=""
            className="mt-8"
            tooltip="Search"
            handleOk={(option) => handleChangeCustomer(option?.customer_id)}
          />
        </div>
        <InputField
          name="customerId"
          label="Customer #"
          placeholder="Customer #"
          form={form}
          disabled
        />
        <InputField
          name="creditHold"
          label="Credit Hold"
          form={form}
          disabled
        />
        <NumberInputField
          name="baseDiscount"
          label="Base Discount %"
          prefix="%"
          form={form}
          disabled
          allowNegative
          fixedDecimalScale
        />
        <NumberInputField
          name="balance"
          label="Balance"
          prefix="$"
          form={form}
          disabled
          thousandSeparator=","
          allowNegative
          fixedDecimalScale
        />

        <AutoCompleteDropdown
          label="Phone"
          placeholder="Select Phone"
          name="phone"
          form={form}
          onSelectChange={(option) =>
            handleChangeCustomer(option?.item?.customer_id)
          }
          url={CUSTOMER_API_ROUTES.ALL}
          labelKey="tel1"
          valueKey="tel1"
          sortBy="first_name"
          formatLabel={stripCountryCode}
          labelComponent={formatPhoneNumber}
          showItem
          acceptAlphanumeric={false}
        />
        <PhoneInputWidget label="Fax" name="fax" form={form} disabled />
        <div className="col-span-2">
          <AppButton
            label="Notes"
            icon={Notebook}
            iconClassName="w-5 h-5"
            className="mt-8"
            disabled={!customerId}
            onClick={toggleOpenNote}
          />
        </div>
        <NumberInputField
          name="deposits"
          label="Deposits"
          prefix="$"
          form={form}
          disabled
          thousandSeparator=","
          allowNegative
          fixedDecimalScale
        />
      </div>
      <div className="grid grid-cols-3 2xl:grid-cols-5  gap-x-5 2xl:gap-x-4 pt-5">
        <div className="col-span-2 2xl:col-span-4 z-[0]">
          <InquiryListTable
            data={data?.data?.inquiryPaymentDetails || []}
            isLoading={false}
          />
        </div>
        <div className="space-y-2">
          <div className="border rounded-md p-3">
            <Labels label="Aged Balances" className="pb-3" />
            {AgedBalances.map((item) => (
              <div className="grid grid-cols-2 items-center gap-2 mb-2">
                <div className="w-fit">{item.label}</div>
                <NumberInputField
                  name={item.name}
                  prefix="$"
                  disabled
                  form={form}
                  className="h-8"
                  thousandSeparator=","
                  fixedDecimalScale
                  allowNegative
                />
              </div>
            ))}
          </div>
          <RadioField
            name="displayEnum"
            label="Display"
            form={form}
            options={displayList}
            pClassName="border rounded-md p-3"
            onChange={(value) => {
              setFilterOption((prev) => ({ ...prev, displayEnum: value }));
              fetchInquiryDetails();
            }}
          />
          <RadioField
            name="balanceTransactionEnum"
            label="Balances & Transactions of"
            form={form}
            options={InquiryBalanceTransactionList}
            optionsPerRow={1}
            className="space-y-3"
            pClassName="border rounded-md p-3"
            onChange={handleBalanceTransactionChange}
          />
        </div>
      </div>
      <CustomDialog
        onOpenChange={toggleOpenNote}
        description=""
        open={openNote}
        className="min-w-[60%] 2xl:min-w-[40%]"
        contentClassName="h-[400px] 2xl:h-[500px]"
        title="Customer Notes"
      >
        <div className="grid grid-cols-1 px-6 pb-3">
          <Notes />
        </div>
      </CustomDialog>

      <AppSpinner isLoading={isLoading} overlay />
    </div>
  );
};

export default InquiryDetails;
