import DataTable from '@/components/common/data-tables';
import { convertToFloat, getQueryParam } from '@/lib/utils';
import { useGetPaymentsInfoQuery } from '@/redux/features/accounting/Inquiry.api';
import { PaymentInfoTyps } from '@/types/accounting.types';
import { ColumnDef } from '@tanstack/react-table';

const PaymentInfo = ({ date }: { date: string }) => {
  const customerId = getQueryParam('customerId') as string;

  const { data, isFetching: isLoading } = useGetPaymentsInfoQuery(
    { customerId, date },
    { skip: !date }
  );
  const columns: ColumnDef<PaymentInfoTyps>[] = [
    {
      header: 'Invoice',
      accessorKey: 'invoiceNo',
      size: 80,
      enableSorting: true,
      invertSorting: true,
    },
    {
      header: 'Payment',
      accessorKey: 'payment',
      size: 150,
      cell: ({ row }) =>
        convertToFloat({ value: row?.original?.payment, prefix: '$' }),
      enableSorting: true,
      invertSorting: true,
    },
    {
      header: 'Discount',
      accessorKey: 'discount',
      size: 120,
      cell: ({ row }) =>
        convertToFloat({ value: row?.original?.discount, prefix: '$' }),
      enableSorting: true,
      invertSorting: true,
    },
  ];

  return (
    <DataTable
      data={data?.data || []}
      columns={columns}
      totalItems={data?.data?.length}
      enablePagination={false}
      tableClassName="max-h-[420px] 2xl:max-h-[480px] overflow-auto"
      isLoading={isLoading}
      loaderRows={9}
      manualSorting={false}
    />
  );
};

export default PaymentInfo;
