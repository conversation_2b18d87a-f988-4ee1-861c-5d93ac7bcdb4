import DataTable from '@/components/common/data-tables';
import { convertToFloat, formatDate, getQueryParam } from '@/lib/utils';
import { useGetInvoiceInfoQuery } from '@/redux/features/accounting/Inquiry.api';
import { InvoiceInfoTypes } from '@/types/accounting.types';
import { ColumnDef } from '@tanstack/react-table';

const InvoiceInfo = ({ invoiceNo }: { invoiceNo: string }) => {
  const customerId = getQueryParam('customerId') as string;

  const { data, isFetching: isLoading } = useGetInvoiceInfoQuery(
    { customerId, orderId: invoiceNo },
    { skip: !invoiceNo }
  );
  const columns: ColumnDef<InvoiceInfoTypes>[] = [
    {
      header: 'Date',
      accessorKey: 'date',
      size: 80,
      cell: ({ row }) => formatDate(row?.original?.date),
      enableSorting: true,
      invertSorting: true,
    },
    {
      header: 'Amount',
      accessorKey: 'amount',
      size: 150,
      cell: ({ row }) =>
        convertToFloat({ value: row?.original?.amount, prefix: '$' }),
      enableSorting: true,
      invertSorting: true,
    },
    {
      header: 'Type',
      accessorKey: 'cardType',
      size: 120,
      enableSorting: true,
      invertSorting: true,
    },
    {
      header: 'Reference',
      accessorKey: 'reference',
      size: 120,
      enableSorting: true,
      invertSorting: true,
    },
    {
      header: 'Total Payment',
      accessorKey: 'totalPayment',
      size: 150,
      cell: ({ row }) =>
        convertToFloat({ value: row?.original?.totalPayment, prefix: '$' }),
      enableSorting: true,
      invertSorting: true,
    },
  ];

  return (
    <DataTable
      data={data?.data || []}
      columns={columns}
      totalItems={data?.data?.length}
      enablePagination={false}
      tableClassName="max-h-[420px] 2xl:max-h-[480px] overflow-auto"
      isLoading={isLoading}
      loaderRows={9}
      manualSorting={false}
    />
  );
};

export default InvoiceInfo;
