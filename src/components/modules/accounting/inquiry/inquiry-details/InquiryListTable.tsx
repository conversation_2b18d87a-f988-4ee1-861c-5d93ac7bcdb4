import DataTable from '@/components/common/data-tables';
import ActionColumnMenu from '@/components/common/data-tables/ActionColumn';
import CustomDialog from '@/components/common/dialog';
import { convertToFloat, DATE_FORMAT_YYYYMMDD, formatDate } from '@/lib/utils';
import { inquiryPaymentDetailsType } from '@/types/accounting.types';
import { ColumnDef } from '@tanstack/react-table';
import { useMemo, useState } from 'react';
import InvoiceInfo from './InvoiceInfo';
import PaymentInfo from './PaymentInfo';

interface InquiryListTableProps {
  data: any[];
  isLoading: boolean;
}

const InquiryListTable = ({ data, isLoading }: InquiryListTableProps) => {
  const [infoDialog, setInfoDialog] = useState<{
    isOpen: boolean;
    type: boolean;
    value: string;
  }>({
    isOpen: false,
    type: false,
    value: '',
  });

  const handleInfoDialogToggle = (
    value: string = '',
    type: boolean = false
  ) => {
    setInfoDialog((prev) => ({
      isOpen: !prev.isOpen,
      value,
      type,
    }));
  };

  const columns: ColumnDef<inquiryPaymentDetailsType>[] = useMemo(() => {
    return [
      {
        header: 'Paid',
        accessorKey: 'paid',
        size: 80,
      },
      {
        header: 'Date',
        accessorKey: 'date',
        size: 100,
        cell: ({ row }) => formatDate(row?.original?.date),
      },
      {
        header: 'Invoice #',
        accessorKey: 'invoiceNo',
        size: 120,
      },
      {
        header: 'Type',
        accessorKey: 'type',
        size: 120,
      },
      {
        header: 'Amount',
        accessorKey: 'amount',
        size: 150,
        cell: ({ row }) =>
          convertToFloat({ value: row?.original?.amount, prefix: '$' }),
      },
      {
        header: 'Discount',
        accessorKey: 'discount',
        size: 100,
        cell: ({ row }) =>
          convertToFloat({ value: row?.original?.discount, prefix: '$' }),
      },
      {
        header: 'Payments',
        accessorKey: 'payments',
        size: 120,
        cell: ({ row }) =>
          convertToFloat({ value: row?.original?.payments, prefix: '$' }),
      },
      {
        header: 'Balance',
        accessorKey: 'balance',
        size: 120,
        cell: ({ row }) =>
          convertToFloat({ value: row?.original?.balance, prefix: '$' }),
      },
      {
        header: 'Deleted',
        accessorKey: 'isDeleted',
        size: 90,
        cell: ({ row }) => (row?.original?.isDeleted ? 'D' : ''),
      },
      {
        header: 'User ID',
        accessorKey: 'userId',
        size: 100,
      },
      {
        id: 'action',
        size: 80,
        header: 'Actions',
        cell: ({ row }) => {
          const TYPE = row?.original?.type?.toUpperCase() === 'PAYMENT';
          const value = TYPE
            ? formatDate(row?.original?.date, DATE_FORMAT_YYYYMMDD)
            : row?.original?.invoiceNo;
          return (
            <ActionColumnMenu
              onEdit={() => handleInfoDialogToggle(value, TYPE)}
              label="View Info"
              triggerClassName="hidden"
            />
          );
        },
      },
    ];
  }, []);

  return (
    <>
      <DataTable
        data={data || []}
        columns={columns}
        enablePagination={false}
        tableClassName="max-h-[580px] overflow-auto"
        isLoading={isLoading}
        highlightKey="isPaymentPending"
        highlightClassName="bg-red-200 hover:bg-red-200"
        selectedHighlightClassName="data-[state=selected]:bg-red-100"
      />
      <CustomDialog
        onOpenChange={() => handleInfoDialogToggle()}
        description=""
        open={infoDialog.isOpen}
        title={`${infoDialog.type ? 'Payment' : 'Invoice'} Info`}
        className="min-w-[60%] 2xl:min-w-[40%]"
        contentClassName="h-[450px] 2xl:h-[500px]"
      >
        <div className="px-6 pb-3 grid grid-cols-1">
          {infoDialog.type ? (
            <PaymentInfo date={infoDialog.value} />
          ) : (
            <InvoiceInfo invoiceNo={infoDialog.value} />
          )}
        </div>
      </CustomDialog>
    </>
  );
};

export default InquiryListTable;
