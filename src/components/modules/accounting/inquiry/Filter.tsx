import AppButton from '@/components/common/app-button';
import Input<PERSON>ield from '@/components/forms/input-field';
import SelectDropDown from '@/components/forms/select-dropdown';
import { Separator } from '@/components/ui/separator';
import {
  OperatorTypeOptions,
  statusListWithAll,
} from '@/constants/common-constants';
import { generateLabelValuePairs, toCapitalize } from '@/lib/utils';
import { useGetInquiryColumsQuery } from '@/redux/features/accounting/Inquiry.api';
import {
  clearAllFilters,
  setFilter,
  updateFormValues,
} from '@/redux/features/accounting/InquirySlice';
import { RootState } from '@/redux/store';
import { memo, useEffect } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
interface FilterFormValues {
  isactive: string;
  searchBy: string;
  operator: string;
  searchValue: string;
}

interface FilterProps {
  setIsFilterOpen: React.Dispatch<React.SetStateAction<boolean>>;
}
const Filter: React.FC<FilterProps> = ({ setIsFilterOpen }) => {
  const dispatch = useDispatch();
  const formValuesFromRedux = useSelector(
    (state: RootState) => state.accountingInquiry.formValues
  );

  // get columns
  const { data, isLoading } = useGetInquiryColumsQuery();
  const searchByList = generateLabelValuePairs({
    data: data?.data,
    labelKey: 'header',
    valueKey: 'accessorKey',
  })?.filter((item) => item?.value !== 'custtype');

  // Initialize form with values from Redux
  const form = useForm<FilterFormValues>({
    defaultValues: formValuesFromRedux,
  });

  // Watch form changes and update Redux state
  useEffect(() => {
    const subscription = form.watch((values) => {
      dispatch(updateFormValues(values));
    });

    return () => subscription.unsubscribe();
  }, [form, dispatch]);

  //Apply Filter
  const onSubmit: SubmitHandler<FilterFormValues> = (data) => {
    const key: string = data.searchBy;
    const filterLabel = searchByList?.find(({ value }) => value == key)?.label;

    const newFilterData: any = [
      {
        label: 'Status',
        value: data?.isactive === 'all' ? '' : data?.isactive,
        name: 'isactive',
        tagValue: data?.isactive === 'true' ? 'Active' : 'Inactive',
        operator: 'Equals',
      },
      {
        label: toCapitalize(filterLabel),
        value: key === 'tel1' ? `+1${data.searchValue}` : data.searchValue,
        name: key,
        tagValue: data.searchValue,
        operator: data.operator,
      },
    ];
    dispatch(setFilter(newFilterData));
    setIsFilterOpen(false);
  };

  const handleClear = () => {
    dispatch(clearAllFilters()); // Clear all filters in Redux
    setIsFilterOpen(false); // Close the filter form
  };

  const searchBy = form.watch('searchBy');

  return (
    <div className="px-3">
      <div className="text-normal py-2 font-semibold">Filters</div>
      <Separator
        orientation="horizontal"
        className="h-[1px] bg-border-Default mb-2"
      />
      <div className="grid grid-cols-2 gap-3">
        <div className="col-span-2">
          <SelectDropDown
            form={form}
            name="isactive"
            label="Status"
            placeholder="Select Status"
            optionsList={statusListWithAll}
            allowClear={false}
          />
        </div>

        <SelectDropDown
          form={form}
          name="searchBy"
          label="Search By"
          placeholder="Search By"
          optionsList={searchByList}
          allowClear={false}
          isLoading={isLoading}
          onChange={(value) => {
            !value && form.setValue('searchValue', '');
          }}
        />
        <SelectDropDown
          form={form}
          name="operator"
          label="Operator"
          allowClear={false}
          placeholder="Operator"
          optionsList={OperatorTypeOptions}
        />
        <InputField
          form={form}
          name="searchValue"
          disabled={!searchBy}
          label="Value"
          placeholder="Search"
          pClassName="col-span-2"
        />
      </div>
      <div className="flex gap-3 mt-7 sticky bottom-2 bg-white">
        <AppButton
          className="w-full"
          onClick={form.handleSubmit(onSubmit)}
          label="Apply"
        />
        <AppButton
          className="w-full"
          label="Clear"
          variant="neutral"
          onClick={handleClear}
        />
      </div>
    </div>
  );
};

export default memo(Filter);
