import AppDataTable from '@/components/common/app-data-table';
import ColumnReOrdering from '@/components/common/column-reording';
import { CUSTOMER_API_ROUTES } from '@/constants/api-constants';

import AppSpinner from '@/components/common/app-spinner';
import ActionColumnMenu from '@/components/common/data-tables/ActionColumn';
import { componentMap } from '@/constants/common-constants';
import {
  useGetInquiryColumsQuery,
  useUpdateInquiryColumsMutation,
} from '@/redux/features/accounting/Inquiry.api';
import { clearFilter } from '@/redux/features/accounting/InquirySlice';
import { RootState } from '@/redux/store';
import { useCallback, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import Filter from './Filter';

const Inquiry = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [openColumnOrdering, setOpenColumnOrdering] = useState<boolean>(false);
  const [isFilterOpen, setIsFilterOpen] = useState<boolean>(false);
  const filter = useSelector(
    (state: RootState) => state.accountingInquiry.filters
  );

  // get reorganize columns
  const { data, isFetching: isLoading } = useGetInquiryColumsQuery();

  // update reorganize columns
  const [updateColums, { isLoading: updateColumIsLoading }] =
    useUpdateInquiryColumsMutation();

  const tableColumns = data?.data;

  // handle change reorganize columns
  const handleOrderingColumn = useCallback(
    async (formData: any) => {
      const updatedColumns = tableColumns?.map((item: any) => ({
        ...item,
        enabled: formData[item.accessorKey],
      }));

      await updateColums(updatedColumns).unwrap();
      setOpenColumnOrdering(false);
    },
    [tableColumns, updateColums]
  );

  // columns
  const memoizedColumns = useMemo(() => {
    if (!tableColumns?.length) return [];

    const formattedColumns = tableColumns
      .filter((column: any) => column.enabled) // Ensure only enabled columns are included
      .map((column: any) => ({
        accessorKey: column.accessorKey,
        header: column.header,
        enableSorting: column.enableSorting || false,
        size: column.size || 200,
        cell: ({ row }: any) => {
          const Component = componentMap[column.component]; // no need to call as function
          const value = row?.original?.[column.accessorKey];
          return Component ? <Component value={value} /> : value;
        },
      }));

    return [
      {
        accessorKey: 'ordering',
        header: () => (
          <ColumnReOrdering
            isOpen={openColumnOrdering}
            handleOrderingColumn={handleOrderingColumn}
            tableColumns={tableColumns}
            setOpenColumnOrdering={setOpenColumnOrdering}
          />
        ),
        size: 50,
        maxSize: 100,
      },
      ...formattedColumns,
      {
        id: 'action',
        size: 110,
        header: 'Actions',
        cell: ({ row }: any) => {
          const customerId = row.original.customer_id;
          return (
            <ActionColumnMenu
              onEdit={() => navigate(`details?customerId=${customerId}`)}
              triggerClassName="hidden"
            />
          );
        },
      },
    ];
  }, [tableColumns, openColumnOrdering, handleOrderingColumn, navigate]);

  // handle Clear function
  const handleClearFilter = useCallback(
    (key: string) => {
      dispatch(clearFilter(key));
      setIsFilterOpen(false);
    },
    [dispatch]
  );

  return (
    <div className="flex flex-col p-6 gap-6">
      <AppDataTable
        url={CUSTOMER_API_ROUTES.ALL}
        columns={memoizedColumns}
        heading="Inquiry"
        enableSearch={true}
        enablePagination={true}
        tableClassName="max-h-[580px] overflow-auto"
        searchKey="full_name"
        enableFilter
        filter={filter}
        handleClearFilter={handleClearFilter}
        filterClassName="w-[550px]"
        filterContent={<Filter setIsFilterOpen={setIsFilterOpen} />}
        setIsFilterOpen={setIsFilterOpen}
        isFilterOpen={isFilterOpen}
      />

      <AppSpinner isLoading={isLoading || updateColumIsLoading} overlay />
    </div>
  );
};

export default Inquiry;
