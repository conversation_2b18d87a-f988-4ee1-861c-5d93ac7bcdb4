import { render, screen, fireEvent } from '@testing-library/react';
import { describe, expect, vi, it } from 'vitest';
import Accountings from '.';
import { MemoryRouter } from 'react-router-dom';
import { beforeAll } from 'vitest';

describe('Accountings Component', () => {
  beforeAll(() => {
    // Mock data for accountingData
    vi.mock('@/mock-data/accounting-mock-data', () => ({
      accountingData: [
        { accountingName: 'Adjustments', to: '/adjustments' },
        { accountingName: 'Inquiry', to: '/inquiry' },
        { accountingName: 'Payments', to: '/payments' },
        {
          accountingName: 'Process Credit Cards',
          to: '/process-credit-cards',
        },
      ],
    }));

    // Mock the ROUTES constants for the test
    vi.mock('@/constants/routes-constants', () => ({
      ROUTES: {
        ADJUSTMENTS: '/adjustments',
        INQUIRY: '/inquiry',
        PAYMENTS: '/payments',
        PROCESS_CREDIT_CARDS: '/process-credit-cards',
      },
    }));
  });

  it('renders the accountings table and links correctly', () => {
    render(
      <MemoryRouter>
        <Accountings />
      </MemoryRouter>
    );
    // Check if the accounting names are rendered
    expect(screen.getByText('Adjustments')).toBeInTheDocument();
    expect(screen.getByText('Inquiry')).toBeInTheDocument();
    expect(screen.getByText('Payments')).toBeInTheDocument();
    expect(screen.getByText('Process Credit Cards')).toBeInTheDocument();
  });

  it('should navigate to the correct link when clicking on an accounting name', () => {
    render(
      <MemoryRouter initialEntries={['/']}>
        <Accountings />
      </MemoryRouter>
    );

    // Find and click on the accounting name link
    const accountingLink = screen.getByText('Adjustments');
    fireEvent.click(accountingLink);

    // Check if the URL is updated correctly
    expect(window.location.pathname).toBe('/');
  });
});
