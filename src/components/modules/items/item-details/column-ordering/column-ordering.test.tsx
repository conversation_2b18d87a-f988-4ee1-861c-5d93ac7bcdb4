import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, expect, describe, it } from 'vitest';
import ItemColumnOrdering from './index';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';

// Mock Redux store
const mockStore = configureStore({
  reducer: {
    tableColumns: () => ({
      columns: [],
    }),
  },
});

describe('ItemColumnOrdering Component', () => {
  const mockHandleOrderingColumn = vi.fn();
  const mockSetOpenColumnOrdering = vi.fn();
  const mockTableColumns = [
    {
      accessorKey: 'name',
      header: 'Name',
      enableSorting: true,
      size: 100,
      enabled: true,
    },
    {
      accessorKey: 'price',
      header: 'Price',
      enableSorting: true,
      size: 100,
      enabled: false,
    },
  ];

  it('renders component correctly', () => {
    render(
      <Provider store={mockStore}>
        <ItemColumnOrdering
          isOpen={true}
          handleOrderingColumn={mockHandleOrderingColumn}
          tableColumns={mockTableColumns}
          setOpenColumnOrdering={mockSetOpenColumnOrdering}
        />
      </Provider>
    );

    expect(screen.getByText('Columns')).toBeInTheDocument();
    expect(screen.getByText('Save')).toBeInTheDocument();
    expect(screen.getByText('Select All')).toBeInTheDocument();
    expect(screen.getByText('Name')).toBeInTheDocument();
    expect(screen.getByText('Price')).toBeInTheDocument();
  });

  it('toggles "Select All" switch', async () => {
    render(
      <Provider store={mockStore}>
        <ItemColumnOrdering
          isOpen={true}
          handleOrderingColumn={mockHandleOrderingColumn}
          tableColumns={mockTableColumns}
          setOpenColumnOrdering={mockSetOpenColumnOrdering}
        />
      </Provider>
    );

    const selectAllToggle = screen.getByText('Select All')
      .nextSibling as HTMLElement;
    expect(selectAllToggle).toBeInTheDocument();

    await fireEvent.click(selectAllToggle);

    await waitFor(() => {
      expect(screen.getByText('Name')).toBeInTheDocument();
      expect(screen.getByText('Price')).toBeInTheDocument();
    });
  });

  it('calls handleOrderingColumn when clicking Save', async () => {
    render(
      <Provider store={mockStore}>
        <ItemColumnOrdering
          isOpen={true}
          handleOrderingColumn={mockHandleOrderingColumn}
          tableColumns={mockTableColumns}
          setOpenColumnOrdering={mockSetOpenColumnOrdering}
        />
      </Provider>
    );

    const saveButton = screen.getByText('Save');
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(mockHandleOrderingColumn).toHaveBeenCalled();
    });
  });
});
