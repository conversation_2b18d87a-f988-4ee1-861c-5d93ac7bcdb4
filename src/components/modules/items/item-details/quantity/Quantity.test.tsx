import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import Quantity from './Quantity';
import { useGetQuantityListQuery } from '@/redux/features/items/item.api';
import itemReducer from '@/redux/features/items/itemSlice';
import { commonApi } from '@/redux/features/common-api/common.api';

// Mock the API hooks
vi.mock('@/redux/features/items/item.api', async () => {
  const actual = await import('@/redux/features/items/item.api');
  return {
    ...actual,
    useGetQuantityListQuery: vi.fn(),
    useAddQuantityMutation: vi.fn(() => [vi.fn(), { isLoading: false }]),
  };
});

vi.mock('@/redux/features/company/company.api', async () => {
  const actual = await import('@/redux/features/company/company.api');
  return {
    ...actual,
    useGetCompanyQuery: vi.fn(() => [vi.fn()]),
  };
});

// Mock the store
const mockStore = configureStore({
  reducer: {
    item: itemReducer,
    [commonApi.reducerPath]: commonApi.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(commonApi.middleware),
});

describe('Quantity Component', () => {
  it('renders the component and fetches data', async () => {
    (useGetQuantityListQuery as any).mockReturnValue({
      data: {
        data: [
          {
            id: 1,
            dateChanged: '2023-10-01',
            purchaseDate: '2023-10-01',
            sequenceNumber: 1,
            quantity: 10,
            totalQuantity: 100,
            description: 'Test Description',
            unitCost: 5.5,
            totalCost: 55,
            vendorName: 'Test Vendor',
            locationName: 'Test Location',
            changedBy: 'Test User',
          },
        ],
      },
    });

    render(
      <Provider store={mockStore}>
        <Quantity />
      </Provider>
    );
    expect(screen.getByText('Adjust Quantity')).toBeInTheDocument();
    expect(screen.getByText('Remove Quantity')).toBeInTheDocument();
    expect(screen.getByText('Add Quantity')).toBeInTheDocument();
  });
});
