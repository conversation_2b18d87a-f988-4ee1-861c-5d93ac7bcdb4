import { useEffect, useMemo, useState } from 'react';

// Components
import AppButton from '@/components/common/app-button';
import DataTable from '@/components/common/data-tables';
import ActionColumnMenu from '@/components/common/data-tables/ActionColumn';
import CustomDialog from '@/components/common/dialog';
import AddQuantityForm from './AddQuantity';
import AdjustQuantity from './AdjustQuantity';
import EditQuantity from './EditQuantity';
import RemoveQuantity from './RemoveQuantity';

// Constants and Utils
import { cn, convertToFloat, formatDate, getQueryParam } from '@/lib/utils';

// API Hooks
import { useGetQuantityListQuery } from '@/redux/features/items/item.api';

// Types
import {
  QuantityDetailsTypes,
  QuantityListDataTypes,
} from '@/types/item.types';
import { ColumnDef, RowSelectionState } from '@tanstack/react-table';

// Assets
import EditIcon from '@/assets/icons/EditIcon';
import { Eye, MinusIcon, PlusIcon, RefreshCw } from 'lucide-react';

// Interface for handling general dialog state with action
interface GeneralDialogStateTypes {
  state: boolean;
  action: string;
}

const initialDialogState = {
  state: false,
  action: '',
};

const initialQuantityDetails = {
  id: null,
  unitCost: null,
};

const Quantity = () => {
  // State and Hooks
  const id = getQueryParam('id') as string;
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const [quantityDetails, setQuantityDetails] = useState<QuantityDetailsTypes>(
    initialQuantityDetails
  );
  const [openDialog, setOpenDialog] =
    useState<GeneralDialogStateTypes>(initialDialogState);
  const [selectGroup, setSelectGroup] = useState<boolean>(false);
  const [selectedGroupId, setSelectedGroupId] = useState<string>('');

  // API Hooks
  const { data, isLoading, isFetching } = useGetQuantityListQuery(
    {
      itemId: id,
      groupBy: 'PURCHASE',
      groupSelect: selectGroup.toString(),
      ...(selectGroup && { itemQuantityId: selectedGroupId }),
    },
    {
      skip: selectGroup && !selectedGroupId, // Skip if no group selected
    }
  );

  // Function to render the appropriate content in the custom dialog based on the action type
  const renderDialogContent = (action: string, toggleDialog: () => void) => {
    switch (action) {
      case 'edit-details':
        return (
          <EditQuantity
            quantityDetails={quantityDetails}
            toggle={toggleDialog}
          />
        );
      case 'add-quantity':
        return <AddQuantityForm toggle={toggleDialog} />;
      case 'remove-quantity':
        return (
          <RemoveQuantity
            toggle={toggleDialog}
            quantityId={Object.keys(rowSelection)[0]}
          />
        );
      case 'adjust-quantity':
        return (
          <AdjustQuantity
            toggle={toggleDialog}
            quantity={data?.data.globalQuantity ?? null}
          />
        );
      default:
        return null;
    }
  };

  const renderDialogTitleClassName = (action: string) => {
    let title;
    let className;
    switch (action) {
      case 'edit-details':
        title = 'Edit Quantity';
        className = 'md:max-w-[30%] 2xl:max-w-[25%]';
        break;
      case 'add-quantity':
        title = 'Add Quantity';
        className = 'md:max-w-[60%] 2xl:max-w-[40%]';
        break;
      case 'remove-quantity':
        title = 'Remove Quantity';
        className = 'md:max-w-[35%] 2xl:max-w-[30%]';
        break;
      case 'adjust-quantity':
        title = 'Adjust Quantity';
        className = 'md:max-w-[35%] 2xl:max-w-[30%]';
        break;
      default:
        title = 'Quantity';
        className = '';
    }
    return { title, className };
  };

  const { title, className } = renderDialogTitleClassName(openDialog.action);

  const columns: ColumnDef<QuantityListDataTypes>[] = useMemo(
    () => [
      {
        accessorKey: 'dateChanged',
        header: 'Date Changed',
        size: 150,
        cell: ({ row }) => formatDate(row.original.dateChanged),
      },
      {
        accessorKey: 'purchaseDate',
        header: 'Purchase Date',
        size: 150,
        cell: ({ row }) => formatDate(row.original.purchaseDate),
      },
      {
        accessorKey: 'sequenceNumber',
        header: 'Seq #',
        size: 80,
      },
      {
        accessorKey: 'quantity',
        header: 'Quantity',
        size: 80,
      },
      {
        accessorKey: 'totalQuantity',
        header: 'Qty Total',
        size: 110,
      },

      {
        accessorKey: 'description',
        header: 'Description',
        size: 200,
      },
      {
        accessorKey: 'unitCost',
        header: 'Unit Cost',
        size: 110,
        cell: ({ row }: any) =>
          convertToFloat({ value: row.original.unitCost, prefix: '$' }),
      },
      {
        accessorKey: 'totalCost',
        header: 'Total Cost',
        size: 120,
        cell: ({ row }: any) =>
          convertToFloat({ value: row.original.totalCost, prefix: '$' }),
      },
      {
        accessorKey: 'vendorName',
        header: 'Vendor Name',
        size: 140,
      },
      {
        accessorKey: 'locationName',
        header: 'Location Name',
        size: 180,
      },
      {
        accessorKey: 'changedBy',
        header: 'Changed By',
        size: 180,
      },
      {
        size: 180,
        id: 'action',
        header: 'Actions',
        cell: ({ row }) => (
          <ActionColumnMenu
            customEdit={
              <button
                onClick={() =>
                  handleEdit(row.original.id, row.original.unitCost)
                }
                className="flex items-center justify-center gap-2 border border-text-tertiary rounded-md px-2 py-1"
              >
                <EditIcon /> Edit details
              </button>
            }
          />
        ),
      },
    ],
    []
  );

  // Function to toggle the custom dialog state and clear any existing query parameters
  const toggleDialog = () => {
    setOpenDialog((prev) => {
      const newState = { state: false, action: '' };
      if (prev.state) {
        setRowSelection({});
        if (quantityDetails.id) {
          setQuantityDetails(initialQuantityDetails);
        }
      }
      return newState;
    });
  };

  // Handle edit action
  const handleEdit = (id: number, unitCost: number) => {
    setQuantityDetails({
      id,
      unitCost,
    });
    setOpenDialog(() => ({
      state: true,
      action: 'edit-details',
    }));
  };

  useEffect(() => {
    if (data?.data && selectGroup) {
      setRowSelection({});
    }
  }, [data?.data, selectGroup]);

  const handleViewGroup = () => {
    if (!selectGroup) {
      const selectedId = Object.keys(rowSelection)[0];
      if (selectedId) {
        setSelectedGroupId(selectedId);
        setSelectGroup(true);
      }
    } else {
      setSelectGroup(false);
      setSelectedGroupId('');
    }
  };

  // Custom toolbar with buttons for adding/removing quantity and viewing selected items
  const CustomToolbar = (
    <div className="flex flex-col sm:flex-row sm:justify-between w-full">
      <div className="flex flex-wrap flex-1 gap-2">
        <AppButton
          className="bg-brand-teal-Default hover:bg-brand-teal-secondary"
          icon={PlusIcon}
          iconClassName="w-5 h-5"
          label="Add Quantity"
          onClick={() => setOpenDialog({ state: true, action: 'add-quantity' })}
        />
        <AppButton
          icon={MinusIcon}
          iconClassName="w-5 h-5"
          disabled={
            isLoading ||
            isFetching ||
            Object.keys(rowSelection).length === 0 ||
            !data?.data?.itemQuantities.some(
              (row) => rowSelection[row.id] && row.isAdded === true
            )
          }
          label="Remove Quantity"
          variant="neutral"
          onClick={() =>
            setOpenDialog({ state: true, action: 'remove-quantity' })
          }
        />
        <AppButton
          icon={RefreshCw}
          iconClassName="w-5 h-5"
          label="Adjust Quantity"
          variant="neutral"
          onClick={() =>
            setOpenDialog({ state: true, action: 'adjust-quantity' })
          }
        />
        <AppButton
          icon={Eye}
          iconClassName="w-5 h-5"
          disabled={!selectGroup && Object.keys(rowSelection).length === 0}
          onClick={handleViewGroup}
          label={
            selectGroup && !isFetching && data
              ? 'View All Groups'
              : 'View Selected Group'
          }
          variant="neutral"
        />
      </div>
      <div className="mt-2 font-semibold w-fit">
        Total Quantity: {data?.data.totalQuantity ?? 0}
      </div>
    </div>
  );

  return (
    <>
      <DataTable
        data={data?.data.itemQuantities ?? []}
        columns={columns}
        isLoading={isLoading || isFetching}
        enablePagination={false}
        tableClassName="max-h-[580px] overflow-auto"
        enableRowSelection
        rowSelection={rowSelection}
        onRowSelectionChange={setRowSelection}
        bindingKey="id"
        customToolBar={CustomToolbar}
      />

      {/* Custom Dialog for Inventory Form */}
      <CustomDialog
        onOpenChange={toggleDialog}
        ariaDescribedby={undefined}
        description=""
        open={openDialog.state}
        className={cn('max-h-[96%] overflow-auto', className)}
        title={title}
        contentClassName="pl-6 pr-6"
      >
        <>{renderDialogContent(openDialog.action, toggleDialog)}</>
      </CustomDialog>
    </>
  );
};

export default Quantity;
