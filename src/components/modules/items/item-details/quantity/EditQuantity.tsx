import { memo, useMemo } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';

import NumberIn<PERSON><PERSON>ield from '@/components/forms/number-input-field';

import { useUpdateQuantityMutation } from '@/redux/features/items/item.api';

import FormActionButtons from '@/components/common/FormActionButtons';
import { TEXT_VALIDATION_RULE } from '@/constants/validation-constants';
import { QuantityDetailsTypes } from '@/types/item.types';
import { getQueryParam } from '@/lib/utils';

type EditQuantityPropsType = {
  toggle: () => void;
  quantityDetails: QuantityDetailsTypes;
};

const EditQuantity = ({ toggle, quantityDetails }: EditQuantityPropsType) => {
  const id = getQueryParam('id') as string;
  const [updateQuantity, { isLoading }] = useUpdateQuantityMutation();

  // Memoized default form values
  const defaultValues = useMemo(() => {
    return {
      id: quantityDetails.id,
      unitCost: quantityDetails.unitCost,
    };
  }, [quantityDetails.id, quantityDetails.unitCost]);

  const form = useForm<QuantityDetailsTypes>({
    defaultValues,
    mode: 'onChange',
  });

  // Submit handler
  const onSubmit: SubmitHandler<QuantityDetailsTypes> = async (formData) => {
    try {
      if (quantityDetails.id) {
        await updateQuantity({
          quantityData: {
            ...formData,
            unitCost: Number(formData.unitCost),
            itemId: Number(id),
          },
        }).unwrap();
      }
      toggle();
    } catch (error) {}
  };

  return (
    <div>
      <NumberInputField
        form={form}
        name="unitCost"
        label="Unit Cost"
        placeholder="$______.__"
        prefix="$"
        maxLength={10}
        decimalScale={2}
        fixedDecimalScale
        validation={TEXT_VALIDATION_RULE}
      />
      <FormActionButtons
        onSubmit={form.handleSubmit(onSubmit)}
        onCancel={toggle}
        isLoading={isLoading}
      />
    </div>
  );
};

export default memo(EditQuantity);
