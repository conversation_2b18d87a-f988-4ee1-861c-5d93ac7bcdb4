import { memo, useEffect, useMemo } from 'react';
import { SubmitHand<PERSON>, useForm } from 'react-hook-form';
import useOptionList from '@/hooks/useOptionList';

import FormActionButtons from '@/components/common/FormActionButtons';
import DatePicker from '@/components/forms/date-picker';
import In<PERSON><PERSON>ield from '@/components/forms/input-field';
import NumberIn<PERSON><PERSON>ield from '@/components/forms/number-input-field';
import SelectWidget from '@/components/forms/select';

import { useGetCompanyQuery } from '@/redux/features/company/company.api';
import { useAddQuantityMutation } from '@/redux/features/items/item.api';
import { useGetListQuery } from '@/redux/features/list/category/list.api';

import {
  STORE_OPTIONS_API_ROUTES,
  VENDORS_API_ROUTES,
} from '@/constants/api-constants';
import { STORAGE_KEYS } from '@/constants/storageKeys';
import { USER_OPTIONS_API } from '@/constants/user-options-constants';
import {
  REQUIRED_TEXT,
  TEXT_VALIDATION_RULE,
} from '@/constants/validation-constants';
import { formatDate, getQueryParam, getStorageValue } from '@/lib/utils';
import { AddQuantityFormDataTypes } from '@/types/item.types';

type AddQuantityFormPropsType = {
  toggle: () => void;
};

const AddQuantityForm = ({ toggle }: AddQuantityFormPropsType) => {
  const id = getQueryParam('id') as string;
  const userId = getStorageValue(STORAGE_KEYS.USER_ID) || '0';

  const [addQuantity, { isLoading }] = useAddQuantityMutation();
  const { data } = useGetCompanyQuery();
  const { data: userData, isLoading: userLoading } = useGetListQuery({
    url: USER_OPTIONS_API?.GET(userId),
  });

  const { options: storeLocationList, optionLoading } = useOptionList({
    url: STORE_OPTIONS_API_ROUTES.ALL,
    labelKey: 'locationName',
    valueKey: 'id',
    sortBy: 'locationName',
    extraKey: 'isDefault',
  });

  const { optionLoading: vendorLoading, options: vendorList } = useOptionList({
    url: VENDORS_API_ROUTES.ALL,
    labelKey: 'vendorName',
    valueKey: 'id',
    sortBy: 'vendorName',
  });

  const defaultStoreLocation = useMemo(() => {
    return storeLocationList?.find((opt: any) => opt.extraKey);
  }, [storeLocationList]);

  const defaultValues = useMemo(() => {
    return {
      purchaseDate: new Date(),
      storeLocationId: data?.data?.useMultiloc
        ? userData?.data?.defaultLocationId
        : defaultStoreLocation?.value || 1,
      description: '',
      quantity: '',
      unitCost: '',
    };
  }, [
    data?.data.useMultiloc,
    defaultStoreLocation?.value,
    userData?.data?.defaultLocationId,
  ]);

  const form = useForm<AddQuantityFormDataTypes>({
    defaultValues: defaultValues,
    mode: 'onChange',
  });
  const { handleSubmit, reset } = form;

  useEffect(() => {
    if (defaultStoreLocation?.value && userData?.data) {
      reset(defaultValues);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [defaultStoreLocation?.value, reset, userData?.data]);

  // Submit handler
  const onSubmit: SubmitHandler<AddQuantityFormDataTypes> = async (
    formData
  ) => {
    const payload = {
      ...formData,
      itemId: Number(id),
      id: null,
      vendorId: formData?.vendorId || null,
      quantity: Number(formData.quantity),
      unitCost: Number(formData.unitCost),
      purchaseDate: formatDate(formData.purchaseDate, 'YYYY-MM-DD'),
      storeLocationId: formData.storeLocationId,
    };

    try {
      await addQuantity({
        quantityData: payload,
      }).unwrap();
      toggle();
    } catch (error) {}
  };

  return (
    <div className="flex flex-col gap-2">
      <div className="grid grid-cols-2 gap-4">
        <DatePicker
          name="purchaseDate"
          form={form}
          enableInput
          label="Purchase Date"
          validation={{
            required: REQUIRED_TEXT,
          }}
        />
        <NumberInputField
          form={form}
          name="quantity"
          label="Quantity"
          placeholder="Quantity"
          decimalScale={0}
          maxLength={9}
          validation={TEXT_VALIDATION_RULE}
        />

        <InputField
          form={form}
          name="description"
          label="Description"
          placeholder="Description"
        />

        <NumberInputField
          form={form}
          name="unitCost"
          label="Unit Cost"
          placeholder="$______.__"
          prefix="$"
          maxLength={10}
          decimalScale={2}
          fixedDecimalScale
          validation={TEXT_VALIDATION_RULE}
        />

        <SelectWidget
          form={form}
          isClearable={true}
          name="vendorId"
          optionsList={vendorList}
          label="Vendor"
          placeholder="Select Vendor"
          menuPosition="absolute"
          isLoading={vendorLoading}
          maxMenuHeight={200}
        />

        <SelectWidget
          form={form}
          optionsList={storeLocationList}
          name="storeLocationId"
          label="Location"
          placeholder="Select Location"
          isClearable={false}
          isLoading={optionLoading}
          disabled={!data?.data.useMultiloc}
          maxMenuHeight={220}
          menuPosition="absolute"
        />
      </div>
      <FormActionButtons
        onSubmit={handleSubmit(onSubmit)}
        onCancel={toggle}
        isLoading={isLoading}
        disabledSubmitButton={optionLoading || userLoading}
      />
    </div>
  );
};

export default memo(AddQuantityForm);
