import { memo, useEffect, useMemo } from 'react';
import { SubmitHand<PERSON>, useForm } from 'react-hook-form';
import useOptionList from '@/hooks/useOptionList';

import FormActionButtons from '@/components/common/FormActionButtons';
import Input<PERSON>ield from '@/components/forms/input-field';
import NumberInputField from '@/components/forms/number-input-field';
import SelectWidget from '@/components/forms/select';

import { useRemoveQuantityMutation } from '@/redux/features/items/item.api';
import { useGetListQuery } from '@/redux/features/list/category/list.api';
import { useGetCompanyQuery } from '@/redux/features/company/company.api';

import { STORAGE_KEYS } from '@/constants/storageKeys';
import { USER_OPTIONS_API } from '@/constants/user-options-constants';
import { TEXT_VALIDATION_RULE } from '@/constants/validation-constants';
import { STORE_OPTIONS_API_ROUTES } from '@/constants/api-constants';

import { RemoveQuantityFormDataTypes } from '@/types/item.types';
import { getQueryParam, getStorageValue } from '@/lib/utils';

const RemoveQuantity = ({
  toggle,
  quantityId,
}: {
  toggle: () => void;
  quantityId?: string;
}) => {
  const id = getQueryParam('id') as string;
  const userId = getStorageValue(STORAGE_KEYS.USER_ID) || '0';

  const [removeQuantity, { isLoading }] = useRemoveQuantityMutation();
  const { data } = useGetCompanyQuery();
  const { data: userData, isLoading: userLoading } = useGetListQuery({
    url: USER_OPTIONS_API?.GET(userId),
  });

  const { options: storeLocationList, optionLoading } = useOptionList({
    url: STORE_OPTIONS_API_ROUTES.ALL,
    labelKey: 'locationName',
    valueKey: 'id',
    sortBy: 'locationName',
    extraKey: 'isDefault',
  });

  const defaultStoreLocation = useMemo(() => {
    return storeLocationList?.find((opt: any) => opt.extraKey);
  }, [storeLocationList]);

  const defaultValues = useMemo(() => {
    return {
      storeLocationId: data?.data?.useMultiloc
        ? userData?.data?.defaultLocationId
        : defaultStoreLocation?.value || 1,
      quantity: '',
    };
  }, [
    data?.data.useMultiloc,
    defaultStoreLocation?.value,
    userData?.data?.defaultLocationId,
  ]);

  const form = useForm<RemoveQuantityFormDataTypes>({
    defaultValues,
    mode: 'onChange',
  });
  const { handleSubmit } = form;

  useEffect(() => {
    if (defaultStoreLocation?.value && userData?.data) {
      form.reset(defaultValues);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [defaultStoreLocation?.value, userData?.data, form]);

  // Submit handler
  const onSubmit: SubmitHandler<RemoveQuantityFormDataTypes> = async (
    formData
  ) => {
    try {
      const response = await removeQuantity({
        quantityData: {
          ...formData,
          id: Number(quantityId) ?? 0,
          itemId: Number(id),
          quantity: Number(formData.quantity),
        },
      });

      if (response.data?.success) {
        // Toggle function after successful submission
        toggle?.();
      }
    } catch (error) {}
  };

  return (
    <div className="flex flex-col gap-2">
      <NumberInputField
        form={form}
        name="quantity"
        label="Quantity"
        placeholder="Quantity"
        decimalScale={0}
        maxLength={9}
        validation={TEXT_VALIDATION_RULE}
      />

      <InputField
        form={form}
        name="description"
        label="Description"
        placeholder="Description"
      />

      <SelectWidget
        form={form}
        optionsList={storeLocationList}
        name="storeLocationId"
        label="Location"
        placeholder="Select Location"
        isClearable={false}
        isLoading={optionLoading}
        disabled={!data?.data.useMultiloc}
        maxMenuHeight={220}
        menuPosition="absolute"
      />

      <FormActionButtons
        onSubmit={handleSubmit(onSubmit)}
        onCancel={toggle}
        isLoading={isLoading}
        disabledSubmitButton={optionLoading || userLoading}
      />
    </div>
  );
};

export default memo(RemoveQuantity);
