import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import AdjustQuantity from './AdjustQuantity';
import { useUpdateAdjustQuantityMutation } from '@/redux/features/items/item.api';
import { useGetCompanyQuery } from '@/redux/features/company/company.api';
import { useGetListQuery } from '@/redux/features/list/category/list.api';
import { commonApi } from '@/redux/features/common-api/common.api';

vi.mock('@/redux/features/items/item.api', () => ({
  useUpdateAdjustQuantityMutation: vi.fn(),
}));

vi.mock('@/redux/features/company/company.api', () => ({
  useGetCompanyQuery: vi.fn(),
}));

vi.mock('@/redux/features/list/category/list.api', () => ({
  useGetListQuery: vi.fn(),
}));

vi.mock(
  import('@/redux/features/common-api/common.api'),
  async (importOriginal) => {
    const actual = await importOriginal();
    return {
      ...actual,
      useGetCommonDataQuery: vi.fn(),
    };
  }
);

const mockStore = configureStore({
  reducer: {
    [commonApi.reducerPath]: commonApi.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(commonApi.middleware),
});

describe('AdjustQuantity Component', () => {
  it('renders the form and submits successfully', async () => {
    const mockUpdateAdjustQuantity = vi.fn().mockResolvedValue({});
    (useUpdateAdjustQuantityMutation as any).mockReturnValue([
      mockUpdateAdjustQuantity,
      { isLoading: false },
    ]);
    (useGetCompanyQuery as any).mockReturnValue([
      mockUpdateAdjustQuantity,
      { isLoading: false },
    ]);
    (useGetListQuery as any).mockReturnValue([
      mockUpdateAdjustQuantity,
      { isLoading: false },
    ]);
    const mockToggle = vi.fn();
    const mockQuantity = 100;
    render(
      <Provider store={mockStore}>
        <AdjustQuantity toggle={mockToggle} quantity={mockQuantity} />
      </Provider>
    );
    expect(screen.getByText('Quantity')).toBeInTheDocument();
    expect(screen.getByText('Description')).toBeInTheDocument();
  });

  it('displays loading state during submission', async () => {
    const mockUpdateAdjustQuantity = vi.fn().mockResolvedValue({});
    (useUpdateAdjustQuantityMutation as any).mockReturnValue([
      mockUpdateAdjustQuantity,
      { isLoading: true },
    ]);
    (useGetCompanyQuery as any).mockReturnValue([
      mockUpdateAdjustQuantity,
      { isLoading: false },
    ]);
    (useGetListQuery as any).mockReturnValue([
      mockUpdateAdjustQuantity,
      { isLoading: false },
    ]);
    const mockToggle = vi.fn();
    const mockQuantity = 100;
    render(
      <Provider store={mockStore}>
        <AdjustQuantity toggle={mockToggle} quantity={mockQuantity} />
      </Provider>
    );
    const submitButton = screen.getByRole('button', { name: /submit/i });
    expect(submitButton).toBeDisabled();
  });

  it('cancels the form without submitting', async () => {
    const mockUpdateAdjustQuantity = vi.fn().mockResolvedValue({});
    (useUpdateAdjustQuantityMutation as any).mockReturnValue([
      mockUpdateAdjustQuantity,
      { isLoading: false },
    ]);
    (useGetCompanyQuery as any).mockReturnValue([
      mockUpdateAdjustQuantity,
      { isLoading: false },
    ]);
    (useGetListQuery as any).mockReturnValue([
      mockUpdateAdjustQuantity,
      { isLoading: false },
    ]);
    const mockToggle = vi.fn();
    const mockQuantity = 100;
    render(
      <Provider store={mockStore}>
        <AdjustQuantity toggle={mockToggle} quantity={mockQuantity} />
      </Provider>
    );
    const cancelButton = screen.getByRole('button', { name: /cancel/i });
    fireEvent.click(cancelButton);
    expect(mockToggle).toHaveBeenCalled();
    expect(mockUpdateAdjustQuantity).not.toHaveBeenCalled();
  });
});
