import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import EditQuantity from './EditQuantity';
import { useUpdateQuantityMutation } from '@/redux/features/items/item.api';
import itemReducer from '@/redux/features/items/itemSlice';
import { commonApi } from '@/redux/features/common-api/common.api';

// Mock the useUpdateQuantityMutation hook
vi.mock('@/redux/features/items/item.api', () => ({
  useUpdateQuantityMutation: vi.fn(() => [vi.fn(), { isLoading: false }]),
}));

// Mock the store
const mockStore = configureStore({
  reducer: {
    item: itemReducer,
    [commonApi.reducerPath]: commonApi.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(commonApi.middleware),
});

describe('EditQuantity Component', () => {
  it('renders the form and submits successfully', async () => {
    const mockUpdateQuantity = vi.fn().mockResolvedValue({});
    (useUpdateQuantityMutation as any).mockReturnValue([
      mockUpdateQuantity,
      { isLoading: false },
    ]);
    const mockToggle = vi.fn();
    const mockQuantityDetails = {
      id: 1,
      unitCost: 10.5,
    };
    render(
      <Provider store={mockStore}>
        <EditQuantity
          toggle={mockToggle}
          quantityDetails={mockQuantityDetails}
        />
      </Provider>
    );
    expect(mockToggle);
  });

  it('displays loading state during submission', async () => {
    const mockUpdateQuantity = vi.fn().mockResolvedValue({});
    (useUpdateQuantityMutation as any).mockReturnValue([
      mockUpdateQuantity,
      { isLoading: true },
    ]);
    const mockToggle = vi.fn();
    const mockQuantityDetails = {
      id: 1,
      unitCost: 10.5,
    };
    render(
      <Provider store={mockStore}>
        <EditQuantity
          toggle={mockToggle}
          quantityDetails={mockQuantityDetails}
        />
      </Provider>
    );
    const submitButton = screen.getByText('Submit');
    const isSubmitted = fireEvent.click(submitButton);
    expect(isSubmitted);
  });

  it('cancels the form without submitting', async () => {
    const mockUpdateQuantity = vi.fn().mockResolvedValue({});
    (useUpdateQuantityMutation as any).mockReturnValue([
      mockUpdateQuantity,
      { isLoading: false },
    ]);
    const mockToggle = vi.fn();
    const mockQuantityDetails = {
      id: 1,
      unitCost: 10.5,
    };
    render(
      <Provider store={mockStore}>
        <EditQuantity
          toggle={mockToggle}
          quantityDetails={mockQuantityDetails}
        />
      </Provider>
    );
    const cancelButton = screen.getByText('Cancel');
    const isCancel = fireEvent.click(cancelButton);
    expect(isCancel);
  });
});
