import { memo, useEffect, useMemo } from 'react';
import { SubmitHand<PERSON>, useForm } from 'react-hook-form';
import useOptionList from '@/hooks/useOptionList';

import FormActionButtons from '@/components/common/FormActionButtons';
import Input<PERSON>ield from '@/components/forms/input-field';
import NumberInputField from '@/components/forms/number-input-field';
import SelectWidget from '@/components/forms/select';

import { useUpdateAdjustQuantityMutation } from '@/redux/features/items/item.api';
import { useGetListQuery } from '@/redux/features/list/category/list.api';
import { useGetCompanyQuery } from '@/redux/features/company/company.api';

import { TEXT_VALIDATION_RULE } from '@/constants/validation-constants';
import { getQueryParam, getStorageValue } from '@/lib/utils';
import { AdjustQuantityFormDataTypes } from '@/types/item.types';
import { STORE_OPTIONS_API_ROUTES } from '@/constants/api-constants';
import { STORAGE_KEYS } from '@/constants/storageKeys';
import { USER_OPTIONS_API } from '@/constants/user-options-constants';

type AdjustQuantityPropsType = {
  toggle: () => void;
  quantity: number | string | null;
};

const AdjustQuantity = ({ toggle, quantity }: AdjustQuantityPropsType) => {
  const id = getQueryParam('id') as string;
  const userId = getStorageValue(STORAGE_KEYS.USER_ID) || '0';

  const [updateAdjustQuantity, { isLoading }] =
    useUpdateAdjustQuantityMutation();
  const { data } = useGetCompanyQuery();
  const { data: userData, isLoading: userLoading } = useGetListQuery({
    url: USER_OPTIONS_API?.GET(userId),
  });

  const { options: storeLocationList, optionLoading } = useOptionList({
    url: STORE_OPTIONS_API_ROUTES.ALL,
    labelKey: 'locationName',
    valueKey: 'id',
    sortBy: 'locationName',
    extraKey: 'isDefault',
  });

  const defaultStoreLocation = useMemo(() => {
    return storeLocationList?.find((opt: any) => opt.extraKey);
  }, [storeLocationList]);

  const defaultValues = useMemo(() => {
    return {
      description: 'Quantity Adjustment',
      quantity: quantity?.toString() ?? '',
      storeLocationId: data?.data?.useMultiloc
        ? userData?.data?.defaultLocationId
        : defaultStoreLocation?.value || 1,
    };
  }, [
    data?.data.useMultiloc,
    defaultStoreLocation?.value,
    quantity,
    userData?.data?.defaultLocationId,
  ]);

  const form = useForm<AdjustQuantityFormDataTypes>({
    defaultValues,
    mode: 'onChange',
  });
  const { handleSubmit } = form;

  useEffect(() => {
    if (defaultStoreLocation?.value && userData?.data) {
      form.reset(defaultValues);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [defaultStoreLocation?.value, userData?.data, form]);

  // Submit handler
  const onSubmit: SubmitHandler<AdjustQuantityFormDataTypes> = async (
    formData
  ) => {
    try {
      await updateAdjustQuantity({
        quantityData: {
          ...formData,
          itemId: Number(id),
          quantity: Number(formData.quantity),
        },
      });
      toggle();
    } catch (error) {}
  };

  return (
    <div className="flex flex-col gap-2">
      <p>
        Change the total quantity here to automatically add or remove the
        appropriate adjustments to the quantity table. (Description is
        optional.)
      </p>
      <NumberInputField
        form={form}
        name="quantity"
        label="Quantity"
        maxLength={9}
        placeholder="Quantity"
        decimalScale={0}
        allowNegative
        validation={TEXT_VALIDATION_RULE}
      />

      <InputField
        form={form}
        name="description"
        label="Description"
        placeholder="Description"
      />

      <SelectWidget
        form={form}
        optionsList={storeLocationList}
        name="storeLocationId"
        label="Location"
        placeholder="Select Location"
        isClearable={false}
        isLoading={optionLoading}
        disabled={!data?.data.useMultiloc}
        maxMenuHeight={220}
        menuPosition="absolute"
      />

      <FormActionButtons
        onSubmit={handleSubmit(onSubmit)}
        onCancel={toggle}
        isLoading={isLoading}
        disabledSubmitButton={optionLoading || userLoading}
      />
    </div>
  );
};

export default memo(AdjustQuantity);
