import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import RemoveQuantity from './RemoveQuantity';
import {
  itemApi,
  useRemoveQuantityMutation,
} from '@/redux/features/items/item.api';
import {
  companyApi,
  useGetCompanyQuery,
} from '@/redux/features/company/company.api';
import {
  listApi,
  useGetListQuery,
} from '@/redux/features/list/category/list.api';
import { commonApi } from '@/redux/features/common-api/common.api';

// Mock API hooks
vi.mock(import('@/redux/features/items/item.api'), async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...actual,
    useRemoveQuantityMutation: vi.fn(),
  };
});

vi.mock(
  import('@/redux/features/company/company.api'),
  async (importOriginal) => {
    const actual = await importOriginal();
    return {
      ...actual,
      useGetCompanyQuery: vi.fn(),
    };
  }
);

vi.mock(
  import('@/redux/features/list/category/list.api'),
  async (importOriginal) => {
    const actual = await importOriginal();
    return {
      ...actual,
      useGetListQuery: vi.fn(),
    };
  }
);

vi.mock(
  import('@/redux/features/common-api/common.api'),
  async (importOriginal) => {
    const actual = await importOriginal();
    return {
      ...actual,
      useGetCommonDataQuery: vi.fn(),
    };
  }
);

const createMockStore = () =>
  configureStore({
    reducer: {
      [itemApi.reducerPath]: itemApi.reducer,
      [companyApi.reducerPath]: companyApi.reducer,
      [listApi.reducerPath]: listApi.reducer,
      [commonApi.reducerPath]: commonApi.reducer,
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware().concat(
        itemApi.middleware,
        companyApi.middleware,
        listApi.middleware,
        commonApi.middleware
      ),
  });

describe('RemoveQuantity Component', () => {
  let store: ReturnType<typeof createMockStore>;
  let mockToggle: ReturnType<typeof vi.fn>;
  const mockQuantityId = '123';

  beforeEach(() => {
    store = createMockStore();
    mockToggle = vi.fn();

    const mockRemoveQuantity = vi
      .fn()
      .mockResolvedValue({ data: { success: true } });

    (useRemoveQuantityMutation as any).mockReturnValue([
      mockRemoveQuantity,
      { isLoading: false },
    ]);
    (useGetCompanyQuery as any).mockReturnValue({
      data: {
        data: {
          useMultiloc: true,
        },
      },
      isLoading: false,
    });
    (useGetListQuery as any).mockReturnValue({
      data: [{ id: 'list-id', name: 'List Item' }],
      isLoading: false,
    });
  });

  it('renders the form and submits successfully', () => {
    render(
      <Provider store={store}>
        <RemoveQuantity toggle={mockToggle} quantityId={mockQuantityId} />
      </Provider>
    );

    expect(screen.getByText('Quantity')).toBeInTheDocument();
    expect(screen.getByText('Description')).toBeInTheDocument();
  });

  it('displays loading state during submission', () => {
    render(
      <Provider store={store}>
        <RemoveQuantity toggle={mockToggle} quantityId={mockQuantityId} />
      </Provider>
    );

    const submitButton = screen.getByRole('button', { name: /submit/i });
    expect(submitButton);
  });

  it('cancels the form without submitting', () => {
    render(
      <Provider store={store}>
        <RemoveQuantity toggle={mockToggle} quantityId={mockQuantityId} />
      </Provider>
    );

    const cancelButton = screen.getByRole('button', { name: /cancel/i });
    fireEvent.click(cancelButton);
    expect(mockToggle).toHaveBeenCalled();
  });
});
