import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import AddQuantityForm from './AddQuantity';
import { useAddQuantityMutation } from '@/redux/features/items/item.api';
import { useGetCompanyQuery } from '@/redux/features/company/company.api';
import useOptionList from '@/hooks/useOptionList';
import itemReducer from '@/redux/features/items/itemSlice';
import { commonApi } from '@/redux/features/common-api/common.api';
import { useGetListQuery } from '@/redux/features/list/category/list.api';

// Mock the hooks
vi.mock('@/redux/features/items/item.api', async () => {
  const actual = await import('@/redux/features/items/item.api');
  return {
    ...actual,
    useAddQuantityMutation: vi.fn(),
  };
});

vi.mock('@/redux/features/company/company.api', async () => {
  const actual = await import('@/redux/features/company/company.api');
  return {
    ...actual,
    useGetCompanyQuery: vi.fn(),
  };
});

vi.mock('@/hooks/useOptionList', async () => {
  const actual = await import('@/hooks/useOptionList');
  return {
    ...actual,
    default: vi.fn(), // Mock the default export of useOptionList
  };
});

vi.mock('@/redux/features/list/category/list.api', async () => {
  const actual = await import('@/redux/features/list/category/list.api');
  return {
    ...actual,
    useGetListQuery: vi.fn(),
  };
});

// Mock the store
const mockStore = configureStore({
  reducer: {
    item: itemReducer,
    [commonApi.reducerPath]: commonApi.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(commonApi.middleware),
});

describe('AddQuantityForm Component', () => {
  it('renders the form and submits successfully', async () => {
    const mockAddQuantity = vi.fn().mockResolvedValue({});
    (useAddQuantityMutation as any).mockReturnValue([
      mockAddQuantity,
      { isLoading: false },
    ]);

    (useGetCompanyQuery as any).mockReturnValue({
      data: { data: [] },
    });

    (useGetListQuery as any).mockReturnValue({
      data: { data: { useMultiloc: true } },
    });

    (useOptionList as any).mockReturnValue({
      options: [
        { id: 1, locationName: 'Location 1' },
        { id: 2, locationName: 'Location 2' },
      ],
      optionLoading: false,
    });

    const mockToggle = vi.fn();

    render(
      <Provider store={mockStore}>
        <AddQuantityForm toggle={mockToggle} />
      </Provider>
    );

    expect(screen.getByText('Purchase Date')).toBeInTheDocument();
    expect(screen.getByText('Quantity')).toBeInTheDocument();
    expect(screen.getByText('Description')).toBeInTheDocument();
    expect(screen.getByText('Unit Cost')).toBeInTheDocument();
    expect(screen.getByText('Location')).toBeInTheDocument();
  });

  it('displays loading state during submission', async () => {
    const mockAddQuantity = vi.fn().mockResolvedValue({});
    (useAddQuantityMutation as any).mockReturnValue([
      mockAddQuantity,
      { isLoading: true },
    ]);

    (useGetCompanyQuery as any).mockReturnValue({
      data: { data: { useMultiloc: true } },
    });

    (useOptionList as any).mockReturnValue({
      options: [
        { id: 1, locationName: 'Location 1' },
        { id: 2, locationName: 'Location 2' },
      ],
      optionLoading: false,
    });

    const mockToggle = vi.fn();

    render(
      <Provider store={mockStore}>
        <AddQuantityForm toggle={mockToggle} />
      </Provider>
    );

    const submitButton = screen.getByText('Submit');
    expect(submitButton);
  });

  it('cancels the form without submitting', async () => {
    const mockAddQuantity = vi.fn().mockResolvedValue({});
    (useAddQuantityMutation as any).mockReturnValue([
      mockAddQuantity,
      { isLoading: false },
    ]);

    (useGetCompanyQuery as any).mockReturnValue({
      data: { data: { useMultiloc: true } },
    });

    (useOptionList as any).mockReturnValue({
      options: [
        { id: 1, locationName: 'Location 1' },
        { id: 2, locationName: 'Location 2' },
      ],
      optionLoading: false,
    });

    const mockToggle = vi.fn();

    render(
      <Provider store={mockStore}>
        <AddQuantityForm toggle={mockToggle} />
      </Provider>
    );

    const cancelButton = screen.getByRole('button', { name: 'Cancel' });
    fireEvent.click(cancelButton);

    expect(mockToggle).toHaveBeenCalled();

    expect(mockAddQuantity).not.toHaveBeenCalled();
  });
});
