import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import PackageItemForm from './PackageItemForm';
import {
  useGetItemBriefMutation,
  useUpdatePackageItemMutation,
} from '@/redux/features/items/item.api';

// Mock the hooks and components
vi.mock('@/redux/features/items/item.api', () => ({
  useGetItemBriefMutation: vi.fn(),
  useUpdatePackageItemMutation: vi.fn(),
}));

vi.mock('@/lib/utils', () => ({
  getQueryParam: vi.fn(() => '123'),
  cn: vi.fn(() => 'mocked-class'),
}));

vi.mock('@/components/common/data-tables', () => ({
  default: vi.fn(() => <div>DataTable Component</div>),
}));

vi.mock('@/components/common/ReactSelect', () => ({
  default: vi.fn(() => <div>ReactSelect Component</div>),
}));

vi.mock('@/components/forms/number-input-field', () => ({
  default: vi.fn(() => <div>NumberInputField Component</div>),
}));

vi.mock('@/components/common/data-tables/ActionColumn', () => ({
  default: vi.fn(() => <div>ActionColumnMenu Component</div>),
}));

vi.mock('@/components/common/app-button', () => ({
  default: vi.fn(({ onClick, label }) => (
    <button onClick={onClick}>{label}</button>
  )),
}));

vi.mock('@/components/common/app-confirmation-modal', () => ({
  default: vi.fn(({ open }) => (open ? <div>Confirmation Modal</div> : null)),
}));

describe('PackageItemForm Component', () => {
  const mockForm = {
    control: {},
    handleSubmit: vi.fn((fn) => fn),
    watch: vi.fn(),
    reset: vi.fn(),
  };
  const mockUpdate = vi.fn();
  const mockAppend = vi.fn();
  const mockRemove = vi.fn();
  const mockOnOpenChange = vi.fn();
  const mockSetActiveTab = vi.fn();

  const props = {
    form: mockForm,
    update: mockUpdate,
    fields: [],
    append: mockAppend,
    onOpenChange: mockOnOpenChange,
    isPackageItemLoading: false,
    remove: mockRemove,
    setActiveTab: mockSetActiveTab,
  };

  beforeEach(() => {
    vi.clearAllMocks();
    (useGetItemBriefMutation as any).mockReturnValue([
      vi.fn().mockResolvedValue({
        data: {
          data: {
            description: 'Test Description',
          },
        },
      }),
    ]);
    (useUpdatePackageItemMutation as any).mockReturnValue([
      vi.fn().mockResolvedValue({}),
      { isLoading: false },
    ]);
  });

  it('renders without crashing', () => {
    render(<PackageItemForm {...props} />);
    expect(screen.getByText('DataTable Component')).toBeInTheDocument();
  });

  it('renders action buttons correctly', () => {
    render(<PackageItemForm {...props} />);
    expect(screen.getByText('Item Lookup')).toBeInTheDocument();
    expect(screen.getByText('+ Add New')).toBeInTheDocument();
    expect(screen.getByText('Submit')).toBeInTheDocument();
    expect(screen.getByText('Cancel')).toBeInTheDocument();
  });

  it('calls append when Add New button is clicked', () => {
    render(<PackageItemForm {...props} />);
    fireEvent.click(screen.getByText('+ Add New'));
    expect(mockAppend).toHaveBeenCalledWith({
      id: '',
      itemId: { label: '', value: '' },
      quantity: null,
      description: '',
    });
  });

  it('switches to item lookup tab when button is clicked', () => {
    render(<PackageItemForm {...props} />);
    fireEvent.click(screen.getByText('Item Lookup'));
    expect(mockSetActiveTab).toHaveBeenCalledWith('item-lookup');
  });

  it('handles form submission correctly', async () => {
    const mockHandleSubmit = vi.fn();
    mockForm.handleSubmit.mockImplementation((fn) => {
      fn({ editPackage: [] });
      return mockHandleSubmit;
    });
    render(<PackageItemForm {...props} />);
    fireEvent.click(screen.getByText('Submit'));
    await waitFor(() => {
      expect(mockForm.handleSubmit).toHaveBeenCalled();
    });
  });

  it('calls onOpenChange when cancel is clicked', () => {
    render(<PackageItemForm {...props} />);
    fireEvent.click(screen.getByText('Cancel'));
    expect(mockOnOpenChange).toHaveBeenCalled();
  });

  it('displays loading state when submitting', () => {
    (useUpdatePackageItemMutation as any).mockReturnValue([
      vi.fn(),
      { isLoading: true },
    ]);
    render(<PackageItemForm {...props} />);
    expect(screen.getByText('Submit'));
  });
});
