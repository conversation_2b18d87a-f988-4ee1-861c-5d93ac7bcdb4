import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Submit<PERSON>and<PERSON> } from 'react-hook-form';

// Components
import AppButton from '@/components/common/app-button';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import ActionColumnMenu from '@/components/common/data-tables/ActionColumn';
import NumberInputField from '@/components/forms/number-input-field';

// Constants and Utils
import { ITEMS_API_ROUTES } from '@/constants/api-constants';
import { cn, getQueryParam } from '@/lib/utils';

// API Hooks
import DataTable from '@/components/common/data-tables';
import ReactSelect from '@/components/common/ReactSelect';
import {
  useGetItemBriefMutation,
  useUpdatePackageItemMutation,
} from '@/redux/features/items/item.api';
import { SearchIcon } from 'lucide-react';

interface PackageItem {
  id: number | null | string;
  itemId: {
    label: string;
    value: string;
  };
  quantity?: number | string | null;
  description: string;
  parentItemId?: number;
  childItemId?: number;
}

interface EditPackageItems {
  editPackage: PackageItem[];
}

interface DeleteDialogState {
  open: boolean;
  id: number;
}

const PackageItemForm = ({
  form,
  update,
  fields,
  append,
  onOpenChange,
  isPackageItemLoading,
  remove,
  setActiveTab,
}: any) => {
  // State and Hooks
  const [openDeleteDialog, setOpenDeleteDialog] = useState<DeleteDialogState>({
    open: false,
    id: 0,
  });
  const onScrollRef = useRef<HTMLTableElement>(null);
  const id = getQueryParam('id') as string;
  const [updatePackage, { isLoading }] = useUpdatePackageItemMutation();
  const [getitemBrief] = useGetItemBriefMutation();

  const handleAddNew = useCallback(() => {
    append({
      id: '',
      itemId: { label: '', value: '' },
      quantity: null,
      description: '',
    });
  }, [append]);

  // Form submission handler
  const onSubmit: SubmitHandler<EditPackageItems> = useCallback(
    async (formData) => {
      try {
        const payload = formData?.editPackage
          ?.filter((item) => item?.itemId?.value)
          ?.map((value) => ({
            id:
              typeof value.id === 'string' || value.id === null
                ? null
                : value.id,
            parentItemId: Number(id),
            childItemId: value.childItemId,
            itemId: value.itemId.label,
            description: value.description,
            quantity: Number(value.quantity),
          }));

        await updatePackage({ body: payload, itemId: id }).unwrap();
        onOpenChange();
      } catch (error) {}
    },
    [id, onOpenChange, updatePackage]
  );

  // toggle delete
  const toggleDelete = useCallback((id?: number) => {
    setOpenDeleteDialog((prev) => ({ open: !prev?.open, id: id || 0 }));
  }, []);

  useEffect(() => {
    if (onScrollRef.current) {
      // Scroll to the bottom of the table when the fields change
      onScrollRef.current.scrollTop = onScrollRef.current.scrollHeight;
    }
  }, [fields]); // Trigger scroll when fields change

  const handleDelete = useCallback(() => {
    remove(openDeleteDialog.id);
    toggleDelete();
  }, [openDeleteDialog.id, remove, toggleDelete]);

  // Table Columns Configuration
  const columns = useMemo(
    () => [
      {
        accessorKey: 'itemId',
        header: 'Item ID',
        size: 80,
        enableSorting: true,
        cell: ({ row }: any) => {
          const handleOnItemChange = async (value: any, index: number) => {
            const itemData = await getitemBrief(value?.value);
            if (itemData?.data) {
              update(index, {
                ...fields[index],
                itemId: value,
                childItemId: value?.value,
                description: itemData.data?.data?.description ?? '',
              });
            }
          };

          return (
            <ReactSelect
              placeholder="Item ID"
              className=" bg-white"
              name={`editPackage.${row?.index}.itemId`}
              form={form}
              onSelectChange={(value) => handleOnItemChange(value, row.index)}
              url={ITEMS_API_ROUTES.PACKAGE_ITEM_LOOKUP_DROPDOWN(id)}
              labelKey="itemId"
              valueKey="id"
              maxMenuHeight={250}
            />
          );
        },
      },
      {
        accessorKey: 'quantity',
        header: 'Quantity',
        size: 80,
        enableSorting: true,
        cell: ({ row }) => {
          const itemId = form.watch(`editPackage.${row?.index}.itemId`);
          const disabled = itemId?.value;
          return (
            <div className="p-1">
              <NumberInputField
                form={form}
                placeholder="Quantity"
                name={`editPackage.${row?.index}.quantity`}
                className="w-28 h-8"
                decimalScale={0}
                disabled={!disabled}
                maxLength={5}
              />
            </div>
          );
        },
      },
      {
        accessorKey: 'description',
        header: 'Item Description',
        maxSize: 300,
        enableSorting: true,
        cell: ({ row }: any) => (
          <div className="flex items-center justify-between ">
            <div className="max-w-[280px] truncate">
              {row?.original?.description}
            </div>
            <ActionColumnMenu
              onDelete={() => toggleDelete(row?.index)}
              contentClassName="z-[99] bg-white"
            />
          </div>
        ),
      },
    ],

    [form, id, getitemBrief, update, fields, toggleDelete]
  );

  return (
    <>
      <div className="flex flex-col gap-2">
        {/* Filter Section */}
        <div className="flex justify-end">
          <AppButton
            label="Item Lookup"
            variant="primary"
            icon={SearchIcon}
            iconClassName="w-5 h-5"
            className="bg-brand-teal-Default hover:bg-brand-teal-Default"
            onClick={() => setActiveTab('item-lookup')}
          />
        </div>

        {/* DataTable Section */}
        <DataTable
          onScrollRef={onScrollRef}
          tableClassName={cn('max-h-[300px] 2xl:max-h-[470px] overflow-auto')}
          data={fields}
          columns={columns}
          isLoading={isPackageItemLoading}
          enableSearch={false}
          enablePagination={false}
          bindingKey="id"
        />

        <AppButton
          label="+ Add New"
          variant="neutral"
          className="bg-brand-teal-Default mt-2 w-fit hover:bg-brand-teal-hover border-border-brand-teal-Default text-white mb-2"
          iconClassName="text-white"
          onClick={handleAddNew}
        />
      </div>

      {/* Fixed Button Section */}
      <div className="absolute bottom-0 left-0 w-full bg-white border-t border-gray-200 px-6 py-4 flex justify-end space-x-4 z-10">
        <AppButton
          label="Submit"
          isLoading={isLoading}
          onClick={form.handleSubmit(onSubmit)}
          className="w-24"
        />
        <AppButton
          label="Cancel"
          onClick={onOpenChange}
          variant="neutral"
          className="w-24"
        />
      </div>
      <AppConfirmationModal
        description={<div>Are you sure you want to delete ?</div>}
        open={openDeleteDialog.open}
        handleCancel={toggleDelete}
        handleSubmit={handleDelete}
      />
    </>
  );
};

export default PackageItemForm;
