import { useCallback, useEffect, useMemo, useState } from 'react';
import { useFieldArray, useForm } from 'react-hook-form';

// Components

// Constants and Utils
import { ITEMS_API_ROUTES } from '@/constants/api-constants';
import { getQueryParam } from '@/lib/utils';

// API Hooks
import BreadcrumbDialogRenderer from '@/components/common/BreadcrumbDialogRenderer';
import { useGetPackageItemsQuery } from '@/redux/features/items/item.api';
import ItemLookup from '../edit-kit/KitItemLookup';
import PackageItemForm from './PackageItemForm';

// Types

interface PackageItem {
  id: number | null | string;
  itemId: {
    label: string;
    value: string;
  };
  quantity?: number | string | null;
  description: string;
  parentItemId?: number;
  childItemId?: number;
}

interface EditPackagePropType {
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  open: boolean;
}

interface EditPackageItems {
  editPackage: PackageItem[];
}

const EditPackage = ({ setOpen, open }: EditPackagePropType) => {
  const id = getQueryParam('id') as string;

  // API Hooks
  const { data, isLoading: isPackageItemLoading } = useGetPackageItemsQuery(
    id,
    {
      skip: !open,
    }
  );

  // Form Setup
  const defaultValues = useMemo(() => {
    return {
      editPackage:
        data?.data?.map((item) => ({
          id: item?.id ?? null,
          parentItemId: item?.parentItemId,
          childItemId: item?.childItemId,
          itemId: {
            label: item?.itemId ?? '',
            value: item?.childItemId?.toString() ?? '',
          },
          quantity: item?.quantity ?? 0,
          description: item?.description ?? '',
        })) || [],
    };
  }, [data?.data]);

  const form = useForm<EditPackageItems>({
    defaultValues,
    mode: 'onChange',
  });

  const { fields, append, remove, update } = useFieldArray({
    control: form.control,
    name: 'editPackage',
  });

  const onOpenChange = useCallback(() => {
    form.reset();
    setActiveTab('edit-package');
    setOpen((prev) => !prev);
  }, [form, setOpen]);

  useEffect(() => {
    if (defaultValues) {
      form.reset(defaultValues);
    }
  }, [defaultValues, form]);

  const handleAddItemLookupData = useCallback(
    (data: any) => {
      setActiveTab('edit-package');
      append(data);
    },
    [append]
  );

  const [activeTab, setActiveTab] = useState('edit-package');
  const handleChangeTab = useCallback((tab?: string) => {
    setActiveTab(tab ?? 'edit-package');
  }, []);

  // tab list
  const listItems = useMemo(() => {
    return [
      {
        label: 'Edit Package',
        value: 'edit-package',
        content: (
          <PackageItemForm
            form={form}
            update={update}
            fields={fields}
            append={append}
            onOpenChange={onOpenChange}
            isPackageItemLoading={isPackageItemLoading}
            remove={remove}
            setActiveTab={setActiveTab}
          />
        ),
      },
      {
        label: 'Item Lookup',
        value: 'item-lookup',
        content: (
          <ItemLookup
            onClick={handleAddItemLookupData}
            url={ITEMS_API_ROUTES.KIT_ITEM_LOOKUP(id)}
            btnLabel="Add to Package List"
            heading="Select one or more Item to add to the Package list"
          />
        ),
      },
    ];
  }, [
    append,
    fields,
    form,
    handleAddItemLookupData,
    id,
    isPackageItemLoading,
    onOpenChange,
    remove,
    update,
  ]);

  return (
    <>
      <BreadcrumbDialogRenderer
        activeTab={activeTab}
        isOpen={open}
        listItems={listItems}
        onOpenChange={onOpenChange}
        setActiveTab={handleChangeTab}
        className="max-w-[80%] 2xl:max-w-[55%] overflow-x-auto"
        contentClassName="h-[500px] 2xl:h-[680px] overflow-y-auto"
      />
    </>
  );
};

export default EditPackage;
