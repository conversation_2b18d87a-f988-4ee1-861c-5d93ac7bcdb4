import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, vi, expect, beforeEach } from 'vitest';
import {
  useGetItemBriefMutation,
  useGetPackageItemsQuery,
  useUpdatePackageItemMutation,
} from '@/redux/features/items/item.api';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import itemsReducer from '@/redux/features/items/itemSlice';
import { commonApi } from '@/redux/features/common-api/common.api';
import EditPackage from './EditPackage';

// Create a mock store
const mockStore = configureStore({
  reducer: {
    items: itemsReducer,
    [commonApi.reducerPath]: commonApi.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(commonApi.middleware),
});

// Mock Dependencies
vi.mock('react-hook-form', async () => {
  const actual = await import('react-hook-form');
  return {
    ...actual,
    useForm: vi.fn(() => ({
      control: {},
      handleSubmit: vi.fn(),
      formState: { errors: {} },
      reset: vi.fn(),
      watch: vi.fn(() => []),
    })),
    useFieldArray: vi.fn(() => ({
      fields: [],
      append: vi.fn(),
      remove: vi.fn(),
      update: vi.fn(),
    })),
  };
});

vi.mock('@/redux/features/items/item.api', async () => {
  const actual = await import('@/redux/features/items/item.api');
  return {
    ...actual,
    useGetPackageItemsQuery: vi.fn(),
    useUpdatePackageItemMutation: vi.fn(),
    useGetItemBriefMutation: vi.fn(),
  };
});

describe('EditPackage Component', () => {
  const setOpenMock = vi.fn();

  beforeEach(() => {
    (useGetPackageItemsQuery as any).mockReturnValue({
      data: { data: [] },
      isLoading: false,
    });

    (useUpdatePackageItemMutation as any).mockReturnValue([
      vi.fn(),
      { isLoading: false },
    ]);

    (useGetItemBriefMutation as any).mockReturnValue([
      vi.fn(),
      { isLoading: false },
    ]);
  });

  const renderWithProvider = (component: React.ReactNode) => {
    return render(<Provider store={mockStore}>{component}</Provider>);
  };

  it('renders the EditPackage component correctly', async () => {
    renderWithProvider(<EditPackage setOpen={setOpenMock} open={true} />);

    expect(screen.getByText(/Edit Package/i)).toBeInTheDocument();
    expect(screen.getByText(/\+ Add New/i)).toBeInTheDocument();
    expect(screen.getByText(/Submit/i)).toBeInTheDocument();
  });

  it('adds a new package item', async () => {
    renderWithProvider(<EditPackage setOpen={setOpenMock} open={true} />);

    const addButton = screen.getByText(/\+ Add New/i);
    const isAdded = fireEvent.click(addButton);
    expect(isAdded);
  });

  it('submits the form', async () => {
    renderWithProvider(<EditPackage setOpen={setOpenMock} open={true} />);

    const submitButton = screen.getByText(/Submit/i);
    const isSubmitted = fireEvent.click(submitButton);
    expect(isSubmitted);
  });
});
