import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { MemoryRouter } from 'react-router-dom';
import Items from './index';
import { AppTableContext } from '@/components/common/app-data-table/AppTableContext';
import { commonApi } from '@/redux/features/common-api/common.api';

vi.mock('@/constants/api-constants', () => ({
  ITEMS_API_ROUTES: {
    ALL: '/api/items/all',
    GET_COLUMN: '/api/items/columns',
    UPDATE_COLUMN: '/api/items/columns/update',
    DOWNLOAD: '/api/items/download',
  },
  ROUTES: {
    ADD_NEW_ITEM: '/items/add',
    EDIT_ITEM: '/items/edit',
  },
}));

vi.mock('@/redux/features/items/item.api', () => ({
  useDeleteItemMutation: vi.fn(() => [vi.fn(), { isLoading: false }]),
}));

vi.mock('@/redux/features/common-api/common.api', async () => {
  const actual = await import('@/redux/features/common-api/common.api');
  return {
    ...actual,
    useAddNewItemMutation: vi.fn(() => [
      vi.fn(() => {
        return Promise.resolve({});
      }),
      { isLoading: false },
    ]),
  };
});

vi.mock('@/redux/features/list/category/list.api', () => ({
  useGetListQuery: vi.fn(() => {
    return {
      data: {
        data: [
          { accessorKey: 'name', header: 'Name', enabled: true, component: '' },
          {
            accessorKey: 'status',
            header: 'Status',
            enabled: true,
            component: 'StatusBadge',
          },
        ],
      },
      refetch: vi.fn(),
      isFetching: false,
    };
  }),
}));

vi.mock('@/hooks/useDownloadFile', () => ({
  useDownloadFile: vi.fn(() => ({
    downloadFile: vi.fn(() => Promise.resolve()),
  })),
}));

vi.mock('@/components/ui/toast/ToastContainer', () => ({
  UseToast: () => ({
    promise: vi.fn(),
  }),
}));

vi.mock('react-redux', async () => {
  const actual = await vi.importActual('react-redux');
  return {
    ...actual,
    useSelector: vi.fn(),
    useDispatch: vi.fn(() => vi.fn()),
  };
});

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: vi.fn(),
    Link: ({ children, to }: { children: React.ReactNode; to: string }) => (
      <a href={to}>{children}</a>
    ),
  };
});

const mockAppTableContext = {
  sorting: [],
  pagination: { pageIndex: 0, pageSize: 10 },
} as any;

const MockAppTableProvider = ({ children }: { children: React.ReactNode }) => (
  <AppTableContext.Provider value={mockAppTableContext}>
    {children}
  </AppTableContext.Provider>
);

describe('Items Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  const renderComponent = () => {
    const store = configureStore({
      reducer: {
        [commonApi.reducerPath]: commonApi.reducer,
        item: () => ({
          filters: [],
          bulkEdit: {
            isOpen: false,
            selectedItems: [],
          },
        }),
      },
      middleware: (getDefaultMiddleware) =>
        getDefaultMiddleware().concat(commonApi.middleware),
    });
    return render(
      <Provider store={store}>
        <MemoryRouter>
          <MockAppTableProvider>
            <Items />
          </MockAppTableProvider>
        </MemoryRouter>
      </Provider>
    );
  };

  it('should render the component with heading', () => {
    renderComponent();
    expect(screen.getByText('Items')).toBeInTheDocument();
  });

  it('should render the new item button', () => {
    renderComponent();
    expect(screen.getByText('New Item')).toBeInTheDocument();
  });

  it('should render the data table', () => {
    const tableRendered = renderComponent();
    expect(tableRendered);
  });

  it('should show column headers from API data', async () => {
    renderComponent();
    expect(screen.getByText('Name')).toBeInTheDocument();
    expect(screen.getByText('Status')).toBeInTheDocument();
    expect(screen.getByText('Actions')).toBeInTheDocument();
  });

  it('should open delete confirmation dialog when delete is clicked', async () => {
    const deleteButtons = renderComponent();
    expect(deleteButtons);
  });

  it('should open bulk edit dialog when bulk edit is clicked', async () => {
    const bulkEdit = renderComponent();
    expect(bulkEdit);
  });
});
