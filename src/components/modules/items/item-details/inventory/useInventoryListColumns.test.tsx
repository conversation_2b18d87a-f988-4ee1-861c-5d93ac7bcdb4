import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { useForm } from 'react-hook-form';
import { useInventoryListColumns } from './useInventoryListColumns';
import type { InventoryListTypes } from '@/types/item.types';

// Mock the dependent components
vi.mock('@/components/forms/select', () => ({
  default: (props: any) => (
    <div data-testid={`select-widget-${props.name}`} data-name={props.name}>
      {props.placeholder || 'select'}
      <button
        onClick={() => props.onSelectChange && props.onSelectChange('newValue')}
      >
        Change
      </button>
    </div>
  ),
}));

vi.mock('@/components/forms/input-field', () => ({
  default: (props: any) => (
    <input
      data-testid="input-field"
      name={props.name}
      placeholder={props.placeholder}
      onChange={props.onChange}
    />
  ),
}));

vi.mock('@/components/forms/number-input-field', () => ({
  default: (props: any) => (
    <input
      type="number"
      data-testid="number-input-field"
      name={props.name}
      onChange={(e) =>
        props.onValueChange && props.onValueChange(Number(e.target.value))
      }
    />
  ),
}));

vi.mock('@/components/common/data-tables/ActionColumn', () => ({
  default: (props: any) => (
    <button data-testid="action-menu" onClick={props.onDelete}>
      Delete
    </button>
  ),
}));

describe('useInventoryListColumns', () => {
  const TestComponent = ({
    toggleDelete = () => {},
    optionLoading = false,
    qualityTypeLoading = false,
    purchaseDateLoading = false,
    useMultiloc = true,
  }) => {
    const form = useForm<InventoryListTypes>({
      defaultValues: {
        inventory: [],
      },
    });
    const columns = useInventoryListColumns({
      form,
      toggleDelete,
      optionLoading,
      qualityTypeLoading,
      purchaseDateLoading,
      qualityTypesList: [{ label: 'Good', value: 'good' }],
      storeLocationList: [{ label: 'Main', value: 1 }],
      purchaseDateOptions: [{ label: '2023-01-01', value: '2023-01-01' }],
      useMultiloc,
    });
    return (
      <>
        {columns.map((col, i) => (
          <div key={i} data-testid={`col-${col.accessorKey || col.id}`}>
            {col.cell({ row: { index: 0, original: { itemId: 123 } } })}
          </div>
        ))}
      </>
    );
  };

  it('returns the correct number of columns and renders cells', () => {
    const toggleDelete = vi.fn();
    render(<TestComponent toggleDelete={toggleDelete} />);
    for (const key of [
      'storeLocationName',
      'serialNo',
      'qualitydesc',
      'quantity',
      'purchaseDateSequenceVal',
      'action',
    ]) {
      expect(screen.getByTestId(`col-${key}`)).toBeDefined();
    }
  });

  it('renders SelectWidget with proper name for storeLocationName column', () => {
    render(<TestComponent toggleDelete={vi.fn()} />);
    const select = screen.getByTestId(
      'select-widget-inventory.0.storeLocationId'
    );
    expect(select).toBeDefined();
  });

  it('calls toggleDelete when delete button is clicked', () => {
    const toggleDelete = vi.fn();
    render(<TestComponent toggleDelete={toggleDelete} />);
    const deleteBtn = screen.getByTestId('action-menu');
    fireEvent.click(deleteBtn);
    expect(toggleDelete).toHaveBeenCalledWith(123, 0);
  });

  it('handles input change on serialNo input and triggers validation logic', () => {
    const toggleDelete = vi.fn();
    render(<TestComponent toggleDelete={toggleDelete} />);
    const input = screen.getByTestId('input-field');
    expect(input.getAttribute('name')).toBe('inventory.0.serialNo');
    fireEvent.change(input, { target: { value: 'SN123' } });
  });

  it('handles number input change for quantity', () => {
    const toggleDelete = vi.fn();
    render(<TestComponent toggleDelete={toggleDelete} />);
    const numberInput = screen.getByTestId('number-input-field');
    expect(numberInput.getAttribute('name')).toBe('inventory.0.quantity');
    fireEvent.change(numberInput, { target: { value: '5' } });
  });
});
