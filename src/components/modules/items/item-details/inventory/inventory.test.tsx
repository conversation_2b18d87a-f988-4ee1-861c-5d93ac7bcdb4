import { render } from '@testing-library/react';
import { Provider } from 'react-redux';
import Inventory from './Inventory';
import { vi, describe, expect, it, beforeEach } from 'vitest';
import {
  useDeleteInventoryMutation,
  useGetInventoryListQuery,
  useGetPurchaseDateSequenceQuery,
  useValidateInventoryQuery,
} from '@/redux/features/items/item.api';
import { useGetCompanyQuery } from '@/redux/features/company/company.api';
import { configureStore } from '@reduxjs/toolkit';
import itemReducer from '@/redux/features/items/itemSlice';
import { commonApi } from '@/redux/features/common-api/common.api';
import { useForm } from 'react-hook-form';
import { InventoryListTypes } from '@/types/item.types';

// Mock the API hooks
vi.mock('@/redux/features/items/item.api', () => ({
  useGetInventoryListQuery: vi.fn(),
  useDeleteInventoryMutation: vi.fn(),
  useGetPurchaseDateSequenceQuery: vi.fn(),
  useValidateInventoryQuery: vi.fn(),
}));

vi.mock('@/redux/features/company/company.api', () => ({
  useGetCompanyQuery: vi.fn(),
}));

describe('Inventory Component', () => {
  const mockInventoryData = {
    data: [
      {
        id: 1,
        storeLocationName: 'Warehouse A',
        serialNo: 'SN123456',
        qualitydesc: 'High',
        quantity: 10,
        purchaseDateSequenceVal: '2024-01-15',
      },
      {
        id: 2,
        storeLocationName: 'Warehouse B',
        serialNo: 'SN654321',
        qualitydesc: 'Medium',
        quantity: 5,
        purchaseDateSequenceVal: '2024-02-10',
      },
    ],
  };

  const mockStore = configureStore({
    reducer: {
      item: itemReducer,
      [commonApi.reducerPath]: commonApi.reducer,
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware().concat(commonApi.middleware),
  });
  beforeEach(() => {
    (useGetInventoryListQuery as any).mockReturnValue({
      data: mockInventoryData,
      isLoading: false,
    });
    (useDeleteInventoryMutation as any).mockReturnValue([
      vi.fn(),
      {
        isLoading: false,
      },
    ]);
    (useGetPurchaseDateSequenceQuery as any).mockReturnValue({
      data: [],
      isLoading: false,
    });
    (useValidateInventoryQuery as any).mockReturnValue({
      data: [],
      isLoading: false,
    });
    (useGetCompanyQuery as any).mockReturnValue({
      data: [],
      isLoading: false,
    });
  });

  function InventoryWrapper() {
    const form = useForm<InventoryListTypes>({
      defaultValues: {
        inventory: [],
      },
    });

    return (
      <Provider store={mockStore}>
        <Inventory form={form} />
      </Provider>
    );
  }

  it('renders the inventory list correctly', () => {
    const isRendered = render(<InventoryWrapper />);
    expect(isRendered);
  });
});
