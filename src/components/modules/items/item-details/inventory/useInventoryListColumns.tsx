import ActionColumnMenu from '@/components/common/data-tables/ActionColumn';
import InputField from '@/components/forms/input-field';
import NumberInputField from '@/components/forms/number-input-field';
import SelectWidget from '@/components/forms/select';
import { TEXT_VALIDATION_RULE } from '@/constants/validation-constants';
import { InventoryListTypes } from '@/types/item.types';
import { useMemo } from 'react';
import { UseFormReturn } from 'react-hook-form';

interface UseItemListColumnsProps {
  form: UseFormReturn<InventoryListTypes>;
  optionLoading: boolean;
  qualityTypeLoading: boolean;
  purchaseDateLoading: boolean;
  qualityTypesList: any;
  storeLocationList: any;
  purchaseDateOptions: any;
  toggleDelete: (id: number | null, index: number | null) => void;
  useMultiloc?: boolean;
}

/**
 * Custom hook for generating inventory list table columns with form integration
 * Handles complex form validation relationships between fields
 */
export const useInventoryListColumns = ({
  form,
  toggleDelete,
  optionLoading,
  storeLocationList,
  qualityTypesList,
  qualityTypeLoading,
  purchaseDateOptions,
  purchaseDateLoading,
  useMultiloc,
}: UseItemListColumnsProps) => {
  // Memoized columns configuration to prevent unnecessary re-renders

  return useMemo(
    () => [
      {
        accessorKey: 'storeLocationName',
        header: 'Location',
        size: 200,
        cell: ({ row }: any) => {
          return (
            <SelectWidget
              placeholder="Location"
              name={`inventory.${row.index}.storeLocationId`}
              form={form}
              optionsList={storeLocationList}
              isClearable={false}
              validation={TEXT_VALIDATION_RULE}
              isLoading={optionLoading}
              disabled={!useMultiloc}
            />
          );
        },
      },
      {
        accessorKey: 'serialNo',
        header: 'Serial #',
        size: 110,
        cell: ({ row }: any) => {
          const index = row.index;
          const quality = form.watch(`inventory.${row.index}.qualityId`);

          const handleSerialNoChange = (value: string) => {
            if (!value && form.formState.errors.inventory?.[index]?.qualityId) {
              form.clearErrors(`inventory[${index}].qualityId` as any);
            } else if (!quality && value) {
              form.setError(`inventory.${index}.qualityId`, {
                message: 'Required',
              });
            }
          };

          return (
            <InputField
              pClassName="w-[120px]"
              form={form}
              name={`inventory.${row.index}.serialNo`}
              placeholder="Serial #"
              onChange={(event) => handleSerialNoChange(event.target.value)}
              maxLength={64}
            />
          );
        },
      },
      {
        accessorKey: 'qualitydesc',
        header: 'Quality',
        size: 120,
        cell: ({ row }: any) => {
          const serialNo = form.watch(`inventory.${row.index}.serialNo`);

          const handleQualityChange = (value: string) => {
            if (
              value &&
              serialNo &&
              form.formState.errors.inventory?.[row.index]?.qualityId
            ) {
              form.clearErrors(`inventory[${row.index}].qualityId` as any);
            }
          };
          return (
            <SelectWidget
              form={form}
              className="min-w-[140px]"
              optionsList={qualityTypesList}
              name={`inventory.${row.index}.qualityId`}
              placeholder="Select Quality"
              isLoading={qualityTypeLoading}
              onSelectChange={(value) => handleQualityChange(value)}
              validation={serialNo ? TEXT_VALIDATION_RULE : {}}
            />
          );
        },
      },
      {
        accessorKey: 'quantity',
        header: 'Quantity',
        size: 80,
        cell: ({ row }) => {
          return (
            <NumberInputField
              form={form}
              name={`inventory.${row.index}.quantity`}
              decimalScale={0}
              pClassName="min-w-[80px]"
              maxLength={5}
              onValueChange={(value) =>
                form.setValue(`inventory.${row.index}.quantity`, value)
              }
            />
          );
        },
      },
      {
        accessorKey: 'purchaseDateSequenceVal',
        header: 'Purchase Date',
        size: 130,
        cell: ({ row }) => (
          <SelectWidget
            returnOptionAsObject
            form={form}
            className="min-w-[160px]"
            optionsList={purchaseDateOptions}
            name={`inventory.${row.index}.purchaseDateSequenceVal`}
            placeholder="Select Purchase Date"
            isLoading={purchaseDateLoading}
            isClearable={false}
          />
        ),
      },
      {
        size: 50,
        id: 'action',
        header: 'Actions',
        cell: ({ row }: any) => (
          <ActionColumnMenu
            onDelete={() => toggleDelete(row.original.itemId, row.index)}
          />
        ),
      },
    ],
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      form,
      optionLoading,
      qualityTypeLoading,
      purchaseDateOptions,
      purchaseDateLoading,
      toggleDelete,
      useMultiloc,
    ]
  );
};
