import { useCallback, useEffect, useMemo, useState } from 'react';
import { useFieldArray, UseFormReturn } from 'react-hook-form';

// Components
import AppButton from '@/components/common/app-button';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import DataTable from '@/components/common/data-tables';

// Hooks & Utilities
import useOptionList from '@/hooks/useOptionList';
import { getQueryParam } from '@/lib/utils';
import {
  useDeleteInventoryMutation,
  useGetInventoryListQuery,
  useGetPurchaseDateSequenceQuery,
  useValidateInventoryQuery,
} from '@/redux/features/items/item.api';

// Types & Constants
import {
  QUALITY_TYPES_API_ROUTES,
  STORE_OPTIONS_API_ROUTES,
} from '@/constants/api-constants';
import { InventoryListTypes } from '@/types/item.types';
import { useInventoryListColumns } from './useInventoryListColumns';
import WarningBanner from '../../../../common/warning/WarningBanner';
import { useGetCompanyQuery } from '@/redux/features/company/company.api';

interface DeleteDialogState {
  isOpen: boolean;
  itemId: number | null;
  index: number | null;
}

const initialDeleteDialogState: DeleteDialogState = {
  isOpen: false,
  itemId: null,
  index: null,
};

interface InventoryProps {
  form: UseFormReturn<InventoryListTypes>;
  onDefaultValuesLoaded?: (defaults: InventoryListTypes) => void;
}

const Inventory = ({ form, onDefaultValuesLoaded }: InventoryProps) => {
  const id = getQueryParam('id') as string;
  const [deleteDialogState, setDeleteDialogState] = useState<DeleteDialogState>(
    initialDeleteDialogState
  );
  const [inventoryWarningMessage, setInventoryWarningMessage] = useState('');

  const { data: companyData } = useGetCompanyQuery();
  const [deleteItem, { isLoading: isDeleteLoading }] =
    useDeleteInventoryMutation();
  const { data: validateInventoryQuantityData } = useValidateInventoryQuery(id);

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'inventory',
  });

  const { data, isLoading } = useGetInventoryListQuery(id);
  const { data: purchaseDate, isLoading: purchaseDateLoading } =
    useGetPurchaseDateSequenceQuery(id);

  // Data hooks for purchase dates and store locations
  const purchaseDateOptions = useMemo(() => {
    return [
      {
        label: '',
        value: '',
        purchaseDate: null,
        sequenceNumber: null,
      },
      ...(purchaseDate?.data?.map((item: any) => ({
        label: item.label,
        value: item.value,
        purchaseDate: item.purchaseDate,
        sequenceNumber: item.sequenceNumber,
      })) || []),
    ];
  }, [purchaseDate]);

  const { options: storeLocationList, optionLoading } = useOptionList({
    url: STORE_OPTIONS_API_ROUTES.ALL,
    labelKey: 'locationName',
    valueKey: 'id',
    sortBy: 'locationName',
    extraKey: 'isDefault',
  });
  const { options: qualityTypesList, optionLoading: qualityTypeLoading } =
    useOptionList({
      url: QUALITY_TYPES_API_ROUTES.ALL,
      labelKey: 'qualityDesc',
      valueKey: 'id',
    });

  const defaultStoreLocation = useMemo(() => {
    return storeLocationList?.find((opt: any) => opt.extraKey);
  }, [storeLocationList]);

  // Set default values for the form
  const defaultValues = useMemo(
    () => ({
      inventory: data?.data?.map((item) => ({
        qualityId: item?.qualityId ?? '',
        quantity: item?.quantity?.toString() ?? '0',
        storeLocationId: item?.storeLocationId ?? '',
        serialNo: item?.serialNo ?? '',
        id: item.id ?? null,
        itemId: item.id,
        purchaseDateSequenceVal: {
          label: item?.purchaseDateSequenceVal ?? '',
          value: item?.purchaseDateSequenceVal ?? '',
          purchaseDate: item?.purchaseDate ?? null,
          sequenceNumber: item?.sequenceNo ?? null,
        },
      })),
    }),
    [data]
  );

  useEffect(() => {
    if (defaultValues) {
      onDefaultValuesLoaded?.(defaultValues as InventoryListTypes);
      form.reset(defaultValues);
    }
  }, [defaultValues, form, onDefaultValuesLoaded]);

  // Validation logic
  const validateOnMount = useCallback(async () => {
    if (validateInventoryQuantityData?.message) {
      setInventoryWarningMessage(validateInventoryQuantityData?.message);
    }
  }, [setInventoryWarningMessage, validateInventoryQuantityData?.message]);

  useEffect(() => {
    if (id && validateInventoryQuantityData) {
      validateOnMount();
    }

    return () => {
      setInventoryWarningMessage('');
    };
  }, [
    id,
    setInventoryWarningMessage,
    validateInventoryQuantityData,
    validateOnMount,
  ]);

  // Handle adding new items
  const handleAddNewItem = useCallback(() => {
    const lastItem =
      form.getValues('inventory')?.[form.getValues('inventory').length - 1];

    if (lastItem?.storeLocationId || fields.length === 0) {
      append({
        itemId: null,
        storeLocationId: !companyData?.data?.useMultiloc
          ? defaultStoreLocation?.value || 1
          : null,
        quantity: '',
        qualityId: null,
        serialNo: null,
        purchaseDateSequenceVal: null,
      });
    }
  }, [
    append,
    companyData?.data?.useMultiloc,
    defaultStoreLocation,
    fields.length,
    form,
  ]);

  // Toggle delete dialog state
  const toggleDelete = useCallback(
    (id: number | null, index: number | null) => {
      setDeleteDialogState((prev) => ({
        isOpen: !prev.isOpen,
        itemId: id,
        index,
      }));
    },
    []
  );

  // Handle deleting an item
  const handleDeleteItem = async () => {
    if (deleteDialogState.itemId) {
      await deleteItem(deleteDialogState.itemId);
    } else if (deleteDialogState.index !== null) {
      remove(deleteDialogState.index);
    }
    setDeleteDialogState(initialDeleteDialogState);
  };

  // Columns configuration for data table
  const columns = useInventoryListColumns({
    form,
    optionLoading,
    purchaseDateLoading,
    purchaseDateOptions,
    qualityTypeLoading,
    qualityTypesList,
    storeLocationList,
    toggleDelete,
    useMultiloc: companyData?.data?.useMultiloc,
  });

  return (
    <div>
      <div className="flex flex-col gap-2">
        {inventoryWarningMessage && (
          <WarningBanner message={inventoryWarningMessage} />
        )}
        {/* Inventory Table */}
        <DataTable
          isLoading={isLoading}
          heading="Inventory List"
          data={fields ?? []}
          columns={columns}
          enablePagination={false}
          tableClassName="max-h-[580px] overflow-auto"
        />
      </div>

      <div className="w-full p-3 border-grayScale-20 border-b border-x rounded-b-md">
        <AppButton
          label="+ Add New"
          className="bg-brand-teal-Default hover:bg-brand-teal-secondary"
          onClick={handleAddNewItem}
        />
      </div>
      <AppConfirmationModal
        description={<div>Are you sure you want to delete this record?</div>}
        open={deleteDialogState.isOpen}
        handleCancel={() => setDeleteDialogState(initialDeleteDialogState)}
        handleSubmit={handleDeleteItem}
        disabled={isDeleteLoading}
        isLoading={isDeleteLoading}
      />
    </div>
  );
};

export default Inventory;
