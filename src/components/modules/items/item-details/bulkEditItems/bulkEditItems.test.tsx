import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import BulkEditItems from './index';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import {
  useBulkEditItemMutation,
  useGetBulkItemsQuery,
} from '@/redux/features/items/item.api';
import { useGetAllMutation } from '@/redux/features/common-api/common.api';
import { vi, expect, describe, beforeEach, afterEach, it } from 'vitest';

// Mock the Redux store
const mockStore = configureStore({
  reducer: {
    bulkItem: () => ({
      filters: [],
    }),
  },
});

// Mock the API hooks
vi.mock('@/redux/features/items/item.api', () => ({
  useBulkEditItemMutation: vi.fn(),
  useGetBulkItemsQuery: vi.fn(),
}));

vi.mock('@/redux/features/common-api/common.api', () => ({
  useGetAllMutation: vi.fn(),
}));

// Mock the DataTable component
vi.mock('@/components/common/data-tables', () => ({
  default: ({ data, columns }: any) => (
    <table>
      <thead>
        <tr>
          {columns.map((col: any) => (
            <th key={col.accessorKey}>{col.header}</th>
          ))}
        </tr>
      </thead>
      <tbody>
        {data.map((row: any, index: number) => (
          <tr key={index}>
            {columns.map((col: any) => (
              <td key={col.accessorKey}>
                {col.cell
                  ? col.cell({ row: { original: row, index } })
                  : row[col.accessorKey]}
              </td>
            ))}
          </tr>
        ))}
      </tbody>
    </table>
  ),
}));

describe('BulkEditItems', () => {
  const mockOnOpenChange = vi.fn();
  const mockOnClickSubmit = vi.fn();

  beforeEach(() => {
    (useBulkEditItemMutation as any).mockReturnValue([
      vi.fn(),
      { isLoading: false },
    ]);
    (useGetAllMutation as any).mockReturnValue([
      vi.fn(),
      { data: { data: [], pagination: { totalCount: 0 } } },
      { isLoading: false },
    ]);
    (useGetBulkItemsQuery as any).mockReturnValue([
      vi.fn(),
      { isLoading: false },
    ]);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('renders the component and displays the form', async () => {
    render(
      <Provider store={mockStore}>
        <BulkEditItems
          onOpenChange={mockOnOpenChange}
          onClickSubmit={mockOnClickSubmit}
        />
      </Provider>
    );

    expect(
      screen.getByLabelText('Quantity Adjustment Description')
    ).toBeInTheDocument();
    expect(screen.getByText('Cost Update')).toBeInTheDocument();
    expect(screen.getByText('Submit')).toBeInTheDocument();
    expect(screen.getByText('Cancel')).toBeInTheDocument();
  });

  it('handles form submission', async () => {
    const mockBulkEditItem = vi
      .fn()
      .mockResolvedValue({ data: { statusCode: 200 } });
    (useBulkEditItemMutation as any).mockReturnValue([
      mockBulkEditItem,
      { isLoading: false },
    ]);

    render(
      <Provider store={mockStore}>
        <BulkEditItems
          onOpenChange={mockOnOpenChange}
          onClickSubmit={mockOnClickSubmit}
        />
      </Provider>
    );

    fireEvent.change(screen.getByLabelText('Quantity Adjustment Description'), {
      target: { value: 'Test Description' },
    });

    fireEvent.click(screen.getByText('Submit'));

    await waitFor(() => {
      expect(mockBulkEditItem);
      expect(mockOnClickSubmit);
    });
  });

  it('handles cancel button click', () => {
    render(
      <Provider store={mockStore}>
        <BulkEditItems
          onOpenChange={mockOnOpenChange}
          onClickSubmit={mockOnClickSubmit}
        />
      </Provider>
    );

    fireEvent.click(screen.getByText('Cancel'));

    expect(mockOnOpenChange).toHaveBeenCalled();
  });

  it('disables submit button when form is not modified', () => {
    render(
      <Provider store={mockStore}>
        <BulkEditItems
          onOpenChange={mockOnOpenChange}
          onClickSubmit={mockOnClickSubmit}
        />
      </Provider>
    );

    expect(screen.getByText('Submit'));
  });

  it('enables submit button when form is modified', async () => {
    render(
      <Provider store={mockStore}>
        <BulkEditItems
          onOpenChange={mockOnOpenChange}
          onClickSubmit={mockOnClickSubmit}
        />
      </Provider>
    );

    fireEvent.change(screen.getByLabelText('Quantity Adjustment Description'), {
      target: { value: 'Test Description' },
    });

    expect(screen.getByText('Submit')).toBeEnabled();
  });
});
