import AppButton from '@/components/common/app-button';
import Input<PERSON>ield from '@/components/forms/input-field';
import MultiCheckboxDropdown from '@/components/forms/MulitCheckbox';
import NumberInputField from '@/components/forms/number-input-field';
import SelectWidget from '@/components/forms/select';
import { Separator } from '@/components/ui/separator';
import { CATEGORY_API_ROUTES } from '@/constants/api-constants';
import { statusList } from '@/constants/common-constants';
import useOptionList from '@/hooks/useOptionList';
import {
  clearAllItemFilters,
  setItemFilter,
  updateItemFormValues,
} from '@/redux/features/items/bulkItemSlice';
import { RootState } from '@/redux/store';
import { useCallback, useEffect } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';

// Define the interface for filter data
interface FilterFormValues {
  [key: string]: string | null | number | any | string[]; // Allow any string key with a string value
}

const defaultValues: FilterFormValues = {
  isActive: '',
  category: [],
  itemId: '',
  description: '',
  location: '',
  sequenceNo: '',
  quantity: '',
};

interface FilterFormValuesFilter {
  label: string;
  value: string;
  name: string;
  operator: string;
  tagValue?: string;
}

interface FilterProps {
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const BulkItemsFilter = ({ setOpen }: FilterProps) => {
  const dispatch = useDispatch();
  const formValuesFromRedux = useSelector(
    (state: RootState) => state.bulkItem.formValues
  );

  const form = useForm<FilterFormValues>({
    defaultValues: formValuesFromRedux,
    mode: 'onChange',
  });

  // Watch form changes and update Redux state
  useEffect(() => {
    const subscription = form.watch((values) => {
      dispatch(updateItemFormValues(values));
    });

    return () => subscription.unsubscribe();
  }, [form, dispatch]);

  const handleClear = useCallback(() => {
    form.reset(defaultValues);
    dispatch(clearAllItemFilters());
    setOpen(false);
  }, [dispatch, form, setOpen]);

  const onSubmit: SubmitHandler<FilterFormValues> = (data) => {
    const newFilterData = [
      {
        label: 'Status',
        value: data?.isActive,
        name: 'isActive',
        tagValue:
          data?.isActive === 'true'
            ? 'Active'
            : data?.isActive === 'false'
              ? 'Inactive'
              : '',
        operator: 'Equals',
      },
      {
        label: 'Category',
        value: data?.category?.toString(),
        name: 'category',
        tagValue: Array.isArray(data?.category)
          ? data?.category?.join(', ')
          : '',
        operator: 'Contains',
      },
      {
        label: 'Item ID',
        value: data?.itemId,
        name: 'itemId',
        tagValue: data?.itemId,
        operator: 'Contains',
      },

      {
        label: 'Description',
        value: data?.description,
        name: 'description',
        tagValue: data?.description,
        operator: 'Contains',
      },
      {
        label: 'Location',
        value: data?.location,
        name: 'location',
        tagValue: data?.location,
        operator: 'Contains',
      },
      {
        label: 'Seq #',
        value: data?.sequenceNo,
        name: 'sequenceNo',
        tagValue: data?.sequenceNo,
        operator: 'Equals',
      },
      {
        label: 'Qty Owned',
        value: data?.quantity,
        name: 'quantity',
        tagValue: data?.quantity,
        operator: 'Equals',
      },
    ]?.filter((item) => item?.value);

    dispatch(setItemFilter(newFilterData as FilterFormValuesFilter[]));
    setOpen(false);
  };

  const { options: categoryList, optionLoading } = useOptionList({
    url: CATEGORY_API_ROUTES.ALL,
    valueKey: 'catDesc',
    labelKey: 'catDesc',
    sortBy: 'catDesc',
  });

  // Track if form values are different from the default values
  const isFormModified =
    JSON.stringify(form.watch()) !== JSON.stringify(defaultValues);

  // const filterData = [];
  // const SavedFilters = useCallback(() => {
  //   if (!filterData?.length) {
  //     return (
  //       <div className="grid justify-center h-full items-center text-text-neutral-tertiary">
  //         <div>
  //           <SearchIcon className="w-4 h-4 ms-14 mb-1" />
  //           No saved filters yet
  //         </div>
  //       </div>
  //     );
  //   }
  // }, [filterData?.length]);

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const uppercaseValue = event.target.value?.toUpperCase();
    form.setValue('itemId', uppercaseValue, { shouldValidate: true });
  };

  return (
    <div className="p-3 gap-5">
      <div>
        <div className="text-normal py-2 font-semibold">Filters</div>
        <Separator
          orientation="horizontal"
          className="h-[1px] bg-border-Default mb-2"
        />
        <div className="grid col-span-1 md:grid-cols-2 gap-3">
          <SelectWidget
            form={form}
            name="isActive"
            label="Status"
            placeholder="Select Status"
            optionsList={statusList}
            menuPosition="absolute"
          />
          <MultiCheckboxDropdown
            name="category"
            form={form}
            optionsList={categoryList ?? []}
            placeholder={'Select Categories'}
            label="Categories"
            className="w-full"
            isLoading={optionLoading}
          />

          <InputField
            form={form}
            name="itemId"
            label="Item ID"
            placeholder="Enter Item ID"
            onChange={handleChange}
            maxLength={32}
          />
          <InputField
            form={form}
            name="description"
            label="Description"
            placeholder="Enter Description"
            maxLength={128}
          />
          <div className="col-span-2">
            <InputField
              form={form}
              name="location"
              label="Location"
              placeholder="Enter Location"
              maxLength={64}
            />
          </div>

          <NumberInputField
            form={form}
            name="sequenceNo"
            label="Seq #"
            placeholder="Enter Seq #"
            maxLength={6}
          />
          <NumberInputField
            form={form}
            name="quantity"
            label="Qty Owned"
            placeholder="Enter Qty Owned"
            maxLength={10}
          />
        </div>
      </div>
      {/* <div>
        <div className="text-normal py-2  font-semibold">Saved Filters</div>
        <Separator
          orientation="horizontal"
          className="h-[1px] bg-border-Default mb-2"
        />
        {<SavedFilters />}
      </div> */}
      <div className="col-span-2 mt-2 gap-5 border-t-2 border-Default">
        <div className="flex items-center gap-5 mt-3">
          <AppButton
            className="w-full"
            onClick={form.handleSubmit(onSubmit)}
            label="Apply"
            disabled={!isFormModified}
          />
          <AppButton
            className="w-full"
            label="Clear"
            variant="neutral"
            onClick={handleClear}
            disabled={!isFormModified}
          />
        </div>
        {/* <AppButton
          className="mt-3"
          onClick={form.handleSubmit(onSubmit)}
          label="Save Current Filter"
          // disabled={!isFormModified}
          disabled
          variant="neutral"
        /> */}
      </div>
    </div>
  );
};

export default BulkItemsFilter;
