import { render, screen, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import Filter from './Filter';
import { describe, expect, it, beforeEach, vi } from 'vitest';
import itemReducer from '@/redux/features/items/itemSlice';
import { commonApi } from '@/redux/features/common-api/common.api';

describe('Filter Component', () => {
  let setIsFilterOpenMock: any;
  const mockStore = configureStore({
    reducer: {
      bulkItem: itemReducer,
      [commonApi.reducerPath]: commonApi.reducer,
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware().concat(commonApi.middleware),
  });

  beforeEach(() => {
    setIsFilterOpenMock = vi.fn();
  });

  it('should render the filter form', () => {
    render(
      <Provider store={mockStore}>
        <Filter setOpen={setIsFilterOpenMock} />
      </Provider>
    );
    expect(screen.getByText('Filters')).toBeInTheDocument();
    expect(screen.getByText('Item ID')).toBeInTheDocument();
    expect(screen.getByText('Description')).toBeInTheDocument();
    expect(screen.getByText('Categories')).toBeInTheDocument();
    expect(screen.getByText('Location')).toBeInTheDocument();
  });

  it('should submit the form and dispatch the correct action', () => {
    render(
      <Provider store={mockStore}>
        <Filter setOpen={setIsFilterOpenMock} />
      </Provider>
    );
    fireEvent.change(screen.getByLabelText('Item ID'), {
      target: { value: '123' },
    });
    fireEvent.change(screen.getByLabelText('Description'), {
      target: { value: 'Test Description' },
    });
    fireEvent.change(screen.getByLabelText('Location'), {
      target: { value: 'Test Location' },
    });
    const actions = fireEvent.click(screen.getByText('Apply'));
    expect(actions);
  });

  it('should clear the form and dispatch the correct action', () => {
    render(
      <Provider store={mockStore}>
        <Filter setOpen={setIsFilterOpenMock} />
      </Provider>
    );
    fireEvent.change(screen.getByLabelText('Item ID'), {
      target: { value: '123' },
    });
    fireEvent.change(screen.getByLabelText('Description'), {
      target: { value: 'Test Description' },
    });
    fireEvent.click(screen.getByText('Clear'));
    expect(screen.getByLabelText('Item ID')).toHaveValue('');
    expect(screen.getByLabelText('Description')).toHaveValue('');
    expect(setIsFilterOpenMock).toHaveBeenCalledWith(false);
  });

  it('should disable the Apply and Clear buttons when the form is not modified', () => {
    render(
      <Provider store={mockStore}>
        <Filter setOpen={setIsFilterOpenMock} />
      </Provider>
    );
    expect(screen.getByText('Apply'));
    expect(screen.getByText('Clear'));
  });

  it('should enable the Apply and Clear buttons when the form is modified', () => {
    render(
      <Provider store={mockStore}>
        <Filter setOpen={setIsFilterOpenMock} />
      </Provider>
    );
    fireEvent.change(screen.getByLabelText('Item ID'), {
      target: { value: '123' },
    });
    expect(screen.getByText('Apply')).not.toBeDisabled();
    expect(screen.getByText('Clear')).not.toBeDisabled();
  });
});
