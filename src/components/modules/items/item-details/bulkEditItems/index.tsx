import AppButton from '@/components/common/app-button';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import DataTable from '@/components/common/data-tables';
import InputField from '@/components/forms/input-field';
import NumberInputField from '@/components/forms/number-input-field';
import RadioField from '@/components/forms/radio-field';
import { BulkEditItemsCost } from '@/constants/common-constants';
import { convertToFloat, getPaginationObject } from '@/lib/utils';
import { clearItemFilter } from '@/redux/features/items/bulkItemSlice';
import {
  useBulkEditItemMutation,
  useGetBulkItemsQuery,
} from '@/redux/features/items/item.api';
import { RootState } from '@/redux/store';
import { PaginationType, SortingStateType } from '@/types/common.types';
import { BulkEditItemType, BulkItemList } from '@/types/item.types';
import { ColumnDef, RowSelectionState } from '@tanstack/react-table';
import isEqual from 'lodash/isEqual';
import { memo, useCallback, useEffect, useMemo, useState } from 'react';
import {
  SubmitHandler,
  useFieldArray,
  useForm,
  UseFormReturn,
} from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import BulkItemsFilter from './Filter';
interface BulkEditItemsType {
  onOpenChange?: () => void;
  onClickSubmit?: () => void;
}

const BulkEditItems = ({ onOpenChange, onClickSubmit }: BulkEditItemsType) => {
  const dispatch = useDispatch();
  const [isFilterOpen, setIsFilterOpen] = useState<boolean>(false);
  const [isUpdateAll, setIsUpdateAll] = useState<boolean>(false);
  const [search, setSearch] = useState<string>('');
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const [pagination, setPagination] = useState<PaginationType>({
    pageSize: 10,
    pageIndex: 0,
  });
  const [sorting, setSorting] = useState<SortingStateType[]>([
    { id: 'itemId', desc: true },
  ]);
  const [isFetchingMore, setIsFetchingMore] = useState<boolean>(false);

  const filter = useSelector((state: RootState) => state.bulkItem.filters);

  // bulk edit item
  const [bulkEditItem, { isLoading: bulkLoading }] = useBulkEditItemMutation();

  const payload = useMemo(() => {
    const normalizedFilters = Array.isArray(filter)
      ? filter.map((f: any) => ({
          field: f.name || f.label,
          value: f.value,
          operator: f.operator,
        }))
      : [];

    return getPaginationObject({
      pagination: pagination,
      sorting: sorting,
      filters: [
        ...normalizedFilters,
        { field: 'filter', value: search, operator: 'Contains' },
      ],
    });
  }, [filter, pagination, search, sorting]);

  // Item bulk list
  const {
    data: getAllData,
    isFetching: isLoading,
    refetch,
    isFetching,
    isSuccess,
  } = useGetBulkItemsQuery(payload);

  const ItemList = useMemo(
    () => (isSuccess && getAllData?.data ? getAllData?.data : []),
    [getAllData?.data, isSuccess]
  );

  const defaultValues = useMemo(() => {
    return {
      quantity: '',
      costUpdate: 'single',
      items:
        ItemList?.map((item: BulkItemList) => ({
          id: item?.id,
          bulkId: item?.id,
          itemId: item?.itemId,
          location: item?.location || '',
          description: item?.description || '',
          sequenceNo: item?.sequenceNo || '',
          quantity: item?.quantity || '',
          unitPrice: item?.unitPrice || '',
          replacementCharge: item?.replacementCharge || '',
          latestCost: item?.latestCost || '',
        })) || [],
    };
  }, [ItemList]);

  const form: UseFormReturn<BulkEditItemType> = useForm<BulkEditItemType>({
    defaultValues,
    mode: 'onChange',
  });

  const { fields } = useFieldArray<BulkEditItemType>({
    control: form.control,
    name: 'items',
  });

  useEffect(() => {
    if (defaultValues) {
      form.reset(defaultValues);
    }
  }, [defaultValues, form]);

  const SelectedRowId = useMemo(
    () => Object.keys(rowSelection)?.map((id) => Number(id)),
    [rowSelection]
  );

  // on submit bulk edit
  const onSubmit: SubmitHandler<any> = useCallback(
    async (formData) => {
      try {
        const payload = formData?.items
          ?.filter((item: BulkItemList) =>
            SelectedRowId?.includes(item?.bulkId)
          )
          ?.map(({ bulkId, ...item }: BulkItemList) => ({
            ...item,
            sequenceNo: item?.sequenceNo || null,
            quantity: item?.quantity || null,
            unitPrice: item?.unitPrice || null,
            replacementCharge: item?.replacementCharge || null,
            latestCost: item?.latestCost || null,
          }));

        const { data } = await bulkEditItem({ body: payload });
        if (data && data?.statusCode === 200) {
          onClickSubmit?.();
          setRowSelection({});
          // fetchAllitemList({ search, filter });
          refetch();
        }
      } catch (error) {
        // Handle error (could display error message to user)
      }
    },
    [SelectedRowId, bulkEditItem, onClickSubmit, refetch]
  );

  // Update all costs info dialog open
  const toggleIsUpdateAll = useCallback(() => {
    setIsUpdateAll((pre) => !pre);
  }, []);

  const handleCancelUpdateAllCosts = useCallback(() => {
    form.setValue('costUpdate', 'single');
    toggleIsUpdateAll();
  }, [form, toggleIsUpdateAll]);

  const columns: ColumnDef<BulkItemList>[] = useMemo(() => {
    const isEdit = (id: number) => SelectedRowId?.includes(id);
    return [
      {
        accessorKey: 'itemId',
        header: 'Item ID',
        size: 150,
        enableSorting: true,
      },
      {
        accessorKey: 'description',
        header: 'Description',
        enableSorting: true,
        maxSize: 250,
        cell: ({ row }) => {
          return isEdit(row?.original?.bulkId) ? (
            <InputField
              name={`items.${row.index}.description`}
              form={form}
              placeholder="Description"
              maxLength={128}
              pClassName="p-1"
            />
          ) : (
            row?.original?.description
          );
        },
      },
      {
        accessorKey: 'location',
        header: 'Location',
        maxSize: 200,
        enableSorting: true,
        cell: ({ row }) => {
          return isEdit(row?.original?.bulkId) ? (
            <InputField
              name={`items.${row.index}.location`}
              form={form}
              placeholder="Location"
              maxLength={64}
              pClassName="p-1"
            />
          ) : (
            row?.original?.location
          );
        },
      },
      {
        accessorKey: 'sequenceNo',
        header: 'Sequence #',
        size: 150,
        enableSorting: true,
        cell: ({ row }) => {
          return isEdit(row?.original?.bulkId) ? (
            <NumberInputField
              name={`items.${row.index}.sequenceNo`}
              form={form}
              placeholder="Sequence #"
              maxLength={6}
              pClassName="p-1"
            />
          ) : (
            row?.original?.sequenceNo
          );
        },
      },
      {
        accessorKey: 'quantity',
        header: 'Quantity Owned',
        size: 180,
        enableSorting: true,
        cell: ({ row }) => row?.original?.quantity,
      },
      {
        accessorKey: 'unitPrice',
        header: 'Unit Price',
        size: 140,
        enableSorting: true,
        cell: ({ row }) => {
          return isEdit(row?.original?.bulkId) ? (
            <NumberInputField
              name={`items.${row.index}.unitPrice`}
              form={form}
              placeholder="Unit Price"
              maxLength={10}
              fixedDecimalScale
              prefix="$"
              pClassName="p-1"
            />
          ) : (
            convertToFloat({ value: row?.original?.unitPrice, prefix: '$' })
          );
        },
      },
      {
        accessorKey: 'replacementCharge',
        header: 'Replacement Charge',
        size: 220,
        enableSorting: true,
        cell: ({ row }) => {
          return isEdit(row?.original?.bulkId) ? (
            <NumberInputField
              name={`items.${row.index}.replacementCharge`}
              form={form}
              placeholder="Replacement Charge"
              maxLength={10}
              fixedDecimalScale
              prefix="$"
              pClassName="p-1"
            />
          ) : (
            convertToFloat({
              value: row?.original?.replacementCharge,
              prefix: '$',
            })
          );
        },
      },
      {
        accessorKey: 'latestCost',
        header: 'Latest Cost',
        size: 150,
        enableSorting: true,
        cell: ({ row }) => {
          return isEdit(row?.original?.bulkId) ? (
            <NumberInputField
              name={`items.${row.index}.latestCost`}
              form={form}
              placeholder="Latest Cost"
              maxLength={10}
              fixedDecimalScale
              prefix="$"
              pClassName="p-1"
            />
          ) : (
            convertToFloat({
              value: row?.original?.latestCost,
              prefix: '$',
            })
          );
        },
      },
    ];
  }, [SelectedRowId, form]);

  // handle Clear function
  const handleClearFilter = useCallback(
    (key: string) => {
      dispatch(clearItemFilter(key));
    },
    [dispatch]
  );

  useEffect(() => {
    // Skip resetting if no row selection change
    if (!rowSelection) return;

    // Get the current form values
    const currentItems = form.getValues('items');

    // Map through the items and reset only those that are not selected
    const updatedItems = currentItems.map((item) => {
      // Check if the row is selected based on its bulkId
      if (SelectedRowId?.includes(item.bulkId)) {
        return item; // Keep selected row's values intact
      }

      // For unselected rows, reset values to default ones
      const defaultItem: any =
        defaultValues.items.find(
          (i: BulkItemList) => i.bulkId === item.bulkId
        ) || {};
      return {
        ...item,
        quantity: defaultItem.quantity || '',
        unitPrice: defaultItem.unitPrice || '',
        replacementCharge: defaultItem.replacementCharge || '',
        latestCost: defaultItem.latestCost || '',
        sequenceNo: defaultItem.sequenceNo || '',
        description: defaultItem.description || '',
        location: defaultItem.location || '',
      };
    });

    // Update the form with the modified items
    form.setValue('items', updatedItems);
  }, [rowSelection, form, defaultValues.items, SelectedRowId]);

  // Track if form values are different from the default values
  const isFormModified = !isEqual(form.watch('items'), defaultValues?.items);

  const fetchMore = useCallback(() => {
    if (isFetchingMore) return; // Avoid duplicate calls

    setIsFetchingMore(true); // Set flag to prevent further calls
    setPagination((prev) => ({ ...prev, pageSize: prev.pageSize + 10 }));
  }, [isFetchingMore]);

  // Reset `isFetchingMore` only after new data arrives
  useEffect(() => {
    if (!isFetching) {
      setIsFetchingMore(false);
    }
  }, [isFetching]);

  return (
    <div className="px-5">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-5 items-center p-2 w-full border-[1px] border-border-Default rounded-md mb-2">
        <InputField
          form={form}
          name="quantity"
          label="Quantity Adjustment Description"
          placeholder="Quantity Adjustment Description"
        />
        <RadioField
          form={form}
          name="costUpdate"
          label="Cost Update"
          options={BulkEditItemsCost}
          optionsPerRow={2}
          onChange={(value) => value === 'all' && toggleIsUpdateAll()}
        />
      </div>

      <DataTable
        columns={columns}
        data={fields || []}
        totalItems={getAllData?.pagination?.totalCount}
        isLoading={isLoading}
        heading=" "
        enableSearch
        search={search}
        setSearch={setSearch}
        enableFilter
        filter={filter}
        sorting={sorting}
        setSorting={setSorting}
        customToolBar
        handleClearFilter={handleClearFilter}
        filterClassName="w-[673px]"
        filterContent={<BulkItemsFilter setOpen={setIsFilterOpen} />}
        filterSide="right"
        setIsFilterOpen={setIsFilterOpen}
        enablePagination={false}
        isFilterOpen={isFilterOpen}
        tableClassName="max-h-[230px] 2xl:max-h-[400px] overflow-auto"
        enableRowSelection
        enableMultiRowSelection
        rowSelection={rowSelection}
        onRowSelectionChange={setRowSelection}
        bindingKey="bulkId"
        isInfiniteScroll
        fetchMore={fetchMore}
      />
      <div className="flex justify-end mt-4 2xl:mt-0 2xl:absolute bottom-4 right-6 z-10 bg-white  space-x-4">
        <AppButton
          label="Submit"
          isLoading={bulkLoading}
          onClick={form.handleSubmit(onSubmit)}
          disabled={!isFormModified}
          className="w-40"
        />
        <AppButton
          label="Cancel"
          onClick={onOpenChange}
          variant="neutral"
          className="w-40"
        />
      </div>

      <AppConfirmationModal
        description={
          <div>
            This option will change all historical cost records for any item
            where you change the latest cost value on this screen. This is not a
            normal operation, so do not continue unless you are certain this is
            the option you want to take. Do you want to continue and modify your
            historical cost records?
          </div>
        }
        open={isUpdateAll}
        handleCancel={handleCancelUpdateAllCosts}
        handleSubmit={toggleIsUpdateAll}
      />
    </div>
  );
};

export default memo(BulkEditItems);
