import AppButton from '@/components/common/app-button';
import Input<PERSON>ield from '@/components/forms/input-field';
import MultiCheckboxDropdown from '@/components/forms/MulitCheckbox';
import NumberInputField from '@/components/forms/number-input-field';
import { Separator } from '@/components/ui/separator';
import { CATEGORY_API_ROUTES } from '@/constants/api-constants';
import useOptionList from '@/hooks/useOptionList';
import {
  clearAllItemFilters,
  setItemFilter,
  updateItemFormValues,
} from '@/redux/features/items/itemSlice';
import { RootState } from '@/redux/store';
import { memo, useEffect } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';

// Define the interface for filter data
interface FilterFormValues {
  [key: string]: string | null | number; // Allow any string key with a string value
}

const defaultValues: FilterFormValues = {
  itemId: '',
  category: '',
  description: '',
  location: '',
  unitPrice: '',
  replacementCharge: '',
};

interface FilterProps {
  setIsFilterOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const Filter: React.FC<FilterProps> = ({ setIsFilterOpen }) => {
  const dispatch = useDispatch();
  const formValuesFromRedux = useSelector(
    (state: RootState) => state.item.formValues
  );

  const form = useForm<FilterFormValues>({
    defaultValues: formValuesFromRedux,
    mode: 'onChange',
  });

  // Watch form changes and update Redux state
  useEffect(() => {
    const subscription = form.watch((values) => {
      dispatch(updateItemFormValues(values));
    });

    return () => subscription.unsubscribe();
  }, [form, dispatch]);

  const handleClear = () => {
    form.reset(defaultValues); // Reset form to default values
    dispatch(clearAllItemFilters()); // Clear all filters in Redux
    setIsFilterOpen(false); // Close the filter form
  };

  const onSubmit: SubmitHandler<FilterFormValues> = (data) => {
    const newFilterData: any[] = [
      {
        label: 'Item ID',
        value: data?.itemId,
        name: 'itemId',
        tagValue: data?.itemId,
        operator: 'Contains',
      },

      {
        label: 'Description',
        value: data?.description,
        name: 'description',
        tagValue: data?.description,
        operator: 'Contains',
      },

      {
        label: 'Category',
        value: data?.category?.toString(),
        name: 'category',
        tagValue: Array.isArray(data?.category)
          ? data?.category?.join(', ')
          : '',
        operator: 'Contains',
      },
      {
        label: 'Location',
        value: data?.location,
        name: 'location',
        tagValue: data?.location,
        operator: 'Contains',
      },
      {
        label: 'Unit Price',
        value: data?.unitPrice,
        name: 'unitPrice',
        tagValue: data?.unitPrice,
        operator: 'Equals',
      },
      {
        label: 'Replacement Charge',
        value: data?.replacementCharge,
        name: 'replacementCharge',
        tagValue: data?.replacementCharge,
        operator: 'Equals',
      },
    ].filter((item) => item.value);

    dispatch(setItemFilter(newFilterData));
    setIsFilterOpen(false);
  };

  const { options: categoryList, optionLoading } = useOptionList({
    url: CATEGORY_API_ROUTES.ALL,
    valueKey: 'catDesc',
    labelKey: 'catDesc',
    sortBy: 'catDesc',
  });

  // Track if form values are different from the default values
  const isFormModified =
    JSON.stringify(form.watch()) !== JSON.stringify(defaultValues);

  return (
    <div className="p-3">
      <div className="text-normal py-2  font-semibold">Filters</div>
      <Separator
        orientation="horizontal"
        className="h-[1px] bg-border-Default mb-2"
      />
      <div className="grid col-span-1 md:grid-cols-2 gap-3">
        <InputField
          form={form}
          name="itemId"
          label="Item ID"
          placeholder="Enter Item ID"
        />
        <InputField
          form={form}
          name="description"
          label="Description"
          placeholder="Enter Description"
        />
        <MultiCheckboxDropdown
          name="category"
          form={form}
          optionsList={categoryList || []}
          placeholder={'Select Categories'}
          label="Categories"
          className="w-full"
          isLoading={optionLoading}
        />

        <InputField
          form={form}
          name="location"
          label="Location"
          placeholder="Enter Location"
        />
        <NumberInputField
          form={form}
          name="unitPrice"
          label="Unit Price"
          placeholder="$______.__"
          maxLength={10}
          fixedDecimalScale
          prefix="$"
        />
        <NumberInputField
          form={form}
          name="replacementCharge"
          label="Replacement Charge"
          placeholder="$______.__"
          maxLength={10}
          prefix="$"
          fixedDecimalScale
        />
      </div>
      <div className="w-full h-full flex justify-between gap-3 mt-3 sticky bottom-0 bg-white z-30">
        <AppButton
          className="w-full"
          onClick={form.handleSubmit(onSubmit)}
          label="Apply"
          disabled={!isFormModified}
        />
        <AppButton
          className="w-full"
          label="Clear"
          variant="neutral"
          onClick={handleClear}
          disabled={!isFormModified}
        />
      </div>
    </div>
  );
};

export default memo(Filter);
