/* eslint-disable react-hooks/exhaustive-deps */
import CheveronLeft from '@/assets/icons/CheveronLeft';
import AppButton from '@/components/common/app-button';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import AppSpinner from '@/components/common/app-spinner';
import AppTabsVertical from '@/components/common/app-tabs-vertical';
import IconButton from '@/components/common/icon-button';
import { itemTabList } from '@/constants/item-constants';
import { ROUTES } from '@/constants/routes-constants';
import { getQueryParam, updateQueryParam } from '@/lib/utils';
import { ItemsContext } from '@/pages/items/ItemsContext';
import {
  useGetItemByIdQuery,
  useSaveInventoryMutation,
  useUpdateItemMutation,
} from '@/redux/features/items/item.api';
import {
  AllTabTypeMap,
  InventoryListTypes,
  ItemTabs,
  ItemType,
} from '@/types/item.types';
import { SaveIcon } from 'lucide-react';
import { useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { FormProvider, SubmitHandler, useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import {
  createPayload,
  generateItemsDefaultValues,
  ITEMS_API_ROUTES_MAP,
} from './constants';
import TooltipWidget from '@/components/common/tooltip-widget';
import isEqual from 'lodash/isEqual';
import { getModifiedItems } from '@/lib/getModifiedItems';

const excludeTabs = [ItemTabs.LINKED_FILES, ItemTabs.QUANTITY];

export const AddEditItems = () => {
  const id = getQueryParam('id') as string;
  const tabName = (getQueryParam('tab') ||
    'information') as keyof AllTabTypeMap;
  const navigation = useNavigate();
  const [activeTab, setActiveTab] = useState<keyof AllTabTypeMap>(tabName);

  const { isItemType, setIsItemType, isPackageItem, setIsPackageItem } =
    useContext(ItemsContext);

  const { data: itemData, isLoading: itemIsLoading } = useGetItemByIdQuery(id, {
    skip: !id,
  });

  const [addUpdateItem, { isLoading }] = useUpdateItemMutation();
  const [saveInventory, { isLoading: saveInventoryLoading }] =
    useSaveInventoryMutation();

  const defaultValues = useMemo(() => {
    return generateItemsDefaultValues(itemData?.data || {});
  }, [itemData?.data]);

  // Initialize form hook
  const form = useForm<AllTabTypeMap[typeof activeTab]>({
    defaultValues: defaultValues,
    mode: 'onChange',
  });
  const { handleSubmit, reset } = form;

  // Inventory State and Handlers
  const [inventoryDefaultValues, setInventoryDefaultValues] =
    useState<InventoryListTypes | null>(null);
  const inventoryForm = useForm<InventoryListTypes>();
  const onDefaultValuesLoaded = useCallback((defaults: InventoryListTypes) => {
    setInventoryDefaultValues(defaults);
  }, []);
  const currentInventory = inventoryForm.watch('inventory');
  const isInventoryModified = useMemo(() => {
    return isEqual(currentInventory, inventoryDefaultValues?.inventory);
  }, [
    JSON.stringify(currentInventory),
    JSON.stringify(inventoryDefaultValues?.inventory),
  ]);
  const isInventoryTab =
    activeTab === (ItemTabs.INVENTORY as keyof AllTabTypeMap);
  const isSaveDisabled = isInventoryTab ? isInventoryModified : false;
  const inventorySubmit: SubmitHandler<InventoryListTypes> = async (
    formData
  ) => {
    const currentInventory = formData?.inventory || [];
    const defaultInventory = inventoryDefaultValues?.inventory || [];

    // Extract only modified or new items.
    const modifiedInventoryItems = getModifiedItems(
      currentInventory,
      defaultInventory
    );
    const payload = modifiedInventoryItems?.map((item) => {
      return {
        id: item?.id ?? null,
        storeLocationId: item?.storeLocationId,
        quantity: Number(item.quantity),
        qualityId: Number(item.qualityId) || null,
        purchaseDate: item?.purchaseDateSequenceVal?.purchaseDate || null,
        sequenceNo: item?.purchaseDateSequenceVal?.sequenceNumber || null,
        serialNo: item.serialNo || null,
      };
    });

    await saveInventory({ inventoryData: payload, itemId: id });
  };

  // Reset form when default values or tab changes
  useEffect(() => {
    if (defaultValues) {
      reset(defaultValues);
    }
  }, [defaultValues, activeTab, reset]);

  // Helper function to trigger the correct submit handler based on tab
  const triggerSubmitHandler = (tabName: string) => {
    switch (tabName) {
      case ItemTabs.INVENTORY:
        return () => inventoryForm.handleSubmit(inventorySubmit)();
      default:
        return () => handleSubmit(onSubmit)();
    }
  };

  const onSubmit: SubmitHandler<AllTabTypeMap[typeof activeTab] | any> =
    useCallback(
      async (formData) => {
        try {
          const url = id
            ? ITEMS_API_ROUTES_MAP[activeTab]?.UPDATE
            : ITEMS_API_ROUTES_MAP[activeTab]?.CREATE;

          if (!url) {
            throw new Error('No URL available for the requested operation');
          }

          const payload = createPayload(activeTab, formData);
          const { data } = await addUpdateItem({
            url,
            body: payload,
            method: id ? 'PUT' : 'POST',
          });
          if (isItemType) {
            setIsItemType(false);
          }
          if (isPackageItem) {
            setIsPackageItem(false);
          }
          const itemId = data?.data?.id;
          if (!id && itemId) {
            updateQueryParam(itemId);
          }
        } catch (error) {
          // Handle error (could display error message to user)
        }
      },
      [
        activeTab,
        addUpdateItem,
        id,
        isItemType,
        isPackageItem,
        setIsItemType,
        setIsPackageItem,
      ]
    );

  // handle on change tab
  const handleTabChange = useCallback((value: keyof AllTabTypeMap) => {
    setActiveTab(value);
    updateQueryParam(value, 'tab');
  }, []);

  // navigate to back
  const navigateToItem = useCallback(() => {
    navigation(ROUTES.ITEMS);
  }, [navigation]);

  const itemType = form.watch('itemType');
  const isPackageItemValue = form.watch('itemOption.isPackageItem' as any);
  const isKitType = itemType === ItemType.KIT_ITEM;

  // handle No / Cancel to item type change
  const handleCancelItemType = useCallback(() => {
    form.setValue('itemType', defaultValues?.itemType);
    setIsItemType(false);
  }, [defaultValues?.itemType, form, setIsItemType]);

  // handle No / Cancel Package item change
  const handelConfirmationPackageItem = useCallback(() => {
    form.setValue(
      'itemOption.isPackageItem' as any,
      defaultValues?.itemOption?.isPackageItem
    );
    setIsPackageItem(false);
  }, [defaultValues?.itemOption?.isPackageItem, form, setIsPackageItem]);

  return (
    <FormProvider {...form}>
      <div className="h-[1000px]">
        <div className="flex justify-between items-center px-4 sticky top-16 z-[12] bg-white py-4">
          <div className="flex gap-x-4 items-center">
            <IconButton onClick={navigateToItem}>
              <CheveronLeft />
            </IconButton>
            <h1
              className="text-2xl text-text-tertiary font-semibold hover:cursor-pointer"
              onClick={navigateToItem}
            >
              Items
            </h1>

            <div className="flex items-center gap-3">
              <span className="text-2xl font-semibold text-text-tertiary">
                {' / '}
              </span>
              <p className="text-2xl capitalize font-semibold">
                {id ? defaultValues?.itemId : 'New Item'}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            {!excludeTabs.includes(tabName as ItemTabs) &&
              (isInventoryTab && isSaveDisabled ? (
                <TooltipWidget content="Please add a new item or update an existing item">
                  <div>
                    <AppButton
                      label="Save Detail"
                      icon={SaveIcon}
                      onClick={() => triggerSubmitHandler(activeTab)()}
                      iconClassName="w-4 h-4"
                      isLoading={isLoading || saveInventoryLoading}
                      disabled={isSaveDisabled}
                    />
                  </div>
                </TooltipWidget>
              ) : (
                <AppButton
                  label="Save Detail"
                  icon={SaveIcon}
                  onClick={() => triggerSubmitHandler(activeTab)()}
                  iconClassName="w-4 h-4"
                  isLoading={isLoading || saveInventoryLoading}
                  disabled={isSaveDisabled}
                />
              ))}
            <AppButton
              label="Cancel"
              onClick={navigateToItem}
              variant="neutral"
            />
          </div>
        </div>
        <AppTabsVertical
          tabs={itemTabList({ inventoryForm, onDefaultValuesLoaded })}
          activeTab={tabName}
          onTabChange={handleTabChange}
          showTabMenu={!!id}
          className="px-4 pb-3 "
        />
      </div>
      <AppConfirmationModal
        title={'Confirmation'}
        open={isItemType}
        isLoading={isLoading}
        description={
          isKitType ? (
            <div>
              Are you sure you want to proceed with the{' '}
              <span className="font-semibold">Kit Item</span>?
              <p>
                Click <span className="font-semibold">Yes</span> to save and
                enable the <span className="font-semibold">Kit Item</span>.
              </p>
            </div>
          ) : (
            <div>
              Are you sure you want to proceed with the{' '}
              <span className="font-semibold capitalize">
                {' '}
                {itemType?.replace('_', ' ')?.toLowerCase()}
              </span>
              ? This action will unlink the current{' '}
              <span className="font-semibold">Kit Item</span> details and update
              the item accordingly.
              <p>
                Click <span className="font-semibold">Yes</span> to unlink and
                disable the <span className="font-semibold">Kit Item</span>.
              </p>
            </div>
          )
        }
        handleCancel={handleCancelItemType}
        handleSubmit={handleSubmit(onSubmit)}
      />
      <AppConfirmationModal
        title={'Confirmation'}
        open={isPackageItem}
        isLoading={isLoading}
        description={
          isPackageItemValue ? (
            <div>
              Are you sure you want to proceed with the{' '}
              <span className="font-semibold">Package Item</span>?
              <p>
                Click <span className="font-semibold">Yes</span> to save and
                enable the <span className="font-semibold">Package Item</span>.
              </p>
            </div>
          ) : (
            <div>
              Are you sure you want to proceed with the{' '}
              <span className="font-semibold">Package Item</span>? This action
              will unlink the current{' '}
              <span className="font-semibold">Package Item</span> details and
              update the item accordingly.
              <p>
                Click <span className="font-semibold">Yes</span> to unlink and
                disable the <span className="font-semibold">Package Item</span>.
              </p>
            </div>
          )
        }
        handleCancel={handelConfirmationPackageItem}
        handleSubmit={handleSubmit(onSubmit)}
      />
      <AppSpinner overlay isLoading={itemIsLoading} />
    </FormProvider>
  );
};
