import CopyIcon from '@/assets/icons/CopyIcon';
import EditPencilIcon from '@/assets/icons/EditPencilIcon';
import AppButton from '@/components/common/app-button';
import CustomDialog from '@/components/common/dialog';
import TooltipWidget from '@/components/common/tooltip-widget';
import In<PERSON><PERSON>ield from '@/components/forms/input-field';
import NumberIn<PERSON><PERSON>ield from '@/components/forms/number-input-field';
import SelectWidget from '@/components/forms/select';
import {
  CATEGORY_API_ROUTES,
  VENDORS_API_ROUTES,
} from '@/constants/api-constants';
import { statusList } from '@/constants/common-constants';
import { TEXT_VALIDATION_RULE } from '@/constants/validation-constants';
import useOptionList from '@/hooks/useOptionList';
import { DEFAULT_FORMAT, getQueryParam, updateQueryParam } from '@/lib/utils';
import { ItemsContext } from '@/pages/items/ItemsContext';
import { useGetEnumsListQuery } from '@/redux/features/enums-api/enums-api';
import { useCopyItemMutation } from '@/redux/features/items/item.api';
import {
  CopyItemsType,
  ItemInformationTypes,
  ItemType,
} from '@/types/item.types';
import { useCallback, useContext, useState } from 'react';
import { SubmitHandler, useForm, useFormContext } from 'react-hook-form';
import EditKit from './edit-kit/EditKit';

const InformationSystem = () => {
  const [open, setOpen] = useState(false);
  const [openKit, setOpenKit] = useState(false);
  const [copyItem, { isLoading: copyItemLoading }] = useCopyItemMutation();

  const id = getQueryParam('id') as string;

  const form = useFormContext<ItemInformationTypes>();
  const copyItemForm = useForm<CopyItemsType>();

  const { setIsItemType } = useContext(ItemsContext);

  const { setValue } = form;
  const isKitItem = form.watch('itemType') === ItemType.KIT_ITEM;
  const isDisabled = !id || !isKitItem;

  const { optionLoading: categoryLoading, options: categoryList } =
    useOptionList({
      url: CATEGORY_API_ROUTES.ALL,
      labelKey: 'catDesc',
      valueKey: 'id',
      sortBy: 'catDesc',
    });

  const { optionLoading: vendorLoading, options: vendorList } = useOptionList({
    url: VENDORS_API_ROUTES.ALL,
    labelKey: 'vendorName',
    valueKey: 'id',
    sortBy: 'vendorName',
  });

  const { data: itemType, isLoading: itemTypeLoading } = useGetEnumsListQuery({
    name: 'ItemType',
  });

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const uppercaseValue = event.target.value?.toUpperCase();
    setValue('itemId', uppercaseValue, { shouldValidate: true });
  };

  const onSubmit: SubmitHandler<CopyItemsType> = async (formData) => {
    const payload = {
      id: Number(id),
      newItemId: formData.newItemId,
    };
    const { data } = await copyItem({
      body: payload,
    });
    const itemId = data?.data?.id;
    if (itemId) {
      updateQueryParam(itemId);
      form.reset();
      setOpen((prev) => !prev);
    }
  };

  const itemTypeValue = form.watch('itemType');
  // handle change item type
  const handleOnChangeItemType = useCallback(
    (value: string) => {
      if (itemTypeValue !== value && value === ItemType.KIT_ITEM && id) {
        setIsItemType(true);
      }
      if (
        itemTypeValue === ItemType.KIT_ITEM &&
        value !== ItemType.KIT_ITEM &&
        id
      ) {
        setIsItemType(true);
      }
    },
    [id, itemTypeValue, setIsItemType]
  );

  const handleCopyItemChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const uppercaseValue = event.target.value?.toUpperCase();
    copyItemForm.setValue('newItemId', uppercaseValue, {
      shouldValidate: true,
    });
  };

  return (
    <div>
      <div className="flex flex-col gap-6 justify-between h-full">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <InputField
            name="itemId"
            form={form}
            label="Item ID"
            placeholder="Enter Item ID"
            onChange={handleChange}
            maxLength={32}
            validation={TEXT_VALIDATION_RULE}
          />
          <InputField
            name="description"
            form={form}
            label="Description"
            maxLength={128}
            placeholder="Enter Description"
          />
          <InputField
            name="location"
            form={form}
            label="Item Location"
            maxLength={64}
            placeholder="Enter Item Location"
          />
          <SelectWidget
            name="categoryId"
            form={form}
            placeholder="Select Category"
            label="Category"
            isClearable={false}
            optionsList={categoryList}
            isLoading={categoryLoading}
            validation={TEXT_VALIDATION_RULE}
          />
          <SelectWidget
            name="vendorId"
            form={form}
            placeholder="Select Vendor"
            label="Vendor"
            isClearable={true}
            optionsList={vendorList}
            isLoading={vendorLoading}
          />

          <SelectWidget
            name="itemType"
            form={form}
            placeholder="Select Item Type"
            label="Item Type"
            isLoading={itemTypeLoading}
            isClearable={false}
            optionsList={itemType?.data ?? []}
            validation={TEXT_VALIDATION_RULE}
            onSelectChange={handleOnChangeItemType}
          />

          <SelectWidget
            name="isActive"
            form={form}
            placeholder="Select Status"
            label="Status"
            isClearable={false}
            optionsList={statusList}
            validation={TEXT_VALIDATION_RULE}
          />
          <div></div>
          <NumberInputField
            form={form}
            name="unitPrice"
            label="Unit Price"
            placeholder="$______.__"
            maxLength={10}
            fixedDecimalScale
            prefix="$"
          />
          <InputField
            form={form}
            name="unitPriceUpdatedOn"
            label="Last Updated on"
            placeholder={DEFAULT_FORMAT}
            disabled
          />
          <NumberInputField
            form={form}
            name="replacementCharge"
            label="Replacement Charge"
            placeholder="$______.__"
            maxLength={10}
            prefix="$"
            fixedDecimalScale
          />
          <InputField
            form={form}
            name="replacementChargeUpdatedOn"
            label="Last Updated on"
            placeholder={DEFAULT_FORMAT}
            disabled
          />
          <InputField
            form={form}
            name="quantity"
            label="Quantity"
            placeholder="______"
            disabled
          />
          <InputField
            form={form}
            name="quantityUpdatedOn"
            label="Last Updated on"
            placeholder={DEFAULT_FORMAT}
            disabled
          />
          <NumberInputField
            form={form}
            name="latestCost"
            label="Latest Cost"
            placeholder="$______.__"
            maxLength={6}
            prefix="$"
            disabled
          />
          <InputField
            form={form}
            name="latestCostUpdatedOn"
            label="Last Updated on"
            placeholder={DEFAULT_FORMAT}
            disabled
          />
          <NumberInputField
            form={form}
            name="averageCost"
            label="Average Cost"
            placeholder="$______.__"
            maxLength={10}
            prefix="$"
            disabled
          />
        </div>
        <div className="flex flex-row gap-2">
          <AppButton
            onClick={() => setOpen((prev) => !prev)}
            label="Copy Item"
            variant="neutral"
            icon={CopyIcon}
            disabled={!id}
          />
          {isDisabled ? (
            <TooltipWidget tooltip="Edit Kit functionality is only available for Kit Items.">
              <div>
                <AppButton
                  label="Edit Kit"
                  variant="neutral"
                  onClick={() => setOpenKit((prev) => !prev)}
                  disabled={isDisabled}
                  icon={EditPencilIcon}
                />
              </div>
            </TooltipWidget>
          ) : (
            <AppButton
              label="Edit Kit"
              variant="neutral"
              onClick={() => setOpenKit((prev) => !prev)}
              disabled={isDisabled}
              icon={EditPencilIcon}
            />
          )}
        </div>
      </div>

      <EditKit open={openKit} setOpen={setOpenKit} />

      {/* Custom Dialog */}
      <CustomDialog
        open={open}
        onOpenChange={() => {
          setOpen((prev) => !prev);
          copyItemForm.clearErrors();
          copyItemForm.setValue('newItemId', '');
        }}
        title="Copy Item"
        contentClassName="py-4"
      >
        <div className="px-6 flex flex-col gap-4">
          <p>
            Please specify an item ID to create using the current item
            information. If this is a Kit or Package Item, all associated
            components will be included.
          </p>
          <form
            onSubmit={copyItemForm.handleSubmit(onSubmit)}
            className="flex flex-col gap-4"
          >
            <InputField
              placeholder="Enter Item ID"
              name="newItemId"
              validation={TEXT_VALIDATION_RULE}
              form={copyItemForm}
              label="Item ID"
              onChange={handleCopyItemChange}
            />
            <AppButton
              label="Submit"
              className="w-full"
              isLoading={copyItemLoading}
            />
          </form>
        </div>
      </CustomDialog>
    </div>
  );
};

export default InformationSystem;
