import { ITEMS_API_ROUTES } from '@/constants/api-constants';
import { booleanToString, formatDate, isValueMatching } from '@/lib/utils';
import { ItemTabTypes, ItemType } from '@/types/item.types';

// ITEMS TAB ENUM DEFINED
enum ITEMS_ENUM {
  INFORMATION = 'information',
  OPTIONS = 'options',
}

interface TAB_TYPE {
  CREATE?: string;
  UPDATE: string;
}
export interface AllTabTypeMap {
  information: TAB_TYPE;
  'linked-files'?: TAB_TYPE;
  options: TAB_TYPE;
}
// API route mapping for each tab
export const ITEMS_API_ROUTES_MAP: AllTabTypeMap = {
  information: {
    CREATE: ITEMS_API_ROUTES.CREATE,
    UPDATE: ITEMS_API_ROUTES.UPDATE,
  },
  options: {
    UPDATE: ITEMS_API_ROUTES?.UPDATE_OPTIONS,
    CREATE: ITEMS_API_ROUTES?.UPDATE_OPTIONS,
  },
};

export const generateItemsDefaultValues = (itemsValue: any = {}): any => {
  const { itemOption, itemLinkedFiles, ...itemsData } = itemsValue;
  return {
    ...itemsData,
    itemType: itemsData?.itemType || ItemType.RENTAL_ITEM,
    isActive: itemsData?.id ? booleanToString(itemsData?.isActive) : 'true',
    unitPriceUpdatedOn: formatDate(itemsData?.unitPriceUpdatedOn),
    replacementChargeUpdatedOn: formatDate(
      itemsData?.replacementChargeUpdatedOn
    ),
    quantityUpdatedOn: formatDate(itemsData?.quantityUpdatedOn),
    latestCostUpdatedOn: formatDate(itemsData?.latestCostUpdatedOn),
    unitPrice: itemsData?.unitPrice || null,
    replacementCharge: itemsData?.replacementCharge || null,
    // Options tab
    itemOption: {
      ...itemOption,
      weight: itemOption?.weight || '0',
      cube: itemOption?.cube || '0',
      cleanupDays: itemOption?.cleanupDays || '0',
      sequenceNo: itemOption?.sequenceNo || null,
      isTumbleDry: booleanToString(itemOption?.isTumbleDry),
      packageDiscount: itemOption?.packageDiscount || null,
    },
  };
};

export const createPayload = (tabName: string, formData: ItemTabTypes): any => {
  const { itemOption, ...itemData } = formData;
  switch (tabName) {
    case ITEMS_ENUM.INFORMATION:
      return {
        ...itemData,
        isActive: isValueMatching({ value: itemData?.isActive }),
        unitPrice: itemData?.unitPrice || null,
        replacementCharge: itemData?.replacementCharge || null,
        vendorId: itemData?.vendorId || null,
      };

    case ITEMS_ENUM.OPTIONS:
      return {
        ...itemOption,
        cleanupDays: itemOption?.cleanupDays || null,
        sequenceNo: itemOption?.sequenceNo || null,
        weight: itemOption?.weight || null,
        cube: itemOption?.cube || null,
        includeItemId: itemOption?.includeItemId || null,
        includeQuantity: itemOption?.includeQuantity || null,
        packageDiscount: itemOption?.packageDiscount || null,
        isTumbleDry: isValueMatching({ value: itemOption?.isTumbleDry }),
      };
    default:
      null;
  }
};
