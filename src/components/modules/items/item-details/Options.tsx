import EditPencilIcon from '@/assets/icons/EditPencilIcon';
import AppButton from '@/components/common/app-button';
import SwitchField from '@/components/common/switch';
import InputField from '@/components/forms/input-field';
import NumberInputField from '@/components/forms/number-input-field';
import SelectWidget from '@/components/forms/select';
import TextAreaField from '@/components/forms/text-area';
import { ItemsContext } from '@/pages/items/ItemsContext';
import {
  ItemOptionTypes,
  OptionsTypes,
  SwitchFieldConfig,
} from '@/types/item.types';
import { useCallback, useContext, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { getQueryParam } from '@/lib/utils';
import EditPackage from './edit-package/EditPackage';

const switchFields: SwitchFieldConfig<OptionsTypes>[] = [
  {
    label: 'Commissionable',
    name: 'isCommissionable',
    description:
      'Item price is included in the commissionable totals on orders.',
  },
  {
    label: 'Taxable',
    name: 'isTaxable',
    description: 'Item price is taxable on orders.',
  },
  {
    label: 'Discountable',
    name: 'isDiscountable',
    description: 'Item price is discountable on orders.',
  },
  {
    label: 'Damage Waiver',
    name: 'isDamageWaiver',
    description:
      'Item price will be included in the damage waiver calculation on orders.',
  },
  {
    label: 'Calc Fuel Surcharge',
    name: 'isCalculateFuelCharge',
    description:
      'Item price will be included in the fuel charge calculation on orders.',
  },
  {
    label: 'Calc Production Fee',
    name: 'isCalculateProdFee',
    description:
      'Item price will be included in the production fee calculation on orders.',
  },
  {
    label: 'Print in Warehouse Manager',
    name: 'isPrintInWarehouseMgr',
    description:
      'Include this item on shipping/return and other warehouse reports.',
  },
  {
    label: 'Print Component When Zero',
    name: 'isPrintComponentWhenZero',
    description:
      'If item is a component on a kit, do not print the item on order forms if its component quantity is zero.',
  },
  {
    label: 'Tent Top',
    name: 'isTentTop',
    description: 'Flag item to be considered a tent top for reporting.',
  },
  {
    label: 'RFID',
    name: 'isRfid',
    description: 'Item will be accessible in the RFID system.',
  },
  {
    label: 'Tagged',
    name: 'isTagged',
    description:
      'Item has an RFID tag attached for scanning in the RFID system. If not tagged, shipped/returned quantities in RFID are manually entered.',
  },
  {
    label: 'Print RFID Labels',
    name: 'isPrintRfidLabel',
    description:
      'This item will print a corresponding label when returned from cleaning in the RFID system.',
  },
  {
    label: 'Item in Swatch',
    name: 'isItemInSwatch',
    description: 'Item exists as a sample in a swatch book.',
  },
  {
    label: 'Item on Web',
    name: 'isItemOnWeb',
    description: 'Item will be included in the website uploader.',
  },
  {
    label: 'Item Pic on Web',
    name: 'isItemPictureOnWeb',
    description:
      'Item has a corresponding image file to be included on the website.',
  },
  {
    label: 'Price Locked',
    name: 'isPriceLocked',
    description: ' Price changes on orders will require a manager approval.',
  },
];

const Options = () => {
  const form = useFormContext<ItemOptionTypes>();
  const { setIsPackageItem } = useContext(ItemsContext);
  const id = getQueryParam('id') as string;

  const selectYesNo = [
    { label: 'Yes', value: 'true' },
    { label: 'No', value: 'false' },
  ];
  const [open, setOpen] = useState<boolean>(false);

  const isPackageItem = form.watch('itemOption.isPackageItem');

  // handle change Package Item
  const handlePackageItem = useCallback(
    (value: boolean) => {
      if (value !== isPackageItem && id) {
        setIsPackageItem(true);
      }
    },
    [id, isPackageItem, setIsPackageItem]
  );

  return (
    <div className="flex flex-col gap-6 justify-between h-full">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <NumberInputField
          form={form}
          name="itemOption.cleanupDays"
          label="Clean-up Days"
          placeholder="Enter Clean-up Days"
          maxLength={2}
        />
        <NumberInputField
          name="itemOption.sequenceNo"
          form={form}
          label="Sequence #"
          placeholder="Enter Sequence #"
          maxLength={6}
          decimalScale={0}
        />
        <NumberInputField
          name="itemOption.weight"
          form={form}
          label="Weight"
          placeholder="Enter Weight"
          maxLength={8}
          fixedDecimalScale
        />
        <NumberInputField
          name="itemOption.cube"
          form={form}
          label="Cube"
          placeholder="Enter Cube"
          maxLength={8}
          fixedDecimalScale
        />
        <NumberInputField
          name="itemOption.includeItemId"
          form={form}
          label="Auto-Include Item ID"
          placeholder="Enter Auto-Include Item ID"
          maxLength={15}
          decimalScale={0}
        />
        <NumberInputField
          name="itemOption.includeQuantity"
          form={form}
          label="Auto-Include Quantity"
          placeholder="Enter Auto-Include Quantity"
          maxLength={4}
          decimalScale={0}
        />
        <div className="col-span-2 border rounded-md grid grid-cols-1 md:grid-cols-2 gap-5 p-6">
          <SwitchField
            label="Inventory Item"
            name={'itemOption.isInventoryItem'}
            // description="Description"
            form={form}
            className="mt-4"
            disabled
          />
          <div>
            <SwitchField
              label="Package Item"
              name={'itemOption.isPackageItem'}
              description="This enables the addition of other items which are a part of package along with this."
              form={form}
              className="my-4"
              onChange={handlePackageItem}
            />
            <EditPackage open={open} setOpen={setOpen} />
            <AppButton
              label="Edit Package"
              variant="neutral"
              icon={EditPencilIcon}
              disabled={!isPackageItem}
              onClick={() => setOpen((prev) => !prev)}
            />
          </div>
        </div>
        <NumberInputField
          name="itemOption.packageDiscount"
          form={form}
          label="Package Discount"
          placeholder="$______.__"
          maxLength={10}
          prefix="$"
          fixedDecimalScale
        />
        <InputField
          name="itemOption.webSku"
          form={form}
          label="Web SKU"
          placeholder="Enter Web SKU"
          maxLength={64}
        />
        <TextAreaField
          name="itemOption.warningMessage"
          form={form}
          label="Warning Message"
          placeholder="Enter Warning Message"
        />
        <TextAreaField
          name="itemOption.itemInfo"
          form={form}
          label="Item Info"
          placeholder="Enter Item Info"
        />
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 p-6 gap-5 border border-border-Default rounded-lg">
        {switchFields?.map((field, index) => (
          <div key={`${field?.label}-${index}`}>
            <SwitchField
              key={`${field?.label}-${index}`}
              label={field?.label}
              name={`itemOption.${field?.name}`}
              form={form}
              className="mt-4"
              disabled={field?.disabled}
              description={field?.description}
            />
          </div>
        ))}
      </div>
      <p className="text-2xl font-semibold text-[#181A1D]">Linen Options</p>
      <div className="grid grid-cols-1 md:grid-cols-2 p-6 gap-4 border border-border-Default rounded-lg">
        <InputField
          name="itemOption.washCycle"
          form={form}
          label="Wash Cycle"
          placeholder="Enter Wash Cycle"
          maxLength={64}
        />
        <SelectWidget
          form={form}
          name="itemOption.isTumbleDry"
          label="Tumble Dry"
          isClearable={false}
          placeholder="Select Tumble Dry"
          optionsList={selectYesNo}
        />
      </div>
    </div>
  );
};

export default Options;
