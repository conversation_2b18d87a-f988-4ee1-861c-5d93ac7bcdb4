import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { useForm, FormProvider } from 'react-hook-form';
import Options from './Options';
import { ItemsContext } from '@/pages/items/ItemsContext';

// Mock child components and hooks
vi.mock('@/components/forms/input-field', () => ({
  default: ({ name, label }: any) => (
    <div>
      <label htmlFor={name}>{label}</label>
      <input id={name} name={name} />
    </div>
  ),
}));

vi.mock('@/components/forms/number-input-field', () => ({
  default: ({ name, label }: any) => (
    <div>
      <label htmlFor={name}>{label}</label>
      <input id={name} name={name} type="number" />
    </div>
  ),
}));

vi.mock('@/components/forms/text-area', () => ({
  default: ({ name, label }: any) => (
    <div>
      <label htmlFor={name}>{label}</label>
      <textarea id={name} name={name} />
    </div>
  ),
}));

vi.mock('@/components/forms/select', () => ({
  default: ({ name, label }: any) => (
    <div>
      <label htmlFor={name}>{label}</label>
      <select id={name} name={name} />
    </div>
  ),
}));

vi.mock('@/components/common/switch', () => ({
  default: ({ name, label, onChange }: any) => (
    <div>
      <label htmlFor={name}>{label}</label>
      <input
        id={name}
        name={name}
        type="checkbox"
        onChange={(e) => onChange(e.target.checked)}
      />
    </div>
  ),
}));

vi.mock(import('@/lib/utils'), async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...actual,
    getQueryParam: vi.fn(),
    cn: vi.fn(),
  };
});

vi.mock('./edit-package/EditPackage', () => ({
  default: ({ open }: any) => (open ? <div>Edit Package Modal</div> : null),
}));

describe('Options Component', () => {
  const mockSetIsPackageItem = vi.fn();
  const mockGetQueryParam = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    mockGetQueryParam.mockReturnValue('123');
  });

  // Test wrapper component
  const TestFormWrapper = () => {
    const methods = useForm<any>({
      defaultValues: {
        itemOption: {
          isPackageItem: false,
          isInventoryItem: true,
        },
      },
    });

    return (
      <ItemsContext.Provider
        value={{
          setIsPackageItem: mockSetIsPackageItem,
          isPackageItem: false,
          setIsItemType: vi.fn(),
          isItemType: false,
        }}
      >
        <FormProvider {...methods}>
          <Options />
        </FormProvider>
      </ItemsContext.Provider>
    );
  };

  // Updated renderComponent (used in your test)
  const renderComponent = () => render(<TestFormWrapper />);

  it('should render all form fields', () => {
    renderComponent();
    expect(screen.getByLabelText('Clean-up Days')).toBeInTheDocument();
    expect(screen.getByLabelText('Sequence #')).toBeInTheDocument();
    expect(screen.getByLabelText('Weight')).toBeInTheDocument();
    expect(screen.getByLabelText('Cube')).toBeInTheDocument();
    expect(screen.getByLabelText('Auto-Include Item ID')).toBeInTheDocument();
    expect(screen.getByLabelText('Auto-Include Quantity')).toBeInTheDocument();
    expect(screen.getByLabelText('Package Discount')).toBeInTheDocument();
    expect(screen.getByLabelText('Web SKU')).toBeInTheDocument();
    expect(screen.getByLabelText('Warning Message')).toBeInTheDocument();
    expect(screen.getByLabelText('Item Info')).toBeInTheDocument();
    expect(screen.getByLabelText('Wash Cycle')).toBeInTheDocument();
    expect(screen.getByLabelText('Tumble Dry')).toBeInTheDocument();
    expect(screen.getByLabelText('Inventory Item')).toBeInTheDocument();
    expect(screen.getByLabelText('Package Item')).toBeInTheDocument();
    expect(screen.getByLabelText('Commissionable')).toBeInTheDocument();
    expect(screen.getByLabelText('Taxable')).toBeInTheDocument();
  });

  it('should show Edit Package button and modal when package item is enabled', () => {
    renderComponent();
    const editButton = screen.getByText('Edit Package');
    expect(editButton);
  });

  it('should call setIsPackageItem when package item value changes for existing item', () => {
    renderComponent();
    const packageSwitch = screen.getByLabelText('Package Item');
    fireEvent.click(packageSwitch);
    expect(mockSetIsPackageItem);
  });

  it('should not call setIsPackageItem when package item changes for new item', () => {
    mockGetQueryParam.mockReturnValue(null);
    renderComponent();
    const packageSwitch = screen.getByLabelText('Package Item');
    fireEvent.click(packageSwitch);
    expect(mockSetIsPackageItem).not.toHaveBeenCalled();
  });

  it('should render all switch fields from the configuration', () => {
    renderComponent();
    expect(screen.getByLabelText('Commissionable')).toBeInTheDocument();
    expect(screen.getByLabelText('Taxable')).toBeInTheDocument();
    expect(screen.getByLabelText('Discountable')).toBeInTheDocument();
    expect(screen.getByLabelText('Damage Waiver')).toBeInTheDocument();
    expect(screen.getByLabelText('Calc Fuel Surcharge')).toBeInTheDocument();
    expect(screen.getByLabelText('Calc Production Fee')).toBeInTheDocument();
    expect(
      screen.getByLabelText('Print in Warehouse Manager')
    ).toBeInTheDocument();
    expect(
      screen.getByLabelText('Print Component When Zero')
    ).toBeInTheDocument();
    expect(screen.getByLabelText('Tent Top')).toBeInTheDocument();
    expect(screen.getByLabelText('RFID')).toBeInTheDocument();
    expect(screen.getByLabelText('Tagged')).toBeInTheDocument();
    expect(screen.getByLabelText('Print RFID Labels')).toBeInTheDocument();
    expect(screen.getByLabelText('Item in Swatch')).toBeInTheDocument();
    expect(screen.getByLabelText('Item on Web')).toBeInTheDocument();
    expect(screen.getByLabelText('Item Pic on Web')).toBeInTheDocument();
    expect(screen.getByLabelText('Price Locked')).toBeInTheDocument();
  });

  it('should render the Linen Options section', () => {
    renderComponent();
    expect(screen.getByText('Linen Options')).toBeInTheDocument();
    expect(screen.getByLabelText('Wash Cycle')).toBeInTheDocument();
    expect(screen.getByLabelText('Tumble Dry')).toBeInTheDocument();
  });
});
