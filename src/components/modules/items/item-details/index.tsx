import EditIcon from '@/assets/icons/EditIcon';
import AppButton from '@/components/common/app-button';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import AppDataTable from '@/components/common/app-data-table';
import { AppTableContext } from '@/components/common/app-data-table/AppTableContext';
import AppSpinner from '@/components/common/app-spinner';
import StatusBadge from '@/components/common/app-status-badge';
import ActionColumnMenu from '@/components/common/data-tables/ActionColumn';
import CustomDialog from '@/components/common/dialog';
import Filter from '@/components/modules/items/item-details/actions/Filter';
import { UseToast } from '@/components/ui/toast/ToastContainer';
import { ITEMS_API_ROUTES } from '@/constants/api-constants';
import { ROUTES } from '@/constants/routes-constants';
import { useDownloadFile } from '@/hooks/useDownloadFile';
import { cn, convertToFloat, getPaginationObject } from '@/lib/utils';
import { useAddNewItemMutation } from '@/redux/features/common-api/common.api';
import { useDeleteItemMutation } from '@/redux/features/items/item.api';
import { clearItemFilter } from '@/redux/features/items/itemSlice';
import { useGetListQuery } from '@/redux/features/list/category/list.api';
import { RootState } from '@/redux/store';
import { ItemsListingTypes } from '@/types/item.types';
import { ColumnDef } from '@tanstack/react-table';
import { Download, PlusIcon } from 'lucide-react';
import { useCallback, useContext, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useNavigate } from 'react-router-dom';
import BulkEditItems from './bulkEditItems';
import ItemColumnOrdering from './column-ordering';
interface ItemToDeleteType {
  id: number | null;
  itemId: string | null;
}

const Items = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const filter = useSelector((state: RootState) => state.item.filters);
  const { sorting } = useContext(AppTableContext); // Use pagination from context
  const [deleteItem, { isLoading: isDeleteLoading }] = useDeleteItemMutation();
  const { data, refetch, isFetching } = useGetListQuery({
    url: ITEMS_API_ROUTES.GET_COLUMN,
  });
  const [addNewItem, { isLoading: newItemLoading }] = useAddNewItemMutation();

  const { downloadFile } = useDownloadFile();
  const toast = UseToast();
  const [openBuilkEdit, setOpenBuilkEdit] = useState<boolean>(false);
  const [openDeleteDialog, setOpenDeleteDialog] = useState<boolean>(false);
  const [openColumnOrdering, setOpenColumnOrdering] = useState<boolean>(false);
  const [itemToDelete, setitemToDelete] = useState<ItemToDeleteType | null>(
    null
  );
  const [isFilterOpen, setIsFilterOpen] = useState<boolean>(false);
  const [refreshList, setRefreshList] = useState<boolean>(false);
  const [searchParams, setSearchParams] = useState<string>('');
  const componentMap: any = useMemo(
    () => ({
      StatusBadge: ({ value }: any) => <StatusBadge status={value} />,
      LabelDollar: ({ value }: any) => convertToFloat({ value, prefix: '$' }),
    }),
    []
  );

  const tableColumns = useMemo(() => {
    return data?.data;
  }, [data?.data]);

  // toggle delete
  const toggleDelete = useCallback((row?: ItemsListingTypes) => {
    setitemToDelete(row ? { id: row?.id, itemId: row?.itemId } : null);
    setOpenDeleteDialog((prevState) => !prevState);
  }, []);

  // handle delete item
  const handleDeleteItem = async () => {
    await deleteItem(itemToDelete?.id as number)
      .unwrap()
      .then(() => {
        setRefreshList(true);
        setitemToDelete(null);
      })
      .finally(() => {
        toggleDelete();
        setTimeout(() => {
          setRefreshList(false);
        }, 500);
      });
  };

  const handleOrderingColumn = useCallback(
    async (formData: any) => {
      const updatedColumns = tableColumns.map((item: any) => ({
        ...item,
        enabled: formData[item.accessorKey],
      }));

      await addNewItem({
        url: ITEMS_API_ROUTES.UPDATE_COLUMN,
        data: updatedColumns,
      }).unwrap();
      setOpenColumnOrdering(false);
      await refetch();
    },
    [addNewItem, refetch, tableColumns]
  );

  const handleClearFilter = useCallback(
    (key: string) => {
      dispatch(clearItemFilter(key));
      setIsFilterOpen(false);
    },
    [dispatch]
  );

  const CustomToolbar = (
    <AppButton
      icon={PlusIcon}
      iconClassName="w-5 h-5"
      label="New Item"
      onClick={() => navigate(`${ROUTES.ADD_NEW_ITEM}?tab=information`)}
    />
  );

  const handleExtractItemsToExcel = useCallback(() => {
    const normalizedFilters = Array.isArray(filter)
      ? filter.map((f: any) => ({
          field: f.name || f.label,
          value: f.value,
          operator: f.operator,
        }))
      : [];
    const payload = getPaginationObject({
      pagination: { pageIndex: 0, pageSize: 0 },
      sorting,
      filters: [
        ...normalizedFilters,
        { field: 'filter', value: searchParams, operator: 'Contains' },
      ],
    });
    const response = downloadFile({
      url: ITEMS_API_ROUTES.DOWNLOAD,
      method: 'POST',
      body: payload,
    });
    toast.promise(response, {
      loading: 'Downloading file...',
      success: 'File downloaded successfully.',
      error: 'Failed to download file',
    });
  }, [filter, sorting, searchParams, downloadFile, toast]);

  const toggleOpenBulkEdit = useCallback(() => {
    setOpenBuilkEdit((prev) => !prev);
  }, []);

  const DropdownMenu = useMemo(() => {
    return [
      {
        label: 'Extract Items to CSV',
        onClick: handleExtractItemsToExcel,
        icon: <Download className="w-5 h-5 text-text-default" />,
      },
      {
        label: 'Bulk Edit',
        onClick: toggleOpenBulkEdit,
        icon: <EditIcon />,
      },
    ];
  }, [handleExtractItemsToExcel, toggleOpenBulkEdit]);

  const actionColumn: ColumnDef<ItemsListingTypes> = useMemo(
    () => ({
      id: 'action',
      size: 110,
      header: 'Actions',
      cell: ({ row }) => (
        <ActionColumnMenu
          customEdit={
            <Link
              to={`${ROUTES.EDIT_ITEM}?id=${row.original.id}&tab=information`}
              className="flex items-center justify-center gap-2 border border-text-tertiary rounded-md px-3 py-1"
            >
              <EditIcon /> Edit details
            </Link>
          }
          onDelete={() => toggleDelete(row.original)}
        />
      ),
    }),
    [toggleDelete]
  );

  const memoizedColumns = useMemo(() => {
    if (!tableColumns?.length) return [];

    const formattedColumns = tableColumns
      .filter((column: any) => column.enabled) // Ensure only enabled columns are included
      .map((column: any) => ({
        accessorKey: column.accessorKey,
        header: column.header,
        enableSorting: column.enableSorting || false,
        size: column.size || 200, // Default size if not provided
        cell: ({ row }: any) => {
          const Component = componentMap[column.component]; // Get the component based on the key
          return Component ? (
            <Component value={row?.original?.[column.accessorKey]} />
          ) : (
            row?.original?.[column.accessorKey]
          );
        },
      }));

    return [
      {
        accessorKey: 'ordering',
        header: () => (
          <ItemColumnOrdering
            isOpen={openColumnOrdering}
            handleOrderingColumn={handleOrderingColumn}
            tableColumns={tableColumns}
            setOpenColumnOrdering={setOpenColumnOrdering}
          />
        ),
        size: 50,
      },
      ...formattedColumns,
      actionColumn,
    ];
  }, [
    tableColumns,
    componentMap,
    openColumnOrdering,
    handleOrderingColumn,
    setOpenColumnOrdering,
    actionColumn,
  ]);

  return (
    <div className="flex flex-col p-4 gap-6">
      <AppDataTable
        url={ITEMS_API_ROUTES.ALL}
        columns={memoizedColumns}
        enableSearch={true}
        searchKey="filter"
        heading="Items"
        enableFilter
        setSearchParams={setSearchParams}
        filter={filter}
        handleClearFilter={handleClearFilter}
        filterClassName="w-[473px]"
        filterContent={<Filter setIsFilterOpen={setIsFilterOpen} />}
        setIsFilterOpen={setIsFilterOpen}
        isFilterOpen={isFilterOpen}
        customToolBar={CustomToolbar}
        enablePagination={true}
        refreshList={refreshList}
        tableClassName="max-h-[580px] overflow-auto"
        dropdownMenus={DropdownMenu}
        dropdownContentClassName="p-4 flex flex-col gap-2 w-full"
      />
      <AppConfirmationModal
        description={
          <div>
            Are you sure you want to delete{' '}
            <span className="font-semibold text-text-Default">
              {itemToDelete?.itemId}
            </span>
            ?
          </div>
        }
        open={openDeleteDialog}
        handleCancel={toggleDelete}
        disabled={isDeleteLoading}
        isLoading={isDeleteLoading}
        handleSubmit={handleDeleteItem}
      />
      <CustomDialog
        title="Bulk Edit Items"
        onOpenChange={toggleOpenBulkEdit}
        description=""
        open={openBuilkEdit}
        contentClassName="h-[400px] lg:h-[460px] 2xl:h-[650px] overflow-auto"
        className={cn('min-w-[85%] 2xl:min-w-[60%]')}
      >
        <BulkEditItems
          onOpenChange={toggleOpenBulkEdit}
          onClickSubmit={() => {
            setRefreshList(true);
            setTimeout(() => {
              setRefreshList(false);
            }, 500);
          }}
        />
      </CustomDialog>
      <AppSpinner overlay isLoading={newItemLoading || isFetching} />
    </div>
  );
};

export default Items;
