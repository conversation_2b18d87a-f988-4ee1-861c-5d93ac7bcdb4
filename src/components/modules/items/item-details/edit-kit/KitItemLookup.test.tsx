import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import ItemLookup from './KitItemLookup';
import * as reactHookForm from 'react-hook-form';

// Mock the necessary dependencies
vi.mock('@/hooks/useOptionList', () => ({
  default: vi.fn(() => ({
    options: [
      { label: 'Category 1', value: '1' },
      { label: 'Category 2', value: '2' },
    ],
    optionLoading: false,
  })),
}));

vi.mock('@/redux/features/items/item.api', () => ({
  useGetItemLookupQuery: vi.fn(() => ({
    data: {
      data: [
        {
          id: '1',
          itemId: 'ITEM001',
          description: 'Test Item 1',
          unitPrice: 10.99,
          itemType: 'Type1',
        },
        {
          id: '2',
          itemId: 'ITEM002',
          description: 'Test Item 2',
          unitPrice: 15.99,
          itemType: 'Type2',
        },
      ],
      pagination: {
        totalCount: 2,
      },
    },
    isFetching: false,
    isSuccess: true,
  })),
}));

// Mock react-hook-form properly
vi.mock('react-hook-form', () => ({
  useForm: () => ({
    formState: { errors: {} },
    handleSubmit: vi.fn((fn) => fn),
    watch: vi.fn(() => []),
    reset: vi.fn(),
    control: {},
    register: vi.fn(),
    setValue: vi.fn(),
    getValues: vi.fn(() => ({})),
  }),
  useFieldArray: () => ({
    fields: [],
    append: vi.fn(),
    remove: vi.fn(),
    update: vi.fn(),
  }),
  Controller: () => <></>,
}));

describe('ItemLookup Component', () => {
  const mockOnClick = vi.fn();
  const mockFields = [
    {
      id: '1',
      itemId: 'ITEM001',
      description: 'Test Item 1',
      price: 10.99,
      itemType: 'Type1',
      quantity: '',
    },
    {
      id: '2',
      itemId: 'ITEM002',
      description: 'Test Item 2',
      price: 15.99,
      itemType: 'Type2',
      quantity: '',
    },
  ];

  vi.mock('react-hook-form', () => {
    return {
      useForm: vi.fn(() => ({
        formState: { errors: {} },
        handleSubmit: vi.fn((fn) => fn),
        watch: vi.fn(() => []),
        reset: vi.fn(),
        control: {},
        register: vi.fn(),
        setValue: vi.fn(),
        getValues: vi.fn(() => ({})),
      })),
      useFieldArray: vi.fn(() => ({
        fields: [],
        append: vi.fn(),
        remove: vi.fn(),
        update: vi.fn(),
      })),
      Controller: () => <></>,
    };
  });

  it('renders without crashing', async () => {
    render(<ItemLookup url="/test-url" onClick={mockOnClick} />);
    await waitFor(() => {
      expect(
        screen.getByText('Select one or more Item to add to the package list')
      ).toBeInTheDocument();
    });
  });

  it('displays the correct heading', async () => {
    render(<ItemLookup url="/test-url" onClick={mockOnClick} />);
    await waitFor(() => {
      expect(
        screen.getByText('Select one or more Item to add to the package list')
      ).toBeInTheDocument();
    });
  });

  it('renders the search input', async () => {
    render(<ItemLookup url="/test-url" onClick={mockOnClick} />);
    await waitFor(() => {
      expect(screen.getByPlaceholderText('Search')).toBeInTheDocument();
    });
  });

  it('renders the data table with items', async () => {
    vi.spyOn(reactHookForm, 'useFieldArray').mockImplementation(
      () =>
        ({
          fields: mockFields,
          append: vi.fn(),
          remove: vi.fn(),
          update: vi.fn(),
        }) as any
    );

    vi.spyOn(reactHookForm, 'useForm').mockImplementation(
      () =>
        ({
          formState: { errors: {} },
          handleSubmit: vi.fn((fn) => fn),
          watch: vi.fn(() => mockFields),
          reset: vi.fn(),
          control: {},
          register: vi.fn(),
          setValue: vi.fn(),
          getValues: vi.fn(() => ({ items: mockFields })),
        }) as any
    );
    render(<ItemLookup url="/test-url" onClick={mockOnClick} />);
    await waitFor(() => {
      expect(screen.getByText('ITEM001')).toBeInTheDocument();
      expect(screen.getByText('Test Item 1')).toBeInTheDocument();
      expect(screen.getByText('ITEM002')).toBeInTheDocument();
      expect(screen.getByText('Test Item 2')).toBeInTheDocument();
    });
  });

  it('renders quantity input fields for each item', async () => {
    vi.spyOn(reactHookForm, 'useFieldArray').mockImplementation(
      () =>
        ({
          fields: mockFields,
          append: vi.fn(),
          remove: vi.fn(),
          update: vi.fn(),
        }) as any
    );
    vi.spyOn(reactHookForm, 'useForm').mockImplementation(
      () =>
        ({
          formState: { errors: {} },
          handleSubmit: vi.fn((fn) => fn),
          watch: vi.fn(() => mockFields),
          reset: vi.fn(),
          control: {},
          register: vi.fn(),
          setValue: vi.fn(),
          getValues: vi.fn(() => ({ items: mockFields })),
        }) as any
    );
    const renderedInput = render(
      <ItemLookup url="/test-url" onClick={mockOnClick} />
    );
    expect(renderedInput);
  });

  it('displays the correct default button labels', async () => {
    render(<ItemLookup url="/test-url" onClick={mockOnClick} />);
    await waitFor(() => {
      expect(screen.getByText('Add to Order Item List')).toBeInTheDocument();
      expect(screen.getByText('Reset')).toBeInTheDocument();
    });
  });

  it('resets the form when reset button is clicked', async () => {
    const mockReset = vi.fn();
    vi.spyOn(reactHookForm, 'useForm').mockImplementation(
      () =>
        ({
          formState: { errors: {} },
          handleSubmit: vi.fn(),
          watch: vi.fn(() => mockFields),
          reset: mockReset,
          control: {},
          register: vi.fn(),
          setValue: vi.fn(),
          getValues: vi.fn(() => ({ items: mockFields })),
        }) as any
    );
    render(<ItemLookup url="/test-url" onClick={mockOnClick} />);
    const resetButton = screen.getByText('Reset');
    fireEvent.click(resetButton);
    await waitFor(() => {
      expect(mockReset).toHaveBeenCalled();
    });
  });

  it('disables submit button when no items are selected', async () => {
    vi.spyOn(reactHookForm, 'useForm').mockImplementation(
      () =>
        ({
          formState: { errors: {} },
          handleSubmit: vi.fn(),
          watch: vi.fn(() =>
            mockFields.map((item) => ({ ...item, quantity: '' }))
          ),
          reset: vi.fn(),
          control: {},
          register: vi.fn(),
          setValue: vi.fn(),
          getValues: vi.fn(() => ({
            items: mockFields.map((item) => ({ ...item, quantity: '' })),
          })),
        }) as any
    );
    render(<ItemLookup url="/test-url" onClick={mockOnClick} />);
    await waitFor(() => {
      const submitButton = screen.getByText('Add to Order Item List');
      expect(submitButton).not.toBeDisabled();
    });
  });

  it('enables submit button when items have quantity', async () => {
    vi.spyOn(reactHookForm, 'useForm').mockImplementation(
      () =>
        ({
          formState: { errors: {} },
          handleSubmit: vi.fn(),
          watch: vi.fn(() => mockFields),
          reset: vi.fn(),
          control: {},
          register: vi.fn(),
          setValue: vi.fn(),
          getValues: vi.fn(() => ({ items: mockFields })),
        }) as any
    );
    render(<ItemLookup url="/test-url" onClick={mockOnClick} />);
    await waitFor(() => {
      const submitButton = screen.getByText('Add to Order Item List');
      expect(submitButton).not.toBeDisabled();
    });
  });
});
