import { Badge<PERSON>heck } from 'lucide-react';
import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from 'react';
import { SubmitHandler, useFieldArray, useForm } from 'react-hook-form';

// Components
import DataTable from '@/components/common/data-tables';
import MultiCheckboxDropdown from '@/components/forms/MulitCheckbox';
import NumberInputField from '@/components/forms/number-input-field';

// Hooks and Utilities
import FormActionButtons from '@/components/common/FormActionButtons';
import { CATEGORY_API_ROUTES } from '@/constants/api-constants';
import useOptionList from '@/hooks/useOptionList';
import { convertToFloat, getPaginationObject } from '@/lib/utils';
import { useGetItemLookupQuery } from '@/redux/features/items/item.api';
import { PaginationType, SortingStateType } from '@/types/common.types';
import {
  OrderItemLookupFormTypes,
  OrderItemLookupTypes,
} from '@/types/order.types';

/**
 * ItemLookup Component
 * Provides a dialog-based interface for searching and selecting items
 * with quantity and price input capabilities.
 */

interface ItemLookupProps {
  onClick?: (value: OrderItemLookupFormTypes[]) => void;
  className?: string;
  btnLabel?: string;
  url: string;
  heading?: string;
  disabled?: boolean;
}

const ItemLookup = ({
  onClick,
  btnLabel = 'Add to Order Item List',
  url,
  heading = 'Select one or more Item to add to the package list',
}: ItemLookupProps) => {
  // State Management
  const [search, setSearch] = useState('');
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [pagination, setPagination] = useState<PaginationType>({
    pageSize: 10,
    pageIndex: 0,
  });
  const [sorting, setSorting] = useState<SortingStateType[]>([
    { id: 'itemId', desc: true },
  ]);
  const [isFetchingMore, setIsFetchingMore] = useState<boolean>(false);

  // API Hooks
  const payload = useMemo(() => {
    return getPaginationObject({
      pagination,
      sorting,
      filters: [
        { field: 'filter', value: search, operator: 'Contains' },
        {
          field: 'categoryId',
          value: selectedCategories.join(','),
          operator: 'Equals',
        },
      ],
    });
  }, [pagination, search, selectedCategories, sorting]);

  const {
    data,
    isFetching: isLoading,
    isFetching,
    isSuccess,
  } = useGetItemLookupQuery({
    url,
    body: payload,
  });
  const ItemList = useMemo(
    () => (isSuccess && data?.data ? data?.data : []),
    [data?.data, isSuccess]
  );

  const { options: categoryList, optionLoading } = useOptionList({
    url: CATEGORY_API_ROUTES.ALL,
    labelKey: 'catDesc',
    valueKey: 'id',
    sortBy: 'catDesc',
  });

  const categoryForm = useForm();

  const defaultValues = useMemo(() => {
    return {
      items:
        ItemList?.map((item: any) => ({
          id: item?.id,
          itemId: item?.itemId,
          description: item?.description,
          price: item?.unitPrice,
          itemType: item?.itemType,
        })) || [],
    };
  }, [ItemList]);

  // Form Management
  const form = useForm<{ items: OrderItemLookupTypes[] }>({
    defaultValues,
    mode: 'onChange',
  });

  const { fields } = useFieldArray({
    control: form.control,
    name: 'items',
  });

  /**
   * Reset form with fresh data when response changes
   */
  useEffect(() => {
    if (defaultValues) {
      form.reset(defaultValues);
    }
  }, [defaultValues, form]);

  /**
   * Handle dialog toggle with form reset
   */
  const toggleDialog = useCallback(() => {
    form.reset();
    setSelectedCategories([]);
    setSearch('');
    categoryForm.reset();
  }, [categoryForm, form]);

  /**
   * Form submission handler
   */
  const handleSubmit: SubmitHandler<{ items: OrderItemLookupTypes[] }> =
    useCallback(
      (formData) => {
        const payload = formData.items
          ?.filter((item) => Number(item?.quantity) > 0)
          ?.map((item) => ({
            id: null,
            childItemId: item.id,
            description: item.description,
            quantity: Number(item.quantity),
            itemId: { label: item.itemId, value: item.id?.toString() ?? '' },
            price: item.price,
            itemType: item.itemType,
          }));
        toggleDialog();
        onClick?.(payload);
      },
      [onClick, toggleDialog]
    );

  /**
   * Reset form to initial data state
   */
  const handleReset = useCallback(() => {
    form.reset({
      items:
        data?.data?.map((item) => ({
          ...item,
          quantity: '',
        })) || [],
    });
  }, [data?.data, form]);

  // Table configuration
  const columns = useMemo(
    () => [
      {
        accessorKey: 'itemId',
        header: 'Item ID',
        size: 80,
        enableSorting: true,
      },
      {
        accessorKey: 'description',
        header: 'Item Description',
        maxSize: 250,
        enableSorting: true,
      },
      {
        accessorKey: 'quantity',
        header: 'Quantity',
        size: 100,
        maxSize: 100,
        cell: ({ row }: any) => (
          <div className="p-1">
            <NumberInputField
              form={form}
              placeholder="Quantity"
              name={`items.${row?.index}.quantity`}
              className="w-full h-8"
              maxLength={5}
              decimalScale={0}
            />
          </div>
        ),
      },
      {
        accessorKey: 'unitPrice',
        header: 'Price',
        size: 80,
        enableSorting: true,
        cell: ({ row }: any) =>
          convertToFloat({ value: row.original.unitPrice, prefix: '$' }),
      },
    ],
    [form]
  );

  // Derived state
  const hasSelectedItems = form
    .watch('items')
    ?.some((item) => Number(item?.quantity) > 0);

  // Custom toolbar for category selection
  const CustomToolbar = (
    <MultiCheckboxDropdown
      name="category"
      form={categoryForm}
      optionsList={categoryList ?? []}
      placeholder={'Select Categories'}
      onChange={(value) => setSelectedCategories(value)}
      isLoading={optionLoading}
    />
  );

  const fetchMore = useCallback(() => {
    if (isFetchingMore) return; // Avoid duplicate calls

    setIsFetchingMore(true); // Set flag to prevent further calls
    setPagination((prev) => ({ ...prev, pageSize: prev.pageSize + 10 }));
  }, [isFetchingMore]);

  // Reset `isFetchingMore` only after new data arrives
  useEffect(() => {
    if (!isFetching) {
      setIsFetchingMore(false);
    }
  }, [isFetching]);

  return (
    <div className="w-full">
      <div className="px-5 py-2">
        <DataTable
          data={fields ?? []}
          pagination={pagination}
          setPagination={setPagination}
          columns={columns}
          isLoading={isLoading}
          totalItems={data?.pagination?.totalCount}
          search={search}
          setSearch={setSearch}
          enableSearch
          sorting={sorting}
          setSorting={setSorting}
          customToolBar={CustomToolbar}
          heading={<div className="font-normal text-base">{heading}</div>}
          enablePagination={false}
          tableClassName="max-h-[310px] 2xl:max-h-[460px] overflow-auto"
          loaderRows={9}
          isInfiniteScroll
          fetchMore={fetchMore}
        />
      </div>

      <FormActionButtons
        className="fixed bottom-0 left-0 right-0 bg-white px-4 py-2 pt-0"
        onSubmit={form.handleSubmit(handleSubmit)}
        submitLabel={btnLabel}
        onCancel={handleReset}
        isLoading={false}
        cancelLabel="Reset"
        submitIcon={BadgeCheck}
        disabledSubmitButton={!hasSelectedItems}
        disabledCancelButton={!hasSelectedItems}
      />
    </div>
  );
};

export default ItemLookup;
