import { useForm, useFieldArray } from 'react-hook-form';
import { describe, expect, vi } from 'vitest';
import { it } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import KitItemForm from './KitItemForm';
import { Provider } from 'react-redux';
import { store } from '@/redux/store';

interface EditKitItem {
  id: number;
  itemId: { label: string; value: string };
  quantity: number;
  description: string;
  childItemId: number;
}

interface FormData {
  editKit: EditKitItem[];
}

describe('KitItemForm', () => {
  function Wrapper(props: any) {
    const form = useForm<FormData>({
      defaultValues: { editKit: [] },
      mode: 'onChange',
    });
    const { append, update, remove, fields } = useFieldArray({
      control: form.control,
      name: 'editKit',
    });
    return (
      <Provider store={store}>
        <KitItemForm
          form={form}
          append={append}
          update={update}
          remove={remove}
          fields={fields}
          {...props}
        />
      </Provider>
    );
  }

  it('renders buttons and table', () => {
    render(
      <Wrapper
        onOpenChange={vi.fn()}
        isKitItemLoading={false}
        setActiveTab={vi.fn()}
      />
    );
    expect(screen.getByText('Item Lookup')).toBeInTheDocument();
    expect(screen.getByText('+ Add New')).toBeInTheDocument();
    expect(screen.getByText('Submit')).toBeInTheDocument();
    expect(screen.getByText('Cancel')).toBeInTheDocument();
  });

  it('adds new item when "+ Add New" clicked', () => {
    let getValuesFn: any;
    function WrapperWithExpose() {
      const form = useForm<FormData>({
        defaultValues: { editKit: [] },
        mode: 'onChange',
      });
      const { append, update, remove, fields } = useFieldArray({
        control: form.control,
        name: 'editKit',
      });
      getValuesFn = form.getValues;
      return (
        <Provider store={store}>
          <KitItemForm
            form={form}
            append={append}
            update={update}
            remove={remove}
            fields={fields}
            onOpenChange={vi.fn()}
            isKitItemLoading={false}
            setActiveTab={vi.fn()}
          />
        </Provider>
      );
    }
    render(<WrapperWithExpose />);
    fireEvent.click(screen.getByText('+ Add New'));
    expect(getValuesFn('editKit')).toHaveLength(1);
  });

  it('calls setActiveTab when "Item Lookup" clicked', () => {
    const setActiveTab = vi.fn();
    render(
      <Wrapper
        onOpenChange={vi.fn()}
        isKitItemLoading={false}
        setActiveTab={setActiveTab}
      />
    );
    fireEvent.click(screen.getByText('Item Lookup'));
    expect(setActiveTab).toHaveBeenCalledWith('item-lookup');
  });

  it('submits form on submit click', async () => {
    let appendFn: any;
    function WrapperWithAppend() {
      const form = useForm<FormData>({
        defaultValues: { editKit: [] },
        mode: 'onChange',
      });
      const { append, update, remove, fields } = useFieldArray({
        control: form.control,
        name: 'editKit',
      });
      appendFn = append;
      return (
        <Provider store={store}>
          <KitItemForm
            form={form}
            append={append}
            update={update}
            remove={remove}
            fields={fields}
            onOpenChange={vi.fn()}
            isKitItemLoading={false}
            setActiveTab={vi.fn()}
          />
        </Provider>
      );
    }
    render(<WrapperWithAppend />);
    appendFn({
      id: 1,
      itemId: { label: 'item1', value: 'item1' },
      quantity: 2,
      description: 'desc',
      childItemId: 1,
    });
    fireEvent.click(screen.getByText('Submit'));
  });

  it('opens and confirms delete modal', async () => {
    function WrapperWithFields() {
      const form = useForm({
        defaultValues: { editKit: [{ id: 0 }] },
        mode: 'onChange',
      });
      const { append, update, remove, fields } = useFieldArray({
        control: form.control,
        name: 'editKit',
      });
      return (
        <Provider store={store}>
          <KitItemForm
            form={form}
            append={append}
            update={update}
            remove={remove}
            fields={fields}
            onOpenChange={vi.fn()}
            isKitItemLoading={false}
            setActiveTab={vi.fn()}
          />
        </Provider>
      );
    }
    const isdeleted = render(<WrapperWithFields />);
    expect(isdeleted);
  });
});
