import { use<PERSON>allback, useEffect, useMemo, useRef, useState } from 'react';
import { Submit<PERSON>and<PERSON> } from 'react-hook-form';

// Components
import AppButton from '@/components/common/app-button';
import ActionColumnMenu from '@/components/common/data-tables/ActionColumn';
import ReactSelect from '@/components/common/ReactSelect';
import Input<PERSON>ield from '@/components/forms/input-field';
import NumberInput<PERSON>ield from '@/components/forms/number-input-field';

// Constants and Utils
import { ITEMS_API_ROUTES } from '@/constants/api-constants';
import { cn, getQueryParam } from '@/lib/utils';

// API Hooks
import DataTable from '@/components/common/data-tables';
import {
  useGetItemBriefMutation,
  useUpdateKitItemMutation,
} from '@/redux/features/items/item.api';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import { SearchIcon } from 'lucide-react';

interface KitItem {
  id: number | null | string;
  itemId: {
    label: string;
    value: string;
  };
  quantity?: number | string | null;
  description: string;
  parentItemId?: number;
  childItemId?: number;
}

interface EditKitItems {
  editKit: KitItem[];
}

interface DeleteDialogState {
  open: boolean;
  id: number;
}

const KitItemForm = ({
  form,
  update,
  fields,
  append,
  onOpenChange,
  isKitItemLoading,
  remove,
  setActiveTab,
}: any) => {
  const [openDeleteDialog, setOpenDeleteDialog] = useState<DeleteDialogState>({
    open: false,
    id: 0,
  });
  const onScrollRef = useRef<HTMLTableElement>(null);
  const id = getQueryParam('id') as string;
  const [getitemBrief] = useGetItemBriefMutation();
  const [updateKit, { isLoading }] = useUpdateKitItemMutation();

  const handleAddNew = useCallback(() => {
    append({
      id: '',
      itemId: { label: '', value: '' },
      quantity: null,
      description: '',
    });
  }, [append]);

  // Form submission handler
  const onSubmit: SubmitHandler<EditKitItems> = useCallback(
    async (formData) => {
      try {
        const payload = formData?.editKit
          ?.filter((item) => item?.itemId?.value)
          ?.map((value) => {
            return {
              id:
                typeof value.id === 'string' || value.id === null
                  ? null
                  : value.id,
              parentItemId: Number(id),
              childItemId: value.childItemId,
              itemId: value.itemId.label,
              description: value.description,
              quantity: Number(value.quantity),
            };
          });
        await updateKit({ body: payload, itemId: id }).unwrap();
        onOpenChange();
      } catch (error) {
        // Handle error (could display error message to user)
      }
    },
    [id, onOpenChange, updateKit]
  );

  // toggle delete
  const toggleDelete = useCallback((id?: number) => {
    setOpenDeleteDialog((prev) => ({ open: !prev?.open, id: id || 0 }));
  }, []);

  useEffect(() => {
    if (onScrollRef.current) {
      // Scroll to the bottom of the table when the fields change
      onScrollRef.current.scrollTop = onScrollRef.current.scrollHeight;
    }
  }, [fields]); // Trigger scroll when fields change

  // handle the delete edit package row
  const handleDelete = useCallback(() => {
    remove(openDeleteDialog.id);
    toggleDelete();
  }, [openDeleteDialog.id, remove, toggleDelete]);

  const columns = useMemo(() => {
    return [
      {
        accessorKey: 'itemId',
        header: 'Item ID',
        size: 80,
        enableSorting: true,
        cell: ({ row }: any) => {
          const handleOnItemChange = async (value: any, index: number) => {
            const itemData = await getitemBrief(value?.value);
            if (itemData?.data) {
              update(index, {
                ...fields[index],
                itemId: value,
                childItemId: value?.value,
                description: itemData.data?.data?.description ?? '',
              });
            }
          };

          return (
            <ReactSelect
              placeholder="Item ID"
              className=" bg-white"
              name={`editKit.${row?.index}.itemId`}
              form={form}
              onSelectChange={(value) => handleOnItemChange(value, row.index)}
              url={ITEMS_API_ROUTES.KIT_ITEM_LOOKUP_DROPDOWN(id)}
              labelKey="itemId"
              valueKey="id"
              maxMenuHeight={250}
            />
          );
        },
      },
      {
        accessorKey: 'quantity',
        header: 'Quantity',
        size: 80,
        enableSorting: true,
        cell: ({ row }: any) => {
          const itemId = form.watch(`editKit.${row?.index}.itemId`);
          const disabled = itemId?.value;
          return (
            <div className="p-1">
              <NumberInputField
                form={form}
                placeholder="Quantity"
                name={`editKit.${row?.index}.quantity`}
                className="w-28 h-8"
                decimalScale={0}
                disabled={!disabled}
                maxLength={5}
              />
            </div>
          );
        },
      },
      {
        accessorKey: 'description',
        header: 'Item Description',
        enableSorting: true,
        cell: ({ row }: any) => {
          const disabled = form.watch(`editKit.${row?.index}.itemId`);
          return (
            <div className="flex items-center justify-between ">
              <div className="truncate">
                <InputField
                  form={form}
                  placeholder="Description"
                  name={`editKit.${row?.index}.description`}
                  disabled={!disabled}
                  pClassName="p-1"
                  className="h-8"
                />
              </div>
              <ActionColumnMenu
                onDelete={() => toggleDelete(row?.index)}
                contentClassName="z-[99] w-fit"
              />
            </div>
          );
        },
      },
    ];
  }, [form, id, getitemBrief, update, fields, toggleDelete]);

  return (
    <>
      <div className="flex flex-col gap-2">
        {/* Filter Section */}
        <div className="flex justify-end">
          <AppButton
            label="Item Lookup"
            variant="primary"
            icon={SearchIcon}
            iconClassName="w-5 h-5"
            className="bg-brand-teal-Default hover:bg-brand-teal-Default"
            onClick={() => setActiveTab('item-lookup')}
          />
        </div>

        {/* DataTable Section */}
        <DataTable
          onScrollRef={onScrollRef}
          tableClassName={cn('max-h-[300px] 2xl:max-h-[470px] overflow-auto')}
          data={fields}
          columns={columns}
          isLoading={isKitItemLoading}
          enableSearch={false}
          enablePagination={false}
          bindingKey="id"
        />

        <AppButton
          label="+ Add New"
          variant="neutral"
          className="bg-brand-teal-Default mt-2 w-fit hover:bg-brand-teal-hover border-border-brand-teal-Default text-white mb-2"
          iconClassName="text-white"
          onClick={handleAddNew}
        />
      </div>

      {/* Fixed Button Section */}
      <div className="absolute bottom-0 left-0 w-full bg-white border-t border-gray-200 px-6 py-4 flex justify-end space-x-4 z-10">
        <AppButton
          label="Submit"
          isLoading={isLoading}
          onClick={form.handleSubmit(onSubmit)}
          className="w-24"
        />
        <AppButton
          label="Cancel"
          onClick={onOpenChange}
          variant="neutral"
          className="w-24"
        />
      </div>
      <AppConfirmationModal
        description={<div>Are you sure you want to delete ?</div>}
        open={openDeleteDialog.open}
        handleCancel={toggleDelete}
        handleSubmit={handleDelete}
      />
    </>
  );
};

export default KitItemForm;
