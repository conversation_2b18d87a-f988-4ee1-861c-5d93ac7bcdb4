import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import EditKit from './EditKit';
import { useGetKitItemsQuery } from '@/redux/features/items/item.api';
import BreadcrumbDialogRenderer from '@/components/common/BreadcrumbDialogRenderer';
import ItemLookup from './KitItemLookup';

// Mock the hooks and components
vi.mock('@/redux/features/items/item.api', () => ({
  useGetKitItemsQuery: vi.fn(),
}));

vi.mock('@/lib/utils', () => ({
  getQueryParam: vi.fn(() => '123'),
}));

vi.mock('@/components/common/BreadcrumbDialogRenderer', () => ({
  default: vi.fn(({ listItems, activeTab }) => (
    <div>
      {listItems.map((item: any) => (
        <div key={item.value} data-testid={`tab-${item.value}`}>
          {activeTab === item.value && item.content}
        </div>
      ))}
    </div>
  )),
}));

vi.mock('./KitItemForm', () => ({
  default: vi.fn(() => <div>KitItemForm Component</div>),
}));

vi.mock('./KitItemLookup', () => ({
  default: vi.fn(() => <div>ItemLookup Component</div>),
}));

describe('EditKit Component', () => {
  const mockSetOpen = vi.fn();
  const props = {
    open: true,
    setOpen: mockSetOpen,
  };

  beforeEach(() => {
    vi.clearAllMocks();

    (useGetKitItemsQuery as any).mockReturnValue({
      data: {
        data: [
          {
            id: 1,
            parentItemId: 1,
            childItemId: 2,
            itemId: 'Test Item',
            quantity: 5,
            description: 'Test Description',
          },
        ],
      },
      isLoading: false,
    });
  });

  it('renders without crashing', () => {
    render(<EditKit {...props} />);
    expect(screen.getByText('KitItemForm Component')).toBeInTheDocument();
  });

  it('fetches kit items when open is true', () => {
    render(<EditKit {...props} />);
    expect(useGetKitItemsQuery).toHaveBeenCalledWith('123', { skip: false });
  });

  it('does not fetch kit items when open is false', () => {
    render(<EditKit {...props} open={false} />);
    expect(useGetKitItemsQuery).toHaveBeenCalledWith('123', { skip: true });
  });

  it('initializes form with default values from API', async () => {
    render(<EditKit {...props} />);
    await waitFor(() => {
      expect(useGetKitItemsQuery).toHaveBeenCalled();
    });
  });

  it('switches between tabs correctly', () => {
    vi.mocked(BreadcrumbDialogRenderer as any).mockImplementation(
      ({ listItems, activeTab, setActiveTab }: any) => (
        <div>
          <button
            onClick={() => setActiveTab('item-lookup')}
            data-testid="switch-tab"
          >
            Switch Tab
          </button>
          {listItems.map((item: any) => (
            <div key={item.value} data-testid={`tab-${item.value}`}>
              {activeTab === item.value && item.content}
            </div>
          ))}
        </div>
      )
    );
    render(<EditKit {...props} />);
    expect(screen.getByTestId('tab-edit-kit')).toBeInTheDocument();
    fireEvent.click(screen.getByTestId('switch-tab'));
    expect(screen.getByTestId('tab-item-lookup')).toBeInTheDocument();
  });

  it('resets form and closes dialog when onOpenChange is called', () => {
    vi.mocked(BreadcrumbDialogRenderer).mockImplementation(
      ({ onOpenChange }: any) => (
        <button onClick={onOpenChange} data-testid="close-button">
          Close
        </button>
      )
    );
    render(<EditKit {...props} />);
    const isClosed = fireEvent.click(screen.getByTestId('close-button'));
    expect(isClosed);
  });

  it('displays loading state when data is loading', () => {
    (useGetKitItemsQuery as any).mockReturnValue({
      data: null,
      isLoading: true,
    });
    render(<EditKit {...props} />);
  });

  it('handles adding items from lookup', () => {
    vi.mocked(ItemLookup as any).mockImplementation(({ onClick }: any) => (
      <button
        onClick={() =>
          onClick({ id: 3, itemId: { label: 'New Item', value: '3' } })
        }
      >
        Add Item
      </button>
    ));
    render(<EditKit {...props} />);
    fireEvent.click(screen.getByTestId('close-button'));
  });
});
