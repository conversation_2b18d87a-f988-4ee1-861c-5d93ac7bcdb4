import { useCallback, useEffect, useMemo, useState } from 'react';
import { useFieldArray, useForm } from 'react-hook-form';

// Components

// Constants and Utils
import { ITEMS_API_ROUTES } from '@/constants/api-constants';
import { getQueryParam } from '@/lib/utils';

// API Hooks
import BreadcrumbDialogRenderer from '@/components/common/BreadcrumbDialogRenderer';
import { useGetKitItemsQuery } from '@/redux/features/items/item.api';
import KitItemForm from './KitItemForm';
import ItemLookup from './KitItemLookup';

// Types

interface KitItem {
  id: number | null | string;
  itemId: {
    label: string;
    value: string;
  };
  quantity?: number | string | null;
  description: string;
  parentItemId?: number;
  childItemId?: number;
}

interface EditKitPropType {
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  open: boolean;
}

interface EditKitItems {
  editKit: KitItem[];
}

const EditKit = ({ setOpen, open }: EditKitPropType) => {
  const id = getQueryParam('id') as string;

  // API Hooks
  const { data, isLoading: isKitItemLoading } = useGetKitItemsQuery(id, {
    skip: !open,
  });

  // Form Setup
  const defaultValues = useMemo(() => {
    return {
      editKit:
        data?.data?.map((item) => ({
          id: item?.id ?? null,
          parentItemId: item?.parentItemId,
          childItemId: item?.childItemId,
          itemId: {
            label: item?.itemId ?? '',
            value: item?.childItemId?.toString() ?? '',
          },
          quantity: item?.quantity ?? 0,
          description: item?.description ?? '',
        })) || [],
    };
  }, [data?.data]);

  const form = useForm<EditKitItems>({
    defaultValues,
    mode: 'onChange',
  });

  const { fields, append, remove, update } = useFieldArray({
    control: form.control,
    name: 'editKit',
  });

  const onOpenChange = useCallback(() => {
    form.reset();
    setActiveTab('edit-kit');
    setOpen((prev) => !prev);
  }, [form, setOpen]);

  useEffect(() => {
    if (defaultValues) {
      form.reset(defaultValues);
    }
  }, [defaultValues, form]);

  const handleAddItemLookupData = useCallback(
    (data: any) => {
      setActiveTab('edit-kit');
      append(data);
    },
    [append]
  );

  const [activeTab, setActiveTab] = useState('edit-kit');
  const handleChangeTab = useCallback((tab?: string) => {
    setActiveTab(tab ?? 'edit-kit');
  }, []);

  // tab list
  const listItems = useMemo(() => {
    return [
      {
        label: 'Edit Kit',
        value: 'edit-kit',
        content: (
          <KitItemForm
            form={form}
            update={update}
            fields={fields}
            append={append}
            onOpenChange={onOpenChange}
            isKitItemLoading={isKitItemLoading}
            remove={remove}
            setActiveTab={setActiveTab}
          />
        ),
      },
      {
        label: 'Item Lookup',
        value: 'item-lookup',
        content: (
          <ItemLookup
            onClick={handleAddItemLookupData}
            url={ITEMS_API_ROUTES.KIT_ITEM_LOOKUP(id)}
            btnLabel="Add to Kit List"
            heading="Select one or more Item to add to the Kit list"
          />
        ),
      },
    ];
  }, [
    append,
    fields,
    form,
    handleAddItemLookupData,
    id,
    isKitItemLoading,
    onOpenChange,
    remove,
    update,
  ]);

  return (
    <>
      <BreadcrumbDialogRenderer
        activeTab={activeTab}
        isOpen={open}
        listItems={listItems}
        onOpenChange={onOpenChange}
        setActiveTab={handleChangeTab}
        className="max-w-[80%] 2xl:max-w-[55%] overflow-x-auto"
        contentClassName="h-[500px] 2xl:h-[680px] overflow-y-auto"
      />
    </>
  );
};

export default EditKit;
