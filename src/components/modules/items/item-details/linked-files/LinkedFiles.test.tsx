import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import LinkedFiles from './LinkedFiles';
import {
  useGetLinkedFilesListByIdQuery,
  useDeleteLinkedFileMutation,
  useDefaultLinkedFileMutation,
} from '@/redux/features/items/linkedFiles.api';
import { useDownloadFile } from '@/hooks/useDownloadFile';
import { UseToast } from '@/components/ui/toast/ToastContainer';
import { useUploadLinkedFilesMutation } from '@/redux/features/items/linkedFiles.api';

// Mock API hooks
vi.mock('@/redux/features/items/linkedFiles.api', async () => {
  const actual = await import('@/redux/features/items/linkedFiles.api');
  return {
    ...actual,
    useGetLinkedFilesListByIdQuery: vi.fn(),
    useDeleteLinkedFileMutation: vi.fn(),
    useDefaultLinkedFileMutation: vi.fn(),
    useUploadLinkedFilesMutation: vi.fn(),
  };
});
vi.mock('@/hooks/useDownloadFile', () => ({ useDownloadFile: vi.fn() }));
vi.mock('@/components/ui/toast/ToastContainer', () => ({ UseToast: vi.fn() }));
vi.mock('@/utils/getQueryParam', () => ({
  getQueryParam: vi.fn(() => 'test-item-id'),
}));

const mockRefetch = vi.fn();
const mockDeleteFile = vi.fn();
const mockDefaultFile = vi.fn();
const mockDownloadFile = vi.fn();
const mockToast = { success: vi.fn(), promise: vi.fn() };

const dummyLinkedFiles = [
  {
    id: '1',
    fileName: 'example-file-1.pdf',
    fileSize: '1.2 MB',
    uploadedAt: '2023-10-01T12:00:00Z',
  },
  {
    id: '2',
    fileName: 'example-file-2.docx',
    fileSize: '0.5 MB',
    uploadedAt: '2023-10-02T14:30:00Z',
  },
];

describe('LinkedFiles Component', () => {
  beforeEach(() => {
    (useGetLinkedFilesListByIdQuery as any).mockReturnValue({
      data: dummyLinkedFiles,
      isFetching: false,
      refetch: mockRefetch,
    });

    (useDeleteLinkedFileMutation as any).mockReturnValue([
      mockDeleteFile,
      { isLoading: false },
    ]);
    (useDefaultLinkedFileMutation as any).mockReturnValue([
      mockDefaultFile,
      { isLoading: false },
    ]);
    (useDownloadFile as any).mockReturnValue({
      downloadFile: mockDownloadFile,
      isLoading: false,
    });
    (UseToast as any).mockReturnValue(mockToast);
    (useUploadLinkedFilesMutation as any).mockReturnValue([
      vi.fn(),
      { isLoading: false },
    ]);
  });

  it('renders the component correctly with no data', () => {
    // Mock empty data
    (useGetLinkedFilesListByIdQuery as any).mockReturnValue({
      data: [],
      isFetching: false,
      refetch: mockRefetch,
    });

    render(<LinkedFiles />);
    expect(screen.getByText('Add File')).toBeInTheDocument();
    expect(screen.getByText('File Name')).toBeInTheDocument();
    expect(screen.getByText('No data found')).toBeInTheDocument();
  });

  it('renders the table headers correctly', () => {
    render(<LinkedFiles />);
    expect(screen.getByText('Icon')).toBeInTheDocument();
    expect(screen.getByText('File Name')).toBeInTheDocument();
    expect(screen.getByText('Order')).toBeInTheDocument();
    expect(screen.getByText('Date Created')).toBeInTheDocument();
    expect(screen.getByText('Size')).toBeInTheDocument();
    expect(screen.getByText('Actions')).toBeInTheDocument();
  });

  it('opens the upload modal when Add File button is clicked', () => {
    render(<LinkedFiles />);
    const addButton = screen.getByText('Add File');
    fireEvent.click(addButton);
    expect(
      screen.getByText(
        'Drag & Drop or upload to add a document as a Linked File to this item.'
      )
    ).toBeInTheDocument();
  });
});
