import LinkedFiles from '@/components/common/LinkedFiles';
import { ITEM_LINKED_FILE_API_ROUTES } from '@/constants/api-constants';
import { getQueryParam } from '@/lib/utils';
import {
  useDefaultLinkedFileMutation,
  useDeleteLinkedFileMutation,
  useGetLinkedFilesListByIdQuery,
} from '@/redux/features/items/linkedFiles.api';
import { ReuploadTypes } from '@/types/common.types';
import { useState } from 'react';

const LinkedFilesTab = () => {
  const itemId = getQueryParam('id') as string;
  const [isReupload, setIsReupload] = useState<ReuploadTypes>({
    id: null,
    state: false,
  });
  const { data, isLoading, refetch } = useGetLinkedFilesListByIdQuery(itemId);
  const [deleteFile, { isLoading: isDeleteLoading }] =
    useDeleteLinkedFileMutation();
  const [defaultFile, { isLoading: isDefaultLoading }] =
    useDefaultLinkedFileMutation();

  return (
    <LinkedFiles
      data={data?.data}
      isFetching={isLoading}
      refetch={refetch}
      deleteFile={deleteFile}
      defaultFile={defaultFile}
      isDeleteLoading={isDeleteLoading}
      isDefaultLoading={isDefaultLoading}
      uploadUrl={ITEM_LINKED_FILE_API_ROUTES.UPLOAD(itemId)}
      downloadUrl={ITEM_LINKED_FILE_API_ROUTES.DOWNLOAD}
      isReupload={isReupload}
      setIsReupload={setIsReupload}
      reuploadUrl={ITEM_LINKED_FILE_API_ROUTES.REUPLOAD(isReupload?.id)}
    />
  );
};

export default LinkedFilesTab;
