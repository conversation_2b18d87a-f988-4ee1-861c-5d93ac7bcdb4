import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import ActionMenuItem, { MenuListTypes } from './ActionColumn';
import { vi, describe, it, expect } from 'vitest';

describe('ActionMenuItem Component', () => {
  const mockOnEdit = vi.fn();
  const mockOnClick = vi.fn();

  const dropdownMenuItems: MenuListTypes[] = [
    { label: 'Edit', onClick: mockOnClick },
    { label: 'Delete', onClick: mockOnClick, disabled: true },
  ];

  it('renders correctly', () => {
    render(
      <ActionMenuItem
        rowId={1}
        dropdownMenuItem={dropdownMenuItems}
        onEdit={mockOnEdit}
      />
    );
    expect(screen.getByText('View details')).toBeInTheDocument();
  });

  it('calls onEdit function when "View details" button is clicked', () => {
    render(
      <ActionMenuItem
        rowId={1}
        dropdownMenuItem={dropdownMenuItems}
        onEdit={mockOnEdit}
      />
    );

    const editButton = screen.getByText('View details');
    fireEvent.click(editButton);

    expect(mockOnEdit).toHaveBeenCalled();
  });

  it('renders dropdown menu and calls onClick with rowId', () => {
    render(<ActionMenuItem rowId={1} dropdownMenuItem={dropdownMenuItems} />);

    const dropdownButton = screen.getByRole('button');
    fireEvent.click(dropdownButton);
    expect(mockOnClick);
  });

  it('disables a menu item when disabled prop is set', () => {
    render(<ActionMenuItem rowId={1} dropdownMenuItem={dropdownMenuItems} />);

    const dropdownButton = screen.getByRole('button');
    fireEvent.click(dropdownButton);
    expect(mockOnClick).not.toHaveBeenCalled();
  });
});
