import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { MemoryRouter, useNavigate } from 'react-router-dom';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { AddEditItems } from './AddEditItems';
import { ItemsContext } from '@/pages/items/ItemsContext';
import {
  useGetItemByIdQuery,
  useUpdateItemMutation,
  useSaveInventoryMutation,
} from '@/redux/features/items/item.api';
import { ROUTES } from '@/constants/routes-constants';

// Mock the necessary hooks and components
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: vi.fn(),
    useParams: vi.fn(() => ({})),
    useSearchParams: vi.fn(() => [new URLSearchParams(''), vi.fn()]),
  };
});

vi.mock('@/redux/features/items/item.api', () => ({
  useGetItemByIdQuery: vi.fn(),
  useUpdateItemMutation: vi.fn(),
  useSaveInventoryMutation: vi.fn(),
}));

vi.mock(import('@/lib/utils'), async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...actual,
    getQueryParam: vi.fn(),
    updateQueryParam: vi.fn(),
  };
});

vi.mock('@/components/common/app-tabs-vertical', () => ({
  default: ({ tabs }: any) => (
    <div data-testid="app-tabs-vertical">
      {tabs.map((tab: any) => tab.label)}
    </div>
  ),
}));

// Mock other components as needed
vi.mock('@/components/common/app-button', () => ({
  default: ({ label, onClick }: any) => (
    <button onClick={onClick}>{label}</button>
  ),
}));

vi.mock('@/components/common/app-confirmation-modal', () => ({
  default: ({ open, description }: any) =>
    open ? <div data-testid="confirmation-modal">{description}</div> : null,
}));

describe('AddEditItems Component', () => {
  const mockNavigate = vi.fn();
  const mockUpdateItem = vi.fn();
  const mockSaveInventory = vi.fn();
  const mockGetQueryParam = vi.fn();
  const mockUpdateQueryParam = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    // Setup mock implementations
    (useNavigate as any).mockReturnValue(mockNavigate);
    (useUpdateItemMutation as any).mockReturnValue([
      mockUpdateItem,
      { isLoading: false },
    ]);
    (useSaveInventoryMutation as any).mockReturnValue([
      mockSaveInventory,
      { isLoading: false },
    ]);
    (useGetItemByIdQuery as any).mockReturnValue({
      data: null,
      isLoading: false,
    });

    // Mock utility functions
    vi.mocked(mockGetQueryParam).mockImplementation((param) => {
      if (param === 'id') return '123';
      if (param === 'tab') return 'information';
      return null;
    });

    vi.mocked(mockUpdateQueryParam).mockImplementation(() => {});
  });

  const renderComponent = (contextValue = {}) => {
    const store = configureStore({
      reducer: {
        items: () => ({}),
      },
    });
    return render(
      <Provider store={store}>
        <MemoryRouter>
          <ItemsContext.Provider
            value={{
              isItemType: false,
              setIsItemType: vi.fn(),
              isPackageItem: false,
              setIsPackageItem: vi.fn(),
              ...contextValue,
            }}
          >
            <AddEditItems />
          </ItemsContext.Provider>
        </MemoryRouter>
      </Provider>
    );
  };

  it('should render the component with header and buttons', () => {
    renderComponent();
    expect(screen.getByText('Items')).toBeInTheDocument();
    expect(screen.getByText('Save Detail')).toBeInTheDocument();
    expect(screen.getByText('Cancel')).toBeInTheDocument();
  });

  it('should display loading spinner when item data is loading', () => {
    (useGetItemByIdQuery as any).mockReturnValue({ isLoading: true });
    renderComponent();
    expect(screen.getByRole('status')).toBeInTheDocument();
  });

  it('should navigate back when cancel button is clicked', () => {
    renderComponent();
    fireEvent.click(screen.getByText('Cancel'));
    expect(mockNavigate).toHaveBeenCalledWith(ROUTES.ITEMS);
  });

  it('should render tabs correctly', () => {
    renderComponent();
    expect(screen.getByTestId('app-tabs-vertical')).toBeInTheDocument();
  });

  it('should show confirmation modal when item type changes', () => {
    renderComponent({ isItemType: true });
    expect(screen.getByTestId('confirmation-modal')).toBeInTheDocument();
    expect(
      screen.getByText(/Are you sure you want to proceed with the/)
    ).toBeInTheDocument();
  });

  it('should show confirmation modal when package item changes', () => {
    renderComponent({ isPackageItem: true });
    expect(screen.getByTestId('confirmation-modal')).toBeInTheDocument();
  });

  it('should call save inventory when inventory tab is active and form is submitted', async () => {
    (mockGetQueryParam as any).mockImplementation((param: any) => {
      if (param === 'tab') return 'inventory';
      return '123';
    });
    const mockInventoryData = {
      inventory: [
        {
          id: 1,
          storeLocationId: 1,
          quantity: 5,
          qualityId: 1,
          serialNo: '123',
        },
      ],
    };
    (useGetItemByIdQuery as any).mockReturnValue({
      data: { data: mockInventoryData },
      isLoading: false,
    });
    renderComponent();
    fireEvent.click(screen.getByText('Save Detail'));
    await waitFor(() => {
      expect(mockSaveInventory);
    });
  });

  it('should disable save button when inventory is not modified', () => {
    (mockGetQueryParam as any).mockImplementation((param: any) => {
      if (param === 'tab') return 'inventory';
      return '123';
    });
    renderComponent();
    const saveButton = screen.getByText('Save Detail');
    expect(saveButton);
  });

  it('should handle new item creation', async () => {
    (mockGetQueryParam as any).mockImplementation((param: any) => {
      if (param === 'id') return null;
      return 'information';
    });
    renderComponent();
    fireEvent.click(screen.getByText('Save Detail'));
    await waitFor(() => {
      expect(mockUpdateItem);
    });
  });

  it('should update query param when tab changes', () => {
    renderComponent();
    expect(mockUpdateQueryParam);
  });
});
