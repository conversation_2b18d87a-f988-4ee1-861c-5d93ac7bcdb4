import AppButton from '@/components/common/app-button';
import { cn } from '@/lib/utils';
import { useLoginMutation } from '@/redux/features/auth/authApi';
import { useGetAssigndPermissionsMutation } from '@/redux/features/user-options/login-profile-api';
import { useCallback, useEffect, useState } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import FormFields from './FormFields';
import FormHeader from './FormHeader';
import { setPermissionsLoading } from '@/redux/features/auth/authSlice';
import { useDispatch } from 'react-redux';

interface LoginFormData {
  email: string;
  password: string;
  rememberMe: boolean;
}

const LoginForm = () => {
  const [login, { isLoading }] = useLoginMutation();
  const [getAssignedPermissions] = useGetAssigndPermissionsMutation();
  const [isAutofilled, setIsAutofilled] = useState(false);
  const dispatch = useDispatch();
  const form = useForm<LoginFormData>({
    defaultValues: {
      email: '',
      password: '',
      rememberMe: true,
    },
    // mode: 'onChange',
  });

  const email = form.watch('email');
  const password = form.watch('password');

  // Detect if the email or password fields have been autofilled
  useEffect(() => {
    if (email && password && !isAutofilled) {
      setIsAutofilled(true);
    }
  }, [email, password, isAutofilled]);

  // Enable the button when both email and password are filled
  // const isDisabled = !(email?.length > 0 && password?.length > 0);

  const onSubmit: SubmitHandler<LoginFormData> = useCallback(
    async (data) => {
      try {
        const response = await login({ ...data }).unwrap();
        if (response.statusCode === 200) {
          dispatch(setPermissionsLoading(true));
          await getAssignedPermissions().unwrap();
          // const role =
          //   localStorage.getItem('role') || sessionStorage.getItem('role');
          // const route =
          //   role === ROLES.SUPER_ADMIN
          //     ? ROUTES.ACCOUNTS
          //     : role === ROLES.ADMIN
          //       ? ROUTES.CUSTOMERS
          //       : ROUTES.HOME;

          // navigate(route);
        }
      } catch (error) {
        // No need to show toast here, it's handled in the authApi
      }
    },
    [dispatch, getAssignedPermissions, login]
  );

  return (
    <div className="w-full h-full overflow-hidden bg-white/10 rounded-2xl lg:px-16 lg:py-20 md:p-12 p-10 flex flex-col justify-center">
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <div className="flex flex-col gap-8">
          <FormHeader title="Login" />
          <FormFields form={form} />
        </div>

        <AppButton
          isLoading={isLoading}
          className={cn(
            'bg-brand-teal-Default text-white hover:bg-brand-teal-Default mt-32 w-full transition-colors sticky bottom-0'
          )}
          spinnerClass={'border-white-500  border-t-transparent animate-spin'}
          label="Login"
        />
      </form>
    </div>
  );
};

export default LoginForm;
