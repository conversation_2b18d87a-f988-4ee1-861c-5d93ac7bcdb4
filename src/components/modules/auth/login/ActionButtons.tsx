import { Button } from '@/components/ui/button';
import { ROUTES } from '@/constants/routes-constants';
import { cn } from '@/lib/utils';
import { LogIn } from 'lucide-react';
import { useLocation, useNavigate } from 'react-router-dom';

interface ActionButtonsProps {
  isLoading: boolean;
}

export const ActionButtons = ({ isLoading }: ActionButtonsProps) => {
  const navigate = useNavigate();
  const location = useLocation();

  // Handle button behavior based on current route
  const isLoginPage = location.pathname === ROUTES.LOGIN;
  const isSignUpPage = location.pathname === ROUTES.SIGNUP;

  const handleLoginClick = () => {
    if (isLoginPage) {
      // Add actual login logic here
    } else {
      // Redirect to login page
      navigate(ROUTES.LOGIN);
    }
  };

  // const handleSignUpClick = () => {
  //   if (isSignUpPage) {
  //     // Add actual sign-up logic here
  //   } else {
  //     // Redirect to sign-up page
  //     navigate(ROUTES.SIGNUP);
  //   }
  // };

  return (
    <div className="flex flex-col gap-6">
      {/* Render Sign Up button first when on Login page, or Login button first when on Sign Up page */}
      {isSignUpPage ? (
        <>
          {/* <Button
            disabled={isLoading}
            onClick={handleSignUpClick}
            type={isSignUpPage ? 'submit' : 'button'}
            className={cn(
              'bg-brand-teal-Default text-brand-violet hover:bg-brand-violet hover:text-white',
              'w-full transition-colors'
            )}
          >
            Sign Up
          </Button> */}
          <Button
            disabled={isLoading}
            onClick={handleLoginClick}
            type={isLoginPage ? 'submit' : 'button'}
            className={cn(
              'bg-transparent border border-white text-white hover:bg-white/10',
              'w-full transition-colors'
            )}
          >
            Login <LogIn className="ml-2 w-4 h-4" />
          </Button>

          <div className="bg-[#6A6FC7] text-white">Or</div>
        </>
      ) : (
        <>
          <Button
            disabled={isLoading}
            onClick={handleLoginClick}
            type={isLoginPage ? 'submit' : 'button'}
            className={cn(
              'bg-brand-teal-Default text-white hover:bg-brand-teal-Default mt-6',
              'w-full transition-colors'
            )}
          >
            Login
          </Button>
          {/* <Button
            disabled={isLoading}
            onClick={handleSignUpClick}
            type={isSignUpPage ? 'submit' : 'button'}
            className={cn(
              'bg-transparent border border-white text-white hover:bg-white/10',
              'w-full transition-colors'
            )}
          >
            Sign Up <LogIn className="ml-2 w-4 h-4" />
          </Button> */}
        </>
      )}
    </div>
  );
};

export default ActionButtons;
