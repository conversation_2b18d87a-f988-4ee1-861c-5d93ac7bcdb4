// import AppCheckbox from '@/components/common/app-checkbox/AppCheckbox';
import InputField from '@/components/forms/input-field';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Label } from '@/components/ui/label';
// import { ROUTES } from '@/constants/routes-constants';
import { Eye, EyeOff, TriangleAlert } from 'lucide-react';
import { useState } from 'react';
import { UseFormReturn } from 'react-hook-form';
// import { NavLink } from 'react-router-dom';

interface FormFieldsProps {
  form: UseFormReturn<{
    email: string;
    password: string;
    rememberMe: boolean;
  }>;
}

const EMAIL_VALIDATION_RULEs = {
  required: 'Email',
  pattern: {
    value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
    message: 'Email',
  },
};

export const FormFields = ({ form }: FormFieldsProps) => {
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);

  const handlePasswordVisibilityToggle = () => {
    setIsPasswordVisible((prev) => !prev);
  };

  const hasError =
    !!form.formState.errors['email'] || !!form.formState.errors['password'];

  function AlertDestructive() {
    return (
      hasError && (
        <Alert
          variant="destructive"
          className="bg-[#FEE9E7] text-text-danger border-0 border-l-4 border-l-danger"
        >
          <TriangleAlert className="h-6 w-6 text-text-danger" />
          <AlertTitle className="text-lg font-semibold text-red-600 ml-1">
            Error
          </AlertTitle>
          <AlertDescription style={{ paddingLeft: '0px' }}>
            {hasError && (
              <div className="text-text-Default text-base">
                Please enter a valid{' '}
                {[
                  form.formState.errors['email']?.message?.toString(),
                  form.formState.errors['password']?.message?.toString(),
                ]
                  .filter((item) => item)
                  .join(' and ')}
              </div>
            )}
          </AlertDescription>
        </Alert>
      )
    );
  }

  return (
    <div className="flex flex-col gap-2">
      <div>
        <Label className="text-sm sm:text-base font-medium text-white">
          Email
        </Label>
        <InputField
          name="email"
          placeholder="<EMAIL>"
          form={form}
          validation={EMAIL_VALIDATION_RULEs}
          className="w-full"
          isShowError={false}
        />
      </div>

      <div>
        <Label className="text-sm sm:text-base font-medium text-white">
          Password
        </Label>
        <div className="relative">
          <InputField
            type={isPasswordVisible ? 'text' : 'password'}
            name="password"
            placeholder="Password"
            form={form}
            // validation={passwordValidationRules}
            validation={{ required: 'Password' }}
            className="w-full pr-12"
            isShowError={false}
          />
          <button
            type="button"
            className="absolute right-3 top-[10px]  text-gray-600 hover:text-gray-800 focus:outline-none transition-colors"
            onClick={handlePasswordVisibilityToggle}
          >
            {isPasswordVisible ? (
              <EyeOff className="w-5 h-5" />
            ) : (
              <Eye className="w-5 h-5" />
            )}
          </button>
        </div>
      </div>

      <div className="flex flex-row justify-between items-center">
        {/* <div className="flex gap-1 justify-center items-center">
          <AppCheckbox
            name="rememberMe"
            control={form.control}
            className=" data-[state=checked]:bg-[#04BCC2]"
          />
          <Label className="text-sm sm:text-base font-medium text-white">
            Remember Me
          </Label>
        </div>
        <div className="flex gap-1">
          <NavLink
            to={ROUTES.FORGOT_PASSWORD}
            className="text-sm sm:text-base font-medium text-white underline cursor-pointer"
          >
            Forgot Password
          </NavLink>
        </div> */}
      </div>

      {
        <div className="h-10">
          <AlertDestructive />
        </div>
      }
    </div>
  );
};

export default FormFields;
