import AppButton from '@/components/common/app-button';
import In<PERSON><PERSON>ield from '@/components/forms/input-field';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { UseToast } from '@/components/ui/toast/ToastContainer';
import { ROUTES } from '@/constants/routes-constants';
import {
  PASSWORD_VALIDATION_RULE,
  TEXT_VALIDATION_RULE,
} from '@/constants/validation-constants';
import { cn } from '@/lib/utils';
import { useSetpasswordMutation } from '@/redux/features/auth/authApi';
import { Eye, EyeOff, TriangleAlert } from 'lucide-react';
import { useState } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { useLocation, useNavigate } from 'react-router-dom';
import FormHeader from '../login/FormHeader';
interface ResetPasswordFormData {
  newPassword: string;
  confirmPassword: string;
  emailId: string;
  token: string;
}

const SetPasswordForm = ({ emailId }: { emailId: string }) => {
  const navigate = useNavigate();
  const location = useLocation();
  let token = location.search.replace('?token=', '');
  const [isNewPasswordVisible, setIsNewPasswordVisible] = useState(false);
  const [isConfirmPasswordVisible, setIsConfirmPasswordVisible] =
    useState(false);

  const [setpassword, { isLoading }] = useSetpasswordMutation();

  const handleNewPasswordVisibilityToggle = () => {
    setIsNewPasswordVisible((prev) => !prev);
  };

  const handleConfirmPasswordVisibilityToggle = () => {
    setIsConfirmPasswordVisible((prev) => !prev);
  };

  const form = useForm<ResetPasswordFormData>({
    defaultValues: {
      newPassword: '',
      confirmPassword: '',
      emailId,
      token: token || '',
    },
    // mode: 'onChange',
  });

  const onSubmit: SubmitHandler<ResetPasswordFormData> = async (data) => {
    try {
      let body = {
        token,
        email: emailId,
        newPassword: data.newPassword,
        confirmPassword: data.confirmPassword,
      };
      await setpassword(body)
        .unwrap()
        .then((response) => {
          if (response.statusCode === 200) {
            UseToast().success(response.message);
            navigate(ROUTES.LOGIN);
          } else {
            UseToast().error(response.message);
          }
        })
        .catch((error) => {
          UseToast().error(error.message);
        });

      //
    } catch (error) {
      // No need to show toast here, it's handled in the authApi
    }
  };
  const hasError =
    !!form.formState.errors['confirmPassword'] ||
    !!form.formState.errors['newPassword'];

  function AlertDestructive() {
    return (
      hasError && (
        <Alert
          variant="destructive"
          className="bg-[#FEE9E7] text-text-danger border-0 border-l-4 border-l-danger"
        >
          <TriangleAlert className="h-6 w-6 text-text-danger" />
          <AlertTitle className="text-lg font-semibold text-red-600 ml-1">
            Error
          </AlertTitle>
          <AlertDescription style={{ paddingLeft: '0px' }}>
            {hasError && (
              <div className="text-text-Default text-base">
                Please Enter a Valid{' '}
                {[
                  form.formState.errors['newPassword']?.message?.toString(),
                  form.formState.errors['confirmPassword']?.message?.toString(),
                ]
                  .filter((item) => item)
                  .join(' and ')}
              </div>
            )}
          </AlertDescription>
        </Alert>
      )
    );
  }

  return (
    <form
      className="flex flex-col gap-8"
      onSubmit={form.handleSubmit(onSubmit)}
    >
      <FormHeader title="Set Password" />
      <div className="flex flex-col gap-4">
        <div className="space-y-2">
          <div className="relative">
            <InputField
              label="Email Address"
              type={'text'}
              name="emailId"
              placeholder="Email"
              form={form}
              labelClassName="text-white"
              validation={TEXT_VALIDATION_RULE}
              isShowError={false}
              disabled
              className="w-full pr-12"
            />
          </div>
        </div>
        <div className="space-y-2">
          <div className="relative">
            <InputField
              label="New Password"
              labelClassName="text-white"
              type={isNewPasswordVisible ? 'text' : 'password'}
              name="newPassword"
              placeholder="New Password"
              form={form}
              validation={{
                required: 'New Password',
                minLength: {
                  value: 8,
                  message: 'Password must be at least 8 characters',
                },

                pattern: PASSWORD_VALIDATION_RULE,
              }}
              isShowError={false}
              className="w-full pr-12"
            />
            <button
              type="button"
              className="absolute right-3 top-[42px] text-gray-600 hover:text-gray-800 focus:outline-none transition-colors"
              onClick={handleNewPasswordVisibilityToggle}
            >
              {isNewPasswordVisible ? (
                <EyeOff className="w-5 h-5" />
              ) : (
                <Eye className="w-5 h-5" />
              )}
            </button>
          </div>
        </div>
        <div className="space-y-2">
          <div className="relative">
            <InputField
              label="Confirm Password"
              labelClassName="text-white"
              type={isConfirmPasswordVisible ? 'text' : 'password'}
              name="confirmPassword"
              placeholder="Confirm Password"
              form={form}
              validation={{
                required: 'Confirm Password',
                minLength: {
                  value: 8,
                  message: 'Password must be at least 8 characters',
                },
                validate: (value) =>
                  value === form.getValues('newPassword') ||
                  'Passwords do not match.',
              }}
              isShowError={false}
              className="w-full pr-12"
            />
            <button
              type="button"
              className="absolute right-3 top-[42px] text-gray-600 hover:text-gray-800 focus:outline-none transition-colors"
              onClick={handleConfirmPasswordVisibilityToggle}
            >
              {isConfirmPasswordVisible ? (
                <EyeOff className="w-5 h-5" />
              ) : (
                <Eye className="w-5 h-5" />
              )}
            </button>
          </div>
        </div>
        <div className="h-10">
          <AlertDestructive />
        </div>
      </div>
      <AppButton
        className={cn(
          'bg-brand-teal-Default text-white hover:bg-brand-teal-Default mt-32 w-full transition-colors sticky bottom-0'
        )}
        spinnerClass={'border-white-500  border-t-transparent animate-spin'}
        isLoading={isLoading}
        label="Set Password"
      />
    </form>
  );
};

export default SetPasswordForm;
