import { useState } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { Eye, EyeOff } from 'lucide-react';
import { Label } from '@/components/ui/label';
import InputField from '@/components/forms/input-field';
import {
  EMAIL_VALIDATION_RULEs,
  passwordValidationRules,
} from '@/constants/auth-constants';

interface FormFieldsProps {
  form: UseFormReturn<{
    companyName: string;
    companyAddress: string;
    companyPhone: string;
    contactName: string;
    email: string;
    password: string;
    timezone: string;
  }>;
}

export const FormFields = ({ form }: FormFieldsProps) => {
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);

  const handlePasswordVisibilityToggle = () => {
    setIsPasswordVisible((prev) => !prev);
  };

  return (
    <div className="grid grid-cols-2 gap-x-4">
      <div className="space-y-2">
        <Label className="text-sm sm:text-base font-medium text-white">
          Company Name
        </Label>
        <InputField
          name="companyName"
          placeholder="Company Name"
          form={form}
          className="w-full"
        />
      </div>
      <div className="space-y-2">
        <Label className="text-sm sm:text-base font-medium text-white">
          Company Address
        </Label>
        <InputField
          name="companyAddress"
          placeholder="Company Address"
          form={form}
          className="w-full"
        />
      </div>
      <div className="space-y-2">
        <Label className="text-sm sm:text-base font-medium text-white">
          Company Phone
        </Label>
        <InputField
          name="companyPhone"
          placeholder="Company Phone"
          form={form}
          className="w-full"
        />
      </div>

      <div className="space-y-2">
        <Label className="text-sm sm:text-base font-medium text-white">
          Contact Name
        </Label>
        <InputField
          name="contactName"
          placeholder="Contact Name"
          form={form}
          className="w-full"
        />
      </div>

      <div className="space-y-2">
        <Label className="text-sm sm:text-base font-medium text-white">
          Email
        </Label>
        <InputField
          name="email"
          placeholder="<EMAIL>"
          form={form}
          validation={EMAIL_VALIDATION_RULEs}
          className="w-full"
        />
      </div>

      <div className="space-y-2">
        <Label className="text-sm sm:text-base font-medium text-white">
          Password
        </Label>
        <div className="relative">
          <InputField
            type={isPasswordVisible ? 'text' : 'password'}
            name="password"
            placeholder="Password"
            form={form}
            validation={passwordValidationRules}
            className="w-full pr-12"
          />
          <button
            type="button"
            className="absolute right-3 top-5 -translate-y-1/2 text-gray-600 hover:text-gray-800 focus:outline-none transition-colors"
            onClick={handlePasswordVisibilityToggle}
          >
            {isPasswordVisible ? (
              <EyeOff className="w-5 h-5" />
            ) : (
              <Eye className="w-5 h-5" />
            )}
          </button>
        </div>
      </div>
      <div className="space-y-2">
        <Label className="text-sm sm:text-base font-medium text-white">
          TimeZone
        </Label>
        <InputField
          name="timezone"
          placeholder="TimeZone"
          form={form}
          className="w-full"
        />
      </div>
    </div>
  );
};

export default FormFields;
