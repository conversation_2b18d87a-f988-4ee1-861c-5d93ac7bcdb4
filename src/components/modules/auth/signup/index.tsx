import ClientEditIcon from '@/assets/icons/UserEdit';
import AppButton from '@/components/common/app-button';
import Input<PERSON>ield from '@/components/forms/input-field';
import PhoneInputWidget from '@/components/forms/phone-input-mask';
import SelectDropDown from '@/components/forms/select-dropdown';
import ZipCodeInput from '@/components/forms/zipcode-input';
import { ROUTES } from '@/constants/routes-constants';
import { INVITE_CLIENT_API } from '@/constants/tenant-constants';
import {
  PASSWORD_VALIDATION_RULE,
  TEXT_VALIDATION_RULE,
  WEB_SITE_VALIDATION_RULE,
} from '@/constants/validation-constants';
import { cn, generateLabelValuePairs } from '@/lib/utils';
import { useAddNewItemMutation } from '@/redux/features/common-api/common.api';
import {
  useGetCountryListQuery,
  useGetStateByCountryQuery,
} from '@/redux/features/country/country.api';
import { useGetTimeZoneListQuery } from '@/redux/features/timezone/timezone.api';
import { CountryType, TimeZoneType } from '@/types/customer.types';
import { ClientDTO } from '@/types/tenant.types';
import { Eye, EyeOff } from 'lucide-react';
import React, { useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useLocation, useNavigate } from 'react-router-dom';

export const FormItemWrapper = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  return <div className="col-span-2">{children}</div>;
};

const SignupForm = ({ email }: { email: string }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const queryParams = new URLSearchParams(location.search);
  const token = queryParams.get('token');
  const [addNewItem, { isLoading }] = useAddNewItemMutation();

  const { data: countryData } = useGetCountryListQuery();
  const { data: timeZoneData } = useGetTimeZoneListQuery();
  const [selectedCountry, setSelectedCountry] = useState(1);
  const { data: statesData } = useGetStateByCountryQuery({
    countryId: selectedCountry,
  });

  const [stateList, setStateList] = useState<any[]>([]);
  const [isNewPasswordVisible, setIsNewPasswordVisible] = useState(false);
  const [isConfirmPasswordVisible, setIsConfirmPasswordVisible] =
    useState(false);

  const handleNewPasswordVisibilityToggle = () => {
    setIsNewPasswordVisible((prev) => !prev);
  };

  const handleConfirmPasswordVisibilityToggle = () => {
    setIsConfirmPasswordVisible((prev) => !prev);
  };

  const defaultValues = useMemo(() => {
    return {
      token: token || '',
      clientName: '',
      contactName: '',
      contactEmail: email || '',
      contactPhone: '',
      location: '',
      website: '',
      addressLine1: '',
      addressLine2: '',
      city: '',
      state: '',
      zipcode: '',
      country: 'USA',
      schemaName: '',
      timezoneId: '',
      confirmPassword: '',
    };
  }, [email, token]);

  // Initialize form with default values for email and token
  const form = useForm<ClientDTO>({
    defaultValues,
    // mode: 'onChange',
  });
  const { handleSubmit } = form;

  const country = form.watch('country');
  const isUSA = ['USA']?.includes(country);

  useEffect(() => {
    if (token && email) {
    }
  }, [email, token]);

  const countryList = generateLabelValuePairs({
    data: countryData as CountryType[],
    labelKey: 'name',
    valueKey: 'name',
  });

  const timeZoneList = generateLabelValuePairs({
    data: timeZoneData as TimeZoneType[],
    labelKey: 'displayName',
    valueKey: 'id',
  });

  useEffect(() => {
    if (statesData && selectedCountry) {
      const newList = generateLabelValuePairs({
        data: statesData,
        labelKey: 'code',
        valueKey: 'name',
      });
      setStateList(newList);
    }
  }, [statesData, selectedCountry]);

  const handleRegistration = async (data: ClientDTO) => {
    await addNewItem({
      url: INVITE_CLIENT_API.REGISTER_CLIENT,
      data,
    })
      .unwrap()
      .then(() => {
        navigate(ROUTES.LOGIN);
      });
  };

  const labelClass = 'text-[#fff]';

  return (
    <div className="w-full h-full overflow-hidden bg-white/10 rounded-2xl lg:px-16 lg:py-20 md:p-12 p-10 flex flex-col justify-center">
      <h3 className="flex items-center gap-3 text-[38px] font-semibold text-[#fff] mb-10">
        <ClientEditIcon /> Client Registration
      </h3>
      {/* <Separator
        aria-orientation="horizontal"
        className="h-[1px] bg-border-Default mb-4"
      /> */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-x-4 gap-y-6">
        {/* Company Name  */}
        <FormItemWrapper>
          <InputField
            form={form}
            name="clientName"
            label="Company Name "
            placeholder="Enter Company Name"
            validation={{ required: 'Required' }}
            labelClassName={labelClass}
            maxLength={32}
            errorDisplayMode
          />
        </FormItemWrapper>

        {/* Contact Name */}
        <FormItemWrapper>
          <InputField
            form={form}
            name="contactName"
            labelClassName={labelClass}
            label="Contact Name"
            placeholder="Enter Contact Name"
            validation={{
              required: 'Required',
            }}
            maxLength={64}
            errorDisplayMode
          />
        </FormItemWrapper>

        {/* Contact Email */}
        <FormItemWrapper>
          <InputField
            form={form}
            name="contactEmail"
            disabled={true}
            label="Email"
            placeholder="Enter Contact Email"
            validation={{
              required: 'Required',
            }}
            labelClassName={labelClass}
          />
        </FormItemWrapper>

        {/* Contact Phone */}
        <FormItemWrapper>
          <PhoneInputWidget
            name="contactPhone"
            label="Contact Phone"
            placeholder="Enter Contact Phone"
            form={form}
            error={form.formState.errors}
            validation={TEXT_VALIDATION_RULE}
            labelClassName={labelClass}
            errorDisplayMode
          />
        </FormItemWrapper>
        {/* Location */}
        <FormItemWrapper>
          <InputField
            form={form}
            name="location"
            label="Store Location (Default)"
            placeholder="Enter Store Location"
            labelClassName={labelClass}
            maxLength={32}
            validation={{
              required: 'Required',
            }}
            errorDisplayMode
          />
        </FormItemWrapper>
        {/* Website */}
        <FormItemWrapper>
          <InputField
            form={form}
            name="website"
            label="Website"
            placeholder="Enter Website"
            labelClassName={labelClass}
            validation={WEB_SITE_VALIDATION_RULE}
            maxLength={64}
          />
        </FormItemWrapper>

        {/* Address Line 1 */}
        <FormItemWrapper>
          <InputField
            form={form}
            name="addressLine1"
            label="Address Line 1"
            placeholder="Enter Address Line 1"
            labelClassName={labelClass}
            maxLength={128}
          />
        </FormItemWrapper>

        {/* Address Line 2 */}
        <FormItemWrapper>
          <InputField
            form={form}
            name="addressLine2"
            label="Address Line 2"
            placeholder="Enter Address Line 2"
            labelClassName={labelClass}
            maxLength={128}
          />
        </FormItemWrapper>

        {/* City */}
        <FormItemWrapper>
          <InputField
            form={form}
            name="city"
            label="City"
            placeholder="Enter City"
            labelClassName={labelClass}
            maxLength={64}
          />
        </FormItemWrapper>

        {/* State */}
        <FormItemWrapper>
          <SelectDropDown
            form={form}
            name="state"
            label="State"
            optionsList={stateList}
            placeholder="Select State"
            validation={{ required: 'Required' }}
            labelClassName={labelClass}
            errorDisplayMode
          />
        </FormItemWrapper>

        {/* Zip Code */}
        <FormItemWrapper>
          {/* Zip Code */}
          <ZipCodeInput
            name="zipcode"
            isUSA={isUSA}
            form={form}
            label="Zip Code"
            labelClassName={labelClass}
            errorDisplayMode
          />
        </FormItemWrapper>

        {/* Country */}
        <FormItemWrapper>
          <SelectDropDown
            form={form}
            name="country"
            label="Country"
            optionsList={countryList}
            placeholder="Select Country"
            labelClassName={labelClass}
            onChange={(value) => {
              form.setValue('country', value);
              const newCountry = countryData?.find(
                (element) => element.name == value
              );
              form.setValue('state', '');
              form.setValue('zipcode', '');
              form.clearErrors('zipcode');
              setSelectedCountry(newCountry?.country_id as number);
            }}
            validation={{ required: 'Required' }}
            errorDisplayMode
          />
        </FormItemWrapper>
        {/* Timezone */}
        <FormItemWrapper>
          <SelectDropDown
            form={form}
            name="timezoneId"
            label="Timezone"
            optionsList={timeZoneList}
            labelClassName={labelClass}
            placeholder="Select Timezone"
            validation={{ required: 'Required' }}
            errorDisplayMode
          />
        </FormItemWrapper>
        <FormItemWrapper>
          <div className="relative">
            <InputField
              label="Password"
              labelClassName={labelClass}
              type={isNewPasswordVisible ? 'text' : 'password'}
              name="password"
              placeholder="Password"
              form={form}
              validation={{
                required: 'Required',
                minLength: {
                  value: 8,
                  message: 'Password must be at least 8 characters',
                },
                pattern: PASSWORD_VALIDATION_RULE,
              }}
              errorDisplayMode
              className="w-full pr-12"
            />
            <button
              type="button"
              className="absolute right-3 top-[42px] text-gray-600 hover:text-gray-800 focus:outline-none transition-colors"
              onClick={handleNewPasswordVisibilityToggle}
            >
              {isNewPasswordVisible ? (
                <EyeOff className="w-5 h-5" />
              ) : (
                <Eye className="w-5 h-5" />
              )}
            </button>
          </div>
        </FormItemWrapper>

        <FormItemWrapper>
          <div className="relative">
            <InputField
              label=" Confirm Password"
              labelClassName={labelClass}
              type={isConfirmPasswordVisible ? 'text' : 'password'}
              name="confirmPassword"
              placeholder="Confirm Password"
              form={form}
              validation={{
                required: 'Required',
                minLength: {
                  value: 8,
                  message: 'Password must be at least 8 characters',
                },
                validate: (value) =>
                  value === form.getValues('password') ||
                  'Passwords do not match.',
              }}
              errorDisplayMode
              className="w-full pr-12"
            />
            <button
              type="button"
              className="absolute right-3 top-[42px] text-gray-600 hover:text-gray-800 focus:outline-none transition-colors"
              onClick={handleConfirmPasswordVisibilityToggle}
            >
              {isConfirmPasswordVisible ? (
                <EyeOff className="w-5 h-5" />
              ) : (
                <Eye className="w-5 h-5" />
              )}
            </button>
          </div>
        </FormItemWrapper>
      </div>

      <AppButton
        isLoading={isLoading}
        className={cn(
          'bg-brand-teal-Default text-white hover:bg-brand-teal-Default mt-20 w-full transition-colors sticky bottom-0'
        )}
        spinnerClass={'border-white-500  border-t-transparent animate-spin'}
        label="Register"
        onClick={handleSubmit(handleRegistration)}
      />
    </div>
  );
};

export default SignupForm;
