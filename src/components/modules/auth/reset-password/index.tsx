import InputField from '@/components/forms/input-field';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import {
  EMAIL_VALIDATION_RULEs,
  passwordValidationRules,
} from '@/constants/auth-constants';
import { ROUTES } from '@/constants/routes-constants';
import { Eye, EyeOff, LogIn } from 'lucide-react';
import { useState } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import FormHeader from '../login/FormHeader';

interface ResetPasswordFormData {
  resetCode: string;
  email: string;
  newPassword: string;
  confirmPassword: string;
}

const ResetPasswordForm = () => {
  const navigate = useNavigate();
  const [isNewPasswordVisible, setIsNewPasswordVisible] = useState(false);
  const [isConfirmPasswordVisible, setIsConfirmPasswordVisible] =
    useState(false);

  const handleNewPasswordVisibilityToggle = () => {
    setIsNewPasswordVisible((prev) => !prev);
  };

  const handleConfirmPasswordVisibilityToggle = () => {
    setIsConfirmPasswordVisible((prev) => !prev);
  };

  const form = useForm<ResetPasswordFormData>({
    defaultValues: {
      email: '',
      newPassword: '',
      confirmPassword: '',
    },
    // mode: 'onChange',
  });

  const onSubmit: SubmitHandler<ResetPasswordFormData> = async () => {
    try {
      navigate(ROUTES.LOGIN);
    } catch (error) {
      // No need to show toast here, it's handled in the authApi
    }
  };

  return (
    <form
      className="flex flex-col gap-8"
      onSubmit={form.handleSubmit(onSubmit)}
    >
      <FormHeader title="Reset Password" />
      <div className="flex flex-col gap-2">
        <div className="space-y-2">
          <Label className="text-sm sm:text-base font-medium text-white">
            Verification Code
          </Label>
          <InputField
            name="resetCode"
            placeholder="Verification Code"
            form={form}
            className="w-full"
          />
        </div>

        <div className="space-y-2">
          <Label className="text-sm sm:text-base font-medium text-white">
            Email
          </Label>
          <InputField
            name="email"
            placeholder="<EMAIL>"
            form={form}
            validation={EMAIL_VALIDATION_RULEs}
            className="w-full"
          />
        </div>

        <div className="space-y-2">
          <Label className="text-sm sm:text-base font-medium text-white">
            New Password
          </Label>
          <div className="relative">
            <InputField
              type={isNewPasswordVisible ? 'text' : 'password'}
              name="newPassword"
              placeholder="New Password"
              form={form}
              validation={passwordValidationRules}
              className="w-full pr-12"
            />
            <button
              type="button"
              className="absolute right-3 top-5 -translate-y-1/2 text-gray-600 hover:text-gray-800 focus:outline-none transition-colors"
              onClick={handleNewPasswordVisibilityToggle}
            >
              {isNewPasswordVisible ? (
                <EyeOff className="w-5 h-5" />
              ) : (
                <Eye className="w-5 h-5" />
              )}
            </button>
          </div>
        </div>
        <div className="space-y-2">
          <Label className="text-sm sm:text-base font-medium text-white">
            Confirm Password
          </Label>
          <div className="relative">
            <InputField
              type={isConfirmPasswordVisible ? 'text' : 'password'}
              name="confirmPassword"
              placeholder="Confirm Password"
              form={form}
              validation={{
                ...passwordValidationRules,
                validate: (value) =>
                  value === form.getValues('newPassword') ||
                  'Passwords do not match',
              }}
              className="w-full pr-12"
            />
            <button
              type="button"
              className="absolute right-3 top-5 -translate-y-1/2 text-gray-600 hover:text-gray-800 focus:outline-none transition-colors"
              onClick={handleConfirmPasswordVisibilityToggle}
            >
              {isConfirmPasswordVisible ? (
                <EyeOff className="w-5 h-5" />
              ) : (
                <Eye className="w-5 h-5" />
              )}
            </button>
          </div>
        </div>
      </div>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <Button
          type="submit"
          className="bg-white text-brand-violet hover:bg-brand-violet hover:text-white w-full transition-colors"
        >
          Submit
        </Button>
        <Button
          onClick={() => navigate(ROUTES.SIGNUP)}
          type="button"
          className="bg-transparent border border-white text-white hover:bg-white/10 w-full transition-colors"
        >
          Sign Up <LogIn className="ml-2 w-4 h-4" />
        </Button>
      </div>
    </form>
  );
};

export default ResetPasswordForm;
