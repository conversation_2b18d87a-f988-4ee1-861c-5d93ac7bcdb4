import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { useResetpasswordMutation } from '@/redux/features/auth/authApi';
import { useCallback } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import FormFields from './FormFields';
import FormHeader from './FormHeader';
import { useNavigate } from 'react-router-dom';
import { ROUTES } from '@/constants/routes-constants';

interface ForgotPasswordFormData {
  email: string;
}

const ForgotPasswordForm = () => {
  const navigate = useNavigate();
  const [resetpassword, { isLoading }] = useResetpasswordMutation();

  const form = useForm<ForgotPasswordFormData>({
    defaultValues: {
      email: '',
    },
    // mode: 'onChange',
  });
  // const email = form.watch('email');

  // const isDisabled = !(email?.length > 0);

  const onSubmit: SubmitHandler<ForgotPasswordFormData> = useCallback(
    async (data) => {
      try {
        await resetpassword({ ...data }).unwrap();

        navigate(ROUTES.LOGIN);
      } catch (error) {
        // No need to show toast here, it's handled in the authApi
      }
    },
    [navigate, resetpassword]
  );

  return (
    <div className="w-full h-full overflow-hidden bg-white/10 rounded-2xl lg:px-16 lg:py-28 md:p-12 p-10 flex flex-col justify-center">
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <div className="flex flex-col gap-8">
          <FormHeader title="Forgot Password" />
          <FormFields form={form} />
        </div>

        <Button
          disabled={isLoading}
          type="submit"
          className={cn(
            'bg-brand-teal-Default text-white hover:bg-brand-teal-Default mt-32',
            'w-full transition-colors sticky bottom-0'
          )}
        >
          Reset Password
        </Button>
      </form>
    </div>
  );
};

export default ForgotPasswordForm;
