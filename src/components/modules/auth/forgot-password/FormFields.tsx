import InputField from '@/components/forms/input-field';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Label } from '@/components/ui/label';
import { InfoIcon, TriangleAlert } from 'lucide-react';
import { UseFormReturn } from 'react-hook-form';

interface FormFieldsProps {
  form: UseFormReturn<{
    email: string;
  }>;
}

const EMAIL_VALIDATION_RULEs = {
  required: 'Email',
  pattern: {
    value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
    message: 'Email',
  },
};

const FormFields = ({ form }: FormFieldsProps) => {
  const hasError = !!form.formState.errors['email'];

  function AlertDestructive() {
    return (
      hasError && (
        <Alert
          variant="destructive"
          className="bg-[#FEE9E7] text-text-danger border-0 border-l-4 border-l-danger"
        >
          <TriangleAlert className="h-6 w-6 text-text-danger" />
          <AlertTitle className="text-lg font-semibold text-red-600 ml-1">
            Error
          </AlertTitle>
          <AlertDescription style={{ paddingLeft: '0px' }}>
            {hasError && (
              <div className="text-text-Default text-base">
                Please enter a valid{' '}
                {[form.formState.errors['email']?.message?.toString()]
                  .filter((item) => item)
                  .join(' and ')}
              </div>
            )}
          </AlertDescription>
        </Alert>
      )
    );
  }

  return (
    <div className="flex flex-col gap-2">
      <div>
        <Label className="text-sm sm:text-base font-medium text-white">
          Email
        </Label>
        <InputField
          name="email"
          placeholder="<EMAIL>"
          form={form}
          validation={EMAIL_VALIDATION_RULEs}
          className="w-full"
          isShowError={false}
        />
      </div>

      <div className="mt-2 flex gap-2">
        <div className="mt-0.5 w-5 h-5">
          <InfoIcon className="text-white w-full h-full" />
        </div>
        <div className="text-white w-full">
          An email will be send to this address which contains the Link to reset
          your password. Link expires in hours.
        </div>
      </div>

      <div className="h-10">
        <AlertDestructive />
      </div>
    </div>
  );
};

export default FormFields;
