import AppButton from '@/components/common/app-button';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import DataTable from '@/components/common/data-tables';
import CustomDialog from '@/components/common/dialog';
import SwitchField from '@/components/common/switch';
import DatePicker from '@/components/forms/date-picker';
import Labels from '@/components/forms/Label';
import RadioField from '@/components/forms/radio-field';
import SelectWidget from '@/components/forms/select';
import { STORE_OPTIONS_API_ROUTES } from '@/constants/api-constants';
import {
  dateOptions,
  invoiceOptionsMethods,
  printEmailOptions,
} from '@/constants/processes-constants';
import useOptionList from '@/hooks/useOptionList';
import { DEFAULT_FORMAT, formatDate } from '@/lib/utils';
import {
  DateOptions,
  InvoiceOptionsMethod,
  InvoicesDeliveriesPackingListsType,
  PrintEmailOptions,
} from '@/types/processes.types';
import { RowSelectionState } from '@tanstack/react-table';
import { FileText, PrinterCheck, PrinterIcon, X } from 'lucide-react';
import { memo, useCallback, useEffect, useMemo, useState } from 'react';
import { useForm, useWatch } from 'react-hook-form';

interface InvoicesDeliveriesPackingListsProps {
  toggle: () => void;
  enableCheckbox?: boolean;
}

const InvoicesDeliveriesPackingLists = ({
  toggle,
  enableCheckbox,
}: InvoicesDeliveriesPackingListsProps) => {
  const today = formatDate(new Date(), DEFAULT_FORMAT);
  const form = useForm<InvoicesDeliveriesPackingListsType>();

  const [disableDateFrom, setDisableDateFrom] = useState(false);
  const [disableDateThru, setDisableDateThru] = useState(false);
  const [invoiceOrdersModel, setInvoiceOrdersModel] = useState(false);
  const [openPrintEmailDialog, setOpenPrintEmailDialog] = useState(false);
  const [selectedRows, setSelectedRows] = useState<RowSelectionState>({});

  const selectedDateOption = useWatch({
    control: form.control,
    name: 'dateOptions',
  });

  const { options: storeLocationList, optionLoading } = useOptionList({
    url: STORE_OPTIONS_API_ROUTES.ALL,
    labelKey: 'locationName',
    valueKey: 'id',
    sortBy: 'locationName',
    extraKey: 'isDefault',
  });

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const locationList = storeLocationList?.length
    ? [...[{ label: 'All Locations', value: '0' }], ...storeLocationList]
    : [];

  const defaultStoreLocation = useMemo(() => {
    return locationList?.find((opt: any) => opt.extraKey);
  }, [locationList]);

  useEffect(() => {
    const subscription = form.watch((value) => {
      const selected = value.invoiceOptions;

      if (selected === 'IncludeOrders') {
        setDisableDateFrom(true);
        setDisableDateThru(false);
      } else if (selected === 'InvoiceOrders') {
        setDisableDateFrom(false);
        setDisableDateThru(false);
      } else if (selected === 'ReInvoiceOrders') {
        setDisableDateFrom(true);
        setDisableDateThru(true);
      }
    });

    return () => subscription.unsubscribe();
  }, [form]);

  const defaultValues = useMemo(() => {
    return {
      location: defaultStoreLocation?.value || 1,
      deliveryDateFrom: today,
      deliveryDateThru: today,
      invoiceOptions: InvoiceOptionsMethod.IncludeOrders,
      dateOptions: DateOptions.DeliveryDate,
      printEmailOptions: PrintEmailOptions.DefaultInvoices,
    };
  }, [defaultStoreLocation?.value, today]);

  useEffect(() => {
    if (defaultValues) {
      form.reset(defaultValues);
    }
  }, [defaultValues, form]);

  // const handlePrint = async (values: InvoicesDeliveriesPackingListsType) => {
  //   try {
  //     const payload = {
  //       locationId: values.location,
  //       invoiceOption: values.invoiceOptions,
  //       dateOption: values.dateOptions,
  //       deliveryDateFrom: values.deliveryDateFrom,
  //       deliveryDateThru: values.deliveryDateThru,
  //       doNotPrintInvoices: values.doNotPrintInvoices || false,
  //     };

  //     console.log('Payload:', payload);

  //      const result = await generateInvoices(payload);
  //      console.log('Invoices generated:', result);
  //   } catch (error) {}
  // };

  const handleConfirmInvoiceOrders = () => {
    setInvoiceOrdersModel(false);
  };

  const handleInvoiceOptionChange = (value: string) => {
    if (value === 'InvoiceOrders') {
      setInvoiceOrdersModel(true); // ✅ open popup once
    }
  };

  const onOpenChangePrintEmailDialog = useCallback(() => {
    setOpenPrintEmailDialog((prev) => !prev);
  }, []);

  const columns = useMemo(() => {
    return [
      {
        accessorKey: 'form',
        header: 'Form',
        size: 80,
      },
      {
        accessorKey: 'copies',
        header: 'Copies',
        size: 200,
      },
      {
        accessorKey: 'printer',
        header: 'Printer',
        size: 100,
      },
      {
        accessorKey: 'faxPrinter',
        header: 'Fax Printer',
        size: 100,
      },
    ];
  }, []);

  return (
    <>
      <div className="pl-6 pr-6">
        <div className="flex flex-col gap-6">
          <SelectWidget
            form={form}
            optionsList={locationList ?? []}
            name="location"
            label="Location"
            placeholder="Select Location"
            isLoading={optionLoading}
            menuPosition="absolute"
            menuPlacement="bottom"
            isClearable={false}
          />
          <div className="p-4 rounded-md border flex flex-col">
            <Labels label="Invoicing Options" className="pb-5" />
            <RadioField
              form={form}
              name="invoiceOptions"
              options={invoiceOptionsMethods}
              optionsPerRow={1}
              onChange={handleInvoiceOptionChange}
            />
          </div>

          <div className="p-4 rounded-md border flex flex-col gap-4">
            <RadioField
              form={form}
              name="dateOptions"
              options={dateOptions}
              optionsPerRow={2}
            />
            <DatePicker
              form={form}
              name="deliveryDateFrom"
              label={
                selectedDateOption === 'DateOfUse'
                  ? 'Date of Use From'
                  : 'Delivery Date From'
              }
              placeholder="Select Date"
              enableInput
              disabled={disableDateFrom}
            />
            <DatePicker
              form={form}
              name="deliveryDateThru"
              label={
                selectedDateOption === 'DateOfUse'
                  ? 'Date of Use Thru'
                  : 'Delivery Date Thru'
              }
              placeholder="Select Date"
              enableInput
              disabled={disableDateThru}
            />

            {enableCheckbox && (
              <SwitchField
                form={form}
                name="doNotPrintInvoices"
                labelEnd="Do not print invoices"
                className="w-fit pt-3"
              />
            )}
          </div>
        </div>

        <div className="w-full h-full justify-end flex gap-4 pt-4 sticky bottom-0 bg-white z-30">
          <AppButton
            className="w-28"
            label="Print"
            icon={PrinterIcon}
            iconClassName="w-5 h-5"
            onClick={onOpenChangePrintEmailDialog}
          />
          <AppButton
            className="w-28"
            label="Close"
            onClick={toggle}
            variant="neutral"
            icon={X}
            iconClassName="w-5 h-5"
          />
        </div>
      </div>
      <AppConfirmationModal
        open={invoiceOrdersModel}
        title="Warning"
        description={
          'This option will cause duplicate invoices to be printed for all orders already invoiced.'
        }
        handleSubmit={handleConfirmInvoiceOrders}
        submitLabel="Ok"
      />
      <CustomDialog
        open={openPrintEmailDialog}
        onOpenChange={onOpenChangePrintEmailDialog}
        title="Print/E-mail"
        contentClassName="py-2"
        className="xl:max-w-6xl overflow-auto"
      >
        <div className="px-6 flex flex-col gap-4">
          <div>
            <div className="flex gap-2 pb-2">
              <AppButton
                icon={PrinterIcon}
                label=""
                tooltip="Print"
                className={
                  'bg-brand-teal-Default hover:bg-brand-teal-secondary rounded-[50%] w-10 h-10'
                }
              />
              <AppButton
                icon={PrinterCheck}
                label=""
                tooltip="Fax"
                className={
                  'bg-brand-teal-Default hover:bg-brand-teal-secondary rounded-[50%] w-10 h-10'
                }
              />
              <AppButton
                icon={FileText}
                label=""
                tooltip="PDF"
                className={
                  'bg-brand-teal-Default hover:bg-brand-teal-secondary rounded-[50%] w-10 h-10'
                }
              />
            </div>
            <DataTable
              data={[]}
              columns={columns}
              enablePagination={false}
              loaderRows={11}
              tableClassName="max-h-[500px] 2xl:max-h-[550px] overflow-auto"
              manualSorting={false}
              noDataPlaceholder="No Data Found."
              enableRowSelection
              rowSelection={selectedRows}
              onRowSelectionChange={setSelectedRows}
              enableMultiRowSelection
            />
            <div className="p-4 rounded-md border flex flex-col mt-4">
              <Labels label="Auto-Select Options" className="pb-5" />
              <RadioField
                form={form}
                name="printEmailOptions"
                options={printEmailOptions}
                optionsPerRow={2}
                rowClassName="grid grid-cols-4 w-full"
                className="space-y-4"
              />
            </div>
          </div>
          <div className="flex flex-col gap-4">
            <div className="flex justify-end bottom-3 pt-3 right-6 space-x-4">
              <AppButton
                onClick={() => {
                  onOpenChangePrintEmailDialog();
                }}
                label="Close"
                className="w-28"
                variant="neutral"
                icon={X}
                iconClassName="w-5 h-5"
              />
            </div>
          </div>
        </div>
      </CustomDialog>
    </>
  );
};

export default memo(InvoicesDeliveriesPackingLists);
