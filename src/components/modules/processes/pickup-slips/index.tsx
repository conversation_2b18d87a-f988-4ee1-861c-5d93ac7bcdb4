import AppButton from '@/components/common/app-button';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import SwitchField from '@/components/common/switch';
import DatePicker from '@/components/forms/date-picker';
import SelectWidget from '@/components/forms/select';
import { STORE_OPTIONS_API_ROUTES } from '@/constants/api-constants';
import useOptionList from '@/hooks/useOptionList';
import { DEFAULT_FORMAT, formatDate } from '@/lib/utils';
import { PickupSlipsType, PrintDialogType } from '@/types/processes.types';
import { PrinterIcon, X } from 'lucide-react';
import { memo, useCallback, useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import PrintDialog from '../PrintDialog';

interface PickupSlipsProps {
  toggle: () => void;
}

const PickupSlips = ({ toggle }: PickupSlipsProps) => {
  const today = formatDate(new Date(), DEFAULT_FORMAT);
  const form = useForm<PickupSlipsType>();
  const printForm = useForm<PrintDialogType>();

  const [invoiceOrdersModel, setInvoiceOrdersModel] = useState(false);
  const [openPrintDialog, setOpenPrintDialog] = useState(false);

  const { options: storeLocationList, optionLoading } = useOptionList({
    url: STORE_OPTIONS_API_ROUTES.ALL,
    labelKey: 'locationName',
    valueKey: 'id',
    sortBy: 'locationName',
    extraKey: 'isDefault',
  });

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const locationList = storeLocationList?.length
    ? [...[{ label: 'All Locations', value: '0' }], ...storeLocationList]
    : [];

  const defaultStoreLocation = useMemo(() => {
    return locationList?.find((opt: any) => opt.extraKey);
  }, [locationList]);

  const toggleInvoiceOrdersModel = useCallback(() => {
    setInvoiceOrdersModel((prevState) => !prevState);
  }, []);

  const defaultValues = useMemo(() => {
    return {
      location: defaultStoreLocation?.value || 1,
      pickupDateFrom: today,
      pickupDateThru: today,
      ordersPickedUp: true,
      ordersBeingReturnedByCustomers: true,
    };
  }, [defaultStoreLocation?.value, today]);

  useEffect(() => {
    if (defaultValues) {
      form.reset(defaultValues);
    }
  }, [defaultValues, form]);

  // const handlePickupSlips = async (values: PickupSlipsType) => {
  //   try {
  //     const payload = {
  //       ...values,
  //       location: Number(values.location), // Ensure it's a number
  //       pickupDateFrom: values.pickupDateFrom || today,
  //       pickupDateThru: values.pickupDateThru || today,
  //     };

  //     console.log('Payload:', payload);

  //     // const result = await generatePickupSlips(payload);
  //     // console.log('Pickup Slips generated:', result);
  //   } catch (error) {}
  // };

  const onOpenChangePrintDialog = useCallback(() => {
    setOpenPrintDialog((prev) => !prev);
  }, []);

  return (
    <>
      <div className="pl-6 pr-6">
        <div className="flex flex-col gap-6">
          <SelectWidget
            form={form}
            optionsList={locationList ?? []}
            name="location"
            label="Location"
            placeholder="Select Location"
            isLoading={optionLoading}
            menuPosition="absolute"
            menuPlacement="bottom"
            isClearable={false}
          />

          <div className="p-4 rounded-md border flex flex-col gap-4">
            <DatePicker
              form={form}
              name="pickupDateFrom"
              label="Pickup Date From"
              placeholder="Select Date"
              enableInput
            />
            <DatePicker
              form={form}
              name="pickupDateThru"
              label="Pickup Date Thru"
              placeholder="Select Date"
              enableInput
            />

            <SwitchField
              form={form}
              name="ordersPickedUp"
              labelEnd="Orders to be picked up"
              className="w-fit pt-3"
            />
            <SwitchField
              form={form}
              name="ordersBeingReturnedByCustomers"
              labelEnd="Orders being returned by customers (CR)"
              className="w-fit"
            />
          </div>
        </div>

        <div className="w-full h-full justify-end flex gap-4 pt-4 sticky bottom-0 bg-white z-30">
          <AppButton
            className="w-28"
            label="Print"
            icon={PrinterIcon}
            iconClassName="w-5 h-5"
            onClick={onOpenChangePrintDialog}
          />
          <AppButton
            className="w-28"
            label="Close"
            onClick={toggle}
            variant="neutral"
            icon={X}
            iconClassName="w-5 h-5"
          />
        </div>
      </div>
      <AppConfirmationModal
        open={invoiceOrdersModel}
        title="Warning"
        description={
          'This option will cause duplicate invoices to be printed for all orders already invoiced.'
        }
        handleSubmit={toggleInvoiceOrdersModel}
        submitLabel="Ok"
      />
      <PrintDialog
        openPrintDialog={openPrintDialog}
        onOpenChangePrintDialog={onOpenChangePrintDialog}
        form={printForm}
      />
    </>
  );
};

export default memo(PickupSlips);
