import AppButton from '@/components/common/app-button';
// import { purgeCustomersType } from '@/types/processes.types';
import { memo } from 'react';
// import { useForm } from 'react-hook-form';

const PurgeCustomers = ({ toggle }: { toggle: () => void }) => {
  //   const form = useForm<purgeCustomersType>({});

  return (
    <>
      <div className="pl-6 pr-6">
        <div className="flex flex-col gap-6">
          <div>
            This program will delete all customers who do not have any order or
            quote records.
          </div>
        </div>

        <div className="w-full h-full justify-end flex gap-4 mt-4 sticky bottom-0 bg-white z-30">
          <AppButton className="w-28" label="Process" />
          <AppButton
            className="w-28"
            label="Close"
            onClick={toggle}
            variant="neutral"
          />
        </div>
      </div>
    </>
  );
};

export default memo(PurgeCustomers);
