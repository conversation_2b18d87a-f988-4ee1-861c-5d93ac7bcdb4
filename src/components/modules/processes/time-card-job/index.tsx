import AppButton from '@/components/common/app-button';
import CalendarView from '@/components/common/CalendarView';
import DataTable from '@/components/common/data-tables';
import InputField from '@/components/forms/input-field';
import SelectWidget from '@/components/forms/select';
import { formatDate } from '@/lib/utils';
import { TimeCardJobType } from '@/types/processes.types';
import dayjs from 'dayjs';
import { memo, useMemo } from 'react';
import { useForm } from 'react-hook-form';

const TimeCardJob = ({ toggle }: { toggle: () => void }) => {
  const form = useForm<TimeCardJobType>();
  const today = formatDate(new Date());

  const columns: any = useMemo(
    () => [
      {
        accessorKey: 'date',
        header: 'Date',
        cell: ({ row }: any) => formatDate(row?.original?.dateOfUseFrom),
        size: 20,
      },
      {
        accessorKey: 'hours',
        header: 'Hours',
        size: 20,
      },
    ],
    []
  );

  return (
    <div className="pl-6 pr-6">
      <div className="grid grid-cols-2 gap-6">
        <SelectWidget
          form={form}
          optionsList={[]}
          name="employee"
          label="Employee"
          placeholder="Select Employee"
          menuPosition="absolute"
        />
      </div>

      <div className="grid grid-cols-2 gap-6 mt-5 mb-5">
        <div>
          <CalendarView
            data={[]}
            value={dayjs(today)?.toDate()}
            onMonthChange={() => {}}
            onSelect={() => {}}
            className="max-w-30"
          />
        </div>

        <div className="flex flex-col gap-4">
          <DataTable
            data={[]}
            columns={columns}
            tableClassName="max-h-[350px] 2xl:max-h-[370px] overflow-auto z-0"
            enablePagination={false}
            manualSorting={false}
          />
          <InputField
            name="totalWeek"
            form={form}
            label="Total for Week"
            placeholder="______.__"
            disabled
          />
        </div>
      </div>

      <div className="w-full h-full justify-end flex gap-4 py-1 sticky bottom-0 bg-white z-30">
        <AppButton
          className="w-28"
          label="Close"
          onClick={toggle}
          variant="neutral"
        />
      </div>
    </div>
  );
};

export default memo(TimeCardJob);
