import AppButton from '@/components/common/app-button';
import DataTable from '@/components/common/data-tables';
import { useGetDepartmentsQuery } from '@/redux/features/customers/customer.api';
import { RowSelectionState } from '@tanstack/react-table';
import { CircleCheckBig, X } from 'lucide-react';
import { memo, useEffect, useMemo, useState } from 'react';

interface SelectDepartmentsProps {
  toggle: () => void;
}

const SelectDepartments = ({ toggle }: SelectDepartmentsProps) => {
  const [selectedRows, setSelectedRows] = useState<RowSelectionState>({});
  const { data: departmentData, isFetching } = useGetDepartmentsQuery();

  useEffect(() => {
    if (departmentData?.data?.length) {
      const allSelected: RowSelectionState = {};
      departmentData.data.forEach((_: any, index: number) => {
        allSelected[index] = true;
      });
      setSelectedRows(allSelected);
    }
  }, [departmentData]);

  const handleOk = () => {
    toggle();
  };

  const columns: any = useMemo(
    () => [
      {
        accessorKey: 'deptDesc',
        header: 'Department',
        size: 200,
      },
    ],
    []
  );

  return (
    <>
      <div className="px-6">
        <div className="grid grid-cols-1">
          <h1 className="pt-3 pb-3">Departments to print for</h1>
          <DataTable
            data={departmentData?.data ?? []}
            columns={columns}
            tableClassName="max-h-[350px] 2xl:max-h-[450px] overflow-auto"
            enablePagination={false}
            manualSorting={false}
            isLoading={isFetching}
            enableRowSelection
            rowSelection={selectedRows}
            onRowSelectionChange={setSelectedRows}
            enableMultiRowSelection
          />
        </div>
        <div className="flex justify-end gap-4 pb-4 fixed bottom-0 bg-white right-6">
          <AppButton
            className="w-28"
            label="Ok"
            icon={CircleCheckBig}
            iconClassName="w-5 h-5"
            onClick={handleOk}
          />
          <AppButton
            className="w-28"
            label="Cancel"
            onClick={handleOk}
            variant="neutral"
            icon={X}
            iconClassName="w-5 h-5"
          />
        </div>
      </div>
    </>
  );
};

export default memo(SelectDepartments);
