import AppButton from '@/components/common/app-button';
import AppSpinner from '@/components/common/app-spinner';
import ColumnReOrdering from '@/components/common/column-reording';
import DataTable from '@/components/common/data-tables';
import { getIconByName } from '@/lib/getIconByName';
import {
  useGetProcessesColumsQuery,
  useGetProcessesQuery,
  useUpdateProcessesColumsMutation,
} from '@/redux/features/processes/processes.api';
import { ProcessDialogAction } from '@/types/processes.types';
import { useCallback, useMemo, useState } from 'react';
import ProcessDialogRenderer from './ProcessesDialogRenderer';

const Processes = () => {
  const [openColumnOrdering, setOpenColumnOrdering] = useState<boolean>(false);

  // get reorganize columns
  const { data, isFetching: isLoading } = useGetProcessesColumsQuery();
  const tableColumns = data?.data;

  // update reorganize columns
  const [updateColums, { isLoading: updateColumIsLoading }] =
    useUpdateProcessesColumsMutation();

  // get processes list
  const { data: processesData, isLoading: processesLoading } =
    useGetProcessesQuery();

  // handle change reorganize columns
  const handleOrderingColumn = useCallback(
    async (formData: any) => {
      const updatedColumns = tableColumns?.map((item: any) => ({
        ...item,
        enabled: formData[item?.accessorKey],
      }));
      await updateColums(updatedColumns).unwrap();
      setOpenColumnOrdering(false);
    },
    [tableColumns, updateColums]
  );

  const memoizedColumns = useMemo(() => {
    if (!tableColumns?.length) return [];

    const formattedColumns = tableColumns
      ?.filter((column: any) => column?.enabled)
      ?.map((column: any) => ({
        accessorKey: column?.accessorKey,
        header: column?.header,
        enableSorting: column?.enableSorting || false,
        size: column?.size || 300,
      }));

    return [
      {
        accessorKey: 'processes',
        header: () => (
          <ColumnReOrdering
            isOpen={openColumnOrdering}
            handleOrderingColumn={handleOrderingColumn}
            tableColumns={tableColumns || []}
            setOpenColumnOrdering={setOpenColumnOrdering}
            className="max-h-52"
          />
        ),
        cell: ({ row }: any) => {
          const icon = getIconByName(row?.original?.icon);
          return <AppButton icon={icon} label="" className="h-9 px-2" />;
        },
        size: 50,
        maxSize: 80,
      },
      ...formattedColumns,
      {
        id: 'action',
        size: 110,
        header: 'Actions',
        cell: ({ row }: any) => (
          <ProcessDialogRenderer
            action={row?.original?.processId as ProcessDialogAction}
          />
        ),
      },
    ];
  }, [tableColumns, openColumnOrdering, handleOrderingColumn]);

  return (
    <div className="p-6">
      <DataTable
        data={processesData?.data || []}
        columns={memoizedColumns}
        heading="Processes"
        customToolBar
        enablePagination={false}
        isLoading={processesLoading || isLoading}
      />

      <AppSpinner isLoading={isLoading || updateColumIsLoading} overlay />
    </div>
  );
};

export default Processes;
