import AppButton from '@/components/common/app-button';
import DatePicker from '@/components/forms/date-picker';
import { DEFAULT_FORMAT, formatDate } from '@/lib/utils';
import { archiveARType } from '@/types/processes.types';
import { memo } from 'react';
import { useForm } from 'react-hook-form';

const ArchiveAR = ({ toggle }: { toggle: () => void }) => {
  const today = formatDate(new Date(), DEFAULT_FORMAT);
  const form = useForm<archiveARType>({
    defaultValues: {
      archiveARThru: today,
    },
  });

  return (
    <>
      <div className="pl-6 pr-6">
        <div className="flex flex-col gap-6">
          <div>
            This program will purge accounts receivable data from current tables
            to history tables. This information is still available to Party
            Track but is not accessed when viewing current information. Any
            accounts receivable information relating to an order with a date of
            use prior to the date entered below will be moved to history tables
            as long as the order is fully paid and the payments applied to the
            order are also prior to the date entered. In the situation where a
            single payment is applied to multiple orders the orders will only be
            moved to history tables if all orders related to that payment are
            fully paid with a date of use prior to the date entered below.
          </div>
          <div>
            Before you continue with this process you must have a good backup of
            your Party Track database and need to print an aged receivables
            report to validate against an aged receivables report that you will
            print after the purge process. You must not have other users in
            Party Track until this process is complete. This process must be
            planned and please notify us in advance so we are available to
            support you through this process.
          </div>
          <div className="grid grid-cols-2 gap-6">
            <DatePicker
              form={form}
              name="archiveARThru"
              label="Archive A/R Thru"
              placeholder="Select Date"
              enableInput
            />
          </div>
        </div>

        <div className="w-full h-full justify-end flex gap-4 mt-4 sticky bottom-0 bg-white z-30">
          <AppButton className="w-28" label="Process" />
          <AppButton
            className="w-28"
            label="Close"
            onClick={toggle}
            variant="neutral"
          />
        </div>
      </div>
    </>
  );
};

export default memo(ArchiveAR);
