import AppButton from '@/components/common/app-button';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import FormActionButtons from '@/components/common/FormActionButtons';
import { NumberInputField } from '@/components/forms';
import { TEXT_VALIDATION_RULE } from '@/constants/validation-constants';
import { useUnPostOrderMutation } from '@/redux/features/processes/processes.api';
import { UnpostType } from '@/types/processes.types';
import { SaveAll, X } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import Modal from '../../accounting/adjustments/add-edit-adjustments/Modal';

interface UnPostOrderProps {
  onSuccess: () => void;
}
const UnPostOrder = ({ onSuccess }: UnPostOrderProps) => {
  const [open, setOpen] = useState<boolean>(false);
  const [confirmation, setConfirmation] = useState<{
    isOpen: boolean;
    message: string;
  }>({ isOpen: false, message: '' });

  const form = useForm<UnpostType>({ defaultValues: { orderId: '' } });

  // upost order
  const [unPostOrder, { isLoading }] = useUnPostOrderMutation();

  // toggle unpost order
  const toggleOpen = () => {
    setOpen((prev) => !prev);
  };

  // toggle warning unpost confirmation
  const toggeleConfirmation = (message: string = '') => {
    setConfirmation((prev) => ({ isOpen: !prev?.isOpen, message: message }));
  };

  const onSubmit =
    (incZeroBalOrd: boolean = false) =>
    async (formatData: UnpostType) => {
      const { data } = await unPostOrder({
        orderId: formatData?.orderId,
        incZeroBalOrd,
      });

      if (data?.statusCode === 200) {
        if (data?.data?.requiresConfirmation) {
          toggeleConfirmation(data?.message);
          setOpen(false);
          return;
        }
        onSuccess();
        setOpen(false);
        confirmation?.isOpen && toggeleConfirmation();
      }
    };

  const orderId = form.watch('orderId');

  useEffect(() => {
    if (open) {
      form.reset();
    }
  }, [form, open]);

  return (
    <>
      <AppButton
        className="bg-brand-teal-Default hover:bg-brand-teal-secondar"
        label="Unpost"
        icon={SaveAll}
        onClick={toggleOpen}
      />
      <Modal
        open={open}
        onOpenChange={toggleOpen}
        title="Unpost"
        size="sm"
        height="auto"
        showHeader={true}
      >
        <NumberInputField
          name="orderId"
          form={form}
          label="Enter order number to unpost"
          placeholder="Order number"
          decimalScale={0}
          validation={TEXT_VALIDATION_RULE}
        />
        <FormActionButtons
          className="pb-2"
          onSubmit={form.handleSubmit(onSubmit())}
          disabledSubmitButton={!orderId}
          submitLabel="Unpost"
          onCancel={toggleOpen}
          isLoading={isLoading}
          cancelLabel="Cancel"
          submitIcon={SaveAll}
          cancelIcon={X}
        />
      </Modal>
      {confirmation?.isOpen && (
        <AppConfirmationModal
          open={confirmation?.isOpen}
          title="Warning"
          description={confirmation?.message}
          handleSubmit={form.handleSubmit(onSubmit(true))}
          submitLabel="Yes"
          handleCancel={toggeleConfirmation}
          isLoading={isLoading}
        />
      )}
    </>
  );
};

export default UnPostOrder;
