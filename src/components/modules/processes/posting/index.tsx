import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import DataTable from '@/components/common/data-tables';
import FormActionButtons from '@/components/common/FormActionButtons';
import SwitchField from '@/components/common/switch';
import DatePicker from '@/components/forms/date-picker';
import SelectWidget from '@/components/forms/select';
import { STORE_OPTIONS_API_ROUTES } from '@/constants/api-constants';
import { ComparisonOperator } from '@/constants/common-constants';
import useOptionList from '@/hooks/useOptionList';
import { formatDateWithTimezone } from '@/lib/formatDateWithTimezone';
import {
  convertToFloat,
  DATE_FORMAT_YYYYMMDD,
  DEFAULT_FORMAT,
  formatDate,
  getPaginationObject,
  getStorageValue,
} from '@/lib/utils';
import {
  useGetPostingOrdersQuery,
  usePostOrdersMutation,
} from '@/redux/features/processes/processes.api';
import { SortingStateType } from '@/types/common.types';
import { PostingListType, PostingType } from '@/types/processes.types';
import { ColumnDef, RowSelectionState } from '@tanstack/react-table';
import dayjs, { Dayjs } from 'dayjs';
import { maxBy, minBy } from 'lodash';
import { Save, X } from 'lucide-react';
import { memo, useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import UnPostOrder from './UnPosting';

const Posting = ({ toggle }: { toggle: () => void }) => {
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const [sorting, setSorting] = useState<SortingStateType[]>([
    { id: 'orderNo', desc: true },
  ]);
  const [openConfirmation, setOpenConfirmation] = useState<boolean>(false);

  const defaultLocationId = getStorageValue('defaultLocationId');
  const form = useForm<PostingType>();
  const yesterday = formatDateWithTimezone();

  // post order
  const [postOrders, { isLoading }] = usePostOrdersMutation();

  const defaultValues = useMemo(() => {
    return {
      locationid: Number(defaultLocationId),
      date: dayjs(yesterday).add(-1, 'day')?.toString(),
      excludeFuturePickups: false,
    };
  }, [defaultLocationId, yesterday]);

  useEffect(() => {
    if (defaultValues) {
      form.reset(defaultValues);
    }
  }, [defaultValues, form]);

  const locationId = form.watch('locationid');
  const date = form.watch('date');
  const excludeFuturePickups = form.watch('excludeFuturePickups');

  // payload
  const payload = useMemo(() => {
    return getPaginationObject({
      pagination: {
        pageSize: -1,
        pageIndex: 0,
      },
      sorting: sorting,
      filters: [
        {
          field: 'locationid',
          value: locationId?.toString(),
          operator: ComparisonOperator.EQUALS,
        },
        {
          field: 'date',
          value: formatDate(date, DATE_FORMAT_YYYYMMDD),
          operator: ComparisonOperator.EQUALS,
        },
        {
          field: 'excludeFuturePickups',
          value: excludeFuturePickups
            ? formatDate(date, DATE_FORMAT_YYYYMMDD)
            : '',
          operator: ComparisonOperator.EQUALS,
        },
      ],
    });
  }, [date, excludeFuturePickups, locationId, sorting]);

  // get the posting list
  const {
    data: postingData,
    isFetching,
    refetch,
  } = useGetPostingOrdersQuery(payload, {
    skip: !locationId || !date,
    refetchOnMountOrArgChange: true,
  });

  // get location list
  const { options: storeLocationList, optionLoading } = useOptionList({
    url: STORE_OPTIONS_API_ROUTES.ALL,
    labelKey: 'locationName',
    valueKey: 'id',
    sortBy: 'locationName',
  });
  // Location List
  const locationList = useMemo(() => {
    return storeLocationList?.length
      ? [{ label: 'All Locations', value: '0' }, ...storeLocationList]
      : [];
  }, [storeLocationList]);

  // reset the check if the new data get
  useEffect(() => {
    if (postingData?.data) {
      setRowSelection({});
    }
  }, [postingData]);

  // selected order Id
  const selectedOrderIds = Object.entries(rowSelection)?.map(([key]) =>
    Number(key)
  );

  // handle submit post
  const onSubmit = async () => {
    await postOrders(selectedOrderIds)?.unwrap();
    toggle();
  };

  const columns: ColumnDef<PostingListType>[] = useMemo(
    () => [
      {
        accessorKey: 'orderNo',
        header: 'Order #',
        enableSorting: true,
      },
      {
        accessorKey: 'dateofUse',
        header: 'Date of Use',
        cell: ({ row }) => formatDate(row?.original?.dateofUse),
        enableSorting: true,
      },
      {
        accessorKey: 'customerName',
        header: 'Customer',
        size: 200,
        maxSize: 200,
        enableSorting: true,
      },
      {
        accessorKey: 'total',
        header: 'Total',
        enableSorting: true,
        cell: ({ row }) =>
          convertToFloat({
            value: row?.original?.total,
            prefix: '$',
          }),
      },
    ],
    []
  );

  const toggeleOpenConfirmation = () => {
    setOpenConfirmation((prev) => !prev);
  };

  // Derive start and end dates from selected orders' usage dates
  const { startDate, endDate } = useMemo(() => {
    const selectedDates: Dayjs[] = postingData?.data?.flatMap(
      (item: PostingListType) =>
        selectedOrderIds?.includes(item?.orderNo)
          ? [dayjs(item?.dateofUse)]
          : []
    );
    const startDate = minBy(selectedDates, (d) => d.valueOf());
    const endDate = maxBy(selectedDates, (d) => d.valueOf());
    return {
      startDate: startDate?.format(DEFAULT_FORMAT) ?? '',
      endDate: startDate?.isSame(endDate)
        ? ''
        : (endDate?.format(DEFAULT_FORMAT) ?? ''),
    };
  }, [postingData?.data, selectedOrderIds]);

  return (
    <div className="space-y-4 px-4">
      <div className="flex gap-5 items-end">
        <DatePicker
          form={form}
          name="date"
          label="Post Orders for Dates of Use Thru"
          placeholder="Select Date"
          enableInput
        />
        <SelectWidget
          form={form}
          optionsList={locationList ?? []}
          name="locationid"
          label="Location"
          placeholder="Select Location"
          isLoading={optionLoading}
          isClearable={false}
          menuPosition="absolute"
          parentClassName="z-[11]"
        />
        <div className="flex justify-end w-full">
          <UnPostOrder onSuccess={refetch} />
        </div>
      </div>
      <SwitchField
        className="w-fit"
        form={form}
        name="excludeFuturePickups"
        labelEnd="Do Not Include Orders with Pickup Dates After the Posting Date"
      />
      <div className="grid grid-cols-1">
        <DataTable
          data={postingData?.data || []}
          columns={columns}
          totalItems={postingData?.pagination?.totalCount}
          enableRowSelection
          isLoading={isFetching}
          rowSelection={rowSelection}
          onRowSelectionChange={setRowSelection}
          tableClassName="max-h-[300px] 2xl:max-h-[450px] overflow-auto"
          enableMultiRowSelection
          sorting={sorting}
          setSorting={setSorting}
          bindingKey="orderNo"
          enablePagination={false}
        />
      </div>
      <div className="fixed bottom-3 right-4">
        <FormActionButtons
          submitLabel="Post"
          submitIcon={Save}
          onSubmit={toggeleOpenConfirmation}
          onCancel={toggle}
          cancelLabel="Close"
          cancelIcon={X}
          disabledSubmitButton={!selectedOrderIds?.length}
        />
      </div>

      {openConfirmation && (
        <AppConfirmationModal
          open={openConfirmation}
          title="Warning"
          description={
            <>
              This will post
              <span className="font-bold text-base">
                {' '}
                {selectedOrderIds?.length}{' '}
              </span>
              order{selectedOrderIds?.length > 1 && 's'}{' '}
              {endDate ? (
                <>
                  between <span className="font-bold">{startDate}</span> and{' '}
                  <span className="font-bold">{endDate}</span>
                </>
              ) : (
                <>
                  from <span className="font-bold">{startDate}</span>
                </>
              )}
              . Do you want to continue with this process?
            </>
          }
          handleSubmit={form.handleSubmit(onSubmit)}
          submitLabel="Yes"
          handleCancel={toggeleOpenConfirmation}
          isLoading={isLoading}
        />
      )}
    </div>
  );
};

export default memo(Posting);
