'use client';

import CustomDialog from '@/components/common/dialog';
import { useCallback, useState } from 'react';

import ArchiveAr from './archive-ar';
import ArchiveOrdersDate from './archive-orders-date';
import BatchEmail from './batch-email';
import DeliverySlips from './delivery-slips';
import FlatCustomersInactive from './flat-customers-inactive';
import InvoicesDeliveriesPackingLists from './invoices-deliveries-packing-lists';
import PackingLists from './packing-lists';
import PickupSlips from './pickup-slips';
import Posting from './posting';
import PurgeCustomers from './purge-customers';
import PurgeDeletedOrders from './purge-deleted-orders';
import PurgeQuotes from './purge-quotes';
import ServiceCharges from './service-charges';
import SubRentals from './sub-rentals';
import TimeCardJob from './time-card-job';
import UndeleteOrder from './undelete-order';

import AppButton from '@/components/common/app-button';
import { cn } from '@/lib/utils';
import { ProcessDialogAction } from '@/types/processes.types';
import { SquareArrowOutUpRight } from 'lucide-react';
import SelectDepartmentsDialog from './SelectDepartmentsDialog';
import { useGetUserDefaultStoreQuery } from '@/redux/features/store/store.api';

interface GeneralDialogStateTypes {
  open: boolean;
  action: ProcessDialogAction | null;
}
interface DialogProps {
  action: ProcessDialogAction | null;
}

export interface ToggleableComponentProps {
  toggle: () => void;
}
const ProcessDialogRenderer = ({ action }: DialogProps) => {
  const [openDialog, setOpenDialog] = useState<GeneralDialogStateTypes>({
    open: false,
    action: null,
  });

  const [openDepartments, setOpenDepartments] = useState(false);

  const { data: storeLocationData } = useGetUserDefaultStoreQuery(undefined, {
    // skip: Boolean(id),
    refetchOnMountOrArgChange: true,
  });
  const printPackagingList =
    storeLocationData?.data?.storePrintSetting?.printPackagingList;

  const toggle = useCallback(
    (action?: string | null) => {
      const normalizedAction = action?.toUpperCase() as ProcessDialogAction;
      if (
        printPackagingList &&
        ['DELIVERY_SLIPS', 'PACKING_LISTS', 'PICKUP_SLIPS']?.includes(
          normalizedAction
        )
      ) {
        setOpenDepartments(true);
        setOpenDialog(() => ({
          open: false,
          action: normalizedAction ?? null,
        }));
        return;
      }
      setOpenDialog((prev) => ({
        open: !prev?.open,
        action: normalizedAction ?? null,
      }));
    },
    [printPackagingList]
  );

  const toggleDepartments = (action?: string | null) => {
    setOpenDepartments((prev) => !prev);
    const normalizedAction = action?.toUpperCase() as ProcessDialogAction;
    if (action) {
      setOpenDialog((prev) => ({
        open: !prev?.open,
        action: normalizedAction ?? null,
      }));
    }
  };

  interface DialogConfigItem {
    title: string;
    className: string;
    contentClassName?: string;
    component: (props: ToggleableComponentProps) => JSX.Element;
  }

  const dialogConfig: Record<ProcessDialogAction, DialogConfigItem> = {
    POSTING: {
      title: 'Posting',
      className: 'sm:max-w-[65%] 2xl:max-w-[50%]',
      contentClassName: 'min-h-[500px] 2xl:h-[650px]',
      component: ({ toggle }: ToggleableComponentProps) => (
        <Posting toggle={toggle} />
      ),
    },
    SERVICE_CHARGES: {
      title: 'Service Charges',
      className: 'max-w-[71%] 2xl:max-w-[50%]',
      contentClassName: 'min-h-[470px] 2xl:h-[570px]',
      component: ({ toggle }: ToggleableComponentProps) => (
        <ServiceCharges toggle={toggle} />
      ),
    },
    UNDELETE_ORDER: {
      title: 'Undelete Order',
      className: 'md:max-w-[35%] 2xl:max-w-[25%]',
      component: ({ toggle }: ToggleableComponentProps) => (
        <UndeleteOrder toggle={toggle} />
      ),
    },
    INVOICES__DELIVERIES_AND_PACKING_LISTS: {
      title: 'Invoices, Deliveries and Packing-Lists',
      className: 'md:max-w-[35%] 2xl:max-w-[30%]',
      component: ({ toggle }: ToggleableComponentProps) => (
        <InvoicesDeliveriesPackingLists toggle={toggle} />
      ),
    },
    INVOICES: {
      title: 'Invoices',
      className: 'md:max-w-[35%] 2xl:max-w-[30%]',
      component: ({ toggle }: ToggleableComponentProps) => (
        <InvoicesDeliveriesPackingLists toggle={toggle} enableCheckbox />
      ),
    },
    DELIVERY_SLIPS: {
      title: 'Delivery Slips',
      className: 'md:max-w-[35%] 2xl:max-w-[30%]',
      component: ({ toggle }: ToggleableComponentProps) => (
        <DeliverySlips toggle={toggle} />
      ),
    },
    PACKING_LISTS: {
      title: 'Packing Lists',
      className: 'md:max-w-[35%] 2xl:max-w-[30%]',
      component: ({ toggle }: ToggleableComponentProps) => (
        <PackingLists toggle={toggle} />
      ),
    },
    PICKUP_SLIPS: {
      title: 'Pickup Slips',
      className: 'md:max-w-[35%] 2xl:max-w-[30%]',
      component: ({ toggle }: ToggleableComponentProps) => (
        <PickupSlips toggle={toggle} />
      ),
    },
    BATCH_E_MAIL: {
      title: 'Batch E-mail',
      className: 'md:max-w-[35%] 2xl:max-w-[30%]',
      component: ({ toggle }: ToggleableComponentProps) => (
        <BatchEmail toggle={toggle} />
      ),
    },
    SUB_RENTALS: {
      title: 'Sub Rentals',
      className: 'md:max-w-[30%] 2xl:max-w-[25%]',
      component: ({ toggle }: ToggleableComponentProps) => (
        <SubRentals toggle={toggle} />
      ),
    },
    TIME_CARD_BY_JOB: {
      title: 'Time Card by Job',
      className: 'md:max-w-[60%] 2xl:max-w-[50%]',
      component: ({ toggle }: ToggleableComponentProps) => (
        <TimeCardJob toggle={toggle} />
      ),
    },
    ARCHIVE_A_R_BY_DATE: {
      title: 'Archive A/R',
      className: 'md:max-w-[50%] 2xl:max-w-[40%]',
      component: ({ toggle }: ToggleableComponentProps) => (
        <ArchiveAr toggle={toggle} />
      ),
    },
    ARCHIVE_ORDERS_BY_DATE: {
      title: 'Archive Orders by Date',
      className: 'md:max-w-[35%] 2xl:max-w-[30%]',
      component: ({ toggle }: ToggleableComponentProps) => (
        <ArchiveOrdersDate toggle={toggle} />
      ),
    },
    FLAG_CUSTOMERS_INACTIVE: {
      title: 'Flat Customers Inactive',
      className: 'md:max-w-[35%] 2xl:max-w-[30%]',
      component: ({ toggle }: ToggleableComponentProps) => (
        <FlatCustomersInactive toggle={toggle} />
      ),
    },
    PURGE_CUSTOMERS: {
      title: 'Purge Customers',
      className: 'md:max-w-[40%] 2xl:max-w-[35%]',
      component: ({ toggle }: ToggleableComponentProps) => (
        <PurgeCustomers toggle={toggle} />
      ),
    },
    PURGE_DELETED_ORDERS_BY_DATE: {
      title: 'Purge Deleted Orders by Date',
      className: 'md:max-w-[35%] 2xl:max-w-[30%]',
      component: ({ toggle }: ToggleableComponentProps) => (
        <PurgeDeletedOrders toggle={toggle} />
      ),
    },
    PURGE_QUOTES_BY_DATE: {
      title: 'Purge Quotes by Date',
      className: 'md:max-w-[35%] 2xl:max-w-[30%]',
      component: ({ toggle }: ToggleableComponentProps) => (
        <PurgeQuotes toggle={toggle} />
      ),
    },
  } as const;

  const config = openDialog?.action && dialogConfig[openDialog?.action];

  return (
    <>
      <AppButton
        label="View details"
        variant="neutral"
        icon={SquareArrowOutUpRight}
        iconClassName="w-4 h-4"
        onClick={() => toggle(action)}
        disabled={
          ![
            'SERVICE_CHARGES',
            'POSTING',
            'UNDELETE_ORDER',
            'INVOICES',
            'DELIVERY_SLIPS',
            'PACKING_LISTS',
            'PICKUP_SLIPS',
            'BATCH_E_MAIL',
            'SUB_RENTALS',
          ]?.includes(action as ProcessDialogAction)
        }
      />
      {openDialog?.open && openDialog?.action && (
        <CustomDialog
          open={openDialog?.open}
          onOpenChange={() => toggle()}
          title={config?.title}
          description=""
          className={cn('max-h-[96%] overflow-auto', config?.className)}
          contentClassName={cn(config?.contentClassName)}
        >
          {config?.component && <config.component toggle={() => toggle()} />}
        </CustomDialog>
      )}
      {openDepartments && (
        <CustomDialog
          open={openDepartments}
          onOpenChange={() => toggleDepartments(openDialog?.action)}
          title="Select Departments"
          description=""
          className="md:max-w-[35%] 2xl:max-w-[30%]"
          contentClassName="h-[480px] 2xl:h-[450px] overflow-y-auto"
        >
          <SelectDepartmentsDialog
            toggle={() => toggleDepartments(openDialog?.action)}
          />
        </CustomDialog>
      )}
    </>
  );
};

export default ProcessDialogRenderer;
