import AppButton from '@/components/common/app-button';
import DataTable from '@/components/common/data-tables';
import CustomDialog from '@/components/common/dialog';
import DatePicker from '@/components/forms/date-picker';
import Labels from '@/components/forms/Label';
import <PERSON>Field from '@/components/forms/radio-field';
import { printOptions } from '@/constants/processes-constants';
import { DEFAULT_FORMAT, formatDate } from '@/lib/utils';
import { PrintOptions, SubRentalsType } from '@/types/processes.types';
import { RowSelectionState } from '@tanstack/react-table';
import { FileText, PrinterIcon, X } from 'lucide-react';
import { memo, useCallback, useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';

interface SubRentalsProps {
  toggle: () => void;
}

const SubRentals = ({ toggle }: SubRentalsProps) => {
  const today = formatDate(new Date(), DEFAULT_FORMAT);
  const form = useForm<SubRentalsType>();
  const [openPrintEmailDialog, setOpenPrintEmailDialog] = useState(false);
  const [selectedRows, setSelectedRows] = useState<RowSelectionState>({});

  const defaultValues = useMemo(() => {
    return {
      pickupDateFrom: today,
      pickupDateThru: today,
      printOptions: PrintOptions.DoNotIncludeSubRentalsAlreadyPrinted,
    };
  }, [today]);

  useEffect(() => {
    if (defaultValues) {
      form.reset(defaultValues);
    }
  }, [defaultValues, form]);

  // const onSubmit = async (values: SubRentalsType) => {
  //   try {
  //     const payload = {
  //       pickupDateFrom: values.pickupDateFrom,
  //       pickupDateThru: values.pickupDateThru,
  //       printOptions: values.printOptions,
  //     };

  //     console.log('Payload:', payload);

  //     // const result = await generateSubRentals(payload);
  //     // console.log('Response Sub Rentals:', result);
  //   } catch (error) {}
  // };

  const onOpenChangePrintDialog = useCallback(() => {
    setOpenPrintEmailDialog((prev) => !prev);
  }, []);

  const columns = useMemo(() => {
    return [
      {
        accessorKey: 'form',
        header: 'Form',
        size: 80,
      },
      {
        accessorKey: 'copies',
        header: 'Copies',
        size: 200,
      },
      {
        accessorKey: 'printer',
        header: 'Printer',
        size: 100,
      },
    ];
  }, []);

  return (
    <>
      <div className="pl-6 pr-6">
        <div className="flex flex-col gap-6">
          <div className="p-4 rounded-md border flex flex-col gap-4">
            <Labels label="Printing Options" />
            <RadioField
              form={form}
              name="printOptions"
              options={printOptions}
              optionsPerRow={1}
            />
          </div>
          <DatePicker
            form={form}
            name="pickupDateFrom"
            label="Pickup Date From"
            placeholder="Select Date"
            enableInput
          />
          <DatePicker
            form={form}
            name="pickupDateThru"
            label="Pickup Date Thru"
            placeholder="Select Date"
            enableInput
          />
        </div>

        <div className="w-full h-full justify-end flex gap-4 pt-4 sticky bottom-0 bg-white z-30">
          <AppButton
            className="w-28"
            label="Print"
            icon={PrinterIcon}
            iconClassName="w-5 h-5"
            onClick={onOpenChangePrintDialog}
          />
          <AppButton
            className="w-28"
            label="Close"
            onClick={toggle}
            variant="neutral"
            icon={X}
            iconClassName="w-5 h-5"
          />
        </div>
      </div>
      <CustomDialog
        open={openPrintEmailDialog}
        onOpenChange={onOpenChangePrintDialog}
        title="Print"
        contentClassName="py-2"
        className="xl:max-w-4xl overflow-auto"
      >
        <div className="px-6 flex flex-col gap-4">
          <div>
            <div className="flex gap-2 pb-2">
              <AppButton
                icon={PrinterIcon}
                label=""
                tooltip="Print"
                className={
                  'bg-brand-teal-Default hover:bg-brand-teal-secondary rounded-[50%] w-10 h-10'
                }
              />

              <AppButton
                icon={FileText}
                label=""
                tooltip="PDF"
                className={
                  'bg-brand-teal-Default hover:bg-brand-teal-secondary rounded-[50%] w-10 h-10'
                }
              />
            </div>
            <DataTable
              data={[]}
              columns={columns}
              enablePagination={false}
              loaderRows={11}
              tableClassName="max-h-[500px] 2xl:max-h-[550px] overflow-auto"
              manualSorting={false}
              noDataPlaceholder="No Data Found."
              enableRowSelection
              rowSelection={selectedRows}
              onRowSelectionChange={setSelectedRows}
              enableMultiRowSelection
            />
          </div>
          <div className="flex flex-col gap-4">
            <div className="flex justify-end bottom-3 pt-3 right-6 space-x-4">
              <AppButton
                onClick={() => {
                  onOpenChangePrintDialog();
                }}
                label="Close"
                className="w-28"
                variant="neutral"
                icon={X}
                iconClassName="w-5 h-5"
              />
            </div>
          </div>
        </div>
      </CustomDialog>
    </>
  );
};

export default memo(SubRentals);
