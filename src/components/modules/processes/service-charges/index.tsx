import AppButton from '@/components/common/app-button';
import DataTable from '@/components/common/data-tables';
import ActionColumnMenu from '@/components/common/data-tables/ActionColumn';
import { NumberInputField } from '@/components/forms';
import DatePicker from '@/components/forms/date-picker';
import SelectWidget from '@/components/forms/select';
import { ComparisonOperator } from '@/constants/common-constants';
import { REQUIRED_TEXT } from '@/constants/validation-constants';
import { formatDateWithTimezone } from '@/lib/formatDateWithTimezone';
import { convertToFloat, DATE_FORMAT_YYYYMMDD, formatDate } from '@/lib/utils';
import { useGetEnumsListQuery } from '@/redux/features/enums-api/enums-api';
import {
  useGetAllServiceChargesMutation,
  useInsertServiceChargesMutation,
} from '@/redux/features/processes/processes.api';
import { SortingStateType } from '@/types/common.types';
import {
  ServiceChargesListType,
  ServiceChargesType,
} from '@/types/processes.types';
import { ColumnDef } from '@tanstack/react-table';
import dayjs from 'dayjs';
import { FileBadge, FileMinus2, FilePlus2, Printer, X } from 'lucide-react';
import { memo, useCallback, useEffect, useMemo, useState } from 'react';
import { useFieldArray, useForm } from 'react-hook-form';

const ServiceCharges = ({ toggle }: { toggle: () => void }) => {
  const today = formatDateWithTimezone({});
  const [sorting, setSorting] = useState<SortingStateType[]>([
    { id: 'customer', desc: true },
  ]);

  // get the allsservice charge list
  const [getAllserviceCharges, { data: serviceChargeData, isLoading }] =
    useGetAllServiceChargesMutation();
  const serviceChargesItems = serviceChargeData?.data;

  // insert service charges
  const [insertServiceCharge, { isLoading: insertServiceChargeLoading }] =
    useInsertServiceChargesMutation();

  const defaultValues = useMemo(() => {
    return {
      date: today,
      days: 'DAYS_30',
      items: [],
    };
  }, [today]);

  const form = useForm<ServiceChargesType>({
    defaultValues,
  });

  const { fields, replace } = useFieldArray<ServiceChargesType>({
    control: form.control,
    name: 'items',
  });

  const fetchAllServiceCharges = useCallback(async () => {
    const days = form.watch('days');
    const date = form.watch('date');
    const payload = {
      pageNumber: 1,
      pageSize: -1,
      sortBy: sorting?.[0]?.id || '',
      sortAscending: sorting?.[0]?.desc,
      filters:
        [
          {
            field: 'date',
            value: formatDate(date, DATE_FORMAT_YYYYMMDD),
            operator: ComparisonOperator.EQUALS,
          },
          {
            field: 'days',
            value: days,
            operator: ComparisonOperator.EQUALS,
          },
        ]?.filter(({ value }) => value) || [],
    };

    await getAllserviceCharges(payload);
  }, [form, getAllserviceCharges, sorting]);

  useEffect(() => {
    if (serviceChargesItems) {
      form.setValue(
        'items',
        serviceChargesItems?.map((item: ServiceChargesListType) => ({
          ...item,
          serviceChargeId: item?.id,
        }))
      );
    }
  }, [form, serviceChargesItems]);

  useEffect(() => {
    fetchAllServiceCharges();
  }, [fetchAllServiceCharges]);

  // get Apply as of enum
  const { data: serviceChargeDays, isLoading: serviceChargeLoading } =
    useGetEnumsListQuery({
      name: 'ServiceChargeDaysEnum',
    });

  // handle fill amount applied
  const handleFillAmountApplied = () => {
    const fillAmountApplied = fields?.map((item) => ({
      ...item,
      amountApplied: Math.abs(Number(item?.serviceCharge)) || undefined,
    }));
    replace(fillAmountApplied);

    form.setValue('items', fillAmountApplied);
  };

  // handle clear amount
  const handleClearAmpuntApplied = () => {
    const clearAmpuntApplied = fields?.map((item) => ({
      ...item,
      amountApplied: undefined,
    }));
    form.setValue('items', clearAmpuntApplied);
  };

  const serviceChargesValue = form.watch('items');

  const hasAmountAppliedValues = serviceChargesValue?.some((item) =>
    Number(item?.amountApplied)
  );

  // print option
  const dropdownMenu = [
    {
      label: 'Print',
      icon: <Printer className="h-5 w-5" />,
      onClick: () => {},
      disabled: true,
    },
    {
      label: 'Fill Amount Applied',
      icon: <FilePlus2 className="h-5 w-5" />,
      onClick: () => handleFillAmountApplied(),
      disabled: !fields?.length,
    },
    {
      label: 'Clear Amount Applied',
      icon: <FileMinus2 className="h-5 w-5" />,
      onClick: () => handleClearAmpuntApplied(),
      disabled: !hasAmountAppliedValues,
    },
  ];

  const columns: ColumnDef<ServiceChargesListType>[] = useMemo(
    () => [
      {
        accessorKey: 'customer',
        header: 'Customer',
        size: 150,
        enableSorting: true,
      },
      {
        accessorKey: 'totalBalance',
        header: 'Total Balance',
        size: 160,
        enableSorting: true,
        cell: ({ row }) =>
          convertToFloat({ value: row?.original?.totalBalance, prefix: '$' }),
      },
      {
        accessorKey: 'overdueBalance',
        header: 'Overdue Balance',
        size: 180,
        enableSorting: true,
        cell: ({ row }) =>
          convertToFloat({ value: row?.original?.overdueBalance, prefix: '$' }),
      },
      {
        accessorKey: 'serviceCharge',
        header: 'Service Charge',
        size: 170,
        enableSorting: true,
        cell: ({ row }) =>
          convertToFloat({ value: row?.original?.serviceCharge, prefix: '$' }),
      },
      {
        accessorKey: 'amountApplied',
        header: 'Amount Applied',
        size: 160,
        cell: ({ row }) => (
          <NumberInputField
            name={`items.${row.index}.amountApplied`}
            form={form}
            placeholder="Amount Applied"
            fixedDecimalScale
            maxLength={10}
            prefix="$"
            className="h-8"
            pClassName="p-1"
          />
        ),
      },
    ],
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [form, fields]
  );

  // handle insert service charges
  const onSubmit = async (formatData: ServiceChargesType) => {
    const payload = formatData?.items
      ?.filter(({ amountApplied }) => Number(amountApplied))
      ?.map((item) => ({
        id: item?.serviceChargeId,
        amountApplied: Number(item?.amountApplied),
      }));

    await insertServiceCharge(payload)?.unwrap();
    toggle();
  };

  const onDateChange = (value?: Date | string) => {
    if (!value || !dayjs(value).isValid()) {
      form.setError('date', { message: REQUIRED_TEXT });
      return null;
    }
    form.clearErrors();
    fetchAllServiceCharges();
  };

  return (
    <>
      <div className="px-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-5 2xl:mb-7">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 ">
            <DatePicker
              form={form}
              name="date"
              label="Service Charge Date"
              placeholder="Select Date"
              enableInput
              onDateChange={onDateChange}
            />
            <SelectWidget
              form={form}
              optionsList={serviceChargeDays?.data ?? []}
              name="days"
              label="Apply as of # of Days"
              placeholder="Select Days"
              isClearable={false}
              isLoading={serviceChargeLoading}
              onSelectChange={fetchAllServiceCharges}
              menuPosition="absolute"
              parentClassName="z-[11]"
            />
          </div>
          <div className="flex justify-end flex-wrap gap-3  items-start gap-x-4 mt-8">
            <AppButton
              label="Process"
              onClick={form.handleSubmit(onSubmit)}
              icon={FileBadge}
              isLoading={insertServiceChargeLoading}
              disabled={!hasAmountAppliedValues}
            />
            <AppButton
              label="Close"
              onClick={toggle}
              variant="neutral"
              icon={X}
              disabled={insertServiceChargeLoading}
            />
            <ActionColumnMenu
              dropdownMenuList={dropdownMenu}
              contentClassName="w-full"
              triggerClassName="border h-10"
            />
          </div>
        </div>
        <div className="grid grid-cols-1">
          <DataTable
            data={fields || []}
            columns={columns}
            totalItems={fields?.length}
            isLoading={isLoading}
            tableClassName="max-h-[350px] 2xl:max-h-[450px] overflow-auto"
            enablePagination={false}
            sorting={sorting}
            setSorting={setSorting}
          />
        </div>
      </div>
    </>
  );
};

export default memo(ServiceCharges);
