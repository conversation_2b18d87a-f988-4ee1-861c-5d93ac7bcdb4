import AppButton from '@/components/common/app-button';
import DatePicker from '@/components/forms/date-picker';
import { DEFAULT_FORMAT, formatDate } from '@/lib/utils';
import { flatCustomersInactiveType } from '@/types/processes.types';
import { memo } from 'react';
import { useForm } from 'react-hook-form';

const FlatCustomersInactive = ({ toggle }: { toggle: () => void }) => {
  const today = formatDate(new Date(), DEFAULT_FORMAT);
  const form = useForm<flatCustomersInactiveType>({
    defaultValues: {
      noOrdersSince: today,
    },
  });

  return (
    <>
      <div className="pl-6 pr-6">
        <div className="flex flex-col gap-6">
          <div>
            Customers without orders since the date entered below will have
            their status changed to inactive.
          </div>
          <div className="grid grid-cols-2 gap-6">
            <DatePicker
              form={form}
              name="noOrdersSince"
              label="No Orders Since"
              placeholder="Select Date"
              enableInput
            />
          </div>
        </div>

        <div className="w-full h-full justify-end flex gap-4 mt-4 sticky bottom-0 bg-white z-30">
          <AppButton className="w-28" label="Process" />
          <AppButton
            className="w-28"
            label="Close"
            onClick={toggle}
            variant="neutral"
          />
        </div>
      </div>
    </>
  );
};

export default memo(FlatCustomersInactive);
