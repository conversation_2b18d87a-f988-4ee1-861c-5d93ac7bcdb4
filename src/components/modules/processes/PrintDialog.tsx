import AppButton from '@/components/common/app-button';
import CustomDialog from '@/components/common/dialog';
import Labels from '@/components/forms/Label';
import RadioField from '@/components/forms/radio-field';
import { printPopupOptions } from '@/constants/processes-constants';
import { PrintDialogType, PrintPopupOptions } from '@/types/processes.types';
import { CircleCheckBig, X } from 'lucide-react';
import { useEffect, useMemo } from 'react';
import { UseFormReturn } from 'react-hook-form';

const PrintDialog = ({
  openPrintDialog,
  onOpenChangePrintDialog,
  form,
}: {
  openPrintDialog: boolean;
  onOpenChangePrintDialog: () => void;
  form: UseFormReturn<PrintDialogType>;
}) => {
  const defaultValues = useMemo(() => {
    return {
      printOptions: PrintPopupOptions.Customer,
    };
  }, []);

  useEffect(() => {
    if (defaultValues) {
      form.reset(defaultValues);
    }
  }, [defaultValues, form]);

  return (
    <CustomDialog
      open={openPrintDialog}
      onOpenChange={onOpenChangePrintDialog}
      title="Print"
      contentClassName="py-4"
      className="xl:max-w-[18%] overflow-auto"
    >
      <div className="px-6 flex flex-col gap-4">
        <div>
          <div className="p-4 rounded-md border flex flex-col">
            <Labels label="Sort Order List By" className="pb-5" />
            <RadioField
              form={form}
              name="printOptions"
              options={printPopupOptions}
              optionsPerRow={1}
              rowClassName="grid grid-cols-1 w-full"
              className="space-y-4"
            />
          </div>
        </div>
        <div className="flex flex-col gap-4">
          <div className="flex justify-end bottom-3 pt-3 right-6 space-x-4">
            <AppButton
              onClick={() => {
                onOpenChangePrintDialog();
              }}
              label="OK"
              className="w-28"
              icon={CircleCheckBig}
              iconClassName="w-5 h-5"
            />
            <AppButton
              onClick={() => {
                onOpenChangePrintDialog();
              }}
              label="Close"
              className="w-28"
              variant="neutral"
              icon={X}
              iconClassName="w-5 h-5"
            />
          </div>
        </div>
      </div>
    </CustomDialog>
  );
};

export default PrintDialog;
