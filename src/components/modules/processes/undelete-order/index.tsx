import FormActionButtons from '@/components/common/FormActionButtons';
import { SelectDropDown } from '@/components/forms';
import { TEXT_VALIDATION_RULE } from '@/constants/validation-constants';
import { generateLabelValuePairs } from '@/lib/utils';
import {
  useGetAllDeleteOrdersQuery,
  useUnDeleteOrderMutation,
} from '@/redux/features/processes/processes.api';
import { UndeleteOrderType } from '@/types/processes.types';
import { PackagePlus, X } from 'lucide-react';
import { memo } from 'react';
import { useForm } from 'react-hook-form';
import { ToggleableComponentProps } from '../ProcessesDialogRenderer';

const UndeleteOrder = ({ toggle }: ToggleableComponentProps) => {
  const form = useForm<UndeleteOrderType>();

  //
  const [unDelete, { isLoading }] = useUnDeleteOrderMutation();
  const { data: deleteOrderData, isLoading: deleteOrderLoading } =
    useGetAllDeleteOrdersQuery(undefined, { refetchOnMountOrArgChange: true });

  // handle un delete order
  const onSubmit = async (formatData: UndeleteOrderType) => {
    await unDelete(formatData?.orderId)?.unwrap();
    toggle();
  };

  // un delete order list
  const deleteOrderList = generateLabelValuePairs({
    data: deleteOrderData?.data,
    labelKey: 'description',
    valueKey: 'id',
  });

  const orderId = form.watch('orderId');
  return (
    <div className="px-6 pb-2">
      <div className="flex flex-col gap-6">
        <SelectDropDown
          name="orderId"
          label="Select order number to undelete"
          placeholder="Select order number"
          form={form}
          optionsList={deleteOrderList}
          isLoading={deleteOrderLoading}
          validation={TEXT_VALIDATION_RULE}
        />
      </div>

      <div className="w-full h-full justify-end flex gap-5 mt-6">
        <FormActionButtons
          onSubmit={form.handleSubmit(onSubmit)}
          onCancel={toggle}
          disabledSubmitButton={!orderId}
          submitLabel="Undelete"
          submitIcon={PackagePlus}
          isLoading={isLoading}
          cancelIcon={X}
        />
      </div>
    </div>
  );
};

export default memo(UndeleteOrder);
