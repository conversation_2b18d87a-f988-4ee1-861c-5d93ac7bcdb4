import AppButton from '@/components/common/app-button';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import DataTable from '@/components/common/data-tables';
import CustomDialog from '@/components/common/dialog';
import SwitchField from '@/components/common/switch';
import {
  InputField,
  NumberInputField,
  TextAreaField,
} from '@/components/forms';
import DatePicker from '@/components/forms/date-picker';
import Labels from '@/components/forms/Label';
import RadioField from '@/components/forms/radio-field';
import SelectWidget from '@/components/forms/select';
import { STORE_OPTIONS_API_ROUTES } from '@/constants/api-constants';
import { EMAIL_VALIDATION_RULEOptional } from '@/constants/auth-constants';
import {
  dateOptionsBatchEmail,
  emailBatchEmail,
  invoiceOptionsBatchEmail,
  sendBatchEmail,
} from '@/constants/processes-constants';
import useOptionList from '@/hooks/useOptionList';
import { DEFAULT_FORMAT, formatDate } from '@/lib/utils';
import {
  BatchEmailType,
  DateOptionsBatchEmail,
  emailMethod,
  invoiceOptionsBatchEmailMethod,
  PrintEmailOptions,
  sendMethod,
  SettingsEmailType,
} from '@/types/processes.types';
import { RowSelectionState } from '@tanstack/react-table';
import {
  Check,
  CircleAlert,
  Info,
  MailIcon,
  Mails,
  SendIcon,
  Settings,
  X,
} from 'lucide-react';
import { memo, useCallback, useEffect, useMemo, useState } from 'react';
import { useForm, useWatch } from 'react-hook-form';

interface BatchEmailProps {
  toggle: () => void;
}

const BatchEmail = ({ toggle }: BatchEmailProps) => {
  const today = formatDate(new Date(), DEFAULT_FORMAT);
  const form = useForm<BatchEmailType>();
  const formSettings = useForm<SettingsEmailType>();

  const [openPrintEmailDialog, setOpenPrintEmailDialog] = useState(false);
  const [selectedRows, setSelectedRows] = useState<RowSelectionState>({});
  const [batchEmailModel, setBatchEmailModel] = useState({
    state: false,
    action: '',
  });
  const [openBatchEmailDialog, setOpenBatchEmailDialog] = useState(false);
  const [openSettingsDialog, setOpenSettingsDialog] = useState(false);

  const selectedDateOption = useWatch({
    control: form.control,
    name: 'dateOptions',
  });

  const selectedSendOption = useWatch({
    control: form.control,
    name: 'send',
  });

  const { options: storeLocationList, optionLoading } = useOptionList({
    url: STORE_OPTIONS_API_ROUTES.ALL,
    labelKey: 'locationName',
    valueKey: 'id',
    sortBy: 'locationName',
    extraKey: 'isDefault',
  });

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const locationList = storeLocationList?.length
    ? [...[{ label: 'All Locations', value: '0' }], ...storeLocationList]
    : [];

  const defaultStoreLocation = useMemo(() => {
    return locationList?.find((opt: any) => opt.extraKey);
  }, [locationList]);

  const defaultValues = useMemo(() => {
    return {
      location: defaultStoreLocation?.value || 1,
      deliveryDateFrom: today,
      deliveryDateThru: today,
      dateOptions: DateOptionsBatchEmail.DeliveryDate,
      invoiceOptions: invoiceOptionsBatchEmailMethod.IncludeOrders,
      send: sendMethod.Invocies,
      email: emailMethod.MainCustomerEmail,
      printEmailOptions: PrintEmailOptions.DefaultInvoices,
    };
  }, [defaultStoreLocation?.value, today]);

  useEffect(() => {
    if (defaultValues) {
      form.reset(defaultValues);
    }
  }, [defaultValues, form]);

  useEffect(() => {
    if (selectedSendOption === sendMethod.OrderConfirmations) {
      form.setValue('email', emailMethod.OrderContactEmail);
    } else if (selectedSendOption === sendMethod.Invocies) {
      form.setValue('email', emailMethod.MainCustomerEmail);
    }
  }, [selectedSendOption, form]);

  const defaultValuesSettings = useMemo(() => {
    return {
      emailBody: 'Test Order batch E-mail',
      emailType: 'orders',
    };
  }, []);

  useEffect(() => {
    if (defaultValuesSettings) {
      formSettings.reset(defaultValuesSettings);
    }
  }, [defaultValuesSettings, formSettings]);

  const handleEmailTypeChange = (value: string) => {
    if (value === 'orders') {
      formSettings.setValue('emailBody', 'Test Order batch E-mail');
    } else if (value === 'statements') {
      formSettings.setValue('emailBody', 'Test Statements batch E-mail');
    }
  };

  // const onSubmit = async (values: BatchEmailType) => {
  //   try {
  //     const payload = {
  //       locationId: values.location,
  //       deliveryDateFrom: values.deliveryDateFrom,
  //       deliveryDateThru: values.deliveryDateThru,
  //       dateType: values.dateOptions,
  //       invoiceOptions: values.invoiceOptions,
  //       sendType: values.send,
  //       emailType: values.email,
  //     };

  //     console.log('Payload:', payload);

  //     // const result = await generateBatchEmail(payload);
  //     // console.log('Response Batch E-mail:', result);
  //   } catch (error) {}
  // };

  const AutoSelectOptions = [
    {
      label: 'Previous Selection',
      value: PrintEmailOptions.PreviousSelection,
    },
    {
      label: 'Default Invoices & Packing Lists',
      value: PrintEmailOptions.DefaultInvoicesPackingLists,
    },
    { label: 'Other', value: PrintEmailOptions.Other },

    { label: 'Default Invoices', value: PrintEmailOptions.DefaultInvoices },
    { label: 'E-sign Documents', value: PrintEmailOptions.EsignDocuments },
    {
      label: 'Default Packing Lists',
      value: PrintEmailOptions.DefaultPackingLists,
    },
  ];

  const EmailTypeOptions = [
    { label: 'Orders', value: 'orders' },
    { label: 'Statements', value: 'statements' },
  ];

  const columns = useMemo(() => {
    return [
      {
        accessorKey: 'form',
        header: 'Form',
        size: 80,
      },
      {
        accessorKey: 'copies',
        header: 'Copies',
        size: 200,
      },
      {
        accessorKey: 'printer',
        header: 'Printer',
        size: 100,
      },
      {
        accessorKey: 'faxPrinter',
        header: 'Fax Printer',
        size: 100,
      },
    ];
  }, []);

  const columnsBatchEmail = useMemo(() => {
    return [
      {
        accessorKey: 'status',
        header: 'Status',
        size: 80,
      },
      {
        accessorKey: 'customer',
        header: 'Customer',
        size: 200,
      },
      {
        accessorKey: 'type',
        header: 'Type',
        size: 100,
      },
      {
        accessorKey: 'orderNo',
        header: 'Order #',
        size: 100,
      },
      {
        accessorKey: 'sendTo',
        header: 'Send To',
        size: 100,
      },
      {
        accessorKey: 'attachment',
        header: 'Attachment(s)',
        size: 100,
      },
    ];
  }, []);

  const onOpenChangePrintEmailDialog = useCallback(() => {
    setOpenPrintEmailDialog((prev) => !prev);
  }, []);

  const handleSendEmail = () => {
    setBatchEmailModel({ state: true, action: 'sendEmail' });
  };

  const handleOrdersBatched = () => {
    setBatchEmailModel({ state: false, action: '' });
    setOpenPrintEmailDialog(false);
    setOpenBatchEmailDialog(true);
  };

  const onOpenChangeOrdersBatchedDialog = useCallback(() => {
    setOpenBatchEmailDialog((prev) => !prev);
    toggle();
  }, [toggle]);

  const handleAbout = () => {
    setBatchEmailModel({ state: true, action: 'about' });
  };

  const handleCancelAbout = () => {
    setBatchEmailModel({ state: false, action: '' });
  };

  const handleSettings = () => {
    setOpenSettingsDialog(true);
  };

  const onOpenChangeSettingsDialog = useCallback(() => {
    setOpenSettingsDialog((prev) => !prev);
  }, []);

  return (
    <>
      <div className="pl-6 pr-6">
        <div className="flex flex-col gap-6">
          <SelectWidget
            form={form}
            optionsList={locationList ?? []}
            name="location"
            label="Location"
            placeholder="Select Location"
            isLoading={optionLoading}
            menuPosition="absolute"
            menuPlacement="bottom"
            isClearable={false}
          />
          <div className="p-4 rounded-md border flex flex-col">
            <Labels label="Invoicing Options" className="pb-5" />
            <RadioField
              form={form}
              name="invoiceOptions"
              options={invoiceOptionsBatchEmail}
              optionsPerRow={1}
            />
          </div>

          <div className="flex gap-4">
            <div className="w-1/2 p-4 rounded-md border flex flex-col">
              <Labels label="Send" className="pb-5" />
              <RadioField
                form={form}
                name="send"
                options={sendBatchEmail}
                optionsPerRow={1}
              />
            </div>

            <div className="w-1/2 p-4 rounded-md border flex flex-col">
              <Labels label="E-mail To" className="pb-5" />
              <RadioField
                form={form}
                name="email"
                options={emailBatchEmail}
                optionsPerRow={1}
              />
            </div>
          </div>

          <div className="p-4 rounded-md border flex flex-col gap-4">
            <RadioField
              form={form}
              name="dateOptions"
              options={dateOptionsBatchEmail}
              optionsPerRow={3}
            />
            <DatePicker
              form={form}
              name="deliveryDateFrom"
              label={
                selectedDateOption === 'DateOfUse'
                  ? 'Date of Use From'
                  : selectedDateOption === 'DateOrdered'
                    ? 'Date Ordered From'
                    : 'Delivery Date From'
              }
              placeholder="Select Date"
              enableInput
            />
            <DatePicker
              form={form}
              name="deliveryDateThru"
              label={
                selectedDateOption === 'DateOfUse'
                  ? 'Date of Use Thru'
                  : selectedDateOption === 'DateOrdered'
                    ? 'Date Ordered Thru'
                    : 'Delivery Date Thru'
              }
              placeholder="Select Date"
              enableInput
            />
          </div>
        </div>

        <div className="w-full h-full justify-end flex gap-4 pt-4 sticky bottom-0 bg-white z-30">
          <AppButton
            className="w-28"
            label="E-mail"
            icon={MailIcon}
            iconClassName="w-5 h-5"
            // onClick={form.handleSubmit(onSubmit)}
            onClick={onOpenChangePrintEmailDialog}
          />
          <AppButton
            className="w-28"
            label="Close"
            onClick={toggle}
            variant="neutral"
            icon={X}
            iconClassName="w-5 h-5"
          />
        </div>
      </div>

      <AppConfirmationModal
        open={batchEmailModel.state}
        title="Confirmation"
        description={
          <div className="flex items-center gap-2">
            <div>
              <CircleAlert className="w-8 h-8 text-violet-500" />
            </div>
            <div>
              {batchEmailModel.action === 'sendEmail'
                ? 'Orders batched for sending.'
                : 'Batch E-mail v3.5.1'}
            </div>
          </div>
        }
        handleSubmit={
          batchEmailModel.action === 'sendEmail'
            ? handleOrdersBatched
            : handleCancelAbout
        }
        submitLabel="Ok"
      />
      <CustomDialog
        open={openPrintEmailDialog}
        onOpenChange={onOpenChangePrintEmailDialog}
        title="Print/E-mail"
        contentClassName="py-2"
        className="xl:max-w-6xl overflow-auto"
      >
        <div className="px-6 flex flex-col gap-4">
          <div>
            <div className="flex gap-2 pb-2">
              <AppButton
                icon={Mails}
                label=""
                tooltip="Batch E-mail"
                className={
                  'bg-brand-teal-Default hover:bg-brand-teal-secondary rounded-[50%] w-10 h-10'
                }
                onClick={handleSendEmail}
              />
            </div>
            <DataTable
              data={[]}
              columns={columns}
              enablePagination={false}
              loaderRows={11}
              tableClassName="max-h-[500px] 2xl:max-h-[550px] overflow-auto"
              manualSorting={false}
              noDataPlaceholder="No Data Found."
              enableRowSelection
              rowSelection={selectedRows}
              onRowSelectionChange={setSelectedRows}
              enableMultiRowSelection
            />
            <div className="p-4 rounded-md border flex flex-col mt-4">
              <Labels label="Auto-Select Options" className="pb-5" />
              <RadioField
                form={form}
                name="printEmailOptions"
                options={AutoSelectOptions}
                optionsPerRow={2}
                rowClassName="grid grid-cols-4 w-full"
                className="space-y-4"
              />
            </div>
          </div>
          <div className="flex flex-col gap-4">
            <div className="flex justify-end bottom-3 pt-3 right-6 space-x-4">
              <AppButton
                label="Close"
                className="w-28"
                variant="neutral"
                icon={X}
                iconClassName="w-5 h-5"
                onClick={onOpenChangePrintEmailDialog}
              />
            </div>
          </div>
        </div>
      </CustomDialog>
      <CustomDialog
        open={openBatchEmailDialog}
        onOpenChange={onOpenChangeOrdersBatchedDialog}
        title="Batch E-mail"
        contentClassName="py-2"
        className="xl:max-w-6xl overflow-auto"
      >
        <div className="px-6 flex flex-col gap-4">
          <div>
            <div className="flex justify-between items-center w-full gap-2 pb-2">
              <div className="flex gap-2">
                <AppButton
                  icon={Settings}
                  label=""
                  tooltip="Settings"
                  className="bg-brand-teal-Default hover:bg-brand-teal-secondary rounded-[50%] w-10 h-10"
                  onClick={handleSettings}
                />
                <AppButton
                  icon={Info}
                  label=""
                  tooltip="About"
                  className="bg-brand-teal-Default hover:bg-brand-teal-secondary rounded-[50%] w-10 h-10"
                  onClick={handleAbout}
                />
              </div>

              <AppButton label="Process" tooltip="Process" />
            </div>
            <DataTable
              data={[]}
              columns={columnsBatchEmail}
              enablePagination={false}
              loaderRows={11}
              tableClassName="max-h-[500px] 2xl:max-h-[550px] overflow-auto"
              manualSorting={false}
              noDataPlaceholder="No Data Found."
              enableRowSelection
              rowSelection={selectedRows}
              onRowSelectionChange={setSelectedRows}
              enableMultiRowSelection
            />
          </div>
          <div className="flex flex-col gap-4">
            <div className="flex justify-end bottom-3 pt-3 right-6 space-x-4">
              <AppButton
                label="Close"
                className="w-28"
                variant="neutral"
                icon={X}
                iconClassName="w-5 h-5"
                onClick={onOpenChangeOrdersBatchedDialog}
              />
            </div>
          </div>
        </div>
      </CustomDialog>
      <CustomDialog
        open={openSettingsDialog}
        onOpenChange={onOpenChangeSettingsDialog}
        title="Settings"
        contentClassName="py-2"
        className="xl:max-w-7xl overflow-auto"
      >
        <div className="px-4">
          <div className="flex flex-col md:flex-row gap-6 p-4 rounded-md border">
            <div className="flex-1 space-y-6">
              <h2 className="text-xl font-semibold text-gray-800 flex items-center gap-2">
                Global E-mail Settings
              </h2>

              <div className="flex flex-col md:flex-row gap-6">
                <div className="flex-1 space-y-2">
                  <InputField
                    name="server"
                    label="Server"
                    form={formSettings}
                    placeholder="smtp.gmail.com"
                  />
                  <NumberInputField
                    name="port"
                    label="Port"
                    maxLength={10}
                    form={formSettings}
                    placeholder="Enter Port"
                  />
                  <InputField
                    name="username"
                    label="Username"
                    form={formSettings}
                    placeholder="Enter Username"
                  />
                  <InputField
                    name="password"
                    label="Password"
                    form={formSettings}
                    placeholder="Enter Password"
                  />
                </div>

                <div className="flex-1 space-y-6 pt-8">
                  <SwitchField
                    labelEnd="Enable SSL"
                    form={formSettings}
                    name="enableSSL"
                    className="w-fit"
                  />
                  <SwitchField
                    labelEnd="Send Read Receipt Requests"
                    form={formSettings}
                    name="sendReadReceipt"
                    className="w-fit"
                  />
                  <NumberInputField
                    name="messageLimit"
                    label="Message Limit Per Minute"
                    maxLength={10}
                    form={formSettings}
                    placeholder="Enter Message Limit"
                  />
                </div>
              </div>
            </div>

            <div className="hidden md:block w-[1px] bg-gray-300"></div>

            <div className="flex-1 space-y-6">
              <h2 className="text-xl font-semibold text-gray-800 flex items-center gap-2">
                Batch E-mail Settings
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <InputField
                  name="emailFrom"
                  label="E-mail From"
                  form={formSettings}
                  placeholder="Enter E-mail From"
                  validation={EMAIL_VALIDATION_RULEOptional}
                />
                <InputField
                  name="cc"
                  label="CC"
                  form={formSettings}
                  placeholder="Enter CC"
                />
              </div>

              <div className="flex flex-col md:flex-row gap-4">
                <div className="w-full md:w-[20%]">
                  <RadioField
                    name="emailType"
                    label="E-mail Body"
                    form={formSettings}
                    options={EmailTypeOptions}
                    optionsPerRow={1}
                    onChange={handleEmailTypeChange}
                  />
                </div>

                <div className="w-full md:w-[80%]">
                  <TextAreaField
                    rows={8}
                    name="emailBody"
                    label=""
                    form={formSettings}
                  />
                </div>
              </div>
            </div>
          </div>

          <div className="flex justify-between items-center pt-2">
            <AppButton
              icon={SendIcon}
              iconClassName="w-5 h-5"
              label="Send Test E-mail"
            />
            <AppButton
              label="Ok"
              className="w-28"
              variant="neutral"
              icon={Check}
              iconClassName="w-5 h-5"
              onClick={onOpenChangeSettingsDialog}
            />
          </div>
        </div>
      </CustomDialog>
    </>
  );
};

export default memo(BatchEmail);
