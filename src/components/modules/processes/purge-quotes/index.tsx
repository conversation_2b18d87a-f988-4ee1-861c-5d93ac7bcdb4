import AppButton from '@/components/common/app-button';
import DatePicker from '@/components/forms/date-picker';
import RadioField from '@/components/forms/radio-field';
import { quotesOptions } from '@/constants/processes-constants';
import { DEFAULT_FORMAT, formatDate } from '@/lib/utils';
import { purgeQuotesType } from '@/types/processes.types';
import { memo } from 'react';
import { useForm } from 'react-hook-form';

const PurgeQuotes = ({ toggle }: { toggle: () => void }) => {
  const today = formatDate(new Date(), DEFAULT_FORMAT);
  const form = useForm<purgeQuotesType>({
    defaultValues: {
      purgeQuotesThru: today,
    },
  });

  return (
    <>
      <div className="pl-6 pr-6">
        <div className="flex flex-col gap-6">
          <div>
            This program will purge all quotes through the date entered.
          </div>
          <RadioField
            form={form}
            name="quotes"
            options={quotesOptions}
            optionsPerRow={1}
          />
          <div className="grid grid-cols-2 gap-6">
            <DatePicker
              form={form}
              name="purgeQuotesThru"
              label="Purge Quotes Thru"
              placeholder="Select Date"
              enableInput
            />
          </div>
        </div>

        <div className="w-full h-full justify-end flex gap-4 mt-4 sticky bottom-0 bg-white z-30">
          <AppButton className="w-28" label="Process" />
          <AppButton
            className="w-28"
            label="Close"
            onClick={toggle}
            variant="neutral"
          />
        </div>
      </div>
    </>
  );
};

export default memo(PurgeQuotes);
