import FileDownloadIcon from '@/assets/icons/FileDownloadIcon';
import AppButton from '@/components/common/app-button';
import DataTable from '@/components/common/data-tables';
import { InputField } from '@/components/forms';
import { PrintList } from '@/types/warehouse.types';
import { ColumnDef, RowSelectionState } from '@tanstack/react-table';
import { PrinterIcon } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';
import { UseFormReturn } from 'react-hook-form';

interface PrintProps {
  form: UseFormReturn<any>;
  handleCancel: React.Dispatch<React.SetStateAction<void>>;
  downloadPrintFile: React.Dispatch<React.SetStateAction<void>>;
}

const Print = ({ form, handleCancel, downloadPrintFile }: PrintProps) => {
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});

  const columns: ColumnDef<PrintList>[] = useMemo(() => {
    return [
      { accessorKey: 'form', header: 'Form', size: 270 },
      {
        accessorKey: 'copies',
        header: 'Copies',
        size: 100,
        cell: ({ row }) => {
          return (
            <div className="flex flex-col">
              <InputField
                form={form}
                name={`print.${row.index}.copies`}
                placeholder="Enter Copies"
                aria-describedby="copies-helper"
              />
            </div>
          );
        },
      },
      {
        accessorKey: 'printer',
        header: 'Printer',
        size: 270,
        cell: ({ row }) => {
          return (
            <div className="flex flex-col">
              <InputField
                form={form}
                name={`print.${row.index}.printer`}
                placeholder="Enter Printer"
                aria-describedby="printer-helper"
              />
            </div>
          );
        },
      },
    ];
  }, [form]);

  useEffect(() => {
    setRowSelection({});
  }, []);

  const downloadPrintFilePDF = () => {
    if (Object.keys(rowSelection).length > 0) {
      downloadPrintFile();
    }
  };

  return (
    <div>
      <div className="flex gap-2 mb-5">
        <AppButton
          label={<PrinterIcon className="text-white" />}
          variant="neutral"
          className="bg-brand-teal-Default hover:bg-brand-teal-secondary rounded-[50%] w-10 h-10"
          tooltip="Print"
          onClick={downloadPrintFilePDF}
          disabled={Object.keys(rowSelection).length === 0}
        />
        <AppButton
          label={<FileDownloadIcon className="text-white" />}
          variant="neutral"
          className="bg-brand-teal-Default hover:bg-brand-teal-secondary rounded-[50%] w-10 h-10"
          tooltip="PDF"
          onClick={downloadPrintFilePDF}
          disabled={Object.keys(rowSelection).length === 0}
        />
      </div>
      <div className="mb-24">
        <DataTable
          columns={columns ?? []}
          data={form.watch('print') ?? []}
          tableClassName="max-h-[480px] lg:max-h-[580px] overflow w-full"
          enableRowSelection
          rowSelection={rowSelection}
          onRowSelectionChange={setRowSelection}
          enablePagination={false}
        />
      </div>
      <div className="flex justify-end gap-4 fixed bottom-0 right-0 bg-white pb-4 pt-4 px-6 z-10 border-t w-full rounded-b-md">
        <AppButton
          label="Cancel"
          onClick={handleCancel}
          className="w-28"
          variant="neutral"
        />
      </div>
    </div>
  );
};

export default Print;
