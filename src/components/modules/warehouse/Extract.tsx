import ExcelIcon from '@/assets/icons/ExcelIcon';
import AppButton from '@/components/common/app-button';
import { AppTableContext } from '@/components/common/app-data-table/AppTableContext';
import SwitchField from '@/components/common/switch';
import { SelectDropDown } from '@/components/forms';
import { UseToast } from '@/components/ui/toast/ToastContainer';
import { WAREHOUSE_CUSTOMER_PICKUPS_RETURNS_API_ROUTES } from '@/constants/api-constants';
import { useDownloadFile } from '@/hooks/useDownloadFile';
import { getPaginationObject } from '@/lib/utils';
import { RootState } from '@/redux/store';
import { CustomerPickupsReturnsForm } from '@/types/warehouse.types';
import { X } from 'lucide-react';
import { useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { useSelector } from 'react-redux';

interface ExtractProps {
  form: UseFormReturn<CustomerPickupsReturnsForm>;
  handleCancel: React.Dispatch<React.SetStateAction<void>>;
  searchParams: string;
}

const Extract = ({ form, handleCancel, searchParams }: ExtractProps) => {
  const { sorting } = useContext(AppTableContext);
  const filter = useSelector((state: RootState) => state.warehouse.filters);
  const [downloadType, setDownloadType] = useState<string>('');

  const toast = UseToast();
  const { downloadFile } = useDownloadFile();

  const defaultValues = useMemo(() => {
    return {
      downloadType: 'dispatchTrack',
      includeSubRentals: false,
      includeDeliveriesPickups: false,
    };
  }, []);

  const downloadCSVList = useMemo(() => {
    return [
      {
        label: 'CSV',
        value: 'csv',
      },
      {
        label: 'Dispatch Track',
        value: 'dispatchTrack',
      },
      {
        label: 'Route4Me',
        value: 'route4Me',
      },
    ];
  }, []);

  const handleExtractVendorsToExcel = useCallback(() => {
    const normalizedFilters = Array.isArray(filter)
      ? filter.map((f: any) => ({
          field: f.name || f.label,
          value: f.value,
          operator: f.operator,
        }))
      : [];
    const payload = getPaginationObject({
      pagination: { pageIndex: 0, pageSize: 0 },
      sorting,
      filters: [
        ...normalizedFilters,
        { field: 'filter', value: searchParams, operator: 'Contains' },
      ],
    });
    const response = downloadFile({
      url: WAREHOUSE_CUSTOMER_PICKUPS_RETURNS_API_ROUTES.EXPORT_CSV,
      method: 'POST',
      body: payload,
    });
    toast.promise(response, {
      loading: 'Downloading file...',
      success: 'File downloaded successfully.',
      error: 'Failed to download file',
    });
  }, [filter, sorting, searchParams, downloadFile, toast]);

  const handleDownloadTypeChange = (value: string) => {
    setDownloadType(value);
    if (value === 'csv') {
      form.setValue('includeSubRentals', false);
      form.setValue('includeDeliveriesPickups', false);
    }

    form.setValue('downloadType', value);
  };

  useEffect(() => {
    form.reset(defaultValues);
  }, [form, defaultValues]);

  const isCSV = downloadType === 'csv';

  return (
    <>
      <div className="flex flex-col gap-6">
        <div className="flex items-center justify-between gap-2">
          <div className="flex-1">
            <SelectDropDown
              name="downloadType"
              label=""
              form={form}
              optionsList={downloadCSVList}
              placeholder="Select Type"
              allowClear={false}
              onChange={handleDownloadTypeChange}
            />
          </div>
          <AppButton
            icon={ExcelIcon}
            label=""
            tooltip="Extract"
            className="bg-white border border-border-Default hover:bg-brand-teal-secondary rounded-full w-10 h-10"
            onClick={handleExtractVendorsToExcel}
            disabled
          />
        </div>
        <div className="flex flex-col gap-2">
          <SwitchField
            form={form}
            name="includeSubRentals"
            labelEnd="Include Sub-Rentals"
            className="mt-2 w-fit"
            disabled={isCSV}
          />
          <SwitchField
            form={form}
            name="includeDeliveriesPickups"
            labelEnd="Include Deliveries & Pickups"
            className="mt-2 w-fit"
            disabled={isCSV}
          />
        </div>
      </div>
      <div className="flex justify-end gap-4 fixed bottom-0 right-0 bg-white pb-3 pt-3 px-6 z-10 border-t w-full">
        <AppButton
          label="Cancel"
          onClick={handleCancel}
          icon={X}
          iconClassName="w-5 h-5"
          className="w-28"
          variant="neutral"
        />
      </div>
    </>
  );
};

export default Extract;
