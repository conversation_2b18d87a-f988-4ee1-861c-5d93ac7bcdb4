import AppButton from '@/components/common/app-button';
import { AppTableContext } from '@/components/common/app-data-table/AppTableContext';
import SelectDropDown from '@/components/forms/select-dropdown';
import { Separator } from '@/components/ui/separator';
import { getQueryParam, updateQueryParam } from '@/lib/utils';
import { RootState } from '@/redux/store';
import { memo, useCallback, useContext, useEffect } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import {
  clearAllFilters,
  setFilter,
  updateFormValues,
} from '@/redux/features/warehouse/warehouseSlice';
import DatePicker from '@/components/forms/date-picker';
import useOptionList from '@/hooks/useOptionList';
import { STORE_OPTIONS_API_ROUTES } from '@/constants/api-constants';
import S<PERSON><PERSON>ield from '@/components/common/switch';

interface FilterFormValues {
  storeLocationId: string;
  dateOfUseFrom: string;
  dateOfUseThru: string;
  arrivalDate: boolean;
  returnShipDate: boolean;
  arrivalReturnDate: boolean;
}

interface FilterItem {
  label: string;
  value: string;
  name: string;
  tagValue: string;
  operator: string;
}

const fieldMappings: { key: keyof FilterFormValues; label: string }[] = [
  { key: 'storeLocationId', label: 'Location' },
  { key: 'dateOfUseFrom', label: 'Start Date' },
  { key: 'dateOfUseThru', label: 'End Date' },
  { key: 'arrivalDate', label: 'Arrival Date' },
  { key: 'returnShipDate', label: 'Return Ship Date' },
  { key: 'arrivalReturnDate', label: 'Arrival Return Date' },
];

const defaultValues: FilterFormValues = {
  storeLocationId: '',
  dateOfUseFrom: '',
  dateOfUseThru: '',
  arrivalDate: true,
  returnShipDate: true,
  arrivalReturnDate: true,
};

interface FilterProps {
  setIsFilterOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const Filter: React.FC<FilterProps> = ({ setIsFilterOpen }) => {
  const search = getQueryParam('search') as string;
  const formValuesFromRedux = useSelector(
    (state: RootState) => state.warehouse.formValues
  );

  const dispatch = useDispatch();
  const { pagination, setPagination } = useContext(AppTableContext);
  // Initialize form with values from Redux
  const form = useForm<FilterFormValues>({
    defaultValues: (formValuesFromRedux as any) || defaultValues,
    mode: 'onChange',
  });

  const { options: storeLocationList, optionLoading } = useOptionList({
    url: STORE_OPTIONS_API_ROUTES.ALL,
    labelKey: 'locationName',
    valueKey: 'id',
    sortBy: 'locationName',
    extraKey: 'isDefault',
  });

  const locationList = storeLocationList?.length
    ? [...[{ label: 'All Locations', value: '0' }], ...storeLocationList]
    : [];

  const onDateChange = useCallback(
    async (
      date: Date | string | undefined,
      name: 'dateOfUseFrom' | 'dateOfUseThru'
    ) => {
      if (name === 'dateOfUseFrom' && (date === '' || !date)) {
        form.setValue('dateOfUseFrom', '');
        form.setValue('dateOfUseThru', '');
      }
    },
    [form]
  );

  // Watch form changes and update Redux state
  useEffect(() => {
    const subscription = form.watch((values: any) => {
      dispatch(updateFormValues(values));
    });

    return () => subscription.unsubscribe();
  }, [form, dispatch]);

  //WarehouseTypeFilter
  const onSubmit: SubmitHandler<FilterFormValues> = (data) => {
    const newFilterData: FilterItem[] = fieldMappings
      .filter(({ key }) => data[key])
      .map(({ key, label }) => ({
        label,
        value: String(data[key]),
        name: key,
        tagValue: String(data[key]),
        operator: 'Contains',
      }));

    dispatch(setFilter(newFilterData));
    setIsFilterOpen(false);
    setPagination({
      ...pagination,
      pageIndex: 0,
    });
  };

  const handleChangeOrderLocation = useCallback(
    async (value: string) => {
      form.setValue('storeLocationId', value);
    },
    [form]
  );

  const handleClear = () => {
    form.reset(defaultValues); // Reset form to default values
    dispatch(clearAllFilters()); // Clear all filters in Redux
    setIsFilterOpen(false); // Close the filter form
    if (search) {
      updateQueryParam(null, 'search');
    }
  };

  // Track if form values are different from the default values
  const isFormModified =
    JSON.stringify(form.watch()) !== JSON.stringify(defaultValues);

  const arrivalReturnDate = form.watch('arrivalReturnDate');
  return (
    <>
      <div className="px-3">
        <div className="text-normal py-2 font-semibold">Filters</div>
        <Separator
          orientation="horizontal"
          className="h-[1px] bg-border-Default mb-2"
        />
        <div className="grid col-span-1 md:grid-cols-1 gap-3">
          <div className="grid grid-cols-1 gap-3">
            <SelectDropDown
              optionsList={locationList || []}
              name="storeLocationId"
              label="Location"
              placeholder="Select Location"
              isLoading={optionLoading}
              onChange={handleChangeOrderLocation}
              form={form}
              allowClear={false}
            />
          </div>
          <div className="grid grid-cols-2 gap-4">
            {/* Left side: DatePickers in 2 columns */}
            <div className="grid grid-cols-2 gap-3">
              <DatePicker
                form={form}
                name="dateOfUseFrom"
                label="Date From"
                placeholder="Select Date"
                onDateChange={(data) => onDateChange(data, 'dateOfUseFrom')}
                enableInput
                pClassName="col-span-2"
                isRenderFirst={true}
              />
              <DatePicker
                form={form}
                name="dateOfUseThru"
                label="Date Thru"
                placeholder="Select Date"
                onDateChange={(data) => onDateChange(data, 'dateOfUseThru')}
                enableInput
                disablePastDate={form.watch('dateOfUseFrom')}
                pClassName="col-span-2"
                isRenderFirst={true}
              />
            </div>
            <div className="flex flex-col justify-center pt-1 pl-3 gap-1 h-full">
              <SwitchField
                form={form}
                name="arrivalDate"
                labelEnd={arrivalReturnDate ? 'Arrival Date' : 'Delivery Date'}
                className="mt-4 w-fit"
              />
              <SwitchField
                form={form}
                name="returnShipDate"
                labelEnd={
                  arrivalReturnDate ? 'Return Ship Date' : 'Pickup Date'
                }
                className="mt-4 w-fit"
              />
              <SwitchField
                form={form}
                name="arrivalReturnDate"
                labelEnd="Arrival/Return Ship"
                className="mt-4 w-fit"
              />
            </div>
          </div>
        </div>
        <div className="w-full h-full flex justify-between gap-3 mt-3 sticky -bottom-2 pt-1 pb-3 bg-white z-30">
          <AppButton
            className="w-full"
            onClick={form.handleSubmit(onSubmit)}
            label="Apply"
            disabled={!isFormModified}
          />
          <AppButton
            className="w-full"
            label="Clear"
            variant="neutral"
            onClick={handleClear}
            disabled={!isFormModified}
          />
        </div>
      </div>
    </>
  );
};

export default memo(Filter);
