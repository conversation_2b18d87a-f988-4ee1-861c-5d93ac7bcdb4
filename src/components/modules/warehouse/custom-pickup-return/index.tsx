import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import AppDataTable from '@/components/common/app-data-table';
import AppTableContextProvider from '@/components/common/app-data-table/AppTableContext';
import AppSpinner from '@/components/common/app-spinner';
// import { ROUTES } from '@/constants/routes-constants';
import {
  cn,
  // formatPhoneNumber,
  getQueryParam,
  updateQueryParam,
} from '@/lib/utils';
import {
  useDeleteWarehouseMutation,
  useDownloadFileMutation,
  // useGetListForPrintQuery,
  // useGetWarehouseColumsQuery,
  // useUpdateWarehouseColumsMutation,
} from '@/redux/features/warehouse/warehouseCustomPickupReturn.api';
import {
  clearFilter,
  setFilter,
  clearAllFilters,
} from '@/redux/features/warehouse/warehouseSlice';
import { RootState } from '@/redux/store';
import { ColumnDef, RowSelectionState } from '@tanstack/react-table';
import { Download, Settings, Truck } from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
// import { Link } from 'react-router-dom';
// import ColumnReOrdering from '@/components/common/column-reording';
import Filter from './Filter';
import {
  CustomerPickupsReturnsForm,
  OpenDialogType,
  WAREHOUSE_POPUPS,
  WarehouseCustomerPickupsReturnsListTypes,
} from '@/types/warehouse.types';
import BreadcrumbDialogRenderer from '@/components/common/BreadcrumbDialogRenderer';
import { useForm } from 'react-hook-form';
import DisplayItems from '../DisplayItems';
import AppButton from '@/components/common/app-button';
import PrinterIcon from '@/assets/icons/PrinterIcon';
import Print from '../Print';
import Options from '../Options';
import Extract from '../Extract';

const CustomPickupReturn = () => {
  const search = getQueryParam('search') as string;

  const dispatch = useDispatch();
  const [openDelete, setOpenDelete] = useState<boolean>(false);
  const [warehouseItem, setWarehouseItem] =
    useState<WarehouseCustomerPickupsReturnsListTypes | null>(null);
  const [refresh, setRefresh] = useState<boolean>(false);
  const [searchParams, setSearchParams] = useState<string>('');
  const [isFilterOpen, setIsFilterOpen] = useState<boolean>(false);
  // const [openColumnOrdering, setOpenColumnOrdering] = useState<boolean>(false);
  const filter = useSelector((state: RootState) => state.warehouse.filters);
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});

  const [downloadPrintFile, { isLoading: isLoadingDownloadFile }] =
    useDownloadFileMutation();
  // const { data: printList, isLoading: isLoadingPrintList } =
  //   useGetListForPrintQuery(null);

  const [activeTab, setActiveTab] = useState<string>('');
  const [open, setOpen] = useState<OpenDialogType>({
    state: false,
    action: '',
  });

  const onOpenChange = useCallback(() => {
    setOpen({ state: false, action: '' });
  }, []);

  const handleSetActiveTab = useCallback((tab: string) => {
    setActiveTab(tab ?? '');
  }, []);

  const form = useForm<CustomerPickupsReturnsForm>({
    // defaultValues: { print: printList?.data ?? [] },
  });

  const handleCancel = useCallback(() => {
    setOpen({ state: false, action: '' });
  }, []);

  // tab list
  const listItems = useMemo(() => {
    const tabList = [
      {
        label: 'Items',
        value: WAREHOUSE_POPUPS.VIEW_ITEMS,
        content: <DisplayItems form={form} handleCancel={handleCancel} />,
      },
      {
        label: 'Filter Options',
        value: WAREHOUSE_POPUPS.OPTIONS,
        content: <Options form={form} handleCancel={handleCancel} />,
      },
      {
        label: 'Print',
        value: WAREHOUSE_POPUPS.PRINT,
        content: (
          <Print
            handleCancel={handleCancel}
            form={form}
            downloadPrintFile={downloadPrintFile}
          />
        ),
      },
      {
        label: 'Extract',
        value: WAREHOUSE_POPUPS.EXTRACT,
        content: (
          <Extract
            form={form}
            handleCancel={handleCancel}
            searchParams={searchParams}
          />
        ),
      },
    ];
    return tabList?.filter((tab) => [activeTab]?.includes(tab?.value));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeTab, form]);

  // Custom toolbar component
  const CustomToolbar = (
    <div className="flex space-x-3">
      <AppButton
        label={
          <div className="flex items-center gap-2">
            <Settings className="w-5 h-5" />
            Options
          </div>
        }
        variant="neutral"
        onClick={() => {
          setOpen({
            state: true,
            action: WAREHOUSE_POPUPS.OPTIONS,
          });
          handleSetActiveTab(WAREHOUSE_POPUPS.OPTIONS);
        }}
      />
      <AppButton
        label={
          <div className="flex items-center gap-2">
            <PrinterIcon />
            Print
          </div>
        }
        variant="neutral"
        onClick={() => {
          setOpen({
            state: true,
            action: WAREHOUSE_POPUPS.PRINT,
          });
          handleSetActiveTab(WAREHOUSE_POPUPS.PRINT);
        }}
      />
      <AppButton
        label={
          <div className="flex items-center gap-2">
            <Download className="w-5 h-5" />
            Extract
          </div>
        }
        variant="neutral"
        onClick={() => {
          setOpen({
            state: true,
            action: WAREHOUSE_POPUPS.EXTRACT,
          });
          handleSetActiveTab(WAREHOUSE_POPUPS.EXTRACT);
        }}
      />
    </div>
  );

  // const { data, refetch, isFetching } = useGetWarehouseColumsQuery();
  // const [updateWarehouseColums, { isLoading: newItemLoading }] =
  //   useUpdateWarehouseColumsMutation();

  // const componentMap: any = useMemo(
  //   () => ({
  //     PhoneWidget: ({ value }: any) => formatPhoneNumber(value),
  //   }),
  //   []
  // );

  const [deleteWarehouse, { isLoading: deleteLoading }] =
    useDeleteWarehouseMutation();

  const toggleDelete = useCallback(
    (row?: WarehouseCustomerPickupsReturnsListTypes) => {
      setWarehouseItem(row ? row : null);
      setOpenDelete((prev) => !prev);
    },
    []
  );

  const handleDelete = useCallback(async () => {
    try {
      await deleteWarehouse(warehouseItem?.id || 0).unwrap();

      toggleDelete();

      setRefresh(true);
      setTimeout(() => {
        setRefresh(false);
      }, 100);
    } catch (err) {}
  }, [deleteWarehouse, warehouseItem?.id, toggleDelete]);

  // const tableColumns = useMemo(() => {
  //   return data?.data;
  // }, [data?.data]);

  // const handleOrderingColumn = useCallback(
  //   async (formData: any) => {
  //     const updatedColumns = tableColumns.map((item: any) => ({
  //       ...item,
  //       enabled: formData[item.accessorKey],
  //     }));

  //     await updateWarehouseColums(updatedColumns).unwrap();
  //     setOpenColumnOrdering(false);
  //     await refetch();
  //   },
  //   [tableColumns, updateWarehouseColums, refetch]
  // );

  // handle Clear function
  const handleClearFilter = useCallback(
    (key: string) => {
      dispatch(clearFilter(key));
      setIsFilterOpen(false);
      if (search) {
        updateQueryParam(null, 'search');
      }
    },
    [dispatch, search]
  );

  const DropdownMenu = useMemo(() => {
    return [
      {
        label: 'View Items for Selected Orders',
        onClick: () => {
          // jobNo
          if (rowSelection?.jobNo) {
            setOpen({
              state: true,
              action: WAREHOUSE_POPUPS.VIEW_ITEMS,
            });
            handleSetActiveTab(WAREHOUSE_POPUPS.VIEW_ITEMS);
          }
        },
        icon: <Truck />,
      },
    ];
  }, [handleSetActiveTab, rowSelection?.jobNo]);

  useEffect(() => {
    if (search) {
      dispatch(
        setFilter([
          {
            label: 'Vendor',
            value: search,
            name: 'vendor',
            tagValue: search,
            operator: 'equals',
          } as any,
        ])
      );
    }
  }, [dispatch, search]);

  useEffect(() => {
    if (!search) {
      dispatch(clearAllFilters());
    }
  }, [dispatch, search]);

  // const actionColumn: ColumnDef<WarehouseCustomerPickupsReturnsListTypes> =
  //   useMemo(
  //     () => ({
  //       id: 'action',
  //       size: 110,
  //       header: 'Actions',
  //       cell: ({ row }) => {
  //         const orderId = row?.original?.orderid;
  //         return (
  //           <ActionColumnMenu
  //             customEdit={
  //               <Link
  //                 to={`${ROUTES?.ORDERS}?id=${orderId}&tab=information`}
  //                 className="flex items-center justify-center gap-2 border border-text-tertiary rounded-md px-3 py-1"
  //               >
  //                 <EditIcon className="w-5 h-5" /> Edit details
  //               </Link>
  //             }
  //             onDelete={() => toggleDelete(row.original)}
  //             contentClassName="w-fit"
  //           />
  //         );
  //       },
  //     }),
  //     [toggleDelete]
  //   );

  // const memoizedColumns = useMemo(() => {
  //   if (!tableColumns?.length) return [];

  //   const formattedColumns = tableColumns
  //     .filter((column: any) => column.enabled) // Ensure only enabled columns are included
  //     .map((column: any) => ({
  //       accessorKey: column.accessorKey,
  //       header: column.header,
  //       enableSorting: column.enableSorting || false,
  //       size: column.size || 200, // Default size if not provided
  //       cell: ({ row }: any) => {
  //         const Component = componentMap[column.component]; // Get the component based on the key
  //         return Component ? (
  //           <Component value={row?.original?.[column.accessorKey]} />
  //         ) : (
  //           row?.original?.[column.accessorKey]
  //         );
  //       },
  //     }));

  //   return [
  //     {
  //       accessorKey: 'ordering',
  //       header: () => (
  //         <ColumnReOrdering
  //           isOpen={openColumnOrdering}
  //           handleOrderingColumn={handleOrderingColumn}
  //           tableColumns={tableColumns}
  //           setOpenColumnOrdering={setOpenColumnOrdering}
  //         />
  //       ),
  //       size: 50,
  //     },
  //     ...formattedColumns,
  //     actionColumn,
  //   ];
  // }, [
  //   tableColumns,
  //   componentMap,
  //   openColumnOrdering,
  //   handleOrderingColumn,
  //   setOpenColumnOrdering,
  //   actionColumn,
  // ]);

  const memoizedColumns = useMemo<ColumnDef<any>[]>(() => {
    return [
      {
        accessorKey: 'jobNo',
        header: 'Job #',
        size: 100,
      },
      {
        accessorKey: 'order',
        header: 'Order #',
        size: 100,
      },
      {
        accessorKey: 'rev',
        header: 'Rev #',
        size: 100,
      },
      {
        accessorKey: 'customer',
        header: 'Customer',
        size: 100,
      },
      {
        accessorKey: 'dateOfUse',
        header: 'Date of Use',
        size: 100,
      },
      {
        accessorKey: 'dp',
        header: 'D/P',
        size: 100,
      },
      {
        accessorKey: 'deliveryDate',
        header: 'Delivery Date',
        size: 100,
      },
      {
        accessorKey: 'deliveryTime',
        header: 'Delivery Time',
        size: 100,
      },
      {
        accessorKey: 'pickupDate',
        header: 'Pickup Date',
        size: 100,
      },
      {
        accessorKey: 'pickupTime',
        header: 'Pickup Time',
        size: 100,
      },
      {
        accessorKey: 'status',
        header: 'Status',
        size: 100,
      },
      {
        accessorKey: 'orderTotal',
        header: 'Order Total',
        size: 100,
      },
    ];
  }, []);

  return (
    <div className="p-6">
      <AppTableContextProvider defaultSort={[{ id: 'jobNo', desc: true }]}>
        <AppDataTable
          // url={WAREHOUSE_CUSTOMER_PICKUPS_RETURNS_API_ROUTES.ALL}
          url={''}
          columns={memoizedColumns}
          heading="Customer Pickups & Returns"
          enableSearch={true}
          enablePagination={true}
          tableClassName="max-h-[400px] 2xl:max-h-[580px] overflow-auto"
          refreshList={refresh}
          searchKey="jobNo"
          setSearchParams={setSearchParams}
          enableFilter
          filter={filter}
          handleClearFilter={handleClearFilter}
          filterClassName="w-[550px]"
          filterContent={<Filter setIsFilterOpen={setIsFilterOpen} />}
          setIsFilterOpen={setIsFilterOpen}
          isFilterOpen={isFilterOpen}
          dropdownMenus={DropdownMenu}
          dropdownContentClassName="p-4 flex flex-col gap-2 w-full"
          customToolBar={CustomToolbar}
          enableRowSelection={true}
          onRowSelectionChange={setRowSelection}
        />
      </AppTableContextProvider>
      <AppConfirmationModal
        description={
          <div>
            Are you sure you want to delete
            <strong> {warehouseItem?.jobNo ?? ''}</strong> ?
          </div>
        }
        open={openDelete}
        onOpenChange={toggleDelete}
        handleCancel={toggleDelete}
        handleSubmit={handleDelete}
        isLoading={deleteLoading}
      />
      <BreadcrumbDialogRenderer
        activeTab={activeTab}
        isOpen={open.state}
        listItems={listItems}
        onOpenChange={onOpenChange}
        setActiveTab={handleSetActiveTab}
        className={cn(
          open.state && activeTab === WAREHOUSE_POPUPS.EXTRACT
            ? 'max-w-[50%] 2xl:max-w-[20%] 2xl:h-[330px]'
            : 'max-w-[60%] 2xl:max-w-[45%]'
        )}
        contentClassName={cn('h-[480px] 2xl:h-[550px] overflow-y-auto')}
      />
      <AppSpinner
        overlay
        isLoading={
          // newItemLoading ||
          // isFetching ||
          isLoadingDownloadFile
          // || isLoadingPrintList
        }
      />
    </div>
  );
};

export default CustomPickupReturn;
