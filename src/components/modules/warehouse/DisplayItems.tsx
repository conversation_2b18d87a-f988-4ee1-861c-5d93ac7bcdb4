import AppButton from '@/components/common/app-button';
import DataTable from '@/components/common/data-tables';
import { NumberInputField } from '@/components/forms';
import RadioField from '@/components/forms/radio-field';
import { useMemo } from 'react';
import { UseFormReturn, useWatch } from 'react-hook-form';

interface DisplayItemsProps {
  form: UseFormReturn<any>;
  handleCancel: React.Dispatch<React.SetStateAction<void>>;
}

const DisplayItems = ({ form, handleCancel }: DisplayItemsProps) => {
  const selectedView = useWatch({
    control: form.control,
    name: 'itemViewType',
  });

  const handleViewTypeChange = (value: string) => {
    form.setValue('itemViewType', value);
  };

  const handleKitTypeChange = (value: string) => {
    form.setValue('kitType', value);
  };

  const columns = useMemo(() => {
    const baseColumns = [
      { accessorKey: 'itemId', header: 'Item ID', size: 120 },
      { accessorKey: 'itemDescription', header: 'Item Description', size: 250 },
      { accessorKey: 'qtyRented', header: 'Qty Rented', size: 130 },
      { accessorKey: 'weight', header: 'Weight', size: 100 },
    ];

    const orderColumns = [
      { accessorKey: 'orderId', header: 'Order #', size: 100 },
      { accessorKey: 'customerName', header: 'Customer Name', size: 250 },
    ];

    return selectedView === 'itemOrders'
      ? [...baseColumns, ...orderColumns]
      : baseColumns;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedView, handleViewTypeChange]);

  return (
    <div className="w-full">
      <div className="flex justify-between mb-5">
        <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4 mb-6">
          <RadioField
            name="itemViewType"
            form={form}
            label=""
            options={[
              { label: 'Item Summary', value: 'itemSummary' },
              { label: 'Item Orders', value: 'itemOrders' },
            ]}
            className="flex flex-cols"
            onChange={handleViewTypeChange}
          />
          <RadioField
            name="kitType"
            form={form}
            label=""
            options={[
              { label: 'Kits', value: 'kits' },
              { label: 'Kit Components', value: 'kitComponents' },
            ]}
            className="flex flex-cols"
            onChange={handleKitTypeChange}
          />
        </div>

        <div className="grid grid-cols-1 items-center gap-2 mb-2">
          <div className="w-fit">Total Weight</div>
          <NumberInputField
            name="totalWeight"
            prefix="$"
            disabled
            form={form}
            className="h-8"
            thousandSeparator=","
            fixedDecimalScale
          />
        </div>
      </div>

      <div>
        <DataTable
          columns={columns}
          data={[]}
          tableClassName="max-h-[480px] lg:max-h-[580px] overflow w-full"
        />
      </div>

      <div className="flex justify-end gap-4 fixed bottom-0 right-6 bg-white pb-4">
        <AppButton
          label="Cancel"
          onClick={handleCancel}
          className="w-28"
          variant="neutral"
        />
      </div>
    </div>
  );
};

export default DisplayItems;
