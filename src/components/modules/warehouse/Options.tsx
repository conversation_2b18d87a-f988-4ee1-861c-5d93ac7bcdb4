import AppButton from '@/components/common/app-button';
import DataTable from '@/components/common/data-tables';
import SwitchField from '@/components/common/switch';
import { OptionsTypes } from '@/types/warehouse.types';
import { ColumnDef, RowSelectionState } from '@tanstack/react-table';
import { CircleCheckBig, X } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';
import { UseFormReturn } from 'react-hook-form';

interface OptionsProps {
  form: UseFormReturn<any>;
  handleCancel: React.Dispatch<React.SetStateAction<void>>;
}

const Options = ({ form, handleCancel }: OptionsProps) => {
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});

  const columns: ColumnDef<OptionsTypes>[] = useMemo(() => {
    return [
      {
        accessorKey: 'status',
        header: 'Status',
        size: 100,
      },
    ];
  }, []);

  useEffect(() => {
    setRowSelection({});
  }, []);

  return (
    <div>
      <h1 className="pb-5">Statuses to display</h1>
      <div className="mb-24">
        <DataTable
          columns={columns ?? []}
          data={[]}
          tableClassName="max-h-[480px] lg:max-h-[480px] overflow w-full"
          enableRowSelection
          rowSelection={rowSelection}
          onRowSelectionChange={setRowSelection}
          enablePagination={false}
        />
        <SwitchField
          form={form}
          name="includeSales"
          labelEnd="Include Sales"
          className="mt-4 w-fit"
        />
      </div>
      <div className="flex justify-end gap-4 fixed bottom-0 right-0 bg-white pb-4 pt-4 px-6 z-10 border-t w-full rounded-b-md">
        <AppButton
          label="OK"
          icon={CircleCheckBig}
          iconClassName="w-5 h-5"
          className="w-28"
        />
        <AppButton
          label="Cancel"
          onClick={handleCancel}
          icon={X}
          iconClassName="w-5 h-5"
          className="w-28"
          variant="neutral"
        />
      </div>
    </div>
  );
};

export default Options;
