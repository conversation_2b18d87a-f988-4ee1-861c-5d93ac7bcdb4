import AppButton from '@/components/common/app-button';
import { AppTableContext } from '@/components/common/app-data-table/AppTableContext';
import SelectDropDown from '@/components/forms/select-dropdown';
import { Separator } from '@/components/ui/separator';
import { getQueryParam, updateQueryParam } from '@/lib/utils';
import { RootState } from '@/redux/store';
import { memo, useCallback, useContext, useEffect } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import {
  CheckBoxMapSubrentalPickupsReturns,
  CheckedBoxType,
  FilterFieldNameSubrentalPickupsReturns,
  StatusCheckBoxSubrentalPickupsReturnsMap,
} from '@/types/warehouse.types';
import {
  clearAllFilters,
  setFilter,
  updateFormValues,
} from '@/redux/features/warehouse/warehouseSlice';
import DatePicker from '@/components/forms/date-picker';
import AppCheckbox from '@/components/common/app-checkbox/AppCheckbox';
import { Label } from '@radix-ui/react-label';
import useOptionList from '@/hooks/useOptionList';
import { STORE_OPTIONS_API_ROUTES } from '@/constants/api-constants';

interface FilterFormValues {
  storeLocationId: string;
  dateOfUseFrom: string;
  dateOfUseThru: string;
  notPickedUp: boolean;
  scheduled: boolean;
  pickUpOnRoute: boolean;
  pickUp: boolean;
  unableToPickup: boolean;
  returnOnRoute: boolean;
  returned: boolean;
  unableToReturn: boolean;
}

interface FilterItem {
  label: string;
  value: string;
  name: string;
  tagValue: string;
  operator: string;
}

const fieldMappings: { key: keyof FilterFormValues; label: string }[] = [
  { key: 'storeLocationId', label: 'Location' },
  { key: 'dateOfUseFrom', label: 'Start Date' },
  { key: 'dateOfUseThru', label: 'End Date' },
  { key: 'notPickedUp', label: 'Not Picked Up' },
  { key: 'scheduled', label: 'Scheduled' },
  { key: 'pickUpOnRoute', label: 'Pick Up On Route' },
  { key: 'pickUp', label: 'Pick Up' },
  { key: 'unableToPickup', label: 'Unable To Pick Up' },
  { key: 'returnOnRoute', label: 'Return On Route' },
  { key: 'returned', label: 'Returned' },
  { key: 'unableToReturn', label: 'Unable To Return' },
];

const defaultValues: FilterFormValues = {
  storeLocationId: '',
  dateOfUseFrom: '',
  dateOfUseThru: '',
  notPickedUp: false,
  scheduled: false,
  pickUpOnRoute: false,
  pickUp: false,
  unableToPickup: false,
  returnOnRoute: false,
  returned: false,
  unableToReturn: false,
};

interface FilterProps {
  setIsFilterOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const Filter: React.FC<FilterProps> = ({ setIsFilterOpen }) => {
  const search = getQueryParam('search') as string;
  const formValuesFromRedux = useSelector(
    (state: RootState) => state.warehouse.formValues
  );

  const dispatch = useDispatch();
  const { pagination, setPagination } = useContext(AppTableContext);
  // Initialize form with values from Redux
  const form = useForm<FilterFormValues>({
    defaultValues: formValuesFromRedux as any,
    mode: 'onChange',
  });

  // Order Location List
  const { options: orderLocationList, optionLoading } = useOptionList({
    url: STORE_OPTIONS_API_ROUTES.ALL,
    labelKey: 'locationName',
    valueKey: 'id',
    sortBy: 'locationName',
  });

  const onDateChange = useCallback(
    async (
      date: Date | string | undefined,
      name: 'dateOfUseFrom' | 'dateOfUseThru'
    ) => {
      if (name === 'dateOfUseFrom' && (date === '' || !date)) {
        form.setValue('dateOfUseFrom', '');
        form.setValue('dateOfUseThru', '');
      }
    },
    [form]
  );

  const handleCheckboxChange = (
    e: boolean,
    name: FilterFieldNameSubrentalPickupsReturns
  ) => {
    try {
      form.setValue(name, e) as any;
    } catch (err) {
      return err;
    }
  };

  // Watch form changes and update Redux state
  useEffect(() => {
    const subscription = form.watch((values: any) => {
      dispatch(updateFormValues(values));
    });

    return () => subscription.unsubscribe();
  }, [form, dispatch]);

  //WarehouseTypeFilter
  const onSubmit: SubmitHandler<FilterFormValues> = (data) => {
    const newFilterData: FilterItem[] = fieldMappings
      .filter(({ key }) => data[key])
      .map(({ key, label }) => ({
        label,
        value: String(data[key]),
        name: key,
        tagValue: String(data[key]),
        operator: 'Contains',
      }));

    dispatch(setFilter(newFilterData));
    setIsFilterOpen(false);
    setPagination({
      ...pagination,
      pageIndex: 0,
    });
  };

  const handleChangeOrderLocation = useCallback(
    async (value: string) => {
      form.setValue('storeLocationId', value);
    },
    [form]
  );

  const handleClear = () => {
    form.reset(defaultValues); // Reset form to default values
    dispatch(clearAllFilters()); // Clear all filters in Redux
    setIsFilterOpen(false); // Close the filter form
    if (search) {
      updateQueryParam(null, 'search');
    }
  };

  // Track if form values are different from the default values
  const isFormModified =
    JSON.stringify(form.watch()) !== JSON.stringify(defaultValues);

  const MapCheckedBox = ({ name, label }: CheckedBoxType) => {
    return (
      <div className="flex gap-3 justify-start items-center flex-row">
        <AppCheckbox
          name={name as FilterFieldNameSubrentalPickupsReturns}
          control={form.control}
          className="data-[state=checked]:bg-[#04BCC2]"
          onChange={(event) =>
            handleCheckboxChange(
              event,
              name as FilterFieldNameSubrentalPickupsReturns
            )
          }
        />
        <Label className="text-sm sm:text-base font-medium">{label}</Label>
      </div>
    );
  };

  return (
    <>
      <div className="px-3">
        <div className="text-normal py-2 font-semibold">Filters</div>
        <Separator
          orientation="horizontal"
          className="h-[1px] bg-border-Default mb-2"
        />
        <div className="grid col-span-1 md:grid-cols-1 gap-3">
          <div className="grid grid-cols-1 gap-3">
            <SelectDropDown
              optionsList={orderLocationList || []}
              name="storeLocationId"
              label="Order Location"
              placeholder="Select Order Location"
              isLoading={optionLoading}
              onChange={handleChangeOrderLocation}
              form={form}
              allowClear={false}
            />
          </div>
          <div className="grid grid-cols-2 gap-3">
            <div>
              <DatePicker
                form={form}
                name="dateOfUseFrom"
                label="Date From"
                placeholder="Select Date"
                onDateChange={(data) => onDateChange(data, 'dateOfUseFrom')}
                enableInput
                pClassName="col-span-2"
                isRenderFirst={true}
              />
            </div>
            <div>
              <DatePicker
                form={form}
                name="dateOfUseThru"
                label="Date Thru"
                placeholder="Select Date"
                onDateChange={(data) => onDateChange(data, 'dateOfUseThru')}
                enableInput
                disablePastDate={form.watch('dateOfUseFrom')}
                pClassName="col-span-2"
                isRenderFirst={true}
              />
            </div>
            <div>
              {CheckBoxMapSubrentalPickupsReturns?.map((ele) => {
                return (
                  <MapCheckedBox
                    key={ele?.name}
                    name={ele?.name}
                    label={ele.label}
                  />
                );
              })}
            </div>
            <div>
              {StatusCheckBoxSubrentalPickupsReturnsMap?.map((ele) => {
                return (
                  <MapCheckedBox
                    key={ele?.name}
                    name={ele?.name}
                    label={ele.label}
                  />
                );
              })}
            </div>
          </div>
        </div>
        <div className="w-full h-full flex justify-between gap-3 mt-3 sticky -bottom-2 pt-1 pb-3 bg-white z-30">
          <AppButton
            className="w-full"
            onClick={form.handleSubmit(onSubmit)}
            label="Apply"
            disabled={!isFormModified}
          />
          <AppButton
            className="w-full"
            label="Clear"
            variant="neutral"
            onClick={handleClear}
            disabled={!isFormModified}
          />
        </div>
      </div>
    </>
  );
};

export default memo(Filter);
