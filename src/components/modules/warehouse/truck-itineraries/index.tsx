import AppDataTable from '@/components/common/app-data-table';
import AppTableContextProvider from '@/components/common/app-data-table/AppTableContext';
import { WAREHOUSE_TRUCK_ITINERARIES_API_ROUTES } from '@/constants/api-constants';
import {
  useDeleteWarehouseMutation,
  useDownloadFileMutation,
  useGetListForPrintQuery,
  useGetWarehouseColumsQuery,
  useUpdateWarehouseColumsMutation,
} from '@/redux/features/warehouse/truckItineraries.api';
import { useCallback, useEffect, useMemo, useState } from 'react';
import ColumnReOrdering from '@/components/common/column-reording';
import { cn, formatPhoneNumber, getQueryParam } from '@/lib/utils';
import ActionColumnMenu from '@/components/common/data-tables/ActionColumn';
import { ColumnDef } from '@tanstack/react-table';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import {
  OpenDialogType,
  TruckItineraryForm,
  WAREHOUSE_POPUPS,
  WarehouseTruckItinerariesListTypes,
} from '@/types/warehouse.types';
import AppSpinner from '@/components/common/app-spinner';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
import Filter from './Filter';
import AppButton from '@/components/common/app-button';
import { EditIcon, PrinterIcon, TruckIcon } from 'lucide-react';
import {
  clearFilter,
  setFilter,
  clearAllFilters,
} from '@/redux/features/warehouse/warehouseSlice';
import BreadcrumbDialogRenderer from '@/components/common/BreadcrumbDialogRenderer';
import { useForm } from 'react-hook-form';
import DatePicker from '@/components/forms/date-picker';
import SelectDropDown from '@/components/forms/select-dropdown';
import TruckItinerariesModal from './TruckItinerariesModal';
import Print from '../Print';

const TruckItineraries = () => {
  const search = getQueryParam('search') as string;
  const [openColumnOrdering, setOpenColumnOrdering] = useState<boolean>(false);
  const [refresh, setRefresh] = useState<boolean>(false);
  const filter = useSelector((state: RootState) => state.warehouse.filters);
  const [isFilterOpen, setIsFilterOpen] = useState<boolean>(false);
  const [openDelete, setOpenDelete] = useState<boolean>(false);
  const [warehouseItem, setWarehouseItem] =
    useState<WarehouseTruckItinerariesListTypes | null>(null);
  const [activeTab, setActiveTab] = useState<string>('');
  const [open, setOpen] = useState<OpenDialogType>({
    state: false,
    action: '',
  });
  const [truckId, setTruckId] = useState<string>('');

  const dispatch = useDispatch();

  const [downloadPrintFile, { isLoading: isLoadingDownloadFile }] =
    useDownloadFileMutation();
  const { data: printList, isLoading: isLoadingPrintList } =
    useGetListForPrintQuery(null);

  const form = useForm<TruckItineraryForm>({
    defaultValues: { print: printList?.data ?? [] },
  });

  const { data, refetch, isFetching } = useGetWarehouseColumsQuery();
  const [updateWarehouseColums, { isLoading: newItemLoading }] =
    useUpdateWarehouseColumsMutation();

  const [deleteWarehouse, { isLoading: deleteLoading }] =
    useDeleteWarehouseMutation();

  const tableColumns = useMemo(() => {
    return data?.data;
  }, [data?.data]);

  const componentMap: any = useMemo(
    () => ({
      PhoneWidget: ({ value }: any) => formatPhoneNumber(value),
    }),
    []
  );

  const handleOrderingColumn = useCallback(
    async (formData: any) => {
      const updatedColumns = tableColumns.map((item: any) => ({
        ...item,
        enabled: formData[item.accessorKey],
      }));

      await updateWarehouseColums(updatedColumns).unwrap();
      setOpenColumnOrdering(false);
      await refetch();
    },
    [tableColumns, updateWarehouseColums, refetch]
  );

  // handle Clear function
  const handleClearFilter = useCallback(
    (key: string) => {
      dispatch(clearFilter(key));
      setIsFilterOpen(false);
    },
    [dispatch]
  );

  const onDateChange = useCallback(
    async (date: Date | string | undefined, name: 'dateFrom' | 'dateTo') => {
      form.setValue(name, date);
    },
    [form]
  );

  const handleCancel = useCallback(() => {
    setOpen({ state: false, action: '' });
  }, []);

  // Custom toolbar component
  const CustomToolbar = (
    <div className="flex space-x-3">
      <AppButton
        label={
          <div className="flex items-center gap-2">
            <PrinterIcon />
            Print
          </div>
        }
        variant="neutral"
        onClick={() => {
          setOpen({
            state: true,
            action: WAREHOUSE_POPUPS.PRINT,
          });
          handleSetActiveTab(WAREHOUSE_POPUPS.PRINT);
        }}
      />
      <AppButton
        label={
          <div className="flex items-center gap-2">
            <TruckIcon />
            New Itinerary
          </div>
        }
        variant="neutral"
        onClick={() => {
          setOpen({
            state: true,
            action: WAREHOUSE_POPUPS.NEW_ITINERARY,
          });
          handleSetActiveTab(WAREHOUSE_POPUPS.NEW_ITINERARY);
        }}
      />
    </div>
  );

  // tab list
  const listItems = useMemo(() => {
    const tabList = [
      {
        label: 'Truck Itinerary',
        value: WAREHOUSE_POPUPS.TRUCK_ITINERARY,
        content: (
          <TruckItinerariesModal
            truckId={truckId}
            setOpen={setOpen}
            setActiveTab={setActiveTab}
          />
        ),
      },
      {
        label: 'New Itinerary',
        value: WAREHOUSE_POPUPS.NEW_ITINERARY,
        content: (
          <div className="flex gap-2 flex-col">
            <DatePicker
              form={form}
              name="dateFrom"
              label="Date From"
              placeholder="Select Date"
              onDateChange={(data) => onDateChange(data, 'dateFrom')}
              enableInput
              pClassName="col-span-2"
              isRenderFirst={true}
            />
            <DatePicker
              form={form}
              name="dateTo"
              label="Date To"
              placeholder="Select Date"
              onDateChange={(data) => onDateChange(data, 'dateTo')}
              enableInput
              pClassName="col-span-2"
              isRenderFirst={true}
            />
            <SelectDropDown
              form={form}
              name="truck"
              label="Truck"
              optionsList={[]}
              placeholder="Select Truck"
              disabled
              aria-describedby="truck-helper"
            />
            <div className="flex justify-end gap-4 fixed bottom-0 right-6 bg-white pb-4">
              <AppButton label="Ok" onClick={() => {}} className="w-28" />
              <AppButton
                label="Cancel"
                onClick={handleCancel}
                className="w-28"
                variant="neutral"
              />
            </div>
          </div>
        ),
      },
      {
        label: 'Print',
        value: WAREHOUSE_POPUPS.PRINT,
        content: (
          <Print
            handleCancel={handleCancel}
            form={form}
            downloadPrintFile={downloadPrintFile}
          />
        ),
      },
    ];
    return tabList?.filter((tab) => [activeTab]?.includes(tab?.value));
  }, [activeTab, downloadPrintFile, form, handleCancel, onDateChange, truckId]);

  const onOpenChange = useCallback(() => {
    setOpen({ state: false, action: '' });
  }, []);

  const handleSetActiveTab = useCallback((tab: string) => {
    setActiveTab(tab ?? '');
  }, []);

  const toggleDelete = useCallback(
    (row?: WarehouseTruckItinerariesListTypes) => {
      setWarehouseItem(row ? row : null);
      setOpenDelete((prev) => !prev);
    },
    []
  );

  const handleDelete = useCallback(async () => {
    try {
      await deleteWarehouse(warehouseItem?.id || 0).unwrap();

      toggleDelete();

      setRefresh(true);
      setTimeout(() => {
        setRefresh(false);
      }, 100);
    } catch (err) {}
  }, [deleteWarehouse, warehouseItem?.id, toggleDelete]);

  const actionColumn: ColumnDef<WarehouseTruckItinerariesListTypes> = useMemo(
    () => ({
      id: 'action',
      size: 110,
      header: 'Actions',
      cell: ({ row }) => {
        const id = row?.original?.id;
        return (
          <ActionColumnMenu
            customEdit={
              <div
                className="flex items-center justify-center gap-2 border border-text-tertiary rounded-md px-3 py-1"
                onClick={() => {
                  if (id) {
                    setOpen({
                      state: true,
                      action: WAREHOUSE_POPUPS.TRUCK_ITINERARY,
                    });
                    handleSetActiveTab(WAREHOUSE_POPUPS.TRUCK_ITINERARY);
                    setTruckId(`${id}`);
                  }
                }}
              >
                <EditIcon className="w-5 h-5" /> Edit details
              </div>
            }
            onDelete={() => toggleDelete(row.original)}
            contentClassName="w-fit"
          />
        );
      },
    }),
    [handleSetActiveTab, toggleDelete]
  );

  const memoizedColumns = useMemo(() => {
    if (!tableColumns?.length) return [];

    const formattedColumns = tableColumns
      .filter((column: any) => column.enabled) // Ensure only enabled columns are included
      .map((column: any) => ({
        accessorKey: column.accessorKey,
        header: column.header,
        enableSorting: column.enableSorting || false,
        size: column.size || 200, // Default size if not provided
        cell: ({ row }: any) => {
          const Component = componentMap[column.component]; // Get the component based on the key
          return Component ? (
            <Component value={row?.original?.[column.accessorKey]} />
          ) : (
            row?.original?.[column.accessorKey]
          );
        },
      }));

    return [
      {
        accessorKey: 'ordering',
        header: () => (
          <ColumnReOrdering
            isOpen={openColumnOrdering}
            handleOrderingColumn={handleOrderingColumn}
            tableColumns={tableColumns}
            setOpenColumnOrdering={setOpenColumnOrdering}
          />
        ),
        size: 50,
      },
      ...formattedColumns,
      actionColumn,
    ];
  }, [
    tableColumns,
    componentMap,
    openColumnOrdering,
    handleOrderingColumn,
    setOpenColumnOrdering,
    actionColumn,
  ]);

  useEffect(() => {
    if (!search) {
      dispatch(clearAllFilters());
    }
  }, [dispatch, search]);

  useEffect(() => {
    if (search) {
      dispatch(
        setFilter([
          {
            label: 'Vendor',
            value: search,
            name: 'vendor',
            tagValue: search,
            operator: 'equals',
          } as any,
        ])
      );
    }
  }, [dispatch, search]);

  // useEffect(() => {
  //   setOpen({
  //     state: true,
  //     action: WAREHOUSE_POPUPS.TRUCK_ITINERARY,
  //   });
  //   handleSetActiveTab(WAREHOUSE_POPUPS.TRUCK_ITINERARY);
  //   setTruckId('1');
  // }, []);

  return (
    <div className="p-6">
      <AppTableContextProvider defaultSort={[{ id: 'jobNo', desc: true }]}>
        <AppDataTable
          url={WAREHOUSE_TRUCK_ITINERARIES_API_ROUTES.ALL}
          columns={memoizedColumns}
          heading="Truck Itineraries"
          enableSearch={true}
          enablePagination={true}
          tableClassName="max-h-[400px] 2xl:max-h-[580px] overflow-auto"
          refreshList={refresh}
          searchKey="id"
          enableFilter
          filter={filter}
          handleClearFilter={handleClearFilter}
          filterClassName="w-[550px]"
          filterContent={<Filter setIsFilterOpen={setIsFilterOpen} />}
          setIsFilterOpen={setIsFilterOpen}
          isFilterOpen={isFilterOpen}
          customToolBar={CustomToolbar}
          dropdownContentClassName="p-4 flex flex-col gap-2 w-full"
          // enableRowSelection={true}
          // onRowSelectionChange={setRowSelection}
        />
      </AppTableContextProvider>
      <AppSpinner
        overlay
        isLoading={
          newItemLoading ||
          isFetching ||
          isLoadingPrintList ||
          isLoadingDownloadFile
        }
      />
      <AppConfirmationModal
        description={
          <div>
            Are you sure you want to delete
            <strong> {warehouseItem?.id ?? ''}</strong> ?
          </div>
        }
        open={openDelete}
        onOpenChange={toggleDelete}
        handleCancel={toggleDelete}
        handleSubmit={handleDelete}
        isLoading={deleteLoading}
      />
      <BreadcrumbDialogRenderer
        activeTab={activeTab}
        isOpen={open.state}
        listItems={listItems}
        onOpenChange={onOpenChange}
        setActiveTab={handleSetActiveTab}
        className={cn(
          'max-w-[60%]  lg:max-w-[45%] 2xl:max-w-[40%]',
          activeTab === WAREHOUSE_POPUPS.TRUCK_ITINERARY &&
            'max-w-[80%]  lg:max-w-[75%] 2xl:max-w-[90%]'
        )}
        contentClassName={cn(
          'h-[480px] 2xl:h-[550px] overflow-y-auto',
          activeTab === WAREHOUSE_POPUPS.TRUCK_ITINERARY &&
            'h-[350px] md:h-[450px] lg:h-[650px] 2xl:h-[800px] overflow-y-auto'
        )}
      />
    </div>
  );
};

export default TruckItineraries;
