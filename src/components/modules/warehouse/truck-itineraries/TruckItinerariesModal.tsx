import ExcelIcon from '@/assets/icons/ExcelIcon';
import AppButton from '@/components/common/app-button';
import AppDataTable from '@/components/common/app-data-table';
import DataTable from '@/components/common/data-tables';
import { InputField, NumberInputField } from '@/components/forms';
import DatePicker from '@/components/forms/date-picker';
import SelectDropDown from '@/components/forms/select-dropdown';
import { WAREHOUSE_TRUCK_ITINERARIES_API_ROUTES } from '@/constants/api-constants';
import { cn, formatPhoneNumber } from '@/lib/utils';
import {
  useGetTruckOrderListQuery,
  useLazyGetWarehouseColumsForOrderJobQuery,
  useLazyGetWarehouseColumsForSubRentalQuery,
  useSaveTruckOrderListMutation,
  useUpdateWarehouseColumsForOrderJobMutation,
  useUpdateWarehouseColumsForSubRentalMutation,
} from '@/redux/features/warehouse/truckItineraries.api';
import { SortingStateType } from '@/types/common.types';
import {
  FormValues,
  ItineraryItem,
  TRUCK_WAREHOUSE_TYPE,
  TRUCK_WAREHOUSE_TYPE_OPTIONS,
} from '@/types/warehouse.types';
import { ColumnDef, RowSelectionState } from '@tanstack/react-table';
import {
  EditIcon,
  FileIcon,
  PrinterIcon,
  Trash2Icon,
  TruckIcon,
} from 'lucide-react';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useFieldArray, useForm } from 'react-hook-form';
import ColumnReOrdering from '@/components/common/column-reording';
import ActionColumnMenu from '@/components/common/data-tables/ActionColumn';
import AppSpinner from '@/components/common/app-spinner';
import RadioField from '@/components/forms/radio-field';

interface TruckItinerariesModalProps {
  truckId: string;
  setOpen: React.Dispatch<
    React.SetStateAction<{ state: boolean; action: string }>
  >;
  setActiveTab: React.Dispatch<React.SetStateAction<string>>;
}

const TruckItinerariesModal = ({
  truckId,
  setActiveTab,
  setOpen,
}: TruckItinerariesModalProps) => {
  const [refresh, setRefresh] = useState<boolean>(false);
  const [openColumnOrdering, setOpenColumnOrdering] = useState<boolean>(false);

  const { data: getTruckOrderList, isLoading: isLoadingGetTruckOrderList } =
    useGetTruckOrderListQuery(truckId, { skip: !truckId });
  const [saveTruckDetails, { isLoading: isLoadingSaveTruckDetails }] =
    useSaveTruckOrderListMutation();

  const [
    getWarehouseColumsForOrderJob,
    { data, isFetching: isFetchingOrderJobQuery },
  ] = useLazyGetWarehouseColumsForOrderJobQuery();

  const [
    getWarehouseColumsForSubRental,
    { data: dataSubRental, isFetching: isFetchingSubRental },
  ] = useLazyGetWarehouseColumsForSubRentalQuery();

  const form = useForm<FormValues>({
    defaultValues: {
      dateFrom: '',
      dateTo: '',
      truck: '',
      routeDescription: '',
      driver: '',
      allowedWeight: 0,
      truckWeight: 0,
      itineraries:
        Array.isArray(getTruckOrderList?.data) && getTruckOrderList.data
          ? getTruckOrderList.data
          : [],
      view: TRUCK_WAREHOUSE_TYPE.ORDERS ?? '',
    },
  });

  const selectedView = form.watch('view');

  const tableColumns = useMemo(() => {
    return selectedView === TRUCK_WAREHOUSE_TYPE.ORDERS
      ? data?.data
      : dataSubRental?.data;
  }, [data?.data, dataSubRental?.data, selectedView]);

  const [updateWarehouseColums, { isLoading: newItemLoading }] =
    useUpdateWarehouseColumsForOrderJobMutation();

  const [
    updateWarehouseColumsSubRental,
    { isLoading: newItemLoadingSubrental },
  ] = useUpdateWarehouseColumsForSubRentalMutation();

  const handleOrderingColumn = useCallback(
    async (formData: any) => {
      const updatedColumns = tableColumns.map((item: any) => ({
        ...item,
        enabled: formData[item.accessorKey],
      }));

      if (selectedView === TRUCK_WAREHOUSE_TYPE.ORDERS) {
        await updateWarehouseColums(updatedColumns).unwrap();
      } else {
        await updateWarehouseColumsSubRental(updatedColumns).unwrap();
      }
      setOpenColumnOrdering(false);
    },
    [
      selectedView,
      tableColumns,
      updateWarehouseColums,
      updateWarehouseColumsSubRental,
    ]
  );

  const actionColumn: ColumnDef<any> = useMemo(
    () => ({
      id: 'action',
      size: 110,
      header: 'Actions',
      cell: ({ row }) => {
        const id = row?.original?.id;
        return (
          <ActionColumnMenu
            customEdit={
              <div
                className="flex items-center justify-center gap-2 border border-text-tertiary rounded-md px-3 py-1"
                onClick={() => {
                  if (id) {
                    setRefresh(true);
                    setTimeout(() => {
                      setRefresh(false);
                    }, 100);
                  }
                }}
              >
                <EditIcon className="w-5 h-5" /> Edit details
              </div>
            }
            contentClassName="w-fit"
          />
        );
      },
    }),
    []
  );

  const componentMap: any = useMemo(
    () => ({
      PhoneWidget: ({ value }: any) => formatPhoneNumber(value),
    }),
    []
  );

  const memoizedColumns = useMemo(() => {
    if (!tableColumns?.length) return [];

    const formattedColumns = tableColumns
      .filter((column: any) => column.enabled) // Ensure only enabled columns are included
      .map((column: any) => ({
        accessorKey: column.accessorKey,
        header: column.header,
        enableSorting: column.enableSorting || false,
        size: column.size || 200, // Default size if not provided
        cell: ({ row }: any) => {
          const Component = componentMap[column.component]; // Get the component based on the key
          return Component ? (
            <Component value={row?.original?.[column.accessorKey]} />
          ) : (
            row?.original?.[column.accessorKey]
          );
        },
      }));

    return [
      {
        accessorKey: 'ordering',
        header: () => (
          <ColumnReOrdering
            isOpen={openColumnOrdering}
            handleOrderingColumn={handleOrderingColumn}
            tableColumns={tableColumns}
            setOpenColumnOrdering={setOpenColumnOrdering}
          />
        ),
        size: 50,
      },
      ...formattedColumns,
      actionColumn,
    ];
  }, [
    tableColumns,
    actionColumn,
    componentMap,
    openColumnOrdering,
    handleOrderingColumn,
  ]);

  const { fields, append } = useFieldArray({
    control: form.control,
    name: 'itineraries',
  });
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const [sorting, setSorting] = useState<SortingStateType[]>([
    { id: 'seqId', desc: true },
    { id: 'orderId', desc: true },
    { id: 'itineraryDescription', desc: true },
    { id: 'date', desc: true },
    { id: 'date', desc: true },
    { id: 'time', desc: true },
    { id: 'delPu', desc: true },
    { id: 'timeFrom', desc: true },
    { id: 'timeFrom', desc: true },
    { id: 'timeTo', desc: true },
    { id: 'startTime', desc: true },
    { id: 'travelTime', desc: true },
    { id: 'estDpTime', desc: true },
    { id: 'setupTime', desc: true },
    { id: 'actualDpTime', desc: true },
    { id: 'actualSetupTime', desc: true },
  ]);
  const onScrollRefItineraryData = useRef(null);

  const onDateChange = useCallback(
    async (date: Date | string | undefined, name: 'dateFrom' | 'dateTo') => {
      form.setValue(name, date);
    },
    [form]
  );

  const handleCancel = () => {
    setActiveTab('');
    setOpen({ state: false, action: '' });
  };

  const handleDeleteRow = useCallback(
    (index: number) => {
      const current = form.getValues('itineraries');
      const updated = current.filter((_, i) => i !== index);
      const reindexed = updated.map((item, idx) => ({
        ...item,
        seqId: idx + 1,
        id: idx + 1,
      }));
      form.setValue('itineraries', reindexed);
    },
    [form]
  );

  const columnsDetailsInfo: ColumnDef<ItineraryItem>[] = useMemo(() => {
    return [
      {
        accessorKey: 'seqId',
        header: 'Seq #',
        size: 100,
        enableSorting: true,
      },
      {
        accessorKey: 'orderId',
        header: 'Order #',
        size: 130,
        enableSorting: true,
        cell: ({ row }) => {
          return (
            <div className="flex flex-col">
              <InputField
                form={form}
                name={`itineraries.${row.index}.orderId`}
                placeholder="Enter Order #"
                aria-describedby="orderId-helper"
              />
            </div>
          );
        },
      },
      {
        accessorKey: 'itineraryDescription',
        header: 'Itinerary Description',
        size: 230,
        enableSorting: true,
        cell: ({ row }) => {
          return (
            <div className="flex flex-col">
              <InputField
                form={form}
                name={`itineraries.${row.index}.itineraryDescription`}
                placeholder="Enter Itinerary Description"
                aria-describedby="itineraryDescription-helper"
              />
            </div>
          );
        },
      },
      {
        accessorKey: 'date',
        header: 'Date',
        size: 150,
        enableSorting: true,
      },
      {
        accessorKey: 'time',
        header: 'TIme',
        size: 130,
        enableSorting: true,
      },
      {
        accessorKey: 'delPu',
        header: 'Del / PU',
        size: 130,
        enableSorting: true,
      },
      {
        accessorKey: 'timeFrom',
        header: 'Time From',
        size: 150,
        enableSorting: true,
      },
      {
        accessorKey: 'timeTo',
        header: 'Time To',
        size: 150,
        enableSorting: true,
      },
      {
        accessorKey: 'startTime',
        header: 'Start Time',
        size: 190,
        enableSorting: true,
        cell: ({ row }) => {
          return (
            <div className="flex flex-col">
              <InputField
                form={form}
                name={`itineraries.${row.index}.startTime`}
                placeholder="Enter Start Time"
                aria-describedby="startTime-helper"
              />
            </div>
          );
        },
      },
      {
        accessorKey: 'travelTime',
        header: 'Travel Time (Hrs)',
        size: 190,
        enableSorting: true,
        cell: ({ row }) => {
          return (
            <div className="flex flex-col">
              <InputField
                form={form}
                name={`itineraries.${row.index}.travelTime`}
                placeholder="Enter Travel Time"
                aria-describedby="travelTime-helper"
              />
            </div>
          );
        },
      },
      {
        accessorKey: 'estDpTime',
        header: 'Est. D / P Time',
        size: 170,
        enableSorting: true,
      },
      {
        accessorKey: 'setupTime',
        header: 'Setup Time (Hrs)',
        size: 190,
        enableSorting: true,
        cell: ({ row }) => {
          return (
            <div className="flex flex-col">
              <InputField
                form={form}
                name={`itineraries.${row.index}.setupTime`}
                placeholder="Enter Setup Time (Hrs)"
                aria-describedby="setupTime-helper"
              />
            </div>
          );
        },
      },
      {
        accessorKey: 'actualDpTime',
        header: 'Actual D / P Time',
        size: 190,
        enableSorting: true,
        cell: ({ row }) => {
          return (
            <div className="flex flex-col">
              <InputField
                form={form}
                name={`itineraries.${row.index}.actualDpTime`}
                placeholder="Enter Actual D / P Time"
                aria-describedby="actualDpTime-helper"
              />
            </div>
          );
        },
      },
      {
        accessorKey: 'actualSetupTime',
        header: 'Actual Setup Time (Hrs)',
        size: 250,
        enableSorting: true,
        cell: ({ row }) => {
          return (
            <div className="flex flex-col">
              <InputField
                form={form}
                name={`itineraries.${row.index}.actualSetupTime`}
                placeholder="Enter Actual Setup Time (Hrs)"
                aria-describedby="actualSetupTime-helper"
              />
            </div>
          );
        },
      },
      {
        id: 'action',
        header: 'Actions',
        size: 80,
        cell: ({ row }) => {
          return (
            <AppButton
              label={
                <>
                  <Trash2Icon className="w-5 h-5 text-red-600" />{' '}
                  <span className="text-red-600">Delete Item</span>
                </>
              }
              variant="neutral"
              iconClassName="w-4 h-4"
              onClick={() => {
                handleDeleteRow(row.index);
              }}
            />
          );
        },
      },
    ];
  }, [form, handleDeleteRow]);

  const handleSaveOrder = async (data: FormValues) => {
    await saveTruckDetails({
      truckOrderList: data.itineraries,
      id: truckId,
    }).unwrap();
  };

  const handleAddNewRow = () => {
    const currentLength = form.getValues('itineraries')?.length ?? 0;
    append({
      id: currentLength + 1,
      seqId: currentLength + 1,
      orderId: '',
      itineraryDescription: '',
      date: '',
      time: '',
      delPu: '',
      timeFrom: '',
      timeTo: '',
      startTime: '',
      travelTime: '',
      estDpTime: '',
      setupTime: '',
      actualDpTime: '',
      actualSetupTime: '',
    });
  };

  const addSelectedItinerary = async () => {
    if (rowSelection?.jobNo && rowSelection?.orderId) {
    }
  };

  useEffect(() => {
    if (selectedView === TRUCK_WAREHOUSE_TYPE.ORDERS) {
      getWarehouseColumsForOrderJob();
    } else {
      getWarehouseColumsForSubRental();
    }
  }, [
    selectedView,
    getWarehouseColumsForOrderJob,
    getWarehouseColumsForSubRental,
  ]);

  useEffect(() => {
    if (onScrollRefItineraryData.current) {
      const container = onScrollRefItineraryData.current as HTMLDivElement;
      container.scrollTo({
        top: container.scrollHeight,
        behavior: 'smooth',
      });
    }
  }, [fields.length]);

  return (
    <div>
      <div className="flex gap-2 mb-5">
        <AppButton
          label={<PrinterIcon className="text-white" />}
          variant="neutral"
          className="bg-brand-teal-Default hover:bg-brand-teal-secondary rounded-[50%] w-10 h-10"
          tooltip="Print"
        />
        <AppButton
          label={<TruckIcon className="text-white" />}
          variant="neutral"
          className="bg-brand-teal-Default hover:bg-brand-teal-secondary rounded-[50%] w-10 h-10"
          tooltip="Equipment"
        />
        <AppButton
          label={<FileIcon className="text-white" />}
          variant="neutral"
          className="bg-brand-teal-Default hover:bg-brand-teal-secondary rounded-[50%] w-10 h-10"
          tooltip="Show Order"
        />
        <AppButton
          label={<FileIcon className="text-white" />}
          variant="neutral"
          className="bg-brand-teal-Default hover:bg-brand-teal-secondary rounded-[50%] w-10 h-10"
          tooltip="Show Order & Sales"
        />
        <AppButton
          label={<ExcelIcon />}
          variant="neutral"
          className="bg-brand-teal-Default hover:bg-brand-teal-secondary rounded-[50%] w-10 h-10"
          tooltip="Summary Extract"
        />
        <AppButton
          label={<ExcelIcon />}
          variant="neutral"
          className="bg-brand-teal-Default hover:bg-brand-teal-secondary rounded-[50%] w-10 h-10"
          tooltip="Detail Extract"
        />
      </div>
      <div>
        <div className="grid grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-3">
          <div>
            <DatePicker
              form={form}
              name="dateFrom"
              label="Date From"
              placeholder="Select Date"
              onDateChange={(data) => onDateChange(data, 'dateFrom')}
              enableInput
              pClassName="col-span-2"
              isRenderFirst={true}
              disabled
            />
          </div>
          <div>
            <DatePicker
              form={form}
              name="dateTo"
              label="Date To"
              placeholder="Select Date"
              onDateChange={(data) => onDateChange(data, 'dateTo')}
              enableInput
              pClassName="col-span-2"
              isRenderFirst={true}
              disabled
            />
          </div>
          <SelectDropDown
            form={form}
            name="truck"
            label="Truck"
            // optionsList={[]}
            optionsList={[
              { label: 'Truck 1 - TRK-001', value: 'TRK-001' },
              { label: 'Truck 2 - TRK-002', value: 'TRK-002' },
            ]}
            placeholder="Select Truck"
            allowClear={false}
            aria-describedby="truck-helper"
          />
          <InputField
            form={form}
            name="routeDescription"
            label="Route Description"
            placeholder="Enter Route Description"
            aria-describedby="routeDescription-helper"
          />
          <SelectDropDown
            form={form}
            name="driver"
            label="Driver"
            // optionsList={[]}
            optionsList={[
              { label: 'John Doe', value: 'John Doe' },
              { label: 'Jane Smith', value: 'Jane Smith' },
            ]}
            allowClear={false}
            placeholder="Select Driver"
            aria-describedby="driver-helper"
          />
          <NumberInputField
            form={form}
            name="allowedWeight"
            label="Allowed Weight"
            placeholder="Enter Allowed Weight"
            aria-describedby="allowedWeight-helper"
            prefix="$"
            disabled
          />
          <NumberInputField
            form={form}
            name="truckWeight"
            label="Truck Weight"
            placeholder="Enter Truck Weight"
            aria-describedby="truckWeight-helper"
            disabled
            prefix="$"
          />
        </div>
      </div>
      {/* Tables */}
      <div className="flex flex-col h-[700px] gap-2 mt-5">
        <div>
          <div className={cn('w-full h-[350px]')}>
            <DataTable
              onScrollRef={onScrollRefItineraryData}
              data={fields ?? []}
              columns={columnsDetailsInfo ?? []}
              enablePagination={false}
              tableClassName="max-h-[310px] overflow-y-auto"
              bindingKey="id"
              totalItems={fields.length}
              sorting={sorting}
              setSorting={setSorting}
              isLoading={isLoadingGetTruckOrderList}
            />
          </div>
          <div className="w-full flex justify-end z-10">
            <div className="flex flex-col sm:flex-row sm:gap-5 gap-3">
              <AppButton
                label="Add New Row"
                variant="primary"
                onClick={handleAddNewRow}
                className="h-10"
                disabled={
                  isLoadingSaveTruckDetails || isLoadingGetTruckOrderList
                }
              />
              <AppButton
                label="Save Order"
                onClick={form.handleSubmit(handleSaveOrder)}
                className="w-32"
                isLoading={isLoadingSaveTruckDetails}
              />
              <AppButton
                label="Cancel"
                onClick={handleCancel}
                className="w-28"
                variant="neutral"
                disabled={
                  isLoadingSaveTruckDetails || isLoadingGetTruckOrderList
                }
              />
            </div>
          </div>
        </div>
        <div>
          <div className={cn('w-full h-[350px]')}>
            <AppDataTable
              url={
                selectedView === TRUCK_WAREHOUSE_TYPE.ORDERS
                  ? WAREHOUSE_TRUCK_ITINERARIES_API_ROUTES.LIST_TRUCK_ITINERARY_JOB_ORDER(
                      truckId
                    )
                  : WAREHOUSE_TRUCK_ITINERARIES_API_ROUTES.LIST_TRUCK_ITINERARY_SUB_RENTAL(
                      truckId
                    )
              }
              columns={memoizedColumns}
              heading=""
              tableClassName="max-h-[400px] 2xl:max-h-[580px] overflow-auto"
              searchKey="id"
              refreshList={refresh}
              filterClassName="w-[550px]"
              dropdownContentClassName="p-4 flex flex-col gap-2 w-full"
              enableRowSelection={true}
              onRowSelectionChange={setRowSelection}
            />
          </div>
          <div className="w-full flex justify-end z-10 mb-5">
            <div className="flex flex-col sm:flex-row sm:gap-5 gap-3">
              <AppButton
                label={'Add Selected to Itinerary'}
                variant="primary"
                className="h-10"
                onClick={addSelectedItinerary}
              />
              <div className="flex flex-row justify-center items-center gap-4">
                <label>View:</label>
                <RadioField
                  form={form}
                  name="view"
                  options={TRUCK_WAREHOUSE_TYPE_OPTIONS}
                  className="flex flex-row justify-center items-center w-full h-full"
                />
              </div>
              <AppButton
                label="Cancel"
                onClick={handleCancel}
                className="w-28"
                variant="neutral"
                disabled={
                  isLoadingSaveTruckDetails || isLoadingGetTruckOrderList
                }
              />
            </div>
          </div>
        </div>
      </div>
      <AppSpinner
        overlay
        isLoading={
          newItemLoading ||
          isFetchingSubRental ||
          isFetchingOrderJobQuery ||
          newItemLoadingSubrental
        }
      />
    </div>
  );
};

export default TruckItinerariesModal;
