import ExcelIcon from '@/assets/icons/ExcelIcon';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import AppDataTable from '@/components/common/app-data-table';
import AppTableContextProvider, {
  AppTableContext,
} from '@/components/common/app-data-table/AppTableContext';
import AppSpinner from '@/components/common/app-spinner';
import { UseToast } from '@/components/ui/toast/ToastContainer';
import { WAREHOUSE_ORDER_DELIVERIES_PICKUP_API_ROUTES } from '@/constants/api-constants';
import { ROUTES } from '@/constants/routes-constants';
import { useDownloadFile } from '@/hooks/useDownloadFile';
import {
  cn,
  formatPhoneNumber,
  getPaginationObject,
  getQueryParam,
  updateQueryParam,
} from '@/lib/utils';
import {
  useDeleteWarehouseMutation,
  useDownloadFileMutation,
  useGetListForPrintQuery,
  useGetWarehouseColumsQuery,
  useUpdateWarehouseColumsMutation,
} from '@/redux/features/warehouse/warehouseOrderDeliveriesPickups.api';
import {
  clearFilter,
  setFilter,
  clearAllFilters,
} from '@/redux/features/warehouse/warehouseSlice';
import { RootState } from '@/redux/store';
import { ColumnDef, RowSelectionState } from '@tanstack/react-table';
import {
  ChevronDown,
  EditIcon,
  PrinterIcon,
  Truck,
  Wrench,
} from 'lucide-react';
import { useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link } from 'react-router-dom';
import ActionColumnMenu from '@/components/common/data-tables/ActionColumn';
import ColumnReOrdering from '@/components/common/column-reording';
import Filter from './Filter';
import {
  OpenDialogType,
  OrderDeliveriesPickupsForm,
  WAREHOUSE_POPUPS,
  WarehouseOrderDeliveriesPickupsListTypes,
} from '@/types/warehouse.types';
import DatePicker from '@/components/forms/date-picker';
import { useForm } from 'react-hook-form';
import SelectDropDown from '@/components/forms/select-dropdown';
import AppButton from '@/components/common/app-button';
import DisplayItems from '../DisplayItems';
import BreadcrumbDialogRenderer from '@/components/common/BreadcrumbDialogRenderer';
import Print from '../Print';

const OrderDeliveriesPickups = () => {
  const search = getQueryParam('search') as string;

  const dispatch = useDispatch();
  const { sorting } = useContext(AppTableContext);
  const [openDelete, setOpenDelete] = useState<boolean>(false);
  const [warehouseItem, setWarehouseItem] =
    useState<WarehouseOrderDeliveriesPickupsListTypes | null>(null);
  const [refresh, setRefresh] = useState<boolean>(false);
  const [searchParams, setSearchParams] = useState<string>('');
  const [isFilterOpen, setIsFilterOpen] = useState<boolean>(false);
  const [openColumnOrdering, setOpenColumnOrdering] = useState<boolean>(false);
  const filter = useSelector((state: RootState) => state.warehouse.filters);

  const [downloadPrintFile, { isLoading: isLoadingDownloadFile }] =
    useDownloadFileMutation();
  const { data: printList, isLoading: isLoadingPrintList } =
    useGetListForPrintQuery(null);

  const form = useForm<OrderDeliveriesPickupsForm>({
    defaultValues: { print: printList?.data ?? [] },
  });

  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const [newItineraryWarningModal, setNewItineraryWarningModal] =
    useState<boolean>(false);

  const [activeTab, setActiveTab] = useState<string>('');
  const [open, setOpen] = useState<OpenDialogType>({
    state: false,
    action: '',
  });

  const onOpenChange = useCallback(() => {
    setOpen({ state: false, action: '' });
  }, []);

  const handleSetActiveTab = useCallback((tab: string) => {
    setActiveTab(tab ?? '');
  }, []);

  const handleCancel = useCallback(() => {
    setOpen({ state: false, action: '' });
  }, []);

  // tab list
  const listItems = useMemo(() => {
    const tabList = [
      {
        label: 'Items',
        value: WAREHOUSE_POPUPS.VIEW_ITEMS,
        content: <DisplayItems form={form} handleCancel={handleCancel} />,
      },
      {
        label: 'New Itinerary',
        value: WAREHOUSE_POPUPS.NEW_ITINERARY,
        content: (
          <div className="flex gap-2 flex-col">
            <DatePicker
              form={form}
              name="dateFrom"
              label="Date From"
              placeholder="Select Date"
              onDateChange={(data) => onDateChange(data, 'dateFrom')}
              enableInput
              pClassName="col-span-2"
              isRenderFirst={true}
            />
            <DatePicker
              form={form}
              name="dateTo"
              label="Date To"
              placeholder="Select Date"
              onDateChange={(data) => onDateChange(data, 'dateTo')}
              enableInput
              pClassName="col-span-2"
              isRenderFirst={true}
            />
            <SelectDropDown
              form={form}
              name="truck"
              label="Truck"
              optionsList={[]}
              placeholder="Select Truck"
              disabled
              aria-describedby="truck-helper"
            />
            <div className="flex justify-end gap-4 fixed bottom-0 right-6 bg-white pb-4">
              <AppButton label="Ok" onClick={() => {}} className="w-28" />
              <AppButton
                label="Cancel"
                onClick={handleCancel}
                className="w-28"
                variant="neutral"
              />
            </div>
          </div>
        ),
      },
      {
        label: 'Print',
        value: WAREHOUSE_POPUPS.PRINT,
        content: (
          <Print
            handleCancel={handleCancel}
            form={form}
            downloadPrintFile={downloadPrintFile}
          />
        ),
      },
    ];
    return tabList?.filter((tab) => [activeTab]?.includes(tab?.value));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeTab, form]);

  const { data, refetch, isFetching } = useGetWarehouseColumsQuery();
  const [updateWarehouseColums, { isLoading: newItemLoading }] =
    useUpdateWarehouseColumsMutation();

  const componentMap: any = useMemo(
    () => ({
      PhoneWidget: ({ value }: any) => formatPhoneNumber(value),
    }),
    []
  );
  const { downloadFile } = useDownloadFile();
  const toast = UseToast();

  const [deleteWarehouse, { isLoading: deleteLoading }] =
    useDeleteWarehouseMutation();

  const toggleDelete = useCallback(
    (row?: WarehouseOrderDeliveriesPickupsListTypes) => {
      setWarehouseItem(row ? row : null);
      setOpenDelete((prev) => !prev);
    },
    []
  );

  const onDateChange = useCallback(
    async (date: Date | string | undefined, name: 'dateFrom' | 'dateTo') => {
      form.setValue(name, date);
    },
    [form]
  );

  // Custom toolbar component
  const CustomToolbar = (
    <div className="flex space-x-3">
      <ActionColumnMenu
        triggerClassName="w-fit hover:bg-white"
        triggerContent={
          <div>
            <AppButton
              label={
                <div className="flex items-center gap-2">
                  <Wrench className="w-5 h-5" />
                  Other Processes
                  <ChevronDown className="w-5 h-5" />
                </div>
              }
              variant="neutral"
            />
          </div>
        }
        contentClassName="p-2 w-fit"
        dropdownMenuList={[
          {
            label: 'View Items for Selected Sub-Rentals',
            onClick: () => {
              // jobNo
              if (rowSelection?.jobNo) {
                setOpen({
                  state: true,
                  action: WAREHOUSE_POPUPS.VIEW_ITEMS,
                });
                handleSetActiveTab(WAREHOUSE_POPUPS.VIEW_ITEMS);
              }
            },
            icon: <Truck />,
          },
          {
            label: 'Create Itinerary from Selected Sub-Rentals',
            onClick: () => {
              // jobNo
              if (rowSelection?.jobNo) {
                setOpen({
                  state: true,
                  action: WAREHOUSE_POPUPS.NEW_ITINERARY,
                });
                handleSetActiveTab(WAREHOUSE_POPUPS.NEW_ITINERARY);
              } else {
                setNewItineraryWarningModal(true);
              }
            },
            icon: <Truck />,
          },
        ]}
      />
      <AppButton
        label={
          <div className="flex items-center gap-2">
            <PrinterIcon />
            Print
          </div>
        }
        variant="neutral"
        onClick={() => {
          setOpen({
            state: true,
            action: WAREHOUSE_POPUPS.PRINT,
          });
          handleSetActiveTab(WAREHOUSE_POPUPS.PRINT);
        }}
      />
    </div>
  );

  const handleDelete = useCallback(async () => {
    try {
      await deleteWarehouse(warehouseItem?.id || 0).unwrap();

      toggleDelete();

      setRefresh(true);
      setTimeout(() => {
        setRefresh(false);
      }, 100);
    } catch (err) {}
  }, [deleteWarehouse, warehouseItem?.id, toggleDelete]);

  const tableColumns = useMemo(() => {
    return data?.data;
  }, [data?.data]);

  const handleOrderingColumn = useCallback(
    async (formData: any) => {
      const updatedColumns = tableColumns.map((item: any) => ({
        ...item,
        enabled: formData[item.accessorKey],
      }));

      await updateWarehouseColums(updatedColumns).unwrap();
      setOpenColumnOrdering(false);
      await refetch();
    },
    [tableColumns, updateWarehouseColums, refetch]
  );

  // handle Clear function
  const handleClearFilter = useCallback(
    (key: string) => {
      dispatch(clearFilter(key));
      setIsFilterOpen(false);
      if (search) {
        updateQueryParam(null, 'search');
      }
    },
    [dispatch, search]
  );

  const handleExtractVendorsToExcel = useCallback(() => {
    const normalizedFilters = Array.isArray(filter)
      ? filter.map((f: any) => ({
          field: f.name || f.label,
          value: f.value,
          operator: f.operator,
        }))
      : [];
    const payload = getPaginationObject({
      pagination: { pageIndex: 0, pageSize: 0 },
      sorting,
      filters: [
        ...normalizedFilters,
        { field: 'filter', value: searchParams, operator: 'Contains' },
      ],
    });
    const response = downloadFile({
      url: WAREHOUSE_ORDER_DELIVERIES_PICKUP_API_ROUTES.EXPORT_CSV,
      method: 'POST',
      body: payload,
    });
    toast.promise(response, {
      loading: 'Downloading file...',
      success: 'File downloaded successfully.',
      error: 'Failed to download file',
    });
  }, [filter, sorting, searchParams, downloadFile, toast]);

  const DropdownMenu = useMemo(() => {
    return [
      {
        label: 'Extract Order Deliveries & Pickups',
        onClick: handleExtractVendorsToExcel,
        icon: <ExcelIcon />,
      },
    ];
  }, [handleExtractVendorsToExcel]);

  useEffect(() => {
    if (search) {
      dispatch(
        setFilter([
          {
            label: 'Vendor',
            value: search,
            name: 'vendor',
            tagValue: search,
            operator: 'equals',
          } as any,
        ])
      );
    }
  }, [dispatch, search]);

  useEffect(() => {
    if (!search) {
      dispatch(clearAllFilters());
    }
  }, [dispatch, search]);

  const actionColumn: ColumnDef<WarehouseOrderDeliveriesPickupsListTypes> =
    useMemo(
      () => ({
        id: 'action',
        size: 110,
        header: 'Actions',
        cell: ({ row }) => {
          const orderId = row?.original?.orderid;
          return (
            <ActionColumnMenu
              customEdit={
                <Link
                  to={`${ROUTES?.ORDERS}?id=${orderId}&tab=information`}
                  className="flex items-center justify-center gap-2 border border-text-tertiary rounded-md px-3 py-1"
                >
                  <EditIcon className="w-5 h-5" /> Edit details
                </Link>
              }
              onDelete={() => toggleDelete(row.original)}
              contentClassName="w-fit"
            />
          );
        },
      }),
      [toggleDelete]
    );

  const memoizedColumns = useMemo(() => {
    if (!tableColumns?.length) return [];

    const formattedColumns = tableColumns
      .filter((column: any) => column.enabled) // Ensure only enabled columns are included
      .map((column: any) => ({
        accessorKey: column.accessorKey,
        header: column.header,
        enableSorting: column.enableSorting || false,
        size: column.size || 200, // Default size if not provided
        cell: ({ row }: any) => {
          const Component = componentMap[column.component]; // Get the component based on the key
          return Component ? (
            <Component value={row?.original?.[column.accessorKey]} />
          ) : (
            row?.original?.[column.accessorKey]
          );
        },
      }));

    return [
      {
        accessorKey: 'ordering',
        header: () => (
          <ColumnReOrdering
            isOpen={openColumnOrdering}
            handleOrderingColumn={handleOrderingColumn}
            tableColumns={tableColumns}
            setOpenColumnOrdering={setOpenColumnOrdering}
          />
        ),
        size: 50,
      },
      ...formattedColumns,
      actionColumn,
    ];
  }, [
    tableColumns,
    componentMap,
    openColumnOrdering,
    handleOrderingColumn,
    setOpenColumnOrdering,
    actionColumn,
  ]);

  return (
    <div className="p-6">
      <AppTableContextProvider defaultSort={[{ id: 'jobNo', desc: true }]}>
        <AppDataTable
          url={WAREHOUSE_ORDER_DELIVERIES_PICKUP_API_ROUTES.ALL}
          columns={memoizedColumns}
          heading="Order Deliveries & Pickups"
          enableSearch={true}
          enablePagination={true}
          tableClassName="max-h-[400px] 2xl:max-h-[580px] overflow-auto"
          refreshList={refresh}
          searchKey="jobNo"
          setSearchParams={setSearchParams}
          enableFilter
          filter={filter}
          handleClearFilter={handleClearFilter}
          filterClassName="w-[550px]"
          filterContent={<Filter setIsFilterOpen={setIsFilterOpen} />}
          setIsFilterOpen={setIsFilterOpen}
          isFilterOpen={isFilterOpen}
          dropdownMenus={DropdownMenu}
          dropdownContentClassName="p-4 flex flex-col gap-2 w-full"
          customToolBar={CustomToolbar}
          enableRowSelection={true}
          onRowSelectionChange={setRowSelection}
        />
      </AppTableContextProvider>
      <AppConfirmationModal
        description={
          <div>
            Are you sure you want to delete
            <strong> {warehouseItem?.jobNo ?? ''}</strong> ?
          </div>
        }
        open={openDelete}
        onOpenChange={toggleDelete}
        handleCancel={toggleDelete}
        handleSubmit={handleDelete}
        isLoading={deleteLoading}
      />
      <AppSpinner
        overlay
        isLoading={
          newItemLoading ||
          isFetching ||
          isLoadingPrintList ||
          isLoadingDownloadFile
        }
      />
      <AppConfirmationModal
        title={'Warning'}
        description={'Please select sub-rentals to cerate an itinerary'}
        open={newItineraryWarningModal}
        handleCancel={() => setNewItineraryWarningModal(false)}
        cancelLabel={'Cancel'}
      />
      <BreadcrumbDialogRenderer
        activeTab={activeTab}
        isOpen={open.state}
        listItems={listItems}
        onOpenChange={onOpenChange}
        setActiveTab={handleSetActiveTab}
        className={cn('max-w-[60%] 2xl:max-w-[40%]')}
        contentClassName={cn('h-[480px] 2xl:h-[550px] overflow-y-auto')}
      />
    </div>
  );
};

export default OrderDeliveriesPickups;
