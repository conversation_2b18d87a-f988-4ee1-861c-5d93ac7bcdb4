import AppButton from '@/components/common/app-button';
import AppDataTable from '@/components/common/app-data-table';
import ColumnReOrdering from '@/components/common/column-reording';
import { SUB_RENTAL_API_ROUTES } from '@/constants/api-constants';
import { ROUTES } from '@/constants/routes-constants';
import { SubRentalInformationTypes } from '@/types/sub-rentals.types';
import { ColumnDef, Row } from '@tanstack/react-table';
import {
  CircleAlert,
  EditIcon,
  Eye,
  PlusIcon,
  PrinterIcon,
  TrashIcon,
} from 'lucide-react';

import AppTableContextProvider from '@/components/common/app-data-table/AppTableContext';
import AppSpinner from '@/components/common/app-spinner';
import ActionColumnMenu from '@/components/common/data-tables/ActionColumn';
import Filter from '@/components/modules/sub-rental/Filter';
import { StatusColors } from '@/constants/common-constants';
import { getQueryParam, updateQueryParam } from '@/lib/utils';
import {
  useGetSubRentalColumsQuery,
  useUpdateSubRentalColumsMutation,
} from '@/redux/features/sub-rental/subRental.api';
import {
  clearAllFilters,
  clearFilter,
  setFilter,
} from '@/redux/features/sub-rental/subRentalSlice';
import { RootState } from '@/redux/store';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import DeleteSubRent from './delete-sub-rent';

const SubRental = () => {
  const search = getQueryParam('search') as string;
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const [refresh, setRefresh] = useState<boolean>(false);
  const [openColumnOrdering, setOpenColumnOrdering] = useState<boolean>(false);
  const [openDelete, setOpenDelete] = useState<boolean>(false);
  const [subRentalDetails, setSubRentalDetails] =
    useState<SubRentalInformationTypes | null>(null);
  const [isFilterOpen, setIsFilterOpen] = useState<boolean>(false);
  const filter = useSelector((state: RootState) => state.subRental.filters);

  const { data, isLoading, isFetching } = useGetSubRentalColumsQuery();
  const [updateSubRentColums, { isLoading: updateColumLoading }] =
    useUpdateSubRentalColumsMutation();

  const handleNewSubRent = useCallback(
    () => navigate(`${ROUTES.ADD_SUB_RENTAL}?tab=information`),
    [navigate]
  );

  const toggleDelete = useCallback(
    (row?: SubRentalInformationTypes | null, success?: boolean) => {
      setSubRentalDetails(row ? row : null);
      setOpenDelete((prev) => !prev);
      if (success) {
        setRefresh(true);
        setTimeout(() => {
          setRefresh(false);
        }, 100);
      }
    },

    []
  );

  const tableColumns = useMemo(() => {
    return data?.data;
  }, [data?.data]);

  const handleOrderingColumn = useCallback(
    async (formData: any) => {
      const updatedColumns = tableColumns?.map((item: any) => ({
        ...item,
        enabled: formData[item.accessorKey],
      }));

      await updateSubRentColums(updatedColumns).unwrap();
      setOpenColumnOrdering(false);
    },
    [tableColumns, updateSubRentColums]
  );

  const actionColumn: ColumnDef<SubRentalInformationTypes> = useMemo(
    () => ({
      id: 'action',
      size: 110,
      header: 'Actions',
      cell: ({ row }) => {
        const subRentId = row?.original?.id;
        const isDeleted = row?.original?.isDeleted;
        const Icon = isDeleted ? Eye : EditIcon;
        return (
          <ActionColumnMenu
            customEdit={
              <button
                onClick={() =>
                  navigate(
                    `${ROUTES?.EDIT_SUB_RENTAL}?id=${subRentId}&tab=information`
                  )
                }
                className="flex items-center justify-center gap-2 border border-text-tertiary rounded-md px-3 py-1"
              >
                <Icon className="w-5 h-5" />
                <span className="w-24 text-left">
                  {isDeleted ? 'View' : 'Edit'} details
                </span>
              </button>
            }
            contentClassName="w-fit"
            dropdownMenuList={[
              isDeleted
                ? {
                    label: 'Deletion Info',
                    onClick: () => toggleDelete(row.original),
                    icon: <CircleAlert className="h-5 w-5" />,
                    className: 'text-base',
                  }
                : {
                    label: 'Delete',
                    onClick: () => toggleDelete(row.original),
                    icon: <TrashIcon className="h-5 w-5" />,
                    className: 'text-base text-text-danger',
                  },
            ]}
          />
        );
      },
    }),
    [navigate, toggleDelete]
  );

  const memoizedColumns = useMemo(() => {
    if (!tableColumns?.length) return [];

    const formattedColumns = tableColumns
      .filter((column: any) => column.enabled) // Ensure only enabled columns are included
      .map((column: any) => ({
        accessorKey: column.accessorKey,
        header: column.header,
        enableSorting: column.enableSorting || false,
        size: column.size || 200, // Default size if not provided
        cell: ({ row }: any) => row?.original?.[column.accessorKey],
        ...(column.accessorKey === 'orderStatus' && {
          className: (row: Row<any>) => {
            const value = row.original?.orderStatus;
            return StatusColors[value];
          },
        }),
      }));

    return [
      {
        accessorKey: 'ordering',
        header: () => (
          <ColumnReOrdering
            isOpen={openColumnOrdering}
            handleOrderingColumn={handleOrderingColumn}
            tableColumns={tableColumns}
            setOpenColumnOrdering={setOpenColumnOrdering}
          />
        ),
        size: 50,
        maxSize: 100,
      },
      ...formattedColumns,
      actionColumn,
    ];
  }, [
    tableColumns,
    openColumnOrdering,
    handleOrderingColumn,
    setOpenColumnOrdering,
    actionColumn,
  ]);

  // handle Clear function
  const handleClearFilter = useCallback(
    (key: string) => {
      dispatch(clearFilter(key));
      setIsFilterOpen(false);
      if (search) {
        updateQueryParam(null, 'search');
      }
    },
    [dispatch, search]
  );

  // Custom toolbar component
  const CustomToolbar = (
    <AppButton
      icon={PlusIcon}
      label="New Sub Rental"
      aria-label="New Sub Rental"
      onClick={handleNewSubRent}
    />
  );

  useEffect(() => {
    if (search) {
      dispatch(
        setFilter([
          {
            label: 'Customer',
            value: search,
            name: 'customer',
            tagValue: search,
            operator: 'equals',
          } as any,
        ])
      );
    }
  }, [dispatch, search]);

  useEffect(() => {
    if (!search) {
      dispatch(clearAllFilters());
    }
  }, [dispatch, search]);

  const DropdownMenus = [
    {
      label: 'Print Sub Rental List',
      icon: <PrinterIcon />,
      onClick: () => {},
      disabled: true,
    },
  ];
  return (
    <div className="flex flex-col p-6 gap-6">
      <AppTableContextProvider defaultSort={[{ id: 'id', desc: false }]}>
        <AppDataTable
          url={SUB_RENTAL_API_ROUTES.ALL}
          customToolBar={CustomToolbar}
          columns={memoizedColumns}
          heading="Sub Rentals"
          enableSearch={true}
          enablePagination={true}
          tableClassName="max-h-[580px] overflow-auto"
          searchKey="filter"
          refreshList={refresh}
          enableFilter
          filter={filter}
          handleClearFilter={handleClearFilter}
          filterClassName="w-[550px]"
          filterContent={<Filter setIsFilterOpen={setIsFilterOpen} />}
          setIsFilterOpen={setIsFilterOpen}
          isFilterOpen={isFilterOpen}
          dropdownMenus={DropdownMenus}
          dropdownContentClassName="p-1"
        />
      </AppTableContextProvider>
      <DeleteSubRent
        open={openDelete}
        onOpenChange={(success?: boolean) => toggleDelete(null, success)}
        subRentData={subRentalDetails}
      />
      <AppSpinner
        isLoading={isLoading || updateColumLoading || isFetching}
        overlay
      />
    </div>
  );
};

export default SubRental;
