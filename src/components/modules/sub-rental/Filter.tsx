import AppButton from '@/components/common/app-button';
import Input<PERSON>ield from '@/components/forms/input-field';
import SelectDropDown from '@/components/forms/select-dropdown';
import { Separator } from '@/components/ui/separator';
import {
  ComparisonOperator,
  ComparisonOperatorOptions,
  OperatorTypeOptions,
  OperatorTypeValue,
} from '@/constants/common-constants';
import { toCapitalize } from '@/lib/utils';
import { useGetEnumsListQuery } from '@/redux/features/enums-api/enums-api';
import {
  clearAllFilters,
  setFilter,
  updateFormValues,
} from '@/redux/features/sub-rental/subRentalSlice';
import { RootState } from '@/redux/store';
import { memo, useEffect, useMemo } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import { searchByFilters } from './constants';

interface FilterFormValues {
  type: string;
  range: string;
  searchBy: string;
  operator: string;
  searchValue: string;
}

interface FilterProps {
  setIsFilterOpen: React.Dispatch<React.SetStateAction<boolean>>;
}
const Filter: React.FC<FilterProps> = ({ setIsFilterOpen }) => {
  const dispatch = useDispatch();
  const formValuesFromRedux = useSelector(
    (state: RootState) => state.subRental.formValues
  );

  // Type list
  const { data: typeData, isLoading: typeLoading } = useGetEnumsListQuery({
    name: 'SubRentStatusEnum',
  });

  const { data: rangeData, isLoading: rangeLoading } = useGetEnumsListQuery({
    name: 'SubRentSearchRange',
  });

  // Initialize form with values from Redux
  const form = useForm<FilterFormValues>({
    defaultValues: formValuesFromRedux,
  });

  // Watch form changes and update Redux state
  useEffect(() => {
    const subscription = form.watch((values) => {
      dispatch(updateFormValues(values));
    });

    return () => subscription.unsubscribe();
  }, [form, dispatch]);

  //SubRentalTypeFilter
  const onSubmit: SubmitHandler<FilterFormValues> = (data) => {
    const key: string = data.searchBy;
    const filterLabel = searchByFilters?.find(
      ({ value }) => value == key
    )?.label;

    const newFilterData: any = [
      {
        label: 'Type',
        value: data?.type ?? '',
        name: 'type',
        tagValue: toCapitalize(data?.type),
        operator: 'equals',
      },
      {
        label: 'Range',
        value: data?.range ?? '',
        name: 'range',
        tagValue:
          data?.range === 'CURRENT_FUTURE'
            ? 'Current / Future'
            : toCapitalize(data?.range),
        operator: 'equals',
      },
      {
        label: toCapitalize(filterLabel),
        value: data.searchValue,
        name: key,
        tagValue: data.searchValue,
        operator: data.operator,
      },
    ].filter((subRental) => subRental.value);
    dispatch(setFilter(newFilterData));
    setIsFilterOpen(false);
  };

  const handleClear = () => {
    dispatch(clearAllFilters()); // Clear all filters in Redux
    setIsFilterOpen(false); // Close the filter form
  };

  const searchBy = form.watch('searchBy');
  const OperatorList = useMemo(() => {
    return searchBy === 'sub-rental-no'
      ? ComparisonOperatorOptions
      : OperatorTypeOptions;
  }, [searchBy]);

  return (
    <div className="px-3">
      <div className="text-normal py-2 font-semibold">Filters</div>
      <Separator
        orientation="horizontal"
        className="h-[1px] bg-border-Default mb-2"
      />
      <div className="grid grid-cols-2 gap-3">
        <SelectDropDown
          form={form}
          name="type"
          label="Type"
          placeholder="Select Type"
          optionsList={typeData?.data || []}
          isLoading={typeLoading}
          onChange={(value) => !value && form.setValue('range', '')}
        />
        <SelectDropDown
          form={form}
          name="range"
          label="Range"
          placeholder="Select Range"
          optionsList={rangeData?.data || []}
          isLoading={rangeLoading}
        />
        <SelectDropDown
          form={form}
          name="searchBy"
          label="Search By"
          placeholder="Search By"
          optionsList={searchByFilters}
          onChange={(value) => {
            form.setValue(
              'operator',
              value === 'sub-rental-no'
                ? ComparisonOperator.EQUALS
                : OperatorTypeValue.CONTAINS
            );
          }}
        />
        <SelectDropDown
          form={form}
          name="operator"
          label="Operator"
          allowClear={false}
          placeholder="Operator"
          optionsList={OperatorList}
        />
        <InputField
          form={form}
          name="searchValue"
          disabled={!searchBy}
          label="Value"
          placeholder="Search"
          pClassName="col-span-2"
        />
      </div>
      <div className="flex gap-3 mt-7 sticky bottom-2 bg-white">
        <AppButton
          className="w-full"
          onClick={form.handleSubmit(onSubmit)}
          label="Apply"
        />
        <AppButton
          className="w-full"
          label="Clear"
          variant="neutral"
          onClick={handleClear}
        />
      </div>
    </div>
  );
};

export default memo(Filter);
