import CheveronLeft from '@/assets/icons/CheveronLeft';
import AppButton from '@/components/common/app-button';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import AppSpinner from '@/components/common/app-spinner';
import AppTabsVertical from '@/components/common/app-tabs-vertical';
import ActionColumnMenu from '@/components/common/data-tables/ActionColumn';
import IconButton from '@/components/common/icon-button';
import { ROUTES } from '@/constants/routes-constants';
import { SubRentalTabList } from '@/constants/sub-rental-constants';
import { formatDateWithTimezone } from '@/lib/formatDateWithTimezone';
import { getModifiedItems } from '@/lib/getModifiedItems';
import {
  DATE_FORMAT_YYYYMMDD,
  formatDate,
  getQueryParam,
  getStorageValue,
  updateQueryParam,
} from '@/lib/utils';
import {
  useAddUpdateSubRentalInfoMutation,
  useAddUpdateSubRentItemsMutation,
  useGetSubRentalQuery,
} from '@/redux/features/sub-rental/subRental.api';
import {
  SubRentalInformationTypes,
  SubRentalItemDetailsTypes,
  SubRentalTabs,
} from '@/types/sub-rentals.types';
import isEqual from 'lodash/isEqual';
import { CircleAlert, Printer, SaveIcon, TrashIcon, X } from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import { getDefaultSubRentalValues } from '../constants';
import DeleteSubRent from '../delete-sub-rent';

const AddEditSubRental = () => {
  const subRentId = getQueryParam('id') as string;
  const tabName = (getQueryParam('tab') ||
    'information') as keyof SubRentalTabs;
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<keyof SubRentalTabs>(tabName);
  const [openDelete, setOpenDelete] = useState<boolean>(false);
  const [itemDefaultValues, setitemDefaultValues] =
    useState<SubRentalItemDetailsTypes | null>(null);
  const [warningModal, setWarningModal] = useState<{
    message: string;
    isOpen: boolean;
  }>({ message: '', isOpen: false });

  // const tz = getStorageValue('timeZoneOffset') || '';
  const currentDate = formatDateWithTimezone({});

  const storeLocationId = getStorageValue('defaultLocationId') ?? '';
  const enteredById = getStorageValue('userId') ?? '';

  // get sub rent details by id
  const { data, isLoading } = useGetSubRentalQuery(subRentId, {
    skip: !subRentId,
  });

  // add new / update sub rental
  const [addUpdateSubRentalInfo, { isLoading: addUpdateLoading }] =
    useAddUpdateSubRentalInfoMutation();

  // add / update items
  const [addUpdateItems, { isLoading: addUpdateItemsLoading }] =
    useAddUpdateSubRentItemsMutation();

  // sub rental information default values
  const defaultValues = useMemo(() => {
    return getDefaultSubRentalValues({
      data: data?.data,
      currentDate,
      enteredById,
      storeLocationId,
    });
  }, [data, currentDate, enteredById, storeLocationId]);

  const form = useForm<SubRentalTabs[typeof activeTab] | any>({
    defaultValues,
    mode: 'onChange',
  });

  useEffect(() => {
    if (defaultValues) {
      form.reset(defaultValues);
    }
  }, [defaultValues, form]);

  const itemDetailsForm = useForm<SubRentalItemDetailsTypes>();

  const onDefaultValuesLoaded = useCallback(
    (defaults: SubRentalItemDetailsTypes) => {
      setitemDefaultValues(defaults);
    },
    []
  );

  const isDeleted = defaultValues?.isDeleted;

  const currentItems = itemDetailsForm?.watch('items')?.map((item) => {
    return {
      ...item,
      quantity: Number(item?.quantity),
      unitPrice: Number(item?.unitPrice),
    };
  });

  const isItemModified = useMemo(() => {
    return isEqual(currentItems, itemDefaultValues?.items);
  }, [currentItems, itemDefaultValues?.items]);

  const navigateToSubRent = useCallback(() => {
    navigate(ROUTES.SUB_RENTALS);
  }, [navigate]);

  // toggle delete SR
  const toggleDelete = useCallback(
    (success?: boolean) => {
      setOpenDelete((prev) => !prev);
      if (success) {
        navigateToSubRent();
      }
    },

    [navigateToSubRent]
  );

  // handle tab change
  const handleTabChange = useCallback((value: keyof SubRentalTabs) => {
    setActiveTab(value);
    updateQueryParam(value, 'tab');
  }, []);

  const validateAddressFields = useCallback(
    ({
      address,
      city,
      zipcode,
    }: {
      address?: string;
      city?: string;
      zipcode?: string;
    }) => {
      const fieldLabels: Record<string, string> = {
        address: 'Address',
        city: 'City',
        zipcode: 'Zip Code',
      };

      const missing = Object.entries({
        address,
        city,
        zipcode,
      })
        .filter(([_, value]) => !value?.trim())
        .map(([key]) => fieldLabels[key]);

      if (!missing.length) return null;

      const message =
        missing.length === 1
          ? `The ${missing?.at(0)} field is required. Please add an ${missing?.at(0)?.toLowerCase()} to this Customer / Vendor record.`
          : `The ${[...missing.slice(0, -1), `and ${missing.at(-1)}`].join(', ')} fields are required. Please add them to this Customer / Vendor record.`;

      setWarningModal({ message, isOpen: true });
    },
    []
  );

  // handle submit sub-rent information
  const onSubmit = useCallback(
    async (formData: SubRentalInformationTypes) => {
      const {
        phone,
        fax,
        address,
        city,
        state,
        zipcode,
        country,
        orderId,
        orderDate,
        pickupDate,
        returnDate,
        rentedFromId,
        ...rest
      } = formData;
      const payload = {
        ...rest,
        orderDate: formatDate(orderDate, DATE_FORMAT_YYYYMMDD),
        pickupDate: pickupDate
          ? formatDate(pickupDate, DATE_FORMAT_YYYYMMDD)
          : null,
        returnDate: returnDate
          ? formatDate(returnDate, DATE_FORMAT_YYYYMMDD)
          : null,
        rentedFromId: rentedFromId?.value ?? null,
        orderId: orderId?.value || null,
      };

      const data = await addUpdateSubRentalInfo(payload).unwrap();
      validateAddressFields({ address, city, zipcode });
      if (!subRentId && data?.statusCode === 200)
        updateQueryParam(data?.data?.id);
    },
    [addUpdateSubRentalInfo, subRentId, validateAddressFields]
  );

  // handle submit items
  const onSubmitItems = useCallback(
    async (formData: SubRentalItemDetailsTypes) => {
      const modified = getModifiedItems(
        formData?.items,
        itemDefaultValues?.items || []
      );
      const payload = modified?.map(({ itemId, listId, total, ...item }) => ({
        ...item,
        itemId: Number(itemId?.value),
        quantity: Number(item?.quantity),
        unitPrice: Number(item?.unitPrice),
        id: listId || null,
        orderSubrentId: subRentId,
      }));
      if (payload?.length) await addUpdateItems({ subRentId, data: payload });
    },
    [addUpdateItems, itemDefaultValues?.items, subRentId]
  );

  // Order Drop down menu
  const subRentDropdownMenu = useMemo(
    () => [
      {
        label: 'Print SR',
        onClick: () => {},
        icon: <Printer className="h-5 w-5" />,
        disabled: true,
      },
      defaultValues?.isDeleted
        ? {
            label: 'Deletion Info',
            onClick: () => toggleDelete(),
            icon: <CircleAlert className="h-5 w-5" />,
          }
        : {
            label: 'Delete SR',
            onClick: () => toggleDelete(),
            icon: <TrashIcon className="h-5 w-5" />,
            className: 'text-base text-text-danger',
          },
    ],
    [defaultValues?.isDeleted, toggleDelete]
  );

  return (
    <FormProvider {...form}>
      <header className="flex justify-between items-center px-4 pt-4 pb-3 sticky top-16 z-[10] bg-white">
        <div className="flex gap-x-4 items-center">
          <IconButton onClick={navigateToSubRent}>
            <CheveronLeft />
          </IconButton>
          <h1
            className="text-2xl text-text-tertiary font-semibold cursor-pointer"
            onClick={navigateToSubRent}
          >
            Sub Rental
          </h1>
          <div className="flex items-center gap-3">
            <span className="text-2xl font-semibold text-text-tertiary">
              {' / '}
            </span>
            <p className="text-2xl capitalize font-semibold">
              {subRentId ? `#${defaultValues?.id ?? ''}` : 'New Sub Rental'}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <AppButton
            label="Save Detail"
            icon={SaveIcon}
            onClick={
              tabName === 'information'
                ? form.handleSubmit(onSubmit)
                : itemDetailsForm.handleSubmit(onSubmitItems)
            }
            iconClassName="w-4 h-4"
            isLoading={addUpdateLoading || addUpdateItemsLoading}
            disabled={
              isDeleted || (isItemModified && tabName === 'item-details')
            }
          />
          <AppButton
            label="Cancel"
            onClick={navigateToSubRent}
            variant="neutral"
            icon={X}
            iconClassName="w-5 h-5"
          />
          {subRentId && (
            <ActionColumnMenu
              triggerClassName="border h-10"
              dropdownMenuList={subRentDropdownMenu}
              contentClassName="p-3 w-full"
            />
          )}
        </div>
      </header>
      <AppTabsVertical
        tabs={SubRentalTabList({ itemDetailsForm, onDefaultValuesLoaded })}
        activeTab={tabName}
        onTabChange={handleTabChange}
        showTabMenu={!!subRentId}
        className="px-4 pb-3"
      />
      <DeleteSubRent
        open={openDelete}
        onOpenChange={(success?: boolean) => toggleDelete(success)}
        subRentData={defaultValues as any}
      />
      <AppConfirmationModal
        title={'Warning'}
        description={<div className="text-sm">{warningModal.message}</div>}
        open={warningModal.isOpen}
        handleCancel={() => setWarningModal({ message: '', isOpen: false })}
        cancelLabel={'Ok'}
      />
      <AppSpinner overlay isLoading={isLoading} />
    </FormProvider>
  );
};

export default AddEditSubRental;
