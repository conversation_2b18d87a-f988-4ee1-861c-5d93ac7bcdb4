import AppButton from '@/components/common/app-button';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import ActionColumnMenu from '@/components/common/data-tables/ActionColumn';
import ItemLookup from '@/components/common/item-lookup';
import ReactSelect from '@/components/common/ReactSelect';
import InputField from '@/components/forms/input-field';
import NumberInputField from '@/components/forms/number-input-field';
import { PURCHASE_API_ROUTES } from '@/constants/api-constants';
import {
  QUANTITY_VALIDATION_RULE,
  TEXT_VALIDATION_RULE,
} from '@/constants/validation-constants';
import { convertToFloat, getQueryParam } from '@/lib/utils';
import { useGetItemBriefMutation } from '@/redux/features/items/item.api';
import {
  useDeleteSubRentalItemMutation,
  useGetSubRentalItemsQuery,
} from '@/redux/features/sub-rental/subRental.api';
import {
  SubRentalInformationTypes,
  SubRentalItemDetailsTypes,
  SubRentalItemListTypes,
} from '@/types/sub-rentals.types';
import { ColumnDef } from '@tanstack/react-table';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useFieldArray, useFormContext, UseFormReturn } from 'react-hook-form';
import ItemListTable, { mapSubRentalItems } from './ItemListTable';

interface ItemDetailsProps {
  form: UseFormReturn<SubRentalItemDetailsTypes>;
  onDefaultValuesLoaded: (defaults: SubRentalItemDetailsTypes) => void;
}

const ItemDetails = ({ form, onDefaultValuesLoaded }: ItemDetailsProps) => {
  const subRentId = getQueryParam('id') as string;
  const informationForm = useFormContext<SubRentalInformationTypes>();
  const isDeleted = informationForm.watch('isDeleted');

  const [isDeleteItemDialogOpen, setIsDeleteItemDialogOpen] =
    useState<boolean>(false);
  const [selectedItem, setSelectedItem] =
    useState<null | SubRentalItemListTypes>(null);

  // get subrentals items
  const { data: subRentalItems, isFetching: isLoading } =
    useGetSubRentalItemsQuery(subRentId, {
      skip: !subRentId,
      refetchOnMountOrArgChange: true,
    });

  // delete item
  const [deleteItem, { isLoading: deleteLoading }] =
    useDeleteSubRentalItemMutation();

  // get item details by item id
  const [getitemBrief] = useGetItemBriefMutation();

  const defaultValues = useMemo(
    () => mapSubRentalItems(subRentalItems?.data as SubRentalItemListTypes[]),
    [subRentalItems?.data]
  );

  useEffect(() => {
    if (defaultValues) {
      form.reset(defaultValues);
      onDefaultValuesLoaded(defaultValues);
    }
  }, [defaultValues, form, onDefaultValuesLoaded]);

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'items',
  });

  // toggle delete item
  const toggleDelete = useCallback((item?: SubRentalItemListTypes | null) => {
    setIsDeleteItemDialogOpen((prev) => !prev);
    setSelectedItem(item ?? null);
  }, []);

  // remove selected item
  const handleDelete = useCallback(async () => {
    if (selectedItem?.isRemove) {
      remove(selectedItem?.rowIndex);
      toggleDelete(null);
    } else {
      await deleteItem({
        subRentId,
        itemId: selectedItem?.listId ?? '',
      }).unwrap();
      toggleDelete(null);
    }
  }, [
    deleteItem,
    remove,
    selectedItem?.isRemove,
    selectedItem?.listId,
    selectedItem?.rowIndex,
    subRentId,
    toggleDelete,
  ]);

  const handleItemIdChange = useCallback(
    async (value: { value: string }, rowIndex: number) => {
      const itemData = await getitemBrief(value?.value);
      if (itemData?.data) {
        form.setValue(`items.${rowIndex}.itemId`, {
          label: itemData?.data?.data?.itemId,
          value: itemData?.data?.data?.id?.toString(),
        });
        form.setValue(
          `items.${rowIndex}.quantity`,
          itemData?.data?.data?.quantity
            ? itemData?.data?.data?.quantity?.toString()
            : ''
        );
        form.setValue(
          `items.${rowIndex}.unitPrice`,
          itemData?.data?.data?.unitPrice?.toString()
        );
        form.setValue(
          `items.${rowIndex}.itemDescription`,
          itemData?.data?.data?.description ?? ''
        );
        form.setValue(`items.${rowIndex}.rowIndex`, rowIndex + 1);
      }
    },
    [getitemBrief, form]
  );

  const handlePriceChange = useCallback(
    (price: string | number, index: number) => {
      form.clearErrors(`items[${index}].unitPrice` as any);
      const quantity = form.getValues(`items.${index}.quantity`);
      const priceNumber = Number(price);
      const quantityNumber = Number(quantity);
      const total =
        !isNaN(priceNumber) && !isNaN(quantityNumber)
          ? priceNumber * quantityNumber
          : 0;
      form.setValue(`items.${index}.total`, Number(total));
      form.setValue(`items.${index}.unitPrice`, price);
    },
    [form]
  );

  const handleQuantityChange = useCallback(
    (quantity: string | number, index: number) => {
      form.clearErrors(`items[${index}].quantity` as any);
      const price = form.getValues(`items.${index}.unitPrice`);
      const quantityNumber = Number(quantity);
      const priceNumber = Number(price);
      const total =
        !isNaN(quantityNumber) && !isNaN(priceNumber)
          ? quantityNumber * priceNumber
          : 0;
      form.setValue(`items.${index}.total`, Number(total));
    },
    [form]
  );

  const columns: ColumnDef<SubRentalItemListTypes>[] = useMemo(() => {
    return [
      {
        accessorKey: 'itemId',
        header: 'Item ID',
        size: 100,
        enableSorting: true,
        cell: ({ row }) => (
          <ReactSelect
            placeholder="Select Item ID"
            className="w-[180px] bg-white"
            name={`items.${row.index}.itemId`}
            form={form}
            onSelectChange={(value) => handleItemIdChange(value, row.index)}
            url={PURCHASE_API_ROUTES.GENERIC_ITEM_LOOKUP}
            labelKey="itemId"
            valueKey="id"
            validation={TEXT_VALIDATION_RULE}
            disabled={isDeleted}
          />
        ),
      },
      {
        accessorKey: 'quantity',
        header: 'Qty',
        size: 80,
        cell: ({ row }) => {
          const itemId = form.watch(`items.${row.index}.itemId`);
          const validation = itemId?.value ? QUANTITY_VALIDATION_RULE : {};
          return (
            <NumberInputField
              form={form}
              placeholder="Quantity"
              name={`items.${row.index}.quantity`}
              className="w-24 h-8"
              maxLength={5}
              decimalScale={0}
              onValueChange={(value) => handleQuantityChange(value, row.index)}
              disabled={isDeleted || !itemId?.value}
              validation={validation}
            />
          );
        },
      },
      {
        accessorKey: 'itemDescription',
        header: 'Item Description',
        maxSize: 300,
        enableSorting: true,
        cell: ({ row }) => {
          const disabled = form.watch(`items.${row.index}.itemId`);
          return (
            <div className="w-full truncate">
              <InputField
                form={form}
                placeholder="Description"
                className="w-48 h-8"
                name={`items.${row?.index}.itemDescription`}
                disabled={isDeleted || !disabled}
                pClassName="p-1"
              />
            </div>
          );
        },
      },
      {
        accessorKey: 'unitPrice',
        header: 'Price',
        size: 80,
        enableSorting: true,
        cell: ({ row }) => {
          const itemId = form.watch(`items.${row.index}.itemId`);
          const validation = itemId?.value ? TEXT_VALIDATION_RULE : {};
          return (
            <NumberInputField
              form={form}
              name={`items.${row.index}.unitPrice`}
              placeholder="Price"
              maxLength={9}
              prefix="$"
              className="w-28 h-8"
              fixedDecimalScale
              thousandSeparator=","
              disabled={isDeleted || !itemId?.value}
              onValueChange={(value) => handlePriceChange(value, row.index)}
              validation={validation}
            />
          );
        },
      },
      {
        accessorKey: 'total',
        header: 'Total',
        size: 80,
        enableSorting: true,
        cell: ({ row }) =>
          convertToFloat({
            value: form.watch(`items.${row.index}.total`) ?? 0,
            prefix: '$',
          }),
      },
      {
        id: 'action',
        size: 80,
        header: 'Actions',
        cell: ({ row }) => {
          const itemId = form.watch(`items.${row.index}.itemId`);
          return (
            <ActionColumnMenu
              contentClassName="w-fit"
              disabled={isDeleted}
              onDelete={() =>
                toggleDelete({
                  ...row.original,
                  rowIndex: row.index,
                  itemIdLabel: itemId?.label,
                  isRemove: !row.original?.listId,
                })
              }
            />
          );
        },
      },
    ];
  }, [
    form,
    handleItemIdChange,
    handlePriceChange,
    handleQuantityChange,
    isDeleted,
    toggleDelete,
  ]);

  const handleAddItemLookupData = useCallback(
    (data: SubRentalItemListTypes | any) => {
      const newItem = data?.map((item: SubRentalItemListTypes) => ({
        ...item,
        itemDescription: item?.description ?? '',
        total: Number(item?.quantity) * Number(item?.unitPrice),
      }));
      append(newItem);
    },
    [append]
  );

  const handleAddNewItem = useCallback(() => {
    const lastItem =
      form.getValues('items')[form.getValues('items')?.length - 1];

    if (lastItem?.itemId?.value || fields?.length === 0) {
      append({
        listId: null,
        itemId: null,
        itemDescription: '',
        quantity: '',
        unitPrice: '0',
        total: 0,
      });
    }
  }, [append, fields?.length, form]);

  return (
    <>
      <div className="flex items-center justify-between gap-3 pb-5">
        <h3 className="text-xl font-semibold text-text-Default">
          Item Details
        </h3>
        <div className="flex items-center gap-x-2">
          <ItemLookup
            onClick={handleAddItemLookupData}
            url={PURCHASE_API_ROUTES.GENERIC_ITEM_LOOKUP}
            heading={''}
            btnLabel="Add to Item List"
            isPriceEditable
            disabled={isDeleted}
          />
        </div>
      </div>
      <div>
        <ItemListTable
          fields={(fields as SubRentalItemDetailsTypes | any) || []}
          columns={columns as SubRentalItemDetailsTypes | any}
          isLoading={isLoading}
        />
        <div className="w-full p-2 border-grayScale-20 border-b border-x rounded-b-md">
          <AppButton
            label="+ Add New"
            className=" bg-brand-teal-Default hover:bg-brand-teal-secondary"
            onClick={handleAddNewItem}
            disabled={isDeleted}
          />
        </div>
        <AppConfirmationModal
          description={
            <div>
              Are you sure you want to delete
              <strong> {selectedItem?.itemIdLabel ?? ''}</strong> ?
            </div>
          }
          open={isDeleteItemDialogOpen}
          onOpenChange={toggleDelete}
          handleCancel={toggleDelete}
          handleSubmit={handleDelete}
          isLoading={deleteLoading}
        />
      </div>
    </>
  );
};

export default ItemDetails;
