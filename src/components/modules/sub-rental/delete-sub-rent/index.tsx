import AppButton from '@/components/common/app-button';
import CustomDialog from '@/components/common/dialog';
import InputField from '@/components/forms/input-field';
import Labels from '@/components/forms/Label';
import Text<PERSON>reaField from '@/components/forms/text-area';
import { cn, formatDate } from '@/lib/utils';
import { useDeleteSubRentalMutation } from '@/redux/features/sub-rental/subRental.api';
import { SubRentalInformationTypes } from '@/types/sub-rentals.types';
import { X } from 'lucide-react';
import { memo, useCallback, useEffect, useMemo } from 'react';
import { useForm, UseFormReturn } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';

interface DeleteOrderProps {
  open: boolean;
  onOpenChange: (success?: boolean) => void;
  subRentData?: SubRentalInformationTypes | null;
}
const DeleteSubRent = ({
  open,
  onOpenChange,
  subRentData,
}: DeleteOrderProps) => {
  const navigate = useNavigate();
  // id: string | number | null; deletedBy: string; deletedReason: string
  const isDeleted = subRentData?.isDeleted;
  const defaultValues = useMemo(() => {
    return {
      id: subRentData?.id,
      deletedBy: subRentData?.deletedBy,
      deletedOn: formatDate(subRentData?.deletedOn ?? ''),
      deletedReason: subRentData?.deletedReason,
    };
  }, [
    subRentData?.deletedBy,
    subRentData?.deletedOn,
    subRentData?.deletedReason,
    subRentData?.id,
  ]);

  const form: UseFormReturn<SubRentalInformationTypes> =
    useForm<SubRentalInformationTypes>({
      defaultValues,
      mode: 'onChange',
    });
  const { handleSubmit, reset } = form;

  useEffect(() => {
    if (defaultValues) {
      form.reset(defaultValues);
    }
  }, [defaultValues, form]);

  const [deleteSubRent, { isLoading: deleteLoading }] =
    useDeleteSubRentalMutation();

  // on submit
  const onSubmit = useCallback(
    async (formData: SubRentalInformationTypes) => {
      try {
        const { data }: any = await deleteSubRent({ ...formData });
        if (data?.statusCode === 200) {
          navigate('/sub-rentals');
          onOpenChange(data?.success);
        }
      } catch (error) {
        // Handle error (could display error message to user)
      }
    },
    [deleteSubRent, navigate, onOpenChange]
  );

  // handle cancel
  const handleCancel = useCallback(() => {
    onOpenChange();
    reset();
  }, [onOpenChange, reset]);

  return (
    <CustomDialog
      open={open}
      onOpenChange={handleCancel}
      description=""
      className={cn('max-w-[90%] w-[80%] md:w-[50%] 2xl:w-[35%]')}
      contentClassName="max-h-[410px] overflow-y-auto px-6 pb-0"
      title={'Deletion Info'}
    >
      <div className="w-full">
        {isDeleted && <Labels label="The sub rent deletion info" />}
        <div className="border rounded-lg p-4 my-3">
          <div className="grid grid-cols-2 gap-4">
            <InputField
              name="deletedBy"
              form={form}
              label="Deleted By"
              placeholder="Deleted By"
              disabled={isDeleted}
              pClassName="min-w-fit"
              maxLength={50}
            />
            <InputField
              name="deletedOn"
              form={form}
              label="Date"
              placeholder="Date"
              disabled
              pClassName="col-span-2 md:col-span-1"
            />
          </div>
          <TextAreaField
            name="deletedReason"
            form={form}
            label="Reason for Deletion"
            placeholder="Reason for Deletion"
            rows={10}
            disabled={isDeleted}
            maxLength={200}
            pClassName="pt-4"
            className="max-h-[100px]"
          />
        </div>

        {!isDeleted && (
          <div className="flex items-center justify-end sticky bottom-0 bg-white gap-4 py-3 mt-2">
            <AppButton
              label="Delete"
              icon={X}
              className="w-28"
              onClick={handleSubmit(onSubmit)}
              isLoading={deleteLoading}
            />
            <AppButton
              label="Cancel"
              variant="neutral"
              onClick={handleCancel}
              className="w-28"
              disabled={deleteLoading}
            />
          </div>
        )}
      </div>
    </CustomDialog>
  );
};

export default memo(DeleteSubRent);
