import { ColumnDef, RowSelectionState } from '@tanstack/react-table';
import debounce from 'lodash/debounce';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';

// Components
import DataTable from '@/components/common/data-tables';
import FormActionButtons from '@/components/common/FormActionButtons';
import InputField from '@/components/forms/input-field';
import PhoneInputWidget from '@/components/forms/phone-input-mask';
import SelectWidget from '@/components/forms/select';

// Hooks & Types
import RadioField from '@/components/forms/radio-field';
import { CustomerSearchType } from '@/constants/common-constants';
import { formatPhoneNumber } from '@/lib/utils';
import { useCustomerVendorSearchQuery } from '@/redux/features/sub-rental/subRental.api';
import { PaginationType, SortingStateType } from '@/types/common.types';
import { CustomerVendorLookupTypes } from '@/types/sub-rentals.types';

interface CustomerLookupProps {
  handleOk: (value: CustomerVendorLookupTypes) => void;
  handleChange: () => void;
}

interface CustomerVendorFilterTypes {
  status: string;
  name: string;
  phone: string;
  type: string;
  searchType: string;
}

const defaultValues = {
  status: '-1',
  searchType: 'name',
  name: '',
  phone: '',
  type: '-1',
};

export const statusList = [
  {
    label: 'All',
    value: '-1',
  },
  {
    label: 'Active',
    value: '1',
  },
  {
    label: 'Inactive',
    value: '0',
  },
];
const CustomerVendorLookup = ({
  handleOk,
  handleChange,
}: CustomerLookupProps) => {
  const form = useForm<CustomerVendorFilterTypes>({
    defaultValues,
    mode: 'onChange',
  });
  const {
    watch,
    formState: { errors },
  } = form;
  const { name, phone, searchType } = watch();

  const initialFilter = useMemo(
    () => [
      { field: 'status', value: '-1', operator: '' },
      { field: 'name', value: '', operator: '' },
      { field: 'type', value: '-1', operator: '' },
    ],
    []
  );

  const [pagination, setPagination] = useState<PaginationType>({
    pageSize: 10,
    pageIndex: 0,
  });
  const [filter, setFilter] = useState<any>(initialFilter);
  const [sorting, setSorting] = useState<SortingStateType[]>([
    { id: 'firstName', desc: true },
  ]);
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const [selectedCustomer, setSelectedCustomer] = useState<
    CustomerVendorLookupTypes[]
  >([]);
  const [search, setSearch] = useState<string>('');

  const isCustomerName = searchType === 'name';

  // Fetch data
  const { data, isFetching: isLoading } = useCustomerVendorSearchQuery({
    body: {
      pageNumber: pagination.pageIndex + 1,
      pageSize: pagination.pageSize,
      sortAscending: sorting[0]?.desc ?? true,
      sortBy: sorting[0]?.id ?? 'firstName',
      filters: filter,
    },
  });

  const columns: ColumnDef<CustomerVendorLookupTypes>[] = useMemo(
    () => [
      {
        accessorKey: 'fullName',
        header: 'Name',
        size: 240,
        enableSorting: true,
        cell: (info) => info.getValue(),
      },
      {
        accessorKey: 'phone',
        header: 'Phone',
        size: 170,
        enableSorting: true,
        cell: (info) => formatPhoneNumber(info.getValue() as string),
      },
      { accessorKey: 'city', header: 'City', size: 130, enableSorting: true },
      { accessorKey: 'state', header: 'State', size: 110, enableSorting: true },
    ],
    []
  );

  const handleTypeChange = useCallback(
    (value: string) => {
      form.setValue('type', value);
      if (name?.length > 1 || phone?.length > 2) {
        form.setValue(value === 'name' ? 'name' : 'phone', '');
      }
    },
    [form, name, phone]
  );

  const handleOnClickOk = useCallback(() => {
    if (selectedCustomer.length > 0) {
      handleOk({ ...selectedCustomer[0] });
      form.reset();
      setSelectedCustomer([]);
      handleChange();
    }
  }, [selectedCustomer, handleOk, form, handleChange]);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedSearch = useCallback(
    debounce((name: string, value: string) => {
      const currentFormValues = form.getValues();
      const filterFields = [
        {
          field: 'status',
          value: currentFormValues.status,
          operator: '',
        },
        {
          field: 'name',
          value: name === 'name' ? value : currentFormValues.name,
          operator: '',
        },
        {
          field: 'phone',
          value:
            name === 'phone'
              ? value.replace(/^\+1/, '')
              : currentFormValues.phone.replace(/^\+1/, ''),
          operator: '',
        },
        {
          field: 'type',
          value: currentFormValues.type,
          operator: '',
        },
      ];
      if (!errors.phone) {
        setFilter(filterFields);
      }
    }, 1000),
    [form] // form is a stable dependency
  );

  useEffect(() => {
    return () => {
      debouncedSearch.cancel();
    };
  }, [debouncedSearch]);

  const handleFilter = (name: string, value: string) => {
    if (name === 'name' || name === 'phone') {
      debouncedSearch(name, value);
    } else {
      const currentFormValues = form.getValues();
      const filterFields = [
        {
          field: 'status',
          value:
            name === 'status'
              ? !value
                ? '-1'
                : value
              : currentFormValues.status,
          operator: '',
        },
        {
          field: 'name',
          value: name === 'name' ? value : currentFormValues.name,
          operator: '',
        },
        {
          field: 'phone',
          value:
            name === 'phone'
              ? value.replace(/^\+1/, '')
              : currentFormValues.phone.replace(/^\+1/, ''),
          operator: '',
        },
        {
          field: 'type',
          value: name === 'type' ? value : currentFormValues.type,
          operator: '',
        },
      ];
      setFilter(filterFields);
    }
  };

  return (
    <div className="flex flex-col gap-2">
      <div className="flex flex-row justify-end gap-3 flex-wrap">
        <SelectWidget
          name="status"
          label="Status"
          form={form}
          optionsList={statusList}
          parentClassName="md:w-40 z-[100]"
          placeholder="Customer Status"
          menuPosition="absolute"
          onSelectChange={(value) => handleFilter('status', value)}
        />
        <SelectWidget
          name="searchType"
          form={form}
          label="Type"
          optionsList={CustomerSearchType}
          parentClassName="md:w-40 z-[100]"
          menuPosition="absolute"
          placeholder="Customer Type"
          isClearable={false}
          onChange={handleTypeChange}
        />
        {isCustomerName ? (
          <InputField
            name="name"
            label="Name"
            form={form}
            className="md:w-48"
            placeholder="Enter Name"
            onChange={(event) => handleFilter('name', event.target.value)}
          />
        ) : (
          <PhoneInputWidget
            label="Phone"
            className="w-48"
            form={form}
            name="phone"
            onChange={(value) => handleFilter('phone', value)}
          />
        )}
        <RadioField
          name="type"
          className="mt-10"
          onChange={(value) => handleFilter('type', value)}
          form={form}
          options={[
            { label: 'Both', value: '-1' },
            { label: 'Customer', value: '1' },
            { label: 'Vendor', value: '2' },
          ]}
        />
      </div>

      <DataTable
        heading=" "
        columns={columns}
        isLoading={isLoading}
        enableSearch={false}
        enablePagination
        data={data?.data ?? []}
        totalItems={data?.pagination?.totalCount}
        enableRowSelection
        rowSelection={rowSelection}
        onRowSelectionChange={setRowSelection}
        onRowsSelected={setSelectedCustomer}
        tableClassName="max-h-[300px] 2xl:max-h-[350px] overflow-auto"
        bindingKey="srNo"
        loaderRows={7}
        pagination={pagination}
        setPagination={setPagination}
        search={search}
        setSearch={setSearch}
        sorting={sorting}
        setSorting={setSorting}
      />
      <FormActionButtons
        className="absolute bottom-4 right-4 w-64"
        onSubmit={handleOnClickOk}
        submitLabel="OK"
        onCancel={handleChange}
        isLoading={false}
        disabledSubmitButton={!selectedCustomer?.length}
      />
    </div>
  );
};

export default CustomerVendorLookup;
