import AppButton from '@/components/common/app-button';
import CustomDialog from '@/components/common/dialog';
import AutoCompleteDropdown from '@/components/forms/auto-complete-dropdown';
import InputField from '@/components/forms/input-field';
import PhoneInputWidget from '@/components/forms/phone-input-mask';
import Text<PERSON><PERSON>Field from '@/components/forms/text-area';
import { SUB_RENTAL_API_ROUTES } from '@/constants/api-constants';
import { TEXT_VALIDATION_RULE } from '@/constants/validation-constants';
import { cn } from '@/lib/utils';
import {
  CustomerVendorLookupTypes,
  SubRentalInformationTypes,
} from '@/types/sub-rentals.types';
import { Search } from 'lucide-react';
import { useCallback, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import CustomerVendorLookup from './customer-vendor-lookup';

const RentedForm = () => {
  // Customer / Vendor Search lookup
  const [openLookup, setOpenLookup] = useState<boolean>(false);
  const form = useFormContext<SubRentalInformationTypes>();
  const isDeleted = form.watch('isDeleted');

  const onOpenChange = useCallback(() => {
    setOpenLookup((prev) => !prev);
  }, []);

  // handle change customer / vendor
  const handleChangeCustomerVendor = useCallback(
    (details: CustomerVendorLookupTypes) => {
      const {
        fullName,
        id,
        phone,
        fax,
        address1,
        address2,
        city,
        state,
        zipcode,
        country,
        specialInstructions,
        contactType,
      } = details || {};
      const address = `${address1 ?? ''} ${address2 ?? ''}`?.trim();
      form.setValue('rentedFromId', { label: fullName, value: id });
      form.setValue('phone', phone ?? '');
      form.setValue('fax', fax ?? '');
      form.setValue('city', city ?? '');
      form.setValue('address', address ?? '');
      form.setValue('state', state ?? '');
      form.setValue('zipcode', zipcode ?? '');
      form.setValue('country', country ?? '');
      form.setValue('specInst1', specialInstructions ?? '');
      form.setValue('contactType', contactType);
      form.clearErrors('rentedFromId');
    },
    [form]
  );

  return (
    <div>
      <h3 className="text-xl font-semibold text-text-Default mb-4">
        RENTED FROM
      </h3>
      <div className="grid grid-cols-2 gap-5">
        <div className="col-span-2 flex gap-3">
          <AutoCompleteDropdown
            label="Customer / Vendor"
            placeholder="Select Customer / Vendor"
            name="rentedFromId"
            form={form}
            onSelectChange={(option) =>
              handleChangeCustomerVendor(option?.item)
            }
            url={SUB_RENTAL_API_ROUTES.CUSTOMER_VENDRO_LOOKUP}
            labelKey="fullName"
            valueKey="srNo"
            sortBy="fullName"
            showItem
            labelComponent={(value, item) => (
              <div className={cn(!item?.isActive && 'text-red-500')}>
                {value} {item?.isActive ? '' : '(Inactive)'}
              </div>
            )}
            validation={TEXT_VALIDATION_RULE}
            disabled={isDeleted}
          />
          <AppButton
            label=""
            icon={Search}
            tooltip="Customer / Vendor Search"
            className="mt-8"
            iconClassName="w-5 h-5"
            onClick={onOpenChange}
            disabled={isDeleted}
          />
        </div>
        <PhoneInputWidget name="phone" form={form} label="Phone" disabled />
        <PhoneInputWidget name="fax" form={form} label="Fax" disabled />
        <InputField
          name="address"
          form={form}
          label="Address"
          placeholder="Address"
          disabled
          pClassName="col-span-2"
        />
        <InputField
          name="city"
          form={form}
          label="City"
          placeholder="City"
          disabled
        />
        <InputField
          name="state"
          form={form}
          label="State"
          placeholder="State"
          disabled
        />
        <InputField
          name="zipcode"
          form={form}
          label="Zip Code"
          placeholder="Zip Code"
          disabled
        />
        <InputField
          name="country"
          form={form}
          label="Country"
          placeholder="Country"
          disabled
        />
        <TextAreaField
          name="specInst1"
          form={form}
          rows={4}
          label="Special Instructions"
          placeholder="Special Instructions"
          className="max-h-[150px]"
          pClassName="col-span-2"
          disabled={isDeleted}
        />
      </div>

      <CustomDialog
        open={openLookup}
        onOpenChange={onOpenChange}
        title="Customer / Vendor Search"
        description=""
        className="min-w-[70%] 2xl:min-w-[50%]"
        contentClassName="min-h-[500px] 2xl:h-[560px]"
      >
        <div className="grid grid-cols-1 px-6">
          <CustomerVendorLookup
            handleOk={handleChangeCustomerVendor}
            handleChange={onOpenChange}
          />
        </div>
      </CustomDialog>
    </div>
  );
};

export default RentedForm;
