import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import AutoCompleteDropdown from '@/components/forms/auto-complete-dropdown';
import DatePicker from '@/components/forms/date-picker';
import InputField from '@/components/forms/input-field';
import SelectWidget from '@/components/forms/select';
import {
  ORDERS_API_ROUTES,
  STORE_OPTIONS_API_ROUTES,
} from '@/constants/api-constants';
import { TEXT_VALIDATION_RULE } from '@/constants/validation-constants';
import useOptionList from '@/hooks/useOptionList';
import { generateLabelValuePairs } from '@/lib/utils';
import { useSalesPersonQuery } from '@/redux/features/customers/choices.api';
import { useGetEnumsListQuery } from '@/redux/features/enums-api/enums-api';
import { SubRentalInformationTypes } from '@/types/sub-rentals.types';
import dayjs from 'dayjs';
import { useCallback, useState } from 'react';
import { useFormContext } from 'react-hook-form';

const Info = () => {
  const form = useFormContext<SubRentalInformationTypes>();
  const isDeleted = form.watch('isDeleted');
  const [warningModal, setWarningModal] = useState<{
    message: string;
    isOpen: boolean;
  }>({ message: '', isOpen: false });
  // Status list
  const { data: statusData, isLoading: statusLoading } = useGetEnumsListQuery({
    name: 'SubRentStatusEnum',
  });

  // Pickup / Return Status list
  const { data: pickupReturnStatusData, isLoading: pickupReturnStatusLoading } =
    useGetEnumsListQuery({
      name: 'SubRentPickupReturnStatusEnum',
    });

  // location listing
  const { options: storeLocationList, optionLoading } = useOptionList({
    url: STORE_OPTIONS_API_ROUTES.ALL,
    labelKey: 'locationName',
    valueKey: 'id',
    sortBy: 'locationName',
  });

  // Entered By listing
  const { data: salespersonData } = useSalesPersonQuery();
  const enteredByList = generateLabelValuePairs({
    data: salespersonData?.data,
    labelKey: 'name',
    valueKey: 'id',
  });

  const pickupDate = form.watch('pickupDate');
  const returnDate = form.watch('returnDate');
  const handleWarningOnBlur = useCallback(
    (
      date: Date | string | undefined,
      fieldName: 'pickupDate' | 'returnDate'
    ) => {
      if (!date) return;
      const selectedDate = dayjs(date);
      if (!selectedDate.isValid()) return;

      if (
        fieldName === 'pickupDate' &&
        selectedDate.isAfter(returnDate, 'day')
      ) {
        form.setValue('returnDate', '');
      }
      if (
        fieldName === 'returnDate' &&
        selectedDate.isBefore(pickupDate, 'day')
      ) {
        form.setValue('returnDate', returnDate);
        setWarningModal({
          message: 'The Return Date cannot be earlier than the Pickup Date.',
          isOpen: true,
        });
      }
    },
    [form, pickupDate, returnDate]
  );

  return (
    <div>
      <h3 className="text-xl font-semibold text-text-Default mb-4">INFO</h3>
      <div className="grid grid-cols-2 gap-5">
        <InputField
          name="id"
          form={form}
          label="SR #"
          placeholder="SR #"
          disabled
        />
        <SelectWidget
          form={form}
          name="orderStatus"
          label="Status"
          placeholder="Select Status"
          isClearable={false}
          optionsList={statusData?.data}
          disabled
          isLoading={statusLoading}
        />
        <DatePicker
          form={form}
          name="orderDate"
          label="Order Date"
          placeholder="Select Date"
          enableInput
          validation={TEXT_VALIDATION_RULE}
          disabled={isDeleted}
        />
        <SelectWidget
          form={form}
          name="pickupReturnStatus"
          label="Pickup / Return Status"
          placeholder="Pickup / Return Status"
          isClearable={false}
          optionsList={pickupReturnStatusData?.data}
          disabled
          isLoading={pickupReturnStatusLoading}
        />
        <DatePicker
          form={form}
          name="pickupDate"
          label="Pickup Date"
          placeholder="Select Date"
          enableInput
          disabled={isDeleted}
          onDateChange={(date) => handleWarningOnBlur(date, 'pickupDate')}
          isRenderFirst
        />
        <InputField
          name="pickupInfo"
          form={form}
          label="Pickup Info"
          placeholder="Pickup Info"
          maxLength={32}
          disabled={isDeleted}
        />
        <DatePicker
          form={form}
          name="returnDate"
          label="Return Date"
          placeholder="Select Date"
          enableInput
          disabled={isDeleted}
          onDateChange={(date) => handleWarningOnBlur(date, 'returnDate')}
          isRenderFirst
        />
        <InputField
          name="returnInfo"
          form={form}
          label="Return Info"
          placeholder="Return Info"
          maxLength={32}
          disabled={isDeleted}
        />
        <div className=" col-span-2 grid grid-cols-1 2xl:grid-cols-2 gap-5">
          <InputField
            name="resvNo"
            form={form}
            label="Reservation #"
            placeholder="Reservation"
            maxLength={20}
            disabled={isDeleted}
          />
          <InputField
            name="name"
            form={form}
            label="Contact"
            placeholder="Contact"
            maxLength={40}
            disabled={isDeleted}
          />
          <SelectWidget
            form={form}
            name="storeLocationId"
            label="Location"
            placeholder="Select Location"
            isClearable={false}
            optionsList={storeLocationList}
            isLoading={optionLoading}
            disabled={isDeleted}
          />
          <SelectWidget
            form={form}
            name="enteredById"
            label="Entered By"
            placeholder="Select Entered By"
            isClearable={false}
            optionsList={enteredByList}
            disabled={isDeleted}
          />

          <AutoCompleteDropdown
            label="Order #"
            placeholder="Select Order #"
            name="orderId"
            form={form}
            url={ORDERS_API_ROUTES.ALL}
            labelKey="orderNo"
            valueKey="id"
            sortBy="orderNo"
            filterBy={[
              { field: 'isDeleted', value: 'false', operator: 'boolean' },
            ]}
            disabled={isDeleted}
            isClearable={true}
          />
        </div>
      </div>
      <AppConfirmationModal
        title={'Warning'}
        description={<div>{warningModal.message}</div>}
        open={warningModal.isOpen}
        handleCancel={() => setWarningModal({ message: '', isOpen: false })}
        cancelLabel={'Ok'}
      />
    </div>
  );
};

export default Info;
