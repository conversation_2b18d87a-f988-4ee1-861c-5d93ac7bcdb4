import {
  GetDefaultSubRentalValuesParams,
  SubRentalInformationTypes,
} from '@/types/sub-rentals.types';

export const searchByFilters = [
  {
    value: 'sub-rental-no',
    label: 'Sub-Rental #',
  },
  {
    value: 'customer',
    label: 'Customer',
  },
];

export const getDefaultSubRentalValues = ({
  data,
  currentDate,
  enteredById,
  storeLocationId,
}: GetDefaultSubRentalValuesParams) => {
  const { customerVendorDetail, ...subRentalData }: SubRentalInformationTypes =
    data || {};

  return {
    ...subRentalData,
    id: subRentalData?.id ?? null,
    orderDate: subRentalData?.orderDate ?? currentDate,
    storeLocationId: subRentalData?.storeLocationId ?? Number(storeLocationId),
    enteredById: subRentalData?.enteredById ?? Number(enteredById),
    orderStatus: subRentalData?.orderStatus ?? 'ORDERED',
    pickupReturnStatus: subRentalData?.pickupReturnStatus ?? 'NOT_PICKED_UP',
    rentedFromId: subRentalData?.id
      ? {
          label: customerVendorDetail?.fullName ?? '',
          value: customerVendorDetail?.id ?? '',
        }
      : null,
    phone: customerVendorDetail?.phone ?? '',
    fax: customerVendorDetail?.fax ?? '',
    city: customerVendorDetail?.city ?? '',
    address:
      `${customerVendorDetail?.address1 ?? ''} ${customerVendorDetail?.address2 ?? ''}`?.trim(),
    state: customerVendorDetail?.state ?? '',
    zipcode: customerVendorDetail?.zipcode ?? '',
    country: customerVendorDetail?.country ?? '',
    specInst1: subRentalData?.specInst1 ?? '',
    orderId: {
      label: subRentalData?.orderId ?? '',
      value: subRentalData?.orderId ?? '',
    },
  };
};
