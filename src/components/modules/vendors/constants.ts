import { VENDORS_API_ROUTES } from '@/constants/api-constants';
// import { isValueMatching } from '@/lib/utils';
import { AllVendorsTabTypeMap, VENDOR_TABS } from '@/types/vendor.types';

export const getApiRouteForTab = (
  activeTab: keyof AllVendorsTabTypeMap,
  id: string
): string => {
  const APIS: any = {
    [VENDOR_TABS.BUSINESS_INFO]: id
      ? VENDORS_API_ROUTES.UPDATE(id)
      : VENDORS_API_ROUTES.ADD,
    [VENDOR_TABS.CONTACT_INFO]: id
      ? VENDORS_API_ROUTES.UPDATE(id)
      : VENDORS_API_ROUTES.ADD,
  };
  return APIS[activeTab];
};

export const generateVendorsDefaultValues = (info: any) => {
  const { countryId, stateId, ...vendorsValue } = info;
  return {
    // Business Info
    ...vendorsValue,
    vendorName: vendorsValue?.vendorName || '',
    address1: vendorsValue?.address1 || '',
    address2: vendorsValue?.address2 || '',
    city: vendorsValue?.city || '',
    state: stateId || '',
    country: countryId || 1,
    zipCode: vendorsValue?.zipCode || '',

    // contact info
    contact: vendorsValue?.contact || '',
    tel1: vendorsValue?.tel1 || '',
    tel2: vendorsValue?.tel2 || '',
    telfax: vendorsValue?.telfax || '',
    emailaddress: vendorsValue?.emailaddress || '',
    isactive: vendorsValue.id
      ? vendorsValue?.isactive
        ? 'true'
        : 'false'
      : 'true',
  };
};

export const createPayload = (
  tabName: string,
  formData: AllVendorsTabTypeMap
) => {
  switch (tabName) {
    case VENDOR_TABS.BUSINESS_INFO:
    case VENDOR_TABS.CONTACT_INFO:
      return {
        ...formData,
        // isactive: isValueMatching(formData?.isactive),
        isactive: formData?.isactive === 'true' ? true : false,
      };

    case VENDOR_TABS.ADDITIONAL_CONTACT_INFO:
      return {
        ...formData,
      };
    default:
      null;
  }
};
