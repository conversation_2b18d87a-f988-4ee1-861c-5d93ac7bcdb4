import ExcelIcon from '@/assets/icons/ExcelIcon';
import AppButton from '@/components/common/app-button';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import AppDataTable from '@/components/common/app-data-table';
import AppTableContextProvider, {
  AppTableContext,
} from '@/components/common/app-data-table/AppTableContext';
import AppSpinner from '@/components/common/app-spinner';
import ColumnReOrdering from '@/components/common/column-reording';
import ActionColumnMenu from '@/components/common/data-tables/ActionColumn';
import { UseToast } from '@/components/ui/toast/ToastContainer';
import { VENDORS_API_ROUTES } from '@/constants/api-constants';
import { ROUTES } from '@/constants/routes-constants';
import { useDownloadFile } from '@/hooks/useDownloadFile';
import {
  formatPhoneNumber,
  // formatPhoneNumber,
  getPaginationObject,
  getQueryParam,
  updateQueryParam,
} from '@/lib/utils';
import {
  useDeleteVendorsMutation,
  useGetVendorColumsQuery,
  useUpdateVendorColumsMutation,
} from '@/redux/features/vendors-api/vendors.api';
import {
  clearAllFilters,
  clearFilter,
  setFilter,
} from '@/redux/features/vendors-api/vendorSlice';
import { RootState } from '@/redux/store';
import { VENDOR_TABS, VendorListTypes } from '@/types/vendor.types';
import { ColumnDef } from '@tanstack/react-table';
import { EditIcon, PlusIcon } from 'lucide-react';
import { useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useNavigate } from 'react-router-dom';
import AddEditVendors from './AddEditVendor';
import Filter from './Filter';

const Vendors = () => {
  const search = getQueryParam('search') as string;
  const tabName = getQueryParam('tab') as string;

  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { sorting } = useContext(AppTableContext);
  const [openDelete, setOpenDelete] = useState<boolean>(false);
  const [vendorItem, setVendorItem] = useState<VendorListTypes | null>(null);
  const [refresh, setRefresh] = useState<boolean>(false);
  const [searchParams, setSearchParams] = useState<string>('');
  const [isFilterOpen, setIsFilterOpen] = useState<boolean>(false);
  const [openColumnOrdering, setOpenColumnOrdering] = useState<boolean>(false);
  const filter = useSelector((state: RootState) => state.vendor.filters);

  const { data, refetch, isFetching } = useGetVendorColumsQuery();
  const [updateVendorColums, { isLoading: newItemLoading }] =
    useUpdateVendorColumsMutation();

  const componentMap: any = useMemo(
    () => ({
      PhoneWidget: ({ value }: any) => formatPhoneNumber(value),
    }),
    []
  );
  const { downloadFile } = useDownloadFile();
  const toast = UseToast();

  const [deleteVendor, { isLoading: deleteLoading }] =
    useDeleteVendorsMutation();

  const toggleDelete = useCallback((row?: VendorListTypes) => {
    setVendorItem(row ? row : null);
    setOpenDelete((prev) => !prev);
  }, []);

  const handleDelete = useCallback(async () => {
    try {
      await deleteVendor(vendorItem?.id || 0).unwrap();

      toggleDelete();

      setRefresh(true);
      setTimeout(() => {
        setRefresh(false);
      }, 100);
    } catch (err) {}
  }, [deleteVendor, vendorItem?.id, toggleDelete]);

  const handleNewVendorsOptions = () => {
    navigate(`${ROUTES.VENDORS}?tab=business-info`);
  };
  const tableColumns = useMemo(() => {
    return data?.data;
  }, [data?.data]);

  const handleOrderingColumn = useCallback(
    async (formData: any) => {
      const updatedColumns = tableColumns.map((item: any) => ({
        ...item,
        enabled: formData[item.accessorKey],
      }));

      await updateVendorColums(updatedColumns).unwrap();
      setOpenColumnOrdering(false);
      await refetch();
    },
    [tableColumns, updateVendorColums, refetch]
  );

  // handle Clear function
  const handleClearFilter = useCallback(
    (key: string) => {
      dispatch(clearFilter(key));
      setIsFilterOpen(false);
      if (search) {
        updateQueryParam(null, 'search');
      }
    },
    [dispatch, search]
  );

  const handleExtractVendorsToExcel = useCallback(() => {
    const normalizedFilters = Array.isArray(filter)
      ? filter.map((f: any) => ({
          field: f.name || f.label,
          value: f.value,
          operator: f.operator,
        }))
      : [];
    const payload = getPaginationObject({
      pagination: { pageIndex: 0, pageSize: 0 },
      sorting,
      filters: [
        ...normalizedFilters,
        { field: 'filter', value: searchParams, operator: 'Contains' },
      ],
    });
    const response = downloadFile({
      url: VENDORS_API_ROUTES.EXPORT_CSV,
      method: 'POST',
      body: payload,
    });
    toast.promise(response, {
      loading: 'Downloading file...',
      success: 'File downloaded successfully.',
      error: 'Failed to download file',
    });
  }, [filter, sorting, searchParams, downloadFile, toast]);

  const DropdownMenu = useMemo(() => {
    return [
      {
        label: 'Extract Vendors',
        onClick: handleExtractVendorsToExcel,
        icon: <ExcelIcon />,
      },
    ];
  }, [handleExtractVendorsToExcel]);

  useEffect(() => {
    if (search) {
      dispatch(
        setFilter([
          {
            label: 'Vendor',
            value: search,
            name: 'vendor',
            tagValue: search,
            operator: 'equals',
          } as any,
        ])
      );
    }
  }, [dispatch, search]);

  useEffect(() => {
    if (!search) {
      dispatch(clearAllFilters());
    }
  }, [dispatch, search]);

  // Custom toolbar component
  const CustomToolbar = (
    <AppButton
      icon={PlusIcon}
      className="w-[150px]"
      label="New Vendor"
      onClick={handleNewVendorsOptions}
    />
  );

  const actionColumn: ColumnDef<VendorListTypes> = useMemo(
    () => ({
      id: 'action',
      size: 110,
      header: 'Actions',
      cell: ({ row }) => {
        const vendorId = row?.original?.id;
        return (
          <ActionColumnMenu
            customEdit={
              <Link
                to={`${ROUTES?.VENDORS}?id=${vendorId}&tab=${VENDOR_TABS.BUSINESS_INFO}`}
                className="flex items-center justify-center gap-2 border border-text-tertiary rounded-md px-3 py-1"
              >
                <EditIcon className="w-5 h-5" /> Edit details
              </Link>
            }
            onDelete={() => toggleDelete(row.original)}
            contentClassName="w-fit"
          />
        );
      },
    }),
    [toggleDelete]
  );

  const memoizedColumns = useMemo(() => {
    if (!tableColumns?.length) return [];

    const formattedColumns = tableColumns
      .filter((column: any) => column.enabled) // Ensure only enabled columns are included
      .map((column: any) => ({
        accessorKey: column.accessorKey,
        header: column.header,
        enableSorting: column.enableSorting || false,
        size: column.size || 200, // Default size if not provided
        cell: ({ row }: any) => {
          const Component = componentMap[column.component]; // Get the component based on the key
          return Component ? (
            <Component value={row?.original?.[column.accessorKey]} />
          ) : (
            row?.original?.[column.accessorKey]
          );
        },
      }));

    return [
      {
        accessorKey: 'ordering',
        header: () => (
          <ColumnReOrdering
            isOpen={openColumnOrdering}
            handleOrderingColumn={handleOrderingColumn}
            tableColumns={tableColumns}
            setOpenColumnOrdering={setOpenColumnOrdering}
          />
        ),
        size: 50,
      },
      ...formattedColumns,
      actionColumn,
    ];
  }, [
    tableColumns,
    componentMap,
    openColumnOrdering,
    handleOrderingColumn,
    setOpenColumnOrdering,
    actionColumn,
  ]);

  if (tabName) {
    return <AddEditVendors />;
  } else {
    return (
      <div className="p-6">
        <AppTableContextProvider
          defaultSort={[{ id: 'vendorName', desc: true }]}
        >
          <AppDataTable
            url={VENDORS_API_ROUTES.ALL}
            customToolBar={CustomToolbar}
            columns={memoizedColumns}
            heading="Vendors"
            enableSearch={true}
            enablePagination={true}
            tableClassName="max-h-[400px] 2xl:max-h-[580px] overflow-auto"
            refreshList={refresh}
            searchKey="vendorName"
            setSearchParams={setSearchParams}
            enableFilter
            filter={filter}
            handleClearFilter={handleClearFilter}
            filterClassName="w-[550px]"
            filterContent={<Filter setIsFilterOpen={setIsFilterOpen} />}
            setIsFilterOpen={setIsFilterOpen}
            isFilterOpen={isFilterOpen}
            dropdownMenus={DropdownMenu}
            dropdownContentClassName="p-4 flex flex-col gap-2 w-full"
          />
        </AppTableContextProvider>
        <AppConfirmationModal
          description={
            <div>
              Are you sure you want to delete
              <strong> {vendorItem?.vendorName ?? ''}</strong> ?
            </div>
          }
          open={openDelete}
          onOpenChange={toggleDelete}
          handleCancel={toggleDelete}
          handleSubmit={handleDelete}
          isLoading={deleteLoading}
        />
        <AppSpinner overlay isLoading={newItemLoading || isFetching} />
      </div>
    );
  }
};

export default Vendors;
