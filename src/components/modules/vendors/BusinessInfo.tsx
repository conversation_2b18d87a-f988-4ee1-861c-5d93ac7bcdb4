import InputField from '@/components/forms/input-field';
import SelectWidget from '@/components/forms/select';
import ZipCodeInput from '@/components/forms/zipcode-input';
import { statusList } from '@/constants/common-constants';
import { TEXT_VALIDATION_RULE } from '@/constants/validation-constants';
import { generateLabelValuePairs } from '@/lib/utils';
import {
  useGetCountryListQuery,
  useGetStateByCountryQuery,
} from '@/redux/features/country/country.api';
import { CountryType } from '@/types/customer.types';
import { BusinessInfoTypes } from '@/types/vendor.types';
import { useMemo } from 'react';
import { useFormContext } from 'react-hook-form';

const BusinessInfo = () => {
  const form = useFormContext<BusinessInfoTypes>();
  const countryId = form.watch('country');

  // state data
  const { data: statesData, isFetching: stateIsLoading } =
    useGetStateByCountryQuery({
      countryId: Number(countryId),
    });
  const { data: countryData = [] } = useGetCountryListQuery();

  // country list
  const countryList = useMemo(
    () =>
      generateLabelValuePairs({
        data: countryData as CountryType[],
        labelKey: 'name',
        valueKey: 'country_id',
      }),
    [countryData]
  );

  // states list
  const stateList = generateLabelValuePairs({
    data: statesData,
    labelKey: 'code',
    valueKey: 'state_id',
  });

  const handleCountryChange = (value: string) => {
    if (Number(value) !== countryId) {
      form.setValue('state', '');
      form.setValue('zipCode', '');
      form.clearErrors('zipCode');
    }
  };

  return (
    <div className="flex flex-col gap-6 justify-between h-full">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <InputField
          name="vendorName"
          form={form}
          label="Vendor"
          placeholder="Vendor Name"
          validation={TEXT_VALIDATION_RULE}
          maxLength={64}
        />
        <InputField
          name="address1"
          form={form}
          label="Address Line 1"
          placeholder="Address Line 1"
          maxLength={64}
        />
        <InputField
          name="address2"
          form={form}
          label="Address Line 2"
          placeholder="Address Line 1"
          maxLength={64}
        />
        <InputField
          name="city"
          form={form}
          label="City"
          placeholder="City"
          maxLength={32}
        />
        <SelectWidget
          name="state"
          form={form}
          placeholder="Select State"
          label="State"
          optionsList={stateList}
          isLoading={stateIsLoading}
          isClearable={false}
          validation={TEXT_VALIDATION_RULE}
        />
        <SelectWidget
          form={form}
          name="country"
          label="Country"
          optionsList={countryList}
          placeholder="Select Country"
          onSelectChange={handleCountryChange}
          isClearable={false}
        />

        <ZipCodeInput
          name="zipCode"
          isUSA={Number(countryId) === 1}
          form={form}
          label="Zip Code"
        />
        <SelectWidget
          form={form}
          name="isactive"
          label="Status"
          placeholder="Select Status"
          optionsList={statusList}
          isClearable={false}
        />
      </div>
    </div>
  );
};

export default BusinessInfo;
