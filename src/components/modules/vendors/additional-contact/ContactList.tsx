import DataTable from '@/components/common/data-tables';
import { formatPhoneNumber } from '@/lib/utils';
import { useGetAdditionalContactQuery } from '@/redux/features/vendors-api/vendors.api';
import { SortingStateType } from '@/types/common.types';
import { AdditionalContactInfoTypes } from '@/types/vendor.types';
import { ColumnDef } from '@tanstack/react-table';
import { useMemo, useState } from 'react';

const ContactList = ({ contactId }: { contactId: number }) => {
  const [sorting, setSorting] = useState<SortingStateType[]>([
    { id: 'phoneno', desc: true },
  ]);

  // get contact details
  const { data: contactData, isLoading } = useGetAdditionalContactQuery(
    contactId,
    { skip: !contactId, refetchOnMountOrArgChange: true }
  );

  const phonesList = contactData?.data?.phones || [];

  const Columns = useMemo<ColumnDef<AdditionalContactInfoTypes>[]>(() => {
    return [
      {
        accessorKey: 'phoneno',
        header: 'Phone #',
        size: 100,
        enableSorting: true,
        invertSorting: true,
        cell: ({ row }) => formatPhoneNumber(row?.original?.phoneno),
      },
      {
        accessorKey: 'phonetype',
        header: 'Type',
        size: 100,
        enableSorting: true,
        invertSorting: true,
      },
    ];
  }, []);

  return (
    <DataTable
      columns={Columns}
      data={phonesList}
      totalItems={phonesList?.length}
      isLoading={isLoading}
      loaderRows={3}
      sorting={sorting}
      setSorting={setSorting}
      manualSorting={false}
      enablePagination={false}
      headerClassName="z-[1]"
      tableClassName="max-h-[200px] lg:max-h-[250px] overflow-auto"
    />
  );
};

export default ContactList;
