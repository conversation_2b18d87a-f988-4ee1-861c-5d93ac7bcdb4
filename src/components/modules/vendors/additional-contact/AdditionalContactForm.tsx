import AppButton from '@/components/common/app-button';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import AppSpinner from '@/components/common/app-spinner';
import DataTable from '@/components/common/data-tables';
import ActionColumnMenu from '@/components/common/data-tables/ActionColumn';
import InputField from '@/components/forms/input-field';
import PhoneInputWidget from '@/components/forms/phone-input-mask';
import SelectWidget from '@/components/forms/select';
import SelectDropDown from '@/components/forms/select-dropdown';
import { Label } from '@/components/ui/label';
import {
  EMAIL_VALIDATION_RULE,
  TEXT_VALIDATION_RULE,
} from '@/constants/validation-constants';
import { generateLabelValuePairs, getQueryParam } from '@/lib/utils';
import {
  useIncludeInBatchQuery,
  usePhoneTypeQuery,
} from '@/redux/features/customers/choices.api';
import {
  useAddUpdateAdditionalContactMutation,
  useDeleteAdditionalContactPhoneNumberMutation,
  useGetAdditionalContactQuery,
} from '@/redux/features/vendors-api/vendors.api';
import { SortingStateType } from '@/types/common.types';
import { AdditionalContactInfoTypes } from '@/types/vendor.types';
import { ColumnDef } from '@tanstack/react-table';
import { Plus } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';
import { SubmitHandler, useFieldArray, useForm } from 'react-hook-form';

interface AdditionalContactListFormProps {
  handleChange: (success?: boolean) => void;
  contactId: number;
}
const AdditionalContactListForm = ({
  contactId,
  handleChange,
}: AdditionalContactListFormProps) => {
  const vendorId = getQueryParam('id') as string;

  // get contact details
  const {
    data: contactData,
    isLoading,
    refetch,
  } = useGetAdditionalContactQuery(contactId, { skip: !contactId });

  // add update contact
  const [addUpdateAdditionalContact, { isLoading: addUpdateLoading }] =
    useAddUpdateAdditionalContactMutation();

  // delete contact
  const [deletePhoneNumber, { isLoading: deleteLoading }] =
    useDeleteAdditionalContactPhoneNumberMutation();

  const defaultValues = useMemo(() => {
    const values = contactData?.data as AdditionalContactInfoTypes;
    return {
      ...values,
      contact_id: values?.contact_id || 0,
      linkid: values?.linkid ?? Number(vendorId),
      phones: values?.phones,
    };
  }, [contactData?.data, vendorId]);

  const form = useForm<AdditionalContactInfoTypes>();
  const [sorting, setSorting] = useState<SortingStateType[]>([
    { id: 'phoneno', desc: true },
  ]);
  const { control, handleSubmit, reset } = form;
  const { fields, append, remove } = useFieldArray({
    control,
    name: 'phones',
  });

  useEffect(() => {
    if (defaultValues) {
      reset(defaultValues);
    }
  }, [defaultValues, reset]);

  const [openDeleteDialog, setOpenDeleteDialog] = useState<boolean>(false);
  const [phoneId, setPhoneId] = useState<{
    id: number | null;
    isPhone: boolean;
  }>({ id: null, isPhone: false });

  const toggleOpenDeleteDialog = (id?: number, isPhone?: boolean) => {
    setOpenDeleteDialog((prev) => !prev);
    setPhoneId({ id: id ?? null, isPhone: isPhone ?? false });
  };

  // Handle deletion of phone number
  const handleDelete = async () => {
    const id = phoneId?.id;
    if (phoneId.isPhone && id) {
      await deletePhoneNumber({ contactId, phoneId: id }).unwrap();
      toggleOpenDeleteDialog();
      refetch();
    } else {
      remove(id || 0);
      toggleOpenDeleteDialog();
    }
  };

  const { data } = usePhoneTypeQuery();
  const phoneTypeList = generateLabelValuePairs({
    data: data?.data,
    labelKey: 'description',
    valueKey: 'phonetype_id',
  });
  const { data: includeInBatchData } = useIncludeInBatchQuery();

  // Transform batch options for dropdown
  const includeInBatchList = useMemo(() => {
    return includeInBatchData?.data?.map((item: any) => {
      const key = Object.keys(item)[0];
      const value = item[key];
      return { label: key, value };
    });
  }, [includeInBatchData?.data]);

  const onSubmit: SubmitHandler<AdditionalContactInfoTypes> = async (
    formData
  ) => {
    try {
      const payload = {
        ...formData,
        first_name: formData.first_name ?? '',
        last_name: formData.last_name ?? '',
        title: formData.title ?? '',
        email: formData?.email ?? '',
        batch: formData?.batch ?? '',
        contacttype_id: 2,
        description: '',
        phones:
          formData?.phones
            ?.filter(
              (item) => item?.phonetype_id || item?.phoneno?.replace('+1', '')
            )
            ?.map((item) => ({
              ...item,
              description: '',
              phone_id: item?.phone_id || 0,
              contact_id: item?.contact_id || 0,
            })) ?? [],
      };
      await addUpdateAdditionalContact(payload).unwrap();
      handleChange(true);
    } catch (error) {
      // console.error('Error adding contact:', error);
    }
  };

  const columns = useMemo<ColumnDef<any>[]>(
    () => [
      {
        accessorKey: 'phoneno',
        header: 'Phone #',
        size: 150,
        enableSorting: true,
        invertSorting: true,
        cell: ({ row }) => (
          <PhoneInputWidget
            form={form}
            name={`phones.${row.index}.phoneno`}
            className="p-[1px]"
          />
        ),
      },
      {
        accessorKey: 'phonetype_id',
        header: 'Type',
        size: 200,
        enableSorting: true,
        cell: ({ row }) => (
          <SelectDropDown
            name={`phones.${row.index}.phonetype_id`}
            form={form}
            optionsList={phoneTypeList}
            placeholder="Select type"
            allowClear={false}
          />
        ),
      },
      {
        id: 'action',
        size: 80,
        header: 'Actions',
        cell: ({ row }) => (
          <ActionColumnMenu
            onDelete={() => {
              const { phone_id } = row?.original || {};
              const id = phone_id === 0 ? row?.index : phone_id;
              const isPhoneId = Boolean(phone_id);
              toggleOpenDeleteDialog(id, isPhoneId);
            }}
            contentClassName="w-fit z-[99]"
          />
        ),
      },
    ],
    [form, phoneTypeList]
  );
  return (
    <div className="px-6">
      <div className="grid grid-cols-2 gap-4 pb-5">
        <InputField
          name="first_name"
          form={form}
          label="First Name"
          placeholder="First Name"
          validation={TEXT_VALIDATION_RULE}
        />
        <InputField
          name="last_name"
          form={form}
          label="Last Name"
          placeholder="Last Name"
        />
        <InputField
          name="title"
          form={form}
          label="Title"
          placeholder="Title"
        />
        <InputField
          name="email"
          form={form}
          label="E-mail"
          placeholder="E-mail"
          validation={EMAIL_VALIDATION_RULE}
        />
        <SelectWidget
          form={form}
          optionsList={includeInBatchList || []}
          label="Include in Batch"
          name="batch"
          placeholder="Select Include in Batch"
          menuPosition="absolute"
          parentClassName="z-20"
          isClearable={false}
        />
      </div>
      <DataTable
        data={fields}
        columns={columns}
        heading={<Label className="text-base font-bold">Phone Numbers</Label>}
        totalItems={fields.length}
        tableClassName="max-h-[200px]"
        enablePagination={false}
        sorting={sorting}
        setSorting={setSorting}
        manualSorting={false}
        customToolBar={
          <AppButton
            variant="neutral"
            label="Add New"
            icon={Plus}
            iconClassName="w-5 h-5"
            className="h-9"
            onClick={() => {
              append({
                phone_id: 0,
                contact_id: 0,
                phoneno: '',
                phonetype_id: '',
              });
            }}
          />
        }
      />
      <div className="flex items-center justify-end pt-4 gap-4">
        <AppButton
          label="Submit"
          className="w-32"
          onClick={handleSubmit(onSubmit)}
          isLoading={addUpdateLoading}
        />
        <AppButton
          label="Cancel"
          className="w-32"
          variant="neutral"
          onClick={() => handleChange()}
        />
      </div>
      <AppConfirmationModal
        description={<div>Are you sure you want to delete this phone?</div>}
        open={openDeleteDialog}
        isLoading={deleteLoading}
        onOpenChange={toggleOpenDeleteDialog}
        handleCancel={toggleOpenDeleteDialog}
        handleSubmit={handleDelete}
      />
      <AppSpinner isLoading={isLoading} overlay />
    </div>
  );
};

export default AdditionalContactListForm;
