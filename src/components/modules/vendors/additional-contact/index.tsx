import AppButton from '@/components/common/app-button';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import AppDataTable from '@/components/common/app-data-table';
import AppTableContextProvider from '@/components/common/app-data-table/AppTableContext';
import ActionColumnMenu from '@/components/common/data-tables/ActionColumn';
import CustomDialog from '@/components/common/dialog';
import { VENDORS_API_ROUTES } from '@/constants/api-constants';
import { getQueryParam } from '@/lib/utils';
import { useDeleteAdditionalContactMutation } from '@/redux/features/vendors-api/vendors.api';
import { AdditionalContactInfoTypes } from '@/types/vendor.types';
import { ColumnDef } from '@tanstack/react-table';
import { EditIcon, PlusIcon } from 'lucide-react';
import { useCallback, useMemo, useState } from 'react';
import AdditionalContactListForm from './AdditionalContactForm';
import ContactList from './ContactList';

const AdditionalContactList = () => {
  const vendorId = getQueryParam('id') as string;
  const [openModal, setOpenModal] = useState(false);
  const [selectedContact, setSelectedContact] = useState<any | null>(null);
  const [openDelete, setOpenDelete] = useState<boolean>(false);
  const [refresh, setRefresh] = useState<boolean>(false);

  const [deleteAdditionalContact, { isLoading }] =
    useDeleteAdditionalContactMutation();

  // open additional contact form dialog
  const toggleOpenModal = useCallback(
    (contact?: AdditionalContactInfoTypes) => {
      setOpenModal((prev) => !prev);
      setSelectedContact(contact ?? null);
    },
    []
  );

  const toggleRefresh = useCallback(() => {
    setRefresh(true);
    setTimeout(() => {
      setRefresh(false);
    }, 100);
  }, []);

  // toggle delete
  const toggleOpenDelete = useCallback(
    (contact?: AdditionalContactInfoTypes) => {
      setOpenDelete((prev) => !prev);
      setSelectedContact(contact ?? null);
    },
    []
  );

  const handleDeleteContact = async () => {
    await deleteAdditionalContact(selectedContact.contact_id).unwrap();
    toggleRefresh();
    toggleOpenDelete();
  };

  const columns = useMemo<ColumnDef<AdditionalContactInfoTypes>[]>(
    () => [
      {
        accessorKey: 'first_name',
        header: 'Name',
        size: 200,
        enableSorting: true,
        cell: ({ row }) =>
          `${row?.original?.first_name ?? ''} ${row?.original?.last_name ?? ''}`,
      },
      {
        accessorKey: 'title',
        header: 'Title',
        size: 130,
        enableSorting: true,
      },
      {
        accessorKey: 'email',
        header: 'E-mail',
        size: 130,
        enableSorting: true,
      },
      {
        accessorKey: 'batch',
        header: 'Include In Batch',
        size: 170,
        enableSorting: true,
        cell: ({ row }) => row?.original?.batchname,
      },
      {
        id: 'action',
        size: 100,
        header: 'Actions',
        cell: ({ row }) => (
          <ActionColumnMenu
            customEdit={
              <EditIcon
                className="w-5 h-5 cursor-pointer"
                onClick={() => toggleOpenModal(row?.original)}
              />
            }
            onDelete={() => toggleOpenDelete(row?.original)}
            contentClassName="w-fit"
            triggerClassName="w-fit"
          />
        ),
      },
    ],
    [toggleOpenDelete, toggleOpenModal]
  );

  const AdditionalContactsTable = useMemo(() => {
    return (
      <AppTableContextProvider
        defaultSort={[{ id: 'first_name', desc: false }]}
      >
        <AppDataTable
          url={VENDORS_API_ROUTES?.ADDITIONAL_CONTACT_ALL}
          columns={columns}
          heading="Additional Contacts"
          customToolBar={
            <AppButton
              icon={PlusIcon}
              className="w-[150px]"
              label="New Contact"
              onClick={toggleOpenModal}
            />
          }
          filter={[
            { name: 'contacttype_id', value: '2', operator: 'Equals' },
            { name: 'linkid', value: vendorId, operator: 'Equals' },
          ]}
          headerClassName="z-[8]"
          enablePagination={true}
          tableClassName="max-h-[400px] overflow-auto"
          searchKey="name"
          isRowExpanded={true}
          renderSubComponent={({ row }) => (
            <ContactList contactId={row?.original?.contact_id} />
          )}
        />
      </AppTableContextProvider>
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [columns, toggleOpenModal, vendorId, refresh]);

  return (
    <div className="p-4">
      {AdditionalContactsTable}
      <CustomDialog
        open={openModal}
        onOpenChange={() => toggleOpenModal()}
        title={selectedContact?.contact_id ? 'Edit Contact' : 'Add Contact'}
        className="min-w-[50%]"
      >
        <AdditionalContactListForm
          handleChange={(success) => {
            if (success) toggleRefresh();

            toggleOpenModal();
          }}
          contactId={selectedContact?.contact_id}
        />
      </CustomDialog>

      <AppConfirmationModal
        open={openDelete}
        description={
          <>
            Are you sure you want to delete this{' '}
            <span className="font-bold text-base">
              {selectedContact?.first_name ?? ''}{' '}
              {selectedContact?.last_name ?? ''}
            </span>{' '}
            contact ?
          </>
        }
        onOpenChange={toggleOpenDelete}
        handleCancel={toggleOpenDelete}
        handleSubmit={handleDeleteContact}
        isLoading={isLoading}
      />
    </div>
  );
};

export default AdditionalContactList;
