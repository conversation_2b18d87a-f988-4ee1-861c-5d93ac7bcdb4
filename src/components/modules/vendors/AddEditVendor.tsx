import CheveronLeft from '@/assets/icons/CheveronLeft';
import AppButton from '@/components/common/app-button';
import AppSpinner from '@/components/common/app-spinner';
import AppTabsVertical from '@/components/common/app-tabs-vertical';
import IconButton from '@/components/common/icon-button';
import { ROUTES } from '@/constants/routes-constants';
import { vendorTabList } from '@/constants/vendor-constants';
import { getQueryParam, updateQueryParam } from '@/lib/utils';
import {
  useAddUpdateVendorInfoMutation,
  useGetVendorsQuery,
} from '@/redux/features/vendors-api/vendors.api';
import { AllVendorsTabTypeMap, VENDOR_TABS } from '@/types/vendor.types';
import { isEqual } from 'lodash';
import { SaveIcon, X } from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, SubmitHand<PERSON>, useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import {
  createPayload,
  generateVendorsDefaultValues,
  getApiRouteForTab,
} from './constants';

const AddEditVendors = () => {
  const id = getQueryParam('id') as string;
  const navigate = useNavigate();
  const tabName = (getQueryParam('tab') ||
    VENDOR_TABS.BUSINESS_INFO) as keyof AllVendorsTabTypeMap;
  const [activeTab, setActiveTab] = useState<
    Extract<keyof AllVendorsTabTypeMap, string>
  >(tabName as Extract<keyof AllVendorsTabTypeMap, string>);

  // get vendor details
  const { data: vendorData, isLoading: isVendorLoading } = useGetVendorsQuery(
    id,
    { skip: !id }
  );

  // add update vendor
  const [addUpdateVendorInfo, { isLoading: isSubmitting }] =
    useAddUpdateVendorInfoMutation();

  const defaultValues = useMemo(() => {
    return generateVendorsDefaultValues(vendorData?.data || {});
  }, [vendorData?.data]);

  const form = useForm<AllVendorsTabTypeMap[typeof activeTab]>({
    defaultValues: defaultValues,
  });
  const { handleSubmit, reset } = form;

  useEffect(() => {
    if (defaultValues) reset(defaultValues);
  }, [defaultValues, reset]);

  const onSubmit: SubmitHandler<
    AllVendorsTabTypeMap[typeof activeTab]
  > = async (formData) => {
    try {
      const url = getApiRouteForTab(activeTab, id);

      const payload = createPayload(activeTab, formData);
      // console.log(payload);
      const { data }: any = await addUpdateVendorInfo({
        url,
        data: payload,
        method: id ? 'PUT' : 'POST',
      });

      if (!id && data?.statusCode === 200) {
        updateQueryParam(data?.data?.id);
      }
    } catch (error) {
      //   console.error('Error submitting form:', error);
    }
  };

  const handleTabChange = useCallback((value: keyof AllVendorsTabTypeMap) => {
    setActiveTab(value as string);
    updateQueryParam(value, 'tab');
  }, []);

  const navigateToVendorsList = useCallback(
    () => navigate(ROUTES.VENDORS),
    [navigate]
  );

  useEffect(() => {
    if (activeTab === VENDOR_TABS.ADDITIONAL_CONTACT_INFO && !id) {
      setActiveTab(VENDOR_TABS.BUSINESS_INFO);
      updateQueryParam(VENDOR_TABS.BUSINESS_INFO, 'tab');
    }
  }, [id, activeTab]);

  const FilteredVendorTabs = useMemo(() => {
    return vendorTabList.filter((tab) =>
      !id && tab.value === VENDOR_TABS.ADDITIONAL_CONTACT_INFO ? false : true
    );
  }, [id]);

  const isVendorName = form.watch('vendorName');
  const isFormModified = isEqual(form.watch(), defaultValues);

  return (
    <FormProvider {...form}>
      <div className="flex flex-col gap-6 p-4 pt-0">
        <header className="flex justify-between items-center px-4 pt-4 pb-2 sticky top-16 z-[10] bg-white">
          <div className="flex gap-x-4 items-center">
            <IconButton onClick={navigateToVendorsList}>
              <CheveronLeft />
            </IconButton>
            <h1
              className="text-2xl text-text-tertiary font-semibold cursor-pointer"
              onClick={navigateToVendorsList}
            >
              Vendor
            </h1>
            <div className="flex items-center gap-3">
              <span className="text-2xl font-semibold text-text-tertiary">
                {' / '}
              </span>
              <p className="text-2xl capitalize font-semibold">
                {id
                  ? ((defaultValues?.vendorName as string) ?? '')
                  : 'New Vendor'}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <AppButton
              label="Save Detail"
              icon={SaveIcon}
              onClick={handleSubmit(onSubmit)}
              iconClassName="w-4 h-4"
              isLoading={isSubmitting}
              disabled={
                isFormModified || (tabName === 'contact-info' && !isVendorName)
              }
            />
            <AppButton
              label="Cancel"
              onClick={navigateToVendorsList}
              variant="neutral"
              icon={X}
              iconClassName="w-5 h-5"
            />
          </div>
        </header>

        <AppTabsVertical
          tabs={FilteredVendorTabs}
          activeTab={activeTab as string}
          onTabChange={handleTabChange}
          className="px-4 pb-3"
        />
      </div>
      <AppSpinner overlay isLoading={isVendorLoading} />
    </FormProvider>
  );
};

export default AddEditVendors;
