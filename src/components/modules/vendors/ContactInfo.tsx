import InputField from '@/components/forms/input-field';
import PhoneInputWidget from '@/components/forms/phone-input-mask';
import { EMAIL_VALIDATION_RULE } from '@/constants/validation-constants';
import { BusinessInfoTypes } from '@/types/vendor.types';
import { useFormContext } from 'react-hook-form';

const ContactInfo = () => {
  const form = useFormContext<BusinessInfoTypes>();

  return (
    <div className="flex flex-col gap-6 justify-between h-full">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <InputField
          name="contact"
          form={form}
          label="Contact"
          placeholder="Contact"
          maxLength={64}
        />
        <PhoneInputWidget form={form} name="tel1" label="Phone" />
        <PhoneInputWidget form={form} name="tel2" label="Phone 2" />
        <PhoneInputWidget form={form} name="telfax" isFax={true} label="Fax" />
        <InputField
          name="emailaddress"
          form={form}
          label="E-mail"
          placeholder="E-mail"
          validation={EMAIL_VALIDATION_RULE}
        />
      </div>
    </div>
  );
};

export default ContactInfo;
