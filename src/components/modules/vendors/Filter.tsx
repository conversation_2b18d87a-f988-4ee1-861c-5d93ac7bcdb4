import AppButton from '@/components/common/app-button';
import { AppTableContext } from '@/components/common/app-data-table/AppTableContext';
import InputField from '@/components/forms/input-field';
import SelectDropDown from '@/components/forms/select-dropdown';
import { Separator } from '@/components/ui/separator';
import { statusList } from '@/constants/common-constants';
import { getQueryParam, updateQueryParam } from '@/lib/utils';
import {
  clearAllFilters,
  setFilter,
  updateFormValues,
} from '@/redux/features/vendors-api/vendorSlice';
import { RootState } from '@/redux/store';
import { searchByFilterOptions } from '@/types/vendor.types';
import { memo, useContext, useEffect } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import { operatorType } from '../orders/constants';

interface FilterFormValues {
  vendor: string;
  address: string;
  city: string;
  state: string;
  phone: string;
  zipCode: string | null;
  searchBy: string;
  operator: string;
  searchValue: string;
  status: string;
}

const defaultValues: FilterFormValues = {
  vendor: '',
  address: '',
  city: '',
  state: '',
  phone: '',
  zipCode: '',
  searchBy: '',
  operator: 'contains',
  searchValue: '',
  status: '',
};

interface FilterProps {
  setIsFilterOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const Filter: React.FC<FilterProps> = ({ setIsFilterOpen }) => {
  const search = getQueryParam('search') as string;
  const formValuesFromRedux = useSelector(
    (state: RootState) => state.vendor.formValues
  );

  const dispatch = useDispatch();
  const { pagination, setPagination } = useContext(AppTableContext);
  // Initialize form with values from Redux
  const form = useForm<FilterFormValues>({
    defaultValues: formValuesFromRedux,
    mode: 'onChange',
  });

  // Watch form changes and update Redux state
  useEffect(() => {
    const subscription = form.watch((values) => {
      dispatch(updateFormValues(values));
    });

    return () => subscription.unsubscribe();
  }, [form, dispatch]);

  //VendorTypeFilter
  const onSubmit: SubmitHandler<any> = (data) => {
    const key: any = data.searchBy;
    const filterLabel = searchByFilterOptions?.find(
      ({ value }) => value == key
    )?.label;
    const newFilterData: any[] = [
      {
        label: filterLabel,
        value: data.searchValue,
        name: key,
        tagValue: data.searchValue,
        operator: data.operator,
      },
      {
        label: 'Status',
        value: data.status,
        name: 'isactive',
        tagValue: data.status === 'true' ? 'Active' : 'Inactive',
        operator: 'Equals',
      },
    ].filter((vendor) => vendor.value);

    dispatch(setFilter(newFilterData));
    setIsFilterOpen(false);
    setPagination({
      ...pagination,
      pageIndex: 0,
    });
  };

  const handleClear = () => {
    form.reset(defaultValues); // Reset form to default values
    dispatch(clearAllFilters()); // Clear all filters in Redux
    setIsFilterOpen(false); // Close the filter form
    if (search) {
      updateQueryParam(null, 'search');
    }
  };

  // Track if form values are different from the default values
  const isFormModified =
    JSON.stringify(form.watch()) !== JSON.stringify(defaultValues);

  useEffect(() => {
    if (!form.watch('searchBy')) {
      form.setValue('searchValue', '');
    }
  }, [form]);

  useEffect(() => {
    if (search) {
      form.setValue('searchBy', 'vendor');
      form.setValue('searchValue', search);
      form.setValue('operator', 'Contains');
    }
  }, [form, search]);

  return (
    <>
      <div className="px-3">
        <div className="text-normal py-2 font-semibold">Filters</div>
        <Separator
          orientation="horizontal"
          className="h-[1px] bg-border-Default mb-2"
        />
        <div className="grid col-span-1 md:grid-cols-1 gap-3">
          <SelectDropDown
            form={form}
            name="status"
            label="Status"
            optionsList={statusList}
            placeholder="Status"
          />

          <div className="grid grid-cols-2 gap-3">
            <SelectDropDown
              form={form}
              name="searchBy"
              label="Search By"
              placeholder="Select Search By"
              onChange={(value) => {
                if (!value) {
                  form.setValue('searchValue', '');
                }
              }}
              optionsList={searchByFilterOptions}
            />
            <SelectDropDown
              form={form}
              name="operator"
              label="Operator"
              allowClear={false}
              placeholder="Select Operator"
              optionsList={operatorType}
            />
          </div>
          <InputField
            form={form}
            name="searchValue"
            disabled={!form.watch('searchBy')}
            label="Value"
            placeholder="Enter Event Description"
          />
        </div>
        <div className="w-full h-full flex justify-between gap-3 mt-3 sticky -bottom-2 pt-1 pb-3 bg-white z-30">
          <AppButton
            className="w-full"
            onClick={form.handleSubmit(onSubmit)}
            label="Apply"
            disabled={!isFormModified}
          />
          <AppButton
            className="w-full"
            label="Clear"
            variant="neutral"
            onClick={handleClear}
            disabled={!isFormModified}
          />
        </div>
      </div>
    </>
  );
};

export default memo(Filter);
