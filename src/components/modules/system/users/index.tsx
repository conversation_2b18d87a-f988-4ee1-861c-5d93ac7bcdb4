import DotIcon from '@/assets/icons/DotIcon';
import EditIcon from '@/assets/icons/EditIcon';
import MailIcon from '@/assets/icons/MailIcon';
import AppButton from '@/components/common/app-button';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import AppDataTable from '@/components/common/app-data-table';
import AppSpinner from '@/components/common/app-spinner';
import StatusBadge from '@/components/common/app-status-badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { UseToast } from '@/components/ui/toast/ToastContainer';
import { SYSTEM_USERS_API_ROUTES } from '@/constants/api-constants';
import { ROUTES } from '@/constants/routes-constants';
import { useGetItemsMutation } from '@/redux/features/common-api/common.api';
import { useUpdateUserStatusMutation } from '@/redux/features/system/users.api';
import { User } from '@/types/system-types';
import { ColumnDef } from '@tanstack/react-table';
import { MonitorCheck, PlusIcon, ShieldX } from 'lucide-react';
import { useCallback, useMemo, useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';

const Users = () => {
  const navigate = useNavigate();
  const toast = UseToast();
  const [addNewItem, { isLoading: resendLoading }] = useGetItemsMutation();
  const [openDelete, setOpenDelete] = useState<boolean>(false);
  const [userDetails, setUserDetails] = useState<User | null>(null);
  const [refresh, setRefresh] = useState<boolean>(false);
  const [updateStatus, { isLoading }] = useUpdateUserStatusMutation();
  const toggleDelete = useCallback((row?: User) => {
    setUserDetails(row ? row : null);
    setOpenDelete((prev) => !prev);
  }, []);

  const handleDelete = useCallback(async () => {
    if (userDetails)
      await updateStatus({
        url: SYSTEM_USERS_API_ROUTES.USER_ACTIVATION(
          userDetails?.id ?? 0,
          !userDetails?.isActive
        ),
      }).unwrap();
    setRefresh(true);
    setTimeout(() => setRefresh(false), 500);

    toggleDelete();
  }, [toggleDelete, updateStatus, userDetails]);

  const handleResendEmail = useCallback(
    async (id?: number) => {
      try {
        if (id) {
          const responseData: any = await addNewItem({
            url: SYSTEM_USERS_API_ROUTES.RESEND_INVITE(id),
          }).unwrap();
          if (responseData) {
            toast.success(responseData.message);
            setRefresh(true);
          }
        }
      } catch (error) {
        // toast.error('Failed to resend invite');
      } finally {
        setTimeout(() => setRefresh(false), 500);
      }
    },
    [addNewItem, toast]
  );

  const columns: ColumnDef<User>[] = useMemo(
    () => [
      {
        accessorKey: 'userId',
        header: 'User ID',
        size: 100,
        enableSorting: true,
      },
      {
        accessorKey: 'firstName',
        header: 'First Name',
        size: 200,
        enableSorting: true,
      },
      {
        accessorKey: 'lastName',
        header: 'Last Name',
        size: 200,
        enableSorting: true,
      },
      {
        accessorKey: 'isActive',
        header: 'Status',
        cell: (info) => <StatusBadge status={info?.row?.original?.isActive} />,
        size: 120,
        enableSorting: true,
      },
    ],
    []
  );

  const actionColumn: ColumnDef<User> = useMemo(
    () => ({
      id: 'action',
      size: 110,
      header: 'Actions',
      cell: ({ row }) => {
        const storeOptionId = row?.original?.id;
        return (
          <div className="flex flex-row gap-x-4 justify-center items-center ">
            <Link
              to={`${ROUTES?.EDIT_SYSTEM_USERS}?id=${storeOptionId}&tab=information`}
              className="flex items-center justify-center gap-2 border border-text-tertiary rounded-md px-3 py-1"
            >
              <EditIcon /> Edit details
            </Link>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className="h-8 w-8 p-0"
                  aria-label="Open actions menu"
                >
                  <DotIcon />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                align="end"
                className="w-[300px] p-2 rounded-lg z-10"
              >
                <DropdownMenuItem
                  onClick={() => toggleDelete(row.original)}
                  className="pl-4 pr-4 pt-3 pb-3 text-base"
                  aria-label={`Activation user with ID ${storeOptionId}`}
                >
                  <div className="flex flex-row gap-3 items-center">
                    {row?.original.isActive ? (
                      <ShieldX className="bg-red" />
                    ) : (
                      <MonitorCheck />
                    )}
                    <span>
                      {row?.original.isActive ? 'Deactivate' : 'Activate'}
                    </span>
                  </div>
                </DropdownMenuItem>
                {!row?.original.isEmailConfirmed && (
                  <DropdownMenuItem
                    onClick={() => handleResendEmail(row?.original?.id)}
                    className="pl-4 pr-4 pt-3 pb-3 text-base text-text-Default"
                  >
                    <div className="flex flex-row gap-3 items-center">
                      <MailIcon />
                      <span>Resend Invite</span>
                    </div>
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        );
      },
    }),
    [toggleDelete, handleResendEmail]
  );

  const handleNewStoreOptions = useCallback(
    () => navigate(`${ROUTES.ADD_SYSTEM_USERS}?tab=information`),
    [navigate]
  );

  // Custom toolbar component
  const CustomToolbar = (
    <AppButton
      icon={PlusIcon}
      className="w-[150px]"
      label="New User"
      aria-label="Add Users"
      onClick={handleNewStoreOptions}
    />
  );

  return (
    <div className="flex flex-col p-6 gap-6">
      <AppDataTable
        url={SYSTEM_USERS_API_ROUTES.ALL}
        customToolBar={CustomToolbar}
        columns={[...columns, actionColumn]}
        heading="Users"
        enableSearch={true}
        enablePagination={true}
        tableClassName="max-h-[580px] overflow-auto"
        searchKey={'userId'}
        refreshList={refresh}
      />
      <AppConfirmationModal
        aria-labelledby="modal-title"
        aria-describedby="user-activation-description"
        title={'Confirmation'}
        description={
          <div>
            Are you sure you want to{' '}
            {userDetails?.isActive
              ? 'deactivate this user'
              : 'activate this user'}{' '}
            <strong> {userDetails?.userId as any}</strong> ?
          </div>
        }
        open={openDelete}
        onOpenChange={toggleDelete}
        handleCancel={toggleDelete}
        handleSubmit={handleDelete}
        // isLoading={isDeleteLoading}
      />
      <AppSpinner overlay isLoading={isLoading || resendLoading} />
    </div>
  );
};

export default Users;
