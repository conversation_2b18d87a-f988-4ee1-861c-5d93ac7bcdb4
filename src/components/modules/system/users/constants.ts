import { SYSTEM_USERS_API_ROUTES } from '@/constants/api-constants';
import { UserTabs } from '@/types/system-types';

// Define the structure of the SystemUsersOptionsTabKeyList object
export const SystemUsersOptionsTabKeyList: {
  [key: string]: string[];
} = {
  information: [
    'userId',
    'firstName',
    'lastName',
    'isActive',
    'salesCommission',
    'emailWork',
    'emailHome',
    'defaultLocationId',
  ],
  'users-options': ['phoneWork', 'phoneCell', 'fax', 'userId'],
  'profile-options': [
    'accessAllOptions',
    'createDeletePayments',
    'processCreditCard',
    'orderEntryReadOnly',
    'accounting',
    'purchaseOrders',
    'customers',
    'reports',
    'items',
    'subRentals',
    'lists',
    'vendors',
    'orders',
    'warehouse',
    'processes',
  ],
};

// API route mapping for each tab
export const ADD_SYSTEM_USERS_API_ROUTES_MAP = {
  information: SYSTEM_USERS_API_ROUTES.INFORMATION_SAVE,
  'users-options': SYSTEM_USERS_API_ROUTES.USER_OPTIONS_SAVE,
  'profile-options': SYSTEM_USERS_API_ROUTES.PROFILE_OPTIONS_SAVE,
};

// API route mapping for updating an existing user
export const EDIT_SYSTEM_USERS_API_ROUTES_MAP_UPDATE = () => ({
  information: SYSTEM_USERS_API_ROUTES.INFORMATION_UPDATE,
  'users-options': SYSTEM_USERS_API_ROUTES.USER_OPTIONS_UPDATE,
  'profile-options': SYSTEM_USERS_API_ROUTES.PROFILE_OPTIONS_UPDATE,
});

export const getApiRouteForTab = (
  activeTab: keyof UserTabs,
  id?: string
): string => {
  if (id) {
    // Update route when ID is present
    const updateRoutes = EDIT_SYSTEM_USERS_API_ROUTES_MAP_UPDATE();
    return updateRoutes[activeTab];
  }
  // Add route when ID is not present
  return ADD_SYSTEM_USERS_API_ROUTES_MAP[activeTab];
};
