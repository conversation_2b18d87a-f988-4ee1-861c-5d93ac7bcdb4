import AppSpinner from '@/components/common/app-spinner';
import <PERSON><PERSON><PERSON>ield from '@/components/common/switch';
import { cn, getQueryParam } from '@/lib/utils';
import { useGetPermissionsQuery } from '@/redux/features/system/users.api';
import { useEffect, useMemo, useState } from 'react';
import { useFormContext } from 'react-hook-form';

// Define TypeScript interfaces
interface Permission {
  permissionId: number;
  permission: string;
  parentPermissionId: number | null;
  parentPermission: string | null;
  enabled: boolean;
  description: string;
}

type PermissionForm = {
  permissions: Record<string, boolean>;
};

const containerClass =
  'grid grid-cols-1 gap-4 md:grid-cols-2 border rounded-md p-6 text-[#1E1E1E]';

const ProfileOptions = () => {
  const id = getQueryParam('id') as string;
  const form = useFormContext<PermissionForm>();
  const { data, isLoading } = useGetPermissionsQuery(id);

  const actionAccess: Permission[] = useMemo(() => {
    return data?.data?.actionAccess || [];
  }, [data]);

  const featureAccess: Permission[] = data?.data?.featureAccess || [];

  const [associtateItems, setAssocitateItems] = useState<number[]>([]);

  useEffect(() => {
    if (actionAccess.length) {
      let temp: number[] = [];
      actionAccess.forEach((element: Permission) => {
        if (element?.parentPermissionId) temp.push(element?.parentPermissionId);
      });

      setAssocitateItems(temp);
    }
  }, [actionAccess, associtateItems]);

  const handleSwitch = (option: Permission) => {
    const parentAssociates = actionAccess.filter(
      (item) => item.parentPermissionId === option.permissionId
    );
    if (parentAssociates.length)
      parentAssociates.forEach((associate) => {
        form.setValue(`permissions.${associate.permissionId}`, false);
      });
  };

  const renderSwitchColumns = (fields: Permission[]) => {
    const columns: JSX.Element[][] = [[], []];
    fields.forEach((option, index) => {
      const isDiabled =
        associtateItems.includes(option?.parentPermissionId ?? 0) &&
        !form.watch(`permissions.${option?.parentPermissionId}`);
      const columnIndex = index % 2;
      columns[columnIndex].push(
        <div
          key={option.permissionId}
          className={cn(
            'flex flex-col gap-2 text-[#1E1E1E] p-1',
            index > 1 ? 'my-4 md:my-6' : ''
            // isDiabled ? 'bg-gray-300 rounded-sm cursor-not-allowed ' : ''
          )}
        >
          <SwitchField
            label={option.permission}
            form={form}
            name={`permissions.${option.permissionId}`}
            className="w-full"
            description={option.description}
            disabled={isDiabled}
            onChange={() => handleSwitch(option)}
          />
        </div>
      );
    });
    return columns.map((column, colIndex) => (
      <div
        key={colIndex}
        className={cn(
          colIndex === 0 ? 'md:pr-6 md:border-r md:border-gray-300' : 'md:pl-6'
        )}
      >
        {column}
      </div>
    ));
  };

  return (
    <div className="flex flex-col gap-6 justify-between h-full">
      <div>
        <h1 className="mb-2 text-[#1E1E1E] text-[22px]">Feature Access</h1>
        <div className={cn(containerClass)}>
          {renderSwitchColumns(featureAccess)}
        </div>
      </div>
      <div className={cn(containerClass, 'mt-4 md:mt-0')}>
        {renderSwitchColumns(actionAccess)}
      </div>
      <AppSpinner overlay isLoading={isLoading} />
    </div>
  );
};

export default ProfileOptions;
