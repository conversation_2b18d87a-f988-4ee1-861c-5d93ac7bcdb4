import PhoneInputWidget from '@/components/forms/phone-input-mask';
import {
  FAX_VALIDATION_RULE,
  PHONE_VALIDATION_RULE,
} from '@/constants/validation-constants';
import { UsersOptionsSystemTypes } from '@/types/system-types';
import { useFormContext } from 'react-hook-form';

const UsersOptions = () => {
  const form = useFormContext<UsersOptionsSystemTypes>();

  return (
    <div className="flex flex-col gap-6 justify-between h-full">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <PhoneInputWidget
          name="phoneWork"
          error={form.formState.errors}
          form={form}
          label="Phone (Work)"
          validation={PHONE_VALIDATION_RULE}
        />
        <PhoneInputWidget
          name="phoneCell"
          error={form.formState.errors}
          form={form}
          label="Phone (Cell)"
          validation={PHONE_VALIDATION_RULE}
        />
        <PhoneInputWidget
          name="fax"
          form={form}
          label="Fax"
          validation={FAX_VALIDATION_RULE}
          isFax
        />
      </div>
    </div>
  );
};

export default UsersOptions;
