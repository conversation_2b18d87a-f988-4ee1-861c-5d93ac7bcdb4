import SwitchField from '@/components/common/switch';
import Input<PERSON>ield from '@/components/forms/input-field';
import NumberIn<PERSON><PERSON>ield from '@/components/forms/number-input-field';
import SelectWidget from '@/components/forms/select';
import { STORE_OPTIONS_API_ROUTES } from '@/constants/api-constants';
import {
  ALPHA_NUMERIC_VALIDATION_RULE,
  emailValidation,
  TEXT_VALIDATION_RULE,
} from '@/constants/validation-constants';
import useOptionList from '@/hooks/useOptionList';
import { getQueryParam } from '@/lib/utils';
import { User } from '@/types/system-types';
import { useFormContext } from 'react-hook-form';

const UserInformation = () => {
  const form = useFormContext<User>();
  const id = getQueryParam('id') as string;

  const { options: storeLocationList, optionLoading } = useOptionList({
    url: STORE_OPTIONS_API_ROUTES.ALL,
    labelKey: 'locationName',
    valueKey: 'id',
    sortBy: 'locationName',
  });

  return (
    <div className="flex flex-col gap-6 justify-between h-full">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <InputField
          name="userId"
          form={form}
          label="User ID"
          placeholder="Enter User ID"
          maxLength={3}
          disabled={Boolean(id)}
          validation={ALPHA_NUMERIC_VALIDATION_RULE}
        />
        {id && (
          <div>
            <SwitchField
              label="Status"
              form={form}
              name="isActive"
              description="This defines if the user is active or inactive"
            />
          </div>
        )}
      </div>
      <div className="grid grid-cols-4 gap-4">
        <InputField
          name="firstName"
          form={form}
          label="First Name"
          placeholder="Enter First Name"
          maxLength={30}
          validation={TEXT_VALIDATION_RULE}
        />
        <InputField
          name="lastName"
          form={form}
          label="Last Name"
          maxLength={30}
          placeholder="Enter Last Name"
          validation={TEXT_VALIDATION_RULE}
        />
      </div>
      <div className="grid grid-cols-2 gap-4">
        <InputField
          name="emailWork"
          form={form}
          label="Email (Work)"
          placeholder="Enter Email (Work)"
          disabled={Boolean(id)}
          validation={emailValidation(true)}
        />
      </div>
      <div className="grid grid-cols-2 gap-4">
        <SelectWidget
          form={form}
          name="defaultLocationId"
          label="Store Location"
          placeholder="Select Store Location"
          isClearable={false}
          validation={TEXT_VALIDATION_RULE}
          optionsList={storeLocationList}
          isLoading={optionLoading}
        />
      </div>
      <div className="grid grid-cols-2 gap-4">
        <InputField
          name="emailHome"
          form={form}
          label="Email (Home)"
          placeholder="Enter Email (Home)"
          validation={emailValidation(false)}
        />
      </div>

      <div className="grid grid-cols-2 gap-4">
        <NumberInputField
          name="salesCommission"
          form={form}
          label="Sales Commision"
          placeholder="___.__%"
          maxLength={6}
          fixedDecimalScale
          suffix="%"
        />
      </div>
      <div className="font-light p-1">
        {' '}
        <span className="text-base font-medium"> Note : </span>Saving these
        details will send an invite e-mail to the user for them to create their
        account and set their password.{' '}
      </div>
    </div>
  );
};

export default UserInformation;
