import CheveronLeft from '@/assets/icons/CheveronLeft';
import AppButton from '@/components/common/app-button';
import AppSpinner from '@/components/common/app-spinner';
import AppTabsVertical from '@/components/common/app-tabs-vertical';
import IconButton from '@/components/common/icon-button';
import { ROUTES } from '@/constants/routes-constants';
import { SystemTabList } from '@/constants/system-constants';
import { filterFormData, getQueryParam, updateQueryParam } from '@/lib/utils';
import {
  useGetPermissionsQuery,
  useGetSystemUsersQuery,
  usePostProfileOptionsMutation,
  usePostSystemUserMutation,
} from '@/redux/features/system/users.api';
import { Permission, UserTabs } from '@/types/system-types';
import { SaveIcon } from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, SubmitHand<PERSON>, useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import { getApiRouteForTab, SystemUsersOptionsTabKeyList } from './constants';
import { RootState } from '@/redux/store';
import { useSelector } from 'react-redux';

const AddEditUsers = () => {
  const id = getQueryParam('id') as string;
  const { data: featureData } = useGetPermissionsQuery(id, { skip: !id });

  const permissionSetterForm = (permissions: Permission[]) => {
    let bindings = permissions.reduce(
      (acc, permission) => {
        acc[permission.permissionId] = permission.enabled;
        return acc;
      },
      {} as { [key: number]: boolean }
    );

    return bindings;
  };

  const tabName = (getQueryParam('tab') || 'information') as keyof UserTabs;
  const profile = useSelector((state: RootState) => state.loginProfile.profile);
  const navigation = useNavigate();
  const [activeTab, setActiveTab] = useState<keyof UserTabs>(tabName);

  const { data: systemUsersData, isLoading: systemUserIsLoading } =
    useGetSystemUsersQuery(id, { skip: !id });

  const [addUpdateSystemUser, { isLoading }] = usePostSystemUserMutation();
  const [updateProfileOptions, { isLoading: profileOptionLoading }] =
    usePostProfileOptionsMutation();

  const defaultValues = useMemo(() => {
    const data = systemUsersData?.data;
    const { actionAccess, featureAccess } = featureData?.data || {
      actionAccess: [],
      featureAccess: [],
    };

    return {
      ...data,
      salesCommission: data?.salesCommission || '0',
      isActive: data?.isActive || false,
      phoneWork: data?.phoneWork ?? '',
      phoneCell: data?.phoneCell ?? '',
      fax: data?.fax ?? '',
      permissions: permissionSetterForm([...actionAccess, ...featureAccess]),
      defaultLocationId: id
        ? data?.defaultLocationId
        : (Number(profile.defaultLocationId) ?? null),
      actionAccess,
      featureAccess,
    };
  }, [id, featureData?.data, profile, systemUsersData?.data]);

  // Initialize form hook
  const form = useForm<UserTabs[typeof activeTab]>({
    defaultValues: defaultValues,
    mode: 'onChange',
  });
  const { handleSubmit, reset } = form;

  // Reset form when default values or tab changes
  useEffect(() => {
    if (defaultValues) {
      reset(defaultValues);
    }
  }, [defaultValues, activeTab, reset]);

  const formDataMapping = (
    actionAccess: Permission[],
    permissions: Record<string, any>
  ): Permission[] => {
    // Map over actionAccess to create a new array with modified enabled values
    return actionAccess?.map((item) => {
      // Check if the item's permission ID exists in the permissions object and modify `enabled`
      const updatedEnabled = permissions[item?.permissionId];
      return {
        ...item,
        enabled: updatedEnabled, // Update the enabled field
      };
    });
  };

  const onSubmit: SubmitHandler<UserTabs[typeof activeTab]> = async (
    formData
  ) => {
    try {
      let url = getApiRouteForTab(activeTab, id);
      if (!url) {
        throw new Error('No URL available for the requested operation');
      }
      if (activeTab === 'profile-options') {
        // console.log('formData', formData);
        const { actionAccess, featureAccess } = featureData?.data || {
          actionAccess: [],
          featureAccess: [],
        };
        if (formData?.permissions) {
          const payload = {
            actionAccess: formDataMapping(actionAccess, formData?.permissions),
            featureAccess: formDataMapping(
              featureAccess,
              formData?.permissions
            ),
          };
          const { data }: any = await updateProfileOptions({
            url,
            data: { ...payload, ...(id && { userId: parseInt(id) }) },
          });
          const storeOPtionId = data?.data?.id;
          if (!id && storeOPtionId) {
            updateQueryParam(storeOPtionId);
          }
        }
      } else {
        const selectedTabKeys = SystemUsersOptionsTabKeyList[
          activeTab
        ] as (keyof typeof formData)[];
        const payload = filterFormData(formData, selectedTabKeys);
        const { data }: any = await addUpdateSystemUser({
          url,
          data: { ...payload, ...(id && { id: parseInt(id) }) },
        });
        const storeOPtionId = data?.data?.id;
        if (!id && storeOPtionId) {
          updateQueryParam(storeOPtionId);
        }
      }
    } catch (error) {
      // Handle error (could display error message to user)
    }
  };

  const handleTabChange = useCallback((value: keyof UserTabs) => {
    setActiveTab(value);
    updateQueryParam(value, 'tab');
  }, []);

  const navigateToUser = useCallback(() => {
    navigation(ROUTES.SYSTEM_USERS);
  }, [navigation]);

  return (
    <FormProvider {...form}>
      <div className="flex flex-col gap-6 p-4 h-[95vh]">
        <div className="flex justify-between items-center px-4 sticky top-16 z-[10] bg-white py-2">
          <div className="flex gap-x-4 items-center">
            <IconButton onClick={navigateToUser}>
              <CheveronLeft />
            </IconButton>
            <h1 className="text-2xl text-text-tertiary font-semibold">
              System /{' '}
              {`${systemUsersData?.data?.userId ? 'Edit User / ' + systemUsersData?.data?.userId : 'Add User'}`}
            </h1>
          </div>
          <div className="flex items-center gap-3">
            <AppButton
              label="Save Detail"
              icon={SaveIcon}
              onClick={handleSubmit(onSubmit)}
              iconClassName="w-4 h-4"
              isLoading={isLoading || profileOptionLoading}
            />
            <AppButton
              label="Cancel"
              onClick={navigateToUser}
              variant="neutral"
            />
          </div>
        </div>
        <AppTabsVertical
          tabs={SystemTabList}
          activeTab={tabName}
          onTabChange={handleTabChange}
          showTabMenu={!!id}
          className="px-4 pb-3"
        />
      </div>
      <AppSpinner
        overlay
        isLoading={systemUserIsLoading || profileOptionLoading}
      />
    </FormProvider>
  );
};

export default AddEditUsers;
