import SwitchField from '@/components/common/switch';
import {
  StoreOrderItemSettingsTypes,
  SwitchFieldConfig,
} from '@/types/store.types';
import { useFormContext } from 'react-hook-form';

// Array of fields to be rendered as SwitchFields
const switchFields: SwitchFieldConfig<StoreOrderItemSettingsTypes>[] = [
  { label: 'Item Delivery Locations', name: 'itemDeliveryLocations' },
  {
    label: 'Include Quotes in Inventory Check',
    name: 'includeQuotesInInventoryChecks',
  },
  { label: 'Edit Item Info', name: 'editItemInfo' },
  { label: 'Show Item Type Column', name: 'showItemTypeColumn' },
  { label: 'Show Sub-Rental Column', name: 'showSubRentalColumn' },
  { label: 'Zero-Out Qtys Instead of Deleting Paid Order', name: 'zeroOutQty' },
  {
    label: 'Combine Like Items When Adding to a Sub-Rental',
    name: 'combineLikeItems',
  },
  {
    label: 'Package Components Show Price',
    name: 'packageComponentsShowPrice',
  },
  { label: 'Retrive Serial #s to Flip', name: 'retrieveSerialToFlip' },
  {
    label: 'Include Serial # When Opening Overbooked Item Info',
    name: 'includeSerialWhenOpeningOverBookedItem',
  },
  { label: 'Surge Pricing', name: 'surgePricing' },
  { label: 'Show Serial# Column', name: 'showSerialNumberColumn' },
];

const OrderItemSettings = () => {
  const form = useFormContext<StoreOrderItemSettingsTypes>();
  return (
    <div className="flex flex-col gap-6 w-full h-full">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Render SwitchField components dynamically */}
        {switchFields?.map((field) => (
          <div key={field.name} className="mt-4">
            <SwitchField label={field.label} form={form} name={field.name} />
          </div>
        ))}
      </div>
    </div>
  );
};

export default OrderItemSettings;
