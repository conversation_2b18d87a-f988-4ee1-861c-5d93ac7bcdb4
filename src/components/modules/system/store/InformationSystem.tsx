import InputField from '@/components/forms/input-field';
import PhoneInputWidget from '@/components/forms/phone-input-mask';
import SelectWidget from '@/components/forms/select';
import ZipCodeInput from '@/components/forms/zipcode-input';
import {
  TEXT_ONLY_VALIDATION_RULE,
  TEXT_VALIDATION_RULE,
  WEB_SITE_VALIDATION_RULE,
} from '@/constants/validation-constants';
import { generateLabelValuePairs, getQueryParam } from '@/lib/utils';
import {
  useGetCountryListQuery,
  useGetStateByCountryQuery,
} from '@/redux/features/country/country.api';
import { StoreInformationTypes } from '@/types/store.types';
import { useCallback } from 'react';
import { useFormContext } from 'react-hook-form';
import InfoCard from '../company/InfoCard';

const InformationSystem = () => {
  const form = useFormContext<StoreInformationTypes>();
  const countryId = form.watch('countryId');
  const storeLocationId = getQueryParam('id') as string;

  const { data: countryData = [] } = useGetCountryListQuery();

  // Memoized country list
  const countryList = generateLabelValuePairs({
    data: countryData,
    labelKey: 'name',
    valueKey: 'country_id',
  });

  const { data: statesData = [], isFetching: stateIsLoading } =
    useGetStateByCountryQuery(
      { countryId: Number(countryId) },
      { refetchOnMountOrArgChange: true }
    );

  const stateList = generateLabelValuePairs({
    data: statesData,
    labelKey: 'code',
    valueKey: 'state_id',
  });

  const handleCountryChange = useCallback(
    (value: string) => {
      if (countryId !== value) {
        form.setValue('stateId', '');
        form.setValue('zipCode', '');
        form.clearErrors('zipCode');
      }
    },
    [countryId, form]
  );

  // Determine if current country is USA for validation
  const isUSA = Number(countryId) === 1;

  return (
    <div className="flex flex-col gap-6 justify-between h-full">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <InputField
          name="businessName"
          form={form}
          label="Business Name"
          placeholder="Enter Business Name"
          validation={TEXT_VALIDATION_RULE}
        />
        <InputField
          name="locationName"
          form={form}
          label="Location Name"
          placeholder="Enter Location Name"
          validation={TEXT_VALIDATION_RULE}
        />
        <InputField
          name="address1"
          form={form}
          label="Address"
          placeholder="Enter Address Line 1"
          validation={TEXT_VALIDATION_RULE}
        />
        <InputField
          name="address2"
          form={form}
          label=""
          placeholder="Enter Address Line 2"
          className="md:mt-8"
        />
        <InputField
          name="city"
          form={form}
          label="City"
          placeholder="Enter City"
          validation={TEXT_ONLY_VALIDATION_RULE}
        />
        <SelectWidget
          name="stateId"
          form={form}
          placeholder="Select State"
          label="State"
          isClearable={true}
          optionsList={stateList}
          isLoading={stateIsLoading}
          validation={TEXT_VALIDATION_RULE}
        />
        <ZipCodeInput
          name="zipCode"
          isUSA={isUSA}
          form={form}
          label="Zip Code"
          validation={TEXT_VALIDATION_RULE}
        />

        <SelectWidget
          form={form}
          name="countryId"
          label="Country"
          placeholder="Select Country"
          optionsList={countryList}
          onSelectChange={handleCountryChange}
          isClearable={false}
          validation={TEXT_VALIDATION_RULE}
        />
        <PhoneInputWidget form={form} name="phone" label="Phone" />
        <InputField
          name="website"
          form={form}
          label="Website"
          placeholder="Enter Website"
          validation={WEB_SITE_VALIDATION_RULE}
        />
      </div>

      {!storeLocationId && (
        <InfoCard
          title="Warning"
          message="Note: Please note that once a store location is created, it cannot be deleted. Consider this action carefully, as it will impact existing and future orders associated with this location. "
        />
      )}
    </div>
  );
};

export default InformationSystem;
