import AppButton from '@/components/common/app-button';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import AppSpinner from '@/components/common/app-spinner';
import AppTabsVertical from '@/components/common/app-tabs-vertical';
import IconButton from '@/components/common/icon-button';
import CheveronLeft from '@/assets/icons/CheveronLeft';
import { ROUTES } from '@/constants/routes-constants';
import { storeTabList } from '@/constants/store-options-constants';
import { filterFormData, getQueryParam, updateQueryParam } from '@/lib/utils';
import {
  useGetStoreQuery,
  usePostStoreMutation,
} from '@/redux/features/store/store.api';
import { AllTabTypeMap } from '@/types/store.types';
import { SaveIcon } from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import {
  ADD_EDIT_STORE_OPTIONS_API_ROUTES_MAP,
  generateStoreOptionsDefaultValues,
  StoreOPtionsTabKeyList,
} from './constants';

export const AddEditStoreOptions = () => {
  const id = getQueryParam('id') as string;
  const tabName = (getQueryParam('tab') ||
    'information') as keyof AllTabTypeMap;
  const navigation = useNavigate();
  const [activeTab, setActiveTab] = useState<keyof AllTabTypeMap>(tabName);
  const [isNewStore, setIsNewStore] = useState<boolean>(false);

  const { data: storeOptionsData, isLoading: storeOptionIsLoading } =
    useGetStoreQuery(id, { skip: !id });

  const [addUpdateStore, { isLoading }] = usePostStoreMutation();

  const defaultValues = useMemo(() => {
    return generateStoreOptionsDefaultValues(storeOptionsData?.data || {});
  }, [storeOptionsData?.data]);

  // Initialize form hook
  const form = useForm<AllTabTypeMap[typeof activeTab]>({
    defaultValues: defaultValues,
    mode: 'onChange',
  });
  const { handleSubmit, reset } = form;

  // Reset form when default values or tab changes
  useEffect(() => {
    if (defaultValues) {
      reset(defaultValues);
    }
  }, [defaultValues, activeTab, reset]);

  // toggle new store location
  const toggleNewStore = useCallback(() => {
    setIsNewStore((prev) => !prev);
  }, []);

  const onSubmit = useCallback(
    (isNewStore: boolean) =>
      async (formData: AllTabTypeMap[typeof activeTab]) => {
        if (isNewStore) {
          toggleNewStore();
          return;
        }

        try {
          const url = ADD_EDIT_STORE_OPTIONS_API_ROUTES_MAP[activeTab];

          if (!url) {
            throw new Error('No URL available for the requested operation');
          }

          const selectedTabKeys = StoreOPtionsTabKeyList[
            activeTab
          ] as (keyof typeof formData)[];

          const payload = filterFormData({ ...formData, id }, selectedTabKeys);
          const { data }: any = await addUpdateStore({
            url,
            data: payload,
          });
          const storeOPtionId = data?.data?.id;
          if (!id && storeOPtionId) {
            updateQueryParam(storeOPtionId);
            !isNewStore && toggleNewStore();
          }
        } catch (error) {
          // Handle error (could display error message to user)
        }
      },
    [activeTab, addUpdateStore, id, toggleNewStore]
  );

  const handleTabChange = useCallback((value: keyof AllTabTypeMap) => {
    setActiveTab(value);
    updateQueryParam(value, 'tab');
  }, []);

  const navigateToStore = useCallback(() => {
    navigation(ROUTES.STORE_OPTIONS);
  }, [navigation]);

  return (
    <FormProvider {...form}>
      <div className="flex flex-col gap-6 px-4 h-[95vh]">
        <div className="flex justify-between items-center px-4 sticky top-16 z-[10] bg-white py-3">
          <div className="flex gap-x-4 items-center">
            <IconButton onClick={navigateToStore}>
              <CheveronLeft />
            </IconButton>
            <h1
              className="text-2xl text-text-tertiary font-semibold hover:cursor-pointer"
              onClick={navigateToStore}
            >
              Store Locations
            </h1>

            <div className="flex items-center gap-3">
              <span className="text-2xl font-semibold text-text-tertiary">
                {' / '}
              </span>
              <p className="text-2xl capitalize font-semibold">
                {defaultValues?.businessName
                  ? defaultValues?.businessName
                  : 'New Store Location'}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <AppButton
              label="Save Detail"
              icon={SaveIcon}
              onClick={handleSubmit(
                onSubmit(activeTab === 'information' && !id)
              )}
              iconClassName="w-4 h-4"
              isLoading={isLoading}
            />
            <AppButton
              label="Cancel"
              onClick={navigateToStore}
              variant="neutral"
            />
          </div>
        </div>
        <AppTabsVertical
          tabs={storeTabList}
          activeTab={tabName}
          onTabChange={handleTabChange}
          showTabMenu={!!id}
          className="px-4 pb-3"
        />
      </div>
      <AppConfirmationModal
        title="Confirmation"
        description={
          <div>
            Once a store location is created, it cannot be deleted and will
            impact all associated orders. Please proceed carefully.
          </div>
        }
        open={isNewStore}
        onOpenChange={toggleNewStore}
        handleCancel={toggleNewStore}
        handleSubmit={handleSubmit(onSubmit(false))}
        isLoading={isLoading}
      />
      <AppSpinner overlay isLoading={storeOptionIsLoading} />
    </FormProvider>
  );
};
