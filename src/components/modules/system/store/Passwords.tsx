import PasswordInputField from '@/components/forms/PasswordInputField';
import { StorePasswordsTypes } from '@/types/store.types';
import { Path, useFormContext } from 'react-hook-form';

type PasswordFieldConfig<T> = {
  label: string;
  name: Path<T>;
};

const Passwords = () => {
  const form = useFormContext<StorePasswordsTypes>();

  // Array of password fields data
  const passwordFields: PasswordFieldConfig<StorePasswordsTypes>[] = [
    { label: 'Delete Order', name: 'deleteOrder' },
    { label: 'Undelete Order', name: 'unDeleteOrder' },
    { label: 'Delete Payment', name: 'deletePayment' },
    { label: 'Employees', name: 'employee' },
    { label: 'Order Comp', name: 'orderComp' },
    { label: 'Approval for Shipment', name: 'approvalShipment' },
    { label: 'Reports', name: 'reports' },
    { label: 'Unpost', name: 'unpost' },
    { label: 'View Credit Card', name: 'viewCreditCard' },
    { label: 'RFID Tag', name: 'rfidTag' },
    { label: 'Time Card', name: 'timecard' },
    { label: 'Data Processes', name: 'dataProcesses' },
  ];

  return (
    <div className="flex flex-col gap-6 w-full h-full">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {passwordFields?.map((field) => (
          <PasswordInputField
            form={form}
            label={field?.label}
            name={field?.name}
            placeholder={field.label}
          />
        ))}
      </div>
    </div>
  );
};

export default Passwords;
