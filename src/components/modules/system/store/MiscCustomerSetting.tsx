import SwitchField from '@/components/common/switch';
import InputField from '@/components/forms/input-field';
import SelectDropDown from '@/components/forms/select-dropdown';
import { useGetEnumsListQuery } from '@/redux/features/enums-api/enums-api';
import { StoreMiscCustomerSetting } from '@/types/store.types';
import { useFormContext } from 'react-hook-form';

const MiscCustomerSetting = () => {
  const form = useFormContext<StoreMiscCustomerSetting>();

  // Save Custom Pricing By
  const { data: customPricingByList, isLoading: customPricingByLoading } =
    useGetEnumsListQuery({
      name: 'CustomerPricingBy',
    });

  return (
    <div className="flex flex-col gap-6 w-full h-full">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <SelectDropDown
          form={form}
          optionsList={customPricingByList?.data || []}
          label="Save Custom Pricing By"
          name="saveCustomPriceBy"
          placeholder="Save Custom Pricing By"
          isLoading={customPricingByLoading}
        />
        <div className="mt-10">
          <SwitchField
            label="Use Pricing on Invoices Option"
            form={form}
            name="usePricing"
          />
        </div>
      </div>

      <InputField
        form={form}
        label="Credit Hold Message"
        name="creditHoldMessage"
        placeholder="Credit Hold Message"
      />
    </div>
  );
};

export default MiscCustomerSetting;
