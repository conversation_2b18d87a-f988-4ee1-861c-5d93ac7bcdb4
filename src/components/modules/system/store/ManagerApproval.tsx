import SwitchField from '@/components/common/switch';
import { StoreManagerApprovalTypes } from '@/types/store.types';
import { useFormContext } from 'react-hook-form';

const ManagerApproval = () => {
  const form = useFormContext<StoreManagerApprovalTypes>();
  const switchFields = [
    {
      label: 'Delivery Charge',
      name: 'deliveryCharges',
    },
    {
      label: 'Discount Changes',
      name: 'discountCharges',
    },
    {
      label: 'Price Changes',
      name: 'priceChanges',
    },
    {
      label: 'Tax Exempt',
      name: 'taxExempt',
    },
    {
      label: 'Payment Terms',
      name: 'paymentTerms',
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {switchFields?.map((field, index) => (
        <SwitchField
          key={`${field?.label}-${index}`}
          label={field?.label}
          name={field?.name as any}
          form={form}
          className="mt-4"
        />
      ))}
    </div>
  );
};

export default ManagerApproval;
