import InputField from '@/components/forms/input-field';
import { StoreFormsTypes } from '@/types/store.types';
import { useFormContext } from 'react-hook-form';

const Forms = () => {
  const form = useFormContext<StoreFormsTypes>();
  return (
    <div className="flex flex-col gap-6 justify-between h-full">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <InputField
          name="purchaseOrder"
          form={form}
          label="Purchase Order"
          placeholder="Enter Purchase Order"
        />
        <InputField
          name="faxPurchaseOrder"
          form={form}
          label="Fax Purchase Order"
          placeholder="Enter Fax Purchase Order"
        />
        <InputField
          name="poRequest"
          form={form}
          label="P/O Request"
          placeholder="Enter P/O Request"
        />
        <InputField
          name="faxPoRequest"
          form={form}
          label="Fax P/O Request"
          placeholder="Enter Fax P/O Request"
        />
        <InputField
          name="equipmentTransfer"
          form={form}
          label="Equipment Transfer"
          placeholder="Enter Equipment Transfer"
        />
        <InputField
          name="faxEquipmentTransfer"
          form={form}
          label="Fax Equipment Transfer"
          placeholder="Enter Fax Equipment Transfer"
        />
        <InputField
          name="arStatement"
          form={form}
          label="A/R Statement"
          placeholder="Enter A/R Statement"
        />
        <InputField
          name="faxArStatement"
          form={form}
          label="Fax A/R Statement"
          placeholder="Enter Fax A/R Statement"
        />
        <InputField
          name="arAdjustment"
          form={form}
          label="A/R Adjustment"
          placeholder="Enter A/R Adjustment"
        />
        <InputField
          name="faxArAdjustment"
          form={form}
          label="Fax A/R Adjustment"
          placeholder="Enter Fax A/R Adjustment"
        />
        <InputField
          name="arInquiry"
          form={form}
          label="A/R Inquiry"
          placeholder="Enter A/R Inquiry"
        />
        <InputField
          name="deliveryLocationDirections"
          form={form}
          label="Delivery Location Directions"
          placeholder="Enter Delivery Location Directions"
        />
        <InputField
          name="creditCardReceipt"
          form={form}
          label="Credit Card Receipt"
          placeholder="Enter Credit Card Receipt"
        />
        <InputField
          name="htmlConfirmation"
          form={form}
          label="HTML Confirmations"
          placeholder="Enter HTML Confirmations"
        />
        <InputField
          name="htmlInvoice"
          form={form}
          label="HTML Invoices"
          placeholder="Enter HTML Invoices"
        />
        <InputField
          name="htmlStatements"
          form={form}
          label="HTML Statements"
          placeholder="Enter HTML Statements"
        />
      </div>
    </div>
  );
};

export default Forms;
