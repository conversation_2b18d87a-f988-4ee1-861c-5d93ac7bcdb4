import AppButton from '@/components/common/app-button';
import CustomDialog from '@/components/common/dialog';
import SwitchField from '@/components/common/switch';
import DatePicker from '@/components/forms/date-picker';
import NumberInputField from '@/components/forms/number-input-field';
import Selector from '@/components/modules/lists/delivery-locations/Selector';
import { CATEGORY_API_ROUTES } from '@/constants/api-constants';
import useOptionList from '@/hooks/useOptionList';
import { cn } from '@/lib/utils';
import {
  StoreTotalExpeditedFeeType,
  StoreTotalsTypes,
  SwitchFieldConfig,
} from '@/types/store.types';
import { RowSelectionState } from '@tanstack/react-table';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import TotalExpeditedFee from './TotalExpeditedFee';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';

// Example data array representing the dynamic switches
const switchFields: SwitchFieldConfig<StoreTotalsTypes>[] = [
  {
    label: 'Production Fee Applies to Will Call Only',
    name: 'prodFeesAppliesToCall',
  },
  {
    label: 'Automatically Apply Damage Waiver Charges',
    name: 'autoApplyDamageWaiverCharges',
  },
  { label: 'Customer Level Damage Waiver', name: 'customerLevelDM' },
  {
    label: 'Customer Level Fuel Surcharge',
    name: 'customerLevelFuelSurcharge',
  },
  {
    label: 'Required Deposit Based on Item Sub-Total',
    name: 'requestedDepositItemSubTotal',
  },
  {
    label: 'Always Tax Sales & Missing Equipments',
    name: 'taxSalesMissingEquipments',
  },
];

const Totals = () => {
  const form = useFormContext<StoreTotalsTypes>();
  const ref = useRef<any>({});
  const allowedProdFeeCategories = form.watch('allowedProdFeeCategories');
  const expeditedFee = form.watch('storeExpeditedFees');
  const [isProductionDialogOpen, setIsProductionDialogOpen] =
    useState<boolean>(false);
  const [rowSelectionProduction, setRowSelectionProduction] =
    useState<RowSelectionState>({});

  const [isExpeditedFeeDialogOpen, setIsExpeditedFeeDialogOpen] =
    useState<boolean>(false);
  const [flag, setFlag] = useState(true);

  const defaultDamageWaiver = form.watch('defaultDamageWaiver');
  const damageWaiverIncrease = form.watch('damageWaiverIncrease');
  const [showConfirmation, setShowConfirmation] = useState(false);

  const handleBlur = (
    fieldName: 'defaultDamageWaiver' | 'damageWaiverIncrease'
  ) => {
    const currentValue = form.getValues(fieldName);
    let previousValue;
    if (fieldName === 'defaultDamageWaiver') {
      previousValue = ref.current.defaultDamageWaiver;
    } else if (fieldName === 'damageWaiverIncrease') {
      previousValue = ref.current.damageWaiverIncrease;
    }
    if (Number(currentValue) !== Number(previousValue)) {
      setShowConfirmation(true);
    }
    ref.current[fieldName] = currentValue;
  };
  useEffect(() => {
    if (flag) {
      ref.current.defaultDamageWaiver = defaultDamageWaiver;
      ref.current.damageWaiverIncrease = damageWaiverIncrease;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [damageWaiverIncrease, defaultDamageWaiver]);

  // toggle the Production Fee Configure
  const toggleProduction = useCallback(() => {
    setIsProductionDialogOpen((prev) => !prev);
  }, []);

  // toggle the  Expedited Fee Configure
  const toggleExpeditedFee = useCallback(() => {
    setIsExpeditedFeeDialogOpen((prev) => !prev);
  }, []);

  // Categories List
  const { options: categoryList, optionLoading: categoryLoading } =
    useOptionList({
      url: isProductionDialogOpen ? CATEGORY_API_ROUTES.ALL : '',
      labelKey: 'catDesc',
      valueKey: 'id',
      sortBy: 'catDesc',
    });

  // New handlers for "Ok" and "Cancel" buttons
  const handleOkProduction = useCallback(() => {
    const allowedProdFeeCategoriesId = Object.keys(rowSelectionProduction)?.map(
      (key) => Number(key)
    );
    form.setValue('allowedProdFeeCategories', allowedProdFeeCategoriesId);
    toggleProduction();
  }, [form, rowSelectionProduction, toggleProduction]);

  // Handle Expedited Fee  ok
  const handleOKExpeditedFee = useCallback(
    (value: StoreTotalExpeditedFeeType[]) => {
      form.setValue('storeExpeditedFees', value || []);

      toggleExpeditedFee();
    },
    [form, toggleExpeditedFee]
  );

  useEffect(() => {
    if (isProductionDialogOpen) {
      setRowSelectionProduction(
        allowedProdFeeCategories?.reduce(
          (acc: { [x: string]: boolean }, curr: string | number) => {
            acc[curr] = true;
            return acc;
          },
          {}
        )
      );
    }
  }, [isProductionDialogOpen, allowedProdFeeCategories]);

  return (
    <div className="flex flex-col gap-6 w-full h-full">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <NumberInputField
          form={form}
          name="defaultDamageWaiver"
          label="Default Damage Waiver"
          placeholder="__.__%"
          maxLength={7}
          suffix="%"
          onBlur={() => handleBlur('defaultDamageWaiver')}
          onValueChange={() => setFlag(false)}
        />
        <NumberInputField
          form={form}
          name="damageWaiverIncrease"
          label="Damage Waiver Increase For Will Calls"
          placeholder="__.__%"
          maxLength={7}
          suffix="%"
          onBlur={() => handleBlur('damageWaiverIncrease')}
          onValueChange={() => setFlag(false)}
        />

        <NumberInputField
          form={form}
          name="itemFuelSurcharge"
          label="Fuel Surcharge (Based on Items)"
          placeholder="___.__%"
          suffix="%"
          disabled
        />

        <NumberInputField
          form={form}
          name="deliveryFuelSurcharge"
          label="Fuel Surcharge (Based on Delivery)"
          placeholder="__.__%"
          suffix="%"
          maxLength={7}
        />
        <NumberInputField
          form={form}
          name="minimumFuelSurcharge"
          label="Minimum Fuel Surcharge"
          placeholder="$__.__"
          prefix="$"
          maxLength={7}
        />

        <DatePicker
          form={form}
          name="fuelSurchargeStartDate"
          label="Fuel Surcharge Start Date"
          placeholder="Fuel Surcharge Start Date"
          format="YYYY-MM-DDTHH:mm:ss[Z]"
        />
        <NumberInputField
          form={form}
          name="productionFee"
          label="Production Fee"
          placeholder="__.__%"
          suffix="%"
          maxLength={7}
          className="w-auto"
        />

        <AppButton
          type="button"
          onClick={toggleProduction}
          label="Production Fee Configure"
          className="md:mt-8 md:w-fit"
        />
        <DatePicker
          form={form}
          name="productionFeeStartDate"
          label="Production Fee Start Date"
          placeholder="Production Fee Start Date"
          format="YYYY-MM-DDTHH:mm:ss[Z]"
        />
        <NumberInputField
          form={form}
          name="requestedDeposit"
          label="Required Deposit"
          placeholder="__.__%"
          suffix="%"
          maxLength={7}
        />
        <DatePicker
          form={form}
          name="expeditedFeeStartDate"
          label="Expedited Fee Start Date"
          placeholder="Expedited Fee Start Date"
          format="YYYY-MM-DDTHH:mm:ss[Z]"
        />
        <AppButton
          type="button"
          onClick={() => setIsExpeditedFeeDialogOpen(true)}
          label="Expedited Fee Configure"
          className="md:mt-8 md:w-fit"
        />

        {switchFields?.map(({ label, name, disabled }) => (
          <SwitchField
            key={name}
            label={label}
            form={form}
            name={name}
            className="mt-8"
            disabled={disabled}
          />
        ))}
      </div>
      <Selector
        title="Select Categories"
        data={categoryList}
        columns={[
          {
            accessorKey: 'label',
            header: 'Category',
            size: 240,
            enableSorting: true,
          },
        ]}
        isLoading={categoryLoading}
        open={isProductionDialogOpen}
        rowSelection={rowSelectionProduction}
        setRowSelection={setRowSelectionProduction}
        onOkClick={handleOkProduction}
        onOpenChange={toggleProduction}
        onCancelClick={toggleProduction}
        bindingKey="value"
        enableMultiRowSelection
        enableRowSelection
      />
      <CustomDialog
        onOpenChange={toggleExpeditedFee}
        open={isExpeditedFeeDialogOpen}
        title={'Expedited Fee'}
        description=""
        className={cn('max-h-[80%] overflow-auto min-w-[60%] md:min-w-[35%]')}
      >
        <TotalExpeditedFee
          expeditedFee={expeditedFee}
          onOkClick={handleOKExpeditedFee}
          onCancel={toggleExpeditedFee}
        />
      </CustomDialog>
      {/* Confirmation Modal */}

      <AppConfirmationModal
        title="Confirmation"
        description={
          <span className="text-sm text-gray-700">
            When editing any current (non-posted) order, this new damage waiver
            will be automatically applied if a charge hasn't been manually
            entered. If you would like to flag your current orders to not
            calculate with this new damage waiver, please contact Party Track
            support.
          </span>
        }
        open={showConfirmation}
        handleSubmit={() => setShowConfirmation(false)} // Close modal when confirmed
        submitLabel="Ok"
      />
    </div>
  );
};

export default Totals;
