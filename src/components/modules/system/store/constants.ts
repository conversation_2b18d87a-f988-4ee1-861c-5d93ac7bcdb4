import { STORE_OPTIONS_API_ROUTES } from '@/constants/api-constants';

// Define the structure of the StoreOPtionsTabKeyList object
export const StoreOPtionsTabKeyList: {
  [key: string]: string[];
} = {
  information: [
    'businessName',
    'locationName',
    'address1',
    'address2',
    'city',
    'stateId',
    'zipCode',
    'countryId',
    'phone',
    'website',
    'id',
  ],
  'copying-orders': [
    'kitsToUse',
    'priceToUse',
    'timeFrameLimit',
    'refetFuelCharge',
    'storeLocationId',
  ],
  'date-calculation': [
    'allowManuallyEnterDate',
    'allowClearExistingDate',
    'shipDatesCalculation',
    'allowArrivalDate',
    'allowAffectPickupDate',
    'maxDaysBeforeDelivery',
    'allowUpdateDate',
    'allowEditingShipDate',
    'storeLocationId',
  ],
  'delivery-charges': [
    'chargeCalculation',
    'defaultCharges',
    'defaultWebRateShip',
    'defaultWebRateReturn',
    'calculateDcCharge',
    'useNoDeliveryCharge',
    'displayDcWarning',
    'dcIncreasePercent',
    'requireDcConfirmation',
    'storeLocationId',
  ],
  'email-settings': [
    'emailFrom',
    'faxService',
    'allowInvoicePackagingList',
    'addEventDescription',
    'addSmtp',
    'sendEsignDocs',
    'storeLocationId',
  ],
  forms: [
    'purchaseOrder',
    'faxPurchaseOrder',
    'poRequest',
    'faxPoRequest',
    'equipmentTransfer',
    'faxEquipmentTransfer',
    'arStatement',
    'faxArStatement',
    'arAdjustment',
    'faxArAdjustment',
    'arInquiry',
    'deliveryLocationDirections',
    'creditCardReceipt',
    'htmlConfirmation',
    'htmlInvoice',
    'htmlStatements',
    'storeLocationId',
  ],
  'manager-approval': [
    'deliveryCharges',
    'discountCharges',
    'priceChanges',
    'taxExempt',
    'paymentTerms',
    'storeLocationId',
  ],
  'multi-location': [
    'defaultStoreLocation',
    'checkInventoryFor',
    'selectSerializedItemsAcrossLocations',
    'selectTaxCodeByOrderLocation',
    'storeLocationId',
  ],
  'order-item-settings': [
    'itemDeliveryLocations',
    'includeQuotesInInventoryChecks',
    'editItemInfo',
    'showItemTypeColumn',
    'showSubRentalColumn',
    'zeroOutQty',
    'combineLikeItems',
    'packageComponentsShowPrice',
    'retrieveSerialToFlip',
    'includeSerialWhenOpeningOverBookedItem',
    'surgePricing',
    'storeLocationId',
    'showSerialNumberColumn',
  ],
  passwords: [
    'deleteOrder',
    'unDeleteOrder',
    'deletePayment',
    'employee',
    'orderComp',
    'approvalShipment',
    'reports',
    'unpost',
    'viewCreditCard',
    'rfidTag',
    'timecard',
    'dataProcesses',
    'storeLocationId',
  ],
  payments: [
    'paymentType',
    'validateCreditCard',
    'declinePartialCreditCardApprovals',
    'itemDeliveryLocations',
    'creditCardConvenienceFees',
    'debitCardConvenienceFees',
    'achConvenienceFees',
    'allowRefundingConvenienceFees',
    'monthsBeforeOnlinePaymentLinkExpire',
    'allowNonProcessedCreditCardPayments',
    'storeLocationId',
  ],
  'print-settings': [
    'allowLocalPackingLists',
    'defaultPrintingToPreviousSelection',
    'optionallyInvoiceOrdersPrintedToNonInvoicePrinters',
    'printPackagingList',
    'includeRevisionNumberInPDF',
    'openPDFAfterCreated',
    'itemPrintSequence',
    'kitComponentPrintSeq',
    'batchInvoicePrintOptions',
    'itemPicsOnForms',
    'allowPrintingToIncludeLockedOrders',
    'maxDaysBeforeInvoicingWarning',
    'processesPrintsortingSelection',
    'storeLocationId',
  ],
  totals: [
    'fuelSurchargeStartDate',
    'productionFeeStartDate',
    'expeditedFeeStartDate',
    'defaultDamageWaiver',
    'damageWaiverIncrease',
    'itemFuelSurcharge',
    'deliveryFuelSurcharge',
    'minimumFuelSurcharge',
    'productionFee',
    'requestedDeposit',
    'autoApplyDamageWaiverCharges',
    'prodFeesAppliesToCall',
    'customerLevelDM',
    'customerLevelFuelSurcharge',
    'requestedDepositItemSubTotal',
    'taxSalesMissingEquipments',
    'storeLocationId',
    'allowedProdFeeCategories',
    'storeExpeditedFees',
  ],
  warehouse: [
    'shippingCompanies',
    'returnCompany',
    'shippingManager',
    'showLastPickedOrders',
    'showTruckNames',
    'showOrderListDefault',
    'splitOrders',
    'usingWareHouseStatus',
    'removeItineraryOrders',
    'storeLocationId',
  ],
  'misc-settings': [
    'serviceChargeAnnualPercentage',
    'payRollStartDay',
    'inventoryAdjustmentType',
    'networkPath',
    'quickBooks',
    'customCleanUpdays',
    'showQtyAvailable',
    'salesTaxReportByCompletedDate',
    'salesTaxFinalization',
    'refreshCustomerList',
    'refreshOrderList',
    'customerNameSearchDefault',
    'locationSearchDefault',
    'sortCategoryListByName',
    'reportSecurity',
    'listOnlinePayments',
    'itemOnWebDefault',
    'showDriverNames',
    'startDateOfUse',
    'storeLocationId',
    'storeCleanupDaysConfig',
  ],
  'misc-order-settings': [
    'defaultOrderType',
    'warnOverdueCustomers',
    'jobInfoAlert',
    'allowCustDiscToEqOrd',
    'updateRevision',
    'newCustContactInfo',
    'defaultDeliveryType',
    'defaultDeliveryTypeCall',
    'defaultSalesTaxCode',
    'rate',
    'defaultTaxCodeForCallOrder',
    'creditLimitWarning',
    'associateSaveShipInfo',
    'copyDeliveryTypeInfo',
    'promptOnSerialId',
    'lockSalePersonField',
    'storeLocationId',
  ],
  'misc-customer-settings': [
    'saveCustomPriceBy',
    'usePricing',
    'creditHoldMessage',
    'storeLocationId',
  ],
  rfid: [
    'rfid',
    'rfidUpdatesPickupDate',
    'rfidReturnAlerts',
    'storeLocationId',
  ],
};

// API route mapping for each tab
export const ADD_EDIT_STORE_OPTIONS_API_ROUTES_MAP = {
  information: STORE_OPTIONS_API_ROUTES.CREATE,
  'copying-orders': STORE_OPTIONS_API_ROUTES.COPYING_ORDER,
  'date-calculation': STORE_OPTIONS_API_ROUTES.DATE_CALCULATIONS,
  'delivery-charges': STORE_OPTIONS_API_ROUTES.DELIVERY_CHARGES,
  'email-settings': STORE_OPTIONS_API_ROUTES.EMAIL_SETTINGS,
  forms: STORE_OPTIONS_API_ROUTES.FORMS,
  'manager-approval': STORE_OPTIONS_API_ROUTES.MANAGER_APPROVAL,
  'multi-location': STORE_OPTIONS_API_ROUTES.MULTI_LOCATION,
  'order-item-settings': STORE_OPTIONS_API_ROUTES.ORDER_ITEM_SETTINGS,
  passwords: STORE_OPTIONS_API_ROUTES.PASSWORDS,
  payments: STORE_OPTIONS_API_ROUTES.PAYMENTS,
  'print-settings': STORE_OPTIONS_API_ROUTES.PRINT_SETTING,
  totals: STORE_OPTIONS_API_ROUTES.TOTALS,
  warehouse: STORE_OPTIONS_API_ROUTES.WAREHOUSE,
  'misc-settings': STORE_OPTIONS_API_ROUTES.MISC_SETTINGS,
  'misc-order-settings': STORE_OPTIONS_API_ROUTES.MISC_ORDER_SETTINGS,
  'misc-customer-settings': STORE_OPTIONS_API_ROUTES.MISC_CUSTOMER_SETTINGS,
  rfid: STORE_OPTIONS_API_ROUTES.RFID_SETTINGS,
};

// EventDays
export const EventDays = [
  {
    label: 'Sunday',
    name: 'sunday',
    placeholder: '__',
  },
  {
    label: 'Monday',
    name: 'monday',
    placeholder: '__',
  },
  {
    label: 'Tuesday',
    name: 'tuesday',
    placeholder: '__',
  },
  {
    label: 'Wednesday',
    name: 'wednesday',
    placeholder: '__',
  },
  {
    label: 'Thursday',
    name: 'thursday',
    placeholder: '__',
  },
  {
    label: 'Friday',
    name: 'friday',
    placeholder: '__',
  },
  {
    label: 'Saturday',
    name: 'saturday',
    placeholder: '__',
  },
];

export const generateStoreOptionsDefaultValues = (storeOptionsValue: any) => {
  const {
    storeCopyingOrder,
    storeDateCalculation,
    storeDeliveryCharges,
    storeEmailSetting,
    storeForm,
    storeManagerApproval,
    storeMultiLocation,
    storeOrderItemSetting,
    storePassword,
    storePayment,
    storePrintSetting,
    storeTotal,
    storeWareHouse,
    storeMiscSetting,
    storeMiscOrderSetting,
    storeMiscCustomerSetting,
    storeRfid,
  } = storeOptionsValue;

  return {
    // Spread the storeOptionsValue, and set default values for missing properties
    ...storeOptionsValue,
    id: storeOptionsValue?.id,
    storeLocationId: storeOptionsValue?.id,
    phone: storeOptionsValue?.phone || '',
    website: storeOptionsValue?.website || '',
    businessName: storeOptionsValue?.businessName || '',
    locationName: storeOptionsValue?.locationName || '',
    address1: storeOptionsValue?.address1 || '',
    address2: storeOptionsValue?.address2 || '',
    city: storeOptionsValue?.city || '',
    zipCode: storeOptionsValue?.zipCode || '',
    countryId: storeOptionsValue?.countryId || 1,
    stateId: storeOptionsValue?.stateId || '',

    ...storeCopyingOrder,
    ...storeDateCalculation,
    ...storeDeliveryCharges,
    dcIncreasePercent: storeDeliveryCharges?.dcIncreasePercent || '0',
    ...storeEmailSetting,
    ...storeForm,
    ...storeManagerApproval,
    ...storeMultiLocation,
    ...storeOrderItemSetting,
    ...storePassword,
    ...storePayment,
    creditCardConvenienceFees: storePayment?.creditCardConvenienceFees || '0',
    debitCardConvenienceFees: storePayment?.debitCardConvenienceFees || '0',
    achConvenienceFees: storePayment?.achConvenienceFees || '0',
    ...storePrintSetting,
    ...storeTotal,
    defaultDamageWaiver: storeTotal?.defaultDamageWaiver || '0',
    damageWaiverIncrease: storeTotal?.damageWaiverIncrease || '0',
    itemFuelSurcharge: storeTotal?.itemFuelSurcharge || '0',
    deliveryFuelSurcharge: storeTotal?.deliveryFuelSurcharge || '0',
    minimumFuelSurcharge: storeTotal?.minimumFuelSurcharge || '0',
    productionFee: storeTotal?.productionFee || '0',
    expeditedFee: storeTotal?.expeditedFee || [],
    ...storeWareHouse,
    ...storeMiscSetting,
    serviceChargeAnnualPercentage:
      storeMiscSetting?.serviceChargeAnnualPercentage || '0',
    ...storeMiscOrderSetting,
    ...storeMiscCustomerSetting,
    ...storeRfid,
  };
};
