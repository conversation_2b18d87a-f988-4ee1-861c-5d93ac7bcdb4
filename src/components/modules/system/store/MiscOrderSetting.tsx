import SwitchField from '@/components/common/switch';
import NumberIn<PERSON>Field from '@/components/forms/number-input-field';
import SelectDropDown from '@/components/forms/select-dropdown';
import {
  DELIVERY_TYPE_API_ROUTES,
  SALES_TAX_CODE_API_ROUTES,
} from '@/constants/api-constants';
import useOptionList from '@/hooks/useOptionList';
import { convertToFloat } from '@/lib/utils';
import { useGetEnumsListQuery } from '@/redux/features/enums-api/enums-api';
import { StoreMiscOrderSettings } from '@/types/store.types';
import { useCallback } from 'react';
import { Path, useFormContext } from 'react-hook-form';

// Array of SwitchField configurations with TypeScript types
type SwitchFieldConfig<T> = {
  label: string;
  name: Path<T>;
};

// Array of SwitchField configurations
const switchFields: SwitchFieldConfig<StoreMiscOrderSettings>[] = [
  { label: 'Warn on Overdue Customers', name: 'warnOverdueCustomers' },
  { label: 'Job Info Alert', name: 'jobInfoAlert' },
  {
    label: 'Apply Customer Discount to Missing Equipment Orders',
    name: 'allowCustDiscToEqOrd',
  },
  {
    label: 'Update Revision # When Order Notes Change',
    name: 'updateRevision',
  },
  {
    label: "Put Contact into 'Ordered By' on New Customer",
    name: 'newCustContactInfo',
  },
  {
    label: 'Use Default Tax Code for Will Call Orders',
    name: 'defaultTaxCodeForCallOrder',
  },
  { label: 'Credit Limit Warning', name: 'creditLimitWarning' },
  {
    label: 'Associate Saved Ship Info with Order Customer',
    name: 'associateSaveShipInfo',
  },
  {
    label: 'Copy Delivery Type into Del/PU Info',
    name: 'copyDeliveryTypeInfo',
  },
  { label: 'Always Prompt to Update Serial #s', name: 'promptOnSerialId' },
  { label: 'Lock Salesperson Fields', name: 'lockSalePersonField' },
];

const MiscOrderSetting = () => {
  const form = useFormContext<StoreMiscOrderSettings>();

  const { data: defaultOrderType, isLoading: isdefaultOrderTypeLoading } =
    useGetEnumsListQuery({
      name: 'DefaultOrderType',
    });

  // Default Delivery Type
  const { options: deliveryTypeList, optionLoading: deliveryTypeListLoading } =
    useOptionList({
      url: DELIVERY_TYPE_API_ROUTES?.ALL,
      labelKey: 'name',
      valueKey: 'deliverytype_id',
    });

  // Default Sales Tax Code
  const { options: salesTaxCodeList, optionLoading: salesTaxCodeListLoading } =
    useOptionList({
      url: SALES_TAX_CODE_API_ROUTES?.ALL,
      valueKey: 'salestaxcode_id',
      labelKey: 'salestaxcode',
      descKey: 'salestaxrate',
    });

  const handleOnchangeSaleTaxCode = useCallback(
    (value: string) => {
      form.setValue('defaultSalesTaxCode', value);
      const salestaxrate = salesTaxCodeList?.find(
        (item: any) => item?.value === value
      )?.descValue;
      const rate = convertToFloat({
        value: Number(salestaxrate),
        decimalPlaces: 3,
      });
      form.setValue('rate', rate);
    },
    [form, salesTaxCodeList]
  );

  return (
    <div className="flex flex-col gap-6 justify-between h-full">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <SelectDropDown
          form={form}
          optionsList={defaultOrderType?.data ?? []}
          label="Default Order Type"
          name="defaultOrderType"
          placeholder="Default Order Type"
          isLoading={isdefaultOrderTypeLoading}
        />
        <SelectDropDown
          form={form}
          optionsList={deliveryTypeList || []}
          label="Default Delivery Type"
          name="defaultDeliveryType"
          placeholder="Default Delivery Type"
          isLoading={deliveryTypeListLoading}
        />
        <SelectDropDown
          form={form}
          optionsList={deliveryTypeList || []}
          label="Default Delivery Type (Will Call)"
          name="defaultDeliveryTypeCall"
          placeholder="Default Delivery Type (Will Call)"
          isLoading={deliveryTypeListLoading}
        />
        <div className="md:col-span-2 2xl:col-span-1 md:w-3/4 2xl:w-full grid grid-cols-1 md:grid-cols-2 gap-4">
          <SelectDropDown
            form={form}
            optionsList={salesTaxCodeList || []}
            label="Default Sales Tax Code"
            name="defaultSalesTaxCode"
            placeholder="Default Sales Tax Code"
            isLoading={salesTaxCodeListLoading}
            onChange={handleOnchangeSaleTaxCode}
          />
          <NumberInputField
            form={form}
            name="rate"
            label="Rate"
            placeholder="___.__%"
            disabled
            suffix="%"
          />
        </div>
        {/* Map through the switch fields array to render them */}
        {switchFields?.map((field) => (
          <div key={field.name} className="mt-4">
            <SwitchField label={field.label} form={form} name={field.name} />
          </div>
        ))}
      </div>
    </div>
  );
};

export default MiscOrderSetting;
