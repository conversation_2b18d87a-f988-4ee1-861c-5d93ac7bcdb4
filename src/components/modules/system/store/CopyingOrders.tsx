import SwitchField from '@/components/common/switch';
import SelectDropDown from '@/components/forms/select-dropdown';
import { useGetEnumsListQuery } from '@/redux/features/enums-api/enums-api';
import { StoreCopyingOrdersTypes } from '@/types/store.types';
import { useFormContext } from 'react-hook-form';

const CopyingOrders = () => {
  const form = useFormContext<StoreCopyingOrdersTypes>();

  const { data: kitToUse, isLoading: isKitToUseLoading } = useGetEnumsListQuery(
    {
      name: 'KitsToUse',
    }
  );

  const { data: priceToUse, isLoading: isPriceToUseLoading } =
    useGetEnumsListQuery({
      name: 'PriceToUse',
    });

  const { data: timeFrameLimit, isLoading: isTimeFrameLimitLoading } =
    useGetEnumsListQuery({
      name: 'TimeFrameLimit',
    });

  return (
    <div className="flex flex-col gap-6 justify-between h-full">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <SelectDropDown
          form={form}
          optionsList={kitToUse?.data ?? []}
          label="Kits of Use"
          name="kitsToUse"
          placeholder="Select Kits of Use"
          isLoading={isKitToUseLoading}
        />
        <SelectDropDown
          form={form}
          optionsList={priceToUse?.data ?? []}
          label="Price to Use"
          name="priceToUse"
          placeholder="Select Price to Use"
          isLoading={isPriceToUseLoading}
        />
        <SelectDropDown
          form={form}
          optionsList={timeFrameLimit?.data ?? []}
          label="Timeframe Limit"
          name="timeFrameLimit"
          placeholder="Select Timeframe Limit"
          isLoading={isTimeFrameLimitLoading}
        />
        <SwitchField
          name="refetFuelCharge"
          form={form}
          label="Reset Fuel Charge"
          className="mt-10"
        />
      </div>
    </div>
  );
};
export default CopyingOrders;
