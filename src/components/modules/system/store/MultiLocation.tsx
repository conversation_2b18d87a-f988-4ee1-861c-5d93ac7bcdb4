import SwitchField from '@/components/common/switch';
import SelectDropDown from '@/components/forms/select-dropdown';
import { useGetEnumsListQuery } from '@/redux/features/enums-api/enums-api';
import { StoreMultiLocationTypes } from '@/types/store.types';
import { useFormContext } from 'react-hook-form';

const switchFields = [
  {
    label: 'Select Serialized Items Across Locations',
    name: 'selectSerializedItemsAcrossLocations',
  },
  {
    label: 'Select Tax Code By Order Location',
    name: 'selectTaxCodeByOrderLocation',
  },
];
const MultiLocation = () => {
  const form = useFormContext<StoreMultiLocationTypes>();

  // Default Store Location For Orders
  const {
    data: defaultStoreLocationList,
    isLoading: defaultStoreLocationLoading,
  } = useGetEnumsListQuery({
    name: 'DefaultStoreLocationOrder',
  });

  // Check Inventry For
  const { data: checkInventoryList, isLoading: checkInventoryLoading } =
    useGetEnumsListQuery({
      name: 'CheckInventory',
    });

  return (
    <div className="flex flex-col gap-6 w-full h-full">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <SelectDropDown
          form={form}
          optionsList={defaultStoreLocationList?.data || []}
          label="Default Store Location for Orders"
          name="defaultStoreLocation"
          placeholder="Default Store Location for Orders"
          isLoading={defaultStoreLocationLoading}
        />
        <SelectDropDown
          form={form}
          optionsList={checkInventoryList?.data || []}
          label="Check Inventory for"
          name="checkInventoryFor"
          placeholder="Check Inventory for"
          isLoading={checkInventoryLoading}
        />
        {switchFields?.map((field, index) => (
          <SwitchField
            key={`${field?.label}-${index}`}
            label={field?.label}
            name={field?.name as any}
            form={form}
            className="mt-4"
          />
        ))}
      </div>
    </div>
  );
};

export default MultiLocation;
