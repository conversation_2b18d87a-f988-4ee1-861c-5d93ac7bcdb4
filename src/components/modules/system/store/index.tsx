import AppButton from '@/components/common/app-button';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import AppDataTable from '@/components/common/app-data-table';
import ActionColumnMenu from '@/components/common/data-tables/ActionColumn';
import EditIcon from '@/assets/icons/EditIcon';
import { STORE_OPTIONS_API_ROUTES } from '@/constants/api-constants';
import { ROUTES } from '@/constants/routes-constants';
import { formatPhoneNumber } from '@/lib/utils';
import { useCloneStoreMutation } from '@/redux/features/store/store.api';
import { StoreOptionsTypes } from '@/types/store.types';
import { ColumnDef } from '@tanstack/react-table';
import { PlusIcon } from 'lucide-react';
import { useCallback, useMemo, useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';

const StoreOptions = () => {
  const navigate = useNavigate();
  const [refresh, setRefresh] = useState<boolean>(false);
  const [cloneState, setCloneState] = useState<{ clone: boolean; id: number }>({
    clone: false,
    id: 0,
  });

  const toggleClone = useCallback((id?: number) => {
    setCloneState((prev) => ({ clone: !prev.clone, id: id || 0 }));
  }, []);

  // clon store
  const [cloneStore, { isLoading }] = useCloneStoreMutation();

  const handleCopy = useCallback(async () => {
    try {
      await cloneStore(cloneState?.id).unwrap(); // make sure cloneStore returns a promise
      toggleClone();
      // If successful, trigger the refresh state logic
      setRefresh(true);

      // After a short delay, turn off the refresh state
      setTimeout(() => {
        setRefresh(false);
      }, 100);
    } catch (err) {}
  }, [cloneStore, cloneState?.id, toggleClone]);

  const columns: ColumnDef<StoreOptionsTypes>[] = useMemo(
    () => [
      {
        accessorKey: 'locationName',
        header: 'Location Name',
        size: 200,
        enableSorting: true,
      },
      {
        accessorKey: 'address1',
        header: 'Address Line 1',
        size: 130,
        enableSorting: true,
      },
      {
        accessorKey: 'address2',
        header: 'Address Line 2',
        size: 150,
        enableSorting: true,
      },
      {
        accessorKey: 'city',
        header: 'City',
        size: 130,
        enableSorting: true,
      },
      {
        accessorKey: 'stateId',
        header: 'State',
        size: 130,
        enableSorting: true,
        cell: ({ row }) => row.original.stateName,
      },
      {
        accessorKey: 'zipCode',
        header: 'Zip',
        size: 130,
        enableSorting: true,
        cell: ({ row }) => row.original.zipCode,
      },
      {
        accessorKey: 'countryName',
        header: 'Country',
        size: 130,
        enableSorting: true,
        cell: ({ row }) => row.original.countryName,
      },
      {
        accessorKey: 'phone',
        header: 'Phone',
        size: 130,
        enableSorting: true,
        cell: (info) => formatPhoneNumber(info.getValue() as string),
      },
      {
        accessorKey: 'website',
        header: 'Website',
        size: 130,
        enableSorting: true,
        cell: ({ row }) => {
          let websiteUrl = row.original.website;

          // Check if the URL is missing the protocol (http:// or https://)
          if (websiteUrl && !/^https?:\/\//i.test(websiteUrl)) {
            websiteUrl = 'https://' + websiteUrl; // Add https:// by default
          } else if (websiteUrl && !/^http?:\/\//i.test(websiteUrl)) {
            websiteUrl = 'http://' + websiteUrl; // Add https:// by default
          }

          return (
            <a
              href={websiteUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="text-[#5f26c9] hover:text-[#4c1d9d] transition duration-200 ease-in-out"
            >
              {row.original.website}
            </a>
          );
        },
      },
      {
        size: 110,
        id: 'action',
        header: 'Actions',
        cell: ({ row }) => {
          const storeOptionId = row?.original?.id;
          return (
            <ActionColumnMenu
              customEdit={
                <Link
                  to={`${ROUTES?.EDIT_STORE_OPTIONS}?id=${storeOptionId}&tab=information`}
                  className="flex items-center justify-center gap-2 border border-text-tertiary rounded-md px-3 py-1"
                >
                  <EditIcon /> Edit details
                </Link>
              }
              onCopy={() => toggleClone(row.original?.id)}
            />
          );
        },
      },
    ],
    [toggleClone]
  );

  const handleNewStoreOptions = useCallback(
    () => navigate(`${ROUTES.ADD_STORE_OPTIONS}?tab=information`),
    [navigate]
  );

  // Custom toolbar component
  const CustomToolbar = (
    <AppButton
      icon={PlusIcon}
      className="w-[200px]"
      label="New Store Location"
      onClick={handleNewStoreOptions}
    />
  );

  return (
    <div className="p-6">
      <AppDataTable
        url={STORE_OPTIONS_API_ROUTES.ALL}
        customToolBar={CustomToolbar}
        columns={columns}
        heading="Store Options"
        enableSearch={true}
        enablePagination={true}
        tableClassName="max-h-[580px] overflow-auto"
        refreshList={refresh}
        searchKey={'locationName'}
      />
      <AppConfirmationModal
        title="Confirmation"
        description={
          <div>
            Are you sure you want to clone this store? Once a store location is
            created, it cannot be deleted and will impact all associated orders.
            Please proceed carefully.
          </div>
        }
        open={cloneState?.clone}
        onOpenChange={toggleClone}
        handleCancel={toggleClone}
        handleSubmit={handleCopy}
        isLoading={isLoading}
      />
    </div>
  );
};

export default StoreOptions;
