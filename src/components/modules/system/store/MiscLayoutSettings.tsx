import MiscSettings from '@/components/modules/system/store/MiscSettings';
import MiscCustomerSetting from '@/components/modules/system/store/MiscCustomerSetting';
import MiscOrderSetting from '@/components/modules/system/store/MiscOrderSetting';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';

const MiscLayoutSettings = () => {
  return (
    <div className="flex flex-row gap-8 flex-1 p-3">
      <div className="flex-1  grid-cols-1 md:grid-cols-2 gap-4 ">
        <Accordion type="single" className="flex flex-col space-y-4">
          <Accordion
            className="AccordionRoot"
            type="single"
            defaultValue="item-1"
            collapsible
          >
            <AccordionItem value="item-1" className="border-0 ">
              <AccordionTrigger className="bg-grayScale-10 rounded-md px-3 hover:no-underline">
                Misc. Customer Settings
              </AccordionTrigger>
              <AccordionContent className="py-4 px-1">
                <MiscCustomerSetting />
              </AccordionContent>
            </AccordionItem>
          </Accordion>
          <Accordion
            className="AccordionRoot"
            type="single"
            defaultValue="item-2"
            collapsible
          >
            <AccordionItem value="item-2" className="border-0">
              <AccordionTrigger className="bg-grayScale-10 rounded-md px-3 hover:no-underline">
                Misc. Order Settings
              </AccordionTrigger>
              <AccordionContent className="py-4 px-1">
                <MiscOrderSetting />
              </AccordionContent>
            </AccordionItem>
          </Accordion>
          <Accordion
            className="AccordionRoot"
            type="single"
            defaultValue="item-3"
            collapsible
          >
            <AccordionItem value="item-3" className="border-0">
              <AccordionTrigger className="bg-grayScale-10 rounded-md px-3 hover:no-underline">
                Misc. Settings
              </AccordionTrigger>
              <AccordionContent className="py-4 px-1">
                <MiscSettings />
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </Accordion>
      </div>
    </div>
  );
};

export default MiscLayoutSettings;
