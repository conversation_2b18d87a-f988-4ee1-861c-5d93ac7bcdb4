import AppButton from '@/components/common/app-button';
import CustomDialog from '@/components/common/dialog';
import SwitchField from '@/components/common/switch';
import DatePicker from '@/components/forms/date-picker';
import InputField from '@/components/forms/input-field';
import NumberInputField from '@/components/forms/number-input-field';
import SelectDropDown from '@/components/forms/select-dropdown';
import { cn } from '@/lib/utils';
import { useGetEnumsListQuery } from '@/redux/features/enums-api/enums-api';
import {
  CustomCleanupDaysType,
  StoreMiscSettings,
  SwitchFieldConfig,
} from '@/types/store.types';
import { useCallback, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { EventDays } from './constants';

const switchFields: SwitchFieldConfig<StoreMiscSettings>[] = [
  {
    label: 'Sales Tax Finalization',
    name: 'salesTaxFinalization',
    disabled: true,
  },
  {
    label: 'Sales Tax Report by Completed Date',
    name: 'salesTaxReportByCompletedDate',
  },
  {
    label: 'QuickBooks',
    name: 'quickBooks',
  },
  { label: 'Show Qty Available', name: 'showQtyAvailable' },
  {
    label: 'Automatically Refresh Customer List',
    name: 'refreshCustomerList',
  },
  { label: 'Automatically Refresh Order List', name: 'refreshOrderList' },
  { label: 'Sort Category List by Name', name: 'sortCategoryListByName' },
  {
    label: 'List Online Payments by Entered By',
    name: 'listOnlinePayments',
  },
  { label: 'Item on Web Default', name: 'itemOnWebDefault' },
  {
    label: 'Show Driver Names on Orders Screen',
    name: 'showDriverNames',
  },
];
const MiscSettings = () => {
  const [isCustomDialogOpen, setIsCustomDialogOpen] = useState<boolean>(false);

  const form = useFormContext<StoreMiscSettings>();

  const toggleConfigure = useCallback(() => {
    setIsCustomDialogOpen((prev) => !prev);
  }, []);

  const { data: payrollStartDay, isLoading: isPayrollStartDayLoading } =
    useGetEnumsListQuery({
      name: 'PayrollStartDay',
    });

  const {
    data: inventoryAdjustmentType,
    isLoading: isInventoryAdjustmentTypeLoading,
  } = useGetEnumsListQuery({
    name: 'InventoryAdjustmentType',
  });

  const { data: reportSecurity, isLoading: isReportSecurityLoading } =
    useGetEnumsListQuery({
      name: 'ReportSecurity',
    });

  // Search By
  const { data: searchByData, isLoading: searchByLoading } =
    useGetEnumsListQuery({
      name: 'SearchBy',
    });
  const searchByList = searchByData?.data;

  // handle the  Custom Cleanup Days
  const handleCancelCustomCleanupDays = useCallback(() => {
    toggleConfigure();
    form.reset();
  }, [form, toggleConfigure]);

  const handleOKCustomCleanupDays = useCallback(() => {
    const storeCleanupDaysConfig = form.watch('storeCleanupDaysConfig');
    const updatedData: CustomCleanupDaysType = Object.fromEntries(
      Object.entries(storeCleanupDaysConfig || {})
        ?.filter(([key]) =>
          [
            'sunday',
            'monday',
            'tuesday',
            'wednesday',
            'thursday',
            'friday',
            'saturday',
          ].includes(key)
        )
        ?.map(([key, value]) => [key, value !== '' ? value : null])
    ) as CustomCleanupDaysType;
    form.setValue('storeCleanupDaysConfig', updatedData || null);
    toggleConfigure();
  }, [form, toggleConfigure]);

  return (
    <div className="flex flex-col gap-6 w-full h-full">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <NumberInputField
          name="serviceChargeAnnualPercentage"
          form={form}
          suffix="%"
          maxLength={7}
          placeholder="___.__%"
          label="Service Charge Annual Percentage"
        />

        <SelectDropDown
          form={form}
          optionsList={payrollStartDay?.data ?? []}
          label="Payroll Start Day"
          name="payRollStartDay"
          placeholder="Payroll Start Day"
          isLoading={isPayrollStartDayLoading}
        />
        <SelectDropDown
          form={form}
          optionsList={inventoryAdjustmentType?.data ?? []}
          label="Inventory Adjustment Type"
          name="inventoryAdjustmentType"
          placeholder="Inventory Adjustment Type"
          isLoading={isInventoryAdjustmentTypeLoading}
        />
        <InputField
          form={form}
          label="Network Path"
          name="networkPath"
          placeholder="Network Path"
        />

        <SelectDropDown
          form={form}
          optionsList={searchByList}
          label="Customer Name Search Default"
          name="customerNameSearchDefault"
          placeholder="Customer Name Search Default"
          isLoading={searchByLoading}
        />
        <SelectDropDown
          form={form}
          optionsList={searchByList}
          label="Location Search Default"
          name="locationSearchDefault"
          placeholder="Location Search Default"
          isLoading={searchByLoading}
        />

        <SelectDropDown
          form={form}
          optionsList={reportSecurity?.data ?? []}
          label="Report Security"
          name="reportSecurity"
          placeholder="Report Security"
          isLoading={isReportSecurityLoading}
        />
        <DatePicker
          form={form}
          name="startDateOfUse"
          label="Start Date Of Use"
          placeholder="Start Date Of Use"
          className="w-full"
          format="YYYY-MM-DDTHH:mm:ss[Z]"
        />

        <div className="mt-8">
          <SwitchField
            label="Custom Cleanup Days"
            form={form}
            name="customCleanUpdays"
          />
        </div>
        <AppButton
          type="button"
          onClick={toggleConfigure}
          label="Configure"
          className="mt-6 w-fit"
        />

        {switchFields?.map(({ label, name, disabled }) => (
          <div className="mt-4" key={name}>
            <SwitchField
              label={label}
              form={form}
              name={name}
              disabled={disabled}
            />
          </div>
        ))}
      </div>
      <CustomDialog
        onOpenChange={handleCancelCustomCleanupDays}
        description=""
        open={isCustomDialogOpen}
        className={cn('max-h-[96%] overflow-auto')}
        title="Custom Cleanup Days"
      >
        <div className="px-6">
          {EventDays?.map((item, index) => (
            <div
              key={`${item?.label}-${index}`}
              className="grid grid-cols-2 gap-5 items-center mb-2"
            >
              <div>{item?.label}</div>
              <NumberInputField
                form={form}
                name={`storeCleanupDaysConfig.${item?.name}` as any}
                placeholder={item?.placeholder as string}
                maxLength={2}
              />
            </div>
          ))}

          <div className="border-t-2 grid grid-cols-2 gap-2 mt-3">
            <AppButton
              type="button"
              onClick={handleOKCustomCleanupDays}
              label="OK"
              className="mt-2"
            />
            <AppButton
              type="button"
              onClick={handleCancelCustomCleanupDays}
              label="Cancel"
              variant="neutral"
              className="mt-2"
            />
          </div>
        </div>
      </CustomDialog>
    </div>
  );
};

export default MiscSettings;
