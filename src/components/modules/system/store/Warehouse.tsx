import SwitchField from '@/components/common/switch';
import SelectDropDown from '@/components/forms/select-dropdown';
import { useGetEnumsListQuery } from '@/redux/features/enums-api/enums-api';
import { StoreWarehouse, SwitchFieldConfig } from '@/types/store.types';
import { useFormContext } from 'react-hook-form';
import useOptionList from '@/hooks/useOptionList';
import { SHIPPING_COMPAINES_API_ROUTES } from '@/constants/api-constants';

const switchFields: SwitchFieldConfig<StoreWarehouse>[] = [
  {
    label: 'Shipping Manager',
    name: 'shippingManager',
  },
  {
    label: 'Show Past 30 Days Non-Picked Up Orders',
    name: 'showLastPickedOrders',
  },
  {
    label: 'Show Truck Names on Itinerary Screens',
    name: 'showTruckNames',
  },
  {
    label: 'Split Orders/Sub-Rentals On Screen',
    name: 'splitOrders',
  },
  {
    label: 'Using Warehouse Status',
    name: 'usingWareHouseStatus',
  },
  {
    label: 'Remove Order from Itinerary on Date Change/Delete',
    name: 'removeItineraryOrders',
  },
];

const Warehouse = () => {
  const form = useFormContext<StoreWarehouse>();

  const {
    options: shippingCompaniesList,
    optionLoading: shippingCompaniesLoaidng,
  } = useOptionList({
    url: SHIPPING_COMPAINES_API_ROUTES.ALL,
    labelKey: 'name',
    valueKey: 'id',
    sortBy: 'name',
    filters: [{ field: 'type', operator: 'Equals', value: '1' }],
  });

  const {
    options: returnShippingCompaniesList,
    optionLoading: returnShippingCompaniesLoading,
  } = useOptionList({
    url: SHIPPING_COMPAINES_API_ROUTES.ALL,
    labelKey: 'name',
    valueKey: 'id',
    sortBy: 'name',
    filters: [{ field: 'type', operator: 'Equals', value: '2' }],
  });

  // Itinerary Order List Default
  const { data: itineraryOrderData, isLoading: itineraryOrderLoading } =
    useGetEnumsListQuery({
      name: 'ItineraryOrderList',
    });
  const itineraryOrderList = itineraryOrderData?.data || [];

  return (
    <div className="flex flex-col gap-6 w-full h-full">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <SelectDropDown
          form={form}
          label="Default Shipping Company for Shipping"
          name="shippingCompanies"
          placeholder="Default Shipping Company for Shipping"
          optionsList={shippingCompaniesList}
          isLoading={shippingCompaniesLoaidng}
        />
        <SelectDropDown
          form={form}
          optionsList={returnShippingCompaniesList}
          label="Default Shipping Company for Return"
          name="returnCompany"
          placeholder="Default Shipping Company for Return"
          isLoading={returnShippingCompaniesLoading}
        />
        <SelectDropDown
          form={form}
          optionsList={itineraryOrderList}
          label="Itinerary Order List Default"
          name="showOrderListDefault"
          placeholder="Itinerary Order List Default"
          isLoading={itineraryOrderLoading}
        />
        <div></div>

        {switchFields?.map(({ label, name, disabled }) => (
          <SwitchField
            key={name}
            label={label}
            form={form}
            name={name}
            className="mt-4"
            disabled={disabled}
          />
        ))}
      </div>
    </div>
  );
};

export default Warehouse;
