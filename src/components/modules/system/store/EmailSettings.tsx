import SwitchField from '@/components/common/switch';
import SelectDropDown from '@/components/forms/select-dropdown';
import { useGetEnumsListQuery } from '@/redux/features/enums-api/enums-api';
import {
  StoreEmailSettingsTypes,
  SwitchFieldConfig,
} from '@/types/store.types';
import { useFormContext } from 'react-hook-form';
const switchFields: SwitchFieldConfig<StoreEmailSettingsTypes>[] = [
  {
    label: 'E-mail/Fax Invoices and Packing Lists',
    name: 'allowInvoicePackagingList',
  },
  {
    label: 'Add Event Description to E-mail Subject Lines',
    name: 'addEventDescription',
  },
  {
    label: 'Add SMTP to E-mail',
    name: 'addSmtp',
  },
  {
    label: 'Send E-sign Docs from Individual Users',
    name: 'sendEsignDocs',
    disabled: true,
  },
];

const EmailSettings = () => {
  const form = useFormContext<StoreEmailSettingsTypes>();

  const { data: emailFrom, isLoading: isEmailFromLoading } =
    useGetEnumsListQuery({
      name: 'EmailFrom',
    });

  const { data: emailFaxService, isLoading: isEmailFaxServiceLoading } =
    useGetEnumsListQuery({
      name: 'EmailFaxService',
    });

  return (
    <div className="flex flex-col gap-6 justify-between h-full">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <SelectDropDown
          form={form}
          optionsList={emailFrom?.data ?? []}
          label="Use E-mail Addresses From"
          name="emailFrom"
          placeholder="Select E-mail Addresses"
          isLoading={isEmailFromLoading}
        />
        <SelectDropDown
          form={form}
          optionsList={emailFaxService?.data ?? []}
          label="E-mail Fax Service"
          name="faxService"
          placeholder="Select E-mail Fax Service"
          isLoading={isEmailFaxServiceLoading}
        />
        {switchFields?.map((field, index) => (
          <SwitchField
            key={`${field?.label}-${index}`}
            label={field?.label}
            name={field?.name as any}
            form={form}
            className="mt-4"
            disabled={field?.disabled}
          />
        ))}
      </div>
    </div>
  );
};

export default EmailSettings;
