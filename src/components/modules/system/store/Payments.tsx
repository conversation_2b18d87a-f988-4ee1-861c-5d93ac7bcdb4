import SwitchField from '@/components/common/switch';
import NumberInputField from '@/components/forms/number-input-field';
import SelectDropDown from '@/components/forms/select-dropdown';
import { useGetEnumsListQuery } from '@/redux/features/enums-api/enums-api';
import { StorePaymentsTypes, SwitchFieldConfig } from '@/types/store.types';
import { useFormContext } from 'react-hook-form';

// Array of fields that need a SwitchField (Yes/No toggles)
const switchFields: SwitchFieldConfig<StorePaymentsTypes>[] = [
  { label: 'Validate Credit Card Numbers', name: 'validateCreditCard' },
  {
    label: 'Decline Partial Credit Card Approvals (Authorize.net)',
    name: 'declinePartialCreditCardApprovals',
  },
  {
    label: 'Allow Non-Processed Credit Card Payments',
    name: 'allowNonProcessedCreditCardPayments',
  },
  {
    label: 'Allow Refunding Convenience Fees',
    name: 'allowRefundingConvenienceFees',
  },
];

const Payments = () => {
  const form = useFormContext<StorePaymentsTypes>();

  // Default Payment Type
  const { data: paymentTypeList, isLoading: paymentTypeLoading } =
    useGetEnumsListQuery({
      name: 'PaymentType',
      isAsc: true,
    });

  return (
    <div className="flex flex-col gap-6 w-full h-full">
      <div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <SelectDropDown
            form={form}
            optionsList={paymentTypeList?.data || []}
            label="Default Payment Type"
            name="paymentType"
            placeholder="Default Payment Type"
            isLoading={paymentTypeLoading}
          />
          <NumberInputField
            form={form}
            name="creditCardConvenienceFees"
            label="Credit Card Convenience Fee"
            placeholder="__.__%"
            maxLength={6}
            suffix="%"
          />

          <NumberInputField
            form={form}
            name="debitCardConvenienceFees"
            label="Debit Card Convenience Fee"
            placeholder="__.__%"
            maxLength={6}
            suffix="%"
          />

          <NumberInputField
            form={form}
            name="achConvenienceFees"
            label="ACH Convenience Fee"
            placeholder="__.__%"
            maxLength={6}
            suffix="%"
          />

          <NumberInputField
            form={form}
            name="monthsBeforeOnlinePaymentLinkExpire"
            label="Months Before Online Payment links Expire"
            placeholder="___"
            maxLength={3}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
          {/* Dynamically render SwitchField components */}
          {switchFields?.map((field) => (
            <div key={field.name} className="mt-4">
              <SwitchField label={field.label} form={form} name={field.name} />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Payments;
