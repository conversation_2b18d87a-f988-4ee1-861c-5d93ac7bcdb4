import SwitchField from '@/components/common/switch';
import { StoreRfidSettings } from '@/types/store.types';
import { useFormContext } from 'react-hook-form';

const RFIDSettings = () => {
  const form = useFormContext<StoreRfidSettings>();

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
      <div>
        <SwitchField label="RFID" form={form} name="rfid" />
      </div>
      <div>
        <SwitchField
          label="RFID Updates Pickup Date"
          form={form}
          name="rfidUpdatesPickupDate"
        />
      </div>
      <div>
        <SwitchField
          label="RFID Return Alerts"
          form={form}
          name="rfidReturnAlerts"
        />
      </div>
    </div>
  );
};

export default RFIDSettings;
