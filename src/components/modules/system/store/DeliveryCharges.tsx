import SwitchField from '@/components/common/switch';
import TooltipWidget from '@/components/common/tooltip-widget';
import NumberInputField from '@/components/forms/number-input-field';
import SelectDropDown from '@/components/forms/select-dropdown';
import { useGetEnumsListQuery } from '@/redux/features/enums-api/enums-api';
import {
  StoreDeliveryChargesTypes,
  SwitchFieldConfig,
} from '@/types/store.types';
import { HelpCircleIcon } from 'lucide-react';
import { useFormContext } from 'react-hook-form';

const switchFields: SwitchFieldConfig<StoreDeliveryChargesTypes>[] = [
  {
    label: 'Calculate Delivery Charge for Sales Order/Quote',
    name: 'calculateDcCharge',
  },
  {
    label: 'Use No Delivery Charge When No Shipping Charge',
    name: 'useNoDeliveryCharge',
  },
  {
    label: 'Display Delivery Charge Warning Message',
    name: 'displayDcWarning',
  },
  {
    label: 'Require Delivery Charge Confirmation',
    name: 'requireDcConfirmation',
  },
];
const DeliveryCharges = () => {
  const form = useFormContext<StoreDeliveryChargesTypes>();

  const {
    data: deliveryChargesCalculation,
    isLoading: isDeliveryChargesCalculationLoading,
  } = useGetEnumsListQuery({
    name: 'DeliveryChargesCalculation',
  });

  const { data: webRateShip, isLoading: isWebRateShipLoading } =
    useGetEnumsListQuery({
      name: 'WebRateShip',
    });

  const { data: webRateReturn, isLoading: isWebRateReturnLoading } =
    useGetEnumsListQuery({
      name: 'WebRateReturn',
    });

  return (
    <div className="flex flex-col gap-6 justify-between h-full">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <SelectDropDown
          form={form}
          optionsList={deliveryChargesCalculation?.data ?? []}
          label="Delivery Charge Calculation"
          name="chargeCalculation"
          placeholder="Select Delivery Charge Calculation"
          isLoading={isDeliveryChargesCalculationLoading}
        />
        <NumberInputField
          form={form}
          name="defaultCharges"
          label="Default Delivery Charge"
          placeholder="$____.__"
          maxLength={8}
          prefix="$"
        />

        <SelectDropDown
          form={form}
          optionsList={webRateShip?.data ?? []}
          label="Default Web Rate Ship"
          name="defaultWebRateShip"
          placeholder="Select Default Web Rate Ship"
          isLoading={isWebRateShipLoading}
        />
        <SelectDropDown
          form={form}
          optionsList={webRateReturn?.data ?? []}
          label="Default Web Rate Return"
          name="defaultWebRateReturn"
          placeholder="Select Default Web Rate Return"
          isLoading={isWebRateReturnLoading}
        />
        <NumberInputField
          form={form}
          name="dcIncreasePercent"
          label="Delivery Charge Increase"
          placeholder="____.__%"
          maxLength={8}
          prefix=""
          suffix="%"
          extraLabel={
            <TooltipWidget
              tooltip={
                <div>
                  The Delivery charge increase percentage only applies to
                  <br />
                  delivery charges imported from UPS/FedEx desktop software.
                </div>
              }
              className="p-6 text-wrap"
            >
              <HelpCircleIcon size={24} className="text-white" fill="#5d26d2" />
            </TooltipWidget>
          }
        />
        <div></div>
        {switchFields?.map((field, index) => (
          <SwitchField
            key={`${field?.label}-${index}`}
            label={field?.label}
            name={field?.name as any}
            form={form}
            className="mt-4"
          />
        ))}
      </div>
    </div>
  );
};

export default DeliveryCharges;
