import SwitchField from '@/components/common/switch';
import NumberInputField from '@/components/forms/number-input-field';
import SelectDropDown from '@/components/forms/select-dropdown';
import { useGetEnumsListQuery } from '@/redux/features/enums-api/enums-api';
import {
  StoreDateCalculationTypes,
  SwitchFieldConfig,
} from '@/types/store.types';
import { useFormContext } from 'react-hook-form';

const DateCalculation = () => {
  const form = useFormContext<StoreDateCalculationTypes>();

  const {
    data: shipDatesCalculation,
    isLoading: isShipDatesCalculationLoading,
  } = useGetEnumsListQuery({
    name: 'ShipDatesCalculation',
  });

  const allowManuallyEnterDate = form.watch('allowManuallyEnterDate');
  const switchFields: SwitchFieldConfig<StoreDateCalculationTypes>[] = [
    {
      label: 'Manually Enter Del/PU Dates',
      name: 'allowManuallyEnterDate',
    },
    {
      label: 'Clear Existing Dates on Customer Change',
      name: 'allowClearExistingDate',
      disabled: !allowManuallyEnterDate,
    },
    {
      label: 'Allow Arrival on Date of Use',
      name: 'allowArrivalDate',
    },
    {
      label: 'Rent Days Affect Pickup Date',
      name: 'allowAffectPickupDate',
    },
    {
      label: 'Update Order Date After Quote Becomes Order',
      name: 'allowUpdateDate',
    },
    {
      label: 'Always Allow Editing of Arrival/Return Ship Dates',
      name: 'allowEditingShipDate',
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <NumberInputField
        form={form}
        name="maxDaysBeforeDelivery"
        label="Max Past Days Before Delivery Date Warning Message"
        placeholder="___"
        maxLength={3}
      />

      <SelectDropDown
        form={form}
        optionsList={shipDatesCalculation?.data ?? []}
        label="Ship Dates Calculation"
        name="shipDatesCalculation"
        placeholder="Select Dates Calculation"
        isLoading={isShipDatesCalculationLoading}
      />

      {switchFields?.map((field, index) => (
        <SwitchField
          key={`${field?.label}-${index}`}
          label={field?.label}
          name={field?.name as any}
          form={form}
          className="mt-4"
          disabled={field?.disabled}
        />
      ))}
    </div>
  );
};

export default DateCalculation;
