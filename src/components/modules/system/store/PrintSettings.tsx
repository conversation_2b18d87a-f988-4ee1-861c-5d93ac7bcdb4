import SwitchField from '@/components/common/switch';
import NumberIn<PERSON><PERSON>ield from '@/components/forms/number-input-field';
import SelectDropDown from '@/components/forms/select-dropdown';
import { useGetEnumsListQuery } from '@/redux/features/enums-api/enums-api';
import {
  StorePrintSettingsTypes,
  SwitchFieldConfig,
} from '@/types/store.types';
import { useFormContext } from 'react-hook-form';

const switchFields: SwitchFieldConfig<StorePrintSettingsTypes>[] = [
  { label: 'Allow Local Packing List', name: 'allowLocalPackingLists' },
  {
    label: 'Default Printing to Previous Selection',
    name: 'defaultPrintingToPreviousSelection',
  },
  {
    label: 'Optionally Invoice Orders Printed to Non-Invoice Printers',
    name: 'optionallyInvoiceOrdersPrintedToNonInvoicePrinters',
  },
  {
    label: 'Print Packing Lists, Delivery/Pickup Slips by Departments',
    name: 'printPackagingList',
  },
  {
    label: 'Include Revision Number In PDF Filename',
    name: 'includeRevisionNumberInPDF',
  },
  { label: 'Open PDFs After Created', name: 'openPDFAfterCreated' },
  { label: 'Item Pics on Forms', name: 'itemPicsOnForms' },
  {
    label: 'Allow Printing to Include Locked Orders',
    name: 'allowPrintingToIncludeLockedOrders',
  },
  {
    label: 'Processes Print Sorting Selection',
    name: 'processesPrintsortingSelection',
  },
];

const PrintSettings = () => {
  const form = useFormContext<StorePrintSettingsTypes>();

  // Item Print Sequence
  const { data: itemPrintSequenceList, isLoading: itemPrintSequenceLoading } =
    useGetEnumsListQuery({
      name: 'ItemPrintSequence',
    });

  // Kit Component Print Sequence
  const {
    data: kitComponentPrintSeqList,
    isLoading: kitComponentPrintSeqLoadin,
  } = useGetEnumsListQuery({
    name: 'KitComponentPrintSeq',
  });

  //Batch Invoice Print Options For Customers Pickups
  const {
    data: batchInvoicePrintOptionsList,
    isLoading: batchInvoicePrintOptionsLoading,
  } = useGetEnumsListQuery({
    name: 'BatchInvoicePrintOptions',
  });

  return (
    <div className="flex flex-col gap-6 w-full h-full">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <SelectDropDown
          form={form}
          optionsList={itemPrintSequenceList?.data || []}
          label="Item Print Sequence"
          name="itemPrintSequence"
          placeholder="Item Print Sequence"
          isLoading={itemPrintSequenceLoading}
        />
        <SelectDropDown
          form={form}
          optionsList={kitComponentPrintSeqList?.data || []}
          label="Kit Component Print Sequence"
          name="kitComponentPrintSeq"
          placeholder="Kit Component Print Sequence"
          isLoading={kitComponentPrintSeqLoadin}
        />
        <SelectDropDown
          form={form}
          optionsList={batchInvoicePrintOptionsList?.data || []}
          label="Batch Invoice Print Options for Customer Pickups"
          name="batchInvoicePrintOptions"
          placeholder="Batch Invoice Print Options for Customer Pickups"
          isLoading={batchInvoicePrintOptionsLoading}
        />
        <NumberInputField
          form={form}
          name="maxDaysBeforeInvoicingWarning"
          label="Max Days Before Invoicing Warning Message"
          placeholder="___"
          maxLength={3}
        />

        {/* Render SwitchField components dynamically from the switchFields array */}
        {switchFields?.map((field) => (
          <SwitchField
            key={field.name}
            className="mt-4"
            label={field.label}
            form={form}
            name={field.name}
          />
        ))}
      </div>
    </div>
  );
};

export default PrintSettings;
