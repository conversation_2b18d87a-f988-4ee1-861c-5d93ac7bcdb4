import AppButton from '@/components/common/app-button';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import ActionColumnMenu from '@/components/common/data-tables/ActionColumn';
import NumberInputField from '@/components/forms/number-input-field';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { REQUIRED_TEXT } from '@/constants/validation-constants';
import {
  ExpeditedFeeErrorShape,
  ExpeditedFeeType,
  StoreTotalExpeditedFeeType,
} from '@/types/store.types';
import { useCallback, useState } from 'react';
import { useFieldArray, useForm } from 'react-hook-form';

const TotalExpeditedFee = ({
  expeditedFee,
  onOkClick,
  onCancel,
}: ExpeditedFeeType) => {
  const form = useForm<{ storeExpeditedFees: StoreTotalExpeditedFeeType[] }>({
    defaultValues: {
      storeExpeditedFees: expeditedFee || [
        { daysFrom: '', daysTo: '', fee: '' },
      ],
    },
    // Apply validation rules
    resolver: async (data) => {
      const errors: ExpeditedFeeErrorShape = {};

      data.storeExpeditedFees.forEach((item, index) => {
        const isRowFilled = item.daysFrom || item.daysTo || item.fee; // Check if any value is entered

        if (isRowFilled) {
          if (!item.daysFrom) {
            errors[`storeExpeditedFees.${index}.daysFrom`] = {
              message: REQUIRED_TEXT,
            };
          }
          if (!item.daysTo) {
            errors[`storeExpeditedFees.${index}.daysTo`] = {
              message: REQUIRED_TEXT,
            };
          }
          if (!item.fee) {
            errors[`storeExpeditedFees.${index}.fee`] = {
              message: REQUIRED_TEXT,
            };
          }
        }
      });

      return { values: data, errors };
    },
  });

  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState<boolean>(false);
  const [selectedItem, setSelectedItem] = useState<number>(0);

  const toggleDelete = useCallback((id?: number) => {
    setIsDeleteDialogOpen((prev) => !prev);
    setSelectedItem(id || 0);
  }, []);

  const { fields, append, remove } = useFieldArray({
    control: form?.control,
    name: 'storeExpeditedFees',
  });

  const onAddNew = useCallback(() => {
    append({
      daysFrom: '',
      daysTo: '',
      fee: '',
    });
  }, [append]);

  const handleDeleteItem = useCallback(() => {
    remove(selectedItem);
    setIsDeleteDialogOpen(false);
  }, [remove, selectedItem]);

  const handleCancelExpeditedFee = useCallback(() => {
    form.reset();
    onCancel();
  }, [form, onCancel]);

  const onSubmit = useCallback(
    (data: { storeExpeditedFees: StoreTotalExpeditedFeeType[] }) => {
      const expeditedFee = data?.storeExpeditedFees?.filter(
        (item: StoreTotalExpeditedFeeType) =>
          item?.daysFrom || item?.daysTo || item?.fee
      );
      onOkClick(expeditedFee);
    },
    [onOkClick]
  );

  return (
    <div className="px-4">
      <AppButton
        className="flex justify-self-end text-text-neutral-Default h-8 mb-2"
        onClick={onAddNew}
        variant="neutral"
        label="+ Add New"
      />

      <div className="col-span-2 border rounded-md border-grayScale-20 lg:max-h-[400px] overflow-auto overflow_initial_table">
        <Table className="rounded-sm">
          <TableHeader
            className="bg-grayScale-10"
            style={{
              position: 'sticky',
              top: 0,
              background: '#f4f4f4',
              zIndex: 1,
            }}
          >
            <TableRow className="text-base font-medium grid grid-cols-3">
              <TableHead className="text-grayScale-90 border-r border-grayScale-20 pt-3">
                Days From
              </TableHead>
              <TableHead className="text-grayScale-90 border-r border-grayScale-20 pt-3">
                Days To
              </TableHead>
              <TableHead className="text-grayScale-90 border-grayScale-20 pt-3">
                Fee
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {fields.map((field, index) => {
              return (
                <TableRow key={field.id} className="grid grid-cols-3">
                  <TableCell className="border-r border-grayScale-20">
                    <NumberInputField
                      form={form}
                      name={`storeExpeditedFees.${index}.daysFrom`}
                      placeholder="Days From"
                      maxLength={4}
                      // errorMessage={
                      //   errors?.storeExpeditedFees?.[index]?.daysFrom
                      //     ?.message ?? ''
                      // }
                    />
                  </TableCell>
                  <TableCell className="border-r border-grayScale-20">
                    <NumberInputField
                      form={form}
                      name={`storeExpeditedFees.${index}.daysTo`}
                      placeholder="Days To"
                      maxLength={4}
                    />
                  </TableCell>
                  <TableCell className="border-grayScale-20">
                    <div className="flex flex-row items-center gap-x-1">
                      <NumberInputField
                        form={form}
                        name={`storeExpeditedFees.${index}.fee`}
                        placeholder="Fee"
                        maxLength={8}
                        suffix="%"
                      />
                      <ActionColumnMenu
                        onDelete={() => toggleDelete(index)}
                        contentClassName="w-fit z-[100]"
                      />
                    </div>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </div>

      {/* Action Buttons Section */}
      <div className="mt-4 flex flex-row justify-end gap-4">
        <AppButton
          label="OK"
          onClick={form.handleSubmit(onSubmit)}
          className="w-20"
        />
        <AppButton
          onClick={handleCancelExpeditedFee}
          label="Cancel"
          className="w-20"
          variant="neutral"
        />
      </div>
      <AppConfirmationModal
        description={<div>Are you sure you want to delete this record?</div>}
        open={isDeleteDialogOpen}
        onOpenChange={toggleDelete}
        handleCancel={toggleDelete}
        handleSubmit={handleDeleteItem}
      />
    </div>
  );
};

export default TotalExpeditedFee;
