import SwitchField from '@/components/common/switch';
import InputField from '@/components/forms/input-field';
import PasswordInputField from '@/components/forms/PasswordInputField';
import { CompanyFormData } from '@/types/company.types';
import { useFormContext } from 'react-hook-form';

const WebRateServices = () => {
  const form = useFormContext<CompanyFormData>();
  const useFedex = form.watch('useFedEx');

  return (
    <div className="flex flex-col gap-6">
      {/* Switch for FedEx */}
      <div>
        <SwitchField form={form} name="useFedEx" label="Use FedEx" />
      </div>
      {/* FedEx User Key */}
      <InputField
        form={form}
        label="User Key"
        maxLength={20}
        name="fedExUserKey"
        disabled={!useFedex}
      />
      {/* FedEx User Password */}
      <PasswordInputField
        form={form}
        label="User Password"
        maxLength={30}
        name="fedExPassword"
        disabled={!useFedex}
      />
      {/* FedEx Account Number */}
      <InputField
        type="number"
        maxLength={15}
        form={form}
        label="Account Number"
        name="fedExAcctNum"
        disabled={!useFedex}
      />
      {/* FedEx Meter Number */}
      <PasswordInputField
        form={form}
        label="Meter Number"
        name="fedExMeter"
        maxLength={15}
        disabled={!useFedex}
      />
    </div>
  );
};

export default WebRateServices;
