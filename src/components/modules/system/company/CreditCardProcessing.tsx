import CheckboxField from '@/components/forms/checkbox';
import Input<PERSON>ield from '@/components/forms/input-field';
import PasswordInputField from '@/components/forms/PasswordInputField';
import RadioField from '@/components/forms/radio-field';
import {
  CC_PROCESSING_TYPE_OPTIONS,
  GRAVITY_CHECKBOX_OPTIONS,
} from '@/constants/company-constants';
import { CCProcessingType, CompanyFormData } from '@/types/company.types';
import { useFormContext } from 'react-hook-form';

const CreditCardProcessing = () => {
  const form = useFormContext<CompanyFormData>();
  const { control, watch } = form;

  const ccProcessingType = watch('ccProcessingType');
  const isGravityPaymentType =
    ccProcessingType === CCProcessingType.GRAViTY_PAYMENTS;

  return (
    <div className="flex flex-col gap-6">
      {/* Credit Card Processing Type Radio Buttons */}
      <RadioField
        form={form}
        name="ccProcessingType"
        optionsPerRow={1}
        options={CC_PROCESSING_TYPE_OPTIONS}
      />

      {isGravityPaymentType && (
        <div className="flex flex-col pl-6 gap-6">
          {/* OID Input Field */}
          <InputField
            form={form}
            label="OID"
            name="gravityOid"
            maxLength={15}
          />
          {/* Authorization Token (Password) */}
          <PasswordInputField
            form={form}
            label="Authorization Token"
            name="gravityAuthToken"
          />
          {/* HPP Username Input Field */}
          <InputField
            form={form}
            label="HPP Username"
            name="gravityHppUsername"
            maxLength={16}
          />
          {/* HPP Password Input Field */}
          <PasswordInputField
            form={form}
            label="HPP Password"
            name="gravityHppPassword"
            maxLength={70}
          />
          {/* Queue ID Input Field */}
          <PasswordInputField
            form={form}
            label="Queue ID"
            name="gravityQueueId"
            maxLength={36}
          />
          {/* Checkbox Fields */}
          <CheckboxField
            control={control}
            name="gravityOptions"
            checkboxGroup={GRAVITY_CHECKBOX_OPTIONS}
          />
        </div>
      )}
    </div>
  );
};

export default CreditCardProcessing;
