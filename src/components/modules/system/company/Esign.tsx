import TooltipWidget from '@/components/common/tooltip-widget';
import InputField from '@/components/forms/input-field';
import RadioField from '@/components/forms/radio-field';
import { ESIGN_TYPE_OPTIONS } from '@/constants/company-constants';
import { CompanyFormData, EsignType } from '@/types/company.types';
import { Eye, EyeOff } from 'lucide-react';
import { useState } from 'react';
import { useFormContext } from 'react-hook-form';

const Esign = () => {
  const form = useFormContext<CompanyFormData>();
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);

  // Watch esignType and check if PTSign is selected
  const esignType = form.watch('esignType');
  const isPtSignSelected = esignType === EsignType.PTSIGN;

  const handlePasswordVisibilityToggle = () => {
    setIsPasswordVisible((prev) => !prev);
  };

  return (
    <div className="p-6 flex flex-col gap-6">
      {/* Esign Type Radio Buttons */}
      <RadioField
        form={form}
        name="esignType"
        optionsPerRow={1}
        options={ESIGN_TYPE_OPTIONS}
      />

      {isPtSignSelected && (
        <div className="relative pl-6">
          <InputField
            form={form}
            label="API Key"
            name="ptSignApiKey"
            maxLength={36}
            type={isPasswordVisible ? 'text' : 'password'}
            disabled={!isPtSignSelected}
          />
          <button
            type="button"
            className="absolute right-3 top-[42px] text-gray-600 hover:text-gray-800 focus:outline-none transition-colors"
            onClick={handlePasswordVisibilityToggle}
            disabled={!isPtSignSelected}
          >
            <TooltipWidget
              content={isPasswordVisible ? 'Hide Key' : 'View Key'}
            >
              {isPasswordVisible ? (
                <EyeOff className="w-5 h-5" />
              ) : (
                <Eye className="w-5 h-5" />
              )}
            </TooltipWidget>
          </button>
        </div>
      )}
    </div>
  );
};

export default Esign;
