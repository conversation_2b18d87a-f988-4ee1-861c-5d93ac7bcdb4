import AppButton from '@/components/common/app-button';
import AppSpinner from '@/components/common/app-spinner';
import AppTabsVertical from '@/components/common/app-tabs-vertical';
import SaveIcon from '@/assets/icons/SaveIcon';
import { UseToast } from '@/components/ui/toast/ToastContainer';
import { COMPANY_API_ROUTES } from '@/constants/api-constants';
import { companyTabList } from '@/constants/company-constants';
import { ROUTES } from '@/constants/routes-constants';
import { getQueryParam, updateQueryParam } from '@/lib/utils';
import { setStorageValue } from '@/redux/features/auth/authSlice';
import {
  useGetCompanyQuery,
  usePostCompanyMutation,
} from '@/redux/features/company/company.api';

import {
  CCProcessingType,
  CompanyTabsEnum,
  CompanyFormData,
  CompanyData,
  EsignType,
} from '@/types/company.types';
import { useEffect, useMemo, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, SubmitHandler, useForm } from 'react-hook-form';
import { useLocation, useNavigate } from 'react-router-dom';

const Company = () => {
  const toast = UseToast();
  const tabName = getQueryParam('tab') || CompanyTabsEnum.COMPANY_DETAILS;
  const [, setActiveTab] = useState(tabName);
  const navigate = useNavigate();
  const location = useLocation();
  const { data, isLoading } = useGetCompanyQuery();
  const [postCompany, { isLoading: isPosting }] = usePostCompanyMutation();

  const defaultValues: CompanyFormData = useMemo(() => {
    const {
      id,
      companyName,
      useMultiloc,
      isIdleLogout,
      idleLogoutMinutes,
      creditCard,
      webRate,
      esign,
    } = (data?.data as CompanyData) || {};
    const {
      ccProcessingType,
      gravityOid,
      gravityAuthToken,
      gravityHppUsername,
      gravityHppPassword,
      gravityLevel2Data,
      gravityQueueId,
      gravityUseDevices,
    } = creditCard || {};
    const { useFedEx, fedExUserKey, fedExAcctNum, fedExMeter, fedExPassword } =
      webRate || {};
    const { esignType, ptSignApiKey } = esign || {};

    return {
      id: id ?? 0,
      companyName: companyName ?? '',
      useMultiloc: useMultiloc ?? false,
      isIdleLogout: isIdleLogout ?? true,
      idleLogoutMinutes: idleLogoutMinutes ?? 120,
      ccProcessingType: ccProcessingType?.toString() ?? CCProcessingType.NONE,
      gravityOid: gravityOid ?? '',
      gravityAuthToken: gravityAuthToken ?? '',
      gravityHppUsername: gravityHppUsername ?? '',
      gravityHppPassword: gravityHppPassword ?? '',
      gravityQueueId: gravityQueueId ?? '',
      gravityUseDevices: gravityUseDevices ?? false,
      gravityLevel2Data: gravityLevel2Data ?? false,
      useFedEx: useFedEx ?? false,
      fedExUserKey: fedExUserKey ?? '',
      fedExPassword: fedExPassword ?? '',
      fedExAcctNum: fedExAcctNum ?? '',
      fedExMeter: fedExMeter ?? '',
      esignType: esignType?.toString() ?? EsignType.NONE,
      ptSignApiKey: ptSignApiKey ?? '',
    };
  }, [data?.data]);

  // Form management
  const form = useForm<CompanyFormData>({
    defaultValues,
    mode: 'onChange',
  });

  // Handle tab change
  const handleTabClick = (value: string) => {
    setActiveTab(value);
    updateQueryParam(value, 'tab');
  };

  const createPayload = (
    tabName: string,
    formData: CompanyFormData
  ): CompanyFormData => {
    const {
      id,
      ccProcessingType,
      gravityOid,
      gravityAuthToken,
      gravityHppUsername,
      gravityHppPassword,
      gravityQueueId,
      gravityUseDevices,
      gravityLevel2Data,
      useFedEx,
      fedExUserKey,
      fedExAcctNum,
      fedExMeter,
      fedExPassword,
      esignType,
      ptSignApiKey,
      companyName,
      useMultiloc,
      idleLogoutMinutes,
      paymentGateway,
    } = formData;

    switch (tabName) {
      case CompanyTabsEnum.CREDIT_CARD_PROCESSING:
        return {
          id,
          ccProcessingType: Number(ccProcessingType),
          gravityOid,
          gravityAuthToken,
          gravityHppUsername,
          gravityHppPassword,
          gravityQueueId,
          gravityUseDevices,
          gravityLevel2Data,
          paymentGateway,
        };

      case CompanyTabsEnum.WEB_RATE_SERVICES:
        return {
          id,
          useFedEx,
          fedExUserKey,
          fedExAcctNum,
          fedExMeter,
          fedExPassword,
        };

      case CompanyTabsEnum.E_SIGN:
        return {
          id,
          esignType: Number(esignType),
          ptSignApiKey,
        };

      case CompanyTabsEnum.COMPANY_DETAILS:
      default:
        return {
          id,
          companyName,
          useMultiloc,
          idleLogoutMinutes: Number(idleLogoutMinutes),
        };
    }
  };

  // Helper function to get the correct URL based on tab name
  const getUrlForTab = (tabName: string) => {
    switch (tabName) {
      case CompanyTabsEnum.CREDIT_CARD_PROCESSING:
        return COMPANY_API_ROUTES.CREDIT_CARD_PROCESSING;
      case CompanyTabsEnum.WEB_RATE_SERVICES:
        return COMPANY_API_ROUTES.WEB_RATE_SERVICES;
      case CompanyTabsEnum.E_SIGN:
        return COMPANY_API_ROUTES.E_SIGN;
      case CompanyTabsEnum.COMPANY_DETAILS:
      default:
        return COMPANY_API_ROUTES.COMPANY_DETAILS;
    }
  };

  // Handle form submission
  const onSubmit: SubmitHandler<CompanyFormData> = async (formData) => {
    formData.paymentGateway =
      formData?.ccProcessingType === CCProcessingType.GRAViTY_PAYMENTS
        ? 'GRAVITY'
        : 'NONE';

    const payload = createPayload(tabName, formData);
    const url = getUrlForTab(tabName);

    try {
      const response = await postCompany({
        url,
        companyData: payload,
      }).unwrap();
      if (url === COMPANY_API_ROUTES.COMPANY_DETAILS) {
        setStorageValue(
          'idleLogoutMinutes',
          formData?.idleLogoutMinutes?.toString() || '',
          true
        );
      }
      toast.success(response?.message);
    } catch (error) {}
  };

  // Handle cancel button click
  const handleCancel = () => {
    if (location?.state?.referrer) {
      navigate(location?.state?.referrer);
    } else {
      navigate(ROUTES.HOME);
    }
  };

  // Reset form when data changes
  useEffect(() => {
    if (data?.data) {
      form.reset(defaultValues);
    }
  }, [data?.data, defaultValues, form]);

  return (
    <>
      <FormProvider {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <div className="flex flex-col gap-4 p-4 ">
            <div className="flex justify-between items-center px-4">
              <div className="flex gap-x-4 items-center">
                <h1 className="text-2xl font-semibold">Company Options</h1>
              </div>
              <div className="flex flex-row gap-2 w-80">
                {/* Submit Button */}
                <AppButton
                  className="w-full"
                  type="submit"
                  label={'Save Detail'}
                  icon={SaveIcon}
                  isLoading={isPosting}
                />
                <AppButton
                  variant="neutral"
                  className="w-full"
                  type="button"
                  label={'Cancel'}
                  onClick={handleCancel}
                />
              </div>
            </div>
            {/* Using AppTabs Component */}
            <AppTabsVertical
              tabs={companyTabList}
              activeTab={tabName}
              onTabChange={handleTabClick}
              className="px-4"
            />
          </div>
        </form>
      </FormProvider>
      <AppSpinner isLoading={isLoading} overlay />
    </>
  );
};

export default Company;
