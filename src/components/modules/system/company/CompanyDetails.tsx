import SwitchField from '@/components/common/switch';
import InputField from '@/components/forms/input-field';
import { CompanyFormData } from '@/types/company.types';
import { useFormContext } from 'react-hook-form';

const CompanyDetails = () => {
  const form = useFormContext<CompanyFormData>();
  const isIdleLogout = form.watch('isIdleLogout');

  return (
    <div className="flex flex-col gap-6 justify-between">
      <div className="flex flex-col gap-6">
        {/* Company Name input field */}
        <InputField
          form={form}
          label="Company Name"
          name="companyName"
          disabled
        />

        {/* Switch for Multi Locations */}
        <div className="mb-2 mt-2">
          <SwitchField
            form={form}
            label="Use Multi Locations"
            name="useMultiloc"
          />
        </div>

        {/* Switch for Idle Logout */}
        <div className="mb-2 mt-2">
          <SwitchField
            form={form}
            label="Idle Logout"
            name="isIdleLogout"
            disabled
          />
        </div>

        {/* Input for Minutes Idle Before Logout */}
        <InputField
          form={form}
          maxLength={3}
          label="Minutes Idle Before Logout"
          name="idleLogoutMinutes"
          type="number"
          disabled={!isIdleLogout}
          validation={{
            required: 'Required',
            min: {
              value: 30,
              message: 'Idle time before logout must be at least 30 minutes',
            },
          }}
        />
      </div>

      {/* Info Card (could be for additional details or settings) */}
      {/* <InfoCard /> */}
    </div>
  );
};

export default CompanyDetails;
