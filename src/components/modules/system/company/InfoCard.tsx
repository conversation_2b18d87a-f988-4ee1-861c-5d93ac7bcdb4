import { BadgeInfoIcon } from 'lucide-react';
import React from 'react';

interface InfoCardProps {
  title?: string;
  message?: string;
}

const InfoCard: React.FC<InfoCardProps> = ({ title, message }) => {
  return (
    <div className=" border border-[#E2C7AC] h-auto bg-[#FFFBEB] rounded-md  flex flex-col gap-6 p-6">
      <div className="text-[#522504] flex flex-row gap-[10px] items-center">
        <BadgeInfoIcon className="w-5 h-5" />
        <h3 className="text-brown-600 font-normal text-lg">{title}</h3>
      </div>
      <div className="text-[#BF6A02]">{message}</div>
    </div>
  );
};

export default InfoCard;
