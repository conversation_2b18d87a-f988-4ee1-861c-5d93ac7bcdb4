import SelectWidget from '@/components/forms/select';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { TEXT_VALIDATION_RULE } from '@/constants/validation-constants';
import { generateLabelValuePairs } from '@/lib/utils';
import { useGetStoreLocationsQuery } from '@/redux/features/store/store.api';
import { useGetEnumsListQuery } from '@/redux/features/enums-api/enums-api';
import { useGetTimeZoneListQuery } from '@/redux/features/timezone/timezone.api';
import { TimeZoneType } from '@/types/customer.types';
import { ProfileGeneralInformation } from '@/types/user-options.types';
import { useFormContext } from 'react-hook-form';

export default function OtherSettings() {
  const form = useFormContext<ProfileGeneralInformation>(); // Specify form data type
  const { data: storeLocations, isLoading } = useGetStoreLocationsQuery();
  const { data: timeZoneData, isLoading: timeZoneLoader } =
    useGetTimeZoneListQuery();
  const storeLocationList = generateLabelValuePairs({
    data: storeLocations?.data,
    labelKey: 'location',
    valueKey: 'config_id',
  });

  const timeZoneList = generateLabelValuePairs({
    data: timeZoneData as TimeZoneType[],
    labelKey: 'displayName',
    valueKey: 'id',
  });

  const { data: pages } = useGetEnumsListQuery({
    name: 'StartPage',
  });
  const { data: LocationSelectionList, isLoading: locationSelectionloader } =
    useGetEnumsListQuery({
      name: 'LocationSelection',
    });

  const pagesList = pages?.data?.map((item: any) => {
    return {
      label: item.value,
      value: item.label,
    };
  });

  return (
    <div className="flex flex-row gap-5 w-full">
      <div className="flex flex-row gap-8 flex-1 p-3">
        {/* Middle Section: Form */}
        <div className="flex-1  grid-cols-1 md:grid-cols-2 gap-4 ">
          <Accordion type="single" className="flex flex-col space-y-4">
            {/* <Accordion
              className="AccordionRoot"
              type="single"
              defaultValue="item-1"
              collapsible
            >
              <AccordionItem value="item-1" className="border-0 ">
                <AccordionTrigger className="bg-grayScale-10 rounded-md px-3 hover:no-underline">
                  SMTP Settings
                </AccordionTrigger>
                <AccordionContent className="grid grid-cols-2 gap-4 py-1 px-1">
                  <InputField
                    name="smtpUsername"
                    form={form}
                    label="SMTP Username"
                    placeholder="Enter SMTP Username"
                  />
                  <InputField
                    name="defaultForm"
                    form={form}
                    label="Default From"
                    placeholder="Enter Default From"
                  />
                  <InputField
                    name="smtpPassword"
                    form={form}
                    label="SMTP Password"
                    placeholder="Enter SMTP Password"
                  />
                  <InputField
                    name="defaultCc"
                    form={form}
                    label="Default CC"
                    placeholder="Enter Default CC"
                  />
                  <div className="col-span-2">
                    <TextAreaField
                      form={form}
                      name="defaultText"
                      label="Default Text"
                    />
                  </div>
                </AccordionContent>
              </AccordionItem>
            </Accordion> */}
            <Accordion
              className="AccordionRoot"
              type="single"
              defaultValue="item-2"
              collapsible
            >
              <AccordionItem value="item-2" className="border-0">
                <AccordionTrigger className="bg-grayScale-10 rounded-md px-3 hover:no-underline">
                  Misc
                </AccordionTrigger>
                <AccordionContent className="grid grid-cols-2 gap-6 py-4 px-1">
                  <SelectWidget
                    name="startPage"
                    form={form}
                    placeholder="Select Page"
                    label="Start Page"
                    isClearable={true}
                    optionsList={pagesList ?? []}
                  />
                  <SelectWidget
                    name="defaultLocationId"
                    form={form}
                    placeholder="Corporate"
                    label="Default Location"
                    isClearable={false}
                    isLoading={isLoading}
                    optionsList={storeLocationList}
                    validation={TEXT_VALIDATION_RULE}
                  />
                  <SelectWidget
                    name="locationSelection"
                    form={form}
                    placeholder="Last Selected Location"
                    label="Location Selection"
                    isClearable={true}
                    isLoading={locationSelectionloader}
                    optionsList={LocationSelectionList?.data || []}
                  />
                  <SelectWidget
                    form={form}
                    name="timezoneId"
                    label="Timezone"
                    optionsList={timeZoneList}
                    placeholder="Select Timezone"
                    // validation={{ required: 'Required' }}
                    isLoading={timeZoneLoader}
                  />
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </Accordion>
        </div>
      </div>
    </div>
  );
}
