import AppButton from '@/components/common/app-button';
import AppSpinner from '@/components/common/app-spinner';
import AppTabsVertical from '@/components/common/app-tabs-vertical';
import { ROUTES } from '@/constants/routes-constants';
import {
  USER_OPTIONS_API,
  UserOptionsTabList,
} from '@/constants/user-options-constants';
import { cn, getQueryParam, updateQueryParam } from '@/lib/utils';
import { setStorageValue } from '@/redux/features/auth/authSlice';
import {
  useGetListQuery,
  useUpdateItemMutation,
} from '@/redux/features/list/category/list.api';
import { setProfile } from '@/redux/features/user-options/loginProfileSlice';
import { RootState } from '@/redux/store';
import { ProfileGeneralInformation } from '@/types/user-options.types';
import { useEffect, useMemo, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation, useNavigate } from 'react-router-dom';

const UserOptions = () => {
  const tab = getQueryParam('tab');
  const [activeTab, setActiveTab] = useState(tab || 'general-information');
  const navigate = useNavigate();
  const location = useLocation();
  const profile = useSelector((state: RootState) => state.loginProfile.profile);
  const dispatch = useDispatch();

  const handleTabClick = (value: string) => {
    setActiveTab(value);
    updateQueryParam(value, 'tab');
  };
  const userId =
    localStorage.getItem('userId') || sessionStorage.getItem('userId') || '0';

  const { data, isFetching } = useGetListQuery({
    url: USER_OPTIONS_API?.GET(userId),
  });

  const [updateItem, { isLoading }] = useUpdateItemMutation();

  useEffect(() => {
    if (data?.data) {
      dispatch(
        setProfile({
          ...data?.data,
        })
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data?.data]);

  const defaultValues = useMemo(() => {
    return {
      ...data?.data,
      phoneCell: data?.data.phoneCell || '',
      phoneWork: data?.data.phoneWork || '',
      fax: data?.data.fax || '',
      updateImage: false,
    };
  }, [data]);

  const form = useForm<ProfileGeneralInformation>({
    defaultValues: defaultValues,
    mode: 'onChange',
  });
  const { handleSubmit, reset } = form;

  useEffect(() => {
    if (defaultValues) {
      reset(defaultValues);
    }
  }, [defaultValues, reset]);

  const onSubmit = async (data: ProfileGeneralInformation) => {
    try {
      if (data.id) {
        data.defaultLocationId = data?.defaultLocationId
          ? Number(data?.defaultLocationId)
          : null;
        data.locationSelection = data.locationSelection
          ? data.locationSelection
          : null;
        data.defLocation = data?.defaultLocationId ? data.defLocation : '';
        data.timezoneId = data?.timezoneId ? Number(data.timezoneId) : null;

        await updateItem({
          url: USER_OPTIONS_API.UPDATE(data.id),
          data: {
            ...data,
            updateImage: false,
            profileImage: '',
          },
        }).unwrap();
        dispatch(
          setProfile({
            ...profile,
            firstName: data.firstName,
            lastName: data?.lastName,
            profileImage: profile.profileImage,
            defaultLocationId: data?.defaultLocationId,
          })
        );
        setStorageValue('firstName', data?.firstName, true);
        setStorageValue('lastName', data?.lastName, true);
        setStorageValue(
          'defaultLocationId',
          data?.defaultLocationId != null
            ? data?.defaultLocationId?.toString()
            : '',
          true
        );
      }
    } catch (error) {
      // Handle error (optional)
    }
  };

  // Handle cancel button click
  const handleCancel = () => {
    if (location?.state?.referrer) {
      navigate(location?.state?.referrer);
    } else {
      navigate(ROUTES.HOME);
    }
  };

  return (
    <FormProvider {...form}>
      <div className="p-5 space-y-5">
        <div className="flex justify-between items-center px-4 pb-2 h-14">
          <div className="flex gap-x-4 items-center">
            <h1 className="text-2xl font-semibold">User Options</h1>
          </div>
          <div
            className={cn(tab !== 'security' ? 'flex flex-row gap-2 w-80' : '')}
          >
            {/* Submit Button */}
            {tab !== 'security' && (
              <AppButton
                className="w-40"
                label={'Save Details'}
                onClick={handleSubmit(onSubmit)}
              />
            )}

            <AppButton
              variant="neutral"
              className="w-40"
              type="button"
              label={'Cancel'}
              onClick={handleCancel}
            />
          </div>
        </div>
        <AppTabsVertical
          tabs={UserOptionsTabList}
          activeTab={activeTab}
          onTabChange={handleTabClick}
          className="px-4"
        />
        <AppSpinner overlay isLoading={isFetching || isLoading} />
      </div>
    </FormProvider>
  );
};

export default UserOptions;
