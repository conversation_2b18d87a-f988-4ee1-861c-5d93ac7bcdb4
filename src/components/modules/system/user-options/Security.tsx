import AppButton from '@/components/common/app-button';
import AppSpinner from '@/components/common/app-spinner';
import InputField from '@/components/forms/input-field';
import { SECURITY_API_ROUTES } from '@/constants/api-constants';
import { PASSWORD_VALIDATION_RULE } from '@/constants/validation-constants';
import { useAddNewProfileDetailsMutation } from '@/redux/features/user-options/profile-details.api';
import { ProfileSecurity } from '@/types/user-options.types';
import { Eye, EyeOff } from 'lucide-react';
import { useState } from 'react';
import { SubmitHandler, useForm, UseFormReturn } from 'react-hook-form';

// Password visibility toggle component
const PasswordVisibilityToggle = ({
  isVisible,
  toggleVisibility,
}: {
  isVisible: boolean;
  toggleVisibility: () => void;
}) => (
  <button
    type="button"
    className="absolute right-3 top-[42px] text-gray-600 hover:text-gray-800 focus:outline-none transition-colors"
    onClick={toggleVisibility}
  >
    {isVisible ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
  </button>
);

// Password input field with visibility toggle
const PasswordInputField = ({
  name,
  label,
  placeholder,
  type,
  validation,
  form,
  isShowError,
}: any) => (
  <div className="relative">
    <InputField
      name={name}
      type={type}
      form={form}
      label={label}
      placeholder={placeholder}
      validation={validation}
      isShowError={isShowError}
      className="w-full pr-12"
    />
  </div>
);

export default function Security() {
  const userId =
    localStorage.getItem('userId') || sessionStorage.getItem('userId') || '0';
  const [isNewPasswordVisible, setIsNewPasswordVisible] = useState(false);
  const [isConfirmPasswordVisible, setIsConfirmPasswordVisible] =
    useState(false);

  const handleNewPasswordVisibilityToggle = () =>
    setIsNewPasswordVisible((prev) => !prev);
  const handleConfirmPasswordVisibilityToggle = () =>
    setIsConfirmPasswordVisible((prev) => !prev);

  const [addNewData, { isLoading }] = useAddNewProfileDetailsMutation();

  const form: UseFormReturn<ProfileSecurity> = useForm<ProfileSecurity>({
    mode: 'onChange',
  });
  const { handleSubmit, reset } = form;

  const onSubmit: SubmitHandler<ProfileSecurity> = async (
    data: ProfileSecurity
  ) => {
    try {
      let body = {
        ...data,
        userId: parseInt(userId),
      };
      await addNewData({
        url: SECURITY_API_ROUTES.CREATE,
        data: body,
      }).unwrap();
      reset({
        confirmPassword: '',
        newPassword: '',
        oldPassword: '',
      });
    } catch (error) {
      // Handle error (optional)
    }
  };

  return (
    <div className="flex flex-row gap-5 w-full">
      <div className="flex flex-row gap-8 flex-1 p-3">
        <div className="flex-1 grid grid-cols-2 gap-10">
          <div className="col-span-2 relative">
            <PasswordInputField
              name="oldPassword"
              type={isNewPasswordVisible ? 'text' : 'password'}
              label="Current Password"
              placeholder="Current Password"
              validation={{ required: 'Required' }}
              form={form}
              isShowError={true}
            />
            <PasswordVisibilityToggle
              isVisible={isNewPasswordVisible}
              toggleVisibility={handleNewPasswordVisibilityToggle}
            />
          </div>

          <div className="col-span-1 relative">
            <PasswordInputField
              name="newPassword"
              type={isConfirmPasswordVisible ? 'text' : 'password'}
              label="New Password"
              placeholder="New Password"
              validation={{
                required: 'Required',
                minLength: {
                  value: 8,
                  message: 'Password must be at least 8 characters',
                },
                pattern: PASSWORD_VALIDATION_RULE,
              }}
              form={form}
              isShowError={true}
            />
            <PasswordVisibilityToggle
              isVisible={isConfirmPasswordVisible}
              toggleVisibility={handleConfirmPasswordVisibilityToggle}
            />
          </div>

          <div className="col-span-1 relative">
            <PasswordInputField
              name="confirmPassword"
              type={isConfirmPasswordVisible ? 'text' : 'password'}
              label="Confirm Password"
              placeholder="Confirm Password"
              validation={{
                required: 'Required',
                minLength: {
                  value: 8,
                  message: 'Password must be at least 8 characters',
                },
                validate: (value: string) =>
                  value === form.getValues('newPassword') ||
                  'Passwords do not match.',
              }}
              form={form}
              isShowError={true}
            />
            <PasswordVisibilityToggle
              isVisible={isConfirmPasswordVisible}
              toggleVisibility={handleConfirmPasswordVisibilityToggle}
            />
          </div>

          <div className="col-span-2 mt-2 flex flex-row gap-x-4 items-center">
            <AppButton
              label={'Update Password'}
              onClick={handleSubmit(onSubmit)}
              isLoading={isLoading}
              spinnerClass={
                'border-white-500  border-t-transparent animate-spin'
              }
              className="bg-brand-teal-Default hover:bg-brand-teal-hover border-border-brand-teal-Default"
            />
          </div>
          <AppSpinner overlay />
        </div>
      </div>
    </div>
  );
}
