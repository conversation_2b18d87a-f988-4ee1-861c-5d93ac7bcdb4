import AppButton from '@/components/common/app-button';
import In<PERSON><PERSON>ield from '@/components/forms/input-field';
import PhoneInputWidget from '@/components/forms/phone-input-mask';
import { EMAIL_VALIDATION_RULEOptional } from '@/constants/auth-constants';
import { ROLES } from '@/constants/common-constants';
import { removeStorageValue } from '@/lib/utils';
import { setStorageValue } from '@/redux/features/auth/authSlice';
import { setProfile } from '@/redux/features/user-options/loginProfileSlice';
import { RootState } from '@/redux/store';
import { ProfileGeneralInformation } from '@/types/user-options.types';
import { useEffect, useMemo, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import ImageUploadDailog from './ProfileImageDailog';
import { USER_OPTIONS_API } from '@/constants/user-options-constants';
import { useUpdateItemMutation } from '@/redux/features/common-api/common.api';
import AppSpinner from '@/components/common/app-spinner';

export default function GeneralInformation() {
  const form = useFormContext<ProfileGeneralInformation>();
  const [imageModal, setImageModal] = useState(false);
  const role = localStorage.getItem('role') || sessionStorage.getItem('role');
  const profile = useSelector((state: RootState) => state.loginProfile.profile);
  const dispatch = useDispatch();
  const ProfileImage = profile.profileImage ? `${profile.profileImage}` : '';
  const [updateItem, { isLoading }] = useUpdateItemMutation();
  const handleAddImageClick = () => {
    setImageModal(true);
  };

  useEffect(() => {
    if (form.getValues('profileImage')) {
      dispatch(
        setProfile({
          ...profile,
          profileImage: form.getValues('profileImage'),
        })
      );
      setStorageValue('profileImage', form.getValues('profileImage'), true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [form.getValues('profileImage')]);

  const handleDeleteImage = async () => {
    await updateItem({
      url: USER_OPTIONS_API.UPDATE(profile.id),
      data: {
        ...profile,
        profileImage: '',
        updateImage: true,
      },
    }).unwrap();
    form.setValue('profileImage', '');
    dispatch(
      setProfile({
        ...profile,
        profileImage: '',
        updateImage: true,
      })
    );
    removeStorageValue('profileImage');
  };

  // Memoize profile image section
  const profileImageSection = useMemo(() => {
    if (isLoading) {
      return (
        <AppSpinner
          className={'border-white-500 border-t-transparent animate-spin'}
        />
      );
    }

    if (ProfileImage) {
      return (
        <img
          src={ProfileImage}
          alt="Profile"
          loading="lazy"
          className="h-full w-full object-cover rounded-full"
        />
      );
    }

    return (
      <span className="text-6xl">
        {profile?.firstName?.charAt(0)?.toUpperCase() +
          profile?.lastName?.charAt(0)?.toUpperCase()}
      </span>
    );
  }, [ProfileImage, isLoading, profile]); // Dependencies for memoization

  return (
    <div className="flex flex-col justify-between ">
      <div className="flex flex-row gap-5">
        <div className="flex flex-row gap-8 flex-1 p-4 ">
          <div className="flex flex-col items-center gap-4">
            <div className="h-[150px] w-[150px] rounded-full bg-[#E5A000] text-white flex items-center justify-center text-3xl font-bold">
              {profileImageSection}
            </div>
            {!ProfileImage && (
              <AppButton
                label="Add Image"
                variant="neutral"
                className="cursor-pointer text-sm w-full font-medium bg-[#04BCC2] hover:bg-[#37b5b9] text-white "
                onClick={handleAddImageClick}
                isLoading={isLoading}
              />
            )}
            {ProfileImage && (
              <AppButton
                label="Edit Image"
                variant="neutral"
                className="cursor-pointer text-sm w-full font-medium bg-[#04BCC2] hover:bg-[#37b5b9] text-white "
                onClick={handleAddImageClick}
                isLoading={isLoading}
              />
            )}
            {ProfileImage && (
              <AppButton
                label="Remove Image"
                variant="neutral"
                className="cursor-pointer text-sm w-full font-medium "
                onClick={handleDeleteImage}
                isLoading={isLoading}
              />
            )}
          </div>

          {/* Middle Section: Form */}
          <div className="flex-1 grid grid-cols-1 md:grid-cols-2 gap-4">
            <InputField
              name="firstName"
              form={form}
              label="First Name"
              placeholder="Enter First Name"
              maxLength={32}
              validation={{
                required: 'Required',
              }}
            />
            <InputField
              name="lastName"
              form={form}
              label="Last Name"
              placeholder="Enter Last Name"
              maxLength={32}
              validation={{
                required: 'Required',
              }}
            />
            <PhoneInputWidget
              name="phoneWork"
              form={form}
              label="Phone (Work)"
              disabled={role === ROLES.ADMIN}
            />
            <PhoneInputWidget
              name="phoneCell"
              form={form}
              label="Phone (Cell)"
            />
            <div className="col-span-2">
              <PhoneInputWidget
                form={form}
                name="fax"
                label="Fax"
                isFax={true}
                // validation={PHONE_VALIDATION_RULE}
              />
            </div>
            <InputField
              name="emailWork"
              form={form}
              label="Email (Work)"
              placeholder="Enter Email Work"
              disabled
              validation={EMAIL_VALIDATION_RULEOptional}
            />
            <InputField
              name="emailHome"
              form={form}
              label="Email (Home)"
              placeholder="Enter Email Home"
              validation={EMAIL_VALIDATION_RULEOptional}
            />
          </div>
        </div>
      </div>
      <ImageUploadDailog
        isModalOpen={imageModal}
        handleClose={() => {
          setImageModal(false);
        }}
      />
    </div>
  );
}
