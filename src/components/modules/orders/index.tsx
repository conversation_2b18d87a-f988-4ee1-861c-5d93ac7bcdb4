import EditIcon from '@/assets/icons/EditIcon';
import ExcelIcon from '@/assets/icons/ExcelIcon';
import AppButton from '@/components/common/app-button';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import AppDataTable from '@/components/common/app-data-table';
import { AppTableContext } from '@/components/common/app-data-table/AppTableContext';
import AppSpinner from '@/components/common/app-spinner';
import StatusBadge from '@/components/common/app-status-badge';
import ColumnReOrdering from '@/components/common/column-reording';
import ActionColumnMenu from '@/components/common/data-tables/ActionColumn';
import CustomDialog from '@/components/common/dialog';
import Filter from '@/components/modules/orders/Filter';
import { Button } from '@/components/ui/button';
import { UseToast } from '@/components/ui/toast/ToastContainer';
import { ORDERS_API_ROUTES } from '@/constants/api-constants';
import { typeColors } from '@/constants/order-constants';
import { ROUTES } from '@/constants/routes-constants';
import { useDownloadFile } from '@/hooks/useDownloadFile';
import { hasEnabledPermission } from '@/lib/hasEnabledPermission';
import {
  cn,
  convertToFloat,
  getPaginationObject,
  getQueryParam,
  updateQueryParam,
} from '@/lib/utils';
import { useAddNewItemMutation } from '@/redux/features/common-api/common.api';
import { useGetListQuery } from '@/redux/features/list/category/list.api';
import { useSubRentByOrderIdMutation } from '@/redux/features/orders/item-details.api';
import { useCheckInactiveItemsMutation } from '@/redux/features/orders/order.api';
import {
  clearAllFilters,
  clearFilter,
  setFilter,
} from '@/redux/features/orders/orderSlice';
import { RootState } from '@/redux/store';
import { OrderInformationTypes } from '@/types/order.types';
import { Row } from '@tanstack/react-table';
import {
  Calendar,
  CalendarDays,
  ChevronDown,
  CircleAlert,
  CreditCard,
  LayoutList,
  ListCheck,
  Map,
  PackageCheck,
  PenTool,
  PlusIcon,
  Signature,
  TrashIcon,
  Truck,
  Wrench,
} from 'lucide-react';
import { useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import PrintEmailOrder from './print-email-order';
import SubRentDialogContent from './SubRentDialogContent';
import DeleteOrder from './view/delete-order';

interface DialogTypes {
  state: boolean;
  id: number | null;
  type?: 'subrent' | 'selectedOrders';
}

const Orders = () => {
  const search = getQueryParam('search') as string;
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { sorting } = useContext(AppTableContext); // Use pagination from context
  const [openDeleteDialog, setOpenDeleteDialog] = useState<boolean>(false);
  const [openSelectedOrdersDialog, setOpenSelectedOrdersDialog] =
    useState<boolean>(false);

  const [orderDetail, setOrderDetail] = useState<OrderInformationTypes | null>(
    null
  );

  const [isFilterOpen, setIsFilterOpen] = useState<boolean>(false);
  const [openColumnOrdering, setOpenColumnOrdering] = useState<boolean>(false);
  const [openSubRental, setOpenSubRental] = useState<DialogTypes>({
    state: false,
    id: null,
  });
  const [openInactiveDialog, setOpenInactiveDialog] = useState<DialogTypes>({
    state: false,
    id: null,
  });
  const [checkInactiveItems, { data: inactiveItems }] =
    useCheckInactiveItemsMutation();

  const [
    getSubRentByOrderId,
    { data: subRentData, isLoading: isSubRentLoading },
  ] = useSubRentByOrderIdMutation();

  const [searchParams, setSearchParams] = useState<string>('');
  const [refresh, setRefresh] = useState<boolean>(false);
  const filter = useSelector((state: RootState) => state.orders.filters);
  const { data, refetch, isFetching } = useGetListQuery({
    url: ORDERS_API_ROUTES.GET_COLUMN,
  });
  const [addNewItem, { isLoading: newItemLoading }] = useAddNewItemMutation();

  const { downloadFile } = useDownloadFile();
  const toast = UseToast();

  // Check if the user has the "Order Entry Read Only" permission enabled
  const readOnlyPermission = hasEnabledPermission('Order Entry Read only');

  // toggle delete
  const toggleDelete = useCallback(
    (orderData?: OrderInformationTypes | null, success?: boolean) => {
      if (success) {
        setRefresh(true);
        setTimeout(() => setRefresh(false), 1000);
      }
      setOrderDetail(orderData || null);
      setOpenDeleteDialog((prevState) => !prevState);
    },
    []
  );

  // navigate to info tab
  const navigateToEditOrder = useCallback(() => {
    navigate(`${ROUTES.ADD_ORDERS}?tab=information`);
  }, [navigate]);

  // navigate to Availability Calendar
  const navigateToAvailabilityCalendar = useCallback(() => {
    navigate(`${ROUTES.AVAILABILITY_CALENDAR}`);
  }, [navigate]);

  // navigate to Busy Calendar
  const navigateToBusyCalendar = useCallback(() => {
    navigate(`${ROUTES.BUSY_CALENDAR}`);
  }, [navigate]);

  const componentMap: any = useMemo(
    () => ({
      StatusBadge: ({ value }: any) => <StatusBadge status={value} />,
      LabelDollar: ({ value }: any) => convertToFloat({ value, prefix: '$' }),
    }),
    []
  );

  const toggleSubRentalDialog = useCallback(() => {
    setOpenSubRental({ state: false, id: null, type: 'subrent' });
  }, []);

  const onOpenChangeSelectedOrdersDialog = useCallback(() => {
    setOpenSelectedOrdersDialog((prevState) => !prevState);
  }, [setOpenSelectedOrdersDialog]);

  const handleSubRentalClose = async () => {
    const id = openSubRental?.id;
    toggleSubRentalDialog();

    if (!id) return;

    try {
      const result = await checkInactiveItems(id).unwrap();
      if (result?.data?.length) {
        setOpenInactiveDialog({ state: true, id: id });
      } else {
        navigate(`${ROUTES.EDIT_ORDERS}?id=${id}&tab=information`);
      }
    } catch (e) {}
  };

  // Handle Sub Rent
  const handleSubRental = useCallback(
    async (id: number, isOrderHasSubrentItem: boolean) => {
      const navigateToEdit = () =>
        navigate(`${ROUTES.EDIT_ORDERS}?id=${id}&tab=information`);

      if (isOrderHasSubrentItem) {
        setOpenSubRental({ state: true, id, type: 'subrent' });
        await getSubRentByOrderId(id);
      } else {
        // No subrent, check for inactive items
        try {
          const result = await checkInactiveItems(id).unwrap();
          if (result?.data?.length) {
            setOpenInactiveDialog({
              state: true,
              id,
            });
          } else {
            navigateToEdit();
          }
        } catch (error) {}
      }
    },
    [checkInactiveItems, getSubRentByOrderId, navigate]
  );

  // Open for selected orders
  // const handleSelectedOrdersDialog = () => {
  //   setOpenSubRental({ state: true, id: null, type: 'selectedOrders' });
  // };

  const tableColumns = useMemo(() => {
    return data?.data;
  }, [data?.data]);

  const handleOrderingColumn = useCallback(
    async (formData: any) => {
      const updatedColumns = tableColumns.map((item: any) => ({
        ...item,
        enabled: formData[item.accessorKey],
      }));

      await addNewItem({
        url: ORDERS_API_ROUTES.UPDATE_COLUMN,
        data: updatedColumns,
      }).unwrap();
      setOpenColumnOrdering(false);
      await refetch();
    },
    [addNewItem, refetch, tableColumns]
  );

  const memoizedColumns = useMemo(() => {
    if (!tableColumns?.length) return [];

    const formattedColumns = tableColumns
      .filter((column: any) => column.enabled) // Ensure only enabled columns are included
      .map((column: any) => ({
        accessorKey: column.accessorKey,
        header: column.header,
        enableSorting: column.enableSorting || false,
        size: column.size || 200, // Default size if not provided
        ...(column.component === 'OrderType' && {
          className: (row: Row<any>) => {
            const value = row.original?.orderType;
            return row.original?.isDeleted ? 'bg-red-300' : typeColors[value];
          },
        }),
        cell: ({ row }: any) => {
          const Component = componentMap[column.component]; // Get the component based on the key
          return Component ? (
            <Component value={row?.original?.[column.accessorKey]} />
          ) : (
            row?.original?.[column.accessorKey]
          );
        },
      }));

    return [
      {
        accessorKey: 'ordering',
        header: () => (
          <ColumnReOrdering
            isOpen={openColumnOrdering}
            handleOrderingColumn={handleOrderingColumn}
            tableColumns={tableColumns}
            setOpenColumnOrdering={setOpenColumnOrdering}
          />
        ),
        size: 50,
      },
      ...formattedColumns,
      {
        id: 'action',
        size: 110,
        header: 'Actions',
        cell: ({ row }: any) => {
          const isDeleted = row.original?.isDeleted;

          return (
            <ActionColumnMenu
              customEdit={
                <button
                  onClick={() =>
                    handleSubRental(
                      row?.original?.id,
                      row?.original?.isOrderHasSubrentItem
                    )
                  }
                  className="flex items-center justify-center gap-2 border border-text-tertiary rounded-md px-3 py-1"
                >
                  <EditIcon />
                  <span className="w-24 text-left">
                    {isDeleted ? 'View' : 'Edit'} details
                  </span>
                </button>
              }
              dropdownMenuList={[
                isDeleted
                  ? {
                      label: 'Deletion Info',
                      onClick: () => toggleDelete(row.original),
                      icon: <CircleAlert className="h-5 w-5" />,
                      className: 'text-base',
                    }
                  : {
                      label: 'Delete',
                      onClick: () => toggleDelete(row.original),
                      icon: <TrashIcon className="h-5 w-5" />,
                      className: 'text-base text-text-danger',
                    },
              ]}
            />
          );
        },
      },
    ];
  }, [
    tableColumns,
    componentMap,
    openColumnOrdering,
    handleOrderingColumn,
    handleSubRental,
    toggleDelete,
  ]);

  // handle Clear function
  const handleClearFilter = useCallback(
    (key: string) => {
      dispatch(clearFilter(key));
      setIsFilterOpen(false);
      if (search) {
        updateQueryParam(null, 'search');
      }
    },
    [dispatch, search]
  );

  const handleExtractOrdersToExcel = useCallback(() => {
    const normalizedFilters = Array.isArray(filter)
      ? filter.map((f: any) => ({
          field: f.name || f.label,
          value: f.value,
          operator: f.operator,
        }))
      : [];
    const payload = getPaginationObject({
      pagination: { pageIndex: 0, pageSize: 0 },
      sorting,
      filters: [
        ...normalizedFilters,
        { field: 'filter', value: searchParams, operator: 'Contains' },
      ],
    });
    const response = downloadFile({
      url: ORDERS_API_ROUTES.EXPORT_CSV,
      method: 'POST',
      body: payload,
    });
    toast.promise(response, {
      loading: 'Downloading file...',
      success: 'File downloaded successfully.',
      error: 'Failed to download file',
    });
  }, [filter, sorting, searchParams, downloadFile, toast]);

  // Custom toolbar component
  const CustomToolbar = (
    <div className="flex space-x-3">
      <AppButton
        icon={PlusIcon}
        iconClassName="w-5 h-5"
        label="New Order"
        onClick={navigateToEditOrder}
        disabled={readOnlyPermission}
      />
      <ActionColumnMenu
        triggerClassName="w-fit hover:bg-white"
        triggerContent={
          <div>
            <AppButton
              label={
                <div className="flex items-center gap-2">
                  <Wrench className="w-5 h-5" />
                  Other Processes
                  <ChevronDown className="w-5 h-5" />
                </div>
              }
              variant="neutral"
            />
          </div>
        }
        contentClassName="p-2 w-fit"
        dropdownMenuList={[
          {
            label: 'Inventory Manager',
            onClick: () => navigate(ROUTES.INVENTORY_MANAGER),
            icon: <Truck />,
          },
          {
            label: 'Overbooked Item Info',
            onClick: () => navigate(ROUTES.OVERBOOKED_ITEM_INFO),
            icon: <PenTool />,
          },
          {
            label: 'Item Inquiry',
            onClick: () => navigate(ROUTES.ITEM_INQUIRY),
            icon: <LayoutList />,
          },
          {
            label: 'Availability Calendar',
            onClick: () => navigateToAvailabilityCalendar(),
            icon: <Calendar />,
          },
          {
            label: 'Busy Calendar',
            onClick: () => navigateToBusyCalendar(),
            icon: <CalendarDays />,
          },
          {
            label: 'E-sign Documents',
            onClick: () => navigate(ROUTES.E_SIGN_DOCUMENTS),
            icon: <Signature />,
          },
          {
            label: 'Online Payments',
            onClick: () => navigate(ROUTES.ONLINE_PAYMENTS),
            icon: <CreditCard />,
          },
          {
            label: 'Dispatch Track',
            onClick: () => {},
            icon: <Map />,
          },
        ]}
      />
    </div>
  );

  const DropdownMenu = useMemo(() => {
    return [
      // {
      //   label: 'Print Options',
      //   icon: <PrinterIcon />,
      //   subMenu: [
      //     {
      //       label: 'Option 1',
      //       onClick: () => {},
      //       icon: <PrinterIcon />,
      //     },
      //     {
      //       label: 'Option 2',
      //       onClick: () => {},
      //       icon: <PrinterIcon />,
      //     },
      //   ],
      // },
      {
        label: 'Selected Orders',
        onClick: () => onOpenChangeSelectedOrdersDialog(),
        icon: <ListCheck />,
      },
      {
        label: 'Order List',
        onClick: () => {},
        icon: <PackageCheck />,
      },
      {
        label: 'Extract Orders',
        onClick: handleExtractOrdersToExcel,
        icon: <ExcelIcon />,
      },
    ];
  }, [handleExtractOrdersToExcel, onOpenChangeSelectedOrdersDialog]);

  useEffect(() => {
    if (search) {
      dispatch(
        setFilter([
          {
            label: 'Customer',
            value: search,
            name: 'customer',
            tagValue: search,
            operator: 'equals',
          } as any,
        ])
      );
    }
  }, [dispatch, search]);

  useEffect(() => {
    if (!search) {
      dispatch(clearAllFilters());
    }
  }, [dispatch, search]);

  const renderDialogContent = () => {
    switch (openSubRental.type) {
      case 'subrent':
        return (
          <SubRentDialogContent
            data={subRentData?.data ?? []}
            isLoading={isSubRentLoading}
          />
        );

      case 'selectedOrders':
        return (
          <PrintEmailOrder
            open={openSelectedOrdersDialog}
            onOpenChange={onOpenChangeSelectedOrdersDialog}
          />
        );

      default:
        return null;
    }
  };

  return (
    <div className="flex flex-col p-4 gap-6">
      <AppDataTable
        url={ORDERS_API_ROUTES.ALL}
        columns={memoizedColumns}
        enableSearch={true}
        searchKey="filter"
        heading="Orders"
        setSearchParams={setSearchParams}
        enableFilter
        filter={filter}
        handleClearFilter={handleClearFilter}
        filterClassName="w-[550px]"
        filterContent={<Filter setIsFilterOpen={setIsFilterOpen} />}
        setIsFilterOpen={setIsFilterOpen}
        isFilterOpen={isFilterOpen}
        customToolBar={CustomToolbar}
        enablePagination={true}
        refreshList={refresh}
        tableClassName="max-h-[580px] overflow-auto"
        dropdownMenus={DropdownMenu}
        dropdownContentClassName="p-4 flex flex-col gap-2 w-full"
      />

      <AppSpinner overlay isLoading={newItemLoading || isFetching} />
      {openDeleteDialog && (
        <DeleteOrder
          open={openDeleteDialog}
          onOpenChange={(success) => toggleDelete(null, success)}
          orderData={orderDetail}
        />
      )}

      {openSelectedOrdersDialog && (
        <PrintEmailOrder
          open={openSelectedOrdersDialog}
          onOpenChange={onOpenChangeSelectedOrdersDialog}
        />
      )}

      <CustomDialog
        title={
          openSubRental.type === 'subrent' ? 'Sub Rentals' : 'Selected Orders'
        }
        onOpenChange={handleSubRentalClose}
        description=""
        open={openSubRental.state}
        className={cn('min-w-[70%] 2xl:min-w-[55%]')}
        contentClassName="h-[450px] 2xl:h-[400px] overflow-y-auto"
        footer={
          <Button className="w-24" onClick={handleSubRentalClose}>
            Close
          </Button>
        }
        footerClassName="p-4"
      >
        <div className="flex flex-col gap-2 px-4 py-2">
          {renderDialogContent()}
        </div>
      </CustomDialog>
      <AppConfirmationModal
        title={'Warning'}
        description={
          <>
            <div>The following items on this order are marked as inactive:</div>
            <div className="flex flex-col gap-1 mb-1 max-h-[400px] overflow-auto ">
              {inactiveItems?.data?.map((item: string, index: number) => (
                <div key={index}>
                  <span>({item})</span>&nbsp;
                  {item}
                </div>
              ))}
            </div>
          </>
        }
        open={openInactiveDialog.state}
        handleSubmit={() => {
          setOpenInactiveDialog({ state: false, id: null });
          navigate(
            `${ROUTES.EDIT_ORDERS}?id=${openInactiveDialog.id}&tab=information`
          );
        }}
        submitLabel="OK"
      />
    </div>
  );
};

export default Orders;
