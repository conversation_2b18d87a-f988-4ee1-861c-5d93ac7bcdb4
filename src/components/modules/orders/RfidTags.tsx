import DataTable from '@/components/common/data-tables';
import { RfidTagsType } from '@/types/order.types';
import { ColumnDef } from '@tanstack/react-table';
import { useMemo } from 'react';
// import { useFormContext } from 'react-hook-form';

export default function RfidTags() {
  //   const form = useFormContext<RfidTagsType>();

  const columns: ColumnDef<RfidTagsType>[] = useMemo(
    () => [
      {
        accessorKey: 'itemID',
        header: 'Item ID',
      },
      {
        accessorKey: 'description',
        header: 'Description',
      },
      {
        accessorKey: 'location',
        header: 'Location',
      },
      {
        accessorKey: 'quantity',
        header: 'Qty',
      },
      {
        accessorKey: 'tagID',
        header: 'Tag ID',
      },
      {
        accessorKey: 'returned',
        header: 'Returned',
      },
      {
        accessorKey: 'status',
        header: 'Status',
      },
      {
        accessorKey: 'lastOrder',
        header: 'last Order',
      },
    ],
    []
  );

  return (
    <>
      <div className="grid grid-cols-1 gap-6 ">
        <h3 className="text-xl font-semibold text-text-Default">RFID Tags</h3>
        <DataTable
          data={[]}
          columns={[...columns]} // Adding the Action column
          noDataPlaceholder="No Data Found."
        />
      </div>
    </>
  );
}
