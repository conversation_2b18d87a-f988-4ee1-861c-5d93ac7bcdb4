/* eslint-disable react-hooks/exhaustive-deps */
import CheveronLeft from '@/assets/icons/CheveronLeft';
import CopyIcon from '@/assets/icons/CopyIcon';
import PrinterIcon from '@/assets/icons/PrinterIcon';
import TrashIcon from '@/assets/icons/TrashIcon';
import AppButton from '@/components/common/app-button';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import AppSpinner from '@/components/common/app-spinner';
import AppTabsVertical from '@/components/common/app-tabs-vertical';
import ActionColumnMenu from '@/components/common/data-tables/ActionColumn';
import IconButton from '@/components/common/icon-button';
import TooltipWidget from '@/components/common/tooltip-widget';
import { orderTabList } from '@/constants/order-constants';
import { ROUTES } from '@/constants/routes-constants';
import { getModifiedItems } from '@/lib/getModifiedItems';
import { hasEnabledPermission } from '@/lib/hasEnabledPermission';
import {
  DATE_FORMAT_YYYYMMDD,
  formatDate,
  getQueryParam,
  updateQueryParam,
} from '@/lib/utils';
import {
  useSaveItemDetailsMutation,
  useSaveSalesReferralsMutation,
} from '@/redux/features/orders/item-details.api';
import {
  useCloneOrderMutation,
  useGetOrderByIdQuery,
  useSaveOrderStatusMutation,
  useSaveTotalPaymentsMutation,
  useUpdateDeleveryChargeMutation,
  useUpdateOrderMutation,
} from '@/redux/features/orders/order.api';

import OrderSessionTimeout from '@/components/common/OrderSessionTimeout';
import { useUnlockOrder } from '@/hooks/useUnlockOrder';
import { useSaveOrderAdditionalInfoMutation } from '@/redux/features/orders/additional-info.api';
import { useGetUserDefaultStoreQuery } from '@/redux/features/store/store.api';
import {
  AdditionalInfoTypes,
  AllTabTypeMap,
  ItemFormDataTypes,
  ItemListTypes,
  OrderInformationTypes,
  ORDERT_TYPE_TAB,
  SalesReferralsType,
  StatusTypes,
  TotalPaymentsTypes,
} from '@/types/order.types';
import dayjs from 'dayjs';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import { isEqual } from 'lodash';
import {
  CircleAlert,
  DollarSign,
  File,
  FileClock,
  Folder,
  History,
  ListCheck,
  Milestone,
  SaveIcon,
  X,
} from 'lucide-react';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { FormProvider, SubmitHandler, useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import {
  createPayload,
  generateOrdersDefaultValues,
  ORDER_TYPE,
  ORDERS_API_ROUTES_MAP,
  ORDERS_ENUM,
} from './constants';
import MissingEquipment from './missing-equipment';
import PrintEmailOrder from './print-email-order';
import Checklist from './view/checklist';
import DeleteOrder from './view/delete-order';
import Directions from './view/directions';
import LinkedFiles from './view/linked-files/linkedFiles';
import OrderHistory from './view/order-history';
import OrderNotes from './view/quick-notes/orderNotes';

type ModalState = {
  orderNotes: boolean;
  linkedFiles: boolean;
  checklist: boolean;
  copyOrderInfo: boolean;
  createME: boolean;
  orderHistory: boolean;
  deleteOrder: boolean;
  directions: boolean;
  copyOrderInfoItems: boolean;
  showMissingOrderAlert?: boolean;
  printEmailOrder: boolean;
};
type ModalKeys = keyof ModalState;

export const AddEditOrders = () => {
  const id = getQueryParam('id') as string;
  const getCustomerId = getQueryParam('custId') as string;

  const tabName = (getQueryParam('tab') ||
    'information') as keyof AllTabTypeMap;
  const navigation = useNavigate();
  const [activeTab, setActiveTab] = useState<keyof AllTabTypeMap>(tabName);
  const hasShownWarningRef = useRef(false);
  const [isDeliveryChargeUpdated, setIsDeliveryChargeUpdated] =
    useState<boolean>(false);

  const [modalState, setModalState] = useState<ModalState>({
    orderNotes: false,
    linkedFiles: false,
    checklist: false,
    copyOrderInfo: false,
    createME: false,
    orderHistory: false,
    deleteOrder: false,
    directions: false,
    copyOrderInfoItems: false,
    showMissingOrderAlert: false,
    printEmailOrder: false,
  });

  const [deliveryChargeModel, setDeliveryChargeModel] = useState<{
    state: boolean;
    data: null | { onConfirm: () => void };
  }>({
    state: false,
    data: null,
  });

  const [useBillingLocation, setUseBillingLocationModel] = useState<{
    state: boolean;
    data: null | { onConfirm: () => void };
  }>({
    state: false,
    data: null,
  });

  const toggleModal = useCallback((key: ModalKeys) => {
    setModalState((prev) => ({ ...prev, [key]: !prev[key] as boolean }));
  }, []);

  // // clone Order
  const [cloneOrderInfo, { isLoading: cloneOrderLoading }] =
    useCloneOrderMutation();

  const [updateDeleveryCharge, { isLoading: updateDeleveryChargeLoading }] =
    useUpdateDeleveryChargeMutation();

  const {
    data: orderData,
    isLoading: orderIsLoading,
    refetch,
  } = useGetOrderByIdQuery(id, {
    skip: !id,
    refetchOnMountOrArgChange: true,
  });

  const [addUpdateOrder, { isLoading }] = useUpdateOrderMutation();

  const { data: storeLocationData, isLoading: storeLoading } =
    useGetUserDefaultStoreQuery(undefined, {
      // skip: Boolean(id),
      refetchOnMountOrArgChange: true,
    });

  const requestedCreditCardConvenienceFees =
    storeLocationData?.data?.storePayment?.creditCardConvenienceFees;
  const requestedCreditCardConvFees = Number(
    requestedCreditCardConvenienceFees
  );

  const requestedDebitCardConvenienceFees =
    storeLocationData?.data?.storePayment?.debitCardConvenienceFees;
  const requestedDebitCardConvFees = Number(requestedDebitCardConvenienceFees);

  const requestedACHConvenienceFees =
    storeLocationData?.data?.storePayment?.achConvenienceFees;
  const requestedACHConvFees = Number(requestedACHConvenienceFees);

  const defaultValues = useMemo(() => {
    const {
      id,
      countryId: storeCountryId,
      storeMiscOrderSetting,
      storeDateCalculation,
      storeOrderItemSetting,
      storeWareHouse,
    } = storeLocationData?.data ?? {};
    const dataValue = orderData?.data;
    // Check if the user has the "Order Entry Read Only" permission enabled
    const readOnlyPermission = hasEnabledPermission('Order Entry Read only');
    // Check if the order is already posted
    const isPosted = dataValue?.status === 'POSTED';

    const baseValues = {
      ...dataValue,
      storeLocationId: dataValue?.storeLocationId ?? id ?? null,
      orderType:
        dataValue?.orderType ?? storeMiscOrderSetting?.defaultOrderType ?? null,
      deliveryTypeId:
        dataValue?.deliveryTypeId ??
        storeMiscOrderSetting?.defaultDeliveryType ??
        null,
      storeDateCalculation,
      userDefaultStoreInfo: {
        storeCountryId,
        ...storeMiscOrderSetting,
        ...storeOrderItemSetting,
      },
      recalculateDate: false,
      surgePricing: storeOrderItemSetting?.surgePricing,
      shippingManager: storeWareHouse?.shippingManager,

      // Enable read-only mode if user has read-only permission or order is posted
      orderEntryReadOnly: readOnlyPermission || isPosted,

      selectedOrderType:
        dataValue?.deliveryOrder &&
        Object.keys(dataValue.deliveryOrder || {}).length > 0
          ? ORDERT_TYPE_TAB.DELIVER
          : dataValue?.shipOrder &&
              Object.keys(dataValue.shipOrder || {}).length > 0
            ? ORDERT_TYPE_TAB.SHIP
            : dataValue?.willCallOrder &&
                Object.keys(dataValue.willCallOrder || {}).length > 0
              ? ORDERT_TYPE_TAB.WILL_CALL
              : (() => {
                  switch (dataValue?.orderType) {
                    case ORDERT_TYPE_TAB.DELIVER:
                      return ORDERT_TYPE_TAB.DELIVER;
                    case ORDERT_TYPE_TAB.SHIP:
                      return ORDERT_TYPE_TAB.SHIP;
                    case ORDERT_TYPE_TAB.WILL_CALL:
                      return ORDERT_TYPE_TAB.WILL_CALL;
                    default:
                      return null;
                  }
                })(),
    };

    return generateOrdersDefaultValues(baseValues);
  }, [orderData?.data, storeLocationData]);

  // Initialize form hook
  const form = useForm<OrderInformationTypes>({
    defaultValues: defaultValues,
  });
  const { handleSubmit, reset } = form;
  const isDeleted = defaultValues?.isDeleted;
  const isDelete = form.watch('isDeleted');

  const isOrderEntryReadOnly = form.watch('orderEntryReadOnly');

  // Automatically unlocks the order when the component unmounts,
  // only if the order is not marked as deleted.
  useUnlockOrder(isDeleted);

  // Initialize the form for the "Item Details" tab using react-hook-form
  const itemDetailsForm = useForm<ItemListTypes>({
    mode: 'onChange',
  });
  const totalPaymentsForm = useForm<TotalPaymentsTypes>({
    mode: 'onChange',
  });
  const orderStatusForm = useForm<StatusTypes>({
    mode: 'onChange',
  });
  const orderAdditionalInfoForm = useForm<AdditionalInfoTypes>({
    mode: 'onChange',
  });
  const orderSalesReferralsForm = useForm<SalesReferralsType>({
    mode: 'onChange',
  });

  useEffect(() => {
    if (
      !hasShownWarningRef.current &&
      activeTab === 'information' &&
      (orderData?.data?.showNoDeliveryChargeWarning ||
        orderData?.data?.showShipInfoWarning)
    ) {
      if (orderData.data.showNoDeliveryChargeWarning) {
        setDeliveryChargeModel({ state: true, data: null });
      } else if (orderData.data.showShipInfoWarning) {
        setUseBillingLocationModel({ state: true, data: null });
      }

      hasShownWarningRef.current = true;
    }
  }, [
    orderData?.data,
    activeTab,
    orderData?.data?.showNoDeliveryChargeWarning,
    orderData?.data?.showShipInfoWarning,
    hasShownWarningRef,
  ]);

  const resetWarning = () => {
    hasShownWarningRef.current = false;
  };

  // Save Item Details Order
  const [saveItemDetails, { isLoading: isSaveItemDetailsLoading }] =
    useSaveItemDetailsMutation();

  // Save Order Status Details
  const [saveStatusDetails, { isLoading: issaveStatusDetailsLoading }] =
    useSaveOrderStatusMutation();

  // Save Total Payment
  const [saveTotalPayments, { isLoading: isLoadingTotalPayments }] =
    useSaveTotalPaymentsMutation();

  // Save Additional Info
  const [saveAdditionalInfo, { isLoading: isLoadingAdditionalInfo }] =
    useSaveOrderAdditionalInfoMutation();

  // Save Sales Referrals
  const [saveSalesReferrals, { isLoading: isLoadingSalesReferrals }] =
    useSaveSalesReferralsMutation();

  const [itemDefaultValues, setitemDefaultValues] =
    useState<ItemListTypes | null>(null);
  const onDefaultValuesLoaded = useCallback((defaults: ItemListTypes) => {
    setitemDefaultValues(defaults);
  }, []);
  const currentItems = itemDetailsForm?.watch('items')?.map((item) => {
    return {
      ...item,
      quantity: Number(item?.quantity),
    };
  });
  const isItemModified = useMemo(() => {
    return isEqual(currentItems, itemDefaultValues?.items);
  }, [JSON.stringify(currentItems), JSON.stringify(itemDefaultValues?.items)]);
  const isItemTab = activeTab === (ORDERS_ENUM.ITEM_DETAILS as string);
  const isSaveDisabled = isItemTab ? isItemModified : false;

  // Reset form when default values or tab changes
  useEffect(() => {
    if (defaultValues && !getCustomerId) {
      reset(defaultValues);
    }
  }, [defaultValues, reset]);

  dayjs.extend(isSameOrBefore);

  const isMissingReturnOrder = ['MISSING_ORDER', 'RETURN_ORDER']?.includes(
    defaultValues?.originType
  );

  // Pre-requisites for create missing order
  const isRentalOrder = [
    ORDER_TYPE.RENTAL_ORDER,
    ORDER_TYPE.RENTAL_QUOTE,
  ]?.includes(defaultValues?.orderType);
  const isDateInFuture = dayjs(defaultValues?.dateOfUseFrom).isAfter(
    dayjs(),
    'day'
  );

  const handleSave = async (
    formData: AllTabTypeMap[typeof activeTab] | any
  ) => {
    try {
      // Proceed with save
      const url = id
        ? ORDERS_API_ROUTES_MAP[activeTab]?.UPDATE
        : ORDERS_API_ROUTES_MAP[activeTab]?.CREATE;

      if (!url) {
        throw new Error('No URL available for the requested operation');
      }
      const payload = createPayload(activeTab, formData);

      const { data } = await addUpdateOrder({
        url,
        body: payload,
        method: 'POST',
      });
      const orderId = data?.data?.id;

      if (!id && orderId) {
        updateQueryParam(orderId);
      }
      if (data?.statusCode === 200) {
        getCustomerId && updateQueryParam(null, 'custId');
        refetch();
      }
    } catch (error) {
      // Handle error (could display error message to user)
    }
  };

  const toggleModalDeliveryCharge = useCallback(() => {
    setDeliveryChargeModel({ state: false, data: null });
    setUseBillingLocationModel({ state: false, data: null });
  }, []);

  const shouldDisableMissingOrder = !isRentalOrder || isDateInFuture;

  const onSubmit: SubmitHandler<AllTabTypeMap[typeof activeTab] | any> = async (
    formData
  ) => {
    // const storeDeliveryCharges = storeLocationData?.data?.storeDeliveryCharges;
    // const defaultCharges = storeDeliveryCharges?.defaultCharges;
    // const displayDcWarning = storeDeliveryCharges?.displayDcWarning;

    // if (defaultCharges === 0 && displayDcWarning === true) {
    //   // Show confirmation popup
    //   setDeliveryChargeModel({
    //     ...deliveryChargeModel,
    //     state: !deliveryChargeModel.state,
    //     data: formData,
    //   });
    // } else {
    // }
    handleSave(formData);
    resetWarning();
  };

  const handleDcWarningConfirm = () => {
    deliveryChargeModel.data?.onConfirm?.();
    handleSave(deliveryChargeModel.data);
    toggleModalDeliveryCharge();
  };

  const handleCloseBillingLocationModal = () => {
    toggleModalDeliveryCharge();
  };

  const handleSubmitBillingLocation = async () => {
    await updateDeleveryCharge(id);
    setIsDeliveryChargeUpdated(true);
    useBillingLocation.data?.onConfirm?.();
    setUseBillingLocationModel({
      state: false,
      data: null,
    });
  };

  const handleCopy = useCallback(async () => {
    if (id)
      try {
        const { data } = await cloneOrderInfo({
          id: Number(id),
          isCopy: modalState.copyOrderInfoItems,
        }).unwrap(); // make sure cloneStore returns a promise
        const copyOrderId = data?.id;
        copyOrderId && updateQueryParam(copyOrderId);
        toggleModal('copyOrderInfo');
        if (modalState.copyOrderInfoItems) toggleModal('copyOrderInfoItems');
      } catch (err) {}
  }, [cloneOrderInfo, id, modalState.copyOrderInfoItems, toggleModal]);

  // Order Drop down menu
  const OrderDropdownMenu = useMemo(
    () => [
      {
        label: 'Print / Email Order',
        onClick: () => toggleModal('printEmailOrder'),
        icon: <PrinterIcon />,
        disabled: isDelete,
      },
      {
        label: 'Copy Order',
        icon: <CopyIcon className="h-5 w-5" />,
        subMenu: [
          {
            label: 'Order Info Only',
            onClick: () => toggleModal('copyOrderInfo'),
            icon: <CopyIcon className="h-5 w-5" />,
            disabled: isDelete || isOrderEntryReadOnly,
          },
          {
            label: 'Order Info & Items',
            onClick: () => {
              toggleModal('copyOrderInfo');
              toggleModal('copyOrderInfoItems');
            },
            icon: <CopyIcon className="h-5 w-5" />,
            disabled: isDelete || isOrderEntryReadOnly,
          },
        ],
      },
      {
        label: 'Create M / E',
        onClick: () =>
          toggleModal(
            shouldDisableMissingOrder ? 'showMissingOrderAlert' : 'createME'
          ),
        icon: <FileClock className="h-5 w-5" />,
        disabled: isDelete || isOrderEntryReadOnly || isMissingReturnOrder,
      },
      {
        label: 'Order History',
        onClick: () => toggleModal('orderHistory'),
        icon: <History className="h-5 w-5" />,
      },
      {
        label: 'Sub Rentals',
        onClick: () => {},
        icon: <DollarSign className="h-5 w-5" />,
        disabled: isDelete,
      },
      {
        label: 'Directions',
        onClick: () => toggleModal('directions'),
        icon: <Milestone className="h-5 w-5" />,
        disabled: isDelete,
      },
      isDeleted
        ? {
            label: 'Deletion Info',
            onClick: () => toggleModal('deleteOrder'),
            icon: <CircleAlert className="h-5 w-5" />,
            disabled: isOrderEntryReadOnly,
          }
        : {
            label: 'Delete Order',
            onClick: () => toggleModal('deleteOrder'),
            icon: <TrashIcon />,
            className: 'text-base text-text-danger',
            disabled: isOrderEntryReadOnly,
          },
    ],
    [
      shouldDisableMissingOrder,
      isDelete,
      isDeleted,
      isOrderEntryReadOnly,
      isMissingReturnOrder,
      toggleModal,
    ]
  );

  // handle on change tab
  const handleTabChange = useCallback((value: keyof AllTabTypeMap) => {
    setActiveTab(value);
    updateQueryParam(value, 'tab');
  }, []);

  // navigate to back
  const navigateToOrder = useCallback(() => {
    navigation(ROUTES.ORDERS);
  }, [navigation]);

  const Modals = useCallback(
    () => (
      <>
        {modalState.orderNotes && (
          <OrderNotes
            open={modalState.orderNotes}
            onOpenChange={() => toggleModal('orderNotes')}
          />
        )}
        {modalState.linkedFiles && (
          <LinkedFiles
            open={modalState.linkedFiles}
            onOpenChange={() => toggleModal('linkedFiles')}
          />
        )}
        {modalState.checklist && (
          <Checklist
            open={modalState.checklist}
            onOpenChange={() => toggleModal('checklist')}
          />
        )}
        {modalState.createME && (
          <MissingEquipment
            open={modalState.createME}
            setOpen={() => toggleModal('createME')}
            customerName={defaultValues?.billTo?.name}
            customerId={defaultValues?.billTo?.customerId?.value}
            orderNo={defaultValues?.orderNo}
          />
        )}
        {modalState.orderHistory && (
          <OrderHistory
            open={modalState.orderHistory}
            onOpenChange={() => toggleModal('orderHistory')}
          />
        )}
        {modalState.deleteOrder && (
          <DeleteOrder
            open={modalState.deleteOrder}
            onOpenChange={() => toggleModal('deleteOrder')}
          />
        )}
        {modalState.directions && (
          <Directions
            open={modalState.directions}
            onOpenChange={() => toggleModal('directions')}
          />
        )}
        {modalState.printEmailOrder && (
          <PrintEmailOrder
            open={modalState.printEmailOrder}
            onOpenChange={() => toggleModal('printEmailOrder')}
            eSign
          />
        )}
      </>
    ),
    [
      defaultValues?.billTo?.customerId?.value,
      defaultValues?.billTo?.name,
      defaultValues?.orderNo,
      modalState.checklist,
      modalState.createME,
      modalState.deleteOrder,
      modalState.directions,
      modalState.printEmailOrder,
      modalState.linkedFiles,
      modalState.orderHistory,
      modalState.orderNotes,
      toggleModal,
    ]
  );

  // Submit handler for Item Details Tab
  const itemDetailSubmit: SubmitHandler<ItemListTypes> = async (formData) => {
    const currentItems = formData?.items || [];
    const defaultItems = itemDefaultValues?.items || [];

    // Extract only modified or new items.
    const modifiedOrderItems = getModifiedItems(currentItems, defaultItems);
    const payload = modifiedOrderItems
      ?.filter((item) => item?.itemId?.value)
      .map((item: ItemFormDataTypes) => {
        const {
          listId,
          itemId,
          description,
          serialNumber,
          subRental,
          price,
          type,
          orderId,
          quantity,
        } = item;
        return {
          id: typeof listId === 'number' ? listId : null,
          itemId: Number(itemId?.value),
          orderId: orderId ?? Number(id),
          serialNumber,
          quantity: Number(quantity) ?? null,
          description,
          type,
          subRental: subRental === 'Yes' ? true : false,
          price: Number(price ?? 0),
        };
      });
    if (payload.length > 0) {
      await saveItemDetails({ orderId: id, body: payload });
    }
  };

  const waitForModalConfirmation = (): Promise<void> => {
    return new Promise(async (resolve) => {
      const showNoDelivery = !!orderData?.data?.showNoDeliveryChargeWarning;
      const showShipInfo =
        !!orderData?.data?.showShipInfoWarning && !isDeliveryChargeUpdated;

      if (!showNoDelivery && !showShipInfo) {
        // Nothing to confirm
        resolve();
        return;
      }

      if (showNoDelivery) {
        await new Promise<void>((confirmDeliveryModal) => {
          setDeliveryChargeModel({
            state: true,
            data: {
              onConfirm: confirmDeliveryModal,
            },
          });
        });
      }

      if (showShipInfo) {
        await new Promise<void>((confirmBillingModal) => {
          setUseBillingLocationModel({
            state: true,
            data: {
              onConfirm: confirmBillingModal,
            },
          });
        });
      }

      resolve();
    });
  };

  // Submit handler for Item Details Tab
  const totalPaymentsSave: SubmitHandler<TotalPaymentsTypes> = async (
    formData
  ) => {
    const chargeConvFee =
      Number(requestedCreditCardConvFees) === 0 &&
      Number(requestedDebitCardConvFees) === 0 &&
      Number(requestedACHConvFees) === 0
        ? false
        : (formData?.chargeConvFee ?? false);
    let body = {
      ...formData,
      fuelSurcharge: formData?.fuelSurcharge ? formData?.fuelSurcharge : 0,
      productionFee: formData?.productionFee ? formData?.productionFee : 0,
      expeditedFee: formData?.expeditedFee ? formData?.expeditedFee : 0,
      chargeConvFee,
    };
    if (
      orderData?.data?.showNoDeliveryChargeWarning ||
      orderData?.data?.showShipInfoWarning
    ) {
      await waitForModalConfirmation();
    }
    await saveTotalPayments({
      body: { ...body, orderId: id },
    });
  };

  // Submit handler for Status Details Tab
  const statusSave: SubmitHandler<StatusTypes> = async (formData) => {
    const {
      dateOrdered,
      enteredBy,
      signed,
      inventoryQuality,
      approvedForShipment,
    } = formData;
    await saveStatusDetails({
      body: {
        dateOrdered: formatDate(dateOrdered, DATE_FORMAT_YYYYMMDD),
        enteredBy: enteredBy,
        signed,
        inventoryQuality,
        approvedForShipment,
        orderId: Number(id),
      },
    });
  };

  // Submit handler for Additional info Details Tab
  const additionalInfoSave: SubmitHandler<AdditionalInfoTypes> = async (
    formData
  ) => {
    const {
      orderInfo,
      subRentInfo,
      customerid,
      buildingPermitDate,
      undergroundDate,
      installStartDate,
      installEndDate,
      dismantleStartDate,
      dismantleEndDate,
      install,
      dismantle,
      buildingPermit,
      undergroundLocate,
      cadDrawing,
      isDeleted,
      ...rest
    } = formData;

    const toBoolean = (value: unknown): boolean =>
      typeof value === 'boolean'
        ? value
        : String(value).toLowerCase() === 'true';

    const formatDateIfPresent = (date?: string | Date | null): string | null =>
      date && String(date).trim() !== ''
        ? formatDate(date, DATE_FORMAT_YYYYMMDD)
        : null;

    const formattedDates = {
      buildingPermitDate: formatDateIfPresent(buildingPermitDate),
      undergroundDate: formatDateIfPresent(undergroundDate),
      installStartDate: formatDateIfPresent(installStartDate),
      installEndDate: formatDateIfPresent(installEndDate),
      dismantleStartDate: formatDateIfPresent(dismantleStartDate),
      dismantleEndDate: formatDateIfPresent(dismantleEndDate),
    };

    const booleanFields = {
      install: toBoolean(install),
      dismantle: toBoolean(dismantle),
      buildingPermit: toBoolean(buildingPermit),
      undergroundLocate: toBoolean(undergroundLocate),
      cadDrawing: toBoolean(cadDrawing),
      isDeleted: toBoolean(isDeleted),
    };

    await saveAdditionalInfo({
      orderId: id,
      body: {
        ...rest,
        ...formattedDates,
        ...booleanFields,
        subRentInfo,
        orderInfo,
        customerid,
      },
    });
  };

  // Submit handler for Sales & Referrals Details Tab
  const salesReferralsSave: SubmitHandler<SalesReferralsType> = async (
    formData
  ) => {
    if (formData?.status) {
      delete formData?.status;
    }
    await saveSalesReferrals({
      body: {
        ...formData,
        orderId: id,
        custPaymentDate: formatDate(
          formData?.custPaymentDate || new Date(),
          'YYYY-MM-DD'
        ),
        salesPaymentDate: formatDate(
          formData?.salesPaymentDate || new Date(),
          'YYYY-MM-DD'
        ),
      },
    });
  };

  // Helper function to trigger the correct submit handler based on tab
  const triggerSubmitHandler = (tabName: string) => {
    switch (tabName) {
      case ORDERS_ENUM.INFORMATION:
        return () => handleSubmit(onSubmit)();
      case ORDERS_ENUM.ITEM_DETAILS:
        return () => itemDetailsForm.handleSubmit(itemDetailSubmit)();
      case ORDERS_ENUM.TOTAL_PAYMENTS:
        return () => totalPaymentsForm.handleSubmit(totalPaymentsSave)();
      case ORDERS_ENUM.STATUS:
        return () => orderStatusForm.handleSubmit(statusSave)();
      case ORDERS_ENUM.ADDITIONAL_INFO:
        return () => orderAdditionalInfoForm.handleSubmit(additionalInfoSave)();
      case ORDERS_ENUM.SALES_REFERRALS:
        return () => orderSalesReferralsForm.handleSubmit(salesReferralsSave)();
      default:
        return () => {}; // No action for other tabs
    }
  };

  useEffect(() => {
    setIsDeliveryChargeUpdated(false);
  }, [orderData?.data?.id]);

  const handleCloseCopyOrder = () => {
    toggleModal('copyOrderInfo');
    if (modalState.copyOrderInfoItems) toggleModal('copyOrderInfoItems');
  };

  return (
    <>
      <FormProvider {...form}>
        <div className="flex justify-between items-center px-6 py-4 sticky top-16 bg-white z-[12]">
          <div className="flex gap-x-4 items-center">
            <IconButton onClick={navigateToOrder}>
              <CheveronLeft />
            </IconButton>
            <h1
              className="text-2xl text-text-tertiary font-semibold hover:cursor-pointer"
              onClick={navigateToOrder}
            >
              Order
            </h1>

            <div className="flex items-center gap-3">
              <span className="text-2xl font-semibold text-text-tertiary">
                {' / '}
              </span>
              <p className="text-2xl capitalize font-semibold">
                {id ? `#${defaultValues?.orderNo ?? ''}` : 'New Order'}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            {isItemTab && isSaveDisabled ? (
              <TooltipWidget content="Please add a new item or update an existing item">
                <div>
                  <AppButton
                    label="Save Detail"
                    icon={SaveIcon}
                    onClick={() => triggerSubmitHandler(activeTab)()}
                    iconClassName="w-4 h-4"
                    isLoading={
                      isLoading ||
                      isSaveItemDetailsLoading ||
                      isLoadingTotalPayments ||
                      issaveStatusDetailsLoading ||
                      isLoadingAdditionalInfo ||
                      isLoadingSalesReferrals
                    }
                    disabled={
                      isSaveDisabled || isDelete || isOrderEntryReadOnly
                    }
                  />
                </div>
              </TooltipWidget>
            ) : (
              <AppButton
                label="Save Detail"
                icon={SaveIcon}
                onClick={() => triggerSubmitHandler(activeTab)()}
                iconClassName="w-4 h-4"
                isLoading={
                  isLoading ||
                  isSaveItemDetailsLoading ||
                  isLoadingTotalPayments ||
                  issaveStatusDetailsLoading ||
                  isLoadingAdditionalInfo ||
                  isLoadingSalesReferrals
                }
                disabled={isDelete || isOrderEntryReadOnly}
              />
            )}
            <AppButton
              label="Cancel"
              icon={X}
              onClick={navigateToOrder}
              iconClassName="w-4 h-4"
              variant="neutral"
            />
            {id && (
              <>
                <AppButton
                  label=""
                  icon={File}
                  onClick={() => toggleModal('orderNotes')}
                  iconClassName="w-4 h-4"
                  variant="neutral"
                  tooltip="Order Notes"
                  side="bottom"
                />
                <AppButton
                  label=""
                  icon={Folder}
                  onClick={() => toggleModal('linkedFiles')}
                  iconClassName="w-4 h-4"
                  variant="neutral"
                  tooltip="Linked Files"
                  side="bottom"
                />
                <AppButton
                  label=""
                  icon={ListCheck}
                  onClick={() => toggleModal('checklist')}
                  iconClassName="w-4 h-4"
                  variant="neutral"
                  tooltip="Checklist"
                  side="bottom"
                />
                <ActionColumnMenu
                  triggerClassName="border h-10"
                  dropdownMenuList={OrderDropdownMenu}
                  contentClassName="p-4 flex flex-col gap-2 w-full"
                  subClassName="p-2 mr-5"
                />
              </>
            )}
          </div>
        </div>

        <Modals />
        <AppTabsVertical
          tabs={orderTabList({
            itemDetailsForm,
            onDefaultValuesLoaded,
            totalPaymentsForm,
            orderStatusForm,
            orderAdditionalInfoForm,
            orderSalesReferralsForm,
            userDefaultStoreInfo: defaultValues?.userDefaultStoreInfo,
            shippingManager: defaultValues?.shippingManager,
            orderInformationForm: form,
          })}
          activeTab={tabName}
          onTabChange={handleTabChange}
          showTabMenu={!!id}
          className="px-4 pb-3"
        />
        <AppConfirmationModal
          title="Confirmation - Copy Order info"
          description={
            <div>
              The Date of Use for this new order has been set to{' '}
              <span className="font-bold">
                {' '}
                {formatDate(orderData?.data?.dateOfUseFrom)}.
              </span>
              <span style={{ display: 'block' }}>
                If this is not the correct Date of Use, it must be changed.
              </span>
            </div>
          }
          open={modalState.copyOrderInfo}
          onOpenChange={handleCloseCopyOrder}
          handleCancel={handleCloseCopyOrder}
          handleSubmit={handleCopy}
          isLoading={cloneOrderLoading}
        />
        <AppConfirmationModal
          title="Confirmation"
          description={
            <div>
              {!isRentalOrder
                ? 'Missing Equipment orders can only be created for Rental orders.'
                : 'A Missing Equipment invoice cannot be created from a future invoice.'}
            </div>
          }
          open={modalState.showMissingOrderAlert}
          handleSubmit={() => toggleModal('showMissingOrderAlert')}
          submitLabel="Ok"
        />

        <AppConfirmationModal
          open={deliveryChargeModel.state}
          title="Warning"
          description={
            'A delivery charge has not been added to this order. Please double check the order and add a delivery charge if necessary.'
          }
          handleSubmit={handleDcWarningConfirm}
          submitLabel="Ok"
          isLoading={orderIsLoading}
        />

        <AppConfirmationModal
          title="Warning"
          description={
            'No delivery charge exists for this shipping location. Would you like to use the billing location? If not, the delivery charge will be cleared.'
          }
          open={useBillingLocation.state}
          onOpenChange={handleCloseBillingLocationModal}
          handleCancel={handleCloseBillingLocationModal}
          handleSubmit={handleSubmitBillingLocation}
          isLoading={updateDeleveryChargeLoading}
        />

        <AppSpinner overlay isLoading={orderIsLoading || storeLoading} />
      </FormProvider>

      {!isDelete && <OrderSessionTimeout />}
    </>
  );
};
