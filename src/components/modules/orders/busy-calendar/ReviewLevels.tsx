import AppButton from '@/components/common/app-button';
import AppSpinner from '@/components/common/app-spinner';
import MultiCheckboxDropdown from '@/components/forms/MulitCheckbox';
import NumberInputField from '@/components/forms/number-input-field';
import { CATEGORY_API_ROUTES } from '@/constants/api-constants';
import useOptionList from '@/hooks/useOptionList';
import { emptyToNull } from '@/lib/utils';
import {
  useGetBusycalReviewlevelQuery,
  useUpdateBusycalReviewlevelMutation,
} from '@/redux/features/orders/order.api';
import { BusyCalReviewLevelsFormType } from '@/types/orders/busy-calendar';
import isEqual from 'lodash/isEqual';
import { useCallback, useEffect, useMemo } from 'react';
import { useForm } from 'react-hook-form';

const ReviewLevels = ({
  handleChange,
}: {
  handleChange: (success?: boolean) => void;
}) => {
  const form = useForm<BusyCalReviewLevelsFormType>();

  // get the review details
  const { data: reviewLevelData, isFetching: isLoading } =
    useGetBusycalReviewlevelQuery(undefined, {
      refetchOnMountOrArgChange: true,
    });
  const [updateReviewlevel, { isLoading: updateLoader }] =
    useUpdateBusycalReviewlevelMutation();

  const defaultValues = useMemo(() => {
    const dataValue = reviewLevelData?.data;
    return {
      ...dataValue,
      totalDeliveries: dataValue?.totalDeliveries ?? 0,
      totalPickups: dataValue?.totalPickups ?? 0,
      totalItineraries: dataValue?.totalItineraries ?? 0,
      totalTentItems: dataValue?.totalTentItems ?? 0,
      totalOtherItems: dataValue?.totalOtherItems ?? 0,
      totalRevenue: dataValue?.totalRevenue ?? 0,
      id: dataValue?.id || null,
      csvCategoryIds: dataValue?.csvCategoryIds
        ? dataValue?.csvCategoryIds?.split(',')?.map(Number)
        : [],
    };
  }, [reviewLevelData?.data]);

  useEffect(() => {
    if (defaultValues) {
      form.reset(defaultValues);
    }
  }, [defaultValues, form]);

  const { options: categoryList, optionLoading } = useOptionList({
    url: CATEGORY_API_ROUTES.ALL,
    labelKey: 'catDesc',
    valueKey: 'id',
    sortBy: 'catDesc',
  });

  // on submit
  const onSubmit = useCallback(
    async (formData: BusyCalReviewLevelsFormType) => {
      try {
        const payload = {
          id: emptyToNull(formData?.id),
          totalDeliveries: emptyToNull(formData?.totalDeliveries),
          totalPickups: emptyToNull(formData?.totalPickups),
          totalItineraries: emptyToNull(formData?.totalItineraries),
          totalTentItems: emptyToNull(formData?.totalTentItems),
          totalOtherItems: emptyToNull(formData?.totalOtherItems),
          totalRevenue: emptyToNull(formData?.totalRevenue),
          csvCategoryIds: formData?.csvCategoryIds?.toString(),
        };

        const { data } = await updateReviewlevel(payload);
        if (data?.success) {
          handleChange(data?.success);
        }
      } catch (error) {
        // Handle error (could display error message to user)
      }
    },
    [handleChange, updateReviewlevel]
  );

  const normalizeValues = (values: BusyCalReviewLevelsFormType) => ({
    ...values,
    totalDeliveries: Number(values?.totalDeliveries),
    totalPickups: Number(values?.totalPickups),
    totalItineraries: Number(values?.totalItineraries),
    totalTentItems: Number(values?.totalTentItems),
    totalOtherItems: Number(values?.totalOtherItems),
    totalRevenue: Number(values?.totalRevenue),
    csvCategoryIds: values?.csvCategoryIds?.map(Number),
  });

  const isFormModified = isEqual(
    normalizeValues(form.watch()),
    normalizeValues(defaultValues)
  );

  return (
    <div className="px-6 py-3 space-y-4 gap-6 relative">
      <div className="grid grid-cols-1 gap-4">
        <h1 className="text-1xl">
          The following settings are the max levels per day before that day will
          turn yellow for review.
        </h1>
        <MultiCheckboxDropdown
          name="csvCategoryIds"
          label="Tent Categoties"
          form={form}
          optionsList={categoryList ?? []}
          placeholder={'Select Categories'}
          isLoading={optionLoading}
          className="w-full"
        />
        <div className="grid md:grid-cols-2 gap-6">
          <NumberInputField
            name="totalDeliveries"
            label="Total Number of Deliveries"
            form={form}
            maxLength={4}
            placeholder="Deliveries"
          />
          <NumberInputField
            name="totalPickups"
            label="Total Number of Pickups"
            form={form}
            maxLength={4}
            placeholder="Pickups"
          />
          <NumberInputField
            name="totalItineraries"
            label="Total Number of Itineraries"
            form={form}
            maxLength={4}
            placeholder="Itineraries"
          />
          <NumberInputField
            name="totalTentItems"
            label="Total Quantity of Tent Items"
            form={form}
            maxLength={4}
            placeholder="Tent Items"
          />
          <NumberInputField
            name="totalOtherItems"
            label="Total Quantity of Other Items"
            form={form}
            maxLength={4}
            placeholder="Other Items"
          />
          <NumberInputField
            name="totalRevenue"
            label="Total Revenue"
            form={form}
            maxLength={10}
            prefix="$"
            placeholder="Total Revenue"
            decimalScale={2}
            fixedDecimalScale
            thousandSeparator=","
          />
        </div>
      </div>
      <div className="flex justify-end space-x-4 pt-6">
        <AppButton
          label="OK"
          className="w-32"
          onClick={form.handleSubmit(onSubmit)}
          isLoading={updateLoader}
          disabled={isFormModified}
        />

        <AppButton
          onClick={() => handleChange()}
          label="Cancel"
          className="w-32"
          variant="neutral"
        />
      </div>
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-white/50 backdrop-blur-[2px] z-10">
          <AppSpinner
            className="h-8 w-8 text-brand-teal-Default"
            isLoading={isLoading}
          />
        </div>
      )}
    </div>
  );
};

export default ReviewLevels;
