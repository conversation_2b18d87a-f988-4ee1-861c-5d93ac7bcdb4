import AppButton from '@/components/common/app-button';
import MonthPickerInfo from '@/components/common/app-month-picker';
import CalendarView from '@/components/common/CalendarView';
import DataTable from '@/components/common/data-tables';
import SwitchField from '@/components/common/switch';
import InputField from '@/components/forms/input-field';
import {
  cn,
  convertToFloat,
  DATE_FORMAT_YYYYMMDD,
  DEFAULT_FORMAT,
  formatDate,
  getStorageValue,
} from '@/lib/utils';

import ActionColumnMenu from '@/components/common/data-tables/ActionColumn';
import CustomDialog from '@/components/common/dialog';
import { ROUTES } from '@/constants/routes-constants';
import { formatDateWithTimezone } from '@/lib/formatDateWithTimezone';
import {
  useGetBusyCalOrdersMutation,
  useLazyGetBusyCalDatesQuery,
  useSetBusyCalStatusMutation,
} from '@/redux/features/orders/order.api';
import { SortingStateType } from '@/types/common.types';
import {
  BusyCalendarTypes,
  BusyDateDataTypes,
} from '@/types/orders/busy-calendar';
import { ColumnDef } from '@tanstack/react-table';
import dayjs from 'dayjs';
import { Settings, SquareArrowOutUpRight } from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import ReviewLevels from './ReviewLevels';

const BusyCalendar = () => {
  const navigation = useNavigate();
  const tz = getStorageValue('timeZoneOffset') || '';
  const currentDate = formatDateWithTimezone({ tz });
  const [sorting, setSorting] = useState<SortingStateType[]>([
    { id: 'orderNo', desc: true },
  ]);
  const [reviewLevels, setReviewLevels] = useState<boolean>(false);

  const form = useForm<BusyCalendarTypes>();
  const info = form.watch();

  const toggleReviewLevels = useCallback((success?: boolean) => {
    setReviewLevels((prevState) => !prevState);
    if (success) {
    }
  }, []);

  // get Busy Calendar details
  const [getBusyCalDates, { data: busyCalendarData }] =
    useLazyGetBusyCalDatesQuery();

  // order details base on  selected date
  const [getBusyCalOrders, { data: ordersData, isLoading: orderLoading }] =
    useGetBusyCalOrdersMutation();
  const OrderList = (info?.selectedDate && ordersData?.data?.orders) || [];

  const defaultVaues = useMemo(() => {
    const dataValue = ordersData?.data;
    return {
      date: info?.date ?? currentDate,
      month: info?.date ?? currentDate,
      selectedDate: info?.selectedDate,
      deliveries: dataValue?.deliveries ?? '0',
      orderPickups: dataValue?.pickups ?? '0',
      itineraries: dataValue?.itineraries ?? '0',
      tentItems: dataValue?.tentItems ?? '0',
      otherItems: dataValue?.otherItems ?? '0',
      revenue: convertToFloat({ value: dataValue?.revenue, prefix: '$' }),
      pickups: info?.pickups ?? false,
      cpu_cr: info?.cpu_cr ?? false,
      quotes: info?.quotes ?? false,
    };
  }, [
    currentDate,
    info?.cpu_cr,
    info?.date,
    info?.pickups,
    info?.quotes,
    info?.selectedDate,
    ordersData?.data,
  ]);

  useEffect(() => {
    if (defaultVaues) {
      form.reset(defaultVaues);
    }
  }, [defaultVaues, form]);

  // get the busy calendar details
  const fetchBusyCalendarDates = useCallback(
    ({
      date,
      pickups = info?.pickups || false,
      cpu_cr = info?.cpu_cr || false,
      quotes = info?.quotes || false,
    }: {
      date: Date | string;
      pickups?: boolean;
      cpu_cr?: boolean;
      quotes?: boolean;
    }) => {
      getBusyCalDates({
        month: formatDate(date, DATE_FORMAT_YYYYMMDD),
        pickups,
        cpu_cr,
        quotes,
      });
    },
    [getBusyCalDates, info?.cpu_cr, info?.pickups, info?.quotes]
  );

  // get busy calendar dates details
  useEffect(() => {
    fetchBusyCalendarDates({ date: currentDate });
  }, [currentDate, fetchBusyCalendarDates]);

  // busy calendar status changes busy / not busy
  const [setBusyCalStatus, { isLoading: statusLoading }] =
    useSetBusyCalStatusMutation();

  // navigate on specific order
  const navigateToOrder = useCallback(
    (id: number) => {
      navigation(`${ROUTES.EDIT_ORDERS}?id=${id}&tab=information`);
    },
    [navigation]
  );

  const BusyCalendarList = useMemo(() => {
    if (!busyCalendarData?.data?.busyDates) return [];

    return busyCalendarData?.data?.busyDates?.map(
      ({ date, busy, review }: BusyDateDataTypes) => {
        const { label, className } = busy
          ? { label: 'Busy', className: 'bg-red-400 rounded-md' }
          : review
            ? { label: 'Review', className: 'bg-yellow-400 rounded-md' }
            : { label: '', className: '' };

        return {
          date,
          label,
          busy,
          className,
          labelClassName: 'text-xs',
          selectedClassName:
            'border-[1px] border-text-brand-violet-Default rounded-md hover:bg-unset',
        };
      }
    );
  }, [busyCalendarData?.data?.busyDates]);

  // handle on date change
  const handleGetBusyCalOrders = useCallback(
    async (selectedDay?: Date | null | string) => {
      const date = formatDate(selectedDay ?? '', DATE_FORMAT_YYYYMMDD);
      form.setValue('date', date);
      form.setValue('selectedDate', dayjs(date).format(DEFAULT_FORMAT));
      const info = form.watch();
      const payload = {
        date,
        pickups: info?.pickups,
        cpu_cr: info?.cpu_cr,
        quotes: info?.quotes,
        sortBy: sorting?.at(1)?.id,
        sortAscending: sorting?.at(1)?.desc,
      };
      await getBusyCalOrders(payload).unwrap();
    },
    [form, getBusyCalOrders, sorting]
  );

  // handle busy calendar status change
  const handleBusyCalStatus = useCallback(
    async (busy: boolean) => {
      const selectedDate = formatDate(info?.selectedDate, DATE_FORMAT_YYYYMMDD);
      if (!selectedDate) return;
      try {
        const { data: busyStatus } = await setBusyCalStatus({
          date: selectedDate,
          busy,
        }).unwrap();

        if (busyStatus) {
          fetchBusyCalendarDates({ date: selectedDate });
        }
      } catch (error) {}
    },
    [fetchBusyCalendarDates, info?.selectedDate, setBusyCalStatus]
  );

  // handle month change
  const handleOnMonthChange = useCallback(
    (date?: string | Date) => {
      const isSameMonth = dayjs(date)?.isSame(dayjs(), 'month');
      form.setValue(
        'date',
        formatDate(
          isSameMonth ? currentDate : (date ?? ''),
          DATE_FORMAT_YYYYMMDD
        )
      );
      form.setValue('selectedDate', '');

      fetchBusyCalendarDates({ date: date || '' });
    },
    [currentDate, fetchBusyCalendarDates, form]
  );

  // check selected day is busy or not
  const isBusy = useMemo(() => {
    const match = BusyCalendarList?.find((item: BusyCalendarTypes) =>
      dayjs(item?.date)?.isSame(dayjs(info?.selectedDate), 'day')
    );
    return match?.busy;
  }, [BusyCalendarList, info?.selectedDate]);

  // Busy Columns
  const columns: ColumnDef<BusyCalendarTypes>[] | any = useMemo(() => {
    return [
      {
        accessorKey: 'orderNo',
        header: 'Order #',
        enableSorting: true,
        invertSorting: true,
      },
      {
        accessorKey: 'deliveryDate',
        size: 160,
        header: 'Delivery Date',
        cell: ({ row }: any) => formatDate(row?.original?.deliveryDate),
        enableSorting: true,
        invertSorting: true,
        sortingFn: 'datetime',
      },
      ...(info?.pickups
        ? [
            {
              accessorKey: 'pickupDate',
              size: 160,
              header: 'Pickup Date',
              sortingFn: 'datetime',
              cell: ({ row }: any) => formatDate(row?.original?.pickupDate),
              enableSorting: true,
              invertSorting: true,
            },
            {
              accessorKey: 'orderType',
              header: 'Type',
              enableSorting: true,
              invertSorting: true,
            },
          ]
        : []),
      {
        accessorKey: 'orderTotal',
        header: 'Order Total',
        enableSorting: true,
        invertSorting: true,
      },
      {
        accessorKey: 'customer',
        header: 'Customer',
        enableSorting: true,
        invertSorting: true,

        size: 80,
      },
      {
        accessorKey: 'city',
        header: 'City',
        enableSorting: true,
        invertSorting: true,
        size: 80,
      },
      {
        id: 'action',
        size: 80,
        header: 'Actions',
        cell: ({ row }: any) => (
          <ActionColumnMenu
            contentClassName="z-[99] w-fit p-2"
            dropdownMenuList={[
              {
                label: (
                  <div className="flex items-center gap-2">
                    Open Order
                    <SquareArrowOutUpRight className="w-4 h-4" />
                  </div>
                ),
                onClick: () => navigateToOrder(row.original.orderId),
              },
            ]}
          />
        ),
      },
    ];
  }, [info?.pickups, navigateToOrder]);

  // switch list
  const filterWitchOptions: {
    name: 'pickups' | 'cpu_cr' | 'quotes';
    label: string;
  }[] = [
    {
      label: 'Pickups',
      name: 'pickups',
    },
    { label: 'CPR/CR', name: 'cpu_cr' },
    { label: 'Quotes', name: 'quotes' },
  ];
  // Busy calendar details fileds
  const fields = [
    {
      name: 'deliveries',
      label: 'Deliveries',
    },
    {
      name: 'orderPickups',
      label: 'Pickups',
    },
    {
      name: 'itineraries',
      label: 'Itineraries',
    },
    {
      name: 'tentItems',
      label: 'Tents',
    },
    {
      name: 'otherItems',
      label: 'Other Items',
    },
    {
      name: 'revenue',
      label: 'Revenue',
      prefix: '$',
    },
  ];

  return (
    <>
      <div className="p-4 border rounded-md grid grid-cols-5 gap-6">
        <div className="col-span-2">
          <div className="flex gap-4">
            <MonthPickerInfo
              form={form}
              name="month"
              label="Date"
              placeholder="Select Month"
              onMonthChange={handleOnMonthChange}
              className="w-full"
            />
            <AppButton
              label="Review Levels"
              className="mt-8 w-fit"
              icon={Settings}
              iconClassName="w-5 h-5"
              onClick={toggleReviewLevels}
            />
          </div>
          <div className="flex flex-wrap gap-10 w-full mt-[50px]">
            {filterWitchOptions?.map((item, index) => (
              <SwitchField
                key={`${item.name}-${index}`}
                name={item.name}
                labelEnd={item.label}
                form={form}
                className="w-fit"
                onChange={() =>
                  info?.selectedDate &&
                  handleGetBusyCalOrders(info?.selectedDate)
                }
              />
            ))}
          </div>
        </div>
        <div className="col-span-3 grid grid-cols-3 gap-4">
          {fields.map((field) => (
            <InputField
              key={field.name}
              name={field.name as any}
              form={form}
              label={field.label}
              disabled
            />
          ))}
        </div>
      </div>

      <div className="grid grid-cols-3 gap-3 xl:gap-4 pt-6 pb-4">
        <div>
          <div className="flex gap-3 mb-3 w-full">
            <InputField
              name="selectedDate"
              form={form}
              placeholder="Date"
              disabled
              pClassName="w-full"
            />
            <AppButton
              label={`${isBusy ? 'Not' : ''} Busy`}
              className={cn(
                'w-36',
                !isBusy &&
                  info?.selectedDate &&
                  'bg-red-400 hover:bg-red-400 text-text-Default'
              )}
              variant={'primary'}
              onClick={() => handleBusyCalStatus(!isBusy)}
              disabled={!info?.selectedDate}
              isLoading={statusLoading}
            />
          </div>

          <CalendarView
            data={BusyCalendarList}
            value={dayjs(info?.date)?.toDate()}
            onMonthChange={handleOnMonthChange}
            onSelect={handleGetBusyCalOrders}
          />
        </div>
        <div className="col-span-2 grid grid-cols-1">
          <DataTable
            data={OrderList}
            totalItems={OrderList?.length}
            isLoading={orderLoading}
            columns={columns}
            tableClassName="max-h-[400px] 2xl:max-h-[450px] overflow-auto"
            enablePagination={false}
            manualSorting={false}
            sorting={sorting}
            setSorting={setSorting}
          />
        </div>
      </div>
      <CustomDialog
        onOpenChange={toggleReviewLevels}
        description=""
        open={reviewLevels}
        className="min-w-[40%]"
        title={'Review Levels'}
      >
        <ReviewLevels
          handleChange={(success) => {
            toggleReviewLevels();
            success &&
              fetchBusyCalendarDates({
                date: formatDate(info?.selectedDate, DATE_FORMAT_YYYYMMDD),
              });
          }}
        />
      </CustomDialog>
    </>
  );
};

export default BusyCalendar;
