import { memo } from 'react';
import { useForm, SubmitHandler } from 'react-hook-form';

import AppButton from '@/components/common/app-button';
import AppSpinner from '@/components/common/app-spinner';
import InputField from '@/components/forms/input-field';
import NumberInputField from '@/components/forms/number-input-field';

import { useCreditCardDetailsMutation } from '@/redux/features/accounting/payments.api';

import { CreditCardDetailsTypes } from '@/types/accounting.types';
import { CreditCardsTypes } from '@/types/order.types';
import { FIRST_SIX_DIGITS_VALIDATION } from '@/constants/validation-constants';

interface CreditCardsProps {
  toggle: () => void;
  onConfirm: (cardType: string) => void;
}

// Maps API response to form shape
const mapResponseToForm = (
  firstSixDigits: string,
  data: CreditCardDetailsTypes
): CreditCardsTypes => ({
  firstSixDigits,
  scheme: data.scheme || '',
  type: data.type || '',
  brand: data.brand || '',
  bank: data.bank?.name || '',
});

const CreditCards = ({ toggle, onConfirm }: CreditCardsProps) => {
  const form = useForm<CreditCardsTypes>();
  const [getCardDetails, { isLoading, data }] = useCreditCardDetailsMutation();

  const onSubmit: SubmitHandler<CreditCardsTypes> = async (formData) => {
    const response = await getCardDetails(formData.firstSixDigits);
    const cardData = response?.data?.data;
    if (response?.data?.success && cardData) {
      form.reset(mapResponseToForm(formData.firstSixDigits, cardData));
    }
  };

  const handleApply = () => {
    onConfirm(data?.data?.type ?? '');
    toggle();
  };

  return (
    <div className="pl-6 pr-6 flex flex-col gap-2">
      <NumberInputField
        name="firstSixDigits"
        form={form}
        label="First 6 Digits"
        placeholder="________"
        maxLength={6}
        validation={FIRST_SIX_DIGITS_VALIDATION}
      />
      <InputField
        name="scheme"
        form={form}
        label="Scheme"
        placeholder="Enter Scheme"
        disabled
      />
      <InputField
        name="type"
        form={form}
        label="Type"
        placeholder="Enter Type"
        disabled
      />
      <InputField
        name="brand"
        form={form}
        label="Brand"
        placeholder="Enter Brand"
        disabled
      />
      <InputField
        name="bank"
        form={form}
        label="Bank"
        placeholder="Enter Bank"
        disabled
      />

      <div className="w-full justify-between flex py-1 sticky bottom-0 pt-5 gap-4 bg-white z-30">
        <div className="flex gap-4">
          <AppButton
            className="w-28"
            label="Check"
            onClick={form.handleSubmit(onSubmit)}
          />
          <AppButton className="w-28" label="Apply" onClick={handleApply} />
        </div>
        <AppButton
          className="w-28"
          label="Close"
          onClick={toggle}
          variant="neutral"
        />
      </div>
      <AppSpinner overlay isLoading={isLoading} />
    </div>
  );
};

export default memo(CreditCards);
