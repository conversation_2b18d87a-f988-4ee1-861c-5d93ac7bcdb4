import AppButton from '@/components/common/app-button';
import NumberInputField from '@/components/forms/number-input-field';
import { getQueryParam } from '@/lib/utils';
import { useCalculateConvFeeTaxMutation } from '@/redux/features/orders/order.api';
import { BreakdownTypes, NewPaymentDTO } from '@/types/order.types';
import { debounce } from 'lodash';
import { memo, useCallback } from 'react';
import { UseFormReturn } from 'react-hook-form';

const Amount = ({
  toggle,
  breakdownForm,
  form,
  setTotalAmount,
  setIsConvenienceFeeApplicable,
}: {
  toggle: () => void;
  breakdownForm: UseFormReturn<BreakdownTypes>;
  form: UseFormReturn<NewPaymentDTO>;
  setTotalAmount: any;
  setIsConvenienceFeeApplicable?: (value: boolean) => void;
}) => {
  const orderId = getQueryParam('id') as string;

  const [calculateConvFeeTax] = useCalculateConvFeeTaxMutation();

  const calculateConvenienceFeeIfApplicable = useCallback(async () => {
    const charge = parseFloat(
      breakdownForm.getValues('charge')?.toString() || '0'
    );

    if (charge <= 0) {
      const convFee = 0;
      const convFeeTax = 0;
      const total = charge;
      form.setValue('amount', +total.toFixed(2));
      breakdownForm.setValue('charge', charge.toString());
      breakdownForm.setValue('convenienceFree', convFee.toString());
      breakdownForm.setValue('tax', convFeeTax.toString());
      breakdownForm.setValue('totalCharge', total.toString());
      setIsConvenienceFeeApplicable?.(convFee > 0);
      return;
    }

    try {
      const Responses = await calculateConvFeeTax({
        orderId,
        paymentTypeId: form.getValues('type'),
        amount: charge,
      }).unwrap();

      const convFee = Responses?.data?.convFee;
      const convFeeTax = Responses?.data?.convFeeTax;
      const total = Responses?.data?.total;

      // Update main amount field with full value
      form.setValue('amount', +total.toFixed(2));
      setTotalAmount(+total.toFixed(2));
      breakdownForm.setValue('charge', charge.toString());
      breakdownForm.setValue('convenienceFree', convFee.toString());
      breakdownForm.setValue('tax', convFeeTax.toString());
      breakdownForm.setValue('totalCharge', total.toString());
      setIsConvenienceFeeApplicable?.(convFee > 0);
    } catch (error) {
      // console.error('Error :', error);
    }
  }, [
    breakdownForm,
    calculateConvFeeTax,
    form,
    orderId,
    setIsConvenienceFeeApplicable,
    setTotalAmount,
  ]);

  const debouncedCalculateConvenienceFeeIfApplicable = debounce(
    calculateConvenienceFeeIfApplicable,
    1000
  );

  return (
    <div className="pl-6 pr-6">
      <div className="grid grid-cols-1 gap-4">
        <NumberInputField
          name="charge"
          form={breakdownForm}
          label="Charge"
          fixedDecimalScale
          placeholder="$______.__"
          prefix="$"
          onValueChange={debouncedCalculateConvenienceFeeIfApplicable}
          allowNegative
        />
        <NumberInputField
          name="convenienceFree"
          form={breakdownForm}
          label="Convenience Fee"
          fixedDecimalScale
          placeholder="$______.__"
          prefix="$"
          disabled
        />
        <NumberInputField
          name="tax"
          form={breakdownForm}
          label="Tax"
          fixedDecimalScale
          placeholder="$______.__"
          prefix="$"
          disabled
        />
        <NumberInputField
          name="totalCharge"
          form={breakdownForm}
          label="Total Charge"
          fixedDecimalScale
          placeholder="$______.__"
          prefix="$"
          disabled
          allowNegative
        />
      </div>

      <div className="w-full h-full justify-end flex py-1 sticky bottom-0 pt-5 bg-white z-30">
        <AppButton
          className="w-28"
          label="Close"
          onClick={() => {
            toggle(); // Close the dialog
          }}
          variant="neutral"
        />
      </div>
    </div>
  );
};

export default memo(Amount);
