import AppButton from '@/components/common/app-button';
import NumberInputField from '@/components/forms/number-input-field';
import RadioField from '@/components/forms/radio-field';
import {
  dataCalculationTypes,
  PaymentSelectionTypes,
} from '@/types/order.types';
import { memo, useEffect, useMemo } from 'react';
import { useForm, useWatch } from 'react-hook-form';

export enum PaymentAmountDiscounts {
  Five = '5',
  Ten = '10',
  Fifteen = '15',
  Twenty = '20',
  TwentyFive = '25',
  Fifty = '50',
}

export const PaymentAmountDiscountsMethods = [
  { label: '5%', value: PaymentAmountDiscounts.Five },
  { label: '10%', value: PaymentAmountDiscounts.Ten },
  { label: '15%', value: PaymentAmountDiscounts.Fifteen },
  { label: '20%', value: PaymentAmountDiscounts.Twenty },
  { label: '25%', value: PaymentAmountDiscounts.TwentyFive },
  { label: '50%', value: PaymentAmountDiscounts.Fifty },
];

type PaymentAmountProps = {
  toggle: () => void;
  dataCalculation?: dataCalculationTypes;
  onSubmit: (data: {
    paymentType: PaymentSelectionTypes['paymentType'];
    amount: number;
    discountPercentage: PaymentSelectionTypes['discountPercentage'];
  }) => void;
  selectedPaymentType?: PaymentSelectionTypes;
};

const PaymentAmount = ({
  toggle,
  dataCalculation,
  onSubmit,
  selectedPaymentType,
}: PaymentAmountProps) => {
  const form = useForm<PaymentSelectionTypes>({
    defaultValues: {
      paymentType: 'percentage',
      discountPercentage: PaymentAmountDiscounts.Five,
      percentageAmount: 0,
      requiredDepositeAmount: 0,
      balanceDueAmount: 0,
    },
  });

  const {
    grandTotal = 0,
    subTotal = 0,
    balanceDueAmount = 0,
  } = dataCalculation || {};

  const discount = useWatch({
    control: form.control,
    name: 'discountPercentage',
  });

  useEffect(() => {
    if (discount) {
      form.setValue('paymentType', 'percentage');
    }

    if (discount && grandTotal) {
      const percentageAmount = (+discount * grandTotal) / 100;
      form.setValue('percentageAmount', percentageAmount);
    }

    if (subTotal) {
      const depositAmount =
        ((dataCalculation?.requestedDepositPercentageAmt ?? 0) * subTotal) /
        100;
      form.setValue('requiredDepositeAmount', +depositAmount);
    }

    if (balanceDueAmount) {
      form.setValue('balanceDueAmount', balanceDueAmount);
    }
  }, [
    discount,
    grandTotal,
    subTotal,
    balanceDueAmount,
    form,
    dataCalculation?.requestedDepositPercentageAmt,
  ]);

  const percentageLabel = useMemo(() => {
    return `Percentage (${discount || '0'}% of order total $${grandTotal})`;
  }, [discount, grandTotal]);

  const depositLabel = useMemo(() => {
    return `Required Deposit (${dataCalculation?.requestedDepositPercentageAmt}% of sub-total $${subTotal})`;
  }, [dataCalculation?.requestedDepositPercentageAmt, subTotal]);

  const handleSubmit = () => {
    const values = form.getValues();
    let amount = 0;

    switch (values.paymentType) {
      case 'percentage':
        amount = values.percentageAmount ?? 0;
        break;
      case 'requiredDeposite':
        amount = values.requiredDepositeAmount ?? 0;
        break;
      case 'balanceDue':
        amount = values.balanceDueAmount ?? 0;
        break;
    }

    onSubmit({
      paymentType: values.paymentType,
      amount,
      discountPercentage: values.discountPercentage,
    });
    toggle();
  };

  useEffect(() => {
    form.reset({
      ...form.getValues(),
      paymentType: selectedPaymentType?.paymentType,
      discountPercentage: selectedPaymentType?.discountPercentage,
    });
  }, [form, selectedPaymentType]);

  return (
    <div className="pl-6 pr-6">
      <div className="grid grid-cols-[55%_45%] justify-between gap-4 pb-2">
        {/* Left Column: Payment Type Options */}
        <div className="flex flex-col gap-6">
          <div>
            <RadioField
              name="paymentType"
              form={form}
              options={[
                {
                  label: percentageLabel,
                  value: 'percentage',
                },
              ]}
            />
            <NumberInputField
              name="percentageAmount"
              form={form}
              label=""
              placeholder="$______.__"
              prefix="$"
              fixedDecimalScale
              className="mt-2"
              disabled
            />
          </div>

          <div>
            <RadioField
              name="paymentType"
              form={form}
              options={[
                {
                  label: depositLabel,
                  value: 'requiredDeposite',
                },
              ]}
            />
            <NumberInputField
              name="requiredDepositeAmount"
              form={form}
              label=""
              placeholder="$______.__"
              prefix="$"
              fixedDecimalScale
              className="mt-2"
              disabled
            />
          </div>

          <div>
            <RadioField
              name="paymentType"
              form={form}
              options={[{ label: 'Balance Due', value: 'balanceDue' }]}
            />
            <NumberInputField
              name="balanceDueAmount"
              form={form}
              label=""
              placeholder="$______.__"
              prefix="$"
              fixedDecimalScale
              className="mt-2"
              disabled
            />
          </div>
        </div>

        {/* Right Column: Discount Options */}
        <div className="flex flex-col justify-start gap-6">
          <RadioField
            form={form}
            name="discountPercentage"
            options={PaymentAmountDiscountsMethods}
            optionsPerRow={3}
            rowClassName="grid grid-cols-3 w-full"
            className="space-y-4"
          />
        </div>
      </div>

      <div className="w-full justify-end flex py-1 sticky bottom-0 pt-5 gap-4 bg-white z-30">
        <AppButton className="w-28" label="OK" onClick={handleSubmit} />
        <AppButton
          className="w-28"
          label="Cancel"
          onClick={toggle}
          variant="neutral"
        />
      </div>
    </div>
  );
};

export default memo(PaymentAmount);
