import AppButton from '@/components/common/app-button';
import DataTable from '@/components/common/data-tables';
import { getQueryParam } from '@/lib/utils';
import { useGetDeliveryChargesHistoryQuery } from '@/redux/features/customers/discount.api';
import { TotalPaymentsDeliveryCharge } from '@/types/order.types';
import { ColumnDef } from '@tanstack/react-table';
import { useMemo } from 'react';

interface DeliveryInfo {
  handleSetActivePopup: any;
}
const EditDeliveryChargeHistory = ({ handleSetActivePopup }: DeliveryInfo) => {
  const id = getQueryParam('id') as string;

  const {
    data: deliveryChargeHistroyListData,
    isFetching: deliveryChargeHistroyListIsLoading,
  } = useGetDeliveryChargesHistoryQuery(id, {
    skip: !id,
    refetchOnMountOrArgChange: true,
  });

  const columns: ColumnDef<TotalPaymentsDeliveryCharge>[] = useMemo(
    () => [
      { accessorKey: 'date', header: 'Date' },
      { accessorKey: 'chargeOption', header: 'Delivery Charge' },
      { accessorKey: 'deliveryCharge', header: 'Delivery Charge Option' },
      { accessorKey: 'chargeBasedOn', header: 'Charge Based On' },
      { accessorKey: 'transitTime', header: 'Transit Time' },
      { accessorKey: 'confirmedBy', header: 'Confirmed By' },
    ],
    []
  );

  return (
    <div className="mt-2">
      <div className="flex flex-col gap-6">
        <DataTable
          data={deliveryChargeHistroyListData?.data || []}
          tableClassName="max-h-[400px] overflow-auto"
          columns={columns}
          enableSearch={false}
          enableColumnVisibility={false}
          enableRowSelection={false}
          enablePagination={false}
          enableFilter={false}
          enableMultiRowSelection={false}
          isLoading={deliveryChargeHistroyListIsLoading}
        />
      </div>
      <div className="flex justify-end gap-4 bg-white px-4 mt-3">
        <AppButton
          type="button"
          label="Cancel"
          variant="neutral"
          className="w-28 absolute bottom-5 right-6"
          onClick={() => handleSetActivePopup('edit-delivery-charge')}
        />
      </div>
    </div>
  );
};

export default EditDeliveryChargeHistory;
