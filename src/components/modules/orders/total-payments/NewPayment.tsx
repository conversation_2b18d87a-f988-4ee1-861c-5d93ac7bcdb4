import AppButton from '@/components/common/app-button';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import CustomDialog from '@/components/common/dialog';
import DatePicker from '@/components/forms/date-picker';
import Input<PERSON>ield from '@/components/forms/input-field';
import NumberIn<PERSON><PERSON>ield from '@/components/forms/number-input-field';
import SelectDropDown from '@/components/forms/select-dropdown';
import {
  BANK_ACCOUNTS_API_ROUTES,
  PAYMENT_TYPE_API_ROUTES,
} from '@/constants/api-constants';
import { PAYMENT_BRED_CRUM_DETAILS } from '@/constants/order-constants';
import { formatDateWithTimezone } from '@/lib/formatDateWithTimezone';
import {
  cn,
  convertToFloat,
  DATE_FORMAT_YYYYMMDD,
  formatDate,
  generateLabelValuePairs,
  getQueryParam,
} from '@/lib/utils';
import { useGetAllMutation } from '@/redux/features/common-api/common.api';
import {
  useAddPaymentMutation,
  useCalculateConvFeeTaxMutation,
  useGetPaymentDetailsQuery,
  useGetRefundAmountMutation,
} from '@/redux/features/orders/order.api';
import {
  AddPaymentType,
  BreakdownTypes,
  dataCalculationTypes,
  ExtendedPaymentInfoDTO,
  NewPaymentDTO,
  OrderInformationTypes,
  PaymentSelectionTypes,
  TotalPaymentsTypes,
} from '@/types/order.types';
import { CircleAlert } from 'lucide-react';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useForm, useFormContext, UseFormReturn } from 'react-hook-form';
import { OpenDialogType } from '../constants';
import Amount from './new-payment/Amount';
import CreditCards from './new-payment/CreditCards';
import PaymentAmount from './new-payment/PaymentAmount';
import RadioField from '@/components/forms/radio-field';
import DataTable from '@/components/common/data-tables';
import { ColumnDef, RowSelectionState } from '@tanstack/react-table';
import { PaymentTypeEnum } from '@/types/accounting.types';

interface NewPaymentProps {
  setOpen: React.Dispatch<React.SetStateAction<OpenDialogType>>;
  handleSetActiveTab: (tab: string) => void;
  salesTaxCodeId?: number;
  dataCalculation?: dataCalculationTypes;
  totalPaymentsForm: UseFormReturn<TotalPaymentsTypes>;
  storeLocationData: any;
}

interface GeneralDialogStateTypes {
  state: boolean;
  action: string;
}

type PaymentData = {
  paymentType: PaymentSelectionTypes['paymentType'];
  discountPercentage: PaymentSelectionTypes['discountPercentage'];
  amount: number;
};

const initialDialogState = {
  state: false,
  action: '',
};

export const CARD_TYPE_MAPPING: Record<string, PaymentTypeEnum> = {
  credit: PaymentTypeEnum.CREDIT_CARD,
  debit: PaymentTypeEnum.DEBIT_CARD,
};

const NewPayment = ({
  setOpen,
  handleSetActiveTab,
  dataCalculation,
  totalPaymentsForm,
  storeLocationData,
}: NewPaymentProps) => {
  const orderId = getQueryParam('id') as string;
  const currentDate = formatDateWithTimezone({});
  const [
    isGravityPaymentWarningModalOpen,
    setIsGravityPaymentWarningModalOpen,
  ] = useState(false);
  const [isAmountEmptyModalOpen, setAmountEmptyModalOpen] = useState(false);
  const [isOrderTotalGraterModalOpen, setIsOrderTotalGraterModalOpen] =
    useState({ state: false, action: '' });
  const [selectedCardType, setSelectedCardType] = useState<any>(null);
  const [isConvenienceFeeApplicable, setIsConvenienceFeeApplicable] =
    useState(false);
  const [isGravityPaymentMethod, setIsGravityPaymentMethod] = useState(false);
  const [disabledCreditCardACHPayment, setDisabledCreditCardACHPayment] =
    useState(false);
  const [isTotalAmount, setTotalAmount] = useState<any>(null);
  const [openRefundConvFeeDialog, setOpenRefundConvFeeDialog] = useState({
    state: false,
    action: '',
  });
  const [selectedRows, setSelectedRows] = useState<RowSelectionState>({});
  const [selectedPayment, setSelectedPayment] = useState<any>(null);
  const [openTransactionDialog, setOpenTransactionDialog] = useState({
    state: false,
    action: '',
  });
  const actionButtonsRef = useRef<HTMLDivElement>(null);

  const selectedIds = useMemo(() => {
    return Object.keys(selectedRows).map(Number);
  }, [selectedRows]);

  const [addPayment, { isLoading: isLoadingAddPayment }] =
    useAddPaymentMutation();

  // get payment Refund API
  const [getRefundAmount, { isLoading: isLoadingRefund }] =
    useGetRefundAmountMutation();

  const [calculateConvFeeTax] = useCalculateConvFeeTaxMutation();

  const orderInfoForm = useFormContext<OrderInformationTypes>();
  const customerData = orderInfoForm?.watch('billTo');

  const balanceDueAmount = dataCalculation?.balanceDueAmount;

  const form = useForm<NewPaymentDTO>();
  const breakdownForm = useForm<BreakdownTypes>({
    defaultValues: {
      charge: '0',
      convenienceFree: '0',
      tax: '0',
      totalCharge: '0',
    },
  });

  const defaultValues = useMemo<NewPaymentDTO>(() => {
    return {
      date: currentDate ?? '',
      type: '',
      amount: 0,
      tax: 0,
      convFee: 0,
      convFeeTax: 0,
      reference: '',
      bankAccountId: null,
      name: customerData?.name ?? '',
      email: customerData?.orderEmail ?? '',
      address: customerData?.address1 ?? '',
      city: customerData?.city ?? '',
      state: Number(customerData?.stateId) ?? 0,
      zipcode: customerData?.zipCode ?? '',
      customerId: customerData?.customerId?.value?.toString() ?? '',
      refundAmountType: 'refundIncludingConvFee',
    };
  }, [currentDate, customerData]);

  const { setValue } = form;
  const requestedDepositPercentageAmt =
    storeLocationData?.data?.storeTotal?.requestedDeposit;
  const paymentType_Id = storeLocationData?.data?.storePayment?.paymentTypeId;
  const refundingConvenienceFees =
    storeLocationData?.data?.storePayment?.allowRefundingConvenienceFees;

  const mergeDataCalculation = useMemo(() => {
    return {
      ...dataCalculation,
      requestedDepositPercentageAmt,
    };
  }, [dataCalculation, requestedDepositPercentageAmt]);

  const calculateConvenienceFeeIfApplicable = useCallback(async () => {
    const charge = parseFloat(form.getValues('amount')?.toString() || '0');

    // If amount is less than zero or negative, skip convFee and convFeeTax calculation
    if (charge <= 0) {
      const convFee = 0;
      const convFeeTax = 0;
      const total = charge;
      setValue('amount', +total.toFixed(2));
      setTotalAmount(+total.toFixed(2));
      breakdownForm.setValue('charge', charge.toString());
      breakdownForm.setValue('convenienceFree', convFee.toString());
      breakdownForm.setValue('tax', convFeeTax.toString());
      breakdownForm.setValue('totalCharge', total.toString());
      setIsConvenienceFeeApplicable(convFee > 0);
      return;
    }

    if (Number(isTotalAmount) === charge) return;

    try {
      const Responses = await calculateConvFeeTax({
        orderId,
        paymentTypeId: form.getValues('type'),
        amount: charge,
      }).unwrap();

      const convFee = Responses?.data?.convFee;
      const convFeeTax = Responses?.data?.convFeeTax;
      const total = Responses?.data?.total;

      // Update main amount field with full value
      setValue('amount', +total.toFixed(2));
      setTotalAmount(+total.toFixed(2));
      breakdownForm.setValue('charge', charge.toString());
      breakdownForm.setValue('convenienceFree', convFee.toString());
      breakdownForm.setValue('tax', convFeeTax.toString());
      breakdownForm.setValue('totalCharge', total.toString());
      setIsConvenienceFeeApplicable(convFee > 0);
    } catch (error) {
      // console.error('Error :', error);
    }
  }, [
    breakdownForm,
    calculateConvFeeTax,
    form,
    isTotalAmount,
    orderId,
    setValue,
  ]);

  useEffect(() => {
    if (defaultValues) {
      form.reset(defaultValues);
    }
  }, [defaultValues, form]);

  const [openDialog, setOpenDialog] =
    useState<GeneralDialogStateTypes>(initialDialogState);
  const [selectedPaymentType, setSelectedPaymentType] =
    useState<PaymentSelectionTypes>();

  // No state needed for payment methods

  const handleCancel = useCallback(() => {
    setOpen({ state: false, action: '', data: null });
  }, [setOpen]);

  const getChargeFromBreakdown = breakdownForm.watch('charge');
  const getTaxFromBreakdown = breakdownForm.watch('tax');
  const getConvFromBreakdown = breakdownForm.watch('convenienceFree');

  const chargeValue = parseFloat(form.watch('amount')?.toString() || '0');
  const ccConvFee = Number(totalPaymentsForm.watch('ccConvenienceFee')) ?? 0;

  //check if amount is empty or greater than 100, then open modal
  // If amount is valid, open process with credit card modal
  // and set active tab to PROCESS_WITH_CREDIT_CARD
  const handleOpenProcessCreditCard = useCallback(
    (charge: number) => {
      const amount = Number(form.getValues('amount'));

      if (!amount) {
        setAmountEmptyModalOpen(true);
        return;
      }

      if (charge <= 0 && ccConvFee > 0 && refundingConvenienceFees) {
        setOpenRefundConvFeeDialog({
          state: true,
          action: 'process-credit-card',
        });
        return;
      }

      if (charge <= 0 && ccConvFee >= 0 && refundingConvenienceFees) {
        setOpenTransactionDialog({ state: true, action: 'select-transaction' });
        return;
      }

      if (charge > +(balanceDueAmount ?? 0)) {
        setIsOrderTotalGraterModalOpen({
          state: true,
          action: 'process-credit-card',
        });
        return;
      }

      const rawBankAccountId = String(form.getValues('bankAccountId'));
      const convertedBankAccountId =
        rawBankAccountId === '0' || !rawBankAccountId
          ? null
          : Number(rawBankAccountId);

      const details = form.watch();
      setOpen({
        state: true,
        action: PAYMENT_BRED_CRUM_DETAILS.PROCESS_WITH_CREDIT_CARD,
        data: {
          date: details?.date,
          type: details?.type,
          amount: details?.amount,
          reference: details?.reference,
          bankAccountId: convertedBankAccountId,
          charge: charge,
          tax: getTaxFromBreakdown,
          // customer details
          name: customerData?.name ?? '',
          email: customerData?.orderEmail ?? '',
          address: customerData?.address1 ?? '',
          city: customerData?.city ?? '',
          state: Number(customerData?.stateId) ?? 0,
          zipcode: customerData?.zipCode ?? '',
          customerId: customerData?.customerId?.value.toString() ?? '',
          convFee: +getConvFromBreakdown || 0,
          convFeeTax: +getTaxFromBreakdown || 0,
        },
      });

      handleSetActiveTab(PAYMENT_BRED_CRUM_DETAILS.PROCESS_WITH_CREDIT_CARD);
    },
    [
      form,
      ccConvFee,
      refundingConvenienceFees,
      balanceDueAmount,
      setOpen,
      getTaxFromBreakdown,
      customerData,
      getConvFromBreakdown,
      handleSetActiveTab,
    ]
  );

  // Close modal and set action to process with credit card
  const handleOrderTotalGraterModal = useCallback(() => {
    setIsOrderTotalGraterModalOpen({ state: false, action: '' });
    const rawBankAccountId = String(form.getValues('bankAccountId'));
    const convertedBankAccountId =
      rawBankAccountId === '0' || !rawBankAccountId
        ? null
        : Number(rawBankAccountId);
    const details = form.watch();

    setOpen({
      state: true,
      action: PAYMENT_BRED_CRUM_DETAILS.PROCESS_WITH_CREDIT_CARD,
      data: {
        date: details?.date,
        type: details?.type,
        amount: details?.amount,
        reference: details?.reference,
        bankAccountId: convertedBankAccountId,
        charge: getChargeFromBreakdown,
        tax: getTaxFromBreakdown,
        // customer details
        name: customerData?.name ?? '',
        email: customerData?.orderEmail ?? '',
        address: customerData?.address1 ?? '',
        city: customerData?.city ?? '',
        state: Number(customerData?.stateId) ?? 0,
        zipcode: customerData?.zipCode ?? '',
        customerId: customerData?.customerId?.value.toString() ?? '',
        convFee: +getConvFromBreakdown || 0,
        convFeeTax: +getTaxFromBreakdown || 0,
      },
    });
    handleSetActiveTab(PAYMENT_BRED_CRUM_DETAILS.PROCESS_WITH_CREDIT_CARD);
  }, [
    form,
    setOpen,
    getChargeFromBreakdown,
    getTaxFromBreakdown,
    customerData,
    getConvFromBreakdown,
    handleSetActiveTab,
  ]);

  const handleAddPayment = useCallback(
    (refundAmount?: number) => {
      const sendPaymentConfirmation = async (data: AddPaymentType) => {
        const response = await addPayment(data).unwrap();
        if (response) {
          setIsGravityPaymentWarningModalOpen(false);
          setIsOrderTotalGraterModalOpen({ state: false, action: '' });
          handleCancel();
        }
      };

      const sendRefundPaymentConfirmation = async (data: AddPaymentType) => {
        const response = await getRefundAmount(data).unwrap();
        if (response) {
          setIsGravityPaymentWarningModalOpen(false);
          setIsOrderTotalGraterModalOpen({ state: false, action: '' });
          handleCancel();
        }
      };

      const rawBankAccountId = String(form.getValues('bankAccountId'));
      const convertedBankAccountId =
        rawBankAccountId === '0' || !rawBankAccountId
          ? null
          : Number(rawBankAccountId);

      const { date, type, amount, reference } = form.getValues();

      if (amount <= 0) {
        const payloadRefund: AddPaymentType = {
          orderId: Number(orderId),
          date: formatDate(date, DATE_FORMAT_YYYYMMDD),
          paymentTypeId: Number(type),
          amount: refundAmount ? Math.abs(refundAmount) : Math.abs(amount) || 0,
          reference: reference,
          bankAccountId: convertedBankAccountId,
          updateCustomer: false,
          customerId: Number(customerData?.customerId),
        };
        sendRefundPaymentConfirmation(payloadRefund);
      } else {
        const payload: AddPaymentType = {
          orderId: Number(orderId),
          date: formatDate(date, DATE_FORMAT_YYYYMMDD),
          paymentTypeId: Number(type),
          amount: +getChargeFromBreakdown || 0,
          reference: reference,
          bankAccountId: convertedBankAccountId,
          customerId: Number(customerData?.customerId),
          convFee: +getConvFromBreakdown || 0,
          convFeeTax: +getTaxFromBreakdown || 0,
        };

        // 👉 Call your API function
        sendPaymentConfirmation(payload);
      }
    },
    [
      addPayment,
      customerData?.customerId,
      form,
      getChargeFromBreakdown,
      getConvFromBreakdown,
      getRefundAmount,
      getTaxFromBreakdown,
      handleCancel,
      orderId,
    ]
  );

  const [
    fetchAllBankAccounts,
    { data: getAllBankAccounts, isLoading: isLoadingBankAccounts },
  ] = useGetAllMutation();

  const getAllBankAccountsList = generateLabelValuePairs({
    data: getAllBankAccounts?.data,
    labelKey: 'account',
    valueKey: 'id',
  });

  // useEffect(() => {
  //   if (getAllBankAccountsList?.length > 0) {
  //     const firstBankId = getAllBankAccountsList[0].value;
  //     form.setValue('bankAccountId', Number(firstBankId));
  //   }
  // }, [getAllBankAccountsList, form]);

  const [
    fetchAllPaymentTypes,
    { data: getAllPaymentTypes, isLoading: isLoadingPaymentTypes },
  ] = useGetAllMutation();

  const getAllPaymentTypesList = generateLabelValuePairs({
    data: getAllPaymentTypes?.data,
    labelKey: 'paymentMethod',
    valueKey: 'id',
  })?.sort((a, b) => a.label.localeCompare(b.label));

  const disallowedMethods = useMemo(
    () => [
      'cash',
      'check',
      'credit adjustment',
      'gift card',
      'money order',
      'paypal',
      'trade',
      'zero sum',
    ],
    []
  );

  const notAllowedGravityPaymentMethods = useMemo(
    () => [
      'credit card',
      'debit card',
      'discover',
      'american express',
      'ach',
      'mastercard',
      'refund',
      'visa',
    ],
    []
  );

  useEffect(() => {
    const fetchData = async () => {
      const payload = {
        pageNumber: 0,
        pageSize: 0,
        sortBy: '',
        sortAscending: false,
        filters: [],
      };
      await fetchAllBankAccounts({
        url: BANK_ACCOUNTS_API_ROUTES.ALL,
        body: payload,
      });
      await fetchAllPaymentTypes({
        url: PAYMENT_TYPE_API_ROUTES.ALL,
        body: payload,
      });
    };
    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const renderDialogTitleClassName = (action: string) => {
    let title;
    let className;
    switch (action) {
      case 'amount':
        title = 'Breakdown';
        className = 'md:max-w-[30%] 2xl:max-w-[20%]';
        break;
      case 'payment-amount':
        title = 'Payment Selection';
        className = 'md:max-w-[40%] 2xl:max-w-[35%]';
        break;
      case 'card-details':
        title = 'Check Credit Card';
        className = 'md:max-w-[35%] 2xl:max-w-[28%]';
        break;
      default:
        title = 'Breakdown';
        className = '';
    }
    return { title, className };
  };

  const handlePaymentSubmit = useCallback(
    (data: PaymentData) => {
      form.reset({
        ...form.getValues(),
        amount: data.amount,
      });
      setSelectedPaymentType(data);
      calculateConvenienceFeeIfApplicable();
    },
    [calculateConvenienceFeeIfApplicable, form]
  );

  const handleApplyCardType = async (cardType: string) => {
    const mappedPaymentType = CARD_TYPE_MAPPING[cardType];

    if (!mappedPaymentType) {
      return;
    }

    const paymentDetails = getAllPaymentTypes?.data?.find(
      (item: any) => item?.paymentMethod === mappedPaymentType
    );
    if (paymentDetails?.id) {
      form.setValue('type', paymentDetails?.id);
      form.setValue('bankAccountId', paymentDetails?.bankAccountId || null);
      setSelectedCardType(paymentDetails);
      await handlePaymentTypeChange(paymentDetails?.id);
    }
  };

  // Function to render the appropriate content in the custom dialog based on the action type
  const renderDialogContent = (
    action: string,
    toggleDialog: () => void,
    breakdownForm: any
  ) => {
    switch (action) {
      case 'amount':
        return (
          <Amount
            toggle={toggleDialog}
            breakdownForm={breakdownForm}
            form={form}
            setTotalAmount={setTotalAmount}
            setIsConvenienceFeeApplicable={setIsConvenienceFeeApplicable}
          />
        );
      case 'payment-amount':
        return (
          <PaymentAmount
            toggle={toggleDialog}
            dataCalculation={mergeDataCalculation}
            selectedPaymentType={selectedPaymentType}
            onSubmit={(data: PaymentData) => {
              handlePaymentSubmit(data);
            }}
          />
        );
      case 'card-details':
        return (
          <CreditCards toggle={toggleDialog} onConfirm={handleApplyCardType} />
        );
      default:
        return null;
    }
  };

  const { title, className } = renderDialogTitleClassName(openDialog.action);

  // Function to toggle the custom dialog state and clear any existing query parameters
  const toggleDialog = () => {
    setOpenDialog({ state: false, action: '' });
  };

  // set default credit card
  useEffect(() => {
    if (getAllPaymentTypes?.data?.length) {
      const creditCardType = getAllPaymentTypes?.data?.find(
        (item: any) => item.id === paymentType_Id
      );

      if (creditCardType) {
        form.setValue('type', creditCardType.id);
        form.setValue('bankAccountId', creditCardType.bankAccountId || null);
        setSelectedCardType(creditCardType);
      }
    }
  }, [getAllPaymentTypes, form, paymentType_Id]);

  // get selected card type
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === 'type') {
        const selectedId = value?.type;
        if (selectedId && getAllPaymentTypes?.data?.length) {
          const matchedType = getAllPaymentTypes?.data?.find(
            (item: any) => item.id === selectedId
          );

          setSelectedCardType(matchedType ?? null);

          if (matchedType?.bankAccountId) {
            form.setValue('bankAccountId', matchedType.bankAccountId || null);
          } else {
            form.setValue('bankAccountId', null);
          }
        }
      }
    });

    return () => subscription.unsubscribe();
  }, [form, getAllPaymentTypes]);

  useEffect(() => {
    if (selectedCardType) {
      const paymentMethod = selectedCardType.paymentMethod?.toLowerCase() ?? '';

      setDisabledCreditCardACHPayment(
        disallowedMethods.includes(paymentMethod)
      );
      setIsGravityPaymentMethod(
        notAllowedGravityPaymentMethods.includes(paymentMethod)
      );
    } else {
      setIsGravityPaymentMethod(false);
      setDisabledCreditCardACHPayment(false);
    }
  }, [disallowedMethods, notAllowedGravityPaymentMethods, selectedCardType]);

  const handleOk = async () => {
    await calculateConvenienceFeeIfApplicable();
    const values = form.getValues(); // Get all form values
    const amount = Number(values.amount);

    // check if amount is empty
    if (!amount) {
      setAmountEmptyModalOpen(true);
      return;
    }

    // handle open modal for selected payment method
    if (isGravityPaymentMethod) {
      setIsGravityPaymentWarningModalOpen(true);
      return;
    }

    handleAddPayment();
  };

  const handleWithoutGravityPaymentModal = () => {
    // Refund
    if (+getChargeFromBreakdown <= 0) {
      setIsGravityPaymentWarningModalOpen(false);
      if (ccConvFee > 0 && refundingConvenienceFees) {
        setOpenRefundConvFeeDialog({
          state: true,
          action: 'gravity',
        });
      } else if (+getChargeFromBreakdown > +(balanceDueAmount ?? 0)) {
        setIsGravityPaymentWarningModalOpen(false);
        setIsOrderTotalGraterModalOpen({ state: true, action: 'gravity' });
      } else {
        handleAddPayment();
      }
    } else if (+getChargeFromBreakdown > 0) {
      if (+getChargeFromBreakdown > +(balanceDueAmount ?? 0)) {
        setIsGravityPaymentWarningModalOpen(false);
        setIsOrderTotalGraterModalOpen({ state: true, action: 'gravity' });
      } else {
        handleAddPayment();
      }
    }
  };

  const onOpenChangeRefundConvFeeDialog = useCallback(() => {
    setOpenRefundConvFeeDialog({ state: false, action: '' });
  }, [setOpenRefundConvFeeDialog]);

  const onOpenChangeTransactionDialog = () => {
    handleCancel();
    setOpenTransactionDialog({ state: false, action: '' });
  };

  const refundIncludingConvFeeLabel = useMemo(() => {
    return `Refund including convenience fee: ${convertToFloat({
      value: chargeValue - ccConvFee,
      prefix: '$',
    })}`;
  }, [ccConvFee, chargeValue]);

  const refundOriginalAmountLabel = useMemo(() => {
    return `Refund for the original amount only: ${convertToFloat({
      value: chargeValue,
      prefix: '$',
    })}`;
  }, [chargeValue]);

  useEffect(() => {
    if (orderId) {
      form.setValue('refundOrderId', Number(orderId));
    }
  }, [form, orderId]);

  const handleRefundConvFee = useCallback(() => {
    if (openRefundConvFeeDialog.action === 'gravity') {
      if (+getChargeFromBreakdown > +(balanceDueAmount ?? 0)) {
        setOpenRefundConvFeeDialog({ state: false, action: '' });
        setIsOrderTotalGraterModalOpen({ state: true, action: 'gravity' });
      } else if (+getChargeFromBreakdown <= +(balanceDueAmount ?? 0)) {
        handleAddPayment();
      }
    } else if (openRefundConvFeeDialog.action === 'process-credit-card') {
      if (+getChargeFromBreakdown > +(balanceDueAmount ?? 0)) {
        setOpenRefundConvFeeDialog({ state: false, action: '' });
        setIsOrderTotalGraterModalOpen({
          state: true,
          action: 'process-credit-card',
        });
      } else {
        setOpenRefundConvFeeDialog({ state: false, action: '' });
        setOpenTransactionDialog({ state: true, action: 'select-transaction' });
      }
    }
  }, [
    balanceDueAmount,
    getChargeFromBreakdown,
    handleAddPayment,
    openRefundConvFeeDialog.action,
  ]);

  const handleSubmitTotalPayment = useCallback(() => {
    if (isOrderTotalGraterModalOpen.action === 'process-credit-card') {
      if (+getChargeFromBreakdown <= 0) {
        setIsOrderTotalGraterModalOpen({ state: false, action: '' });
        setOpenTransactionDialog({ state: true, action: 'select-transaction' });
      } else {
        handleOrderTotalGraterModal();
      }
    } else if (isOrderTotalGraterModalOpen.action === 'gravity') {
      const refundAmount = form.getValues('refundAmountType');
      if (refundAmount === 'refundIncludingConvFee') {
        handleAddPayment(chargeValue - ccConvFee);
      } else if (refundAmount === 'refundOriginalAmount') {
        handleAddPayment(chargeValue);
      }
    }
  }, [
    ccConvFee,
    chargeValue,
    form,
    getChargeFromBreakdown,
    handleAddPayment,
    handleOrderTotalGraterModal,
    isOrderTotalGraterModalOpen.action,
  ]);

  const handleTransaction = useCallback(() => {
    const rawBankAccountId = String(form.getValues('bankAccountId'));
    const convertedBankAccountId =
      rawBankAccountId === '0' || !rawBankAccountId
        ? null
        : Number(rawBankAccountId);

    let totalAmount;
    if (ccConvFee > 0 && refundingConvenienceFees) {
      const refundAmount = form.getValues('refundAmountType');
      if (refundAmount === 'refundIncludingConvFee') {
        totalAmount = chargeValue - ccConvFee;
      } else if (refundAmount === 'refundOriginalAmount') {
        totalAmount = chargeValue;
      }
    } else {
      totalAmount = form.getValues('amount');
    }
    const cardNumber = selectedPayment[0].cardNumber;
    const paymentId = selectedPayment[0].id;

    setOpen({
      state: true,
      action: PAYMENT_BRED_CRUM_DETAILS.PROCESS_WITH_REFUND,
      data: {
        date: form.getValues('date'),
        type: form.getValues('type'),
        amount: totalAmount,
        reference: form.getValues('reference'),
        bankAccountId: convertedBankAccountId,
        charge: getChargeFromBreakdown,
        cardNumber,
        paymentId,
        refundConvFee:
          form.getValues('refundAmountType') === 'refundIncludingConvFee'
            ? true
            : false,
      },
    });

    handleSetActiveTab(PAYMENT_BRED_CRUM_DETAILS.PROCESS_WITH_REFUND);
  }, [
    ccConvFee,
    chargeValue,
    form,
    getChargeFromBreakdown,
    handleSetActiveTab,
    refundingConvenienceFees,
    selectedPayment,
    setOpen,
  ]);

  const { data, isFetching: isLoading } = useGetPaymentDetailsQuery(
    {
      body: {
        pageNumber: 0,
        pageSize: 0,
        sortBy: 'id',
        sortAscending: true,
        filters: [{ field: 'orderId', value: orderId, operator: 'Equals' }],
      },
    },
    { skip: !orderId }
  );

  const paymentDetailsData = data?.data?.filter(
    (data: ExtendedPaymentInfoDTO) =>
      data.isDeleted === false &&
      data.isOnlinePayment === true &&
      data.transactionOperation === 'PAYMENT'
  );

  const handlePaymentTypeChange = useCallback(
    async (value: string) => {
      const charge = parseFloat(
        breakdownForm.getValues('charge')?.toString() || '0'
      );
      form.setValue('type', value);
      if (charge > 0) {
        try {
          const Responses = await calculateConvFeeTax({
            orderId,
            paymentTypeId: form.getValues('type'),
            amount: charge,
          }).unwrap();

          const convFee = Responses?.data?.convFee;
          const convFeeTax = Responses?.data?.convFeeTax;
          const total = Responses?.data?.total;

          // Update main amount field with full value
          setValue('amount', +total.toFixed(2));
          setTotalAmount(+total.toFixed(2));
          breakdownForm.setValue('charge', charge.toString());
          breakdownForm.setValue('convenienceFree', convFee.toString());
          breakdownForm.setValue('tax', convFeeTax.toString());
          breakdownForm.setValue('totalCharge', total.toString());
          setIsConvenienceFeeApplicable(convFee > 0);
        } catch (error) {
          // console.error('Error :', error);
        }
      }
    },
    [breakdownForm, calculateConvFeeTax, form, orderId, setValue]
  );

  const columns = useMemo<ColumnDef<ExtendedPaymentInfoDTO>[]>(() => {
    return [
      {
        accessorKey: 'cardType',
        header: 'Payment Type',
        cell: ({ row }: any) => {
          return row?.original?.cardType == null
            ? row?.original?.paymentMethodName
            : row?.original?.cardType;
        },
      },
      {
        accessorKey: 'amount',
        header: 'Payment Amount',
        cell: ({ row }: any) =>
          convertToFloat({ value: row?.original?.amount, prefix: '$' }),
        size: 150,
      },
      {
        accessorKey: 'reference',
        header: 'Payment Reference',
        size: 200,
      },
      {
        accessorKey: 'cardNumber',
        header: 'Credit Card/ACH #',
        size: 250,
        cell: ({ row }: any) => {
          const cardNumber = String(row?.original?.cardNumber ?? '');
          return (
            <span className="tracking-wide text-base font-sans">
              {cardNumber.includes('*')
                ? cardNumber.replace(/\*/g, '\u002A')
                : ''}
            </span>
          );
        },
      },
      {
        accessorKey: 'user',
        header: 'User',
        size: 200,
      },
    ];
  }, []);

  const handleCalculateFeeThenOpenModal = async () => {
    await calculateConvenienceFeeIfApplicable(); // Wait until fee is done
    const charge = Number(breakdownForm.getValues('charge'));
    handleOpenProcessCreditCard(charge); // Now open modal
  };

  const handleAmountBlur = () => {
    setTimeout(() => {
      const activeElement = document.activeElement as HTMLElement;

      const clickedInsideButtons =
        actionButtonsRef.current?.contains(activeElement);

      if (!clickedInsideButtons) {
        calculateConvenienceFeeIfApplicable();
      }
    }, 0);
  };

  return (
    <div className="space-y-6">
      {/* Action Buttons */}
      <div className="flex flex-wrap gap-4 px-2">
        <AppButton
          label="Select Payment Amount"
          onClick={() => {
            setOpenDialog({
              state: true,
              action: 'payment-amount',
            }),
              handleOk;
          }}
          className="w-60"
          variant="neutral"
        />
        <AppButton
          label="Check Card Details"
          onClick={() => {
            setOpenDialog({
              state: true,
              action: 'card-details',
            }),
              handleOk;
          }}
          className="w-60"
          variant="neutral"
        />
      </div>

      {/* Form Inputs Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-5 max-h-[550px] overflow-y-auto p-2">
        <DatePicker name="date" form={form} enableInput label="Date" />

        <SelectDropDown
          form={form}
          optionsList={getAllPaymentTypesList ?? []}
          name="type"
          label="Type"
          placeholder="Select Type"
          allowClear={false}
          onChange={handlePaymentTypeChange}
        />

        <div className="flex items-end gap-3">
          <div className="flex-1 relative">
            <NumberInputField
              form={form}
              name="amount"
              label="Amount"
              placeholder="Enter Amount"
              maxLength={9}
              prefix="$"
              fixedDecimalScale
              onBlur={handleAmountBlur}
              allowNegative
            />
            {isConvenienceFeeApplicable && (
              <small className="text-red-500 absolute bottom-[-20px] ">
                * A convenience fee has been applied
              </small>
            )}
          </div>
          <AppButton
            label=""
            icon={CircleAlert}
            spinnerClass="border-white border-t-transparent animate-spin"
            className="bg-white border hover:bg-white text-text-Default border-border-Default mt-8"
            iconClassName="w-5"
            onClick={() =>
              setOpenDialog({
                state: true,
                action: 'amount',
              })
            }
            tooltip="Breakdown"
          />
        </div>

        <InputField
          name="reference"
          label="Reference #"
          form={form}
          placeholder="Enter Reference"
          maxLength={35}
        />

        <SelectDropDown
          form={form}
          optionsList={getAllBankAccountsList ?? []}
          name="bankAccountId"
          label="Bank Account"
          placeholder="Select Bank Account"
        />
      </div>

      {/* Footer Actions */}
      <div className="fixed bottom-0 left-0 right-0 bg-white px-8 py-4 shadow-md border-t border-gray-200">
        <div
          className="flex justify-between items-center gap-4 w-full"
          ref={actionButtonsRef}
        >
          <div className="flex justify-start">
            <AppButton
              label="Process Credit Card/ACH"
              onClick={handleCalculateFeeThenOpenModal}
              className="w-full sm:w-60"
              disabled={disabledCreditCardACHPayment}
            />
          </div>
          <div className="flex justify-end gap-4">
            <AppButton
              label="OK"
              onClick={handleOk}
              className="w-full sm:w-28"
              disabled={isLoadingBankAccounts || isLoadingPaymentTypes}
              isLoading={isLoadingAddPayment || isLoadingRefund}
            />
            <AppButton
              label="Cancel"
              onClick={handleCancel}
              className="w-full sm:w-28"
              variant="neutral"
            />
          </div>
        </div>
      </div>
      <CustomDialog
        onOpenChange={toggleDialog}
        description=""
        open={openDialog.state}
        className={cn('max-h-[96%] overflow-auto', className)}
        title={title}
      >
        <>
          {renderDialogContent(openDialog.action, toggleDialog, breakdownForm)}
        </>
      </CustomDialog>

      <AppConfirmationModal
        title="Confirmation"
        description={
          <div>
            Are you sure you want to apply this credit card payment without
            processing through Gravity Payments?
          </div>
        }
        open={isGravityPaymentWarningModalOpen}
        handleCancel={() => setIsGravityPaymentWarningModalOpen(false)}
        handleSubmit={handleWithoutGravityPaymentModal}
        isLoading={isLoadingAddPayment || isLoadingRefund}
      />

      <AppConfirmationModal
        title="Confirmation"
        description={
          <div>
            This payment will make the total payments greater than the order
            total. Do you want to continue adding this payment?
          </div>
        }
        open={isOrderTotalGraterModalOpen.state}
        handleCancel={() => {
          setIsGravityPaymentWarningModalOpen(false);
          setIsOrderTotalGraterModalOpen({ state: false, action: '' });
          setOpenRefundConvFeeDialog({ state: false, action: '' });
        }}
        handleSubmit={handleSubmitTotalPayment}
        isLoading={isLoadingAddPayment || isLoadingRefund}
      />

      <AppConfirmationModal
        title={'Warning'}
        description={
          <>
            <div>Please enter an amount.</div>
          </>
        }
        open={isAmountEmptyModalOpen}
        handleSubmit={() => {
          setAmountEmptyModalOpen(false);
        }}
        submitLabel="OK"
      />
      <CustomDialog
        open={openRefundConvFeeDialog.state}
        onOpenChange={onOpenChangeRefundConvFeeDialog}
        title="Refund Convenience Fee"
        contentClassName="py-4"
        className="xl:max-w-1xl overflow-auto"
      >
        <div className="px-6 flex flex-col gap-4">
          <InputField
            name="refundOrderId"
            form={form}
            label="Order #"
            disabled
          />
          <div className="flex items-center justify-between">
            <p>Do you want to refund the convenience fee for this order?</p>
          </div>
          <div className="flex flex-col gap-4">
            <RadioField
              name="refundAmountType"
              form={form}
              options={[
                {
                  label: refundIncludingConvFeeLabel,
                  value: 'refundIncludingConvFee',
                },
                {
                  label: refundOriginalAmountLabel,
                  value: 'refundOriginalAmount',
                },
              ]}
              optionsPerRow={1}
            />
            <div className="flex justify-end bottom-3 pt-3 right-6 space-x-4">
              <AppButton
                onClick={handleRefundConvFee}
                label="OK"
                className="w-28"
                isLoading={isLoadingRefund}
              />

              <AppButton
                onClick={() => {
                  onOpenChangeRefundConvFeeDialog();
                }}
                label="Cancel"
                className="w-28"
                variant="neutral"
              />
            </div>
          </div>
        </div>
      </CustomDialog>

      <CustomDialog
        open={openTransactionDialog.state}
        onOpenChange={onOpenChangeTransactionDialog}
        title="Select Transaction"
        contentClassName="py-4"
        className="xl:max-w-6xl overflow-auto"
      >
        <div className="px-6 flex flex-col gap-4">
          <div>
            <DataTable
              data={paymentDetailsData || []}
              bindingKey="id"
              columns={columns}
              heading="Saved Payments"
              enablePagination={false}
              loaderRows={11}
              tableClassName="max-h-[500px] 2xl:max-h-[550px] overflow-auto"
              manualSorting={false}
              isLoading={isLoading}
              noDataPlaceholder="No Data Found."
              enableRowSelection
              rowSelection={selectedRows}
              onRowSelectionChange={setSelectedRows}
              onRowsSelected={setSelectedPayment}
            />
          </div>
          <div className="flex flex-col gap-4">
            <div className="flex justify-end bottom-3 pt-3 right-6 space-x-4">
              <AppButton
                onClick={handleTransaction}
                disabled={!selectedIds.length}
                label="OK"
                className="w-28"
                // isLoading={isLoadingAddPayment}
              />

              <AppButton
                onClick={() => {
                  onOpenChangeTransactionDialog();
                  handleCancel();
                }}
                label="Cancel"
                className="w-28"
                variant="neutral"
              />
            </div>
          </div>
        </div>
      </CustomDialog>
      {/* <AppSpinner overlay isLoading={isLoadingConvFeeTax} /> */}
    </div>
  );
};

export default NewPayment;
