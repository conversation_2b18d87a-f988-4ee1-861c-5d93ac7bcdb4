import CloseIcon from '@/assets/icons/CloseIcon';
import EditIcon from '@/assets/icons/EditIcon';
import SubmitIcon from '@/assets/icons/SubmitIcon';
import AppSpinner from '@/components/common/app-spinner';
import { Button } from '@/components/ui/button';
import { UseToast } from '@/components/ui/toast/ToastContainer';
import { convertToFloat, getQueryParam } from '@/lib/utils';
import { useUpdateOrderDiscountMutation } from '@/redux/features/customers/discount.api';
import { TotalPaymentsTypes } from '@/types/order.types';
import { useState } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { NumericFormat } from 'react-number-format';

interface DiscountCellProps {
  row: any;
  setDiscountData: any;
  discountData: any;
  refetchDiscounts: any;
  form: UseFormReturn<TotalPaymentsTypes>;
}

const DiscountCell = ({
  row,
  setDiscountData,
  discountData,
  refetchDiscounts,
  form,
}: DiscountCellProps) => {
  const id = getQueryParam('id') as string;

  const [isEditing, setIsEditing] = useState(false);
  const [discount, setDiscount] = useState<string>(
    row?.original?.discount ? `${row?.original?.discount}` : ''
  );
  const [, setIsFocused] = useState(false);
  const [updateDiscount, { isLoading }] = useUpdateOrderDiscountMutation();

  // Sanitizing the discount input to remove special characters
  const sanitizeDiscount = (input: string) => {
    return input.replace(/%/g, '').replace(/_/g, '0');
  };

  // Toggle edit mode
  const handleEditClick = () => setIsEditing(true);

  // Save the discount and reset edit mode
  const handleSaveClick = async () => {
    const sanitizedDiscount = sanitizeDiscount(discount);

    const body = {
      orderId: Number(id),
      discounts: [
        {
          categoryId: row?.original?.categoryId,
          discount: Number(sanitizedDiscount),
          id: row?.original?.id,
        },
      ],
    };

    const response = await updateDiscount(body);

    const selectedCategoryIds = new Set(
      response?.data?.data?.map((selected: any) => selected.categoryId)
    );

    const updatedDiscountData = discountData.map((element: any) => {
      const isSelected = selectedCategoryIds.has(element.categoryId);

      return {
        ...element,
        id: isSelected
          ? (response?.data?.data?.find(
              (selected: any) => selected.categoryId === element.categoryId
            )?.id ?? null)
          : element.id,
        discount: isSelected
          ? (response?.data?.data?.find(
              (selected: any) => selected.categoryId === element.categoryId
            )?.discount ?? null)
          : element.discount,
      };
    });
    const paymentData = response?.data;
    form.setValue('discount', paymentData?.discount);
    form.setValue('grandTotal', paymentData?.grandTotal);
    form.setValue('discountAmount', paymentData?.discount);

    const discountPercentage: any =
      (parseFloat(paymentData?.discount) / paymentData?.subTotal) * 100;
    form.setValue('discountPercentage', discountPercentage.toString());
    setDiscount(sanitizedDiscount);
    setDiscountData(updatedDiscountData);
    UseToast().success(response?.data?.message ?? '');
    setIsEditing(false);
    refetchDiscounts();
  };

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setDiscount(e.target.value);
  };

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = () => {
    setIsFocused(false);
  };

  // Cancel editing and reset the discount value
  const handleCancelClick = () => {
    setDiscount(`${row?.original?.discount}`);
    setIsEditing(false);
  };

  // Check if the row is selected
  const shouldShowEditIcon = row.getIsSelected();
  return (
    <div className="flex items-center justify-between h-8">
      {/* Conditionally render based on the editing state */}
      {isEditing ? (
        <div className="flex items-center justify-between gap-4 w-full">
          <NumericFormat
            value={discount}
            placeholder="__.__%"
            decimalScale={2}
            fixedDecimalScale
            isAllowed={({ formattedValue }) => formattedValue.length <= 6}
            suffix={'%'}
            className="flex h-8 w-[100%] rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground  focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
            onFocus={handleFocus}
            onBlur={handleBlur}
            onChange={handleInputChange}
          />

          <Button
            variant="ghost"
            className="p-0"
            onClick={handleSaveClick}
            disabled={!discount || isLoading}
          >
            {isLoading ? <AppSpinner className="w-4 h-4" /> : <SubmitIcon />}
          </Button>
          <Button variant="ghost" className="p-0" onClick={handleCancelClick}>
            <CloseIcon />
          </Button>
        </div>
      ) : (
        <div className="flex items-center justify-between w-full">
          {/* Display the discount or a placeholder */}
          <span>
            {Number(discount) > 0
              ? convertToFloat({ value: discount, postfix: '%' })
              : '---'}
          </span>
          {/* Show edit icon only when row is selected */}
          {shouldShowEditIcon && (
            <div className="flex gap-3">
              <div onClick={handleEditClick}>
                <EditIcon />
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default DiscountCell;
