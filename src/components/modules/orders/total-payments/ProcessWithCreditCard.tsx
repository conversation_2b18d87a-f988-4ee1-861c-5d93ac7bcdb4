import AppButton from '@/components/common/app-button';
import AppCheckbox from '@/components/common/app-checkbox/AppCheckbox';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import AppSpinner from '@/components/common/app-spinner';
import DatePicker from '@/components/forms/date-picker';
import InputField from '@/components/forms/input-field';
import NumberInputField from '@/components/forms/number-input-field';
import SelectDropDown from '@/components/forms/select-dropdown';
import { PAYMENT_TYPE_API_ROUTES } from '@/constants/api-constants';
import {
  DATE_FORMAT_YYYYMMDD,
  formatDate,
  generateLabelValuePairs,
  getQueryParam,
} from '@/lib/utils';
import { useGetAllMutation } from '@/redux/features/common-api/common.api';
import { useGetStateByCountryQuery } from '@/redux/features/country/country.api';
import {
  useGetCustomerCardInfoQuery,
  useGetPaymentLinkMutation,
  useRemoveCustomerCardMutation,
} from '@/redux/features/orders/order.api';
import { NewPaymentDTO, ProcessWithCreditCardDTO } from '@/types/order.types';
import { Label } from '@radix-ui/react-label';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { SubmitHandler, useForm, useWatch } from 'react-hook-form';

interface ProcessWithCreditCardTypes {
  state: boolean;
  action: string;
}

interface ProcessWithCreditCardProps {
  setOpen: React.Dispatch<React.SetStateAction<ProcessWithCreditCardTypes>>;
  data: NewPaymentDTO;
  toggleToNewPayment: () => void;
  refetchPaymentDetails: () => void;
}

const ProcessWithCreditCard = ({
  setOpen,
  data,
  refetchPaymentDetails,
}: ProcessWithCreditCardProps) => {
  const id = getQueryParam('id') as string;
  const form = useForm<ProcessWithCreditCardDTO>();
  const [openDelete, setOpenDelete] = useState<boolean>(false);

  const getChargeAmount = data?.charge;
  const getTaxAmount = data?.tax;
  const getBankAccountId = data?.bankAccountId;
  const getReference = data?.reference;

  // //get customer data from context
  // const orderInfoForm = useFormContext<OrderInformationTypes>();

  // const customerData = orderInfoForm?.watch('billTo');
  // const customerId = customerData?.customerId?.value;
  const customerId = data?.customerId;

  const defaultValues = useMemo(() => {
    return {
      date: data?.date || '', // Use data.date if available
      type: data?.type || '', // Use data.type if available
      amount: data?.amount || 0, // Use data.amount if available
      reference: data?.reference || '', // Use data.reference if available
      // name: customerData?.name || '',
      // email: customerData?.orderEmail || '',
      // address: customerData?.address1 || '',
      // city: customerData?.city || '',
      // state: +(customerData?.stateId ?? 0) || 0,
      // zipcode: customerData?.zipCode || '',
      name: data?.name ?? '',
      email: data?.email ?? '',
      address: data?.address ?? '',
      city: data?.city ?? '',
      state: data?.state ?? '',
      zipcode: data?.zipcode ?? '',
      bankAccountId: data?.bankAccountId || null,
      convFee: data?.convFee ?? 0,
      convFeeTax: data?.convFeeTax ?? 0,
      cardExpiry: '',
    };
  }, [data]);

  const isReuseCCChecked = useWatch({
    control: form.control,
    name: 'reuseCC',
  });

  const isAchType = data?.type == '13'; // If type is ACH then disable the reuse CC/ACH checkbox

  const handleProcess = () => {
    setOpen({ state: false, action: '' });
  };

  // Customer card info API
  const { data: customerCardInfoData } = useGetCustomerCardInfoQuery(
    { customerId },
    {
      skip: !customerId,
    }
  );

  const customerCardList = generateLabelValuePairs({
    data: customerCardInfoData?.data,
    labelKey: 'cardNumber',
    valueKey: 'id',
  });

  const [RemoveCustomerCard, { isLoading: deleteLoading }] =
    useRemoveCustomerCardMutation();

  const toggleDelete = useCallback(() => {
    setOpenDelete((prev) => !prev);
  }, []);

  const selectedCardId = form.watch('reuseOption');
  const handleDelete = useCallback(async () => {
    try {
      await RemoveCustomerCard(selectedCardId).unwrap();
      toggleDelete();
      form.setValue('reuseOption', null);
    } catch (err) {}
  }, [RemoveCustomerCard, form, selectedCardId, toggleDelete]);

  // get payment link API
  const [getPaymentLink, { isLoading: isLoadingPaymentLink }] =
    useGetPaymentLinkMutation();

  // Payment Details Reuse CC/ACH
  // const [processPaymentReuseCCACH, { isLoading: isLoadingPaymentReuse }] =
  //   useProcessPaymentReuseCCACHMutation();

  const [
    fetchAllPaymentTypes,
    { data: getAllPaymentTypes, isLoading: isLoadingPaymentTypes },
  ] = useGetAllMutation();

  const getAllPaymentTypesList = generateLabelValuePairs({
    data: getAllPaymentTypes?.data,
    labelKey: 'paymentMethod',
    valueKey: 'id',
  });

  useEffect(() => {
    if (defaultValues) {
      form.reset(defaultValues);
    }
  }, [defaultValues, form]);

  // state data
  const { data: statesData, isFetching: stateIsLoading } =
    useGetStateByCountryQuery({
      countryId: 1,
    });
  // states list
  const stateList = generateLabelValuePairs({
    data: statesData,
    labelKey: 'code',
    valueKey: 'state_id',
  });
  useEffect(() => {
    const fetchData = async () => {
      const payload = {
        pageNumber: 0,
        pageSize: 0,
        sortBy: '',
        sortAscending: false,
        filters: [],
      };
      await fetchAllPaymentTypes({
        url: PAYMENT_TYPE_API_ROUTES.ALL,
        body: payload,
      });
    };
    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const onProcessSubmit: SubmitHandler<ProcessWithCreditCardDTO> = async (
    formData
  ) => {
    const formFields = {
      orderId: +id,
      date: formatDate(formData.date, DATE_FORMAT_YYYYMMDD),
      paymentTypeId: formData.type,
      amount: +(getChargeAmount ?? 0),
      convFee: formData?.convFee ?? 0,
      convFeeTax: formData?.convFeeTax ?? 0,
      tax: +(getTaxAmount ?? 0) || 0,
      billingName: formData.name,
      billingAddress: formData.address,
      billingCity: formData.city,
      billingZipCode: formData.zipcode,
      email: formData.email,
      updateCustomer: formData?.updateCustomer,
      stateId: formData?.state,
      reuseCC: isReuseCCChecked || false,
      bankAccountId: getBankAccountId ?? null,
      reference: getReference ?? '',
      customerCardDetailId: formData?.reuseOption || null,
      customerId: Number(customerId),
    };

    try {
      if (isReuseCCChecked) {
        const responsePaymentLink = await getPaymentLink(formFields).unwrap();
        if (responsePaymentLink && responsePaymentLink?.data) {
          handleProcess();
        }
      } else {
        const responsePaymentLink = await getPaymentLink(formFields).unwrap();
        if (responsePaymentLink && responsePaymentLink?.data) {
          handleProcess();
          const url = responsePaymentLink.data;
          // window.open(url, '_blank');
          const newTab = window.open(url, '_blank');

          if (newTab) {
            let hasRefetched = false;

            const checkTabClosed = setInterval(() => {
              if (newTab.closed && !hasRefetched) {
                hasRefetched = true;
                clearInterval(checkTabClosed);
                refetchPaymentDetails();
              }
            }, 500);
          }
        }
      }
    } catch (error) {}
  };

  const handleReuseOptionChange = (value: any) => {
    form.setValue('reuseOption', value); // set selected value manually

    const selectedCard = customerCardInfoData?.data?.find(
      (card: any) => card.id === value
    );

    form.setValue('cardExpiry', selectedCard?.cardExpiry || '');
  };

  return (
    <div className="space-y-5 pb-24">
      {/* Date, Type, Amount */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <DatePicker name="date" form={form} enableInput label="Date" disabled />
        <SelectDropDown
          form={form}
          optionsList={getAllPaymentTypesList ?? []}
          name="type"
          label="Type"
          placeholder="Select Type"
          disabled
        />
        <NumberInputField
          form={form}
          name="amount"
          label="Amount"
          placeholder="Enter Amount"
          prefix="$"
          fixedDecimalScale
          disabled
        />
      </div>

      {/* Cardholder Info */}
      <div className="flex items-center justify-start gap-2">
        <h3 className="text-lg font-semibold">Cardholder Info</h3>
        <span>( * Missing info may result in higher fees )</span>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <InputField
          name="name"
          label="Name"
          form={form}
          placeholder="Enter Name"
        />
        <InputField
          name="email"
          label="E-mail Receipt"
          form={form}
          placeholder="Enter Email"
        />
        <InputField
          name="address"
          label="Address"
          form={form}
          placeholder="Enter Address"
        />
        <InputField
          name="city"
          label="City"
          form={form}
          placeholder="Enter City"
        />
        {/* <InputField
          form={form}
          name="state"
          label="State"
          placeholder="Select State"
          maxLength={2}
        /> */}
        <SelectDropDown
          name="state"
          form={form}
          placeholder="Select State"
          label="State"
          allowClear={true}
          optionsList={stateList}
          isLoading={stateIsLoading}
        />
        <InputField
          name="zipcode"
          label="Zip Code"
          form={form}
          placeholder="Enter Zip Code"
        />
      </div>

      {/* Update Customer Checkbox */}
      <div className="flex items-center gap-2">
        <AppCheckbox
          name="updateCustomer"
          control={form.control}
          className="data-[state=checked]:bg-[#5f26c9] text-white"
        />
        <Label
          htmlFor="updateCustomer"
          className="text-sm sm:text-base font-medium text-black"
        >
          Update Customer Record{' '}
          <span className="text-xs"> ( * Does not update customer name)</span>
        </Label>
      </div>

      {/* Reuse CC/ACH section */}
      <div className="flex items-end gap-3">
        <div className="flex items-center gap-2 flex-1">
          <AppCheckbox
            name="reuseCC"
            control={form.control}
            className="data-[state=checked]:bg-[#5f26c9] text-white"
            disabled={isAchType}
          />
          <Label
            htmlFor="reuseCC"
            className="text-sm sm:text-base font-medium text-black"
          >
            Reuse CC/ACH
          </Label>
        </div>
        <div className="flex-1">
          <SelectDropDown
            form={form}
            name="reuseOption"
            optionsList={customerCardList ?? []}
            placeholder="Select Option"
            disabled={!isReuseCCChecked}
            onChange={handleReuseOptionChange}
            itemClassName="tracking-wide text-base font-sans"
          />
        </div>
        <InputField
          name="cardExpiry"
          label=""
          form={form}
          placeholder="Card Expiry Date"
          disabled
        />
        <AppButton
          label="Remove"
          onClick={toggleDelete}
          variant="neutral"
          disabled={!isReuseCCChecked || !selectedCardId}
        />
      </div>

      {/* Footer Buttons */}
      <div className="fixed bottom-0 left-0 right-0 bg-white px-5 py-4 shadow-md border-t border-gray-200">
        <div className="flex justify-between items-center gap-4 w-full">
          <AppButton
            label="Process"
            onClick={form.handleSubmit(onProcessSubmit)}
            disabled={isLoadingPaymentTypes}
          />
          <AppButton
            label="Close"
            onClick={handleProcess}
            className="w-full sm:w-28"
            variant="neutral"
          />
        </div>
      </div>
      <AppConfirmationModal
        title={'Confirmation'}
        description={<div>Are you sure you want to remove ? </div>}
        open={openDelete}
        onOpenChange={toggleDelete}
        handleCancel={toggleDelete}
        handleSubmit={handleDelete}
        isLoading={deleteLoading}
      />
      <AppSpinner overlay isLoading={isLoadingPaymentLink} />
    </div>
  );
};

export default ProcessWithCreditCard;
