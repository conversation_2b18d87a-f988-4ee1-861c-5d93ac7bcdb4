import AppButton from '@/components/common/app-button';
import DataTable from '@/components/common/data-tables';
import {
  convertToFloat,
  DEFAULT_FORMAT,
  formatDate,
  getQueryParam,
} from '@/lib/utils';
import { useGetPaymentDetailsQuery } from '@/redux/features/orders/order.api';
import { SortingStateType } from '@/types/common.types';
import { ExtendedPaymentInfoDTO } from '@/types/order.types';
import { ColumnDef } from '@tanstack/react-table';
import { useCallback, useMemo, useState } from 'react';

interface PaymentInfoTypes {
  state: boolean;
  action: string;
}

interface ExtendedPaymentInfoProps {
  setOpen: React.Dispatch<React.SetStateAction<PaymentInfoTypes>>;
}

const ExtendedPaymentInfo = ({ setOpen }: ExtendedPaymentInfoProps) => {
  const orderId = getQueryParam('id') as string;
  const [sorting, setSorting] = useState<SortingStateType[]>([
    { id: 'paymentDate', desc: true },
  ]);
  const { data, isFetching: isLoading } = useGetPaymentDetailsQuery(
    {
      body: {
        pageNumber: 0,
        pageSize: 0,
        sortBy: sorting[0]?.id,
        sortAscending: sorting[0]?.desc ?? true,
        filters: [{ field: 'orderId', value: orderId, operator: 'Equals' }],
      },
    },
    { skip: !orderId }
  );

  const columns = useMemo<ColumnDef<ExtendedPaymentInfoDTO>[]>(() => {
    return [
      {
        accessorKey: 'isDeleted',
        header: 'Status',
        enableSorting: true,
        cell: ({ row }: any) =>
          row?.original?.isDeleted === true ? 'Deleted' : '',
        size: 150,
      },
      {
        accessorKey: 'paymentDate',
        header: 'Date',
        enableSorting: true,
        cell: ({ row }: any) => {
          return formatDate(row?.original?.paymentDate, DEFAULT_FORMAT);
        },
        size: 100,
      },
      {
        accessorKey: 'cardType',
        header: 'Type',
        enableSorting: true,
        cell: ({ row }: any) => {
          return row?.original?.cardType == null
            ? row?.original?.paymentMethodName
            : row?.original?.cardType;
        },
        size: 150,
      },
      {
        accessorKey: 'amount',
        header: 'Amount',
        cell: ({ row }: any) =>
          convertToFloat({ value: row?.original?.amount, prefix: '$' }),
        enableSorting: true,
        size: 150,
      },
      {
        accessorKey: 'discount',
        header: 'Discount',
        cell: ({ row }: any) =>
          convertToFloat({ value: row?.original?.discount, prefix: '$' }),
        enableSorting: true,
        size: 120,
      },
      {
        accessorKey: 'reference',
        header: 'Reference',
        enableSorting: true,
        size: 150,
      },
      {
        accessorKey: 'bankAccount',
        header: 'Bank Account',
        enableSorting: true,
        size: 200,
      },
      {
        accessorKey: 'cardNumber',
        header: 'Credit Card/ACH #',
        enableSorting: true,
        size: 200,
        cell: ({ row }: any) => {
          const cardNumber = String(row?.original?.cardNumber ?? '');
          return (
            <span className="tracking-wide text-base font-sans">
              {cardNumber.includes('*')
                ? cardNumber.replace(/\*/g, '\u002A')
                : ''}
            </span>
          );
        },
      },
      {
        accessorKey: 'authCode',
        header: 'Authorization Code',
        enableSorting: true,
        size: 200,
      },
      {
        accessorKey: 'transactionId',
        header: 'Transaction ID',
        enableSorting: true,
        size: 200,
      },
      {
        accessorKey: 'user',
        header: 'User',
        enableSorting: true,
        size: 200,
      },
      {
        accessorKey: 'level2PoNo',
        header: 'Level 2 PO #',
        enableSorting: true,
        size: 200,
      },
      {
        accessorKey: 'level2Tax',
        header: 'Level 2 Tax',
        enableSorting: true,
        size: 200,
      },
      {
        accessorKey: 'level2Freight',
        header: 'Level 2 Freight',
        enableSorting: true,
        size: 200,
      },
      {
        accessorKey: 'convFee',
        header: 'Convenience Fee',
        cell: ({ row }: any) =>
          convertToFloat({ value: row?.original?.convFee, prefix: '$' }),
        enableSorting: true,
        size: 200,
      },
      {
        accessorKey: 'originalCharge',
        header: 'Original Charge',
        cell: ({ row }: any) =>
          convertToFloat({ value: row?.original?.originalCharge, prefix: '$' }),
        enableSorting: true,
        size: 200,
      },
    ];
  }, []);

  const handleCancel = useCallback(() => {
    setOpen({ state: false, action: '' });
  }, [setOpen]);

  return (
    <div className="space-y-6">
      <div>
        <DataTable
          data={data?.data || []}
          columns={columns}
          totalItems={data?.data?.length}
          heading="Saved Payments"
          enablePagination={false}
          loaderRows={11}
          isLoading={isLoading}
          tableClassName="max-h-[500px] 2xl:max-h-[550px] overflow-auto"
          manualSorting={false}
          sorting={sorting}
          setSorting={setSorting}
          noDataPlaceholder="No Data Found."
        />
      </div>
      <div className="flex justify-end gap-4 fixed bottom-0 right-6 bg-white pb-4">
        <AppButton
          label="Cancel"
          onClick={handleCancel}
          className="w-28"
          variant="neutral"
        />
      </div>
    </div>
  );
};

export default ExtendedPaymentInfo;
