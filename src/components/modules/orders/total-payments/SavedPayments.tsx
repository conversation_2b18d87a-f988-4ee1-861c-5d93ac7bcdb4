import AppButton from '@/components/common/app-button';
import DataTable from '@/components/common/data-tables';
import { useCallback, useMemo } from 'react';

interface SavedPaymentTypes {
  state: boolean;
  action: string;
}

interface SavedPaymentProps {
  setOpen: React.Dispatch<React.SetStateAction<SavedPaymentTypes>>;
}

const SavedPayments = ({ setOpen }: SavedPaymentProps) => {
  const columns = useMemo(() => {
    return [
      {
        accessorKey: 'paymentType',
        header: 'Payment Type',
      },
      {
        accessorKey: 'paymentAmount',
        header: 'Payment Amount',
        size: 200,
      },
      {
        accessorKey: 'paymentDiscount',
        header: 'Payment Discount',
        size: 200,
      },
      {
        accessorKey: 'paymentReference',
        header: 'Payment Reference',
        size: 250,
      },
      {
        accessorKey: 'bankAccount',
        header: 'Bank Account',
        size: 250,
      },
      {
        accessorKey: 'creditCardId',
        header: 'Credit Card #',
        size: 250,
      },
      {
        accessorKey: 'transactionId',
        header: 'Transaction ID',
      },
      {
        accessorKey: 'user',
        header: 'User',
      },
      {
        accessorKey: 'level2Data',
        header: 'Level 2 Data',
      },
    ];
  }, []);

  const handleCancel = useCallback(() => {
    setOpen({ state: false, action: '' });
  }, [setOpen]);

  return (
    <div className="space-y-6">
      {/* Table */}
      <div>
        <DataTable
          data={[]}
          columns={columns}
          heading="Saved Payments"
          enablePagination={false}
          tableClassName="max-h-[430px] overflow-y-auto"
        />
      </div>

      {/* Fixed Footer Buttons */}
      <div className="fixed bottom-0 left-0 right-0 bg-white px-5 py-4 shadow-md border-t border-gray-200">
        <div className="flex justify-between items-center gap-4 w-full">
          <AppButton
            label="Process Credit Card"
            onClick={handleCancel}
            className="w-full sm:w-44"
          />
          <AppButton
            label="Cancel"
            onClick={handleCancel}
            className="w-full sm:w-28"
            variant="neutral"
          />
        </div>
      </div>
    </div>
  );
};

export default SavedPayments;
