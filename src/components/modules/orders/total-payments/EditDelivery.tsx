import CheckIcon from '@/assets/icons/CheckIcon';
import AppButton from '@/components/common/app-button';
import DataTable from '@/components/common/data-tables';
import InputField from '@/components/forms/input-field';
import NumberInputField from '@/components/forms/number-input-field';
import { Checkbox } from '@/components/ui/checkbox';
import { UseToast } from '@/components/ui/toast/ToastContainer';
import { useHasPermission } from '@/hooks/useHasPermission';
import { cn, getQueryParam } from '@/lib/utils';
import {
  useGetDeliveryChargesQuery,
  useUpdateDeliveryChargesMutation,
} from '@/redux/features/customers/discount.api';
import {
  OrderInformationTypes,
  TotalPaymentsDeliveryCharge,
} from '@/types/order.types';
import { ColumnDef } from '@tanstack/react-table';
import { History } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';
import { useFormContext } from 'react-hook-form';

interface DeliveryInfo {
  open: boolean;
  onOpenChange: () => void;
  refetch: any;
  handleSetActivePopup: any;
}
const EditDelivery = ({
  onOpenChange,
  refetch,
  handleSetActivePopup,
}: DeliveryInfo) => {
  const formOrder = useFormContext<OrderInformationTypes>();
  const isDeleted = formOrder.watch('isDeleted');
  const isOrderEntryReadOnly = formOrder.watch('orderEntryReadOnly');

  const id = getQueryParam('id') as string;
  const form = useFormContext<TotalPaymentsDeliveryCharge>();
  const { hasPermission } = useHasPermission('Edit Order Delivery charge');
  const [selectedRows, setSelectedRows] = useState<any>('');
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [deliveryChargeList, setDeliveryChargeList] = useState([]);
  const { handleSubmit } = form;
  const {
    data: deliveryChargeListData,
    isFetching: deliveryChargeListIsLoading,
    // refetch,
  } = useGetDeliveryChargesQuery(id, {
    skip: !id,
    refetchOnMountOrArgChange: true,
  });

  // const {
  //   data: deliveryChargeHistroyListData,
  //   // isFetching: deliveryChargeHistroyListIsLoading,
  //   // refetch,
  // } = useGetDeliveryChargesHistoryQuery(id, {
  //   skip: !id,
  //   refetchOnMountOrArgChange: true,
  // });
  const [updateDeliveryCharge, { isLoading }] =
    useUpdateDeliveryChargesMutation();

  const columns: ColumnDef<TotalPaymentsDeliveryCharge>[] = useMemo(
    () => [
      {
        accessorKey: 'chargeOption',
        header: 'Delivery Charge Option',
      },
      { accessorKey: 'chargeBasedOn', header: 'Charge Based On' },
      { accessorKey: 'standardRates', header: 'Standard Rate' },
      { accessorKey: 'transitTime', header: 'Transit Time' },
      {
        accessorKey: 'forShip',
        header: 'Ship',
        cell: ({ row }: { row: any }) => {
          const chargeType = row?.original?.chargeType;
          // const forShip = row?.original?.forShip;

          return chargeType === 'UPS' ? (
            <Checkbox
              checked={row.getIsSelected()}
              onCheckedChange={(value) => row.toggleSelected(!!value)}
              className={cn(
                row.getIsSelected()
                  ? 'data-[state=checked]:bg-grayScale-600 data-[state=checked]:text-primary-foreground'
                  : 'border-grayScale-400 border-2',
                'w-5 h-5'
              )}
            >
              <CheckIcon />
            </Checkbox>
          ) : (
            <Checkbox
              checked={row.getIsSelected()}
              onCheckedChange={(value) => row.toggleSelected(!!value)}
              className={cn(
                row.getIsSelected()
                  ? 'data-[state=checked]:bg-grayScale-600 data-[state=checked]:text-primary-foreground'
                  : 'border-grayScale-400 border-2',
                'w-5 h-5'
              )}
            >
              <CheckIcon />
            </Checkbox>
          );
        },
      },
      {
        accessorKey: 'returnCharge',
        header: 'Return',
        cell: ({ row }: { row: any }) => (
          <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={(value) => row.toggleSelected(!!value)}
            className={cn(
              row.getIsSelected()
                ? 'data-[state=checked]:bg-grayScale-600 data-[state=checked]:text-primary-foreground'
                : 'border-grayScale-400 border-2',
              'w-5 h-5'
            )}
          >
            <CheckIcon />
          </Checkbox>
        ),
      },
    ],
    []
  );

  useEffect(() => {
    if (deliveryChargeListData?.data?.deliveryOptions) {
      setDeliveryChargeList(
        deliveryChargeListData?.data?.deliveryOptions || []
      );
      const findSelectedIndex =
        deliveryChargeListData?.data?.deliveryOptions?.findIndex(
          (element: { forReturn: boolean }) => element.forReturn === true
        );
      setSelectedRows({ [findSelectedIndex]: true });
    }

    return () => {
      setDeliveryChargeList([]);
    };
  }, [deliveryChargeListData?.data?.deliveryOptions]);

  useEffect(() => {
    const selectedIndex: any = Object.keys(selectedRows);

    const selectedItem: any = deliveryChargeList[selectedIndex];
    const defaultValues = {
      standardRates: '',
      shipCharge: '',
      returnCharge: '',
      shipDeliveryChargeOption: '',
      returnDeliveryChargeOption: '',
      returnChargeBasedOn: '',
      shipChargeBasedOn: '',
      shipTransitTime: '',
      returnTransitTime: '',
    };
    const valuesToSet = selectedItem
      ? {
          standardRates: selectedItem
            ? +selectedItem.standardRates
            : String(deliveryChargeListData?.data?.totalDeliveryCharge),
          shipCharge: selectedItem
            ? +selectedItem.shipCharge
            : String(deliveryChargeListData?.data?.defaultShipCharge),
          returnCharge: selectedItem
            ? +selectedItem.returnCharge
            : String(deliveryChargeListData?.data?.defaultReturnCharge),
          shipDeliveryChargeOption: selectedItem.chargeOption,
          returnDeliveryChargeOption: selectedItem.chargeOption,
          returnChargeBasedOn: selectedItem.chargeBasedOn,
          shipChargeBasedOn: selectedItem.chargeBasedOn,
          shipTransitTime: selectedItem.transitTime,
          returnTransitTime: selectedItem.transitTime,
        }
      : defaultValues;

    Object.entries(valuesToSet).forEach(([key, value]: any) => {
      form.setValue(key, value);
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedRows]);

  const onSubmit = async (formData: TotalPaymentsDeliveryCharge) => {
    const selectedIndex: any = Object.keys(selectedRows);
    const selectedItem: any = deliveryChargeList[selectedIndex];
    if (selectedItem) {
      const modified = deliveryChargeList.map((element: any) => {
        const isSelected = selectedItem.id === element.id;
        return isSelected
          ? {
              ...element,
              forReturn: true,
              forShip: true,
              returnCharge: +formData?.returnCharge,
              shipCharge: +formData?.shipCharge,
              deliveryCharge: +formData?.returnCharge + +formData?.shipCharge,
              standardRates: +formData?.returnCharge + +formData?.shipCharge,
            }
          : { ...element, forReturn: false, forShip: false };
      });
      let body = {
        orderId: id,
        deliveryOptions: modified,
      };
      await updateDeliveryCharge(body)
        .unwrap()
        .then(async (response) => {
          UseToast().success(response?.message);
          onOpenChange();
          await refetch();
        });
    } else {
      let body = {
        orderId: id,
        deliveryOptions: [],
        defaultShipCharge: formData.shipCharge || 0,
        defaultReturnCharge: formData.returnCharge || 0,
      };
      await updateDeliveryCharge(body)
        .unwrap()
        .then(async (response) => {
          UseToast().success(response?.message);
          onOpenChange();
          await refetch();
        });
    }
  };

  const handleReturnCharge = (event: any) => {
    const returnCharge = event?.target?.value || '';
    const returnChargeValue =
      parseFloat(returnCharge.replace(/[^0-9.-]+/g, '')) || 0;
    const shipCharge = parseFloat(form.getValues('shipCharge'));
    const totalStandardRate = returnChargeValue + shipCharge;
    form.setValue('standardRates', totalStandardRate.toFixed(2));
  };

  const handleShipCharge = (event: any) => {
    const shipCharge = event?.target?.value || '';
    const shipChargeValue =
      parseFloat(shipCharge.replace(/[^0-9.-]+/g, '')) || 0;
    const returnCharge = parseFloat(form.getValues('returnCharge'));
    const totalStandardRate = shipChargeValue + returnCharge;
    form.setValue('standardRates', totalStandardRate.toFixed(2));
  };

  const handleStandardRates = (event: any) => {
    const standardRates = event?.target?.value || '';
    const standardRatesValue =
      parseFloat(standardRates.replace(/[^0-9.-]+/g, '')) || 0;
    const splitAmount: any = standardRatesValue / 2;
    form.setValue('returnCharge', splitAmount);
    form.setValue('shipCharge', splitAmount);
  };

  return (
    <div className="pl-6 pr-6">
      <div className="p-6 gap-3 border border-border-Default rounded-lg w-full mb-5">
        <div className="grid grid-cols-1 md:grid-cols-7 gap-3 mb-5">
          <div className="col-span-1 md:col-span-4">
            <NumberInputField
              name="standardRates"
              form={form}
              label="Delivery Charge"
              placeholder="$______.__"
              prefix="$"
              disabled={!hasPermission}
              onBlur={handleStandardRates}
            />
          </div>
          <div className="col-span-1 md:col-span-3 flex items-end">
            <AppButton
              label="Delivery Charge History"
              icon={History}
              iconClassName="w-5"
              spinnerClass="border-white-500 border-t-transparent animate-spin"
              onClick={() =>
                handleSetActivePopup('edit-delivery-charge-history')
              }
              className="bg-white border border-border-Default hover:bg-white text-text-Default w-full"
            />
          </div>
        </div>

        <div className="flex flex-row gap-3 justify-between">
          <div className="flex-1 flex flex-col gap-3">
            <h3 className="text-xl font-semibold text-text-Default">Ship</h3>
            <div className="col-span-2">
              {' '}
              <InputField
                name="shipDeliveryChargeOption"
                form={form}
                label="Delivery Charge Option"
                placeholder="Enter Delivery Charge Option"
                disabled
              />{' '}
            </div>
            <div className="col-span-2">
              {' '}
              <InputField
                name="shipChargeBasedOn"
                form={form}
                label="Charge Based On"
                placeholder="Enter Charge Based On"
                disabled
              />{' '}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
              <div className="col-span-2">
                <NumberInputField
                  name="shipCharge"
                  form={form}
                  label="Delivery Charges"
                  placeholder="$______.__"
                  prefix="$"
                  disabled={!hasPermission}
                  onBlur={handleShipCharge}
                />
              </div>
              <div className="col-span-2">
                <InputField
                  name="shipTransitTime"
                  form={form}
                  label="Transit Time"
                  placeholder="--:--"
                  disabled
                />
              </div>
            </div>
          </div>
          <div className="col-span-2 w-[1px] border rounded-md bg-gray-100 text-gray-600"></div>
          <div className="flex-1 flex flex-col gap-3">
            <h3 className="text-xl font-semibold text-text-Default">Return</h3>
            <InputField
              name="returnDeliveryChargeOption"
              form={form}
              label="Delivery Charge Option"
              placeholder="Enter Delivery Charge Option"
              disabled
            />
            <InputField
              name="returnChargeBasedOn"
              form={form}
              label="Charge Based On"
              placeholder="Enter Charge Based On"
              disabled
            />
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="col-span-2">
                <NumberInputField
                  name="returnCharge"
                  form={form}
                  label="Delivery Charges"
                  placeholder="$______.__"
                  prefix="$"
                  onBlur={handleReturnCharge}
                  disabled={!hasPermission}
                />
              </div>
              <div className="col-span-2">
                <InputField
                  name="returnTransitTime"
                  form={form}
                  label="Transit Time"
                  placeholder="--:--"
                  disabled
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="flex flex-col gap-6">
        <DataTable
          data={deliveryChargeList || []}
          tableClassName="max-h-[400px] overflow-auto"
          columns={columns}
          enableSearch={false}
          enableColumnVisibility={false}
          enableRowSelection={false}
          enablePagination={false}
          enableFilter={false}
          isFilterOpen={isFilterOpen}
          rowSelection={selectedRows}
          onRowSelectionChange={setSelectedRows}
          setIsFilterOpen={setIsFilterOpen}
          enableMultiRowSelection={false}
          isLoading={deliveryChargeListIsLoading}
        />
      </div>
      <div className="sticky bottom-0 py-4 flex justify-end gap-4 bg-white">
        <AppButton
          type="button"
          label={'Submit'}
          onClick={handleSubmit(onSubmit)}
          className="min-w-28"
          isLoading={isLoading}
          disabled={
            isDeleted || isOrderEntryReadOnly || deliveryChargeListIsLoading
          }
        />
        <AppButton
          type="button"
          label="Cancel"
          variant="neutral"
          className="w-28"
          onClick={onOpenChange}
        />
      </div>
    </div>
  );
};

export default EditDelivery;
