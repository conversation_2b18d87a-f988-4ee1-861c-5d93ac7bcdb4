import CheckVerified from '@/assets/icons/CheckVerified';
import AppButton from '@/components/common/app-button';
import { AppTableContext } from '@/components/common/app-data-table/AppTableContext';
import DataTable from '@/components/common/data-tables';
import CustomDialog from '@/components/common/dialog';
import Labels from '@/components/forms/Label';
import NumberInputField from '@/components/forms/number-input-field';
import RadioField from '@/components/forms/radio-field';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { UseToast } from '@/components/ui/toast/ToastContainer';
import { QuickDiscountsMethods } from '@/constants/order-constants';
import { cn, getQueryParam, searchPayload } from '@/lib/utils';
import { useGetDepartmentsQuery } from '@/redux/features/customers/customer.api';
import {
  useGetOrderDiscountsMutation,
  useUpdateOrderDiscountMutation,
} from '@/redux/features/customers/discount.api';
import { DiscountsTypes } from '@/types/customer.types';
import {
  OrderDiscountsTypes,
  OrderInformationTypes,
  TotalPaymentsTypes,
} from '@/types/order.types';
import { ColumnDef, RowSelectionState } from '@tanstack/react-table';
import { useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { useFormContext, UseFormReturn } from 'react-hook-form';
import { NumericFormat } from 'react-number-format';

import ApplyDiscountSection from './ApplyDiscountSection';
import DiscountCell from './DiscountCell';

interface DeliveryInfo {
  open: boolean;
  onOpenChange: () => void;
  form: UseFormReturn<TotalPaymentsTypes>;
}
const EditDiscount = ({ open, onOpenChange, form }: DeliveryInfo) => {
  // const form = useFormContext<TotalPaymentsDeliveryCharge>();
  const formOrder = useFormContext<OrderInformationTypes>();
  const isDeleted = formOrder.watch('isDeleted');
  const isOrderEntryReadOnly = formOrder.watch('orderEntryReadOnly');

  const id = getQueryParam('id') as string;
  const [selectedRows, setSelectedRows] = useState<RowSelectionState>({});
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  // console.log(form.getValues());
  // Extract customer ID from URL params

  const { data: departmentData, isFetching } = useGetDepartmentsQuery();
  const [updatedDiscounts, setUpdatedDiscounts] = useState<{
    [key: string]: string;
  }>({});
  // State hooks for managing component state
  const [selectedRowsData, setSelectedRowsData] = useState<
    OrderDiscountsTypes[]
  >([]);
  const [discountData, setDiscountData] = useState<OrderDiscountsTypes[]>([]);
  const [bulkDiscount, setBulkDiscount] = useState('');
  const [search, setSearch] = useState('');
  const { pagination, setPagination } = useContext(AppTableContext);
  const [isCustomDialogOpen, setIsCustomDialogOpen] = useState<boolean>(false);
  const isDiscount = Object.values(updatedDiscounts)?.filter((value) => value);
  // API hooks for fetching and updating discount data
  const [getDiscount, { isLoading, data }] = useGetOrderDiscountsMutation();

  const [updateDiscount, { isLoading: isUpdating }] =
    useUpdateOrderDiscountMutation();

  const refetchDiscounts = useCallback(async () => {
    const payload = searchPayload({
      pageNumber: pagination.pageIndex + 1,
      pageSize: pagination.pageSize,
      sortBy: '',
      sortAscending: true,
      filters: [{ field: '', value: '', operator: '' }],
    });
    // Refetch the discount data
    const response = await getDiscount({
      id: id ?? '',
      body: payload,
    }).unwrap();
    setDiscountData(
      response?.data.map((item) => ({
        ...item,
        disabledCheckBox: isDeleted || isOrderEntryReadOnly,
      }))
    );
  }, [
    getDiscount,
    id,
    isDeleted,
    isOrderEntryReadOnly,
    pagination.pageIndex,
    pagination.pageSize,
  ]);
  const discount: any = form.watch('discount');

  // Memoized table columns for performance
  const columnsDiscount: ColumnDef<DiscountsTypes>[] = useMemo(
    () => [
      { accessorKey: 'catno', header: 'Category #' },
      { accessorKey: 'deptdesc', header: 'Department' },
      { accessorKey: 'catdesc', header: 'Category' },
      {
        accessorKey: 'discount',
        header: 'Discount',
        cell: ({ row }: any) => (
          <DiscountCell
            row={row}
            form={form}
            setDiscountData={setDiscountData}
            discountData={discountData}
            refetchDiscounts={refetchDiscounts}
          />
        ),
        maxSize: 100,
        size: 100,
      },
    ],
    [discountData, form, refetchDiscounts]
  );

  // Function to handle changes in the discount for each department
  const handleDepartmentDiscountChange = useCallback(
    (departmentId: string, newDiscount: string) => {
      setUpdatedDiscounts((prevState) => ({
        ...prevState,
        [departmentId]: newDiscount, // Store the updated discount for this department
      }));
    },
    []
  );

  const DepartmentDiscountCell = ({
    row,
    onDiscountChange,
  }: {
    row: any;
    onDiscountChange: (departmentId: string, discount: string) => void;
  }) => {
    const [discount, setDiscount] = useState({
      departmentId: row.original?.id,
      custdiscount: '',
    });

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const newDiscount = e.target.value;
      setDiscount({
        ...discount,
        custdiscount: newDiscount,
      });

      // Call the prop function to update parent state
      onDiscountChange(row.original?.id, newDiscount);
    };

    return (
      <NumericFormat
        value={discount.custdiscount}
        placeholder="__.__%"
        decimalScale={2}
        fixedDecimalScale
        isAllowed={({ formattedValue }) => formattedValue.length <= 6}
        suffix={'%'}
        className="flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground  focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
        onChange={handleInputChange}
      />
    );
  };

  const discountByDepartmentColumns = useMemo(
    () => [
      { accessorKey: 'dept', header: 'Department' },
      { accessorKey: 'deptDesc', header: 'Description' },
      {
        accessorKey: 'catdiscount',
        header: 'Discount',
        cell: ({ row }: any) => (
          <DepartmentDiscountCell
            row={row}
            onDiscountChange={handleDepartmentDiscountChange}
          />
        ),
        maxSize: 100,
        size: 100,
      },
    ],
    [handleDepartmentDiscountChange]
  );

  // Fetch discount data based on filter, search, and pagination
  useEffect(() => {
    // Define an async function inside the useEffect hook
    // const fetchDiscountData = async () => {
    //   try {
    //     // Construct the payload for the API request
    //     const payload = searchPayload({
    //       pageNumber: pagination.pageIndex + 1,
    //       pageSize: pagination.pageSize,
    //       sortBy: '',
    //       sortAscending: true,
    //       filters: [{ field: '', value: '', operator: '' }],
    //     });

    //     // Fetch discount data for the specific customer
    //     const response = await getDiscount({
    //       id: id ?? '',
    //       body: payload,
    //     }).unwrap();

    //     // Set the discount data in state
    //     setDiscountData(response?.data);
    //   } catch (error) {
    //     // Handle any errors
    //   }
    // };
    if (discount) {
      form.setValue('discountAmount', discount);
      const total = parseFloat(form.watch('amountSubjectToDiscount'));
      const discountPercentage: any = (parseFloat(discount) / total) * 100;
      setBulkDiscount(discountPercentage);
      form.setValue('discountPercentage', discountPercentage);
    }

    // Call the async function inside useEffect
    // fetchDiscountData();
    refetchDiscounts();

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [search, pagination]); // Ensure this effect runs when 'search' or 'pagination' change

  /**
   * Handles applying the bulk discount to the selected rows or all categories.
   */
  const handleApplyDiscount = async () => {
    if (!bulkDiscount) return;

    try {
      // If specific rows are selected, update discounts for those
      if (selectedRowsData.length > 0) {
        const discountData = selectedRowsData.map((row) => ({
          id: Number(row.id),
          departmentId: row.departmentId,
          categoryId: row.categoryId,
          discount: Number(bulkDiscount),
        }));

        const body = {
          orderId: Number(id),
          discounts: discountData,
        };

        await updateDiscount(body)
          .unwrap()
          .then((response) => {
            const paymentData = response?.data;
            form.setValue('discount', paymentData?.discount);
            form.setValue('grandTotal', paymentData?.grandTotal);
            form.setValue('discountAmount', paymentData?.discount);
            const discountPercentage: any =
              (parseFloat(paymentData?.discount) /
                paymentData?.amountSubjectToDiscount) *
              100;
            form.setValue('discountPercentage', discountPercentage.toString());
            refetchDiscounts();
            // setBulkDiscount('');
            showSuccessMessage(response?.message);
          });
      } else {
        // If no specific rows selected, apply discount in bulk

        const body = {
          orderId: Number(id),
          discounts: [
            {
              discount: Number(bulkDiscount),
            },
          ],
        };

        await updateDiscount(body)
          .unwrap()
          .then((response) => {
            const paymentData = response?.data;
            form.setValue('discount', paymentData?.discount);
            form.setValue('discountAmount', paymentData?.discount);
            form.setValue('grandTotal', paymentData?.grandTotal);
            const discountPercentage: any =
              (parseFloat(paymentData?.discount) /
                paymentData?.amountSubjectToDiscount) *
              100;
            form.setValue('discountPercentage', discountPercentage.toString());
            // setBulkDiscount('');
            refetchDiscounts();
            showSuccessMessage(response?.message);
          });
      }

      // Fetch updated discount data after applying the discount
    } catch (error) {
      // Handle error (could be improved with specific error messages)
    }
  };

  /**
   * Shows a success toast message.
   */
  const showSuccessMessage = (message: string) => {
    UseToast().success(message);
  };

  // Toggle Custom Dialog
  const toggleCustomDialog = useCallback(() => {
    setIsCustomDialogOpen((prevState) => !prevState);
  }, []);

  const handleApplyDiscountByDept = async () => {
    // Collect updated discount data from the rows
    const discountPayload = departmentData?.data
      ?.map((row: any) => {
        // Ensure updatedDiscounts[row.department_id] is defined before using 'replace'
        let updatedValue = updatedDiscounts[row?.id];

        if (!updatedValue) {
          return null; // Skip if no discount is provided for this department
        }

        updatedValue = updatedValue.replace(/%/g, '').replace(/_/g, '0');

        // If the value is in the format '__.__%', set it to empty
        if (updatedValue === '__.__') {
          updatedValue = ''; // Clear the field
        }

        // If the updatedValue is empty or invalid, skip this department
        if (!updatedValue || updatedValue === '') {
          return null;
        }

        return {
          departmentId: row?.id,
          discount: Number(updatedValue || 0),
        };
      })
      .filter((item: any) => item !== null); // Filter out invalid (null) values

    const body = {
      orderId: Number(id),
      discounts: discountPayload,
    };
    await updateDiscount(body)
      .unwrap()
      .then((response) => {
        const paymentData = response?.data;
        form.setValue('discount', paymentData?.discount);
        form.setValue('grandTotal', paymentData?.grandTotal);
        form.setValue('discountAmount', paymentData?.discount);

        const discountPercentage: any =
          (parseFloat(paymentData?.discount) /
            paymentData?.amountSubjectToDiscount) *
          100;
        form.setValue('discountPercentage', discountPercentage.toString());
        showSuccessMessage(response?.data?.message ?? '');
        toggleCustomDialog();
        refetchDiscounts();
      });
  };

  const CustomToolbar = (
    <AppButton
      label="Discount By Department"
      onClick={() => toggleCustomDialog()}
      icon={CheckVerified}
      disabled={isDeleted || isOrderEntryReadOnly}
    />
  );

  useEffect(() => {
    const selectedData =
      data?.data.filter((_row, index) => selectedRows[index]) || [];
    setSelectedRowsData(selectedData);
  }, [data?.data, selectedRows]);

  const handleApplyInstantDiscount = (event: any) => {
    const value = event?.target?.value || '';
    const newValue: any = parseFloat(value.replace(/[^0-9.-]+/g, '')) || 0;
    const total = parseFloat(form.watch('amountSubjectToDiscount'));
    const discountPercentage: any = (parseFloat(newValue) / total) * 100;
    form.setValue('discountPercentage', discountPercentage.toString());
    // const formattedDiscount = `${discountPercentage.toFixed(2)}%`;
    setBulkDiscount(discountPercentage);
  };

  const handleQuickDiscount = (value: string) => {
    form.setValue('discountPercentage', value); // set the % value in form
    const total = parseFloat(form.watch('amountSubjectToDiscount')) || 0;
    const discountAmount = (parseFloat(value) / 100) * total;
    form.setValue('discountAmount', discountAmount); // optional: store discount amount
    setBulkDiscount(value);
  };

  // const handleApplyPercentageDiscounts = (event: any) => {
  //   const value = event?.target?.value || '';
  //   const newValue: any = parseFloat(value.replace(/[^0-9.-]+/g, '')) || 0;
  //   const total = parseFloat(form.watch('total')) || 0;
  //   const discountAmount = (parseFloat(newValue) / 100) * total;
  //   form.setValue('discountAmount', discountAmount); // optional: store discount amount
  //   setBulkDiscount(value);
  // };

  return (
    <CustomDialog
      onOpenChange={onOpenChange}
      description=""
      open={open}
      className={cn('max-w-[80%] md:w-[70%] h-[90%] overflow-auto')}
      title={'Discount'}
    >
      <div className="pl-6 pr-6">
        <div className="p-6 gap-6 border border-border-Default rounded-lg w-full mb-5">
          <div className="flex flex-row gap-6 justify-between">
            <div className="flex-1 flex flex-col gap-6">
              <NumberInputField
                name="amountSubjectToDiscount"
                form={form}
                prefix="$"
                fixedDecimalScale
                decimalScale={2}
                label="Amount Subject to Discount"
                placeholder="Enter Amount"
                disabled
              />
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="col-span-2">
                  <NumberInputField
                    name="discountAmount"
                    form={form}
                    label="Discount Amount"
                    maxValue={parseFloat(form.watch('amountSubjectToDiscount'))}
                    placeholder="$______.__"
                    onBlur={handleApplyInstantDiscount}
                    prefix="$"
                    fixedDecimalScale
                    decimalScale={2}
                    disabled={isDeleted || isOrderEntryReadOnly}
                  />
                </div>
                <div className="col-span-2">
                  <NumberInputField
                    name="discountPercentage"
                    form={form}
                    label="Discount Percentage"
                    placeholder="______.__%"
                    disabled
                    // onBlur={handleApplyPercentageDiscounts}
                    suffix="%"
                  />
                </div>
              </div>
            </div>
            <div className="flex-1 flex flex-col gap-6">
              <Labels label="Quick Discounts" />
              <RadioField
                form={form}
                name="discountPercentage"
                options={QuickDiscountsMethods}
                optionsPerRow={4}
                onChange={handleQuickDiscount}
                disabled={isDeleted || isOrderEntryReadOnly}
              />{' '}
              <div className="col-span-4">
                <AppButton
                  label={'Apply Discount'}
                  onClick={handleApplyDiscount}
                  icon={CheckVerified}
                  isLoading={isUpdating}
                  // disabled={!bulkDiscount}
                  disabled={isDeleted || isOrderEntryReadOnly}
                />
              </div>
            </div>
          </div>
        </div>
        <div className="flex flex-col gap-6">
          {/* Data Table */}
          <Accordion type="single" className="flex flex-col space-y-4">
            <Accordion
              className="AccordionRoot"
              type="single"
              defaultValue="item-1"
              collapsible
            >
              <AccordionItem value="item-1" className="border-0">
                <AccordionTrigger className="bg-grayScale-10 rounded-md px-3 hover:no-underline">
                  Discount by Category / Department
                </AccordionTrigger>
                <AccordionContent className="grid grid-cols-1 gap-6 py-4 px-1">
                  <DataTable
                    search={search}
                    setSearch={setSearch}
                    data={discountData ?? []}
                    columns={columnsDiscount}
                    enableSearch={false}
                    heading={
                      <h1 className="text-2xl font-semibold text-[#181A1D]">
                        Department & Categories
                      </h1>
                    }
                    isLoading={isLoading}
                    totalItems={data?.pagination?.totalCount}
                    pagination={pagination}
                    setPagination={setPagination}
                    enableColumnVisibility={false}
                    enableRowSelection
                    customToolBar={CustomToolbar}
                    enableFilter={false}
                    rowSelection={selectedRows}
                    onRowSelectionChange={setSelectedRows}
                    setIsFilterOpen={setIsFilterOpen}
                    enableMultiRowSelection
                    disableSelectAllCheckbox={isDeleted || isOrderEntryReadOnly}
                  />
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </Accordion>

          {/* Apply Discount Section */}
          <ApplyDiscountSection
            bulkDiscount={bulkDiscount}
            setBulkDiscount={setBulkDiscount}
            onApplyDiscount={handleApplyDiscount}
            disabled={
              isDeleted ||
              isOrderEntryReadOnly ||
              isUpdating ||
              data?.data?.length === 0
            }
            isLoading={isUpdating}
            label={
              selectedRowsData.length > 0 ? 'Apply Discount' : 'Apply Flat Rate'
            }
          />

          <CustomDialog
            onOpenChange={() => {
              setUpdatedDiscounts({});
              toggleCustomDialog();
            }}
            description=""
            open={isCustomDialogOpen}
            className="lg:min-w-[60%]"
            title={'Discount By Department'}
          >
            <div className="pl-6 pr-6 p-2">
              <DataTable
                tableClassName="max-h-[400px] overflow-auto"
                data={departmentData?.data ?? []}
                columns={discountByDepartmentColumns}
                enableSearch={false}
                isLoading={isFetching}
                enableColumnVisibility={false}
                enableRowSelection={false}
                isFilterOpen={isFilterOpen}
                enableFilter={false}
                enablePagination={false}
                loaderRows={5}
                totalItems={departmentData?.pagination?.totalCount}
                setIsFilterOpen={setIsFilterOpen}
              />
              <div className="mt-4  flex flex-row justify-end gap-4">
                <div className="w-[50%] flex flex-row justify-end gap-4">
                  <AppButton
                    label="Apply Discount"
                    className="w-full"
                    onClick={handleApplyDiscountByDept}
                    icon={CheckVerified}
                    isLoading={isUpdating}
                    disabled={!isDiscount?.length}
                  />
                  <AppButton
                    className="w-full"
                    label="Cancel"
                    variant="neutral"
                    onClick={() => {
                      toggleCustomDialog();
                      setUpdatedDiscounts({});
                    }}
                  />
                </div>
              </div>
            </div>
          </CustomDialog>
        </div>
      </div>
    </CustomDialog>
  );
};

export default EditDiscount;
