import AppButton from '@/components/common/app-button';
import AppCheckbox from '@/components/common/app-checkbox/AppCheckbox';
import AppSpinner from '@/components/common/app-spinner';
import DatePicker from '@/components/forms/date-picker';
import Input<PERSON>ield from '@/components/forms/input-field';
import NumberInputField from '@/components/forms/number-input-field';
import SelectDropDown from '@/components/forms/select-dropdown';
import { PAYMENT_TYPE_API_ROUTES } from '@/constants/api-constants';
import {
  DATE_FORMAT_YYYYMMDD,
  formatDate,
  generateLabelValuePairs,
  getQueryParam,
} from '@/lib/utils';
import { useGetAllMutation } from '@/redux/features/common-api/common.api';
import { useGetStateByCountryQuery } from '@/redux/features/country/country.api';
import { useGetRefundAmountMutation } from '@/redux/features/orders/order.api';
import {
  NewPaymentDTO,
  OrderInformationTypes,
  ProcessWithRefundDTO,
} from '@/types/order.types';
import { Label } from '@radix-ui/react-label';
import { useEffect, useMemo } from 'react';
import { SubmitHandler, useForm, useFormContext } from 'react-hook-form';

interface ProcessWithRefundTypes {
  state: boolean;
  action: string;
}

interface ProcessWithRefundProps {
  setOpen: React.Dispatch<React.SetStateAction<ProcessWithRefundTypes>>;
  data: NewPaymentDTO;
  toggleToNewPayment: () => void;
}

const ProcessWithRefund = ({ setOpen, data }: ProcessWithRefundProps) => {
  const id = getQueryParam('id') as string;
  const form = useForm<ProcessWithRefundDTO>();

  const getChargeAmount = data?.charge;
  const getBankAccountId = data?.bankAccountId;
  const getReference = data?.reference;
  const getPaymentId = data?.paymentId;
  const getRefundConvFee = data?.refundConvFee;

  //get customer data from context
  const orderInfoForm = useFormContext<OrderInformationTypes>();

  const customerData = orderInfoForm?.watch('billTo');

  const defaultValues = useMemo(() => {
    return {
      date: data?.date || '', // Use data.date if available
      type: data?.type || '', // Use data.type if available
      amount: data?.amount || 0, // Use data.amount if available
      reference: data?.reference || '', // Use data.reference if available
      name: customerData?.name || '',
      email: customerData?.orderEmail || '',
      address: customerData?.address1 || '',
      city: customerData?.city || '',
      state: +(customerData?.stateId ?? 0) || 0,
      zipcode: customerData?.zipCode || '',
      bankAccountId: data?.bankAccountId || null,
      cardExpiry: '',
      cardNumber: data?.cardNumber ?? '',
      paymentId: data?.paymentId ?? null,
    };
  }, [customerData, data]);

  const handleProcess = () => {
    setOpen({ state: false, action: '' });
  };

  // get payment Refund API
  const [getRefundAmount, { isLoading: isLoadingRefund }] =
    useGetRefundAmountMutation();

  const [
    fetchAllPaymentTypes,
    { data: getAllPaymentTypes, isLoading: isLoadingPaymentTypes },
  ] = useGetAllMutation();

  const getAllPaymentTypesList = generateLabelValuePairs({
    data: getAllPaymentTypes?.data,
    labelKey: 'paymentMethod',
    valueKey: 'id',
  });

  useEffect(() => {
    if (defaultValues) {
      form.reset(defaultValues);
    }
  }, [defaultValues, form]);

  // state data
  const { data: statesData, isFetching: stateIsLoading } =
    useGetStateByCountryQuery({
      countryId: 1,
    });
  // states list
  const stateList = generateLabelValuePairs({
    data: statesData,
    labelKey: 'code',
    valueKey: 'state_id',
  });
  useEffect(() => {
    const fetchData = async () => {
      const payload = {
        pageNumber: 0,
        pageSize: 0,
        sortBy: '',
        sortAscending: false,
        filters: [],
      };
      await fetchAllPaymentTypes({
        url: PAYMENT_TYPE_API_ROUTES.ALL,
        body: payload,
      });
    };
    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const onProcessSubmit: SubmitHandler<ProcessWithRefundDTO> = async (
    formData
  ) => {
    const formFields = {
      orderId: +id,
      date: formatDate(formData.date, DATE_FORMAT_YYYYMMDD),
      paymentTypeId: formData.type,
      amount: Math.abs(parseFloat(getChargeAmount as string) || 0),
      billingName: formData.name,
      billingAddress: formData.address,
      billingCity: formData.city,
      billingZipCode: formData.zipcode,
      email: formData.email,
      updateCustomer: formData?.updateCustomer,
      stateId: formData?.state,
      bankAccountId: getBankAccountId ?? null,
      refundConvFee: getRefundConvFee ?? false,
      paymentId: getPaymentId ?? null,
      reference: getReference ?? '',
    };

    try {
      const responseRefund = await getRefundAmount(formFields).unwrap();
      if (responseRefund) {
        handleProcess();
      }
    } catch (error) {}
  };

  return (
    <div className="space-y-5 pb-24">
      {/* Date, Type, Amount */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <DatePicker name="date" form={form} enableInput label="Date" disabled />
        <SelectDropDown
          form={form}
          optionsList={getAllPaymentTypesList ?? []}
          name="type"
          label="Type"
          placeholder="Select Type"
          disabled
        />
        <NumberInputField
          form={form}
          name="amount"
          label="Amount"
          placeholder="Enter Amount"
          prefix="$"
          allowNegative
          fixedDecimalScale
          disabled
        />
        <InputField
          form={form}
          name="cardNumber"
          label="Original Account"
          placeholder="Enter Account"
          className="tracking-wide text-base font-sans"
          disabled
        />
      </div>

      {/* Cardholder Info */}
      <div className="flex items-center justify-start gap-2">
        <h3 className="text-lg font-semibold">Cardholder Info</h3>
        <span>( * Missing info may result in higher fees )</span>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <InputField
          name="name"
          label="Name"
          form={form}
          placeholder="Enter Name"
        />
        <InputField
          name="email"
          label="E-mail Receipt"
          form={form}
          placeholder="Enter Email"
        />
        <InputField
          name="address"
          label="Address"
          form={form}
          placeholder="Enter Address"
        />
        <InputField
          name="city"
          label="City"
          form={form}
          placeholder="Enter City"
        />
        <SelectDropDown
          name="state"
          form={form}
          placeholder="Select State"
          label="State"
          allowClear={true}
          optionsList={stateList}
          isLoading={stateIsLoading}
        />
        <InputField
          name="zipcode"
          label="Zip Code"
          form={form}
          placeholder="Enter Zip Code"
        />
      </div>

      {/* Update Customer Checkbox */}
      <div className="flex items-center gap-2">
        <AppCheckbox
          name="updateCustomer"
          control={form.control}
          className="data-[state=checked]:bg-[#5f26c9] text-white"
        />
        <Label
          htmlFor="updateCustomer"
          className="text-sm sm:text-base font-medium text-black"
        >
          Update Customer Record
          <span className="text-xs"> ( * Does not update customer name)</span>
        </Label>
      </div>

      {/* Footer Buttons */}
      <div className="fixed bottom-0 left-0 right-0 bg-white px-5 py-4 shadow-md border-t border-gray-200">
        <div className="flex justify-between items-center gap-4 w-full">
          <AppButton
            label="Process"
            onClick={form.handleSubmit(onProcessSubmit)}
            disabled={isLoadingPaymentTypes}
          />
          <AppButton
            label="Close"
            onClick={handleProcess}
            className="w-full sm:w-28"
            variant="neutral"
          />
        </div>
      </div>

      <AppSpinner overlay isLoading={isLoadingRefund} />
    </div>
  );
};

export default ProcessWithRefund;
