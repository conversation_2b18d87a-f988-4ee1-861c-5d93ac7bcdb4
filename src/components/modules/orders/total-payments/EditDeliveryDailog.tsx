import BreadcrumbDialogRenderer from '@/components/common/BreadcrumbDialogRenderer';
import { useCallback, useMemo, useState } from 'react';
import EditDelivery from './EditDelivery';
import { cn } from '@/lib/utils';
import EditDeliveryChargeHistory from './EditDeliveryChargeHistory';

const EditDeliveryDailog = ({ openDelivery, onOpenChange, refetch }: any) => {
  const [activePopup, setActivePopup] = useState('edit-delivery-charge');

  const handleSetActivePopup = useCallback((tab?: string) => {
    setActivePopup(tab ?? 'edit-delivery-charge-history');
  }, []);

  // tab list
  const dialogTabs = useMemo(
    () => [
      {
        label: 'Delivery Charge',
        value: 'edit-delivery-charge',
        content: (
          <EditDelivery
            open={false}
            onOpenChange={onOpenChange}
            refetch={refetch}
            handleSetActivePopup={handleSetActivePopup}
          />
        ),
      },
      {
        label: 'Delivery Charge History',
        value: 'edit-delivery-charge-history',
        content: (
          <EditDeliveryChargeHistory
            handleSetActivePopup={handleSetActivePopup}
          />
        ),
      },
    ],
    [handleSetActivePopup, onOpenChange, refetch]
  );

  return (
    <>
      <BreadcrumbDialogRenderer
        activeTab={activePopup}
        isOpen={openDelivery}
        listItems={dialogTabs ?? []}
        onOpenChange={onOpenChange}
        setActiveTab={handleSetActivePopup}
        className={cn('lg:max-w-[70%]  md:w-[90%]', 'max-w-[90%]')}
        // className="max-w-[95%] 2xl:max-w-[75%]"
        contentClassName="h-[480px] lg:h-[500px] xl:h-[600px] pb-0 overflow-y-auto"
      />
    </>
  );
};

export default EditDeliveryDailog;
