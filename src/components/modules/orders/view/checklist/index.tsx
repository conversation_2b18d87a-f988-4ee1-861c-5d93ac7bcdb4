import AppButton from '@/components/common/app-button';
import DataTable from '@/components/common/data-tables';
import CustomDialog from '@/components/common/dialog';
import InputField from '@/components/forms/input-field';
import {
  cn,
  DATE_TIME_FORMAT,
  formatDate,
  getPaginationObject,
  getQueryParam,
} from '@/lib/utils';
import {
  useGetCheckListMutation,
  useUpdateCheckListMutation,
} from '@/redux/features/orders/order.api';
import { SortingStateType } from '@/types/common.types';
import {
  CheckListItemsType,
  ChecklistType,
  CheckListTypes,
} from '@/types/order.types';
import { ColumnDef, RowSelectionState } from '@tanstack/react-table';
import { memo, useCallback, useEffect, useMemo, useState } from 'react';
import {
  SubmitHandler,
  useFieldArray,
  useForm,
  UseFormReturn,
} from 'react-hook-form';

interface ChecklistInfo {
  open: boolean;
  onOpenChange: () => void;
}

const Checklist = ({ open, onOpenChange }: ChecklistInfo) => {
  const orderId = getQueryParam('id') as string;

  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const [sorting, setSorting] = useState<SortingStateType[]>([
    { id: 'description', desc: true },
  ]);

  // check list
  const [getCheckList, { data: checkListItems, isLoading }] =
    useGetCheckListMutation();

  // update the selected check list items
  const [updateCheckLis, { isLoading: updateCheckListLoading }] =
    useUpdateCheckListMutation();

  const fetchAllitemList = useCallback(async () => {
    const payload = getPaginationObject({
      pagination: {
        pageSize: -1,
        pageIndex: 0,
      },
      sorting: sorting,
      filters: [],
    });
    const { data } = await getCheckList({
      body: payload,
      orderId,
    });
    if (data?.statusCode === 200) {
      const selectedCheckListItems = Object.fromEntries(
        data?.data
          ?.filter((item: CheckListItemsType) => item?.isChecked)
          .map((item: CheckListItemsType) => [item.id, true]) || []
      );
      setRowSelection(selectedCheckListItems || {});
    }
  }, [getCheckList, orderId, sorting]);

  useEffect(() => {
    fetchAllitemList();
  }, [fetchAllitemList]);

  const defaultValues = useMemo(() => {
    return {
      items:
        checkListItems?.data?.map((item: CheckListItemsType) => ({
          ...item,
          checkListId: item?.id,
          checkedAt: formatDate(item?.checkedAt, DATE_TIME_FORMAT),
        })) || [],
    };
  }, [checkListItems?.data]);

  const form: UseFormReturn<CheckListTypes> = useForm<CheckListTypes>({
    defaultValues,
    mode: 'onChange',
  });

  useEffect(() => {
    if (defaultValues) {
      form.reset(defaultValues);
    }
  }, [defaultValues, form]);

  const { fields } = useFieldArray<CheckListTypes>({
    control: form.control,
    name: 'items',
  });

  const SelectedRowId = useMemo(
    () => Object.keys(rowSelection)?.map((id) => Number(id)),
    [rowSelection]
  );

  // on submit
  const onSubmit: SubmitHandler<CheckListTypes> = useCallback(
    async (formData) => {
      try {
        const checkLists = formData?.items
          ?.filter((item) => SelectedRowId?.includes(item?.checkListId))
          ?.map((item) => ({
            id: item?.checkListId,
            notes: item?.notes,
          }));
        const { data } = await updateCheckLis({
          body: { orderId, checkLists },
        });
        if (data && data?.statusCode === 200) {
          fetchAllitemList();
        }
      } catch (error) {
        // Handle error (could display error message to user)
      }
    },
    [SelectedRowId, fetchAllitemList, orderId, updateCheckLis]
  );

  // check list coumns
  const columns: ColumnDef<ChecklistType | any>[] = useMemo(() => {
    const isEdit = (id: number) => SelectedRowId?.includes(id);
    return [
      {
        accessorKey: 'description',
        header: 'Description',
        enableSorting: true,
        size: 250,
        maxSize: 250,
      },
      {
        accessorKey: 'notes',
        header: 'Note',
        enableSorting: true,
        size: 200,
        maxSize: 200,
        cell: ({ row }) => {
          return isEdit(row?.original?.checkListId) ? (
            <InputField
              name={`items.${row?.index}.notes`}
              form={form}
              placeholder="Note"
              maxLength={128}
              pClassName="p-[1px]"
            />
          ) : (
            ''
          );
        },
      },
      {
        accessorKey: 'checkedAt',
        header: 'Date',
        size: 200,
      },
      {
        accessorKey: 'user',
        header: 'User',
        size: 150,
      },
    ];
  }, [SelectedRowId, form]);

  useEffect(() => {
    if (!rowSelection) return;

    // Get current form values
    const currentItems = form.getValues('items') || [];

    // Explicitly define the type for defaultItemsMap
    const defaultItemsMap = new Map<number, CheckListItemsType>(
      (defaultValues?.items || [])?.map((item: CheckListItemsType) => [
        item.checkListId,
        item,
      ])
    );

    // Efficiently update only unselected rows
    const updatedItems = currentItems.map((item) =>
      SelectedRowId?.includes(item?.checkListId)
        ? item // Keep selected rows unchanged
        : { ...item, note: defaultItemsMap.get(item.checkListId)?.notes ?? '' }
    );

    form.setValue('items', updatedItems);
  }, [rowSelection, form, defaultValues?.items, SelectedRowId]);

  return (
    <CustomDialog
      open={open}
      onOpenChange={onOpenChange}
      description=""
      className={cn('max-w-[90%] w-[70%] 2xl:w-[55%]')}
      contentClassName="h-[370px] 2xl:h-[450px]"
      title={'Checklist'}
    >
      <div className="grid grid-cols-1 px-5">
        <DataTable
          columns={columns}
          data={fields || []}
          isLoading={isLoading}
          totalItems={checkListItems?.pagination?.totalCount}
          sorting={sorting}
          setSorting={setSorting}
          enablePagination={false}
          tableClassName="max-h-[300px] 2xl:max-h-[380px] overflow-y-auto"
          enableRowSelection
          rowSelection={rowSelection}
          onRowSelectionChange={setRowSelection}
          bindingKey="checkListId"
          enableMultiRowSelection
          disableSelectAllCheckbox={true}
        />

        <div className="w-full pt-4 pb-1 space-x-3 text-right absolute bottom-2 right-6">
          <AppButton
            label="Submit"
            isLoading={updateCheckListLoading}
            onClick={form.handleSubmit(onSubmit)}
            className="w-40"
          />
          <AppButton
            label="Cancel"
            onClick={onOpenChange}
            variant="neutral"
            className="w-40"
          />
        </div>
      </div>
    </CustomDialog>
  );
};

export default memo(Checklist);
