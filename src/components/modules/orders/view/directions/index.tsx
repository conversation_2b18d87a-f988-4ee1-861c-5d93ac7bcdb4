import AppButton from '@/components/common/app-button';
import AppSpinner from '@/components/common/app-spinner';
import CustomDialog from '@/components/common/dialog';
import Text<PERSON>reaField from '@/components/forms/text-area';
import { hasEnabledPermission } from '@/lib/hasEnabledPermission';
import { cn, getQueryParam } from '@/lib/utils';
import {
  useAddUpdateOrderDirectionMutation,
  useGetOrderDirectionQuery,
} from '@/redux/features/orders/order.api';
import { OrderDirectionsTyps } from '@/types/order.types';
import { memo, useCallback, useEffect, useMemo } from 'react';
import { SubmitHandler, useForm, UseFormReturn } from 'react-hook-form';

interface DirectionsProps {
  open: boolean;
  onOpenChange: () => void;
}
const Directions = ({ open, onOpenChange }: DirectionsProps) => {
  const orderId = getQueryParam('id') as string;

  const { data, isFetching: isLoading } = useGetOrderDirectionQuery(orderId, {
    skip: !orderId,
    refetchOnMountOrArgChange: true,
  });

  const [addUpdateDirection, { isLoading: addUpdateLoading }] =
    useAddUpdateOrderDirectionMutation();

  // Check if the user has the "Order Entry Read Only" permission enabled
  const readOnlyPermission = hasEnabledPermission('Order Entry Read only');

  const defaultValues = useMemo(() => {
    const dataValue = data?.data;

    const baseDefaults = {
      id: dataValue?.orderDirection?.id ?? 0,
      deliveryDefaultLocation: dataValue?.deliveryDefaultLocation ?? '',
      directionText: dataValue?.orderDirection?.directionText ?? '',
      orderId,
      orderEntryReadOnly: readOnlyPermission,
    };

    return baseDefaults;
  }, [data?.data, orderId, readOnlyPermission]);

  const form: UseFormReturn<OrderDirectionsTyps> = useForm<OrderDirectionsTyps>(
    {
      defaultValues,
    }
  );

  useEffect(() => {
    if (defaultValues) {
      form.reset(defaultValues);
    }
  }, [defaultValues, form]);

  const isOrderEntryReadOnly = form.watch('orderEntryReadOnly');

  // on submit
  const onSubmit: SubmitHandler<OrderDirectionsTyps> = useCallback(
    async (formData) => {
      try {
        const { data }: any = await addUpdateDirection(formData);
        if (data?.statusCode === 200) {
          onOpenChange();
        }
      } catch (error) {
        // Handle error (could display error message to user)
      }
    },
    [addUpdateDirection, onOpenChange]
  );

  return (
    <CustomDialog
      open={open}
      onOpenChange={onOpenChange}
      description=""
      className={cn('max-w-[90%] w-[70%] md:w-[50%] 2xl:w-[35%] ')}
      contentClassName="h-[450px] 2xl:h-[450px] pb-0"
      title={'Directions'}
    >
      <>
        <div className="relative">
          <div className="grid grid-cols-1 gap-4 px-6">
            <TextAreaField
              name="deliveryDefaultLocation"
              form={form}
              label="Location Directions"
              placeholder="Location directions"
              rows={5}
              disabled
              className="min-h-[150px] max-h-[150px]"
            />
            <TextAreaField
              name="directionText"
              form={form}
              label="Order Directions"
              placeholder="Order directions"
              rows={5}
              className="min-h-[150px] max-h-[150px]"
              disabled={isOrderEntryReadOnly}
            />
          </div>
          <div className="flex items-center gap-4 fixed bottom-0 right-6 bg-white mt-3 pb-4 rounded-b-lg">
            <AppButton
              label="Submit"
              className="w-28"
              onClick={form.handleSubmit(onSubmit)}
              isLoading={addUpdateLoading}
              disabled={isOrderEntryReadOnly}
            />
            <AppButton
              label="Cancel"
              variant="neutral"
              onClick={onOpenChange}
              className="w-28"
            />
          </div>
        </div>
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-white/50 backdrop-blur-[2px] z-10 rounded-lg">
            <AppSpinner
              className="h-8 w-8 text-brand-teal-Default"
              isLoading={isLoading}
            />
          </div>
        )}
      </>
    </CustomDialog>
  );
};

export default memo(Directions);
