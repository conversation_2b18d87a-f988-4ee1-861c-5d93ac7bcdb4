import DataTable from '@/components/common/data-tables';
import CustomDialog from '@/components/common/dialog';
import { Button } from '@/components/ui/button';
import { ROUTES } from '@/constants/routes-constants';
import { cn, DEFAULT_FORMAT, formatDate } from '@/lib/utils';
import { OrderSubRentsTypes } from '@/types/orders/order-item-details.types';
import { ColumnDef } from '@tanstack/react-table';
import { useCallback, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';

const SubRentDialog = ({
  openSubRental,
  setOpenSubRental,
  data,
  isLoading,
}: any) => {
  const navigate = useNavigate();

  const toggleSubRentalDialog = useCallback(() => {
    setOpenSubRental({ state: false, id: null });
  }, [setOpenSubRental]);

  const handleSubRentalClose = () => {
    navigate(`${ROUTES.EDIT_ORDERS}?id=${openSubRental?.id}&tab=information`);
    toggleSubRentalDialog();
  };

  const subRentalColumns: ColumnDef<OrderSubRentsTypes>[] = useMemo(() => {
    return [
      {
        accessorKey: 'id',
        header: 'SR#',
        size: 80,
      },
      {
        accessorKey: 'createdBy',
        header: 'SP',
        size: 80,
      },
      {
        accessorKey: 'resvNo',
        header: 'Reservation #',
      },
      {
        accessorKey: 'name',
        header: 'Equipment From',
      },
      {
        accessorKey: 'pickupDate',
        header: 'Pickup Date',
        size: 100,
        cell: ({ row }: any) =>
          formatDate(row?.original?.pickupDate, DEFAULT_FORMAT),
      },
      {
        accessorKey: 'returnDate',
        header: 'Return Date',
        size: 100,
        cell: ({ row }: any) =>
          formatDate(row?.original?.returnDate, DEFAULT_FORMAT),
      },
    ];
  }, []);
  return (
    <CustomDialog
      title="Sub Rentals"
      onOpenChange={handleSubRentalClose}
      description=""
      open={openSubRental?.state}
      className={cn('min-w-[70%] 2xl:min-w-[55%]')}
      contentClassName="h-[450px] 2xl:h-[400px] overflow-y-auto"
      footer={
        <Button className="w-24" onClick={handleSubRentalClose}>
          Close
        </Button>
      }
      footerClassName="p-4"
    >
      <div className="flex flex-col gap-2 px-4 py-2">
        <p>This order required the following Sub-Rentals</p>
        <DataTable
          data={data ?? []}
          columns={subRentalColumns}
          isLoading={isLoading}
          enableSearch={false}
          enablePagination={false}
          loaderRows={6}
          tableClassName="max-h-[300px] 2xl:max-h-[320px] overflow-auto"
        />
      </div>
    </CustomDialog>
  );
};

export default SubRentDialog;
