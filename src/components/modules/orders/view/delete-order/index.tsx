import AppButton from '@/components/common/app-button';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import CustomDialog from '@/components/common/dialog';
import InputField from '@/components/forms/input-field';
import Labels from '@/components/forms/Label';
import TextAreaField from '@/components/forms/text-area';
import { cn, DATE_TIME_FORMAT, formatDate, getQueryParam } from '@/lib/utils';
import { useDeleteOrderMutation } from '@/redux/features/orders/order.api';
import { DeleteOrderType, OrderInformationTypes } from '@/types/order.types';
import { PackageX } from 'lucide-react';
import { memo, useCallback, useEffect, useMemo, useState } from 'react';
import { useForm, useFormContext, UseFormReturn } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';

interface DeleteOrderProps {
  open: boolean;
  onOpenChange: (success?: boolean) => void;
  orderData?: OrderInformationTypes | null;
}
const DeleteOrder = ({ open, onOpenChange, orderData }: DeleteOrderProps) => {
  const queryParamOrderId = getQueryParam('id') as string;

  // order id by props
  const id = orderData?.id;
  const orderId = queryParamOrderId ?? id;
  const orderForm = useFormContext<DeleteOrderType>();
  const isDeleted = queryParamOrderId
    ? orderForm?.watch('isDeleted')
    : orderData?.isDeleted;
  const orderDetails = queryParamOrderId ? orderForm?.watch() : orderData;
  const navigate = useNavigate();

  const defaultValues = useMemo(() => {
    return {
      deletedBy: orderDetails?.deletedBy,
      takenBy: orderDetails?.takenBy,
      updatedAt: isDeleted
        ? formatDate(orderDetails?.updatedAt ?? '', DATE_TIME_FORMAT)
        : '',
      reasonOfDeletion: orderDetails?.reasonOfDeletion,
    };
  }, [
    isDeleted,
    orderDetails?.deletedBy,
    orderDetails?.reasonOfDeletion,
    orderDetails?.takenBy,
    orderDetails?.updatedAt,
  ]);

  const form: UseFormReturn<DeleteOrderType> = useForm<DeleteOrderType>({
    defaultValues,
    mode: 'onChange',
  });
  const { handleSubmit, reset } = form;

  useEffect(() => {
    if (defaultValues) {
      form.reset(defaultValues);
    }
  }, [defaultValues, form]);

  const [missingOrderWarning, setMissingOrderWarning] =
    useState<boolean>(false);
  const [actionType, setActionType] = useState<'ok' | 'cancel' | string>('');

  const isMissingOrder = ['MISSING_ORDER']?.includes(
    orderDetails?.originType || ''
  );

  const [deleteOrder, { isLoading: deleteLoading }] = useDeleteOrderMutation();

  // toggle missing Order Warning
  const toggleMissingOrderWarning = useCallback(() => {
    setMissingOrderWarning((prev) => !prev);
  }, []);

  // on submit
  const onSubmit = useCallback(
    (action?: 'ok' | 'cancel') => async (formData: DeleteOrderType) => {
      try {
        setActionType(action || 'ok');
        if (!action && isMissingOrder) {
          toggleMissingOrderWarning();
          return;
        }

        const payload = { ...formData, id: orderId };
        const { data }: any = await deleteOrder(payload);
        if (data?.statusCode === 200) {
          navigate('/orders');
          onOpenChange(data?.success);
          toggleMissingOrderWarning();
        }
      } catch (error) {
        // Handle error (could display error message to user)
      }
    },
    [
      deleteOrder,
      isMissingOrder,
      navigate,
      onOpenChange,
      orderId,
      toggleMissingOrderWarning,
    ]
  );

  // handle cancel
  const handleCancel = useCallback(() => {
    onOpenChange();
    reset();
  }, [onOpenChange, reset]);

  return (
    <div>
      <CustomDialog
        open={open}
        onOpenChange={handleCancel}
        description=""
        className={cn('max-w-[90%] w-[80%] md:w-[50%] 2xl:w-[35%]')}
        contentClassName="max-h-[410px] overflow-y-auto px-6 pb-0"
        title={'Deletion Info'}
      >
        <div className="w-full">
          {isDeleted && <Labels label="The order deletion info" />}
          <div className="border rounded-lg p-4 my-3">
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              <InputField
                name="deletedBy"
                form={form}
                label="Deleted By"
                placeholder="Deleted By"
                disabled={isDeleted}
                pClassName="min-w-fit"
                maxLength={50}
              />
              <InputField
                name="takenBy"
                form={form}
                label="Taken By"
                placeholder="Taken By"
                disabled
              />
              <InputField
                name="updatedAt"
                form={form}
                label="Date & Time"
                placeholder="Date & Time"
                disabled
                pClassName="col-span-2 md:col-span-1"
              />
            </div>
            <TextAreaField
              name="reasonOfDeletion"
              form={form}
              label="Reason for Deletion"
              placeholder="Reason for Deletion"
              rows={10}
              disabled={isDeleted}
              maxLength={200}
              pClassName="pt-4"
              className="max-h-[100px]"
            />
          </div>

          {!isDeleted && (
            <div className="flex items-center justify-end sticky bottom-0 bg-white gap-4 py-3 mt-2">
              <AppButton
                label="Delete"
                icon={PackageX}
                className="w-28"
                onClick={handleSubmit(onSubmit())}
                isLoading={deleteLoading}
              />
              <AppButton
                label="Cancel"
                variant="neutral"
                onClick={handleCancel}
                className="w-28"
              />
            </div>
          )}
        </div>
      </CustomDialog>
      <AppConfirmationModal
        title="Confirmation"
        description={
          <div>
            This is a missing equipment invoice. Deleting the invoice will
            normally add the quantities for all items on this invoice back into
            inventory. Do you want these quantities added back into inventory?
          </div>
        }
        onOpenChange={toggleMissingOrderWarning}
        open={missingOrderWarning}
        handleSubmit={handleSubmit(onSubmit('ok'))}
        handleCancel={handleSubmit(onSubmit('cancel'))}
        isLoading={actionType === 'ok' && deleteLoading}
        cancelLoading={actionType === 'cancel' && deleteLoading}
      />
    </div>
  );
};

export default memo(DeleteOrder);
