import EditIcon from '@/assets/icons/EditIcon';
import AppButton from '@/components/common/app-button';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import DataTable from '@/components/common/data-tables';
import ActionColumnMenu from '@/components/common/data-tables/ActionColumn';
import {
  DATE_TIME_FORMAT,
  formatDate,
  getQueryParam,
  updateQueryParam,
} from '@/lib/utils';
import {
  useDeleteNoteMutation,
  useGetNotesByOrderIdQuery,
} from '@/redux/features/orders/order.api';
import {
  OrderInformationTypes,
  OrderNotesType,
  OrderNoteTabEnum,
} from '@/types/order.types';
import { ColumnDef } from '@tanstack/react-table';
import { Plus } from 'lucide-react';
import { useCallback, useState } from 'react';
import { useFormContext } from 'react-hook-form';

type OrderNotesProps = {
  setActiveTab: (tab: string) => void;
};

const OrderNotesTab = ({ setActiveTab }: OrderNotesProps) => {
  const orderIdFromQuery = getQueryParam('id') as string;
  const form = useFormContext<OrderInformationTypes>();
  const isDeleted = form.watch('isDeleted');
  const isOrderEntryReadOnly = form.watch('orderEntryReadOnly');

  const { data, isFetching } = useGetNotesByOrderIdQuery(
    Number(orderIdFromQuery),
    { skip: !orderIdFromQuery }
  );

  const [deleteNote, { isLoading: isDeleteLoading }] = useDeleteNoteMutation();
  const [openDeleteDialog, setOpenDeleteDialog] = useState<boolean>(false);
  const [notesToDelete, setNotesToDelete] = useState<number | null>(null);

  const toggleNotes = useCallback(() => {
    setActiveTab(OrderNoteTabEnum.ADD_NOTE);
  }, [setActiveTab]);

  const onOpenChanged = useCallback(() => {
    setOpenDeleteDialog((prevState) => !prevState);
  }, [setOpenDeleteDialog]);

  const handleDeleteClick = useCallback(
    (noteId: number) => {
      setNotesToDelete(noteId);
      onOpenChanged();
    },
    [onOpenChanged]
  );

  const handleDeleteCustomer = async () => {
    try {
      await deleteNote({
        orderId: Number(orderIdFromQuery),
        orderNoteId: Number(notesToDelete),
      }).unwrap();
      onOpenChanged();
      setNotesToDelete(null);
    } catch (error) {}
  };

  const columns: ColumnDef<OrderNotesType>[] = [
    {
      accessorKey: 'createdBy',
      header: 'User',
      size: 100,
    },

    {
      accessorKey: 'createdAt',
      header: 'Date',
      size: 100,
      cell: (info) =>
        formatDate(info?.row?.original?.createdAt, DATE_TIME_FORMAT),
    },
    {
      accessorKey: 'note',
      header: 'Note',
      size: 250,
      maxSize: 250,
    },
    {
      id: 'action',
      size: 110,
      header: 'Actions',
      cell: ({ row }) => (
        <ActionColumnMenu
          customEdit={
            <AppButton
              label=""
              icon={EditIcon}
              variant="neutral"
              className="border-0 p-0 h-0"
              onClick={() => {
                updateQueryParam(row.original.id, 'noteId');
                toggleNotes();
              }}
              disabled={isDeleted || isOrderEntryReadOnly}
            />
          }
          onDelete={() => handleDeleteClick(row.original.id)}
          contentClassName="w-fit z-[99]"
          disabled={isDeleted || isOrderEntryReadOnly}
        />
      ),
    },
  ];

  const CustomToolbar = (
    <div className="flex flex-row justify-end">
      <AppButton
        label="Add Note"
        variant="neutral"
        className="bg-brand-teal-Default hover:bg-brand-teal-hover border-border-brand-teal-Default text-white"
        iconClassName="text-white"
        icon={Plus}
        onClick={toggleNotes}
        disabled={isDeleted || isOrderEntryReadOnly}
      />
    </div>
  );
  return (
    <>
      <DataTable
        tableClassName="max-h-[300px] 2xl:[250px] overflow-y-auto"
        columns={columns}
        data={data?.data ?? []}
        enablePagination={false}
        isLoading={isFetching}
        loaderRows={5}
        customToolBar={CustomToolbar}
      />
      <AppConfirmationModal
        description={<div>Are you sure you want to delete records ?</div>}
        open={openDeleteDialog}
        onOpenChange={onOpenChanged}
        handleCancel={onOpenChanged}
        disabled={isDeleteLoading}
        handleSubmit={handleDeleteCustomer}
        isLoading={isDeleteLoading}
      />
    </>
  );
};

export default OrderNotesTab;
