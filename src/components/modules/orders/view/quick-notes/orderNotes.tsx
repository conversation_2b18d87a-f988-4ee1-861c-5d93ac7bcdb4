import BreadcrumbDialogRenderer from '@/components/common/BreadcrumbDialogRenderer';
import { getQueryParam, updateQueryParam } from '@/lib/utils';
import { OrderNoteTabEnum } from '@/types/order.types';
import { useMemo, useState } from 'react';
import Notes from './addNote';
import OrderNotesTab from './orderNotesTab';

interface OrderNotesInfo {
  open: boolean;
  onOpenChange: () => void;
}

const OrderNotes = ({ open, onOpenChange }: OrderNotesInfo) => {
  const [activeTab, setActiveTab] = useState('order-notes');

  const noteId = getQueryParam('noteId') as string;

  // Sample tab content for the dialog
  const dialogTabs = useMemo(
    () => [
      {
        label: 'Order Notes',
        value: OrderNoteTabEnum.ORDER_NOTES,
        content: <OrderNotesTab setActiveTab={setActiveTab} />,
      },
      {
        label: noteId ? 'Edit Note' : 'Add Note',
        value: OrderNoteTabEnum.ADD_NOTE,
        content: <Notes setActiveTab={setActiveTab} />,
      },
    ],
    [noteId]
  );

  return (
    <>
      <BreadcrumbDialogRenderer
        listItems={dialogTabs}
        activeTab={activeTab}
        isOpen={open}
        onOpenChange={onOpenChange}
        setActiveTab={(tab) => {
          noteId && updateQueryParam(null, 'noteId');
          setActiveTab(tab);
        }}
        className="min-w-[80%] md:min-w-[65%] 2xl:min-w-[50%]"
        contentClassName="h-[400px]"
      />
    </>
  );
};

export default OrderNotes;
