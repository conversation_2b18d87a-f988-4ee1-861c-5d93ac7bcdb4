import AppButton from '@/components/common/app-button';
import AppSpinner from '@/components/common/app-spinner';
import <PERSON><PERSON><PERSON><PERSON>ield from '@/components/forms/text-area';
import { getQueryParam, updateQueryParam } from '@/lib/utils';
import {
  useCreateNoteMutation,
  useGetOrderNoteByIdQuery,
} from '@/redux/features/orders/order.api';
import { OrderNoteTabEnum } from '@/types/order.types';
import { useCallback, useEffect, useMemo } from 'react';
import { useForm } from 'react-hook-form';

type NoteForm = {
  note: string;
};

type OrderNotesProps = {
  setActiveTab: (tab: string) => void;
};

const Notes = ({ setActiveTab }: OrderNotesProps) => {
  const orderId = getQueryParam('id') as string;
  const noteId = getQueryParam('noteId') as string;

  // get note details
  const { data: noteData, isLoading } = useGetOrderNoteByIdQuery(
    {
      orderId,
      orderNoteId: noteId,
    },
    { skip: !noteId, refetchOnMountOrArgChange: true }
  );

  // add update note
  const [addUpdateNote, { isLoading: isCreating }] = useCreateNoteMutation();

  const defaultValues = useMemo(() => {
    return {
      note: noteData?.data?.note || '',
    };
  }, [noteData?.data?.note]);

  const form = useForm<NoteForm>({
    defaultValues,
    mode: 'onChange',
  });

  useEffect(() => {
    if (defaultValues) {
      form.reset(defaultValues);
    }
  }, [form, defaultValues]);

  // handle close
  const handleDialogClose = useCallback(() => {
    if (noteId) {
      updateQueryParam(null, 'noteId');
    }
    form.reset();
    setActiveTab(OrderNoteTabEnum.ORDER_NOTES);
  }, [form, noteId, setActiveTab]);

  const onSubmit = useCallback(
    async (data: NoteForm) => {
      try {
        await addUpdateNote({
          orderId: Number(orderId),
          body: {
            id: Number(noteId) || 0,
            note: data?.note,
          },
        });
        handleDialogClose();
      } catch (error) {}
    },
    [addUpdateNote, handleDialogClose, noteId, orderId]
  );

  return (
    <>
      <div className="px-1 relative">
        <TextAreaField
          name="note"
          form={form}
          placeholder="Enter Note"
          rows={13}
          className="max-h-[280px]"
        />
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-black/10 backdrop-blur-sm rounded-md">
            <AppSpinner isLoading />
          </div>
        )}
      </div>
      <div className="flex justify-end gap-4 mt-5 bottom-0 fixed right-6 mb-4 bg-white">
        <AppButton
          onClick={form.handleSubmit(onSubmit)}
          label={'Submit'}
          className="min-w-28"
          isLoading={isCreating}
        />
        <AppButton
          type="button"
          label="Cancel"
          variant="neutral"
          className="w-28"
          onClick={handleDialogClose}
        />
      </div>
    </>
  );
};

export default Notes;
