import DataTable from '@/components/common/data-tables';
import CustomDialog from '@/components/common/dialog';
import DatePicker from '@/components/forms/date-picker';
import Labels from '@/components/forms/Label';
import {
  cn,
  convertToFloat,
  DATE_TIME_FORMAT,
  formatDate,
  getQueryParam,
} from '@/lib/utils';
import { useGetOrderHistoryQuery } from '@/redux/features/orders/order.api';
import { memo, useEffect, useMemo } from 'react';
import { useForm } from 'react-hook-form';

interface OrderHistoryProps {
  open: boolean;
  onOpenChange: () => void;
}

const OrderHistory = ({ open, onOpenChange }: OrderHistoryProps) => {
  const orderId = getQueryParam('id') as string;
  const { data, isFetching } = useGetOrderHistoryQuery(orderId, {
    refetchOnMountOrArgChange: true,
  });
  const orderHistories = data?.data?.orderHistories;

  const form = useForm({
    defaultValues: { date: new Date() },
    mode: 'onChange',
  });

  useEffect(() => {
    form.reset();
  }, [form]);

  // coumns
  const columns = useMemo(() => {
    return [
      {
        accessorKey: 'createdAt',
        header: 'Date',
        size: 150,
        cell: ({ row }: any) =>
          formatDate(row?.original?.createdAt, DATE_TIME_FORMAT),
      },
      {
        accessorKey: 'createdBy',
        header: 'User',
        size: 200,
      },
      {
        accessorKey: 'salesAmount',
        header: 'Total',
        size: 100,
        cell: ({ row }: any) =>
          convertToFloat({ value: row?.original?.salesAmount, prefix: '$' }),
      },
    ];
  }, []);

  return (
    <CustomDialog
      open={open}
      onOpenChange={onOpenChange}
      description=""
      className={cn('max-w-[90%] w-[90%] md:w-[50%] 2xl:w-[40%]')}
      title={'Order History'}
    >
      <>
        <div className="grid grid-cols-1 px-6">
          <DataTable
            columns={columns}
            data={orderHistories ?? []}
            enablePagination={false}
            isLoading={isFetching}
            tableClassName="h-[250px] overflow-y-auto"
          />
        </div>
        <div className="px-6 mt-5 pb-2 flex items-center gap-4">
          <Labels label="Date Quote Switched to Order" />
          <DatePicker name="date" form={form} disabled />
        </div>
      </>
    </CustomDialog>
  );
};

export default memo(OrderHistory);
