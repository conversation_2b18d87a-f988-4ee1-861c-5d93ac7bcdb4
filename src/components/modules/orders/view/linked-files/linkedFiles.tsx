import AppButton from '@/components/common/app-button';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import CustomDialog from '@/components/common/dialog';
import UploadFile from '@/components/common/UploadFile';
import { UseToast } from '@/components/ui/toast/ToastContainer';
import { ORDERS_API_ROUTES } from '@/constants/api-constants';
import { ACCEPTED_FILE_TYPES, getFileIcon } from '@/constants/order-constants';
import { useDownloadFile } from '@/hooks/useDownloadFile';
import { cn, formatDate, getQueryParam } from '@/lib/utils';
import {
  useDefaultLinkedFileMutation,
  useDeleteLinkedFileMutation,
  useGetLinkedFilesListByIdQuery,
} from '@/redux/features/orders/order.api';
import { LinkedFilesType } from '@/types/order.types';
import { ColumnDef } from '@tanstack/react-table';
import { Download, Plus, Trash2 } from 'lucide-react';
import { useCallback, useMemo, useState } from 'react';
import ActionMenuItem, { MenuListTypes } from './ActionColumn';
import DataTable from '@/components/common/data-tables';

interface LinkedFilesInfo {
  open: boolean;
  onOpenChange: () => void;
}

type ReuploadTypes = {
  id: number | null;
  state: boolean;
};

const LinkedFiles = ({ open, onOpenChange }: LinkedFilesInfo) => {
  // states
  const [fileId, setFileId] = useState<number | null>(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [isReupload, setIsReupload] = useState<ReuploadTypes>({
    id: null,
    state: false,
  });
  const [openActionDialog, setOpenActionDialog] = useState({
    state: false,
    action: '',
  });

  // hooks
  const toast = UseToast();
  const orderId = getQueryParam('id') as string;
  const { data, isFetching, refetch } = useGetLinkedFilesListByIdQuery(orderId);
  const [deleteFile, { isLoading: isDeleteLoading }] =
    useDeleteLinkedFileMutation();
  const [defaultFile, { isLoading: isDefaultLoading }] =
    useDefaultLinkedFileMutation();
  const { downloadFile, isLoading: isDownloading } = useDownloadFile();
  const [downloadingId, setDownloadingId] = useState<number | null>(null);

  // Determine the appropriate API URL and payload
  const uploadUrl =
    isReupload && isReupload.id
      ? ORDERS_API_ROUTES.REUPLOAD(isReupload?.id)
      : ORDERS_API_ROUTES.UPLOAD(orderId);

  // Columns for the data table
  const columns: ColumnDef<LinkedFilesType>[] = [
    {
      accessorKey: 'icon',
      header: 'Icon',
      size: 50,
      cell: ({ row }) => {
        const fileName: string = row.original.fileName;
        return (
          <div className="flex justify-center">
            {getFileIcon(fileName ?? '')}
          </div>
        );
      },
    },
    {
      accessorKey: 'fileName',
      header: 'File Name',
      size: 300,
    },

    {
      accessorKey: 'createdAt',
      header: 'Date Created',
      size: 100,
      cell: (info) =>
        formatDate(info?.row?.original?.createdAt, 'MM/DD/YYYY hh:mm A'),
    },
    {
      accessorKey: 'fileSizeText',
      header: 'Size',
      size: 90,
    },
  ];

  // Open/Close Dialog handlers
  const onCloseDialog = useCallback(() => {
    setOpenActionDialog({ state: false, action: '' });
  }, []);

  const handleClose = () => {
    if (isReupload) {
      setIsReupload({ state: false, id: null });
    }
    setOpenDialog(false);
  };

  const handleFileAction = useCallback(
    (id: number, action: 'delete' | 'default') => {
      setFileId(id);
      setOpenActionDialog({ state: true, action });
    },
    []
  );

  // Action handling functions
  const handleAction = useCallback(
    async (action: 'delete' | 'default') => {
      if (!fileId) return;
      const actionFn = action === 'delete' ? deleteFile : defaultFile;
      try {
        const response = await actionFn(fileId).unwrap();
        toast.success(response.message);
        setFileId(null);
        onCloseDialog();
        refetch();
      } catch (error) {
        // Handle error
      }
    },
    [fileId, deleteFile, defaultFile, toast, onCloseDialog, refetch]
  );

  const handleDownload = useCallback(
    (id: number) => {
      setDownloadingId(id);
      const response = downloadFile({
        url: ORDERS_API_ROUTES.DOWNLOAD(id),
      });
      toast.promise(response, {
        loading: 'Downloading file...',
        success: 'File downloaded successfully.',
        error: 'Failed to download file',
      });
    },
    [downloadFile, toast]
  );

  // Action column configuration
  const dropdownMenuItem = useMemo(() => {
    const menuList: MenuListTypes[] = [
      {
        label: 'Delete',
        onClick: (id: number) => handleFileAction?.(id, 'delete'),
        icon: <Trash2 className="w-5 h-5" />,
        className: 'text-base text-text-danger',
      },
    ];

    return menuList?.filter((order) => order?.onClick);
  }, [handleFileAction]);

  const ActionColumn: ColumnDef<LinkedFilesType> = useMemo(
    () => ({
      id: 'action',
      header: 'Actions',
      size: 180,
      cell: ({ row }) => {
        const value = row.original?.id;
        return (
          <ActionMenuItem
            customEdit={
              <AppButton
                onClick={() => handleDownload(row.original.id)}
                variant="neutral"
                label="Download"
                icon={Download}
                iconClassName="w-5"
                isLoading={isDownloading && downloadingId === row.original.id}
              />
            }
            dropdownMenuItem={dropdownMenuItem}
            rowId={value}
          />
        );
      },
    }),
    [downloadingId, dropdownMenuItem, handleDownload, isDownloading]
  );

  const CustomToolbar = (
    <div className="flex flex-row justify-end">
      <AppButton
        label="Add File"
        variant="neutral"
        className="bg-brand-teal-Default hover:bg-brand-teal-hover border-border-brand-teal-Default text-white"
        iconClassName="text-white"
        icon={Plus}
        onClick={() => setOpenDialog(true)}
      />
    </div>
  );

  return (
    <div>
      <CustomDialog
        onOpenChange={onOpenChange}
        description=""
        open={open}
        className={cn('max-w-[90%] w-[71%] 2xl:w-[50%]')}
        title={'Linked Files'}
        contentClassName="h-[370px] 2xl:h-[450px]"
      >
        <div className="px-6 grid grid-cols-1 gap-6 pb-4">
          <DataTable
            columns={[...columns, ActionColumn]}
            data={data?.data ?? []}
            enablePagination={false}
            isLoading={isFetching}
            tableClassName="max-h-[300px] 2xl:max-h-[350px] overflow-y-auto"
            customToolBar={CustomToolbar}
          />
        </div>
      </CustomDialog>

      {/* Confirmation Modal for actions */}
      <AppConfirmationModal
        title={openActionDialog.action === 'delete' ? 'Delete' : 'Make Default'}
        description={
          openActionDialog.action === 'delete'
            ? 'Are you sure you want to delete this file?'
            : 'Are you sure you want to set this file as default?'
        }
        open={openActionDialog.state}
        onOpenChange={onCloseDialog}
        handleCancel={onCloseDialog}
        disabled={isDeleteLoading || isDefaultLoading}
        isLoading={isDeleteLoading || isDefaultLoading}
        handleSubmit={() =>
          handleAction(openActionDialog.action as 'delete' | 'default')
        }
      />

      {/* File Upload Component */}
      <UploadFile
        acceptedFileTypes={ACCEPTED_FILE_TYPES}
        onUpload={refetch}
        isModalOpen={openDialog}
        onClose={handleClose}
        url={uploadUrl}
        dragDropText="Drag & Drop or upload to add a document as a Linked File to this item."
      />
    </div>
  );
};

export default LinkedFiles;
