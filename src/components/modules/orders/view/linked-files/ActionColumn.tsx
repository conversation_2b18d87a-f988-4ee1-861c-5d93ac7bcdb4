import DotIcon from '@/assets/icons/DotIcon';
import AppButton from '@/components/common/app-button';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';
import { SquareArrowOutUpRight } from 'lucide-react';

interface ActionColumnProps {
  onEdit?: () => void;
  customEdit?: React.ReactNode;
  dropdownMenuItem: MenuListTypes[];
  rowId: number;
}
export interface MenuListTypes {
  label: string;
  onClick?: (id: number) => void;
  icon?: React.ReactNode;
  className?: string;
  disabled?: boolean;
}

const ActionMenuItem = ({
  dropdownMenuItem,
  rowId,
  onEdit,
  customEdit,
}: ActionColumnProps) => {
  return (
    <div className="flex flex-row gap-x-2 justify-center items-center ">
      {customEdit ||
        (onEdit && (
          <AppButton
            label="View details"
            variant="neutral"
            className=""
            icon={SquareArrowOutUpRight}
            iconClassName="w-4 h-4"
            onClick={onEdit}
          />
        ))}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <DotIcon />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-[200px] rounded-xl">
          {dropdownMenuItem?.map((item, index: number) => {
            return (
              <DropdownMenuItem
                disabled={item?.disabled}
                key={`${item?.label}-${index}`}
                className={cn(item?.className)}
                onClick={() => {
                  // Pass rowId only if onClick expects it
                  if (rowId && item.onClick) {
                    item.onClick(rowId); // Pass id to onClick
                  }
                }}
              >
                <div className="flex flex-row gap-3 items-center">
                  {item?.icon && item?.icon}
                  <span>{item?.label}</span>
                </div>
              </DropdownMenuItem>
            );
          })}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

export default ActionMenuItem;
