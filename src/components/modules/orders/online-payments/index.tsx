import EditIcon from '@/assets/icons/EditIcon';
import DataTable from '@/components/common/data-tables';
import { ListType } from '@/components/forms/select';
import {
  matchCustomerSalesStatus,
  matchOrderAmount,
} from '@/constants/order-constants';
import { ROUTES } from '@/constants/routes-constants';
import { cn, convertToFloat, formatDate } from '@/lib/utils';
import { useGetEnumsListQuery } from '@/redux/features/enums-api/enums-api';
import {
  useCancelLinkMutation,
  useGetOnlinePaymentDataQuery,
} from '@/redux/features/online-payment/online-payment.api';
import { OperatorType, SortingStateType } from '@/types/common.types';
import { OnlinePaymentDTOForm } from '@/types/order.types';
import { OnlinePaymentListTypes } from '@/types/orders/online-payments.types';
import { ColumnDef } from '@tanstack/react-table';
import debounce from 'lodash/debounce';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import CustomToolbar from './CustomToolbar';
import Header from './Header';

const OnlinePayments = () => {
  const navigate = useNavigate();
  const [operatorOptions, setOperatorOptions] = useState<ListType[]>(
    matchCustomerSalesStatus
  );
  const [inputValue, setInputValue] = useState('');
  const [debouncedSearch, setDebouncedSearch] = useState('');
  const [sorting, setSorting] = useState<SortingStateType[]>([
    { id: 'orderid', desc: true },
  ]);
  const [pagination, setPagination] = useState({ pageSize: 10, pageIndex: 0 });

  const { data: paymentDays, isLoading: paymentDaysLoading } =
    useGetEnumsListQuery({ name: 'PaymentDays' });
  const { data: paymentStatus, isLoading: paymentStatusLoading } =
    useGetEnumsListQuery({ name: 'PaymentStatus' });
  const { data: paymentFilterFields, isLoading: paymentFilterFieldsLoading } =
    useGetEnumsListQuery({ name: 'PaymentFilterFields' });

  const form = useForm<OnlinePaymentDTOForm>({
    defaultValues: {
      laststatuschange: 'ALL',
      field: 'status',
      operator: OperatorType.Equals,
      statusvalue: 'WAITING_FOR_PAYMENT',
      value: '',
    },
  });

  const [cancelLink] = useCancelLinkMutation();

  const { data, isLoading } = useGetOnlinePaymentDataQuery({
    pageSize: pagination.pageSize,
    pageNumber: pagination.pageIndex + 1,
    sortBy: sorting[0]?.id,
    sortAscending: sorting[0]?.desc,
    filters: [
      {
        field: 'laststatuschange',
        operator: '',
        value: form.watch('laststatuschange'),
      },
      {
        field: form.watch('field'),
        operator: form.watch('operator'),
        value:
          form.watch('field') === 'status'
            ? form.watch('statusvalue')
            : debouncedSearch,
      },
    ],
  });

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(event.target.value);
  };
  // Debounced search handler
  const debouncedSearchHandler = useMemo(
    () =>
      debounce((val: string) => {
        setDebouncedSearch(val);
      }, 500),
    []
  );

  // Update debounce handler when input changes
  useEffect(() => {
    debouncedSearchHandler(inputValue);
    return () => {
      debouncedSearchHandler.cancel();
    };
  }, [inputValue, debouncedSearchHandler]);
  const handleChangeField = (value: string) => {
    setOperatorOptions(
      value === 'orderid' || value === 'amount'
        ? matchOrderAmount
        : matchCustomerSalesStatus
    );
    if (value === 'orderid' || value === 'amount') {
      form.setValue('operator', 'equals');
    } else {
      form.setValue('operator', 'contains');
    }
  };

  const navigateToOrder = useCallback(
    () => navigate(ROUTES.ORDERS),
    [navigate]
  );

  const handleCancelLink = useCallback(
    async (row: OnlinePaymentListTypes) => {
      const { id, status, token, uniqueId } = row;
      try {
        await cancelLink({
          body: {
            id,
            status,
            token,
            uniqueId,
          },
        });
      } catch (error) {}
    },
    [cancelLink]
  );

  const OnlinePaymentsColumn: ColumnDef<OnlinePaymentListTypes>[] = useMemo(
    () => [
      {
        accessorKey: 'orderid',
        header: 'Order ID',
        enableSorting: true,
        size: 150,
      },
      {
        accessorKey: 'customer',
        header: 'Customer',
        size: 250,
        enableSorting: true,
      },
      {
        accessorKey: 'salesperson',
        header: 'Salesperson',
        enableSorting: true,
        size: 200,
      },
      {
        accessorKey: 'laststatuschange',
        header: 'Last Status Change',
        enableSorting: true,
        size: 250,
        cell: ({ row }) =>
          formatDate(row.original.laststatuschange, 'MM/DD/YYYY hh:mm A'),
      },
      {
        accessorKey: 'amount',
        header: 'Amount',
        enableSorting: true,
        size: 200,
        cell: ({ row }) =>
          convertToFloat({ value: row.original.amount, prefix: '$' }),
      },
      {
        accessorKey: 'status',
        header: 'Status',
        enableSorting: true,
        size: 200,
      },
      {
        id: 'action',
        size: 110,
        header: 'Actions',
        cell: ({ row }) => (
          <button
            onClick={() => handleCancelLink(row?.original)}
            className={cn(
              'flex items-center justify-center gap-2 cursor-pointer border rounded-md px-3 py-1',
              row.original.status !== 'WAITING_FOR_PAYMENT'
                ? 'border-gray-300 bg-[#F5F5F5] cursor-not-allowed'
                : 'border-text-tertiary text-black'
            )}
            disabled={row.original.status !== 'WAITING_FOR_PAYMENT'}
          >
            <EditIcon /> Cancel Link
          </button>
        ),
      },
    ],
    [handleCancelLink]
  );

  return (
    <div className="px-6 space-y-4">
      <Header navigateToOrder={navigateToOrder} />
      <div className="mt-5">
        <DataTable
          columns={OnlinePaymentsColumn}
          data={data?.data ?? []}
          isLoading={isLoading}
          customToolBar={
            <CustomToolbar
              form={form}
              inputValue={inputValue}
              handleSearchChange={handleSearchChange}
              paymentDaysLoading={paymentDaysLoading}
              paymentDays={paymentDays?.data ?? []}
              paymentFilterFieldsLoading={paymentFilterFieldsLoading}
              paymentFilterFields={paymentFilterFields?.data ?? []}
              operatorOptions={operatorOptions}
              paymentStatusLoading={paymentStatusLoading}
              paymentStatus={paymentStatus?.data ?? []}
              handleChangeField={handleChangeField}
            />
          }
          enablePagination={true}
          pagination={pagination}
          setPagination={setPagination}
          tableClassName="max-h-[400px] lg:max-h-[500px] overflow-auto"
          sorting={sorting}
          setSorting={setSorting}
          totalItems={data?.pagination?.totalCount}
        />
      </div>
    </div>
  );
};

export default OnlinePayments;
