import { memo } from 'react';
import { UseFormReturn } from 'react-hook-form';
import Labels from '@/components/forms/Label';
import SelectWidget, { ListType } from '@/components/forms/select';
import { Input } from '@/components/ui/input';
import { OnlinePaymentDTOForm } from '@/types/order.types';
import AppButton from '@/components/common/app-button';
import { RefreshCcw } from 'lucide-react';
import { useRefreshListMutation } from '@/redux/features/online-payment/online-payment.api';

interface CustomToolbarProps {
  form: UseFormReturn<OnlinePaymentDTOForm>;
  inputValue: string;
  handleSearchChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  paymentDaysLoading: boolean;
  paymentDays: any[];
  paymentFilterFieldsLoading: boolean;
  paymentFilterFields: any[];
  operatorOptions: ListType[];
  paymentStatusLoading: boolean;
  paymentStatus: any[];
  handleChangeField: (value: string) => void;
}

const CustomToolbar = ({
  form,
  inputValue,
  handleSearchChange,
  paymentDaysLoading,
  paymentDays,
  paymentFilterFieldsLoading,
  paymentFilterFields,
  operatorOptions,
  paymentStatusLoading,
  paymentStatus,
  handleChangeField,
}: CustomToolbarProps) => {
  const [refreshList, { isLoading }] = useRefreshListMutation();

  const handleRefreshList = async () => {
    await refreshList();
  };
  return (
    <div className="w-full justify-between flex gap-2 mt-2">
      <div>
        <AppButton
          label="Refresh"
          variant="neutral"
          className="bg-brand-teal-Default mt-2 hover:bg-brand-teal-hover border-border-brand-teal-Default text-white"
          iconClassName="text-white"
          icon={RefreshCcw}
          onClick={handleRefreshList}
          isLoading={isLoading}
        />
      </div>
      <div className="w-full 2xl:w-3/4 flex gap-2 mt-2">
        <Labels label="Last Status Change" className="w-[150px]" />
        <SelectWidget
          name="laststatuschange"
          form={form}
          isClearable={false}
          optionsList={paymentDays}
          isLoading={paymentDaysLoading}
        />
        <SelectWidget
          name="field"
          form={form}
          isClearable={false}
          isLoading={paymentFilterFieldsLoading}
          onSelectChange={handleChangeField}
          optionsList={paymentFilterFields}
        />
        <SelectWidget
          name="operator"
          form={form}
          isClearable={false}
          optionsList={operatorOptions}
        />
        {form.watch('field') === 'status' ? (
          <SelectWidget
            name="statusvalue"
            form={form}
            isClearable={false}
            isLoading={paymentStatusLoading}
            optionsList={paymentStatus}
          />
        ) : (
          <Input
            value={inputValue}
            name="value"
            onChange={handleSearchChange}
            className="text-base placeholder:text-[#B3B3B3] w-[239px]"
          />
        )}
      </div>
    </div>
  );
};

export default memo(CustomToolbar);
