import AppButton from '@/components/common/app-button';
import IconButton from '@/components/common/icon-button';
import { X } from 'lucide-react';
import CheveronLeft from '@/assets/icons/CheveronLeft';

const Header = ({ navigateToOrder }: { navigateToOrder: () => void }) => (
  <div className="flex gap-x-4 items-center justify-between sticky top-16 pt-3 pb-2 bg-white z-20 mb-4">
    <div className="flex items-center gap-3">
      <IconButton onClick={navigateToOrder}>
        <CheveronLeft />
      </IconButton>
      <h1 className="text-2xl font-semibold">Online Payments</h1>
    </div>
    <AppButton
      label="Cancel"
      icon={X}
      onClick={navigateToOrder}
      iconClassName="w-4 h-4"
      variant="neutral"
    />
  </div>
);

export default Header;
