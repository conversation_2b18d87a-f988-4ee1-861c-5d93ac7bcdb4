import { render, screen } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import SubRentDialogContent from './SubRentDialogContent';
import { OrderSubRentsTypes } from '@/types/orders/order-item-details.types';

vi.mock('@/lib/utils', () => ({
  DEFAULT_FORMAT: 'MM/dd/yyyy',
  formatDate: (date: string) => `formatted_${date}`,
}));

vi.mock('@/components/common/data-tables', () => ({
  default: ({ data, columns, isLoading }: any) => (
    <div data-testid="data-table">
      <div data-testid="mock-data">{JSON.stringify(data)}</div>
      <div data-testid="mock-columns">{JSON.stringify(columns)}</div>
      <div data-testid="mock-loading">{isLoading.toString()}</div>
    </div>
  ),
}));

describe('SubRentDialogContent', () => {
  const mockData: OrderSubRentsTypes[] = [
    {
      id: 1001,
      pickupDate: '2023-05-15T08:00:00',
      returnDate: '2023-05-20T17:00:00',
      ordStatus: 'Confirmed',
      firstName: 'John',
      lastName: 'Doe',
      contactTypeValue: 1,
      contactTypeText: 'Primary',
      resvNo: 'RES-2023-1001',
      ordBy: 'Jane Smith',
      custContact: '<EMAIL> | (555) 123-4567',
      pickupReturnStatus: 'Scheduled',
      specInst1: 'Handle with care - fragile equipment',
      specInst2: 'Requires climate-controlled storage',
      customerId: 5001,
    },
    {
      id: 1002,
      pickupDate: '2023-06-01T09:30:00',
      returnDate: '2023-06-10T16:00:00',
      ordStatus: 'Completed',
      firstName: 'Sarah',
      lastName: 'Johnson',
      contactTypeValue: 2,
      contactTypeText: 'Secondary',
      resvNo: 'RES-2023-1002',
      ordBy: 'Michael Brown',
      custContact: '<EMAIL> | (555) 987-6543',
      pickupReturnStatus: 'Returned',
      specInst1: 'Needs daily maintenance check',
      specInst2: 'Include operation manual',
      customerId: 5002,
    },
    {
      id: 1003,
      pickupDate: '2023-07-10T07:00:00',
      returnDate: '2023-07-15T18:00:00',
      ordStatus: 'Pending',
      firstName: 'Robert',
      lastName: 'Chen',
      contactTypeValue: 3,
      contactTypeText: 'Tertiary',
      resvNo: 'RES-2023-1003',
      ordBy: 'Lisa Wong',
      custContact: '<EMAIL> | (*************',
      pickupReturnStatus: 'Ready for Pickup',
      specInst1: 'Special power requirements - 220V',
      specInst2: 'Needs safety training before use',
      customerId: 5003,
    },
    {
      id: 1004,
      pickupDate: '2023-08-05T10:00:00',
      returnDate: '2023-08-12T15:00:00',
      ordStatus: 'Cancelled',
      firstName: 'Emily',
      lastName: 'Williams',
      contactTypeValue: 1,
      contactTypeText: 'Primary',
      resvNo: 'RES-2023-1004',
      ordBy: 'David Wilson',
      custContact: '<EMAIL> | (*************',
      pickupReturnStatus: 'Cancelled',
      specInst1: 'Requires insurance coverage',
      specInst2: 'Needs weekly inspection',
      customerId: 5004,
    },
  ];
  it('renders the component with correct text', () => {
    render(<SubRentDialogContent data={mockData} isLoading={false} />);
    expect(
      screen.getByText('This order required the following Sub-Rentals')
    ).toBeInTheDocument();
  });

  it('passes correct data to DataTable when not loading', () => {
    render(<SubRentDialogContent data={mockData} isLoading={false} />);
    const dataElement = screen.getByTestId('mock-data');
    expect(dataElement.textContent).toBe(JSON.stringify(mockData));
    const loadingElement = screen.getByTestId('mock-loading');
    expect(loadingElement.textContent).toBe('false');
  });

  it('passes correct loading state to DataTable', () => {
    render(<SubRentDialogContent data={mockData} isLoading={true} />);
    const loadingElement = screen.getByTestId('mock-loading');
    expect(loadingElement.textContent).toBe('true');
  });

  it('passes correct columns configuration to DataTable', () => {
    render(<SubRentDialogContent data={mockData} isLoading={false} />);
    const columnsElement = screen.getByTestId('mock-columns');
    const columns = JSON.parse(columnsElement.textContent || '[]');
    expect(columns).toHaveLength(6);
    expect(columns[0]).toEqual({
      accessorKey: 'id',
      header: 'SR#',
      size: 80,
    });
  });

  it('renders with empty data', () => {
    render(<SubRentDialogContent data={[]} isLoading={false} />);
    const dataElement = screen.getByTestId('mock-data');
    expect(dataElement.textContent).toBe('[]');
  });
});
