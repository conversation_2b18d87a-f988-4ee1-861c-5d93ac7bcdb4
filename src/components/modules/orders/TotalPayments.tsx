import EditPencilIcon from '@/assets/icons/EditPencilIcon';
import AppButton from '@/components/common/app-button';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import AppSpinner from '@/components/common/app-spinner';
import BreadcrumbDialogRenderer from '@/components/common/BreadcrumbDialogRenderer';
import DataTable from '@/components/common/data-tables';
import ActionColumnMenu from '@/components/common/data-tables/ActionColumn';
import CustomDialog from '@/components/common/dialog';
import SwitchField from '@/components/common/switch';
import InputField from '@/components/forms/input-field';
import NumberInputField from '@/components/forms/number-input-field';
import SelectWidget from '@/components/forms/select';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Separator } from '@/components/ui/separator';
import { PAYMENT_BRED_CRUM_DETAILS } from '@/constants/order-constants';
import {
  cn,
  convertToFloat,
  DEFAULT_FORMAT,
  formatDate,
  generateLabelValuePairs,
  getQueryParam,
} from '@/lib/utils';
import {
  usePaymentTermsQuery,
  usePaymentTypeQuery,
} from '@/redux/features/customers/choices.api';
import {
  useGetPaymentDetailsQuery,
  useGetTaxCodeListMutation,
  useGetTotalPaymentQuery,
  useRecalCulateMutation,
  useRemovePaymentMutation,
  useTaxExemptMutation,
} from '@/redux/features/orders/order.api';
import { useGetSalesTaxCodeQuery } from '@/redux/features/sales-tax-code/sales-tax-code.api';
import {
  ExtendedPaymentInfoDTO,
  OrderInformationTypes,
  TaxBreakdown,
  TotalPaymentsTypes,
  VoidDeletePaymentFormTypes,
} from '@/types/order.types';
import { ColumnDef } from '@tanstack/react-table';
import {
  Check,
  CircleDollarSign,
  HandCoinsIcon,
  PlusIcon,
  RefreshCcw,
} from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useForm, useFormContext, UseFormReturn } from 'react-hook-form';
import { OpenDialogType } from './constants';
import EditDeliveryDailog from './total-payments/EditDeliveryDailog';
import EditDiscount from './total-payments/EditDiscount';
import ExtendedPaymentInfo from './total-payments/ExtendedPaymentInfo';
import NewPayment from './total-payments/NewPayment';
import ProcessWithCreditCard from './total-payments/ProcessWithCreditCard';
import SavedPayments from './total-payments/SavedPayments';
import { useGetUserDefaultStoreQuery } from '@/redux/features/store/store.api';
import ProcessWithRefund from './total-payments/ProcessWithRefund';

type ChargeType =
  | 'PRODUCTION_FEE'
  | 'EXPEDITED_FEE'
  | 'FUEL_SURCHARGE'
  | 'DAMAGE_WAIVER';

interface TotalPaymentsProps {
  form: UseFormReturn<TotalPaymentsTypes>;
}

export default function TotalPayments({ form }: TotalPaymentsProps) {
  const formOrder = useFormContext<OrderInformationTypes>();
  const orderId = getQueryParam('id') as string;
  const isDeleted = formOrder.watch('isDeleted');
  const isOrderEntryReadOnly = formOrder.watch('orderEntryReadOnly');

  const id = getQueryParam('id') as string;
  const [openDelivery, setOpenDelivery] = useState(false);
  const [OpenDiscount, setOpenDiscount] = useState(false);
  const [toggleSalesTaxCode, setToggleSalesTaxCode] = useState(false);
  const [message, setMessage] = useState('');
  const [modifiedData, setModifiedData] = useState({
    fuelSurcharge: 0,
    productionFee: 0,
    expeditedFee: 0,
    damageWaiver: 0,
    labour: 0,
  });
  const [orderTaxBreakDown, setTaxBreakdown] = useState<TaxBreakdown[]>([]);
  const [activeTab, setActiveTab] = useState<string>(
    PAYMENT_BRED_CRUM_DETAILS.NEW_PAYMENT
  );
  const [open, setOpen] = useState<OpenDialogType>({
    state: false,
    action: '',
    data: null,
  });

  const compedOrderList = [
    { label: 'Yes', value: 'true' },
    { label: 'No', value: 'false' },
  ];

  const onOpenChange = useCallback(() => {
    setOpen({ state: false, action: '' });
  }, []);

  const [openDeletePaymentDialog, setOpenDeletepaymentDialog] = useState({
    state: false,
    msg: '',
  });

  const [
    openDeleteVoidProcessPaymentDialog,
    setOpenDeleteVoidProcessPaymentDialog,
  ] = useState<boolean>(false);

  const [paymentToDeleteObj, setpaymentToDeleteObj] = useState<{
    paymentId: number;
    voidTransaction: boolean;
    authCode?: string;
  }>({ paymentId: 0, voidTransaction: false, authCode: '' });

  //defeine dedault values for VoidDeletePaymentFormTypes
  const defaultValues = useMemo(
    () => ({
      id: 0,
      paymentDate: '',
      type: '',
      amount: '',
      originalAccount: '',
      emailReceipt: '',
    }),
    []
  );

  const voidDeletePaymentForm = useForm<VoidDeletePaymentFormTypes>({
    defaultValues,
  });

  useEffect(() => {
    if (defaultValues) {
      voidDeletePaymentForm.reset(defaultValues);
    }
  }, [defaultValues, voidDeletePaymentForm]);

  const {
    data,
    refetch: refetchPaymentDetails,
    isFetching: isLoadingPaymentDetails,
  } = useGetPaymentDetailsQuery(
    {
      body: {
        pageNumber: 0,
        pageSize: 0,
        sortBy: 'paymentDate',
        sortAscending: false,
        filters: [{ field: 'orderId', value: orderId, operator: 'Equals' }],
      },
    },
    { skip: !orderId }
  );

  const paymentDetailsData = data?.data?.filter(
    (data) => data?.isDeleted === false
  );

  const toggleToNewPayment = useCallback(() => {
    setOpen({ state: true, action: PAYMENT_BRED_CRUM_DETAILS.NEW_PAYMENT });
    handleSetActiveTab(PAYMENT_BRED_CRUM_DETAILS.NEW_PAYMENT);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const [removePayment, { isLoading: isLoadingRemove }] =
    useRemovePaymentMutation();

  const [
    removePaymentWithoutVoid,
    { isLoading: isLoadingRemovePaymentWithoutVoid },
  ] = useRemovePaymentMutation();

  const { data: storeLocationData } = useGetUserDefaultStoreQuery(undefined, {
    // skip: Boolean(id),
    refetchOnMountOrArgChange: true,
  });

  const requestedCreditCardConvenienceFees =
    storeLocationData?.data?.storePayment?.creditCardConvenienceFees;
  const requestedCreditCardConvFees = Number(
    requestedCreditCardConvenienceFees
  );

  const requestedDebitCardConvenienceFees =
    storeLocationData?.data?.storePayment?.debitCardConvenienceFees;
  const requestedDebitCardConvFees = Number(requestedDebitCardConvenienceFees);

  const requestedACHConvenienceFees =
    storeLocationData?.data?.storePayment?.achConvenienceFees;
  const requestedACHConvFees = Number(requestedACHConvenienceFees);

  const handleSetActiveTab = useCallback((tab?: string) => {
    setActiveTab(tab ?? '');
  }, []);

  const salesTaxCodeId = parseFloat(form.getValues('salesTaxCodeId')) || 0;

  const grandTotal = form.getValues('grandTotal') || 0;
  const subTotal = +form.getValues('subTotal') || 0;
  const balanceDueAmount = parseFloat(form.getValues('balanceDue')) || 0;

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const dataCalculation = { grandTotal, subTotal, balanceDueAmount }; // Add correct dependencies here

  // tab list
  const listItems = useMemo(() => {
    const tabList = [
      {
        label: 'New Payment',
        value: PAYMENT_BRED_CRUM_DETAILS.NEW_PAYMENT,
        content: (
          <NewPayment
            setOpen={setOpen}
            handleSetActiveTab={handleSetActiveTab}
            salesTaxCodeId={salesTaxCodeId}
            dataCalculation={dataCalculation}
            totalPaymentsForm={form}
            storeLocationData={storeLocationData}
          />
        ),
      },
      {
        label: 'Saved Payments',
        value: PAYMENT_BRED_CRUM_DETAILS.SAVED_PAYMENTS,
        content: <SavedPayments setOpen={setOpen} />,
      },
      {
        label: 'Extended Payment Info',
        value: PAYMENT_BRED_CRUM_DETAILS.EXTENDED_PAYMENT_INFO,
        content: <ExtendedPaymentInfo setOpen={setOpen} />,
      },
      {
        label: 'Process With Credit Card/ACH',
        value: PAYMENT_BRED_CRUM_DETAILS.PROCESS_WITH_CREDIT_CARD,
        content: (
          <ProcessWithCreditCard
            setOpen={setOpen}
            toggleToNewPayment={toggleToNewPayment}
            data={open.data}
            refetchPaymentDetails={refetchPaymentDetails}
          />
        ),
      },
      {
        label: 'Process Refund',
        value: PAYMENT_BRED_CRUM_DETAILS.PROCESS_WITH_REFUND,
        content: (
          <ProcessWithRefund
            setOpen={setOpen}
            toggleToNewPayment={toggleToNewPayment}
            data={open.data}
          />
        ),
      },
    ];
    return tabList?.filter((tab) => {
      return [activeTab]?.includes(tab?.value);
    });
  }, [
    activeTab,
    dataCalculation,
    form,
    handleSetActiveTab,
    open.data,
    refetchPaymentDetails,
    salesTaxCodeId,
    storeLocationData,
    toggleToNewPayment,
  ]);

  const {
    data: totalPaymentsData,
    isFetching,
    refetch,
  } = useGetTotalPaymentQuery(id, {
    skip: !id,
    refetchOnMountOrArgChange: true,
  });

  const [getRecaluclateData, { isLoading: isLoadingRecalculate }] =
    useRecalCulateMutation();

  const [getTaxCodeList, { isLoading: isTaxCodeLoading }] =
    useGetTaxCodeListMutation();

  const [getTaxExempt, { isLoading: taxExemptIsLoading }] =
    useTaxExemptMutation();

  const { data: salesTaxCodeData, isLoading: salesTaxCodLoading } =
    useGetSalesTaxCodeQuery(undefined, {
      refetchOnMountOrArgChange: true,
    });
  const { data: paymentTypeData, isLoading: paymentTypeLoading } =
    usePaymentTypeQuery();

  const { data: paymentTermsData, isLoading: paymentTermLoading } =
    usePaymentTermsQuery();

  const paymentTypeList = paymentTypeData?.data?.map((item: any) => {
    const key = Object.keys(item)[0];
    const value = item[key];
    return {
      label: key,
      value: value,
    };
  });

  const paymentTermList = paymentTermsData?.data?.map((item: any) => {
    const key = Object.keys(item)[0];
    const value = item[key];
    return {
      label: key,
      value: value,
    };
  });

  const salesTaxCodeList = generateLabelValuePairs({
    data: salesTaxCodeData?.data,
    labelKey: 'salestaxcode',
    valueKey: 'salestaxcode_id',
  });

  const paymentData = totalPaymentsData?.data;
  const isDwEdited = form.watch('isDwEdited');
  const isProdFeeEdited = form.watch('isProdFeeEdited');
  const isFuelChargeEdited = form.watch('isFuelChargeEdited');
  const isExpeditedFeeEdited = form.watch('isExpeditedFeeEdited');

  useEffect(() => {
    const editedFees = [];
    if (isProdFeeEdited) editedFees.push('Production fee');
    if (isDwEdited) editedFees.push('Damage Waiver');
    if (isFuelChargeEdited) editedFees.push('Fuel Surcharge');
    if (isExpeditedFeeEdited) editedFees.push('Expedited fee');
    setMessage(
      editedFees.length > 0
        ? `${editedFees.join(', ')} have been manually edited and will not automatically recalculate.`
        : ''
    );
  }, [isDwEdited, isExpeditedFeeEdited, isFuelChargeEdited, isProdFeeEdited]);

  useEffect(() => {
    if (!paymentData) return;

    const fieldMap = {
      total: paymentData?.total,
      amountSubjectToDiscount: paymentData?.amountSubjectToDiscount,
      discount: paymentData?.discount,
      subTotal: paymentData?.subTotal,
      deliveryCharge: paymentData?.deliveryCharge,
      labour: paymentData?.labour,
      damageWaiver: paymentData?.damageWaiver,
      fuelSurcharge: paymentData?.fuelSurcharge,
      productionFee: paymentData?.productionFee,
      expeditedFee: paymentData?.expeditedFee,
      ccConvenienceFee: paymentData?.ccConvenienceFee,
      tax: paymentData?.tax,
      grandTotal: paymentData?.grandTotal,
      dwPercent: paymentData?.dwPercent,
      requiredDeposit: paymentData?.requiredDeposit,
      isTaxExempted: paymentData?.isTaxExempted,
      chargeConvFee: paymentData?.chargeConvFee,
      paymentType: paymentData?.paymentType,
      compedOrder: paymentData?.compedOrder,
      'taxCode.code': paymentData?.code,
      'taxCode.rate': paymentData?.rate,
      dwCalculation: paymentData?.dwCalculation,
      orderTaxBreakDown: paymentData?.orderTaxBreakDown,
      balanceDue: paymentData?.balanceDue,
      amountPaid: paymentData?.amountPaid,
      writeOffDiscount: paymentData?.writeOffDiscount,
      isDwEdited: paymentData?.isDwEdited,
      isProdFeeEdited: paymentData?.isProdFeeEdited,
      isExpeditedFeeEdited: paymentData?.isExpeditedFeeEdited,
      isFuelChargeEdited: paymentData?.isFuelChargeEdited,
      paymentTerms: paymentData?.paymentTerms,
      salesTaxCodeId: paymentData?.salesTaxCodeId,
    };

    Object.entries(fieldMap).forEach(([field, value]) => {
      form.setValue(field as any, value);
    });

    setTaxBreakdown(paymentData?.orderTaxBreakDown);

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [paymentData]);

  const toggleEditDelivery = useCallback(() => {
    setOpenDelivery((prev) => !prev);
  }, []);

  const toggleEditDiscount = useCallback(() => {
    setOpenDiscount((prev) => !prev);

    //call refetch only if modal is closed
    if (OpenDiscount) {
      refetch();
    }
  }, [OpenDiscount, refetch]);

  const onOpenChangePaymentDeleteDialog = useCallback(() => {
    setOpenDeletepaymentDialog({ state: false, msg: '' });
  }, [setOpenDeletepaymentDialog]);

  const onOpenChangeVoidProcessPaymentDeleteDialog = useCallback(() => {
    setOpenDeleteVoidProcessPaymentDialog((prevState) => !prevState);
  }, [setOpenDeleteVoidProcessPaymentDialog]);

  const handleDeleteClick = useCallback(
    (paymentObj: {
      deleteData: any;
      paymentId: number;
      voidTransaction: boolean;
      isOnlinePayment: boolean;
      transactionOperation: string;
    }) => {
      setpaymentToDeleteObj({
        paymentId: paymentObj.paymentId ?? 0,
        voidTransaction: false,
      });

      if (
        paymentObj?.isOnlinePayment &&
        paymentObj?.transactionOperation === 'PAYMENT'
      ) {
        voidDeletePaymentForm.setValue(
          'date',
          formatDate(paymentObj.deleteData?.paymentDate, DEFAULT_FORMAT)
        );
        voidDeletePaymentForm.setValue('type', paymentObj.deleteData?.cardType);
        voidDeletePaymentForm.setValue('amount', paymentObj.deleteData?.amount);
        voidDeletePaymentForm.setValue(
          'originalAccount',
          paymentObj.deleteData?.cardNumber
        );
        voidDeletePaymentForm.setValue(
          'emailReceipt',
          paymentObj.deleteData?.email
        );
        onOpenChangeVoidProcessPaymentDeleteDialog();
      } else if (
        paymentObj?.isOnlinePayment &&
        paymentObj?.transactionOperation === 'REFUND'
      ) {
        setOpenDeletepaymentDialog({
          state: true,
          msg: 'Deleting this negative payment will not make any changes in Gravity. Do you want to delete this negative payment?',
        });
      } else {
        setOpenDeletepaymentDialog({
          state: true,
          msg: 'Are you sure you want to delete this payment?',
        });
      }
    },
    [onOpenChangeVoidProcessPaymentDeleteDialog, voidDeletePaymentForm]
  );

  const handleDeletePaymentClick = async () => {
    try {
      const payload = {
        paymentId: paymentToDeleteObj.paymentId ?? 0,
        voidTransaction: false,
      };
      await removePayment(payload).unwrap();
      onOpenChangePaymentDeleteDialog();
    } catch (error) {}
  };

  const handleDeleteProcessClick = async () => {
    const formData = voidDeletePaymentForm.getValues();
    try {
      const payload = {
        ...formData,
        paymentId: paymentToDeleteObj.paymentId ?? 0,
        voidTransaction: true,
      };
      await removePayment(payload).unwrap();
      onOpenChangeVoidProcessPaymentDeleteDialog();
    } catch (error) {}
  };

  const handleDeletePaymentWithoutVoid = async () => {
    const formData = voidDeletePaymentForm.getValues();
    try {
      const payload = {
        ...formData,
        paymentId: paymentToDeleteObj.paymentId ?? 0,
        voidTransaction: false,
      };
      await removePaymentWithoutVoid(payload).unwrap();
      onOpenChangeVoidProcessPaymentDeleteDialog();
    } catch (error) {}
  };

  const handleTaxExemptedChange = async (value: boolean) => {
    await getTaxExempt({
      orderId: id,
      toExempt: value,
    })
      .unwrap()
      .then((data) => {
        if (data.data) {
          const responseData: any = data?.data;
          setTaxBreakdown(responseData.taxBreakDown);
          form.setValue('orderTaxBreakDown', responseData.taxBreakDown);
          form.setValue('grandTotal', responseData.grandTotal);
          form.setValue('tax', responseData.tax);
        }
      })
      .catch((_error) => {
        form.setValue('isTaxExempted', !value);
      });
  };

  const handleLabourCalculation = (event: any) => {
    const labourInput = event?.target?.value || '';
    const labourValue = parseFloat(labourInput.replace(/[^0-9.-]+/g, '')) || 0;
    let subTotal = parseFloat(form.getValues('subTotal')) || 0;
    let damageWaiver = parseFloat(form.getValues('damageWaiver')) || 0;
    let deliveryCharge = parseFloat(form.getValues('deliveryCharge')) || 0;
    let productionFee = parseFloat(form.getValues('productionFee')) || 0;
    let expeditedFee = parseFloat(form.getValues('expeditedFee')) || 0;
    let ccConvenienceFee = parseFloat(form.getValues('ccConvenienceFee')) || 0;
    let fuelSurcharge = parseFloat(form.getValues('fuelSurcharge')) || 0;
    if (labourValue) {
      let labour = labourValue || 0;
      let summation = subTotal + labour + damageWaiver;
      const updatedTaxBreakdown = orderTaxBreakDown?.map((taxItem) => {
        const taxAmount = (summation * taxItem?.baseRate) / 100;
        return { ...taxItem, amount: taxAmount };
      });
      let totalTax = updatedTaxBreakdown?.reduce(
        (acc, taxItem) => acc + taxItem?.amount,
        0
      );
      let grandTotal =
        summation +
        totalTax +
        fuelSurcharge +
        deliveryCharge +
        productionFee +
        expeditedFee +
        ccConvenienceFee;

      if (totalTax) form.setValue('tax', totalTax);
      if (grandTotal) form.setValue('grandTotal', grandTotal);
      setTaxBreakdown(updatedTaxBreakdown);
      setModifiedData({
        ...modifiedData,
        labour: labourValue,
      });
    } else {
      form.setValue('labour', '0');
      let subTotal = parseFloat(form.getValues('subTotal')) || 0;
      let damageWaiver = parseFloat(form.getValues('damageWaiver')) || 0;
      // Exclude labour from summation
      let summation = subTotal + damageWaiver;
      // Recalculate tax
      const updatedTaxBreakdown = orderTaxBreakDown?.map((taxItem) => {
        const taxAmount = (summation * taxItem?.baseRate) / 100;
        return { ...taxItem, amount: taxAmount };
      });
      let totalTax = updatedTaxBreakdown?.reduce(
        (acc, taxItem) => acc + taxItem?.amount,
        0
      );
      let grandTotal =
        summation +
        totalTax +
        fuelSurcharge +
        deliveryCharge +
        productionFee +
        expeditedFee +
        ccConvenienceFee;
      form.setValue('tax', totalTax);
      form.setValue('grandTotal', grandTotal);
      setTaxBreakdown(updatedTaxBreakdown);
      setModifiedData({
        ...modifiedData,
        labour: 0,
      });
    }
  };

  const handleDamageWaiverCalculation = (event: any) => {
    const labourInput = event?.target?.value || '';
    const damageWaiverValue =
      parseFloat(labourInput.replace(/[^0-9.-]+/g, '')) || 0;
    let subTotal = parseFloat(form.getValues('subTotal')) || 0;
    let labour = parseFloat(form.getValues('labour')) || 0;
    let deliveryCharge = parseFloat(form.getValues('deliveryCharge')) || 0;
    let productionFee = parseFloat(form.getValues('productionFee')) || 0;
    let expeditedFee = parseFloat(form.getValues('expeditedFee')) || 0;
    let ccConvenienceFee = parseFloat(form.getValues('ccConvenienceFee')) || 0;
    let fuelSurcharge = parseFloat(form.getValues('fuelSurcharge')) || 0;
    if (damageWaiverValue) {
      let damageWaiver = damageWaiverValue || 0;
      let summation = subTotal + labour + damageWaiver;
      const updatedTaxBreakdown = orderTaxBreakDown?.map((taxItem) => {
        const taxAmount = (summation * taxItem.baseRate) / 100;
        return { ...taxItem, amount: taxAmount };
      });
      let totalTax = updatedTaxBreakdown?.reduce(
        (acc, taxItem) => acc + taxItem.amount,
        0
      );

      let grandTotal =
        summation +
        totalTax +
        fuelSurcharge +
        deliveryCharge +
        productionFee +
        expeditedFee +
        ccConvenienceFee;

      if (totalTax) form.setValue('tax', totalTax);
      if (grandTotal) form.setValue('grandTotal', grandTotal);
      setTaxBreakdown(updatedTaxBreakdown);
      if (paymentData?.damageWaiver !== damageWaiverValue) {
        form.setValue('isDwEdited', true);
      } else {
        form.setValue('isDwEdited', false);
      }
      setModifiedData({
        ...modifiedData,
        damageWaiver: damageWaiverValue,
      });
    } else {
      // let previousDamageWaiver = modifiedData.damageWaiver || 0;
      let subTotal = parseFloat(form.getValues('subTotal')) || 0;
      let labour = parseFloat(form.getValues('labour')) || 0;
      form.setValue('damageWaiver', '0');
      // Subtract damage waiver
      let summation = subTotal + labour;
      // Recalculate tax based on the updated summation
      const updatedTaxBreakdown = orderTaxBreakDown?.map((taxItem) => {
        const taxAmount = (summation * taxItem.baseRate) / 100;
        return { ...taxItem, amount: taxAmount };
      });

      let totalTax = updatedTaxBreakdown?.reduce(
        (acc, taxItem) => acc + taxItem.amount,
        0
      );

      let grandTotal =
        summation +
        totalTax +
        fuelSurcharge +
        deliveryCharge +
        productionFee +
        expeditedFee +
        ccConvenienceFee;

      form.setValue('tax', totalTax);
      form.setValue('grandTotal', grandTotal);
      setTaxBreakdown(updatedTaxBreakdown);
      if (paymentData?.damageWaiver !== damageWaiverValue) {
        form.setValue('isDwEdited', true);
      } else {
        form.setValue('isDwEdited', false);
      }
      setModifiedData({
        ...modifiedData,
        damageWaiver: 0,
      });
    }
  };

  const recalculateCharge = async (orderId: string, chargeType: ChargeType) => {
    try {
      const response = await getRecaluclateData({
        orderId,
        chargeType,
      }).unwrap();

      if (response?.success && response?.data) {
        const chargeMapping: Record<ChargeType, string> = {
          PRODUCTION_FEE: 'productionFee',
          EXPEDITED_FEE: 'expeditedFee',
          FUEL_SURCHARGE: 'fuelSurcharge',
          DAMAGE_WAIVER: 'damageWaiver',
        };
        type ChargeField =
          | 'productionFee'
          | 'fuelSurcharge'
          | 'expeditedFee'
          | 'damageWaiver';

        const chargeKey = chargeMapping[chargeType];
        const currentChargeValue = Number(
          form.getValues(chargeKey as ChargeField) || 0
        );
        const newValue = Number(response?.data[chargeKey] || 0);

        if (chargeKey && newValue !== null) {
          // Step 1: Get current values from form
          const currentGrandTotal: Number = form.getValues('grandTotal') || 0;

          // Step 2: Calculate updated grand total
          if (currentChargeValue) {
            const updatedGrandTotal =
              Number(currentGrandTotal) - currentChargeValue + newValue;

            // Step 3: Update both fields
            form.setValue(chargeKey as ChargeField, newValue.toString());
            if (chargeKey === 'damageWaiver') {
              form.setValue('dwCalculation', response?.data['dwCalculation']);
              form.setValue('dwPercent', response?.data['dwPercent']);
            }

            setModifiedData({
              ...modifiedData,
              [chargeKey]: newValue,
            });
            const editedFieldMapping: Record<string, string> = {
              productionFee: 'isProdFeeEdited',
              expeditedFee: 'isExpeditedFeeEdited',
              fuelSurcharge: 'isFuelChargeEdited',
              damageWaiver: 'isDwEdited',
            };

            const editedFieldKey = editedFieldMapping[chargeKey];
            if (editedFieldKey) {
              form.setValue(editedFieldKey as any, false);
            }
            form.setValue('grandTotal', updatedGrandTotal);
          } else if (chargeKey === 'damageWaiver') {
            // console.log('called');
            const updatedGrandTotal =
              Number(currentGrandTotal) - currentChargeValue + newValue;
            form.setValue(chargeKey as ChargeField, newValue.toString());
            if (chargeKey === 'damageWaiver') {
              form.setValue('dwCalculation', response?.data['dwCalculation']);
              form.setValue('dwPercent', response?.data['dwPercent']);
            }

            setModifiedData({
              ...modifiedData,
              [chargeKey]: newValue,
            });
            const editedFieldMapping: Record<string, string> = {
              productionFee: 'isProdFeeEdited',
              expeditedFee: 'isExpeditedFeeEdited',
              fuelSurcharge: 'isFuelChargeEdited',
              damageWaiver: 'isDwEdited',
            };

            const editedFieldKey = editedFieldMapping[chargeKey];
            if (editedFieldKey) {
              form.setValue(editedFieldKey as any, false);
            }
            form.setValue('grandTotal', updatedGrandTotal);
          }
        }
      } else {
        // console.warn('Charge recalculation failed:', response?.message);
      }
    } catch (error) {
      // console.error('Error recalculating charge:', error?.message || error);
    }
  };

  const handleSaleTaxCode = (value: string) => {
    const labour = form.getValues('labour');
    const damageWaiver = form.getValues('damageWaiver');
    const isTaxExempted = form.getValues('isTaxExempted');
    if (value) {
      getTaxCodeList({
        orderId: id,
        labour: labour || 0.0,
        damageWaiver: damageWaiver || 0.0,
        salesTaxCodeId: value,
        isTaxExempted,
      })
        .unwrap()
        .then((response) => {
          const data = response?.data;

          if (data) {
            form.setValue('labour', data?.labor || 0);
            form.setValue('damageWaiver', data?.damageWaiver || 0);
            form.setValue('tax', data?.tax || 0);
            form.setValue('grandTotal', data?.grandTotal || 0);
            form.setValue('balanceDue', data?.balanceDue || 0);
            form.setValue('taxCode.rate', data?.rate || 0);
            form.setValue('taxCode.code', data?.code);
            // Optional: Update local state if needed
            setTaxBreakdown(data?.taxBreakDown || []);

            setModifiedData((prev) => ({
              ...prev,
              labour: data?.labor || 0,
              damageWaiver: data?.damageWaiver || 0,
            }));
          }
        })
        .catch((_err) => {
          // console.error('Error fetching tax code data:', err);
        });
    }
  };

  const handleSurchargeBlur = (event: any) => {
    const labourInput = event?.target?.value || '';
    const inputSurcharge =
      parseFloat(labourInput.replace(/[^0-9.-]+/g, '')) || 0;
    const currentGrandTotal = Number(form.getValues('grandTotal')) || 0;
    const prevModifiedSurcharge = modifiedData.fuelSurcharge || 0;
    const originalSurcharge = paymentData?.fuelSurcharge || 0;
    if (!inputSurcharge) {
      form.setValue('fuelSurcharge', '0.00');
    }
    // New value to use for modified fuelSurcharge
    const newModifiedSurcharge = inputSurcharge - originalSurcharge;
    // Calculate new grand total
    const updatedGrandTotal =
      currentGrandTotal - prevModifiedSurcharge + newModifiedSurcharge;
    // Update form and local state
    form.setValue('grandTotal', updatedGrandTotal);
    setModifiedData({
      ...modifiedData,
      fuelSurcharge: newModifiedSurcharge,
    });
    if (paymentData?.fuelSurcharge !== inputSurcharge) {
      form.setValue('isFuelChargeEdited', true);
    } else {
      form.setValue('isFuelChargeEdited', false);
    }
  };

  const handleExpeditedFee = (event: any) => {
    const labourInput = event?.target?.value || '';
    const inputExpeditedFee =
      parseFloat(labourInput.replace(/[^0-9.-]+/g, '')) || 0;

    const currentGrandTotal = Number(form.getValues('grandTotal')) || 0;
    const prevModifiedExpeditedFee = modifiedData.expeditedFee || 0;
    const originalExpeditedFee = paymentData?.expeditedFee || 0;
    if (!inputExpeditedFee) {
      form.setValue('expeditedFee', '0.00');
    }

    // Calculate the new modified fee (difference from original)
    const newModifiedExpeditedFee = inputExpeditedFee - originalExpeditedFee;

    // Update grand total
    const updatedGrandTotal =
      currentGrandTotal - prevModifiedExpeditedFee + newModifiedExpeditedFee;
    form.setValue('grandTotal', updatedGrandTotal);

    // Update local state
    setModifiedData({
      ...modifiedData,
      expeditedFee: newModifiedExpeditedFee,
    });
    if (paymentData?.expeditedFee !== inputExpeditedFee) {
      form.setValue('isExpeditedFeeEdited', true);
    } else {
      form.setValue('isExpeditedFeeEdited', false);
    }
  };

  const handleProductionFee = (event: any) => {
    const labourInput = event?.target?.value || '';
    const inputProductionFee =
      parseFloat(labourInput.replace(/[^0-9.-]+/g, '')) || 0;
    const currentGrandTotal = Number(form.getValues('grandTotal')) || 0;
    const prevModifiedProductionFee = modifiedData.productionFee || 0;
    const originalProductionFee = paymentData?.productionFee || 0;
    if (!inputProductionFee) {
      form.setValue('productionFee', '0.00');
    }

    // Calculate the new modified value (difference from original)
    const newModifiedProductionFee = inputProductionFee - originalProductionFee;
    // Update grand total
    const updatedGrandTotal =
      currentGrandTotal - prevModifiedProductionFee + newModifiedProductionFee;
    form.setValue('grandTotal', updatedGrandTotal);
    // Update local modifiedData state
    setModifiedData({
      ...modifiedData,
      productionFee: newModifiedProductionFee,
    });
    if (paymentData?.productionFee !== inputProductionFee) {
      form.setValue('isProdFeeEdited', true);
    } else {
      form.setValue('isProdFeeEdited', false);
    }
  };

  // Memoized column definitions for the contact table
  const columns: ColumnDef<TotalPaymentsTypes>[] = [
    {
      accessorKey: 'rateCode',
      header: 'Rate Code',
    },
    {
      accessorKey: 'state',
      header: 'State',
      size: 130,
    },
    {
      accessorKey: 'baseRate',
      header: 'Rate',
      cell: ({ row }: any) =>
        convertToFloat({ value: row?.original?.baseRate, postfix: '%' }),
      size: 130,
    },
    {
      accessorKey: 'amount',
      header: 'Amount',
      cell: ({ row }: any) =>
        convertToFloat({ value: row?.original?.amount, prefix: '$' }),
      size: 100,
    },
  ];

  const columnsPayments: ColumnDef<ExtendedPaymentInfoDTO>[] = [
    {
      accessorKey: 'paymentDate',
      header: 'Date',
      cell: ({ row }: any) => {
        return formatDate(row?.original?.paymentDate, DEFAULT_FORMAT);
      },
      size: 100,
    },
    {
      accessorKey: 'cardType',
      header: 'Type',
      cell: ({ row }: any) => {
        return row?.original?.cardType == null
          ? row?.original?.paymentMethodName
          : row?.original?.cardType;
      },
      size: 150,
    },
    {
      accessorKey: 'amount',
      header: 'Amount',
      cell: ({ row }: any) =>
        convertToFloat({ value: row?.original?.amount, prefix: '$' }),
      size: 150,
    },
    {
      accessorKey: 'discount',
      header: 'Discount',
      cell: ({ row }: any) =>
        convertToFloat({ value: row?.original?.discount, prefix: '$' }),
      size: 120,
    },
    {
      accessorKey: 'reference',
      header: 'Reference',
    },
    {
      accessorKey: 'cardNumber',
      header: 'Credit Card/ACH #',
      size: 200,
      cell: ({ row }: any) => {
        const cardNumber = String(row?.original?.cardNumber ?? '');
        return (
          <span className="tracking-wide text-base font-sans">
            {cardNumber.includes('*')
              ? cardNumber.replace(/\*/g, '\u002A')
              : ''}
          </span>
        );
      },
    },
    {
      id: 'action',
      size: 80,
      header: 'Actions',
      cell: ({ row }) => {
        return (
          <ActionColumnMenu
            onDelete={() =>
              handleDeleteClick({
                deleteData: row?.original,
                paymentId: row?.original?.id ?? 0,
                voidTransaction: false,
                isOnlinePayment: row?.original?.isOnlinePayment ?? false,
                transactionOperation: row?.original?.transactionOperation ?? '',
              })
            }
          />
        );
      },
    },
  ];

  return (
    <>
      <div className="flex items-center justify-between gap-3 pb-8">
        <h3 className="text-xl font-semibold text-text-Default min-w-40">
          Total / Payments
        </h3>
        <div className="flex flex-wrap items-center gap-2 justify-end">
          <AppButton
            label={'New Payment'}
            icon={PlusIcon}
            spinnerClass={'border-white-500  border-t-transparent animate-spin'}
            className="bg-brand-teal-Default hover:bg-brand-teal-hover border-border-brand-teal-Default mx-1"
            onClick={() => {
              setOpen({
                state: true,
                action: PAYMENT_BRED_CRUM_DETAILS.NEW_PAYMENT,
              });
              handleSetActiveTab(PAYMENT_BRED_CRUM_DETAILS.NEW_PAYMENT);
            }}
            disabled={isDeleted}
          />
          <AppButton
            label={'Saved Payments'}
            icon={CircleDollarSign}
            spinnerClass={'border-white-500  border-t-transparent animate-spin'}
            className="bg-white border-[1px] hover:bg-white text-text-Default border-border-Default mx-1"
            iconClassName="w-5"
            onClick={() => {
              setOpen({
                state: true,
                action: PAYMENT_BRED_CRUM_DETAILS.SAVED_PAYMENTS,
              });
              handleSetActiveTab(PAYMENT_BRED_CRUM_DETAILS.SAVED_PAYMENTS);
            }}
            disabled={isDeleted}
          />
          <AppButton
            label={'Extended Payment Info'}
            icon={HandCoinsIcon}
            spinnerClass={'border-white-500  border-t-transparent animate-spin'}
            className="bg-white border-[1px] hover:bg-white text-text-Default border-border-Default mx-1"
            iconClassName="w-5"
            onClick={() => {
              setOpen({
                state: true,
                action: PAYMENT_BRED_CRUM_DETAILS.EXTENDED_PAYMENT_INFO,
              });
              handleSetActiveTab(
                PAYMENT_BRED_CRUM_DETAILS.EXTENDED_PAYMENT_INFO
              );
            }}
          />
          <ActionColumnMenu
            triggerClassName="border h-10"
            contentClassName="w-full"
            dropdownMenuList={[
              {
                label: 'No Data Found!',
                onClick: () => {},
              },
            ]}
          />
        </div>
      </div>
      <div className="flex gap-6">
        <div className="grid grid-cols-1 md:grid-cols-1 gap-6 w-1/3 h-full">
          <NumberInputField
            name="total"
            form={form}
            label="Total"
            fixedDecimalScale
            placeholder="$______.__"
            prefix="$"
            disabled
          />
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="col-span-4 flex gap-x-5">
              <NumberInputField
                name="discount"
                form={form}
                label="Discount"
                fixedDecimalScale
                placeholder="$______.__"
                prefix="$"
                disabled
              />
              <div className="w-fit">
                <AppButton
                  label={'Edit'}
                  onClick={toggleEditDiscount}
                  icon={EditPencilIcon}
                  spinnerClass={
                    'border-white-500  border-t-transparent animate-spin'
                  }
                  className="bg-white border-[1px] hover:bg-white text-text-Default border-border-Default mt-8"
                  iconClassName="w-5"
                />
              </div>
            </div>
          </div>
          <NumberInputField
            name="subTotal"
            form={form}
            label="Sub-Total"
            fixedDecimalScale
            placeholder="$______.__"
            prefix="$"
            disabled
          />
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="col-span-4 flex gap-x-5">
              <NumberInputField
                name="deliveryCharge"
                form={form}
                label="Delivery"
                fixedDecimalScale
                placeholder="$______.__"
                prefix="$"
                disabled
              />
              <div className="w-fit">
                <AppButton
                  label={'Edit'}
                  onClick={toggleEditDelivery}
                  icon={EditPencilIcon}
                  spinnerClass={
                    'border-white-500  border-t-transparent animate-spin'
                  }
                  className="bg-white border-[1px] hover:bg-white text-text-Default border-border-Default mt-8"
                  iconClassName="w-5"
                />
              </div>
            </div>
          </div>
          <NumberInputField
            name="labour"
            form={form}
            label="Labor"
            fixedDecimalScale
            placeholder="$______.__"
            prefix="$"
            onBlur={handleLabourCalculation}
            disabled={isDeleted || isOrderEntryReadOnly}
          />
          <NumberInputField
            name="damageWaiver"
            form={form}
            label="Damage Waiver"
            placeholder="$______.__"
            fixedDecimalScale
            prefix="$"
            onBlur={handleDamageWaiverCalculation}
            disabled={isDeleted || isOrderEntryReadOnly}
          />
          <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
            <div className="col-span-5 flex gap-x-5">
              <NumberInputField
                name="fuelSurcharge"
                form={form}
                label="Fuel Surcharge"
                placeholder="$______.__"
                prefix="$"
                fixedDecimalScale
                onBlur={handleSurchargeBlur}
                disabled={isDeleted || isOrderEntryReadOnly}
              />
              <div className="w-fit">
                <AppButton
                  label={''}
                  icon={RefreshCcw}
                  spinnerClass={
                    'border-white-500  border-t-transparent animate-spin'
                  }
                  onClick={() => {
                    recalculateCharge(id, 'FUEL_SURCHARGE');
                  }}
                  className="bg-white border-[1px] hover:bg-white text-text-Default border-border-Default mt-8"
                  iconClassName="w-5"
                  disabled={isDeleted || isOrderEntryReadOnly}
                />
              </div>
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
            <div className="col-span-5 flex gap-x-5">
              <NumberInputField
                name="productionFee"
                form={form}
                fixedDecimalScale
                label="Production Fee"
                placeholder="$______.__"
                onBlur={handleProductionFee}
                prefix="$"
                disabled={isDeleted || isOrderEntryReadOnly}
              />
              <div className="w-fit">
                <AppButton
                  label={''}
                  icon={RefreshCcw}
                  spinnerClass={
                    'border-white-500  border-t-transparent animate-spin'
                  }
                  onClick={() => {
                    recalculateCharge(id, 'PRODUCTION_FEE');
                  }}
                  className="bg-white border-[1px] hover:bg-white text-text-Default border-border-Default mt-8"
                  iconClassName="w-5"
                  disabled={isDeleted || isOrderEntryReadOnly}
                />
              </div>
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
            <div className="col-span-5 flex gap-x-5">
              <NumberInputField
                name="expeditedFee"
                form={form}
                fixedDecimalScale
                label="Expedited Fee"
                placeholder="$______.__"
                prefix="$"
                onBlur={handleExpeditedFee}
                disabled={isDeleted || isOrderEntryReadOnly}
              />
              <div className="w-fit">
                <AppButton
                  label={''}
                  icon={RefreshCcw}
                  spinnerClass={
                    'border-white-500  border-t-transparent animate-spin'
                  }
                  onClick={() => {
                    recalculateCharge(id, 'EXPEDITED_FEE');
                  }}
                  className="bg-white border-[1px] hover:bg-white text-text-Default border-border-Default mt-8"
                  iconClassName="w-5"
                  disabled={isDeleted || isOrderEntryReadOnly}
                />
              </div>
            </div>
          </div>
          <NumberInputField
            name="ccConvenienceFee"
            form={form}
            fixedDecimalScale
            label="C.C. Convenience Fee"
            placeholder="$______.__"
            prefix="$"
            allowNegative
            disabled
          />
          <NumberInputField
            name="tax"
            form={form}
            label="Tax"
            fixedDecimalScale
            placeholder="$______.__"
            prefix="$"
            allowNegative
            disabled
          />
          <NumberInputField
            name="grandTotal"
            form={form}
            label="Grand Total"
            fixedDecimalScale
            placeholder="$______.__"
            prefix="$"
            allowNegative
            disabled
          />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-1 gap-6 w-2/3 h-full">
          <div className="grid grid-cols-1 md:grid-cols-6 gap-6">
            <div className="col-span-3 flex justify-between gap-x-5">
              {toggleSalesTaxCode ? (
                <SelectWidget
                  form={form}
                  optionsList={salesTaxCodeList}
                  name="salesTaxCodeId"
                  label="Tax Code"
                  isLoading={salesTaxCodLoading}
                  placeholder="Tax Code"
                  onSelectChange={(value) => handleSaleTaxCode(value)}
                  isClearable={false}
                />
              ) : (
                <InputField
                  name="taxCode.code"
                  form={form}
                  label="Tax Code"
                  // placeholder="$______.__"
                  disabled
                  pClassName="w-full"
                />
              )}
              <div className="w-fit">
                <AppButton
                  label={'Change'}
                  icon={RefreshCcw}
                  onClick={() => {
                    setToggleSalesTaxCode((prev) => !prev);
                  }}
                  isLoading={isTaxCodeLoading}
                  spinnerClass={
                    'border-white-500  border-t-transparent animate-spin'
                  }
                  className="bg-white border-[1px] hover:bg-white text-text-Default border-border-Default mt-8"
                  iconClassName="w-4"
                  disabled={isDeleted || isOrderEntryReadOnly}
                />
              </div>
            </div>

            <div className="col-span-3">
              <NumberInputField
                name="taxCode.rate"
                form={form}
                label="Tax Rate"
                placeholder="______.__"
                fixedDecimalScale
                suffix="%"
                disabled
              />
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="mt-4">
              <SwitchField
                label={'Tax-Exempt'}
                form={form}
                name={'isTaxExempted'}
                onChange={handleTaxExemptedChange}
                disabled={isDeleted || isOrderEntryReadOnly}
              />
            </div>
            {(requestedCreditCardConvFees > 0 ||
              requestedDebitCardConvFees > 0 ||
              requestedACHConvFees > 0) && (
              <div className="mt-4">
                <SwitchField
                  label="Charge Convenience Fee"
                  form={form}
                  name="chargeConvFee"
                  disabled={isDeleted || isOrderEntryReadOnly}
                />
              </div>
            )}
            {/* ))} */}
          </div>
          <Accordion type="single" className="flex flex-col space-y-4">
            <Accordion
              className="AccordionRoot"
              type="single"
              defaultValue="item-1"
              collapsible
            >
              <AccordionItem value="item-1" className="border-0">
                <AccordionTrigger className="bg-grayScale-10 rounded-md px-3 hover:no-underline">
                  Tax Breakdown
                </AccordionTrigger>
                <AccordionContent className="grid grid-cols-1 gap-6 py-4 px-1">
                  <DataTable
                    data={(orderTaxBreakDown as any) || []}
                    columns={[...columns]} // Adding the Action column
                    noDataPlaceholder="No Data Found."
                    isLoading={taxExemptIsLoading}
                    loaderRows={2}
                    enableSearch={false}
                    enablePagination={false}
                  />
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </Accordion>
          <Separator
            orientation="horizontal"
            className="h-[1px] bg-border-Default mt-3 mb-3"
          />
          <div className="grid grid-cols-3 gap-6">
            <SelectWidget
              name="paymentType"
              form={form}
              placeholder="Select Payment Type"
              label="Payment Type"
              isClearable={true}
              isLoading={paymentTypeLoading}
              optionsList={paymentTypeList}
              disabled={isDeleted || isOrderEntryReadOnly}
            />
            <SelectWidget
              name="paymentTerms"
              form={form}
              placeholder="Select Payment Terms"
              label="Payment Terms"
              isClearable={true}
              isLoading={paymentTermLoading}
              optionsList={paymentTermList}
              disabled={isDeleted || isOrderEntryReadOnly}
            />
            <SelectWidget
              name="compedOrder"
              form={form}
              placeholder="Select Comped Order"
              label="Comped Order"
              isClearable={true}
              optionsList={compedOrderList}
              disabled={isDeleted || isOrderEntryReadOnly}
            />
            <NumberInputField
              name="dwPercent"
              form={form}
              label="DW Percentage"
              placeholder="______.__"
              suffix="%"
              disabled
            />
            <NumberInputField
              name="dwCalculation"
              form={form}
              label="DW Calculation"
              placeholder="$______.__"
              prefix="$"
              disabled
            />
            <div className="flex items-end">
              <AppButton
                label={'Accept DW'}
                icon={Check}
                spinnerClass={
                  'border-white-500 border-t-transparent animate-spin'
                }
                onClick={() => {
                  recalculateCharge(id, 'DAMAGE_WAIVER');
                }}
                className="bg-white border-[1px] hover:bg-white text-text-Default border-border-Default w-full break-words whitespace-normal h-auto py-2 px-4"
                iconClassName="w-5 min-w-5 flex-shrink-0"
                disabled={isDeleted || isOrderEntryReadOnly}
              />
            </div>
            <div className="col-span-3">
              <NumberInputField
                name="requiredDeposit"
                form={form}
                label="Required Deposit"
                placeholder="$______.__"
                prefix="$"
                disabled
              />
            </div>
            <h2 className="text-1xl font-semibold text-[#181A1D]">Payments</h2>
            <div className="col-span-3">
              <DataTable
                data={paymentDetailsData || []}
                columns={[...columnsPayments]} // Adding the Action column
                isLoading={isLoadingPaymentDetails}
                noDataPlaceholder="No Data Found."
                loaderRows={5}
                enablePagination={false}
                manualSorting={false}
                tableClassName="max-h-[350px] 2xl:max-h-[385px] overflow-auto"
              />
              <h2 className="text-1xl font-semibold text-[#EC221F] pb-3">
                {message}
              </h2>
            </div>
          </div>
        </div>
      </div>
      <div className="grid grid-cols-3 gap-6 border rounded-lg p-4 mt-4">
        <NumberInputField
          name="amountPaid"
          form={form}
          label="Amount Paid"
          placeholder="$______.__"
          prefix="$"
          allowNegative
          disabled
        />
        <NumberInputField
          name="writeOffDiscount"
          form={form}
          label="Write-Off Discount"
          placeholder="$______.__"
          prefix="$"
          allowNegative
          disabled
        />
        <NumberInputField
          name="balanceDue"
          form={form}
          label="Balance Due"
          placeholder="$______.__"
          prefix="$"
          allowNegative
          disabled
        />
      </div>
      {/* Edit Discount */}
      {OpenDiscount && (
        <EditDiscount
          open={OpenDiscount}
          onOpenChange={toggleEditDiscount}
          form={form}
        />
      )}

      {/* Edit Delivery */}
      {openDelivery && (
        <EditDeliveryDailog
          openDelivery={openDelivery}
          open={openDelivery}
          onOpenChange={toggleEditDelivery}
          refetch={refetch}
        />
      )}
      <BreadcrumbDialogRenderer
        activeTab={activeTab}
        isOpen={open.state}
        listItems={listItems}
        onOpenChange={onOpenChange}
        setActiveTab={handleSetActiveTab}
        className={cn(
          'max-w-[95%]',
          open.action === PAYMENT_BRED_CRUM_DETAILS.NEW_PAYMENT ||
            open.action ===
              PAYMENT_BRED_CRUM_DETAILS.PROCESS_WITH_CREDIT_CARD ||
            open.action === PAYMENT_BRED_CRUM_DETAILS.PROCESS_WITH_REFUND
            ? '2xl:max-w-[50%]'
            : '2xl:max-w-[75%]'
        )}
        contentClassName="h-[480px] 2xl:h-[650px] overflow-y-auto"
      />
      <AppConfirmationModal
        description={<div>{openDeletePaymentDialog.msg}</div>}
        open={openDeletePaymentDialog.state}
        onOpenChange={onOpenChangePaymentDeleteDialog}
        handleCancel={onOpenChangePaymentDeleteDialog}
        // disabled={isDeleteLoading}
        handleSubmit={handleDeletePaymentClick}
        isLoading={isLoadingRemove}
      />

      {/* Custom Dialog */}
      <CustomDialog
        open={openDeleteVoidProcessPaymentDialog}
        onOpenChange={onOpenChangeVoidProcessPaymentDeleteDialog}
        title="Process Void"
        contentClassName="py-4"
        className="xl:max-w-2xl overflow-auto"
      >
        <div className="px-6 flex flex-col gap-4">
          <div className="flex items-center justify-between">
            <p className="border border-border-Default rounded-lg px-6 py-3 bg-[#F5F5F5]">
              Click "Process" to void this transaction
            </p>
            <AppButton
              onClick={handleDeleteProcessClick}
              label="Process"
              className="w-28"
              variant="primary"
              isLoading={isLoadingRemove}
            />
          </div>
          <div className="flex flex-col gap-4">
            <InputField
              name="date"
              form={voidDeletePaymentForm}
              label="Date"
              disabled
            />
            <InputField
              name="type"
              form={voidDeletePaymentForm}
              label="Type"
              disabled
            />
            <InputField
              name="amount"
              form={voidDeletePaymentForm}
              label="Amount"
              disabled
            />
            <InputField
              name="originalAccount"
              form={voidDeletePaymentForm}
              label="Original Account"
              className="tracking-wide text-base font-sans"
              disabled
            />
            <InputField
              name="emailReceipt"
              form={voidDeletePaymentForm}
              label="E-mail Receipt"
            />

            <div className="flex justify-between gap-4 bg-white pb-4 mt-6">
              <AppButton
                onClick={handleDeletePaymentWithoutVoid}
                label="Delete Payment Without Void"
                className="w-[50%]"
                isLoading={isLoadingRemovePaymentWithoutVoid}
              />

              <AppButton
                onClick={onOpenChangeVoidProcessPaymentDeleteDialog}
                label="Close"
                className="w-28"
                variant="neutral"
              />
            </div>
          </div>
        </div>
      </CustomDialog>

      <AppSpinner
        overlay
        isLoading={isFetching || isLoadingRecalculate || isTaxCodeLoading}
      />
    </>
  );
}
