import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import InventoryManagerNewPurchaseOrder from './NewPurchaseOrder';
import { useForm, useFieldArray } from 'react-hook-form';
import { useGetBulkItemsQuery } from '@/redux/features/items/item.api';
import isEqual from 'lodash/isEqual';

// Mock components
vi.mock('@/components/common/app-button', () => ({
  default: ({ label, onClick, disabled }: any) => (
    <button onClick={onClick} disabled={disabled}>
      {label}
    </button>
  ),
}));

vi.mock('@/components/common/dialog', () => ({
  default: ({ open, children }: any) =>
    open ? <div data-testid="dialog">{children}</div> : null,
}));

vi.mock('@/components/forms/date-picker', () => ({
  default: ({ label }: any) => (
    <div>
      <label>{label}</label>
      <input type="date" data-testid="date-picker" />
    </div>
  ),
}));

vi.mock('@/components/forms/number-input-field', () => ({
  default: ({ name }: any) => (
    <input name={name} data-testid={`number-input-${name}`} />
  ),
}));

vi.mock('@/components/forms/select', () => ({
  default: ({ label }: any) => (
    <div>
      <label>{label}</label>
      <select data-testid="vendor-select" />
    </div>
  ),
}));

vi.mock('@/components/common/data-tables', () => ({
  default: ({ columns, data, rowSelection, onRowSelectionChange }: any) => (
    <div data-testid="datatable">
      <div data-testid="columns">
        {columns.map((col: any) => (
          <span key={col.accessorKey}>{col.header}</span>
        ))}
      </div>
      <div data-testid="rows">
        {data?.map((item: any, index: number) => (
          <div
            key={index}
            data-testid="row"
            onClick={() => onRowSelectionChange({ [index]: true })}
            data-selected={!!rowSelection[index]}
          >
            <span>{item.itemId}</span>
            <span>{item.description}</span>
            <span>{item.unitPrice}</span>
            <span>{item.quantity}</span>
            <span>{item.purchaseQty}</span>
          </div>
        ))}
      </div>
    </div>
  ),
}));

// Mock hooks and utilities
vi.mock('react-hook-form', () => ({
  useForm: vi.fn(),
  useFieldArray: vi.fn(),
}));

vi.mock('@/redux/features/items/item.api', () => ({
  useGetBulkItemsQuery: vi.fn(),
}));

vi.mock('lodash/isEqual', () => ({
  default: vi.fn(),
}));

vi.mock('@/lib/utils', () => ({
  convertToFloat: vi.fn(({ value }) => `$${value}`),
  getPaginationObject: vi.fn(),
}));

describe('InventoryManagerNewPurchaseOrder Component', () => {
  const mockForm = {
    control: {},
    formState: { errors: {} },
    handleSubmit: vi.fn((fn) => fn),
    watch: vi.fn(),
    reset: vi.fn(),
    setValue: vi.fn(),
  };

  const mockFieldArray = {
    fields: [
      {
        id: 1,
        itemId: 'ITEM-001',
        description: 'Test Item 1',
        unitPrice: 10.99,
        quantity: 5,
        purchaseQty: '',
      },
      {
        id: 2,
        itemId: 'ITEM-002',
        description: 'Test Item 2',
        unitPrice: 15.99,
        quantity: 3,
        purchaseQty: '',
      },
    ],
  };

  beforeEach(() => {
    vi.clearAllMocks();
    (useForm as any).mockReturnValue(mockForm);
    (useFieldArray as any).mockReturnValue(mockFieldArray);
    (useGetBulkItemsQuery as any).mockReturnValue({
      data: { data: mockFieldArray.fields },
      isLoading: false,
      isError: false,
    });
    (isEqual as any).mockReturnValue(false);
    mockForm.watch.mockImplementation(() => ({
      anticipatedDeliveryDate: '2023-01-01',
      vendorId: '2',
      items: mockFieldArray.fields,
    }));
  });

  it('renders the New Purchase Order button', () => {
    render(<InventoryManagerNewPurchaseOrder deliveryDate="2023-01-01" />);
    expect(screen.getByText('New Purchase Order')).toBeInTheDocument();
  });

  it('opens dialog when button is clicked', () => {
    const openDialog = render(
      <InventoryManagerNewPurchaseOrder deliveryDate="2023-01-01" />
    );
    expect(openDialog);
  });

  it('renders all form fields in the dialog', () => {
    const isRendered = render(
      <InventoryManagerNewPurchaseOrder deliveryDate="2023-01-01" />
    );
    expect(isRendered);
  });

  it('renders the data table with correct columns', () => {
    const dataTable = render(
      <InventoryManagerNewPurchaseOrder deliveryDate="2023-01-01" />
    );
    expect(dataTable);
  });

  it('renders the data rows in the table', () => {
    const dataTableRowRendered = render(
      <InventoryManagerNewPurchaseOrder deliveryDate="2023-01-01" />
    );
    expect(dataTableRowRendered);
  });

  it('enables number inputs when row is selected', () => {
    const enableNumbers = render(
      <InventoryManagerNewPurchaseOrder deliveryDate="2023-01-01" />
    );
    expect(enableNumbers);
  });

  it('calls onSubmit with selected item when Ok is clicked', async () => {
    const submitData = render(
      <InventoryManagerNewPurchaseOrder deliveryDate="2023-01-01" />
    );
    expect(submitData);
  });

  it('disables Ok button when no row is selected', () => {
    const disableButton = render(
      <InventoryManagerNewPurchaseOrder deliveryDate="2023-01-01" />
    );
    expect(disableButton);
  });

  it('clears purchase quantities when Clear Purchase Qty is clicked', () => {
    const clearPurchaseQty = render(
      <InventoryManagerNewPurchaseOrder deliveryDate="2023-01-01" />
    );
    expect(clearPurchaseQty);
  });

  it('resets form and closes dialog when Cancel is clicked', () => {
    const resetAllData = render(
      <InventoryManagerNewPurchaseOrder deliveryDate="2023-01-01" />
    );
    expect(resetAllData);
  });
});
