import AppButton from '@/components/common/app-button';
import DataTable from '@/components/common/data-tables';
import InputField from '@/components/forms/input-field';
import NumberInputField from '@/components/forms/number-input-field';
import { getPaginationObject } from '@/lib/utils';
import { useGetBulkItemsQuery } from '@/redux/features/items/item.api';
import { SortingStateType } from '@/types/common.types';
import { TransferInventoryTypes } from '@/types/inventory-manager.type';
import { isEqual } from 'lodash';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { SubmitHandler, useFieldArray, useForm } from 'react-hook-form';

interface TransferInventoryProps {
  handleChange: () => void;
}
const TransferInventory = ({ handleChange }: TransferInventoryProps) => {
  const [sorting, setSorting] = useState<SortingStateType[]>([
    { id: 'itemId', desc: false },
  ]);
  //   const [rowSelection, setRowSelection] = useState<RowSelectionState>({});

  const payload = useMemo(() => {
    return getPaginationObject({
      pagination: {
        pageSize: 100,
        pageIndex: 0,
      },
      sorting: sorting,
      filters: [],
    });
  }, [sorting]);

  const { data: getAllData } = useGetBulkItemsQuery(payload);

  const defaultValues = useMemo(() => {
    return {
      itemId: '',
      qtyShort: '',
      transferTo: '',
      leftTo: '',
      items: getAllData?.data?.map((item) => ({
        listId: item?.id,
        location: item?.location,
        serial: item?.serial,
        quality: item?.quality,
        owned: item?.owned,
        available: item?.available,
        toTransfer: item?.toTransfer || '',
      })),
    };
  }, [getAllData?.data]);

  const form = useForm<TransferInventoryTypes>({ defaultValues });
  const { fields } = useFieldArray<TransferInventoryTypes>({
    control: form.control,
    name: 'items',
  });
  useEffect(() => {
    if (defaultValues) form.reset(defaultValues);
  }, [defaultValues, form]);

  const columns = useMemo(() => {
    return [
      {
        accessorKey: 'location',
        header: 'Location',
        enableSorting: true,
        size: 150,
      },
      {
        accessorKey: 'serial',
        header: 'Serial #',
        enableSorting: true,
        size: 150,
      },
      {
        accessorKey: 'Quality',
        header: 'Quality',
        enableSorting: true,
        size: 150,
      },
      {
        accessorKey: 'owned',
        header: 'Owned',
        enableSorting: true,
        size: 150,
      },
      {
        accessorKey: 'available',
        header: 'Available',
        enableSorting: true,
        size: 100,
      },
      {
        accessorKey: 'toTransfer',
        header: 'To Transfer',
        enableSorting: true,
        size: 150,
        cell: ({ row }: any) => (
          <NumberInputField
            form={form}
            placeholder="To Transfer"
            name={`items.${row?.index}.toTransfer`}
            className="w-full h-9"
            maxLength={7}
            decimalScale={0}
          />
        ),
      },
    ];
  }, [form]);

  // on submit
  const onSubmit: SubmitHandler<TransferInventoryTypes> = useCallback(
    async (formData) => {
      const payload = formData?.items?.filter((item) => item.toTransfer);
      // eslint-disable-next-line no-console
      console.log('payload', payload);
    },
    []
  );

  const isFormModified = isEqual(form.watch('items'), defaultValues?.items);

  return (
    <div>
      <div className="rounded-md grid grid-cols-4 gap-3 pb-1">
        <InputField
          form={form}
          name="itemId"
          label="Item ID"
          placeholder="Item ID"
          disabled
        />
        <InputField
          form={form}
          name="qtyShort"
          label="Qty Short"
          placeholder="Qty Short"
          disabled
        />
        <InputField
          form={form}
          name="transferTo"
          label="Transfer To"
          placeholder="TransferTo"
          disabled
        />
        <InputField
          form={form}
          name="leftTo"
          label="Left To"
          placeholder="Left To"
          disabled
        />
      </div>
      <div className="py-3">
        <DataTable
          columns={columns}
          data={fields || []}
          totalItems={fields?.length}
          sorting={sorting}
          setSorting={setSorting}
          //   enableRowSelection
          //   rowSelection={rowSelection}
          //   onRowSelectionChange={setRowSelection}
          enablePagination={false}
          tableClassName="max-h-[240px] xl:max-h-[340px] overflow-auto"
        />
      </div>
      <div className="absolute bottom-4 right-6 flex items-center gap-4">
        <AppButton
          label="Ok"
          className="w-32"
          disabled={isFormModified}
          onClick={form.handleSubmit(onSubmit)}
        />
        <AppButton
          label="Cancel"
          variant="neutral"
          className="w-32"
          onClick={handleChange}
        />
      </div>
    </div>
  );
};

export default TransferInventory;
