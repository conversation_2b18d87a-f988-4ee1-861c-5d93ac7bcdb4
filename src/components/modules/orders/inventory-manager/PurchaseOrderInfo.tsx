import DataTable from '@/components/common/data-tables';
import { getPaginationObject } from '@/lib/utils';
import { useGetBulkItemsQuery } from '@/redux/features/items/item.api';
import { SortingStateType } from '@/types/common.types';
import { useMemo, useState } from 'react';

const PurchaseOrderInfo = () => {
  const [sorting, setSorting] = useState<SortingStateType[]>([
    { id: 'itemId', desc: false },
  ]);
  const payload = useMemo(() => {
    return getPaginationObject({
      pagination: {
        pageSize: 100,
        pageIndex: 0,
      },
      sorting: sorting,
      filters: [],
    });
  }, [sorting]);

  const { data: getAllData } = useGetBulkItemsQuery(payload);

  const columns = useMemo(() => {
    return [
      {
        accessorKey: 'itemId',
        header: 'P.O. #',
        enableSorting: true,
        size: 200,
        maxSize: 200,
      },
      {
        accessorKey: 'vendor',
        header: 'Vendor',
        enableSorting: true,
        size: 150,
      },
      {
        accessorKey: 'qtyOrdered',
        header: 'Qty Ordered',
        enableSorting: true,
        size: 150,
      },
      {
        accessorKey: 'anticipatedDeliveryDate',
        header: 'Anticipated Delivery Date',
        enableSorting: true,
        size: 300,
      },
    ];
  }, []);
  return (
    <div className="py-3">
      <DataTable
        columns={columns}
        data={getAllData?.data || []}
        totalItems={getAllData?.data?.length}
        sorting={sorting}
        setSorting={setSorting}
        enablePagination={false}
        tableClassName="max-h-[350px] lg:max-h-[450px] overflow-auto"
      />
    </div>
  );
};
export default PurchaseOrderInfo;
