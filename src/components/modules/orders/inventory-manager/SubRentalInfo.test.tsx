import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import SubRentalInfo from './SubRentalInfo';
import { useGetBulkItemsQuery } from '@/redux/features/items/item.api';

// Mock the DataTable component
vi.mock('@/components/common/data-tables', () => ({
  default: vi.fn(({ columns, data }) => (
    <div>
      <div data-testid="datatable">
        {columns.map((col: any) => (
          <span key={col.accessorKey}>{col.header}</span>
        ))}
        {data?.map((item: any, index: number) => (
          <div key={index} data-testid="datatable-row">
            <span>{item.itemId}</span>
            <span>{item.rentedFrom}</span>
            <span>{item.order}</span>
            <span>{item.qty}</span>
          </div>
        ))}
      </div>
    </div>
  )),
}));

// Mock API call
vi.mock('@/redux/features/items/item.api', () => ({
  useGetBulkItemsQuery: vi.fn(),
}));

describe('SubRentalInfo Component', () => {
  const mockData = [
    {
      itemId: 'ET-001',
      rentedFrom: 'Supplier A',
      order: 'ORD-1001',
      qty: 5,
    },
    {
      itemId: 'ET-002',
      rentedFrom: 'Supplier B',
      order: 'ORD-1002',
      qty: 10,
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    (useGetBulkItemsQuery as any).mockReturnValue({
      data: { data: mockData },
      isLoading: false,
      isError: false,
    });
  });

  it('renders the component without crashing', () => {
    render(<SubRentalInfo />);
    expect(screen.getByTestId('datatable')).toBeInTheDocument();
  });

  it('displays all column headers correctly', () => {
    render(<SubRentalInfo />);
    expect(screen.getByText('E.T. #')).toBeInTheDocument();
    expect(screen.getByText('Rented From')).toBeInTheDocument();
    expect(screen.getByText('Order #')).toBeInTheDocument();
    expect(screen.getByText('Qty')).toBeInTheDocument();
  });

  it('renders the data rows correctly', () => {
    render(<SubRentalInfo />);
    const rows = screen.getAllByTestId('datatable-row');
    expect(rows).toHaveLength(2);
    expect(screen.getByText('ET-001')).toBeInTheDocument();
    expect(screen.getByText('Supplier A')).toBeInTheDocument();
    expect(screen.getByText('ORD-1001')).toBeInTheDocument();
    expect(screen.getByText('5')).toBeInTheDocument();
    expect(screen.getByText('ET-002')).toBeInTheDocument();
    expect(screen.getByText('Supplier B')).toBeInTheDocument();
    expect(screen.getByText('ORD-1002')).toBeInTheDocument();
    expect(screen.getByText('10')).toBeInTheDocument();
  });

  it('handles empty data state', () => {
    (useGetBulkItemsQuery as any).mockReturnValue({
      data: { data: [] },
      isLoading: false,
      isError: false,
    });
    render(<SubRentalInfo />);
    const rows = screen.queryAllByTestId('datatable-row');
    expect(rows).toHaveLength(0);
  });

  it('shows loading state when data is loading', () => {
    (useGetBulkItemsQuery as any).mockReturnValue({
      data: undefined,
      isLoading: true,
      isError: false,
    });
    const isLoading = render(<SubRentalInfo />);
    expect(isLoading);
  });

  it('handles error state', () => {
    (useGetBulkItemsQuery as any).mockReturnValue({
      data: undefined,
      isLoading: false,
      isError: true,
    });
    const throwError = render(<SubRentalInfo />);
    expect(throwError);
  });

  it('passes correct sorting state to DataTable', () => {
    const isRendered = render(<SubRentalInfo />);
    expect(isRendered);
  });
});
