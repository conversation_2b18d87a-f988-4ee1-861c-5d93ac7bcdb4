import CheveronLeft from '@/assets/icons/CheveronLeft';
import ExcelIcon from '@/assets/icons/ExcelIcon';
import AppButton from '@/components/common/app-button';
import AppTableContextProvider from '@/components/common/app-data-table/AppTableContext';
import DataTable from '@/components/common/data-tables';
import ActionColumnMenu from '@/components/common/data-tables/ActionColumn';
import CustomDialog from '@/components/common/dialog';
import IconButton from '@/components/common/icon-button';
import SwitchField from '@/components/common/switch';
import DatePicker from '@/components/forms/date-picker';
import SelectWidget from '@/components/forms/select';
import { UseToast } from '@/components/ui/toast/ToastContainer';
import { INVENTORY_MANAGER_API_ROUTES } from '@/constants/api-constants';
import { ROUTES } from '@/constants/routes-constants';
import { useDownloadFile } from '@/hooks/useDownloadFile';
import { formatDateWithTimezone } from '@/lib/formatDateWithTimezone';
import {
  DATE_FORMAT_YYYYMMDD,
  formatDate,
  generateLabelValuePairs,
  getStorageValue,
} from '@/lib/utils';
import { useGetInventoryManagerDataQuery } from '@/redux/features/inventory-manager/inventory-manager.api';
import { useGetStoreLocationsQuery } from '@/redux/features/store/store.api';
import { SortingStateType } from '@/types/common.types';
import {
  InventoryManagerFormType,
  OptionformTyps,
} from '@/types/inventory-manager.type';
import dayjs from 'dayjs';
import { Merge, PrinterIcon, Split, X } from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import InventoryManagerNewPurchaseOrder from './NewPurchaseOrder';
import InventoryManagerOptions from './Options';
import PurchaseOrderInfo from './PurchaseOrderInfo';
import SubRendererTable from './SubRendererTable';
import SubRentalInfo from './SubRentalInfo';
import TransferInventory from './TransferInventory';

const InventoryManager = () => {
  const navigation = useNavigate();
  const toast = UseToast();
  const [action, setAction] = useState<
    'transfer' | 'poInfo' | 'subRentalInfo' | string
  >('');
  const [sorting, setSorting] = useState<SortingStateType[]>([
    { id: 'category', desc: true },
  ]);
  const [isExpandAllRow, setIsExpandAllRow] = useState<boolean>(false);
  const toggleExpnadAllRow = () => {
    setIsExpandAllRow((prev) => !prev);
  };
  const tz = getStorageValue('timeZoneOffset') || '';
  const currentDate = formatDateWithTimezone({ tz });

  const defaultValues = useMemo(() => {
    return {
      storeLocationId: '0',
      dateFrom: currentDate,
      dateTo: currentDate,
      hideResolvedOverbooking: false,
      showNonOverbookedOrders: false,
      includeQuotes: false,
      includeSalesItemsOnRentals: false,
      checkAcrossAllLocation: false,
      sortByDeliveryDate: false,
      displayByCategory: true,
      departments: '',
      categories: '',
    };
  }, [currentDate]);

  // Initialize form hook
  const form = useForm<InventoryManagerFormType>({
    defaultValues: defaultValues,
  });

  useEffect(() => {
    if (defaultValues) {
      form.reset(defaultValues);
    }
  }, [defaultValues, form]);

  const inventoryInfo = form.watch();

  const { data: locationData, isLoading: locationLoading } =
    useGetStoreLocationsQuery();

  const locationList = useMemo(() => {
    const list = generateLabelValuePairs({
      data: locationData?.data,
      labelKey: 'location',
      valueKey: 'id',
    });
    return list?.length
      ? [...[{ label: 'All Locations', value: '0' }], ...list]
      : [];
  }, [locationData?.data]);

  // navigate to back
  const navigateToOrder = useCallback(() => {
    navigation(ROUTES.ORDERS);
  }, [navigation]);

  // Export Inventory Manager
  const { downloadFile } = useDownloadFile();

  const dateFrom = formatDate(inventoryInfo?.dateFrom, DATE_FORMAT_YYYYMMDD);
  const dateTo = formatDate(inventoryInfo?.dateTo, DATE_FORMAT_YYYYMMDD);

  const payload = useMemo(() => {
    return {
      storeLocationId: inventoryInfo?.storeLocationId,
      dateFrom,
      dateTo,
      hideResolvedOverbooking: inventoryInfo?.hideResolvedOverbooking,
      showNonOverbookedOrders: inventoryInfo?.showNonOverbookedOrders,
      includeQuotes: inventoryInfo?.includeQuotes,
      includeSalesItemsOnRentals: inventoryInfo?.includeSalesItemsOnRentals,
      checkAcrossAllLocation: inventoryInfo?.checkAcrossAllLocation,
      sortByDeliveryDate: inventoryInfo?.sortByDeliveryDate,
      displayByCategory: inventoryInfo?.displayByCategory,
      departments: inventoryInfo?.departments?.toString(),
      categories: inventoryInfo?.categories?.toString(),
      sortBy: sorting?.at(0)?.id,
      sortAscending: sorting?.at(0)?.desc,
    };
  }, [
    dateFrom,
    dateTo,
    inventoryInfo?.categories,
    inventoryInfo?.checkAcrossAllLocation,
    inventoryInfo?.departments,
    inventoryInfo?.displayByCategory,
    inventoryInfo?.hideResolvedOverbooking,
    inventoryInfo?.includeQuotes,
    inventoryInfo?.includeSalesItemsOnRentals,
    inventoryInfo?.showNonOverbookedOrders,
    inventoryInfo?.sortByDeliveryDate,
    inventoryInfo?.storeLocationId,
    sorting,
  ]);

  // Export Inventory Manager data
  const exportInventoryManagerData = useCallback(() => {
    const response = downloadFile({
      url: INVENTORY_MANAGER_API_ROUTES.EXPORT_CSV,
      method: 'POST',
      body: payload,
    });
    toast.promise(response, {
      loading: 'Downloading file...',
      success: 'File downloaded successfully.',
      error: 'Failed to download file',
    });
  }, [downloadFile, payload, toast]);

  const OrderDropdownMenu = [
    {
      label: 'Print',
      onClick: () => {},
      icon: <PrinterIcon />,
      disabled: true,
    },
    {
      label: 'Extract',
      icon: <ExcelIcon />,
      onClick: exportInventoryManagerData,
    },
  ];

  //Handles the date change for delivery and pickup dates.
  const handleOnDateChange = useCallback(
    (newDate: Date | string, fieldName: 'dateFrom' | 'dateTo') => {
      const updatedDate = dayjs(newDate);
      const currentDate = dayjs(form.watch(fieldName));
      if (
        (fieldName === 'dateTo' && updatedDate?.isAfter(currentDate)) ||
        (fieldName === 'dateFrom' && updatedDate?.isBefore(currentDate))
      ) {
        form.setValue(fieldName, updatedDate?.format('YYYY-MM-DD'));
      }
      const dateFieldName = fieldName === 'dateTo' ? 'dateFrom' : 'dateTo';
      const hasErrorsAndValue = !!(
        Object.keys(form.formState.errors).length && newDate
      );

      if (!newDate) {
        form.setError(dateFieldName, { message: 'Required' });
      } else if (hasErrorsAndValue) {
        form.clearErrors(dateFieldName);
      }
    },
    [form]
  );

  const handleSetAction = (
    action?: 'transfer' | 'poInfo' | 'subRentalInfo'
  ) => {
    setAction(action ?? '');
  };
  const InventoryColumn = useMemo(
    () => [
      inventoryInfo?.displayByCategory
        ? {
            accessorKey: 'category',
            header: 'Category',
            enableSorting: true,
            size: 200,
            maxSize: 200,
          }
        : {
            accessorKey: 'department',
            header: 'Department',
            enableSorting: true,
            size: 200,
            maxSize: 200,
          },
      {
        accessorKey: 'itemId',
        header: 'Item ID',
        enableSorting: true,
      },
      {
        accessorKey: 'description',
        header: 'Item Description',
        size: 200,
        maxSize: 200,
        enableSorting: true,
      },
      {
        accessorKey: 'location',
        header: 'Location',
        enableSorting: true,
      },
      {
        accessorKey: 'owned',
        header: 'Owned',
        enableSorting: true,
      },
      {
        accessorKey: 'rented',
        header: 'Rented',
        enableSorting: true,
      },
      {
        accessorKey: 'ordered',
        header: 'Ordered',
        enableSorting: true,
      },
      {
        accessorKey: 'subrented',
        header: 'Sub Rented',
        enableSorting: true,
        size: 160,
      },
      {
        accessorKey: 'shortBy',
        header: 'Short',
        enableSorting: true,
      },
      {
        accessorKey: 'otherLoc',
        header: 'Other Location',
        enableSorting: true,
        size: 170,
      },
      {
        size: 80,
        id: 'action',
        header: 'Actions',
        cell: () => {
          return (
            <ActionColumnMenu
              disabled={true}
              dropdownMenuList={[
                {
                  label: 'Transfer',
                  onClick: () => handleSetAction('transfer'),
                  // disabled: true,
                },
                {
                  label: 'P.O. Info',
                  onClick: () => handleSetAction('poInfo'),
                  // disabled: true,
                },
                {
                  label: 'Sub Rental Info',
                  onClick: () => handleSetAction('subRentalInfo'),
                  // disabled: true,
                },
              ]}
              contentClassName="w-fit p-3"
            />
          );
        },
      },
    ],
    [inventoryInfo?.displayByCategory]
  );

  // Header section
  const Header = () => {
    return (
      <div className="flex gap-x-4 items-center justify-between sticky top-16 pt-3 pb-2 bg-white z-20">
        <div className="flex items-center gap-3">
          <IconButton onClick={navigateToOrder}>
            <CheveronLeft />
          </IconButton>
          <h1 className="text-2xl font-semibold">Inventory Manager</h1>
        </div>

        <div className="flex items-center gap-3">
          <InventoryManagerNewPurchaseOrder
            deliveryDate={inventoryInfo?.dateFrom}
          />
          <AppButton
            label="Cancel"
            icon={X}
            onClick={navigateToOrder}
            iconClassName="w-4 h-4"
            variant="neutral"
          />
          <ActionColumnMenu
            triggerClassName="border h-10"
            dropdownMenuList={OrderDropdownMenu}
            contentClassName="p-4 flex flex-col gap-2 w-full"
          />
        </div>
      </div>
    );
  };

  // Item list
  const { data: getAllData, isFetching: isLoading } =
    useGetInventoryManagerDataQuery(payload, {
      skip: !dateFrom || !dateTo,
      refetchOnMountOrArgChange: true,
    });

  const handleOptionFilter = useCallback(
    (optionFormData: OptionformTyps) => {
      form.reset({ ...inventoryInfo, ...optionFormData });
      if (optionFormData.checkAcrossAllLocation) {
        form.setValue('storeLocationId', '0');
      }
      setSorting([
        {
          id: optionFormData?.displayByCategory ? 'category' : 'department',
          desc: true,
        },
      ]);
    },
    [form, inventoryInfo]
  );

  const { component, label } = useMemo(() => {
    const componentMap: Record<
      string,
      { component: JSX.Element; label: string }
    > = {
      transfer: {
        component: <TransferInventory handleChange={() => setAction('')} />,
        label: 'Transfer Inventory',
      },
      poInfo: {
        component: <PurchaseOrderInfo />,
        label: 'Purchase Order Info',
      },
      subRentalInfo: {
        component: <SubRentalInfo />,
        label: 'Sub Rental Info',
      },
    };

    return componentMap[action] || { component: null, label: '' };
  }, [action]);

  return (
    <div className="px-6 pb-4">
      <Header />
      <div className="p-3 border rounded-md grid grid-cols-1 md:grid-cols-4 gap-5 my-3 items-start">
        <div className="col-span-2 flex items-end gap-4">
          <SelectWidget
            form={form}
            optionsList={locationList}
            name="storeLocationId"
            label="Location"
            placeholder="Select Location"
            isLoading={locationLoading}
            menuPosition="absolute"
            isClearable={false}
            disabled={inventoryInfo.checkAcrossAllLocation}
            parentClassName="z-[15]"
          />
          <InventoryManagerOptions onClickOptionFilter={handleOptionFilter} />
        </div>
        <DatePicker
          form={form}
          name="dateFrom"
          label="Date From"
          placeholder="Select Date"
          enableInput
          onDateChange={(date) => handleOnDateChange(date ?? '', 'dateTo')}
        />
        <DatePicker
          form={form}
          name="dateTo"
          label="Date Thru"
          placeholder="Select Date"
          enableInput
          onDateChange={(date) => handleOnDateChange(date ?? '', 'dateFrom')}
        />
        <div className="col-span-4 grid grid-cols-1 md:grid-cols-3 gap-5 items-center mt-1">
          <SwitchField
            label="Hide Resolved Overbookings"
            form={form}
            name="hideResolvedOverbooking"
          />
          <SwitchField
            label="Show Non-Overbooked Orders"
            form={form}
            name="showNonOverbookedOrders"
          />
          <div className="flex justify-end">
            <AppButton
              label={isExpandAllRow ? 'Collapse' : 'Expand'}
              className="w-fit bg-brand-teal-Default hover:bg-brand-teal-Default/85"
              icon={isExpandAllRow ? Merge : Split}
              iconClassName="w-5 h-5"
              onClick={toggleExpnadAllRow}
              disabled={!getAllData?.data?.length}
            />
          </div>
        </div>
      </div>
      <div className="my-2">
        <AppTableContextProvider defaultSort={[{ id: 'itemId', desc: true }]}>
          <DataTable
            columns={InventoryColumn}
            data={getAllData?.data || []}
            totalItems={getAllData?.data?.length + 1}
            isLoading={isLoading}
            heading="Item List"
            sorting={sorting}
            setSorting={setSorting}
            customToolBar
            enablePagination={false}
            tableClassName="max-h-[300px] lg:max-h-[465px] overflow-auto"
            isRowExpanded={true}
            isExpandAllRow={isExpandAllRow}
            renderSubComponent={({ row }) => (
              <SubRendererTable
                data={(row?.original?.inventoryManagerOrders as any) || []}
              />
            )}
          />
        </AppTableContextProvider>
      </div>
      <CustomDialog
        onOpenChange={() => setAction('')}
        description=""
        open={['transfer', 'poInfo', 'subRentalInfo'].includes(action)}
        className="min-w-[83%] xl:min-w-[57%]"
        contentClassName="min-h-[400px] xl:min-h-[500px] overflow-y-auto"
        title={label}
      >
        <div className="px-6">{component}</div>
      </CustomDialog>
    </div>
  );
};

export default InventoryManager;
