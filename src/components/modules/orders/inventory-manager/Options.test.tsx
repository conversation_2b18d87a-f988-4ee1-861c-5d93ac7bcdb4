import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import InventoryManagerOptions from './Options';
import { useForm } from 'react-hook-form';
import useOptionList from '@/hooks/useOptionList';

// Mock components
vi.mock('@/components/common/app-button', () => ({
  default: ({ label, onClick }: any) => (
    <button onClick={onClick}>{label}</button>
  ),
}));

vi.mock('@/components/common/dialog', () => ({
  default: ({ open, children }: any) =>
    open ? <div data-testid="dialog">{children}</div> : null,
}));

vi.mock('@/components/common/switch', () => ({
  default: ({ label, name, onChange }: any) => (
    <label>
      <input
        type="checkbox"
        name={name}
        onChange={(e) => onChange(e.target.checked)}
        data-testid={`switch-${name}`}
      />
      {label}
    </label>
  ),
}));

vi.mock('@/components/forms/MulitCheckbox', () => ({
  default: ({ name, label, disabled }: any) => (
    <div data-testid={`multicheckbox-${name}`} data-disabled={disabled}>
      {label}
    </div>
  ),
}));

// Mock hooks
vi.mock('react-hook-form', () => ({
  useForm: vi.fn(),
}));

vi.mock('@/hooks/useOptionList', () => ({
  default: vi.fn(),
}));

describe('InventoryManagerOptions Component', () => {
  const mockOnClickOptionFilter = vi.fn();
  const mockForm = {
    control: {},
    formState: { errors: {} },
    handleSubmit: vi.fn((fn) => fn),
    watch: vi.fn(),
    reset: vi.fn(),
    setValue: vi.fn(),
  };

  const mockOptionList = [
    { label: 'Include Quotes', name: 'includeQuotes' },
    {
      label: 'Include Sales Items on Rentals',
      name: 'includeSalesItemsOnRentals',
    },
    {
      label: 'Check Only Across All Locations',
      name: 'checkAcrossAllLocation',
    },
    { label: 'Sort by Delivery Date', name: 'sortByDeliveryDate' },
    { label: 'Display by Category', name: 'displayByCategory' },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    (useForm as any).mockReturnValue(mockForm);
    (useOptionList as any).mockImplementation(({ url }: any) => {
      if (url.includes('department')) {
        return {
          options: [
            { id: '1', deptDesc: 'Department 1' },
            { id: '2', deptDesc: 'Department 2' },
          ],
          optionLoading: false,
        };
      }
      return {
        options: [
          { id: '1', catDesc: 'Category 1' },
          { id: '2', catDesc: 'Category 2' },
        ],
        optionLoading: false,
      };
    });
    mockForm.watch.mockImplementation((name) => {
      if (name === 'displayByCategory') return true;
      return undefined;
    });
  });

  it('renders the Options button', () => {
    render(
      <InventoryManagerOptions onClickOptionFilter={mockOnClickOptionFilter} />
    );
    expect(screen.getByText('Options')).toBeInTheDocument();
  });

  it('opens dialog when Options button is clicked', () => {
    render(
      <InventoryManagerOptions onClickOptionFilter={mockOnClickOptionFilter} />
    );
    fireEvent.click(screen.getByText('Options'));
    expect(screen.getByTestId('dialog')).toBeInTheDocument();
  });

  it('renders all option switches in the dialog', () => {
    render(
      <InventoryManagerOptions onClickOptionFilter={mockOnClickOptionFilter} />
    );
    fireEvent.click(screen.getByText('Options'));
    mockOptionList.forEach((option) => {
      expect(screen.getByText(option.label)).toBeInTheDocument();
      expect(screen.getByTestId(`switch-${option.name}`)).toBeInTheDocument();
    });
  });

  it('renders department and category multi-checkbox dropdowns', () => {
    render(
      <InventoryManagerOptions onClickOptionFilter={mockOnClickOptionFilter} />
    );
    fireEvent.click(screen.getByText('Options'));
    expect(screen.getByTestId('multicheckbox-departments')).toBeInTheDocument();
    expect(screen.getByTestId('multicheckbox-categories')).toBeInTheDocument();
  });

  it('disables departments dropdown when displayByCategory is true', () => {
    render(
      <InventoryManagerOptions onClickOptionFilter={mockOnClickOptionFilter} />
    );
    fireEvent.click(screen.getByText('Options'));
    const departmentsDropdown = screen.getByTestId('multicheckbox-departments');
    expect(departmentsDropdown.getAttribute('data-disabled')).toBe('true');
  });

  it('enables categories dropdown when displayByCategory is true', () => {
    render(
      <InventoryManagerOptions onClickOptionFilter={mockOnClickOptionFilter} />
    );
    fireEvent.click(screen.getByText('Options'));
    const categoriesDropdown = screen.getByTestId('multicheckbox-categories');
    expect(categoriesDropdown.getAttribute('data-disabled')).toBe('false');
  });

  it('calls onClickOptionFilter with form data when Ok is clicked', async () => {
    const mockFormData = {
      includeQuotes: true,
      includeSalesItemsOnRentals: false,
      checkAcrossAllLocation: false,
      sortByDeliveryDate: true,
      displayByCategory: true,
      departments: [],
      categories: ['1'],
    };
    mockForm.handleSubmit.mockImplementation((submitFn) => () => {
      submitFn(mockFormData);
    });
    render(
      <InventoryManagerOptions onClickOptionFilter={mockOnClickOptionFilter} />
    );
    fireEvent.click(screen.getByText('Options'));
    fireEvent.click(screen.getByText('Ok'));
    await waitFor(() => {
      expect(mockOnClickOptionFilter).toHaveBeenCalledWith(mockFormData);
    });
  });

  it('resets form and closes dialog when Cancel is clicked', () => {
    render(
      <InventoryManagerOptions onClickOptionFilter={mockOnClickOptionFilter} />
    );
    fireEvent.click(screen.getByText('Options'));
    fireEvent.click(screen.getByText('Cancel'));
    expect(mockForm.reset).toHaveBeenCalled();
    expect(screen.queryByTestId('dialog')).not.toBeInTheDocument();
  });

  it('resets departments/categories when displayByCategory changes', () => {
    render(
      <InventoryManagerOptions onClickOptionFilter={mockOnClickOptionFilter} />
    );
    fireEvent.click(screen.getByText('Options'));
    const displayByCategorySwitch = screen.getByTestId(
      'switch-displayByCategory'
    );
    fireEvent.click(displayByCategorySwitch, { target: { checked: false } });
    expect(mockForm.setValue).toHaveBeenCalledWith('departments', '');
  });

  it('shows loading state for dropdowns when data is loading', () => {
    (useOptionList as any).mockImplementationOnce(() => ({
      options: [],
      optionLoading: true,
    }));
    render(
      <InventoryManagerOptions onClickOptionFilter={mockOnClickOptionFilter} />
    );
    const setValuesToInputField = fireEvent.click(screen.getByText('Options'));
    expect(setValuesToInputField);
  });
});
