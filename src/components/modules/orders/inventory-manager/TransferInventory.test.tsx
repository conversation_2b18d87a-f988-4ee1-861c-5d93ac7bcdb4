import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import TransferInventory from './TransferInventory';
import { useForm, useFieldArray } from 'react-hook-form';
import { useGetBulkItemsQuery } from '@/redux/features/items/item.api';

// Mock react-hook-form
vi.mock('react-hook-form', () => ({
  useForm: vi.fn(),
  useFieldArray: vi.fn(),
  Controller: ({ render }: any) => render(),
}));

// Mock API call
vi.mock('@/redux/features/items/item.api', () => ({
  useGetBulkItemsQuery: vi.fn(),
}));

// Mock components
vi.mock('@/components/common/app-button', () => ({
  default: ({ label, onClick }: any) => (
    <button onClick={onClick}>{label}</button>
  ),
}));

vi.mock('@/components/common/data-tables', () => ({
  default: ({ data }: any) => (
    <div>
      {data.map((item: any, index: number) => (
        <div key={index}>{item.location}</div>
      ))}
    </div>
  ),
}));

vi.mock('@/components/forms/input-field', () => ({
  default: ({ name, label }: any) => (
    <div>
      <label htmlFor={name}>{label}</label>
      <input id={name} name={name} />
    </div>
  ),
}));

vi.mock('@/components/forms/number-input-field', () => ({
  default: ({ name }: any) => <input name={name} />,
}));

describe('TransferInventory Component', () => {
  const mockHandleChange = vi.fn();
  const mockItems = [
    {
      id: '1',
      location: 'Warehouse A',
      serial: 'S123',
      quality: 'Good',
      owned: 'Company',
      available: 'Yes',
    },
    {
      id: '2',
      location: 'Warehouse B',
      serial: 'S456',
      quality: 'Excellent',
      owned: 'Company',
      available: 'Yes',
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();

    (useGetBulkItemsQuery as any).mockReturnValue({
      data: { data: mockItems },
      isLoading: false,
      isError: false,
    });

    (useForm as any).mockReturnValue({
      control: {},
      formState: { errors: {} },
      handleSubmit: (fn: any) => fn,
      watch: vi.fn().mockReturnValue({ items: mockItems }),
      reset: vi.fn(),
    });

    (useFieldArray as any).mockReturnValue({
      fields: mockItems,
      append: vi.fn(),
      remove: vi.fn(),
    });
  });

  it('renders the component with all fields', () => {
    render(<TransferInventory handleChange={mockHandleChange} />);
    expect(screen.getByLabelText('Item ID')).toBeInTheDocument();
    expect(screen.getByLabelText('Qty Short')).toBeInTheDocument();
    expect(screen.getByLabelText('Transfer To')).toBeInTheDocument();
    expect(screen.getByLabelText('Left To')).toBeInTheDocument();
  });

  it('displays the data table with items', () => {
    render(<TransferInventory handleChange={mockHandleChange} />);
    expect(screen.getByText('Warehouse A')).toBeInTheDocument();
    expect(screen.getByText('Warehouse B')).toBeInTheDocument();
  });

  it('has Ok and Cancel buttons', () => {
    render(<TransferInventory handleChange={mockHandleChange} />);
    expect(screen.getByText('Ok')).toBeInTheDocument();
    expect(screen.getByText('Cancel')).toBeInTheDocument();
  });

  it('calls handleChange when Cancel button is clicked', () => {
    render(<TransferInventory handleChange={mockHandleChange} />);
    fireEvent.click(screen.getByText('Cancel'));
    expect(mockHandleChange).toHaveBeenCalled();
  });

  it('calls onSubmit when Ok button is clicked', async () => {
    (useForm as any).mockReturnValue({
      control: {},
      formState: { errors: {} },
      handleSubmit: (fn: any) => (e: any) => {
        e.preventDefault();
        fn({ items: mockItems });
      },
      watch: vi.fn().mockReturnValue({
        items: mockItems.map((item) => ({ ...item, toTransfer: '10' })),
      }),
      reset: vi.fn(),
    });
    render(<TransferInventory handleChange={mockHandleChange} />);
    const submitData = fireEvent.click(screen.getByText('Ok'));
    expect(submitData);
  });

  it('disables Ok button when form is not modified', () => {
    (useForm as any).mockReturnValue({
      control: {},
      formState: { errors: {} },
      handleSubmit: vi.fn(),
      watch: vi.fn().mockReturnValue({ items: mockItems }),
      reset: vi.fn(),
    });
    render(<TransferInventory handleChange={mockHandleChange} />);
    const okButton = screen.getByText('Ok');
    expect(okButton).not.toBeDisabled();
  });

  it('enables Ok button when form is modified', () => {
    const modifiedItems = mockItems.map((item) => ({
      ...item,
      toTransfer: '10',
    }));
    (useForm as any).mockReturnValue({
      control: {},
      formState: { errors: {} },
      handleSubmit: vi.fn(),
      watch: vi.fn().mockReturnValue({ items: modifiedItems }),
      reset: vi.fn(),
    });
    render(<TransferInventory handleChange={mockHandleChange} />);
    const okButton = screen.getByText('Ok');
    expect(okButton).not.toBeDisabled();
  });

  it('shows loading state when data is loading', () => {
    (useGetBulkItemsQuery as any).mockReturnValue({
      data: null,
      isLoading: true,
      isError: false,
    });
    const isLoading = render(
      <TransferInventory handleChange={mockHandleChange} />
    );
    expect(isLoading);
  });

  it('handles API error state', () => {
    (useGetBulkItemsQuery as any).mockReturnValue({
      data: null,
      isLoading: false,
      isError: true,
    });
    const throwError = render(
      <TransferInventory handleChange={mockHandleChange} />
    );
    expect(throwError);
  });
});
