import {
  render,
  screen,
  waitFor,
  fireEvent,
  within,
} from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import InventoryManager from './index';
import { useGetInventoryManagerDataQuery } from '@/redux/features/inventory-manager/inventory-manager.api';
import { useGetStoreLocationsQuery } from '@/redux/features/store/store.api';
import { itemApi } from '@/redux/features/items/item.api';

// Create a mock store
const mockStore = configureStore({
  reducer: {
    [itemApi.reducerPath]: itemApi.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(itemApi.middleware),
});

vi.mock('dayjs', async () => {
  const actual = await vi.importActual('dayjs');
  return {
    ...actual,
    tz: () => ({
      format: () => '2023-01-01',
    }),
    extend: vi.fn(),
  };
});

// Mock formatDateWithTimezone
vi.mock('@/lib/formatDateWithTimezone', () => ({
  formatDateWithTimezone: () => '2023-01-01',
}));

// Mock all the external dependencies
vi.mock('@/assets/icons/CheveronLeft', () => ({
  default: () => <div>CheveronLeft</div>,
}));

vi.mock('@/assets/icons/ExcelIcon', () => ({
  default: () => <div>ExcelIcon</div>,
}));

vi.mock('@/components/common/app-button', () => ({
  default: ({ label, onClick }: { label: string; onClick: () => void }) => (
    <button onClick={onClick}>{label}</button>
  ),
}));

vi.mock('@/components/common/data-tables', () => ({
  default: ({ data }: { data: any[] }) => (
    <div>
      <h3>DataTable</h3>
      <div data-testid="mock-data-table">{JSON.stringify(data)}</div>
    </div>
  ),
}));

vi.mock('@/components/common/dialog', () => ({
  default: ({
    open,
    children,
  }: {
    open: boolean;
    children: React.ReactNode;
  }) => (open ? <div data-testid="mock-dialog">{children}</div> : null),
}));

vi.mock('@/components/forms/date-picker', () => ({
  default: ({ name, form }: { name: string; form: any }) => (
    <input
      data-testid={`date-picker-${name}`}
      value={form.watch(name)}
      onChange={(e) => form.setValue(name, e.target.value)}
    />
  ),
}));

vi.mock('@/components/forms/select', () => ({
  default: ({ name, optionsList }: { name: string; optionsList: any[] }) => (
    <select data-testid={`select-${name}`}>
      {optionsList.map((option) => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  ),
}));

// Mock for useDownloadFile
vi.mock('@/hooks/useDownloadFile', () => ({
  useDownloadFile: () => ({
    downloadFile: vi.fn(() => Promise.resolve()),
  }),
}));

// Mock API calls
vi.mock('@/redux/features/inventory-manager/inventory-manager.api', () => ({
  useGetInventoryManagerDataQuery: vi.fn(() => ({
    data: [],
    isFetching: false,
  })),
  useGetBulkItemsQuery: vi.fn(() => ({ data: [], isLoading: false })),
  useExportInventoryManagerDataMutation: vi.fn(),
}));

vi.mock('@/redux/features/store/store.api', () => ({
  useGetStoreLocationsQuery: vi.fn(() => ({
    data: [],
    isLoading: false,
  })),
}));

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => vi.fn(),
  };
});

describe('InventoryManager', () => {
  const mockLocationData = {
    data: [
      { id: '1', location: 'Location 1' },
      { id: '2', location: 'Location 2' },
    ],
  };

  const mockInventoryData = {
    data: [
      {
        category: 'Category 1',
        itemId: 'ITEM001',
        description: 'Test Item',
        location: 'Location 1',
        owned: 10,
        rented: 5,
        ordered: 3,
        subrented: 2,
        shortBy: 0,
        otherLoc: 1,
        inventoryManagerOrders: [],
      },
    ],
  };

  beforeEach(() => {
    vi.mocked(useGetStoreLocationsQuery).mockReturnValue({
      data: mockLocationData,
      isLoading: false,
    } as any);
    vi.mocked(useGetInventoryManagerDataQuery).mockReturnValue({
      data: mockInventoryData,
      isFetching: false,
    } as any);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  const renderComponent = () => {
    return render(
      <Provider store={mockStore}>
        <MemoryRouter>
          <InventoryManager />
        </MemoryRouter>
      </Provider>
    );
  };

  it('should render the component with header', () => {
    renderComponent();
    expect(screen.getByText('Inventory Manager')).toBeInTheDocument();
    expect(screen.getByText('Cancel')).toBeInTheDocument();
  });

  it('should render location select with options', () => {
    renderComponent();
    const locationSelect = screen.getByTestId('select-storeLocationId');
    expect(locationSelect).toBeInTheDocument();
    expect(
      within(locationSelect).getByText('All Locations')
    ).toBeInTheDocument();
    expect(within(locationSelect).getByText('Location 1')).toBeInTheDocument();
  });

  it('should render date pickers', () => {
    renderComponent();
    expect(screen.getByTestId('date-picker-dateFrom')).toBeInTheDocument();
    expect(screen.getByTestId('date-picker-dateTo')).toBeInTheDocument();
  });

  it('should render the data table with inventory data', async () => {
    renderComponent();
    await waitFor(() => {
      expect(screen.getByTestId('mock-data-table')).toBeInTheDocument();
      expect(screen.getByTestId('mock-data-table')).toHaveTextContent(
        'ITEM001'
      );
    });
  });

  it('should handle location change', async () => {
    renderComponent();
    const locationSelect = screen.getByTestId('select-storeLocationId');
    fireEvent.change(locationSelect, { target: { value: '1' } });
    expect(vi.mocked(useGetInventoryManagerDataQuery)).toHaveBeenCalled();
  });

  it('should handle date change', async () => {
    renderComponent();
    const dateFromPicker = screen.getByTestId('date-picker-dateFrom');
    fireEvent.change(dateFromPicker, { target: { value: '2023-01-01' } });
    expect(vi.mocked(useGetInventoryManagerDataQuery)).toHaveBeenCalled();
  });

  it('should toggle expand/collapse button', async () => {
    renderComponent();
    const expandButton = screen.getByText('Expand');
    fireEvent.click(expandButton);
    expect(screen.getByText('Collapse')).toBeInTheDocument();
  });

  it('should export data when extract button is clicked', async () => {
    const extractButton = renderComponent();
    expect(extractButton);
  });

  it('should show loading state when data is fetching', () => {
    vi.mocked(useGetInventoryManagerDataQuery).mockReturnValue({
      data: undefined,
      isFetching: true,
    } as any);
    const isLoading = renderComponent();
    expect(isLoading);
  });

  it('should handle error state', () => {
    vi.mocked(useGetInventoryManagerDataQuery).mockReturnValue({
      error: new Error('API Error'),
      isFetching: false,
    } as any);
    const errorLoadingData = renderComponent();
    expect(errorLoadingData);
  });
});
