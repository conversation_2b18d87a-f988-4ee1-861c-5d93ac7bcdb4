import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import SubRendererTable from './SubRendererTable';

// Mock the DataTable component
vi.mock('@/components/common/data-tables', () => ({
  default: vi.fn(({ columns, data }) => (
    <div data-testid="datatable-mock">
      <div data-testid="columns">
        {columns.map((col: any) => (
          <span key={col.accessorKey}>{col.header}</span>
        ))}
      </div>
      <div data-testid="rows">
        {data?.map((item: any, index: number) => (
          <div key={index} data-testid="row">
            <span data-testid={`orderNo-${index}`}>{item.orderNo}</span>
            <span data-testid={`customer-${index}`}>{item.customer}</span>
            <span data-testid={`rented-${index}`}>{item.rented}</span>
            <span data-testid={`deliveryDate-${index}`}>
              {item.deliveryDate}
            </span>
            <span data-testid={`pickupDate-${index}`}>{item.pickupDate}</span>
            <span data-testid={`cleanupDays-${index}`}>{item.cleanupDays}</span>
            <span data-testid={`availableDate-${index}`}>
              {item.availableDate}
            </span>
            <span data-testid={`shortBy-${index}`}>{item.shortBy}</span>
            <span data-testid={`flipped-${index}`}>{item.flipped}</span>
            <span data-testid={`note-${index}`}>{item.note}</span>
          </div>
        ))}
      </div>
    </div>
  )),
}));

// Mock the formatDate utility
vi.mock('@/lib/utils', () => ({
  formatDate: vi.fn((date) => `formatted-${date}`),
}));

describe('SubRendererTable Component', () => {
  const mockData = [
    {
      orderNo: 'ORD-001',
      customer: 'Customer A',
      rented: 10,
      deliveryDate: '2023-01-15',
      pickupDate: '2023-01-20',
      cleanupDays: 2,
      availableDate: '2023-01-22',
      shortBy: 0,
      flipped: 'No',
      note: 'Test note 1',
    },
    {
      orderNo: 'ORD-002',
      customer: 'Customer B',
      rented: 15,
      deliveryDate: '2023-02-10',
      pickupDate: '2023-02-15',
      cleanupDays: 1,
      availableDate: '2023-02-16',
      shortBy: 2,
      flipped: 'Yes',
      note: 'Test note 2',
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders without crashing', () => {
    render(<SubRendererTable data={mockData} />);
    expect(screen.getByTestId('datatable-mock')).toBeInTheDocument();
  });

  it('displays all column headers correctly', () => {
    render(<SubRendererTable data={mockData} />);
    expect(screen.getByText('Order #')).toBeInTheDocument();
    expect(screen.getByText('Customer')).toBeInTheDocument();
    expect(screen.getByText('Rented')).toBeInTheDocument();
    expect(screen.getByText('Delivery Date')).toBeInTheDocument();
    expect(screen.getByText('Pickup Date')).toBeInTheDocument();
    expect(screen.getByText('Cleanup Days')).toBeInTheDocument();
    expect(screen.getByText('Available Date')).toBeInTheDocument();
    expect(screen.getByText('Short')).toBeInTheDocument();
    expect(screen.getByText('Flipped')).toBeInTheDocument();
    expect(screen.getByText('Note')).toBeInTheDocument();
  });

  it('renders the correct data rows', () => {
    render(<SubRendererTable data={mockData} />);
    const rows = screen.getAllByTestId('row');
    expect(rows).toHaveLength(2);
    expect(screen.getByTestId('orderNo-0')).toHaveTextContent('ORD-001');
    expect(screen.getByTestId('customer-0')).toHaveTextContent('Customer A');
    expect(screen.getByTestId('rented-0')).toHaveTextContent('10');
    expect(screen.getByTestId('deliveryDate-0')).toHaveTextContent(
      '2023-01-15'
    );
    expect(screen.getByTestId('pickupDate-0')).toHaveTextContent('2023-01-20');
    expect(screen.getByTestId('cleanupDays-0')).toHaveTextContent('2');
    expect(screen.getByTestId('availableDate-0')).toHaveTextContent(
      '2023-01-22'
    );
    expect(screen.getByTestId('shortBy-0')).toHaveTextContent('0');
    expect(screen.getByTestId('flipped-0')).toHaveTextContent('No');
    expect(screen.getByTestId('note-0')).toHaveTextContent('Test note 1');
    expect(screen.getByTestId('orderNo-1')).toHaveTextContent('ORD-002');
    expect(screen.getByTestId('customer-1')).toHaveTextContent('Customer B');
    expect(screen.getByTestId('rented-1')).toHaveTextContent('15');
    expect(screen.getByTestId('deliveryDate-1')).toHaveTextContent(
      '2023-02-10'
    );
    expect(screen.getByTestId('pickupDate-1')).toHaveTextContent('2023-02-15');
    expect(screen.getByTestId('cleanupDays-1')).toHaveTextContent('1');
    expect(screen.getByTestId('availableDate-1')).toHaveTextContent(
      '2023-02-16'
    );
    expect(screen.getByTestId('shortBy-1')).toHaveTextContent('2');
    expect(screen.getByTestId('flipped-1')).toHaveTextContent('Yes');
    expect(screen.getByTestId('note-1')).toHaveTextContent('Test note 2');
  });

  it('formats date fields using formatDate utility', () => {
    const formatUtility = render(<SubRendererTable data={mockData} />);
    expect(formatUtility);
  });

  it('handles empty data array', () => {
    render(<SubRendererTable data={[]} />);
    const rows = screen.queryAllByTestId('row');
    expect(rows).toHaveLength(0);
  });

  it('passes correct props to DataTable', () => {
    const passesProps = render(<SubRendererTable data={mockData} />);
    expect(passesProps);
  });

  it('initializes with default sorting by orderNo descending', () => {
    const defaultSorting = render(<SubRendererTable data={mockData} />);
    expect(defaultSorting);
  });
});
