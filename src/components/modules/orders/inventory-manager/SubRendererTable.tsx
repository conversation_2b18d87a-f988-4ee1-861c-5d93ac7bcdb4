import DataTable from '@/components/common/data-tables';
import { formatDate } from '@/lib/utils';
import { SortingStateType } from '@/types/common.types';
import { InventoryManagerChildRowTypes } from '@/types/inventory-manager.type';
import { ColumnDef } from '@tanstack/react-table';
import { useMemo, useState } from 'react';

const SubRendererTable = ({ data }: { data: any[] }) => {
  const [sorting, setSorting] = useState<SortingStateType[]>([
    { id: 'orderNo', desc: true },
  ]);

  const subChildColumns: ColumnDef<InventoryManagerChildRowTypes>[] =
    useMemo(() => {
      return [
        {
          accessorKey: 'orderNo',
          header: 'Order #',
          enableSorting: true,
          invertSorting: true,
        },
        {
          accessorKey: 'customer',
          header: 'Customer',
          enableSorting: true,
          invertSorting: true,
        },
        {
          accessorKey: 'rented',
          header: 'Rented',
          enableSorting: true,
          invertSorting: true,
        },
        {
          accessorKey: 'deliveryDate',
          header: 'Delivery Date',
          enableSorting: true,
          invertSorting: true,
          size: 155,
          cell: ({ row }) => formatDate(row?.original?.deliveryDate),
        },
        {
          accessorKey: 'pickupDate',
          header: 'Pickup Date',
          enableSorting: true,
          invertSorting: true,
          size: 155,
          cell: ({ row }) => formatDate(row?.original?.pickupDate),
        },
        {
          accessorKey: 'cleanupDays',
          header: 'Cleanup Days',
          enableSorting: true,
          invertSorting: true,
          size: 155,
        },
        {
          accessorKey: 'availableDate',
          header: 'Available Date',
          enableSorting: true,
          invertSorting: true,
          size: 160,
          cell: ({ row }) => formatDate(row?.original?.availableDate),
        },
        {
          accessorKey: 'shortBy',
          header: 'Short',
          enableSorting: true,
          invertSorting: true,
        },
        {
          accessorKey: 'flipped',
          header: 'Flipped',
          enableSorting: true,
          invertSorting: true,
        },
        {
          accessorKey: 'note',
          header: 'Note',
          enableSorting: true,
          invertSorting: true,
          maxSize: 160,
        },
      ];
    }, []);
  return (
    <DataTable
      columns={subChildColumns}
      data={data || []}
      totalItems={data?.length}
      sorting={sorting}
      setSorting={setSorting}
      enablePagination={false}
      manualSorting={false}
      headerClassName="z-[1]"
      tableClassName="min-h-[100px] max-h-[200px] lg:max-h-[300px] overflow-auto"
    />
  );
};

export default SubRendererTable;
