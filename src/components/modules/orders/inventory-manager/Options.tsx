import AppButton from '@/components/common/app-button';
import CustomDialog from '@/components/common/dialog';
import <PERSON><PERSON><PERSON>ield from '@/components/common/switch';
import MultiCheckboxDropdown from '@/components/forms/MulitCheckbox';
import {
  CATEGORY_API_ROUTES,
  DEPARTMENT_API_ROUTES,
} from '@/constants/api-constants';
import useOptionList from '@/hooks/useOptionList';
import { OptionformTyps, OptionListType } from '@/types/inventory-manager.type';
import { SettingsIcon } from 'lucide-react';
import { memo, useCallback, useEffect, useMemo, useState } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';

const InventoryManagerOptions = ({
  onClickOptionFilter,
}: {
  onClickOptionFilter: (filters: OptionformTyps) => void;
}) => {
  const [open, setOpen] = useState<boolean>(false);

  // Initialize form hook
  const defaultValues = useMemo(() => {
    return {
      includeQuotes: false,
      includeSalesItemsOnRentals: false,
      checkOnlyAcrossAllLocations: false,
      sortByDeliveryDate: false,
      displayByCategory: true,
      category: [],
      department: [],
    };
  }, []);
  const form = useForm<OptionformTyps>({ defaultValues });

  useEffect(() => {
    if (defaultValues) {
      form.reset(defaultValues);
    }
  }, [defaultValues, form]);

  // otggle Option
  const toggleOpen = () => {
    setOpen((prev) => !prev);
  };

  const OptionList: OptionListType[] = [
    { label: 'Include Quotes', name: 'includeQuotes' },
    {
      label: 'Include Sales Items on Rentals',
      name: 'includeSalesItemsOnRentals',
    },
    {
      label: 'Check Only Across All Locations',
      name: 'checkAcrossAllLocation',
    },
    { label: 'Sort by Delivery Date', name: 'sortByDeliveryDate' },
    { label: 'Display by Category', name: 'displayByCategory' },
  ];

  // Departments List
  const { options: departmetList, optionLoading: departmetLoading } =
    useOptionList({
      url: DEPARTMENT_API_ROUTES.ALL,
      labelKey: 'deptDesc',
      valueKey: 'id',
      sortBy: 'deptDesc',
      skip: !open,
    });

  // Linen Categories List
  const { options: categoryList, optionLoading } = useOptionList({
    url: CATEGORY_API_ROUTES.ALL,
    labelKey: 'catDesc',
    valueKey: 'id',
    sortBy: 'catDesc',
    skip: !open,
  });

  // on submit bulk edit
  const onSubmit: SubmitHandler<OptionformTyps> = useCallback(
    async (formData) => {
      onClickOptionFilter(formData);
      toggleOpen();
    },
    [onClickOptionFilter]
  );

  const handleCancel = useCallback(() => {
    toggleOpen();
    form.reset();
  }, [form]);

  const isDisplayByCategory = form.watch('displayByCategory');

  return (
    <>
      <AppButton
        label="Options"
        icon={SettingsIcon}
        iconClassName="w-5 h-5"
        className="bg-brand-teal-Default hover:bg-brand-teal-Default/85"
        onClick={toggleOpen}
      />
      <CustomDialog
        onOpenChange={handleCancel}
        description=""
        open={open}
        className="max-h-[96%] overflow-y-auto min-w-72"
        title="Options"
      >
        <>
          <div className="px-6 py-4 pt-2 grid grid-cols-2 gap-4">
            <div className="col-span-2 space-y-3 mb-3 ms-[-8px]">
              {OptionList?.map((option, index) => (
                <SwitchField
                  key={`${option?.name}-${index}`}
                  label={option.label}
                  form={form}
                  name={option.name}
                  className="ml-2"
                  onChange={(value) => {
                    form.setValue(
                      value && option?.name === 'displayByCategory'
                        ? 'departments'
                        : 'categories',
                      ''
                    );
                  }}
                />
              ))}
            </div>
            <MultiCheckboxDropdown
              name="departments"
              form={form}
              optionsList={departmetList ?? []}
              placeholder={'Departments'}
              label="Departments"
              className="w-full"
              isLoading={departmetLoading}
              disabled={isDisplayByCategory}
            />
            <MultiCheckboxDropdown
              name="categories"
              form={form}
              optionsList={categoryList ?? []}
              placeholder={'Categories'}
              label="Linen Categories"
              className="w-full"
              isLoading={optionLoading}
              disabled={!isDisplayByCategory}
            />
          </div>
          <div className="flex items-center justify-end gap-3 px-6 mt-5 pb-2">
            <AppButton
              label="Ok"
              className="w-32"
              onClick={form.handleSubmit(onSubmit)}
            />
            <AppButton
              label="Cancel"
              className="w-32"
              variant="neutral"
              onClick={handleCancel}
            />
          </div>
        </>
      </CustomDialog>
    </>
  );
};

export default memo(InventoryManagerOptions);
