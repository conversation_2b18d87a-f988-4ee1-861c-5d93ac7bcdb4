import AppButton from '@/components/common/app-button';
import DataTable from '@/components/common/data-tables';
import CustomDialog from '@/components/common/dialog';
import DatePicker from '@/components/forms/date-picker';
import NumberInputField from '@/components/forms/number-input-field';
import SelectWidget from '@/components/forms/select';
import { TEXT_VALIDATION_RULE } from '@/constants/validation-constants';
import { convertToFloat, getPaginationObject } from '@/lib/utils';
import { useGetBulkItemsQuery } from '@/redux/features/items/item.api';
import { PaginationType, SortingStateType } from '@/types/common.types';
import { RowSelectionState } from '@tanstack/react-table';
import isEqual from 'lodash/isEqual';
import { TruckIcon } from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { SubmitHandler, useFieldArray, useForm } from 'react-hook-form';

interface NewPurchaseOrderTableListTypes {
  id: number;
  itemId: string;
  description: string;
  unitPrice: number;
  quantity: number;
  purchaseQty: number | string;
}
interface InventoryManagerNewPurchaseOrderTypes {
  deliveryDate: string;
  vendorId: string;
  items: NewPurchaseOrderTableListTypes[];
}

const InventoryManagerNewPurchaseOrder = ({
  deliveryDate,
}: {
  deliveryDate: string;
}) => {
  const [open, setOpen] = useState<boolean>(false);
  const [pagination] = useState<PaginationType>({
    pageSize: 100,
    pageIndex: 0,
  });
  const [sorting, setSorting] = useState<SortingStateType[]>([
    { id: 'itemId', desc: false },
  ]);
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});

  // otggle Option
  const toggleOpen = () => {
    setOpen((prev) => !prev);
  };

  const payload = useMemo(() => {
    return getPaginationObject({
      pagination: pagination,
      sorting: sorting,
      filters: [],
    });
  }, [pagination, sorting]);

  const { data: getAllData } = useGetBulkItemsQuery(payload);

  // Initialize form hook
  const defaultValues = useMemo(() => {
    return {
      anticipatedDeliveryDate: deliveryDate,
      vendorId: '2',
      items: getAllData?.data?.map((item) => ({
        id: item?.id,
        itemId: item?.itemId,
        description: item?.description || '',
        unitPrice: item?.unitPrice || '',
        quantity: item?.quantity || '',
        purchaseQty: item?.unitPrice || '',
      })),
    };
  }, [deliveryDate, getAllData?.data]);

  const form = useForm<InventoryManagerNewPurchaseOrderTypes>({
    defaultValues,
  });
  const { fields } = useFieldArray<InventoryManagerNewPurchaseOrderTypes>({
    control: form.control,
    name: 'items',
  });

  useEffect(() => {
    if (defaultValues) {
      form.reset(defaultValues);
    }
  }, [defaultValues, form]);

  // handle cancel a& reset the form
  const handleCancel = useCallback(() => {
    toggleOpen();
    form.reset();
    setRowSelection({});
  }, [form]);

  // clear purchase Qty
  const handleClearPurchaseQty = () => {
    form.setValue(
      'items',
      fields.map((field) => ({ ...field, purchaseQty: '' })),
      { shouldDirty: true, shouldTouch: true }
    );
  };

  const rowSelectionId = Object.keys(rowSelection).at(0);
  const columns = useMemo(() => {
    const isEdit = (id: number) => id === Number(rowSelectionId);
    return [
      {
        accessorKey: 'itemId',
        header: 'Item ID',
        enableSorting: true,
        size: 200,
        maxSize: 200,
      },
      {
        accessorKey: 'description',
        header: 'Item Description',
        enableSorting: true,
        size: 200,
        maxSize: 200,
      },
      {
        accessorKey: 'unitPrice',
        header: 'Unit Price',
        enableSorting: true,
        size: 170,
        cell: ({ row }: any) =>
          isEdit(row.index) ? (
            <NumberInputField
              form={form}
              placeholder="Quantity"
              name={`items.${row?.index}.unitPrice`}
              className="w-full h-8"
              maxLength={13}
              prefix="$"
              fixedDecimalScale
              validation={TEXT_VALIDATION_RULE}
            />
          ) : (
            convertToFloat({ value: row?.original.unitPrice, prefix: '$' })
          ),
      },
      {
        accessorKey: 'quantity',
        header: 'Qty Short',
        enableSorting: true,
      },
      {
        accessorKey: 'purchaseQty',
        header: 'Purchase Qty',
        enableSorting: true,
        size: 150,
        cell: ({ row }: any) =>
          isEdit(row.index) ? (
            <NumberInputField
              form={form}
              placeholder="Quantity"
              name={`items.${row?.index}.purchaseQty`}
              className="w-full h-8"
              maxLength={10}
              decimalScale={0}
              validation={TEXT_VALIDATION_RULE}
            />
          ) : (
            row.original.purchaseQty
          ),
      },
    ];
  }, [form, rowSelectionId]);

  // on submit
  const onSubmit: SubmitHandler<InventoryManagerNewPurchaseOrderTypes> =
    useCallback(
      async (formData) => {
        const payload = formData?.items?.filter(
          (_, index: number) => index === Number(rowSelectionId)
        );
        // eslint-disable-next-line no-console
        console.log('payload', payload);
      },
      [rowSelectionId]
    );

  // Check if form is modified
  const isFormModified = isEqual(form.watch(), defaultValues);
  return (
    <>
      <AppButton
        label="New Purchase Order"
        icon={TruckIcon}
        iconClassName="w-5 h-5"
        className="bg-brand-teal-Default hover:bg-brand-teal-Default/85"
        onClick={toggleOpen}
        disabled
      />

      <CustomDialog
        onOpenChange={handleCancel}
        description=""
        open={open}
        className="min-w-[80%] xl:min-w-[60%]"
        contentClassName="h-[430px] xl:h-[500px]"
        title="New Purchase Order"
      >
        <div className="px-6 space-y-3">
          <div className="grid grid-cols-3 gap-4 h-[90px]">
            <DatePicker
              form={form}
              name="anticipatedDeliveryDate"
              label="Anticipated Delivery Date"
              placeholder="Select Date"
              enableInput
              validation={TEXT_VALIDATION_RULE}
            />
            <SelectWidget
              form={form}
              optionsList={[]}
              name="vendorId"
              label="Vendor"
              placeholder="Select Vendor"
              // isLoading={locationLoading}
              menuPosition="absolute"
              isClearable={false}
              className="z-[20]"
              validation={TEXT_VALIDATION_RULE}
            />

            <AppButton
              label="Clear Purchase Qty"
              className="w-fit mt-8 bg-brand-teal-Default hover:bg-brand-teal-Default/85"
              onClick={handleClearPurchaseQty}
            />
          </div>
          <div className="grid grid-cols-1">
            <DataTable
              columns={columns}
              data={fields || []}
              totalItems={fields?.length}
              sorting={sorting}
              setSorting={setSorting}
              enableRowSelection
              rowSelection={rowSelection}
              onRowSelectionChange={setRowSelection}
              enablePagination={false}
              tableClassName="max-h-[260px] lg:max-h-[330px] overflow-auto"
            />
          </div>
          <div className="absolute bottom-3 right-6 space-x-4">
            <AppButton
              label="Ok"
              className="w-32"
              disabled={isFormModified || !rowSelectionId}
              onClick={form.handleSubmit(onSubmit)}
            />
            <AppButton
              label="Cancel"
              className="w-32"
              variant="neutral"
              onClick={handleCancel}
            />
          </div>
        </div>
      </CustomDialog>
    </>
  );
};

export default InventoryManagerNewPurchaseOrder;
