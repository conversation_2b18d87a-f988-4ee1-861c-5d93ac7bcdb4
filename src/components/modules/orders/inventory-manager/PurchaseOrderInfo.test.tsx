import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import PurchaseOrderInfo from './PurchaseOrderInfo';
import { useGetBulkItemsQuery } from '@/redux/features/items/item.api';

// Mock the DataTable component to verify props and render simplified output
vi.mock('@/components/common/data-tables', () => ({
  default: vi.fn(({ columns, data }) => (
    <div data-testid="datatable-mock">
      <div data-testid="columns">
        {columns.map((col: any) => (
          <span key={col.accessorKey}>{col.header}</span>
        ))}
      </div>
      <div data-testid="rows">
        {data?.map((item: any, index: number) => (
          <div key={index} data-testid="row">
            <span data-testid={`itemId-${index}`}>{item.itemId}</span>
            <span data-testid={`vendor-${index}`}>{item.vendor}</span>
            <span data-testid={`qtyOrdered-${index}`}>{item.qtyOrdered}</span>
            <span data-testid={`deliveryDate-${index}`}>
              {item.anticipatedDeliveryDate}
            </span>
          </div>
        ))}
      </div>
    </div>
  )),
}));

// Mock API call
vi.mock('@/redux/features/items/item.api', () => ({
  useGetBulkItemsQuery: vi.fn(),
}));

describe('PurchaseOrderInfo Component', () => {
  const mockData = [
    {
      itemId: 'PO-1001',
      vendor: 'Vendor A',
      qtyOrdered: 50,
      anticipatedDeliveryDate: '2023-12-15',
    },
    {
      itemId: 'PO-1002',
      vendor: 'Vendor B',
      qtyOrdered: 120,
      anticipatedDeliveryDate: '2023-12-20',
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    (useGetBulkItemsQuery as any).mockReturnValue({
      data: { data: mockData },
      isLoading: false,
      isError: false,
    });
  });

  it('renders without crashing', () => {
    render(<PurchaseOrderInfo />);
    expect(screen.getByTestId('datatable-mock')).toBeInTheDocument();
  });

  it('displays correct column headers', () => {
    render(<PurchaseOrderInfo />);
    expect(screen.getByText('P.O. #')).toBeInTheDocument();
    expect(screen.getByText('Vendor')).toBeInTheDocument();
    expect(screen.getByText('Qty Ordered')).toBeInTheDocument();
    expect(screen.getByText('Anticipated Delivery Date')).toBeInTheDocument();
  });

  it('renders correct data rows', () => {
    render(<PurchaseOrderInfo />);
    const rows = screen.getAllByTestId('row');
    expect(rows).toHaveLength(2);
    expect(screen.getByTestId('itemId-0')).toHaveTextContent('PO-1001');
    expect(screen.getByTestId('vendor-0')).toHaveTextContent('Vendor A');
    expect(screen.getByTestId('qtyOrdered-0')).toHaveTextContent('50');
    expect(screen.getByTestId('deliveryDate-0')).toHaveTextContent(
      '2023-12-15'
    );
    expect(screen.getByTestId('itemId-1')).toHaveTextContent('PO-1002');
    expect(screen.getByTestId('vendor-1')).toHaveTextContent('Vendor B');
    expect(screen.getByTestId('qtyOrdered-1')).toHaveTextContent('120');
    expect(screen.getByTestId('deliveryDate-1')).toHaveTextContent(
      '2023-12-20'
    );
  });

  it('handles empty data state', () => {
    (useGetBulkItemsQuery as any).mockReturnValue({
      data: { data: [] },
      isLoading: false,
      isError: false,
    });
    render(<PurchaseOrderInfo />);
    expect(screen.queryAllByTestId('row')).toHaveLength(0);
  });

  it('shows loading state when data is loading', () => {
    (useGetBulkItemsQuery as any).mockReturnValue({
      data: undefined,
      isLoading: true,
      isError: false,
    });
    const isLoading = render(<PurchaseOrderInfo />);
    expect(isLoading);
  });

  it('handles error state', () => {
    (useGetBulkItemsQuery as any).mockReturnValue({
      data: undefined,
      isLoading: false,
      isError: true,
    });
    const checkError = render(<PurchaseOrderInfo />);
    expect(checkError);
  });

  it('passes correct props to DataTable', () => {
    render(<PurchaseOrderInfo />);
    const columns = screen.getByTestId('columns');
    expect(columns.children).toHaveLength(4);
    const rows = screen.getByTestId('rows');
    expect(rows.children).toHaveLength(2);
  });

  it('maintains default sorting state', () => {
    const defaultSorting = render(<PurchaseOrderInfo />);
    expect(defaultSorting);
  });
});
