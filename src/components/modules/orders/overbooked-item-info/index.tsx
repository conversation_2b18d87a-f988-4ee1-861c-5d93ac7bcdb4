import CheveronLeft from '@/assets/icons/CheveronLeft';
import AppButton from '@/components/common/app-button';
import IconButton from '@/components/common/icon-button';
import { ROUTES } from '@/constants/routes-constants';
import { X } from 'lucide-react';
import { useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import OverbookedItemInfo from '../item-details/overbooked-item-info';

const OverbookedItemInfoPage = () => {
  const navigation = useNavigate();

  // navigate to back
  const navigateToOrder = useCallback(() => {
    navigation(ROUTES.ORDERS);
  }, [navigation]);

  // Header section
  const Header = () => {
    return (
      <div className="flex gap-x-4 items-center justify-between sticky top-16 pt-3 pb-2 bg-white z-20 mb-4">
        <div className="flex items-center gap-3">
          <IconButton onClick={navigateToOrder}>
            <CheveronLeft />
          </IconButton>
          <h1 className="text-2xl font-semibold">Overbooked Item Info</h1>
        </div>

        <AppButton
          label="Cancel"
          icon={X}
          onClick={navigateToOrder}
          iconClassName="w-4 h-4"
          variant="neutral"
        />
      </div>
    );
  };

  return (
    <div className="px-6">
      <Header />
      <OverbookedItemInfo isModal={false} />
    </div>
  );
};

export default OverbookedItemInfoPage;
