import { ORDERS_API_ROUTES } from '@/constants/api-constants';
import {
  DATE_FORMAT_YYYYMMDD,
  DATE_TIME_FORMAT,
  // DATE_TIME_FORMAT,
  formatDate,
  formatPhoneNumber,
  formatTime,
  getDayInitial,
} from '@/lib/utils';
import { OrderInformationTypes, ORDERT_TYPE_TAB } from '@/types/order.types';

// ORDERS TAB ENUM DEFINED
export enum ORDERS_ENUM {
  INFORMATION = 'information',
  OPTIONS = 'options',
  ITEM_DETAILS = 'item-details',
  TOTAL_PAYMENTS = 'total-payments',
  STATUS = 'status',
  ADDITIONAL_INFO = 'additional-info',
  SALES_REFERRALS = 'sales-referrals',
}

interface TAB_TYPE {
  CREATE?: string;
  UPDATE: string;
}
export interface AllTabTypeMap {
  information: TAB_TYPE;
  options: TAB_TYPE;
}
// API route mapping for each tab
export const ORDERS_API_ROUTES_MAP: AllTabTypeMap = {
  information: {
    CREATE: ORDERS_API_ROUTES.CREATE,
    UPDATE: ORDERS_API_ROUTES.UPDATE,
  },
  options: {
    UPDATE: ORDERS_API_ROUTES?.UPDATE_OPTIONS,
    CREATE: ORDERS_API_ROUTES?.UPDATE_OPTIONS,
  },
};

export const generateOrdersDefaultValues = (
  orderDetails: OrderInformationTypes
): any => {
  const {
    deliveryOrder,
    shipOrder,
    willCallOrder,
    billTo,
    shipTo,
    storeDateCalculation,
    ...orderInfo
  } = orderDetails || {};

  const selectedTab = () => {
    if (deliveryOrder?.id) {
      return ORDERT_TYPE_TAB.DELIVER;
    } else if (willCallOrder?.id) {
      return ORDERT_TYPE_TAB.WILL_CALL;
    } else {
      return ORDERT_TYPE_TAB.SHIP;
    }
  };

  return {
    ...orderInfo,
    id: orderInfo?.id || null,
    selectedOrderType: selectedTab(),
    eventFrequency: orderInfo?.eventFrequency ?? 'ONE_TIME',
    updatedAt: formatDate(orderInfo?.updatedAt, DATE_TIME_FORMAT),
    rentDays: orderInfo?.rentDays ?? '',
    deliveryOrder: {
      ...deliveryOrder,
      arrivalDay: getDayInitial({ date: deliveryOrder?.arrivalDate }),
    },
    shipOrder: {
      ...shipOrder,
      shipDay: getDayInitial({ date: shipOrder?.shipDate }),
      returnArrivalDay: getDayInitial({ date: shipOrder?.returnArrivalDate }),
    },
    willCallOrder: {
      ...willCallOrder,
    },
    billTo: {
      ...billTo,
      customerId: billTo?.customerId
        ? { label: billTo?.name, value: billTo?.customerId }
        : null,
      customerPhone: {
        label: formatPhoneNumber(billTo?.customerPhone as unknown as string),
        value: billTo?.customerPhone,
      },
      orderedBy: billTo?.orderedBy
        ? {
            label: billTo?.orderedBy ?? '',
            value: billTo?.orderedBy ?? '',
          }
        : null,
      orderPhone: billTo?.orderPhone
        ? { label: billTo?.orderPhone ?? '', value: billTo?.orderPhone ?? '' }
        : null,
    },
    shipTo: {
      ...shipTo,
      shipLocation: shipTo?.shipLocation
        ? {
            label: shipTo?.shipLocation,
            value: shipTo?.shipLocation,
          }
        : null,
      phone: {
        label: formatPhoneNumber(shipTo?.phone as unknown as string),
        value: shipTo?.phone,
      },
      countryId:
        shipTo?.countryId ??
        (orderInfo?.userDefaultStoreInfo?.storeCountryId || 1),
      contactPhone: shipTo?.contactPhone || '',
    },
    storeDateCalculation: { ...storeDateCalculation },
  };
};

export const createPayload = (
  tabName: string,
  formData: OrderInformationTypes
): any => {
  const {
    deliveryOrder,
    shipOrder,
    willCallOrder,
    billTo,
    shipTo,
    selectedOrderType,
    updatedAt,
    originType,
    userDefaultStoreInfo,
    checkList,
    ...orderData
  } = formData as any;

  switch (tabName) {
    case ORDERS_ENUM.INFORMATION:
      return {
        ...orderData,
        revision: orderData?.revision || 0,
        orderType: orderData?.orderType || null,
        dateOfUseFrom:
          formatDate(orderData?.dateOfUseFrom, DATE_FORMAT_YYYYMMDD) || null,
        dateOfUseThru:
          formatDate(orderData?.dateOfUseThru, DATE_FORMAT_YYYYMMDD) || null,
        timeOfUseFrom: orderData?.timeOfUseFrom
          ? formatTime(orderData?.timeOfUseFrom)
          : null,
        timeOfUseThru: orderData?.timeOfUseThru
          ? formatTime(orderData?.timeOfUseThru)
          : null,
        dayOfUseFrom: orderData?.dayOfUseFrom || null,
        dayOfUseThru: orderData?.dayOfUseThru || null,
        location: orderData?.location || null,
        eventDescription: orderData?.eventDescription || null,
        deliveryTypeId: orderData?.deliveryTypeId ?? '',
        eventTypeId: orderData?.eventTypeId || null,
        eventFrequency: orderData?.eventFrequency || null,
        rentDays: Number(orderData?.rentDays) || 0,

        deliveryOrder:
          selectedOrderType === ORDERT_TYPE_TAB.DELIVER
            ? {
                ...deliveryOrder,
                deliveryDate: deliveryOrder?.deliveryDate
                  ? formatDate(
                      deliveryOrder?.deliveryDate,
                      DATE_FORMAT_YYYYMMDD
                    )
                  : null,
                deliveryDay: deliveryOrder?.deliveryDay || null,
                deliveryInfo: deliveryOrder?.deliveryInfo || null,
                pickupDate: deliveryOrder?.pickupDate
                  ? formatDate(deliveryOrder?.pickupDate, DATE_FORMAT_YYYYMMDD)
                  : null,
                pickupDay: deliveryOrder?.pickupDay || null,
                pickupInfo: deliveryOrder?.pickupInfo || null,
                pickupTimeIn: deliveryOrder?.pickupTimeIn
                  ? formatTime(deliveryOrder?.pickupTimeIn)
                  : null,
                pickupTimeOut: deliveryOrder?.pickupTimeOut
                  ? formatTime(deliveryOrder?.pickupTimeOut)
                  : null,
                deliveryTimeIn: deliveryOrder?.deliveryTimeIn
                  ? formatTime(deliveryOrder?.deliveryTimeIn)
                  : null,
                deliveryTimeOut: deliveryOrder?.deliveryTimeOut
                  ? formatTime(deliveryOrder?.deliveryTimeOut)
                  : null,
                arrivalDate: deliveryOrder?.arrivalDate
                  ? formatDate(deliveryOrder?.arrivalDate, DATE_FORMAT_YYYYMMDD)
                  : null,
                returnShipDate: deliveryOrder?.returnShipDate
                  ? formatDate(
                      deliveryOrder?.returnShipDate,
                      DATE_FORMAT_YYYYMMDD
                    )
                  : null,
              }
            : null,

        shipOrder:
          selectedOrderType === ORDERT_TYPE_TAB.SHIP
            ? {
                ...shipOrder,
                orderSetup: shipOrder?.orderSetup || null,
                takedown: shipOrder?.takedown || null,
                shipDate: shipOrder?.shipDate
                  ? formatDate(shipOrder?.shipDate, DATE_FORMAT_YYYYMMDD)
                  : null,
                shipDay: shipOrder?.shipDay || null,
                shipInfo: shipOrder?.shipInfo || null,
                shipTimeIn: shipOrder?.shipTimeIn
                  ? formatTime(shipOrder?.shipTimeIn)
                  : null,
                shipTimeOut: shipOrder?.shipTimeOut
                  ? formatTime(shipOrder?.shipTimeOut)
                  : null,
                returnTimeIn: shipOrder?.returnTimeIn
                  ? formatTime(shipOrder?.returnTimeIn)
                  : null,
                returnTimeOut: shipOrder?.returnTimeOut
                  ? formatTime(shipOrder?.returnTimeOut)
                  : null,
                returnInfo: shipOrder?.returnInfo || null,
                returnArrivalDate: shipOrder?.returnArrivalDate
                  ? formatDate(
                      shipOrder?.returnArrivalDate,
                      DATE_FORMAT_YYYYMMDD
                    )
                  : null,
                returnArrivalDay: shipOrder?.returnArrivalDay || null,
                arrivalDate: shipOrder?.arrivalDate
                  ? formatDate(shipOrder?.arrivalDate, DATE_FORMAT_YYYYMMDD)
                  : null,
                arrivalDay: shipOrder?.arrivalDay || null,
                returnShipDate: shipOrder?.returnShipDate || null,
                returnShipDay: shipOrder?.returnShipDay || null,
              }
            : null,

        willCallOrder:
          selectedOrderType === ORDERT_TYPE_TAB.WILL_CALL
            ? {
                ...willCallOrder,
                orderSetup: willCallOrder?.orderSetup || null,
                takedown: willCallOrder?.takedown || null,
                pickupDate: willCallOrder?.pickupDate
                  ? formatDate(willCallOrder?.pickupDate, DATE_FORMAT_YYYYMMDD)
                  : null,
                pickupDay: willCallOrder?.pickupDay || null,
                pickupInfo: willCallOrder?.pickupInfo || null,
                pickupTimeIn: willCallOrder?.pickup
                  ? formatTime(willCallOrder?.pickupTimeIn)
                  : null,
                pickupTimeOut: willCallOrder?.pickupTimeOut
                  ? formatTime(willCallOrder?.pickupTimeOut)
                  : null,
                returnInfo: willCallOrder?.returnInfo || null,
                returnDate: willCallOrder?.returnDate
                  ? formatDate(willCallOrder?.returnDate, DATE_FORMAT_YYYYMMDD)
                  : null,
                returnDay: willCallOrder?.returnDay || null,
                returnArrivalDay: willCallOrder?.returnArrivalDay || null,
                returnTimeIn: willCallOrder?.returnTimeIn
                  ? formatTime(willCallOrder?.returnTimeIn)
                  : null,
                returnTimeOut: willCallOrder?.returnTimeOut
                  ? formatTime(willCallOrder?.returnTimeOut)
                  : null,
                arrivalDate: willCallOrder?.arrivalDate
                  ? formatDate(willCallOrder?.arrivalDate, DATE_FORMAT_YYYYMMDD)
                  : null,
                arrivalDay: willCallOrder?.arrivalDay || null,
                returnShipDate: willCallOrder?.returnShipDate
                  ? formatDate(
                      willCallOrder?.returnShipDate,
                      DATE_FORMAT_YYYYMMDD
                    )
                  : null,
                returnShipDay: willCallOrder?.returnShipDay || null,
              }
            : null,

        billTo: {
          ...billTo,
          customerId: billTo?.customerId?.value || null,
          customerPhone: billTo?.customerPhone?.value,
          customerContactId: billTo?.customerContactId || null,
          orderEmail: billTo?.orderEmail || null,
          orderedBy: billTo?.orderedBy?.value ?? '',
          orderPhone: billTo?.orderPhone?.value || null,
          address: billTo?.address || null,
          address2: billTo?.address2 || null,
          city: billTo?.city || null,
          stateId: billTo?.stateId || null,
          countryId: billTo?.countryId || null,
          zipCode: billTo?.zipCode || null,
          customerInstructions: billTo?.customerInstructions || null,
        },
        shipTo: {
          ...shipTo,
          shipLocation: shipTo?.shipLocation?.value || null,
          phone: shipTo?.phone?.value || null,
          contact: shipTo?.contact || null,
          contactEmail: shipTo?.contactEmail || null,
          contactPhone: shipTo?.contactPhone || null,
          address1: shipTo?.address1 || null,
          address2: shipTo?.address2 || null,
          city: shipTo?.city || null,
          stateId: shipTo?.stateId || null,
          countryId: shipTo?.countryId || null,
          zipCode: shipTo?.zipCode || null,
          additionalInstructions: shipTo?.additionalInstructions || null,
        },
      };

    default:
      null;
  }
};

// Filter constants
export const operatorType = [
  { value: 'Contains', label: 'Contains' },
  { value: 'equals', label: 'Equals' },
  { value: 'startswith', label: 'Starts With' },
];

export const rangeFilterList = [
  { value: 'current', label: 'Current' },
  { value: 'future', label: 'Future' },
  // { value: 'past15days', label: 'Past 15 days' },
  { value: 'past30days', label: 'Past 30 days' },
  { value: 'past60days', label: 'Past 60 days' },
  { value: 'past90days', label: 'Past 90 days' },
  // { value: 'past180days', label: 'Past 180 days' },
  { value: 'past1year', label: '1 year' },
  { value: 'currentyear', label: 'Year-to-date' },
  { value: 'custom', label: 'Custom' },
  { value: 'archived', label: 'Archived' },
];

export enum FILTER_CUSTOM {
  CUSTOM = 'custom',
}

export enum DATE_TYPE {
  DATE_OF_USE = 'Date of Use',
  DELIVERY_DATE = 'Delivery Date',
  PICKUP_DATE = 'Pickup Date',
  ORDER_DATE = 'Order Date',
}

export const dateTypeList = [
  {
    value: 'dateOfUseFrom',
    label: 'Date of Use',
  },
  {
    value: 'deliveryDate',
    label: 'Delivery Date',
  },
  {
    value: 'pickupDate',
    label: 'Pickup Date',
  },
  {
    value: 'orderDate',
    label: 'Order Date',
  },
];

export const searchByFilters = [
  {
    value: 'customer',
    label: 'Customer',
  },
  {
    value: 'eventDescription',
    label: 'Event Description',
  },
  {
    value: 'total',
    label: 'Total',
  },
  {
    value: 'shipLocation',
    label: 'Ship Location',
  },
  {
    value: 'address',
    label: 'Address',
  },
  {
    value: 'address2',
    label: 'Address 2',
  },
  {
    value: 'town',
    label: 'Town',
  },
  {
    value: 'state',
    label: 'State',
  },
  {
    value: 'zipCode',
    label: 'Zip Code',
  },

  {
    value: 'orderedBy',
    label: 'Ordered By',
  },
  {
    value: 'purchaseOrderNo',
    label: 'P.O. #',
  },
  {
    value: 'salesperson',
    label: 'Salesperson',
  },
  {
    value: 'enteredBy',
    label: 'Entered By',
  },
  {
    value: 'deliveryOption',
    label: 'Delivery Option',
  },
  {
    value: 'customerType',
    label: 'Customer Type',
  },
  {
    value: 'webOrderNo',
    label: 'Web Order #',
  },
];

export enum ORDER_TYPE {
  SALES_ORDER = 'SALES_ORDER',
  RENTAL_ORDER = 'RENTAL_ORDER',
  RENTAL_QUOTE = 'RENTAL_QUOTE',
  SALES_QUOTE = 'SALES_QUOTE',
}

export interface OpenDialogType {
  state: boolean;
  action: string;
  data?: any;
}

export const CustomerSearchStatus = [
  { label: 'All', value: 'all' },
  { label: 'Active', value: 'true' },
  { label: 'Inactive', value: 'false' },
];

export const CustomerSearchTypeLists = [
  { value: 'full_name', label: 'Name' },
  { value: 'custtype', label: 'Type' },
  { value: 'tel1', label: 'Phone' },
  { value: 'city', label: 'City' },
  { value: 'state', label: 'State' },
];

export enum BoxType {
  SHIPPING = 'SHIPPING',
  RETURN = 'RETURN',
}
