import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import OrderInformation from './index';
import { UseFormReturn } from 'react-hook-form';
import { OrderInformationTypes } from '@/types/order.types';

// Mock useWatch from react-hook-form
vi.mock('react-hook-form', async () => {
  const actual = await vi.importActual('react-hook-form');
  return {
    ...actual,
    useWatch: vi.fn().mockImplementation(({ name }) => {
      const values = {
        selectedOrderType: 'SHIP',
        originType: 'STANDARD',
        orderType: 'RENTAL_QUOTE',
      } as any;
      return values[name] as any;
    }),
  };
});

// Mock API hooks - Order API
vi.mock('@/redux/features/orders/order.api', () => ({
  useDateCalculationMutation: vi.fn().mockReturnValue([
    vi.fn().mockResolvedValue({
      data: {
        calculatedDate: '2023-01-01',
        success: true,
      },
    }),
    {
      isLoading: false,
      error: null,
      data: null,
    },
  ]),
  useLazyGetBusyStatusByDateQuery: vi.fn().mockReturnValue([
    vi.fn().mockResolvedValue({
      data: {
        isBusy: false,
        availableSlots: [],
      },
    }),
    {
      isLoading: false,
      error: null,
      data: null,
    },
  ]),
}));

// Mock Store API
vi.mock('@/redux/features/store/store.api', () => ({
  useLazyGetStoreQuery: vi.fn().mockReturnValue([
    vi.fn().mockResolvedValue({
      data: {
        id: '1',
        name: 'Test Store',
        location: 'Test Location',
        defaultDeliveryType: '1',
        defaultDeliveryTypeCall: '2',
      },
    }),
    {
      isLoading: false,
      error: null,
      data: null,
    },
  ]),
}));

// Mock Delivery Type API
vi.mock('@/redux/features/delivery-type/delivery-type.api', () => ({
  useGetDeliveryTypeQuery: vi.fn().mockReturnValue({
    data: [
      { id: 1, name: 'Standard Delivery', code: 'STANDARD' },
      { id: 2, name: 'Express Delivery', code: 'EXPRESS' },
      { id: 3, name: 'Will Call', code: 'WILL_CALL' },
    ],
    isLoading: false,
    error: null,
    refetch: vi.fn(),
  }),
}));

// Mock Enums API
vi.mock('@/redux/features/enums-api/enums-api', () => ({
  useGetEnumsListQuery: vi.fn().mockReturnValue({
    data: {
      orderTypes: [
        { value: 'RENTAL_QUOTE', label: 'Rental Quote' },
        { value: 'SALES_ORDER', label: 'Sales Order' },
        { value: 'SALES_QUOTE', label: 'Sales Quote' },
      ],
      originTypes: [
        { value: 'STANDARD', label: 'Standard' },
        { value: 'MISSING_ORDER', label: 'Missing Order' },
      ],
      deliveryTypes: [
        { value: 'SHIP', label: 'Ship' },
        { value: 'DELIVER', label: 'Deliver' },
        { value: 'WILL_CALL', label: 'Will Call' },
      ],
    },
    isLoading: false,
    error: null,
    refetch: vi.fn(),
  }),
}));

// Mock all the other dependencies
vi.mock('@/components/common/action-area');
vi.mock('@/components/forms/date-picker');
vi.mock('@/components/forms/input-field');
vi.mock('@/components/forms/number-input-field');
vi.mock('@/components/forms/select');
vi.mock('@/components/common/app-confirmation-modal');
vi.mock('@/components/forms/text-area');
vi.mock('@/hooks/useOptionList', () => ({
  default: vi.fn(() => ({
    options: [],
    loading: false,
    error: null,
  })),
}));

vi.mock('../files-orders-tab/useSetupTakedownList', () => ({
  default: vi.fn(() => ({
    setupList: [],
    takedownList: [],
    loading: false,
  })),
}));
vi.mock('../files-orders-tab/deliver');
vi.mock('../files-orders-tab/ship');
vi.mock('../files-orders-tab/willCall');
vi.mock('./billing-info');
vi.mock('./shippingInfo');

describe('OrderInformation', () => {
  const mockForm = {
    watch: vi.fn(),
    setValue: vi.fn(),
    getValues: vi.fn(),
    control: {
      _fields: {},
    },
  } as unknown as UseFormReturn<OrderInformationTypes>;

  beforeEach(() => {
    vi.clearAllMocks();

    // Reset form mocks
    (mockForm.watch as any).mockImplementation((field: any) => {
      const defaultValues: Partial<OrderInformationTypes> = {
        isDeleted: false,
        orderEntryReadOnly: false,
        originType: 'STANDARD',
        selectedOrderType: 'SHIP',
        storeLocationId: '1',
        orderType: 'RENTAL_QUOTE',
        deliveryTypeId: 1,
        dateOfUseFrom: '2023-01-01',
        dateOfUseThru: '2023-01-05',
        recalculateDate: false,
        updateData: false,
      };
      return field
        ? defaultValues[field as keyof OrderInformationTypes]
        : defaultValues;
    });

    (mockForm.getValues as any).mockReturnValue({
      isDeleted: false,
      orderEntryReadOnly: false,
      originType: 'STANDARD',
      selectedOrderType: 'SHIP',
      storeLocationId: '1',
      orderType: 'RENTAL_QUOTE',
      deliveryTypeId: 1,
      dateOfUseFrom: '2023-01-01',
      dateOfUseThru: '2023-01-05',
      userDefaultStoreInfo: {
        defaultDeliveryTypeCall: '2',
        defaultDeliveryType: '1',
      },
    });
  });

  it('renders the component with all sections', () => {
    render(<OrderInformation form={mockForm} />);
    expect(screen.getByText('Event Info')).toBeInTheDocument();
    expect(screen.getByText('Order Type')).toBeInTheDocument();
    expect(screen.getByText('Billing & Shipping Info')).toBeInTheDocument();
  });
});
