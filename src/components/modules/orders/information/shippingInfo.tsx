import ActionColumnMenu from '@/components/common/data-tables/ActionColumn';
import LocationLookup from '@/components/common/lookups/location-lookup';
import AutoCompleteDropdown from '@/components/forms/auto-complete-dropdown';
import InputField from '@/components/forms/input-field';
import PhoneInputWidget from '@/components/forms/phone-input-mask';
import SelectWidget from '@/components/forms/select';
import Text<PERSON>reaField from '@/components/forms/text-area';
import ZipCodeInput from '@/components/forms/zipcode-input';
import { Separator } from '@/components/ui/separator';
import { DELIVERY_LOCATION_API_ROUTES } from '@/constants/api-constants';
import { EMAIL_VALIDATION_RULEOptional } from '@/constants/auth-constants';
import { TEXT_VALIDATION_RULE } from '@/constants/validation-constants';
import {
  formatPhoneNumber,
  generateLabelValuePairs,
  stripCountryCode,
} from '@/lib/utils';
import {
  useGetCountryListQuery,
  useGetStateByCountryQuery,
} from '@/redux/features/country/country.api';
import { DeliveryLocationFormType } from '@/types/list.types';
import { OrderInformationTypes } from '@/types/order.types';
import { MapPinPlus, TrashIcon } from 'lucide-react';
import { useCallback, useMemo, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import NewLocations from '../new-location';
import { ComparisonOperator } from '@/constants/common-constants';

const ShippingInfo = () => {
  const form = useFormContext<OrderInformationTypes>();
  const isDeleted = form.watch('isDeleted');
  const isOrderEntryReadOnly = form.watch('orderEntryReadOnly');
  const { errors } = form.formState;
  const [newLocationDialog, setNewLocationDialog] = useState<boolean>(false);
  const [refetchNewLocation, setRefetchNewLocation] = useState<boolean>(false);

  const shipToInfo = form.watch('shipTo');
  const countryId = shipToInfo?.countryId;

  // country data
  const { data: countryData = [] } = useGetCountryListQuery();
  // country list
  const countryList = useMemo(
    () =>
      generateLabelValuePairs({
        data: countryData,
        labelKey: 'name',
        valueKey: 'country_id',
      }),
    [countryData]
  );

  // state list base on the country id
  const { data: statesData = [], isFetching: stateIsLoading } =
    useGetStateByCountryQuery({ countryId: Number(countryId || 1) });
  // state list
  const stateList = generateLabelValuePairs({
    data: statesData,
    labelKey: 'code',
    valueKey: 'state_id',
  });

  // get the state id
  const getStateId = useCallback(
    (name: string) => {
      const result = statesData?.find((item) => item?.code === name);
      return result?.state_id || '';
    },
    [statesData]
  );

  // get the country id
  const getcountryId = useCallback(
    (name: string) => {
      const result = countryData?.find((item) => item?.name === name);
      return result?.country_id || '';
    },
    [countryData]
  );

  // handle change country
  const handleCountryChange = useCallback(
    (value: string) => {
      form.setValue('shipTo.countryId', value);
      if (countryId !== value) {
        form.setValue('shipTo.stateId', '');
        form.setValue('shipTo.zipCode', '');
        form.clearErrors('shipTo.zipCode');
      }
    },
    [countryId, form]
  );

  // Clear Info - Ship To
  const handleClearInfoShipTo = useCallback(() => {
    form.setValue('shipTo', {
      shipLocation: null,
      phone: null,
      contact: '',
      contactEmail: '',
      contactPhone: '',
      address1: '',
      city: '',
      state: '',
      stateId: '',
      address2: '',
      zipCode: '',
      countryId: 1,
      additionalInstructions: '',
    });
  }, [form]);

  const toggleNewLocationDialog = useCallback(() => {
    setNewLocationDialog((prev) => !prev);
    if (refetchNewLocation) {
      setRefetchNewLocation(false);
    }
  }, [refetchNewLocation]);

  // Ship to Drop Dowm menu
  const DropdownMenu = useMemo(() => {
    return [
      {
        label: 'New Location',
        onClick: () => toggleNewLocationDialog(),
        icon: <MapPinPlus className="h-5 w-5" />,
      },
      {
        label: 'Clear Info',
        onClick: () => handleClearInfoShipTo(),
        icon: <TrashIcon className="w-5 h-5" />,
        className: 'text-base text-text-danger',
        disabled: !shipToInfo?.shipLocation,
      },
    ];
  }, [
    handleClearInfoShipTo,
    shipToInfo?.shipLocation,
    toggleNewLocationDialog,
  ]);

  // check is country USA
  const isUSA = form.watch('shipTo.countryId') === 1;

  const handleSelectLocation = useCallback(
    async (selectedLocation: DeliveryLocationFormType) => {
      if (newLocationDialog) {
        setRefetchNewLocation(true);
      }
      const {
        location,
        phone,
        contactEmail,
        contact,
        contactPhone,
        locationLine2,
        town,
        state,
        zipcode,
        country,
        instructions,
      } = selectedLocation || {};
      form.setValue('shipTo', {
        shipLocation: {
          label: location,
          value: location,
        },
        phone: { label: formatPhoneNumber(phone), value: phone },
        contact: contact || '',
        contactEmail: contactEmail || '',
        contactPhone: contactPhone || '',
        address1: location || '',
        city: town || '',
        state: state,
        stateId: getStateId(state),
        countryId: getcountryId(country) || 1,
        address2: locationLine2 || '',
        zipCode: zipcode || '',
        additionalInstructions: instructions || '',
      });

      form.clearErrors('shipTo');
    },
    [form, getStateId, getcountryId, newLocationDialog]
  );

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between mt-1">
        <h3 className="text-xl font-semibold text-text-Default">Ship To</h3>
        <ActionColumnMenu
          triggerClassName="border h-10"
          contentClassName="px-4 py-3"
          dropdownMenuList={DropdownMenu}
          disabled={isDeleted || isOrderEntryReadOnly}
        />
      </div>
      <div className="flex items-center w-full gap-3">
        <div className="w-full">
          <AutoCompleteDropdown
            label="Location (Lookup)"
            placeholder="Select Location"
            name="shipTo.shipLocation"
            form={form}
            onSelectChange={(option, value) =>
              handleSelectLocation({ ...option?.item, location: value })
            }
            url={DELIVERY_LOCATION_API_ROUTES.ALL}
            labelKey="location"
            valueKey="location"
            showItem
            sortBy="location"
            validation={TEXT_VALIDATION_RULE}
            isRefetch={refetchNewLocation}
            disabled={isDeleted || isOrderEntryReadOnly}
            allowCustomEntry
          />
        </div>

        <LocationLookup
          handleOk={handleSelectLocation}
          className={errors?.shipTo?.shipLocation?.message ? 'mt-2' : 'mt-8'}
          disabled={isDeleted || isOrderEntryReadOnly}
        />
      </div>
      <AutoCompleteDropdown
        label="Phone (Lookup)"
        placeholder="Select Phone"
        name="shipTo.phone"
        form={form}
        onSelectChange={(option) => handleSelectLocation(option?.item)}
        url={DELIVERY_LOCATION_API_ROUTES.ALL}
        labelKey="phone"
        valueKey="phone"
        sortBy="location"
        operator={ComparisonOperator.STARTS_WITH}
        showItem
        searchType="startsWith"
        formatSearch={(value) => `+1${value}`}
        formatLabel={stripCountryCode}
        labelComponent={formatPhoneNumber}
        isRefetch={refetchNewLocation}
        disabled={isDeleted || isOrderEntryReadOnly}
        acceptAlphanumeric={false}
      />
      <div className="col-span-4 pt-3 pb-3">
        <Separator
          orientation="horizontal"
          className=" h-[1px] bg-border-Default col-span-4"
        />
      </div>

      <InputField
        name="shipTo.contact"
        form={form}
        label="Contact"
        placeholder="Enter Contact"
        maxLength={45}
        disabled={isDeleted || isOrderEntryReadOnly}
      />
      <InputField
        name="shipTo.contactEmail"
        form={form}
        label="Contact E-mail"
        placeholder="Enter Contact E-mail"
        validation={EMAIL_VALIDATION_RULEOptional}
        disabled={isDeleted || isOrderEntryReadOnly}
      />
      <PhoneInputWidget
        form={form}
        name="shipTo.contactPhone"
        label="Contact Phone"
        disabled={isDeleted || isOrderEntryReadOnly}
      />

      <div className="grid grid-cols-2 gap-4">
        <InputField
          name="shipTo.address1"
          form={form}
          label="Address"
          placeholder="Enter Address"
          validation={TEXT_VALIDATION_RULE}
          isShowError={true}
          disabled={isDeleted || isOrderEntryReadOnly}
          autoComplete="shipping address-line1"
        />
        <InputField
          name="shipTo.address2"
          form={form}
          label="Address Line 2"
          placeholder="Enter Address"
          disabled={isDeleted || isOrderEntryReadOnly}
          autoComplete="shipTo cc-family-name webauthn"
        />
        <InputField
          name="shipTo.city"
          form={form}
          label="City"
          placeholder="Enter City"
          validation={TEXT_VALIDATION_RULE}
          disabled={isDeleted || isOrderEntryReadOnly}
        />
        <SelectWidget
          name="shipTo.stateId"
          form={form}
          placeholder="Select State"
          label="State"
          isClearable={false}
          optionsList={stateList}
          isLoading={stateIsLoading}
          validation={TEXT_VALIDATION_RULE}
          disabled={isDeleted || isOrderEntryReadOnly}
        />

        <ZipCodeInput
          name="shipTo.zipCode"
          isUSA={isUSA}
          form={form}
          label="Zip Code"
          validation={TEXT_VALIDATION_RULE}
          disabled={isDeleted || isOrderEntryReadOnly}
          autoComplete="postal-code"
        />
        <SelectWidget
          enableSearch={true}
          form={form}
          name="shipTo.countryId"
          label="Country"
          placeholder="Select Country"
          optionsList={countryList}
          onSelectChange={handleCountryChange}
          isClearable={false}
          disabled={isDeleted || isOrderEntryReadOnly}
        />
      </div>
      <TextAreaField
        form={form}
        name="shipTo.additionalInstructions"
        label="Additional Instructions"
        disabled={isDeleted || isOrderEntryReadOnly}
      />
      {/* Add New location */}
      {newLocationDialog && (
        <NewLocations
          open={newLocationDialog}
          addNewLocation={handleSelectLocation}
          onCancel={toggleNewLocationDialog}
        />
      )}
    </div>
  );
};

export default ShippingInfo;
