import ActionArea from '@/components/common/action-area';
import DatePicker from '@/components/forms/date-picker';
import Input<PERSON>ield from '@/components/forms/input-field';
import NumberInputField from '@/components/forms/number-input-field';
import SelectWidget from '@/components/forms/select';
import {
  EVENT_TYPES_API_ROUTES,
  STORE_OPTIONS_API_ROUTES,
} from '@/constants/api-constants';
import { TEXT_VALIDATION_RULE } from '@/constants/validation-constants';
import useOptionList from '@/hooks/useOptionList';
import {
  DATE_FORMAT_YYYYMMDD,
  formatDate,
  generateLabelValuePairs,
  getDayInitial,
  getQueryParam,
} from '@/lib/utils';
import { useGetDeliveryTypeQuery } from '@/redux/features/delivery-type/delivery-type.api';
import { useGetEnumsListQuery } from '@/redux/features/enums-api/enums-api';
import {
  useDateCalculationMutation,
  useLazyGetBusyStatusByDateQuery,
} from '@/redux/features/orders/order.api';
import { useLazyGetStoreQuery } from '@/redux/features/store/store.api';
import {
  OrderInformationTypes,
  ORDERT_TYPE_TAB,
  TabType,
} from '@/types/order.types';
// import { startOfToday } from 'date-fns';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import TextAreaField from '@/components/forms/text-area';
import { Box, LucideIcon, PhoneCall, Truck } from 'lucide-react';
import { memo, useCallback, useEffect, useMemo, useState } from 'react';
// import { useFormContext } from 'react-hook-form';
import Deliver from '../files-orders-tab/deliver';
import Ship from '../files-orders-tab/ship';
import useSetupTakedownList from '../files-orders-tab/useSetupTakedownList';
import WillCall from '../files-orders-tab/willCall';
import BillingInfo from './billing-info';
import ShippingInfo from './shippingInfo';
import { UseFormReturn, useWatch } from 'react-hook-form';
// import { StoreDateCalculationTypes } from '@/types/store.types';

interface OrderInformationProps {
  form: UseFormReturn<OrderInformationTypes>;
}

function OrderInformation({ form }: OrderInformationProps) {
  const id = getQueryParam('id') as string;

  // const form = useFormContext<OrderInformationTypes>();
  const isDeleted = form.watch('isDeleted');
  const isOrderEntryReadOnly = form.watch('orderEntryReadOnly');
  const orderDetails = form.watch();
  // to check order type is missing order
  const isMissingOrder = orderDetails?.originType === 'MISSING_ORDER';
  const selectedOrderTypeTab = useWatch({
    control: form.control,
    name: 'selectedOrderType',
  });
  const [activeTab, setActiveTab] = useState<TabType | string>(
    selectedOrderTypeTab
  );
  // Flag to indicate whether the "Use From Date" is marked as busy on the calendar
  const [isUseFromDateBusy, setIsUseFromDateBusy] = useState<boolean>(false);

  const [isSalesOrderWarning, setIsSalesOrderWarning] =
    useState<boolean>(false);

  const toggleSalesOrderWarning = useCallback(() => {
    setIsSalesOrderWarning((prev) => !prev);
  }, []);

  // const storeDateCalculationConfig = form.getValues('storeDateCalculation');
  const [isDefaultLocDialog, setDefaultLocDialog] = useState<boolean>(false);
  const [showPastDateWarningModal, setShowPastDateWarningModal] =
    useState<boolean>(false);
  const [dateCalculation, { isLoading: dateCalculationLoading }] =
    useDateCalculationMutation();
  // const today = startOfToday();

  // Event Info - Order Type List
  const { data: defaultOrderType, isLoading: isdefaultOrderTypeLoading } =
    useGetEnumsListQuery({
      name: 'DefaultOrderType',
    });

  // Order Location List
  const { options: orderLocationList, optionLoading } = useOptionList({
    url: STORE_OPTIONS_API_ROUTES.ALL,
    labelKey: 'locationName',
    valueKey: 'id',
    sortBy: 'locationName',
  });
  const { options: eventTypeList } = useOptionList({
    url: EVENT_TYPES_API_ROUTES.ALL,
    valueKey: 'id',
    labelKey: 'eventDesc',
    sortBy: 'eventDesc',
  });

  const { data: deliveryTypeData, isLoading: deliveryTyLoading } =
    useGetDeliveryTypeQuery();

  const deliveryTypeList = generateLabelValuePairs({
    data: deliveryTypeData?.data,
    labelKey: 'name',
    valueKey: 'deliverytype_id',
  });

  const { data: eventFrequencyList, isLoading: eventFrequencyLoading } =
    useGetEnumsListQuery({
      name: 'EventFrequency',
    });

  // Fetch busy status for the "Use From" date; skips the call if the date is not available
  const [getBusyStatusByDate] = useLazyGetBusyStatusByDateQuery();

  const formatTime = (timeStr: any) => {
    if (!timeStr) return '';
    let [hour, minute] = timeStr?.split(':');
    let period = hour >= 12 ? 'PM' : 'AM';
    hour = hour % 12 || 12;
    return `${hour}:${minute} ${period}`;
  };
  // get the store optional data by id
  const [getStoreInfo, { isFetching: storeLocationLoading }] =
    useLazyGetStoreQuery();

  // handle on change the order location
  const handleChangeOrderLocation = useCallback(
    async (value: string) => {
      form.setValue('storeLocationId', value);
      if (value) {
        const { data }: any = await getStoreInfo(value);
        const {
          defaultOrderType,
          defaultDeliveryType,
          defaultDeliveryTypeCall,
        } = data?.data?.storeMiscOrderSetting;

        // If the active tab is "Will Call," set `deliveryTypeId` to `defaultDeliveryTypeCall`.
        // else deliveryTypeId to defaultDeliveryType
        const deliveryTypeValue =
          activeTab === ORDERT_TYPE_TAB.WILL_CALL
            ? defaultDeliveryTypeCall
            : defaultDeliveryType;

        !isMissingOrder && form.setValue('orderType', defaultOrderType);
        form.setValue('deliveryTypeId', deliveryTypeValue);
      }
    },
    [activeTab, form, getStoreInfo, isMissingOrder]
  );

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const formatDateIfAvailable = (key: 'dateOfUseFrom' | 'dateOfUseThru') =>
    form.watch(key) ? formatDate(form.watch(key), 'YYYY-MM-DD') : null;

  const createPayload = useCallback(
    (overrides: any = {}) => ({
      deliveryTypeId: form.watch('deliveryTypeId') || null,
      dateOfUseFrom: formatDateIfAvailable('dateOfUseFrom'),
      dateOfUseThru: formatDateIfAvailable('dateOfUseThru'),
      storeLocationId: form.watch('storeLocationId'),
      rentDays: 0,
      orderType: form.watch('orderType') || 'RENTAL_QUOTE',
      orderCategory: activeTab,
      ...overrides,
    }),
    [activeTab, form, formatDateIfAvailable]
  );

  const updateFormValues = useCallback(
    (
      data: any,
      orderType: ORDERT_TYPE_TAB
      // storeDateCalculationConfig: StoreDateCalculationTypes
    ) => {
      const ORDER_TYPE_MAP: Record<ORDERT_TYPE_TAB, string> = {
        [ORDERT_TYPE_TAB.SHIP]: 'shipOrder',
        [ORDERT_TYPE_TAB.WILL_CALL]: 'willCallOrder',
        [ORDERT_TYPE_TAB.DELIVER]: 'deliveryOrder',
      };

      if (data?.data) {
        const fieldPrefix = ORDER_TYPE_MAP[orderType];
        Object.entries(data.data).forEach(([key, value]) => {
          form.setValue(`${fieldPrefix}.${key}` as any, value);
        });
        form.setValue('updateData', true);
        setTimeout(() => {
          form.setValue('updateData', false);
        }, 100);
      } else {
      }
    },
    [form]
  );

  const handleTabChange = useCallback(
    async (value: string) => {
      form.setValue('selectedOrderType', value);
      setActiveTab(value);
      // Retrieve the user's default store information.
      const userDefaultStoreInfo = form.getValues('userDefaultStoreInfo');

      // If the order type is "Will Call", use 'defaultDeliveryTypeCall'; otherwise, use 'defaultDeliveryType'.
      const deliveryTypeId =
        value === ORDERT_TYPE_TAB.WILL_CALL
          ? userDefaultStoreInfo?.defaultDeliveryTypeCall
          : userDefaultStoreInfo?.defaultDeliveryType;
      form.setValue('deliveryTypeId', deliveryTypeId);

      if (!form.watch('dateOfUseFrom')) return;

      try {
        const { data } = await dateCalculation({
          body: createPayload({ orderCategory: value }),
        });
        updateFormValues(
          data,
          value as ORDERT_TYPE_TAB
          // storeDateCalculationConfig
        );
      } catch (error) {
        // console.error('Error in date calculation:', error);
      }
    },
    [
      createPayload,
      dateCalculation,
      form,
      // storeDateCalculationConfig,
      updateFormValues,
    ]
  );

  const recalCulateDateCalculations = useCallback(async () => {
    if (!form.watch('dateOfUseFrom') || !form.watch('storeLocationId')) return;
    try {
      const { data } = await dateCalculation({
        body: createPayload(),
      });
      updateFormValues(
        data,
        activeTab as ORDERT_TYPE_TAB
        // storeDateCalculationConfig
      );
    } catch (error) {
      // console.error('Error in date calculation:', error);
    }
  }, [activeTab, createPayload, dateCalculation, form, updateFormValues]);
  const recalCulateDateCal = form.watch('recalculateDate');

  useEffect(() => {
    if (recalCulateDateCal) {
      recalCulateDateCalculations();
      form.setValue('recalculateDate', false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [recalCulateDateCal, recalCulateDateCalculations]);

  // Checks if the provided date is marked as busy from the server.
  const checkFromDateBusyStatus = useCallback(
    async (date?: Date | string) => {
      if (date) {
        const { data }: any = await getBusyStatusByDate(
          formatDate(date, DATE_FORMAT_YYYYMMDD)
        );

        if (data?.data?.busy) {
          setIsUseFromDateBusy(true);
        }
      }
    },
    [getBusyStatusByDate]
  );

  const onDateChange = useCallback(
    async (
      date: Date | string | undefined,
      name: 'dayOfUseFrom' | 'dayOfUseThru'
    ) => {
      if (name === 'dayOfUseFrom' && !date) {
        form.setValue('dayOfUseFrom', '');
        form.setValue('dateOfUseFrom', '');
        form.setValue('timeOfUseFrom', '');
        form.setValue('timeOfUseThru', '');
        form.setValue('dateOfUseThru', '');
        form.setValue('dayOfUseThru', '');
        form.setValue('timeOfUseFrom', '');
        form.setValue('timeOfUseThru', '');
      }

      if (name == 'dayOfUseThru' && !date) {
        form.setValue('dateOfUseThru', '');
        form.setValue('dayOfUseThru', '');
        form.setValue('timeOfUseFrom', '');
        form.setValue('timeOfUseThru', '');
      }
      if (
        form.watch('dateOfUseFrom') === '' &&
        form.watch('dateOfUseThru') === ''
      ) {
        form.setValue('shipOrder', {
          shipDate: '',
          shipDay: '',
          shipInfo: '',
          orderSetupId: '',
          shipTimeIn: '',
          shipTimeOut: '',
          returnArrivalDate: '',
          returnArrivalDay: '',
          returnInfo: '',
          takedownId: '',
          returnTimeIn: '',
          returnTimeOut: '',
          arrivalDate: '',
          arrivalDay: '',
          returnShipDate: '',
          returnShipDay: '',
          id: 0,
        });
        form.setValue('willCallOrder', {
          pickupDate: '',
          pickupDay: '',
          pickupInfo: '',
          orderSetupId: '',
          pickupTimeIn: '',
          pickupTimeOut: '',
          returnDate: '',
          returnDay: '',
          returnInfo: '',
          takedownId: '',
          returnTimeIn: '',
          returnTimeOut: '',
          arrivalDate: '',
          arrivalDay: '',
          returnShipDate: '',
          returnShipDay: '',
          id: 0,
        });
        form.setValue('deliveryOrder', {
          pickupDate: '',
          pickupDay: '',
          pickupInfo: '',
          orderSetupId: '',
          pickupTimeIn: '',
          pickupTimeOut: '',
          deliveryDay: '',
          deliveryInfo: '',
          takedownId: '',
          deliveryTimeIn: '',
          deliveryTimeOut: '',
          arrivalDate: '',
          arrivalDay: '',
          returnShipDate: '',
          returnShipDay: '',
          id: 0,
          deliveryDate: '',
        });
        return;
      }
      const dateOfUseFrom = new Date(form.watch('dateOfUseFrom'));
      const dateOfUseThru = new Date(form.watch('dateOfUseThru'));

      if (
        name === 'dayOfUseFrom' &&
        dateOfUseThru &&
        dateOfUseFrom > dateOfUseThru
      ) {
        form.setValue('dateOfUseThru', '');
        form.setValue('dayOfUseThru', '');
        form.setValue('timeOfUseFrom', '');
        form.setValue('timeOfUseThru', '');
        form.setValue('shipOrder', {
          shipDate: '',
          shipDay: '',
          shipInfo: '',
          orderSetupId: '',
          shipTimeIn: '',
          shipTimeOut: '',
          returnArrivalDate: '',
          returnArrivalDay: '',
          returnInfo: '',
          takedownId: '',
          returnTimeIn: '',
          returnTimeOut: '',
          arrivalDate: '',
          arrivalDay: '',
          returnShipDate: '',
          returnShipDay: '',
          id: 0,
        });
        form.setValue('willCallOrder', {
          pickupDate: '',
          pickupDay: '',
          pickupInfo: '',
          orderSetupId: '',
          pickupTimeIn: '',
          pickupTimeOut: '',
          returnDate: '',
          returnDay: '',
          returnInfo: '',
          takedownId: '',
          returnTimeIn: '',
          returnTimeOut: '',
          arrivalDate: '',
          arrivalDay: '',
          returnShipDate: '',
          returnShipDay: '',
          id: 0,
        });
        form.setValue('deliveryOrder', {
          pickupDate: '',
          pickupDay: '',
          pickupInfo: '',
          orderSetupId: '',
          pickupTimeIn: '',
          pickupTimeOut: '',
          deliveryDay: '',
          deliveryInfo: '',
          takedownId: '',
          deliveryTimeIn: '',
          deliveryTimeOut: '',
          arrivalDate: '',
          arrivalDay: '',
          returnShipDate: '',
          returnShipDay: '',
          id: 0,
          deliveryDate: '',
        });

        return;
      }

      if (date) {
        const selectedDate = new Date(date);
        const today = new Date();
        today.setHours(0, 0, 0, 0); // Reset time for an accurate comparison
        const day = getDayInitial({ date });
        form.setValue(name, day);
        if (selectedDate < today && name === 'dayOfUseFrom') {
          // Handle past date selection (show error or reset field)
          // console.warn('Selected date is in the past');
          setShowPastDateWarningModal(true);
          return;
        }

        if (!day || !form.watch('storeLocationId')) return;
      }
      try {
        const { data } = await dateCalculation({ body: createPayload() });
        updateFormValues(
          data,
          activeTab as ORDERT_TYPE_TAB
          // storeDateCalculationConfig
        );
      } catch (error) {
        // console.error('Error in date calculation:', error);
      }
    },
    [
      form,
      dateCalculation,
      createPayload,
      updateFormValues,
      activeTab,
      // storeDateCalculationConfig,
    ]
  );

  const handleOnSelectDeliveryType = async (value: string) => {
    if (!form.watch('dateOfUseFrom')) return;

    try {
      const { data } = await dateCalculation({
        body: createPayload({ deliveryTypeId: value }),
      });
      updateFormValues(
        data,
        activeTab as ORDERT_TYPE_TAB
        // storeDateCalculationConfig
      );
    } catch (error) {
      // console.error('Error in date calculation:', error);
    }
  };
  const toggleDefaultLocDialog = useCallback(() => {
    setDefaultLocDialog((prevState) => !prevState);
  }, []);

  const storeLocationId = orderDetails?.storeLocationId;
  useEffect(() => {
    if (!id && !storeLocationId) {
      // setDefaultLocDialog(true);
    }
  }, [id, optionLoading, storeLocationId]);

  const handleToggleWarningModal = useCallback(async () => {
    setShowPastDateWarningModal((prevState) => !prevState);
    try {
      const { data } = await dateCalculation({ body: createPayload() });
      updateFormValues(
        data,
        activeTab as ORDERT_TYPE_TAB
        // storeDateCalculationConfig
      );
    } catch (error) {
      // console.error('Error in date calculation:', error);
    }
  }, [
    activeTab,
    createPayload,
    dateCalculation,
    // storeDateCalculationConfig,
    updateFormValues,
  ]);

  const getActiveOrderType = useCallback(() => {
    const ORDER_TYPE_MAP: Record<ORDERT_TYPE_TAB, string> = {
      [ORDERT_TYPE_TAB.SHIP]: 'shipOrder',
      [ORDERT_TYPE_TAB.WILL_CALL]: 'willCallOrder',
      [ORDERT_TYPE_TAB.DELIVER]: 'deliveryOrder',
    };
    return ORDER_TYPE_MAP[activeTab as ORDERT_TYPE_TAB] as
      | 'shipOrder'
      | 'willCallOrder'
      | 'deliveryOrder';
  }, [activeTab]);

  // setup / takedown list and default values
  const { setupList, takedownList, loading } = useSetupTakedownList({
    form,
    name: getActiveOrderType(),
  });

  // Prevent order type change for missing orders; only Sales Order type is allowed.
  const orderTypeValue = form.watch('orderType');
  const handleChangeOrderType = useCallback(
    (value: string) => {
      if (isMissingOrder && !['SALES_ORDER', 'SALES_QUOTE'].includes(value)) {
        toggleSalesOrderWarning();
        form.setValue('orderType', orderTypeValue);
      }
    },
    [form, isMissingOrder, orderTypeValue, toggleSalesOrderWarning]
  );

  // Orders Type Tabs Constants
  const ordersTypeTablist = useMemo(() => {
    const TabLabel = ({ Icon, label }: { Icon: LucideIcon; label: string }) => (
      <div className="flex items-center gap-x-2">
        <Icon className="h-4" />
        <span>{label}</span>
      </div>
    );
    return [
      {
        value: ORDERT_TYPE_TAB.DELIVER,
        label: <TabLabel Icon={Box} label="Deliver" />,
        content: (
          <Deliver
            setupList={setupList}
            takedownList={takedownList}
            loading={loading}
          />
        ),
      },
      {
        value: ORDERT_TYPE_TAB.SHIP,
        label: <TabLabel Icon={Truck} label="Ship" />,
        content: (
          <Ship
            setupList={setupList}
            takedownList={takedownList}
            loading={loading}
          />
        ),
      },
      {
        value: ORDERT_TYPE_TAB.WILL_CALL,
        label: <TabLabel Icon={PhoneCall} label="Will Call" />,
        content: (
          <WillCall
            setupList={setupList}
            takedownList={takedownList}
            loading={loading}
          />
        ),
      },
    ];
  }, [loading, setupList, takedownList]);

  return (
    <div className="space-y-6">
      <p className="text-2xl font-semibold text-[#181A1D]">Event Info</p>

      {/* Order basic info */}
      <div className="grid grid-cols-1 md:grid-cols-7 gap-4">
        <NumberInputField
          form={form}
          name="orderNo"
          label="Order#"
          placeholder="Enter Order#"
          disabled
          pClassName="md:col-span-2"
        />
        <NumberInputField
          name="revision"
          form={form}
          label="Rev."
          placeholder="Rev."
          disabled
        />
        <div className="md:col-span-2">
          <SelectWidget
            optionsList={orderLocationList || []}
            name="storeLocationId"
            label="Order Location"
            placeholder="Select Order Location"
            isLoading={optionLoading}
            validation={TEXT_VALIDATION_RULE}
            onSelectChange={handleChangeOrderLocation}
            form={form}
            isClearable={false}
            disabled={isDeleted || isOrderEntryReadOnly}
          />
        </div>
        <div className="md:col-span-2">
          <SelectWidget
            name="orderType"
            label="Order Type"
            placeholder="Select Order Type"
            optionsList={defaultOrderType?.data ?? []}
            isLoading={isdefaultOrderTypeLoading || storeLocationLoading}
            validation={TEXT_VALIDATION_RULE}
            onSelectChange={handleChangeOrderType}
            form={form}
            isClearable={false}
            disabled={isDeleted || isOrderEntryReadOnly}
          />
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <NumberInputField
          form={form}
          name="grandTotal"
          label="Grand Total"
          placeholder="Grand Total"
          disabled
        />
        <NumberInputField
          name="Balance Due"
          form={form}
          label="Balance Due"
          placeholder="Balance Due"
          disabled
        />
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="grid grid-cols-5 gap-4 border border-border-Default rounded-lg p-4">
          <p className="col-span-5 text-2xl text-[#181A1D]">Use From</p>
          <DatePicker
            form={form}
            name="dateOfUseFrom"
            label="Date"
            placeholder="Select Date"
            onDateChange={(data) => {
              onDateChange(data, 'dayOfUseFrom');
              checkFromDateBusyStatus(data);
            }}
            validation={TEXT_VALIDATION_RULE}
            enableInput
            pClassName="col-span-2"
            isRenderFirst={true}
            disabled={isDeleted || isOrderEntryReadOnly}
          />

          <InputField
            name="dayOfUseFrom"
            form={form}
            label="Day"
            placeholder="Day"
            disabled
            pClassName="col-span-1"
          />
          <InputField
            name="timeOfUseFrom"
            form={form}
            label="Time"
            onChange={formatTime}
            type="time"
            pClassName="col-span-2"
            disabled={isDeleted || isOrderEntryReadOnly}
          />
        </div>
        <div className="grid grid-cols-5 gap-4 border border-border-Default rounded-lg p-4">
          <p className="col-span-5 text-2xl text-[#181A1D]">Use Thru</p>
          <DatePicker
            form={form}
            name="dateOfUseThru"
            label="Date"
            disablePastDate={new Date(form.watch('dateOfUseFrom'))}
            placeholder="Select Date"
            onDateChange={(data) => onDateChange(data, 'dayOfUseThru')}
            pClassName="col-span-2"
            disabled={
              isDeleted || isOrderEntryReadOnly || !form.watch('dateOfUseFrom')
            }
            enableInput
            isRenderFirst={true}
          />
          <InputField
            name="dayOfUseThru"
            form={form}
            label="Day"
            placeholder="Day"
            disabled
            pClassName="col-span-1"
          />
          <InputField
            name="timeOfUseThru"
            form={form}
            label="Time"
            disabled={
              isDeleted || isOrderEntryReadOnly || !form.watch('dateOfUseFrom')
            }
            onChange={formatTime}
            type="time"
            pClassName="col-span-2"
          />
        </div>
      </div>

      {/* Order Type */}
      <div>
        <p className="text-2xl font-semibold text-[#181A1D] pb-2">Order Type</p>
        <ActionArea
          tabs={ordersTypeTablist}
          defaultValue={selectedOrderTypeTab}
          onValueChange={handleTabChange}
          tabsContentClassName="p-4 gap-4 border border-border-Default rounded-lg"
          isLoading={dateCalculationLoading}
          disabled={isDeleted || isOrderEntryReadOnly}
        />
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 border border-border-Default rounded-lg p-4">
        <SelectWidget
          form={form}
          name="deliveryTypeId"
          label="Delivery Type"
          placeholder="Select Delivery Type"
          isClearable={false}
          onSelectChange={handleOnSelectDeliveryType}
          validation={TEXT_VALIDATION_RULE}
          optionsList={deliveryTypeList}
          isLoading={deliveryTyLoading || storeLocationLoading}
          disabled={isDeleted || isOrderEntryReadOnly}
        />

        <InputField
          name="eventDescription"
          form={form}
          label="Event Description"
          placeholder="Enter Event Description"
          maxLength={60}
          disabled={isDeleted || isOrderEntryReadOnly}
        />
        <SelectWidget
          form={form}
          name="eventTypeId"
          label="Event Type"
          placeholder="Select Event Type"
          isClearable={false}
          optionsList={eventTypeList}
          disabled={isDeleted || isOrderEntryReadOnly}
        />
        <SelectWidget
          form={form}
          name="eventFrequency"
          label="Event Frequency"
          placeholder="Select Event Frequency"
          isClearable={false}
          optionsList={eventFrequencyList?.data || []}
          validation={TEXT_VALIDATION_RULE}
          isLoading={eventFrequencyLoading}
          disabled={isDeleted || isOrderEntryReadOnly}
        />

        <InputField
          name="eventPurchase"
          form={form}
          label="Purchase Order #"
          placeholder="Enter Purchase Order"
          disabled
        />
        <NumberInputField
          name="rentDays"
          form={form}
          label="Rent Days"
          placeholder="____"
          maxLength={4}
          disabled
          decimalScale={0}
        />
      </div>

      {/* Billing & (Shipping /  Missing Equipment Info) */}
      <div>
        <p className="text-2xl font-semibold text-[#181A1D] pb-2">
          Billing & Shipping Info
        </p>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-5 border border-border-Default rounded-lg p-4">
          <BillingInfo />
          {isMissingOrder ? (
            <div className="space-y-4">
              <h3 className="text-xl font-semibold text-text-Default my-3 pb-1">
                M / E Info
              </h3>
              <TextAreaField
                name="missingInfo"
                form={form}
                label="Description"
                placeholder="Description"
                rows={13}
                disabled={isDeleted || isOrderEntryReadOnly}
                validation={TEXT_VALIDATION_RULE}
              />
            </div>
          ) : (
            <ShippingInfo />
          )}
        </div>
      </div>
      <AppConfirmationModal
        title={'Warning'}
        open={isDefaultLocDialog}
        description={
          <div>
            Default Order Location is not set as Default. Please select the{' '}
            <span className="font-bold">Order Location</span> manually.
          </div>
        }
        handleSubmit={toggleDefaultLocDialog}
        submitLabel="Ok"
      />

      <AppConfirmationModal
        title={'Warning'}
        open={showPastDateWarningModal}
        description={
          <div>
            The Date of Use just Entered is prior to today. Please be make sure
            {''}
            <span className="font-bold"> this is the correct date</span>
          </div>
        }
        handleSubmit={handleToggleWarningModal}
        submitLabel="Ok"
      />
      <AppConfirmationModal
        title={'Warning'}
        open={isSalesOrderWarning}
        description={
          <div>This order can only be converted to a sales quote.</div>
        }
        handleSubmit={toggleSalesOrderWarning}
        submitLabel="Ok"
      />
      <AppConfirmationModal
        title={'Warning'}
        open={isUseFromDateBusy}
        description={
          <div>
            The delivery date of{' '}
            <span className="font-bold">
              {formatDate(orderDetails?.dateOfUseFrom)}
            </span>{' '}
            is flagged as busy. Please check with a supervisor before booking
            for this day.
          </div>
        }
        handleSubmit={() => setIsUseFromDateBusy(false)}
        submitLabel="Ok"
      />
    </div>
  );
}

export default memo(OrderInformation);
