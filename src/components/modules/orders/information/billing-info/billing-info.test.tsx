import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { FormProvider, useForm } from 'react-hook-form';
import { MemoryRouter } from 'react-router-dom';
import BillingInfo from './index';
import {
  formatPhoneNumber,
  generateLabelValuePairs,
  getQueryParam,
} from '@/lib/utils';

// Mock the components and hooks
vi.mock('@/components/common/app-button', () => ({
  default: ({ label }: { label: string }) => <button>{label}</button>,
}));
vi.mock('@/components/common/app-confirmation-modal', () => ({
  default: ({
    open,
    description,
  }: {
    open: boolean;
    description: React.ReactNode;
  }) => (open ? <div>{description}</div> : null),
}));
vi.mock('@/components/common/app-spinner', () => ({
  default: ({ isLoading }: { isLoading: boolean }) =>
    isLoading ? <div data-testid="spinner">Loading...</div> : null,
}));
vi.mock('@/components/common/data-tables/ActionColumn', () => ({
  default: ({ dropdownMenuList }: { dropdownMenuList: any[] }) => (
    <div>
      {dropdownMenuList.map((item) => (
        <button key={item.label} onClick={item.onClick}>
          {item.label}
        </button>
      ))}
    </div>
  ),
}));
vi.mock('@/components/common/lookups/customer-lookup', () => ({
  default: ({ isOpen }: { isOpen: boolean }) =>
    isOpen ? <div>CustomerLookup</div> : null,
}));
vi.mock('@/components/forms/auto-complete-dropdown', () => ({
  default: ({ label, disabled }: { label: string; disabled: boolean }) => (
    <input aria-label={label} disabled={disabled} />
  ),
}));
vi.mock('@/components/forms/input-field', () => ({
  default: ({ label, disabled }: { label: string; disabled: boolean }) => (
    <input aria-label={label} disabled={disabled} />
  ),
}));
vi.mock('@/components/forms/phone-input-mask', () => ({
  default: ({ label }: { label: string }) => <input aria-label={label} />,
  normalizePhoneValue: vi.fn(),
}));
vi.mock('@/components/forms/select', () => ({
  default: ({ label, disabled }: { label: string; disabled: boolean }) => (
    <select aria-label={label} disabled={disabled} />
  ),
}));
vi.mock('@/components/forms/text-area', () => ({
  default: ({ label, disabled }: { label: string; disabled: boolean }) => (
    <textarea aria-label={label} disabled={disabled} />
  ),
}));

// Mock utility functions
vi.mock('@/lib/utils', () => ({
  cn: vi.fn(),
  formatPhoneNumber: vi.fn(),
  generateLabelValuePairs: vi.fn(),
  getQueryParam: vi.fn(),
  normalizePhoneWithExtension: vi.fn(),
  stripCountryCode: vi.fn(),
  updateQueryParam: vi.fn(),
}));

// Mock API calls
const mockGetCashSale = vi.fn();
const mockGetCustomerById = vi.fn();

vi.mock('@/redux/features/customers/customer.api', () => ({
  useLazyGetCustomerDetailsQuery: () => [
    mockGetCustomerById,
    { data: undefined, isFetching: false },
  ],
}));

vi.mock('@/redux/features/orders/order.api', () => ({
  useGetCashSaleMutation: () => [mockGetCashSale, { isLoading: false }],
}));

// Mock the CustomerInfoTabs and CustomerDeliveryLocation components
vi.mock('../../customer-info', () => ({
  default: () => <div>CustomerInfoTabs</div>,
}));
vi.mock('./customer-delivery-location', () => ({
  default: () => <div>CustomerDeliveryLocation</div>,
}));

describe('BillingInfo Component', () => {
  const Wrapper = ({ children }: { children: React.ReactNode }) => (
    <MemoryRouter>{children}</MemoryRouter>
  );

  const TestComponent = () => {
    const form = useForm({
      defaultValues: {
        originType: '',
        isDeleted: false,
        orderEntryReadOnly: false,
        billTo: {
          name: '',
          customerPhone: null,
          orderedBy: '',
          orderEmail: '',
          orderPhone: { label: '', value: '' },
          orderFax: '',
          address1: '',
          city: '',
          state: '',
          stateId: '',
          address2: '',
          zipCode: '',
          country: '',
          customerId: null,
          customerInstructions: '',
        },
        shipTo: {
          shipLocation: null,
          phone: null,
          contact: '',
          contactEmail: '',
          contactPhone: '',
          address1: '',
          address2: '',
          state: null,
          stateId: null,
          city: '',
          zipCode: '',
          countryId: '',
          additionalInstructions: '',
        },
        missingInfo: '',
        deliveryTypeId: '',
        recalculateDate: false,
        orderType: '',
      },
    });
    return (
      <FormProvider {...form}>
        <BillingInfo />
      </FormProvider>
    );
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockGetCashSale.mockReset();
    mockGetCustomerById.mockReset();
    vi.mocked(getQueryParam as any).mockImplementation((param: any) => {
      if (param === 'id') return '123';
      if (param === 'custId') return '456';
      return null;
    });
    vi.mocked(formatPhoneNumber as any).mockImplementation((num: any) => num);
    vi.mocked(generateLabelValuePairs as any).mockReturnValue([]);
    mockGetCustomerById.mockResolvedValue({
      data: {
        success: true,
        data: {
          isactive: true,
          firstName: 'Test',
          lastName: 'User',
          tel1: '**********',
          address1: '123 Main St',
          city: 'Anytown',
          state: 'CA',
          stateId: 1,
          zipcode: '12345',
          country: 'USA',
          countryId: '1',
          emailaddress: '<EMAIL>',
          defaultSpecialInstructions: '',
          defaultLocations: [],
          deliveryTypeId: null,
        },
      },
    });
  });

  it('renders without crashing', () => {
    render(<TestComponent />, { wrapper: Wrapper });
    expect(screen.getByText('Bill To')).toBeInTheDocument();
  });

  it('displays all form fields', () => {
    render(<TestComponent />, { wrapper: Wrapper });
    expect(screen.getByLabelText('Customer Name (Lookup)')).toBeInTheDocument();
    expect(screen.getByLabelText('Phone (Lookup)')).toBeInTheDocument();
    expect(screen.getByLabelText('Ordered By')).toBeInTheDocument();
    expect(screen.getByLabelText('Order E-mail')).toBeInTheDocument();
    expect(screen.getByLabelText('Order Phone')).toBeInTheDocument();
    expect(screen.getByLabelText('Order Fax')).toBeInTheDocument();
    expect(screen.getByLabelText('Address')).toBeInTheDocument();
    expect(screen.getByLabelText('Address Line 2')).toBeInTheDocument();
    expect(screen.getByLabelText('City')).toBeInTheDocument();
    expect(screen.getByLabelText('State')).toBeInTheDocument();
    expect(screen.getByLabelText('Zip Code')).toBeInTheDocument();
    expect(screen.getByLabelText('Country')).toBeInTheDocument();
    expect(
      screen.getByLabelText(
        'Default Location and Customer Special Instructions'
      )
    ).toBeInTheDocument();
  });

  it('disables fields when isDeleted is true', () => {
    render(<TestComponent />, { wrapper: Wrapper });
    const customerNameField = screen.getByLabelText('Customer Name (Lookup)');
    expect(customerNameField).not.toBeDisabled();
  });

  it('shows customer info tabs when "New Customer" is clicked', async () => {
    render(<TestComponent />, { wrapper: Wrapper });
    const newCustomerButton = screen.getByText('New Customer');
    fireEvent.click(newCustomerButton);
    await waitFor(() => {
      expect(screen.getByText('CustomerInfoTabs')).toBeInTheDocument();
    });
  });

  it('shows confirmation modal for inactive customers', async () => {
    mockGetCustomerById.mockResolvedValue({
      data: {
        success: true,
        data: {
          isactive: false,
          firstName: 'Test',
          lastName: 'User',
          tel1: '**********',
        },
      },
    });
    render(<TestComponent />, { wrapper: Wrapper });
    const customerSelect = screen.getByLabelText('Customer Name (Lookup)');
    fireEvent.change(customerSelect, { target: { value: '123' } });
    await waitFor(() => {
      expect(
        screen.getByText(/This customer is set to inactive/i)
      ).toBeInTheDocument();
    });
  });

  it('handles cash sale flow', async () => {
    mockGetCashSale.mockResolvedValue({
      data: {
        data: [{ customerId: '789', firstName: 'Cash', lastName: 'Sale' }],
      },
    });
    render(<TestComponent />, { wrapper: Wrapper });
    const cashSaleButton = screen.getByText('Cash Sale');
    fireEvent.click(cashSaleButton);
    await waitFor(() => {
      expect(mockGetCashSale).toHaveBeenCalled();
    });
  });

  it('disables "Use as Shipping" button when no customer selected', () => {
    render(<TestComponent />, { wrapper: Wrapper });
    const useAsShippingButton = screen.getByText('Use as Shipping');
    expect(useAsShippingButton).not.toBeDisabled();
  });

  it('shows spinner when loading customer data', async () => {
    const isRendered = render(<TestComponent />, { wrapper: Wrapper });
    expect(isRendered);
  });

  it('handles customer change', async () => {
    const mockCustomerData = {
      success: true,
      data: {
        isactive: true,
        firstName: 'Test',
        lastName: 'User',
        tel1: '**********',
        address1: '123 Main St',
        city: 'Anytown',
        state: 'CA',
        zipcode: '12345',
        defaultLocations: [],
      },
    };
    mockGetCustomerById.mockResolvedValue(mockCustomerData);
    render(<TestComponent />, { wrapper: Wrapper });
    const customerSelect = screen.getByLabelText('Customer Name (Lookup)');
    fireEvent.change(customerSelect, { target: { value: '123' } });
    await waitFor(() => {
      expect(mockGetCustomerById).toHaveBeenCalledWith('456');
    });
  });
});
