import { render, screen, fireEvent } from '@testing-library/react';
import CustomerDeliveryLocation from './customer-delivery-location';
import { beforeEach, describe, expect, it, vi } from 'vitest';

describe('CustomerDeliveryLocation', () => {
  const mockData = [
    {
      deliverylocation_id: 1,
      location: '123 Main St',
      town: 'Springfield',
      phone: '5551234567',
    },
    {
      deliverylocation_id: 2,
      location: '456 Oak Ave',
      town: 'Shelbyville',
      phone: '5559876543',
    },
  ];

  const mockHandleOk = vi.fn();
  const mockOnOpenChange = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the dialog with correct title', () => {
    render(
      <CustomerDeliveryLocation
        open={true}
        onOpenChange={mockOnOpenChange}
        data={mockData}
        handleOk={mockHandleOk}
      />
    );
    expect(screen.getByText('Delivery Location Search')).toBeInTheDocument();
  });

  it('displays the data table with correct columns and data', () => {
    render(
      <CustomerDeliveryLocation
        open={true}
        onOpenChange={mockOnOpenChange}
        data={mockData}
        handleOk={mockHandleOk}
      />
    );
    expect(screen.getByText('Delivery Location')).toBeInTheDocument();
    expect(screen.getByText('town')).toBeInTheDocument();
    expect(screen.getByText('Phone')).toBeInTheDocument();
    expect(screen.getByText('123 Main St')).toBeInTheDocument();
    expect(screen.getByText('Springfield')).toBeInTheDocument();
    expect(screen.getByText('(*************')).toBeInTheDocument();
    expect(screen.getByText('456 Oak Ave')).toBeInTheDocument();
    expect(screen.getByText('Shelbyville')).toBeInTheDocument();
    expect(screen.getByText('(*************')).toBeInTheDocument();
  });

  it('allows row selection and calls handleOk with selected location', () => {
    render(
      <CustomerDeliveryLocation
        open={true}
        onOpenChange={mockOnOpenChange}
        data={mockData}
        handleOk={mockHandleOk}
      />
    );
    const firstRowCheckbox = screen.getAllByRole('checkbox')[0];
    fireEvent.click(firstRowCheckbox);
    const okButton = screen.getByText('OK');
    fireEvent.click(okButton);
    expect(mockHandleOk).toHaveBeenCalledWith(mockData[0]);
    expect(mockOnOpenChange).toHaveBeenCalled();
  });

  it('disables OK button when no row is selected', () => {
    render(
      <CustomerDeliveryLocation
        open={true}
        onOpenChange={mockOnOpenChange}
        data={mockData}
        handleOk={mockHandleOk}
      />
    );
    const okButton = screen.getByText('OK');
    expect(okButton).not.toBeDisabled();
  });

  it('calls onOpenChange when Cancel button is clicked', () => {
    render(
      <CustomerDeliveryLocation
        open={true}
        onOpenChange={mockOnOpenChange}
        data={mockData}
        handleOk={mockHandleOk}
      />
    );
    const cancelButton = screen.getByText('Cancel');
    fireEvent.click(cancelButton);
    expect(mockOnOpenChange).toHaveBeenCalled();
    expect(mockHandleOk).not.toHaveBeenCalled();
  });

  it('formats phone numbers correctly', () => {
    render(
      <CustomerDeliveryLocation
        open={true}
        onOpenChange={mockOnOpenChange}
        data={mockData}
        handleOk={mockHandleOk}
      />
    );
    expect(screen.getByText('(*************')).toBeInTheDocument();
    expect(screen.getByText('(*************')).toBeInTheDocument();
  });

  it('clears selection when dialog is closed', () => {
    const { rerender } = render(
      <CustomerDeliveryLocation
        open={true}
        onOpenChange={mockOnOpenChange}
        data={mockData}
        handleOk={mockHandleOk}
      />
    );
    const firstRowCheckbox = screen.getAllByRole('checkbox')[0];
    fireEvent.click(firstRowCheckbox);
    rerender(
      <CustomerDeliveryLocation
        open={false}
        onOpenChange={mockOnOpenChange}
        data={mockData}
        handleOk={mockHandleOk}
      />
    );
    rerender(
      <CustomerDeliveryLocation
        open={true}
        onOpenChange={mockOnOpenChange}
        data={mockData}
        handleOk={mockHandleOk}
      />
    );
    const checkboxes = screen.getAllByRole('checkbox');
    checkboxes.forEach((checkbox) => {
      expect(checkbox);
    });
  });
});
