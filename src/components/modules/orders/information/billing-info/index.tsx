import AppButton from '@/components/common/app-button';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import AppSpinner from '@/components/common/app-spinner';
import ActionColumnMenu from '@/components/common/data-tables/ActionColumn';
import CustomerLookup from '@/components/common/lookups/customer-lookup';
import AutoCompleteDropdown from '@/components/forms/auto-complete-dropdown';
import InputField from '@/components/forms/input-field';
import PhoneInputWidget, {
  normalizePhoneValue,
} from '@/components/forms/phone-input-mask';
import SelectWidget from '@/components/forms/select';
import Text<PERSON>reaField from '@/components/forms/text-area';
import { CUSTOMER_API_ROUTES } from '@/constants/api-constants';
import { EMAIL_VALIDATION_RULEs } from '@/constants/auth-constants';
import { TEXT_VALIDATION_RULE } from '@/constants/validation-constants';
import {
  cn,
  formatPhoneNumber,
  generate<PERSON>abel<PERSON><PERSON>ueP<PERSON>s,
  getQueryParam,
  normalizePhoneWithExtension,
  stripCountryCode,
  updateQueryParam,
} from '@/lib/utils';
import { useLazyGetCustomerDetailsQuery } from '@/redux/features/customers/customer.api';
import { useGetCashSaleMutation } from '@/redux/features/orders/order.api';
import { OptionsListTypes } from '@/types/common.types';
import { CustomerDetailTypes } from '@/types/customer.types';
import { OrderInformationTypes, shipToType } from '@/types/order.types';
import { Separator } from '@radix-ui/react-select';
import {
  ArrowRight,
  CircleUserRound,
  HandCoins,
  Pencil,
  PencilOff,
  TrashIcon,
  UserPlus,
} from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { ORDER_TYPE } from '../../constants';
import CustomerInfoTabs from '../../customer-info';
import CustomerDeliveryLocation from './customer-delivery-location';
import { ComparisonOperator } from '@/constants/common-constants';

const BillingInfo = () => {
  const id = getQueryParam('id') as string;
  const getCustomerId = getQueryParam('custId') as string;
  const form = useFormContext<OrderInformationTypes>();
  const isMissingOrder = form.watch('originType') === 'MISSING_ORDER';

  const isDeleted = form.watch('isDeleted');
  const isOrderEntryReadOnly = form.watch('orderEntryReadOnly');
  const { errors } = form.formState;

  const [isDisabled, setIsDisabled] = useState<boolean>(true);
  const [openCustomerDialog, SetOpenCustomerDialog] = useState<boolean>(false);
  const [isCustomerInfo, setIsCustomerInfo] = useState<boolean>(false);
  const [isShippingAddress, setIsShippingAddress] = useState<boolean>(false);
  const [shouldClearAddr, setShouldClearAddr] = useState<boolean>(false);
  const [hasInitialCustomerInfoLoaded, setHasInitialCustomerInfoLoaded] =
    useState<boolean>(false);
  const [isRefetchNewCustInfo, setIsRefetchNewCustInfo] =
    useState<boolean>(false);
  const [openMultiLocaion, setOpenMultiLocaion] = useState<boolean>(false);
  const [deliverylocation, setDeliverylocation] = useState<any>({});
  const [isInActiveCustomer, setIsInActiveCustomer] = useState<boolean>(false);
  // get customer details API
  const [
    getCustomerById,
    { data: customerDetails, isFetching: customerLoading },
  ] = useLazyGetCustomerDetailsQuery();
  const customerContactsDetails = customerDetails?.data?.additionalContacts;
  // customer default locations
  const defaultLocations = customerDetails?.data?.defaultLocations;

  // State for cash sale info (combining customer-related data)
  const [cashSaleData, setCashSaleData] = useState({
    isOpen: false,
    isCashSaleCustomer: false,
    customerFirstName: '',
    customerLastName: '',
  });
  const [getCashSale, { isLoading: isCashSaleLoading }] =
    useGetCashSaleMutation();

  // toggle add new customer
  const toggleOpenCustomerDialog = useCallback(() => {
    SetOpenCustomerDialog((prev) => !prev);
    if (isCustomerInfo) {
      setIsCustomerInfo(false);
      updateQueryParam(null, 'customerId');
    }
    if (cashSaleData?.customerFirstName && cashSaleData?.customerLastName) {
      setCashSaleData((prevData) => ({
        ...prevData,
        customerFirstName: '',
        customerLastName: '',
      }));
    }
    if (isRefetchNewCustInfo) {
      setIsRefetchNewCustInfo(false);
    }
  }, [
    cashSaleData?.customerFirstName,
    cashSaleData?.customerLastName,
    isCustomerInfo,
    isRefetchNewCustInfo,
  ]);

  // toggle customer has multiple location
  const toggleOpenMultiLocaion = useCallback(() => {
    setOpenMultiLocaion((prev) => !prev);
  }, []);

  const toggleIsInActiveCustomer = useCallback(() => {
    setIsInActiveCustomer((prev) => !prev);
  }, []);

  // toggle shouldClearAddr
  const toggleShouldClearAddr = useCallback(() => {
    setShouldClearAddr((prev) => !prev);
    setDeliverylocation({});
  }, []);

  // toggle the is shipping address
  const toggleIsShippingAddress = useCallback(() => {
    setIsShippingAddress((prev) => !prev);
  }, []);

  // toggle the is cash sale
  const toggleIsCashSaleCustomer = useCallback(() => {
    setCashSaleData((prevData) => ({
      ...prevData,
      isCashSaleCustomer: !prevData.isCashSaleCustomer,
    }));
  }, []);

  // billing info
  const billTo = form.watch('billTo');

  const getFullName = useCallback((item: any) => {
    const firstName = item?.firstName?.trim() || '';
    const lastName = item?.lastName?.trim() || '';

    return firstName || lastName ? `${firstName} ${lastName}`.trim() : '';
  }, []);

  // order by list
  const OrderByList = useMemo(() => {
    return customerContactsDetails
      ?.map((item: CustomerDetailTypes) => {
        const label = getFullName(item);

        return { label, value: label };
      })
      ?.filter((item: OptionsListTypes) => Boolean(item?.value));
  }, [customerContactsDetails, getFullName]);

  // order phone list
  const orderPhoneList = useMemo(() => {
    const contact = customerContactsDetails?.find(
      (item: CustomerDetailTypes) =>
        getFullName(item) === billTo?.orderedBy?.value
    );

    const phoneList = generateLabelValuePairs({
      data: contact?.phones || [],
      labelKey: 'phoneno',
      valueKey: 'phoneno',
    });

    return (
      phoneList?.map((items) => ({
        ...items,
        label: formatPhoneNumber(items?.label),
      })) || []
    );
  }, [billTo?.orderedBy, customerContactsDetails, getFullName]);

  // Enable the textarea when clicking the pencil icon
  const handleEditClick = useCallback(() => {
    setIsDisabled((prev) => !prev);
  }, []);

  // Clear Info - Bill To
  const handleClearBillingInfo = useCallback(() => {
    form.setValue('eventDescription', '');
    form.setValue('billTo', {
      name: '',
      customerPhone: null,
      orderedBy: null,
      orderEmail: '',
      orderPhone: { label: '', value: '' },
      orderFax: '',
      address1: '',
      city: '',
      state: '',
      stateId: '',
      address2: '',
      zipCode: '',
      country: '',
      customerId: null,
      customerInstructions: '',
    });
  }, [form]);

  // Clear shipping info if customer changes and confirms.
  const handleAddClearShippingInfo = useCallback(
    (info: shipToType | any) => {
      if (isMissingOrder) {
        form.setValue('missingInfo', info?.missingItemInfo);
      } else {
        const formatShipTo = {
          shipLocation: info?.location
            ? { label: info.location, value: info.location }
            : null,
          phone: info?.phone
            ? { label: formatPhoneNumber(info.phone) ?? '', value: info.phone }
            : null,
          contact: info?.contact ?? '',
          contactEmail: info?.contactemail ?? '',
          contactPhone: info?.contactphone ?? '',
          address1: info?.locationline2 ?? '',
          address2: '',
          state: info?.state ?? null,
          stateId: info?.stateId ?? null,
          city: info?.town ?? '',
          zipCode: info?.zipcode ?? '',
          countryId: info?.countryId || '',
          additionalInstructions: info?.instructions ?? '',
        };

        form.setValue('shipTo', formatShipTo);
        form.clearErrors('shipTo');
      }

      // Reset state
      setShouldClearAddr(false);
      setDeliverylocation({});
    },
    [form, isMissingOrder]
  );

  // Checks if the shipping address has any information
  const hasShippingAddress = useCallback(() => {
    const shippingAddressInfo = form.watch('shipTo');
    const { countryId, contactPhone, phone, ...restShipToInfo } =
      shippingAddressInfo;

    const hasMissingEquipmentInfo =
      form.watch('missingInfo')?.trim()?.length > 0;
    const hasAddress = Object.values(restShipToInfo)?.some((value) =>
      Boolean(value)
    );
    return isMissingOrder ? hasMissingEquipmentInfo : hasAddress;
  }, [form, isMissingOrder]);

  // handle change the Customer
  const handleChangeCustomer = useCallback(
    async (customerId: string) => {
      const { data } = await getCustomerById(customerId);
      if (data?.success) {
        const {
          customerId,
          isactive,
          firstName,
          lastName,
          tel1,
          telfax,
          address1,
          address2,
          city,
          state,
          stateId,
          country,
          zipcode,
          countryId,
          emailaddress,
          defaultSpecialInstructions,
          defaultLocations,
          deliveryTypeId,
        } = data?.data;

        const name = `${firstName} ${lastName}`;
        // Show a warning if the customer is inactive
        if (!isactive) {
          toggleIsInActiveCustomer();
        }
        if (deliveryTypeId) {
          form.setValue('deliveryTypeId', deliveryTypeId);
          form.setValue('recalculateDate', true);
        }
        // set form values with the customer data
        form.setValue('billTo', {
          customerId: { label: name, value: customerId },
          name: name ?? '',
          customerPhone: { label: formatPhoneNumber(tel1), value: tel1 },
          orderedBy: null,
          orderEmail: emailaddress ?? '',
          orderPhone: { label: '', value: '' },
          orderFax: telfax,
          address1: address1 ?? '',
          city: city ?? '',
          state: state ?? '',
          stateId: stateId ?? 1 ?? '',
          address2: address2 ?? '',
          zipCode: zipcode ?? '',
          country: country ?? '',
          countryId: countryId ?? '',
          customerInstructions: defaultSpecialInstructions ?? '',
        });
        form.clearErrors('billTo');

        // // when the customer updates or changes.
        const defaultLocation = defaultLocations && defaultLocations.at(0);
        const hasShippingAdd = hasShippingAddress();

        // shipping blank
        if (!hasShippingAdd) {
          // customer has default location
          if (defaultLocations?.length === 1) {
            handleAddClearShippingInfo(defaultLocation);
          } else if (defaultLocations?.length > 1) {
            toggleOpenMultiLocaion();
          }
        }

        // shipping not blank
        else if (hasShippingAdd) {
          if (defaultLocations?.length === 1) {
            toggleShouldClearAddr();
            setDeliverylocation(defaultLocation);
          } else if (defaultLocations?.length > 1) {
            toggleOpenMultiLocaion();
          } else if (defaultLocations?.length === 0) {
            toggleShouldClearAddr();
          } else {
            toggleShouldClearAddr();
          }
        }
      }
    },
    [
      form,
      getCustomerById,
      handleAddClearShippingInfo,
      hasShippingAddress,
      toggleIsInActiveCustomer,
      toggleOpenMultiLocaion,
      toggleShouldClearAddr,
    ]
  );

  // handle on click on Customer Info
  const handleOnClickCustomerInfo = useCallback(() => {
    toggleOpenCustomerDialog();
    setIsCustomerInfo(true);

    updateQueryParam(billTo?.customerId?.value as string, 'customerId');
  }, [billTo?.customerId?.value, toggleOpenCustomerDialog]);

  // Cash sale handler function
  const handleCashSale = useCallback(async () => {
    try {
      // Default to '0' if id is undefined or null
      const response = await getCashSale(id || '0');

      // Check if no customers found for cash sale
      if (response?.data?.data?.length === 0) {
        toggleIsCashSaleCustomer(); // Trigger customer creation flow
      }
      // If exactly one customer found, handle customer change
      else if (response?.data?.data?.length === 1) {
        const customerId = response.data.data[0].customerId;
        handleChangeCustomer(customerId?.toString() as string);
        form.setValue('orderType', ORDER_TYPE.SALES_ORDER);
        form.setValue('recalculateDate', true);
      }
      // If more than one customer found, open a modal to select a customer
      else if (response?.data?.data?.length ?? 0 > 1) {
        setCashSaleData((prevData) => ({
          ...prevData,
          isOpen: true, // Set modal to open for customer selection
        }));
      }
    } catch (error) {}
  }, [form, getCashSale, handleChangeCustomer, id, toggleIsCashSaleCustomer]);

  // Bill Drop down menu
  const BillingDropdownMenu = useMemo(() => {
    return [
      {
        label: 'New Customer',
        onClick: () => toggleOpenCustomerDialog(),
        icon: <UserPlus className="h-5 w-5" />,
        disabled: isDeleted || isOrderEntryReadOnly,
      },
      {
        label: 'Customer Info',
        onClick: () => handleOnClickCustomerInfo(),
        icon: <CircleUserRound className="h-5 w-5" />,
        disabled: !billTo?.customerId,
      },
      {
        label: 'Cash Sale',
        onClick: () => handleCashSale(),
        icon: <HandCoins className="h-5 w-5" />,
        disabled: Boolean(id) || isDeleted || isOrderEntryReadOnly,
      },
      {
        label: 'Clear Info',
        onClick: () => handleClearBillingInfo(),
        icon: <TrashIcon className="w-5 h-5" />,
        className: 'text-base text-text-danger',
        disabled: isDeleted || isOrderEntryReadOnly || !billTo?.customerId,
      },
    ];
  }, [
    isDeleted,
    isOrderEntryReadOnly,
    billTo?.customerId,
    id,
    toggleOpenCustomerDialog,
    handleOnClickCustomerInfo,
    handleCashSale,
    handleClearBillingInfo,
  ]);

  const handleBillToShipAddress = useCallback(() => {
    // Checks if the shipping address has any information if has then prevent overwriting
    const hasShippingAdd = hasShippingAddress();
    if (hasShippingAdd) {
      toggleIsShippingAddress();
      return null;
    }

    const {
      name,
      customerPhone,
      orderedBy,
      orderEmail,
      orderPhone,
      address1,
      address2,
      city,
      zipCode,
      state,
      stateId,
      countryId,
      customerInstructions,
    } = billTo;

    // get the order by value base on the id
    const contact = OrderByList?.find(
      (item: OptionsListTypes) => item?.value === orderedBy?.value
    )?.label;

    form.setValue('shipTo', {
      shipLocation: { label: name, value: name },
      phone: {
        label: customerPhone?.label as string,
        value: customerPhone?.value as string,
      },
      contact: contact || '',
      contactEmail: orderEmail,
      contactPhone: normalizePhoneValue(orderPhone as unknown as string),
      address1,
      address2,
      city,
      state,
      stateId: stateId,
      zipCode,
      countryId,
      additionalInstructions: customerInstructions,
    });
    form.clearErrors('shipTo');
  }, [OrderByList, billTo, form, hasShippingAddress, toggleIsShippingAddress]);

  // find out the order phone list
  const handleOnchangeOrderBy = useCallback(
    async (value: string, setValues: boolean = true) => {
      const contact = customerContactsDetails?.find(
        (item: any) => getFullName(item) === value
      );

      const phoneList = generateLabelValuePairs({
        data: contact?.phones || [],
        labelKey: 'phoneno',
        valueKey: 'phoneno',
      });

      if (setValues) {
        form.setValue(
          'billTo.orderEmail',
          contact?.email || billTo?.orderEmail || ''
        );
        form.setValue('billTo.orderPhone', {
          label: normalizePhoneWithExtension(phoneList[0]?.value),
          value: normalizePhoneValue(phoneList[0]?.value),
        });
      }
    },
    [billTo?.orderEmail, customerContactsDetails, form, getFullName]
  );

  useEffect(() => {
    const customerId = billTo?.customerId?.value;
    if (!hasInitialCustomerInfoLoaded && id && customerId) {
      setHasInitialCustomerInfoLoaded(true);
      getCustomerById(customerId);
    }
  }, [billTo?.customerId, getCustomerById, hasInitialCustomerInfoLoaded, id]);

  useEffect(() => {
    if (getCustomerId) {
      handleChangeCustomer(getCustomerId);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [getCustomerId]);

  return (
    <div className="relative">
      <div className="space-y-4">
        <div className="grid grid-cols-3 items-center mt-1">
          <h3 className="col-span-2 text-xl font-semibold text-text-Default">
            Bill To
          </h3>
          <div className="flex items-center space-x-2 justify-end">
            <AppButton
              label={'Use as Shipping'}
              onClick={() => handleBillToShipAddress()}
              icon={ArrowRight}
              iconClassName="h-4"
              disabled={
                isDeleted ||
                isOrderEntryReadOnly ||
                !billTo?.customerId ||
                isMissingOrder
              }
              spinnerClass={
                'border-white-500  border-t-transparent animate-spin'
              }
              className="bg-brand-teal-Default hover:bg-brand-teal-hover border-border-brand-teal-Default"
            />
            <ActionColumnMenu
              triggerClassName="border h-10"
              contentClassName="px-4 py-3"
              dropdownMenuList={BillingDropdownMenu}
            />
          </div>
        </div>
        <div className="flex items-center w-full gap-3">
          <AutoCompleteDropdown
            label="Customer Name (Lookup)"
            placeholder="Select Customer Name"
            name="billTo.customerId"
            form={form}
            onSelectChange={(option) => handleChangeCustomer(option?.value)}
            url={CUSTOMER_API_ROUTES.ALL}
            labelKey="full_name"
            valueKey="customer_id"
            sortBy="first_name"
            labelComponent={(value, item) => (
              <div className={cn(!item?.isactive && 'text-red-500')}>
                {value} {item?.isactive ? '' : '(Inactive)'}
              </div>
            )}
            validation={TEXT_VALIDATION_RULE}
            isRefetch={isRefetchNewCustInfo}
            disabled={isDeleted || isOrderEntryReadOnly}
          />
          <CustomerLookup
            handleOk={(customer) => {
              handleChangeCustomer(customer?.customer_id?.toString());
              setCashSaleData((prevData) => ({
                ...prevData,
                isOpen: false,
              }));
            }}
            onCancel={() => {
              setCashSaleData((prevData) => ({
                ...prevData,
                isOpen: false,
              }));
            }}
            className={errors?.billTo?.customerId?.message ? 'mt-2' : 'mt-8'}
            disabled={isDeleted || isOrderEntryReadOnly}
            isOpen={cashSaleData.isOpen}
            searchFilter={cashSaleData.isOpen ? 'CASH SALE' : ''}
          />
        </div>
        <AutoCompleteDropdown
          label="Phone (Lookup)"
          placeholder="Select Phone"
          name="billTo.customerPhone"
          form={form}
          onSelectChange={(option) =>
            handleChangeCustomer(option?.item?.customer_id)
          }
          showItem
          url={CUSTOMER_API_ROUTES.ALL}
          labelKey="tel1"
          valueKey="tel1"
          sortBy="first_name"
          searchType="startsWith"
          formatSearch={(value) => `+1${value}`}
          operator={ComparisonOperator.STARTS_WITH}
          formatLabel={stripCountryCode}
          labelComponent={formatPhoneNumber}
          isRefetch={isRefetchNewCustInfo}
          disabled={isDeleted || isOrderEntryReadOnly}
          acceptAlphanumeric={false}
        />
        <div className="py-3">
          <Separator className="h-[1px] bg-border-Default" />
        </div>

        <SelectWidget
          form={form}
          optionsList={OrderByList}
          label="Ordered By"
          name="billTo.orderedBy"
          placeholder="Enter Ordered By"
          onChange={handleOnchangeOrderBy}
          disabled={isDeleted || isOrderEntryReadOnly || !billTo.customerId}
          allowCustomEntry
          returnOptionAsObject
        />
        <InputField
          name="billTo.orderEmail"
          form={form}
          label="Order E-mail"
          placeholder="Enter Order E-Mail"
          validation={EMAIL_VALIDATION_RULEs}
          disabled={isDeleted || isOrderEntryReadOnly}
        />
        <div className="grid grid-cols-2 gap-4">
          <SelectWidget
            form={form}
            optionsList={orderPhoneList || []}
            isLoading={customerLoading}
            label="Order Phone"
            name="billTo.orderPhone"
            placeholder="Select Order Phone"
            disabled={isDeleted || isOrderEntryReadOnly}
            isClearable={false}
            allowCustomEntry
            numericOnly
            formatCreateLabel={normalizePhoneWithExtension}
            returnOptionAsObject
            onSelectChange={(value: any) => {
              const formatedValue = normalizePhoneWithExtension(value.value);
              const orderPhone = {
                label: normalizePhoneWithExtension(formatedValue),
                value: normalizePhoneValue(formatedValue),
              };
              form.setValue('billTo.orderPhone', orderPhone as any);
            }}
          />

          <PhoneInputWidget
            form={form}
            name="billTo.orderFax"
            label="Order Fax"
            isFax={true}
            disabled
          />
        </div>
        <div className="grid grid-cols-2 gap-4">
          <InputField
            name="billTo.address1"
            form={form}
            label="Address"
            placeholder="Address"
            disabled
          />
          <InputField
            name="billTo.address2"
            form={form}
            label="Address Line 2"
            placeholder="Address"
            disabled
          />
          <InputField
            name="billTo.city"
            form={form}
            label="City"
            placeholder="City"
            disabled
          />
          <InputField
            name="billTo.state"
            form={form}
            label="State"
            placeholder="State"
            disabled
          />

          <InputField
            name="billTo.zipCode"
            form={form}
            label="Zip Code"
            placeholder="Zipcode"
            disabled
          />
          <InputField
            name="billTo.country"
            form={form}
            label="Country"
            placeholder="Country"
            disabled
          />
        </div>
        <TextAreaField
          form={form}
          name="billTo.customerInstructions"
          label="Default Location and Customer Special Instructions"
          disabled={isDeleted || isOrderEntryReadOnly || isDisabled}
          extraLabel={
            <div
              title={
                isDeleted || isOrderEntryReadOnly || isDisabled
                  ? 'Edit'
                  : 'View'
              }
            >
              <AppButton
                label={''}
                icon={
                  isDeleted || isOrderEntryReadOnly || isDisabled
                    ? Pencil
                    : PencilOff
                }
                iconClassName="w-5 h-5"
                onClick={handleEditClick}
                variant="neutral"
                className="border-none h-8 w-10"
              />
            </div>
          }
        />
        {/* Add New Customer */}
        {openCustomerDialog && (
          <CustomerInfoTabs
            open={openCustomerDialog}
            showTabMenu={isCustomerInfo}
            onOpenChange={toggleOpenCustomerDialog}
            handleOk={(value) => {
              isCustomerInfo && setIsRefetchNewCustInfo(true);
              handleChangeCustomer(value);
            }}
            firstName={
              cashSaleData.customerFirstName
                ? cashSaleData.customerFirstName
                : ''
            }
            lastName={
              cashSaleData.customerLastName ? cashSaleData.customerLastName : ''
            }
          />
        )}

        {/* //setCustomerLocation */}
        <AppConfirmationModal
          title="Confirmation"
          description={
            <div>
              Shipping infomation already exists on this order and will not be
              overwritten with the billing address.
            </div>
          }
          open={isShippingAddress}
          onOpenChange={toggleIsShippingAddress}
          handleSubmit={toggleIsShippingAddress}
          submitLabel="Ok"
        />
        <AppConfirmationModal
          title="Confirmation"
          description={
            defaultLocations?.length ? (
              <>
                There is currently shipping information on this order. Do you
                want to clear it and replace it with the customer's delivery
                location?
              </>
            ) : (
              <>
                There is currently shipping information on this order. Do you
                want to clear it?
              </>
            )
          }
          open={shouldClearAddr}
          onOpenChange={toggleShouldClearAddr}
          handleSubmit={() => handleAddClearShippingInfo(deliverylocation)}
          handleCancel={toggleShouldClearAddr}
        />
      </div>

      <CustomerDeliveryLocation
        open={openMultiLocaion}
        onOpenChange={toggleOpenMultiLocaion}
        handleOk={(location) => {
          const hasShippingAdd = hasShippingAddress();
          if (hasShippingAdd) {
            toggleShouldClearAddr();
            setDeliverylocation(location);
          } else {
            handleAddClearShippingInfo(location);
          }
        }}
        data={defaultLocations}
      />

      {/* No CASH SALE customer confirmation */}
      <AppConfirmationModal
        title="Confirmation"
        description={
          <div>
            Customer “Cash Sale” does not exist. Would you like to create this
            customer?
          </div>
        }
        open={cashSaleData.isCashSaleCustomer}
        onOpenChange={toggleIsCashSaleCustomer}
        handleCancel={toggleIsCashSaleCustomer}
        handleSubmit={() => {
          toggleIsCashSaleCustomer();
          setCashSaleData((prevData) => ({
            ...prevData,
            customerFirstName: 'CASH',
            customerLastName: 'SALE',
          }));
          toggleOpenCustomerDialog();
        }}
        submitLabel="Yes"
        cancelLabel="No"
      />

      {/* Warning popup for inactive customers */}
      <AppConfirmationModal
        title="Confirmation"
        description={
          <div>
            This customer is set to inactive. Please confirm before continuing
            with this order.
          </div>
        }
        open={
          isInActiveCustomer &&
          !openCustomerDialog &&
          !isShippingAddress &&
          !shouldClearAddr &&
          !openMultiLocaion
        }
        onOpenChange={toggleIsInActiveCustomer}
        handleSubmit={toggleIsInActiveCustomer}
        submitLabel="Ok"
      />

      {(customerLoading || isCashSaleLoading) && (
        <div className="absolute inset-0 flex items-center justify-center bg-white/50 backdrop-blur-[2px] z-10">
          <AppSpinner
            className="h-8 w-8 text-brand-teal-Default"
            isLoading={customerLoading || isCashSaleLoading}
          />
        </div>
      )}
    </div>
  );
};

export default BillingInfo;
