import AppButton from '@/components/common/app-button';
import DataTable from '@/components/common/data-tables';
import CustomDialog from '@/components/common/dialog';
import { cn, formatPhoneNumber } from '@/lib/utils';
import { RowSelectionState } from '@tanstack/react-table';
import { memo, useCallback, useMemo, useState } from 'react';

interface CustomerDeliveryLocationProps {
  open: boolean;
  onOpenChange: () => void;
  data: any[];
  handleOk: (location: any) => void;
  className?: string;
}
const CustomerDeliveryLocation = ({
  open,
  onOpenChange,
  data,
  handleOk,
}: CustomerDeliveryLocationProps) => {
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});

  const columns = useMemo(
    () => [
      {
        accessorKey: 'location',
        header: 'Delivery Location',
        size: 200,
      },
      { accessorKey: 'town', header: 'town', size: 130 },
      {
        accessorKey: 'phone',
        header: 'Phone',
        size: 150,
        cell: ({ row }: any) => formatPhoneNumber(row?.original?.phone ?? ''),
      },
    ],
    []
  );

  const selectedId = Object.keys(rowSelection)[0];

  // handle on click OK
  const handleOnClickOk = useCallback(() => {
    const location = data?.find(
      (item) => item?.deliverylocation_id === Number(selectedId)
    );
    handleOk(location);
    setRowSelection({});
    onOpenChange();
  }, [data, handleOk, onOpenChange, selectedId]);

  const handleCancel = useCallback(() => {
    onOpenChange();
    setRowSelection({});
  }, [onOpenChange]);

  return (
    <CustomDialog
      open={open}
      onOpenChange={handleCancel}
      title="Delivery Location Search"
      description=""
      className={cn('min-w-[50%] 2xl:min-w-[40%]')}
      contentClassName="h-[380px]"
    >
      <div className="grid grid-cols-1 px-6">
        <DataTable
          columns={columns}
          data={data}
          tableClassName="h-[300px] max-h-[300px] overflow-y-auto"
          enablePagination={false}
          enableRowSelection
          rowSelection={rowSelection}
          bindingKey="deliverylocation_id"
          onRowSelectionChange={setRowSelection}
        />

        <div className="flex justify-end gap-4 fixed bottom-0 right-6 bg-white pb-4">
          <AppButton
            onClick={handleOnClickOk}
            label="OK"
            disabled={!selectedId}
            className="w-28"
          />

          <AppButton
            onClick={handleCancel}
            label="Cancel"
            className="w-28"
            variant="neutral"
          />
        </div>
      </div>
    </CustomDialog>
  );
};

export default memo(CustomerDeliveryLocation);
