import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import ShippingInfo from './shippingInfo';
import { FormProvider, useForm } from 'react-hook-form';

// Mock the components and hooks
vi.mock('@/components/common/data-tables/ActionColumn', () => ({
  default: ({ dropdownMenuList }: any) => (
    <div>
      <button>Actions</button>
      <div data-testid="dropdown-menu">
        {dropdownMenuList.map((item: any) => (
          <div key={item.label} onClick={item.onClick}>
            {item.label}
          </div>
        ))}
      </div>
    </div>
  ),
}));

vi.mock('@/components/common/lookups/location-lookup', () => ({
  default: ({ handleOk, disabled }: any) => (
    <button
      onClick={() => handleOk({})}
      disabled={disabled}
      data-testid="location-lookup"
    >
      Location Lookup
    </button>
  ),
}));

vi.mock('@/components/forms/auto-complete-dropdown', () => ({
  default: ({ label, name, disabled }: any) => (
    <div>
      <label>{label}</label>
      <input
        name={name}
        disabled={disabled}
        data-testid={`auto-complete-${name}`}
      />
    </div>
  ),
}));

vi.mock('@/components/forms/input-field', () => ({
  default: ({ label, name, disabled }: any) => (
    <div>
      <label>{label}</label>
      <input name={name} disabled={disabled} data-testid={`input-${name}`} />
    </div>
  ),
}));

vi.mock('@/components/forms/phone-input-mask', () => ({
  default: ({ label, name, disabled }: any) => (
    <div>
      <label>{label}</label>
      <input
        name={name}
        disabled={disabled}
        data-testid={`phone-input-${name}`}
      />
    </div>
  ),
}));

vi.mock('@/components/forms/select', () => ({
  default: ({ label, name, disabled }: any) => (
    <div>
      <label>{label}</label>
      <select name={name} disabled={disabled} data-testid={`select-${name}`}>
        <option value="1">USA</option>
        <option value="2">Canada</option>
      </select>
    </div>
  ),
}));

vi.mock('@/components/forms/text-area', () => ({
  default: ({ label, name, disabled }: any) => (
    <div>
      <label>{label}</label>
      <textarea
        name={name}
        disabled={disabled}
        data-testid={`textarea-${name}`}
      />
    </div>
  ),
}));

vi.mock('@/components/forms/zipcode-input', () => ({
  default: ({ label, name, disabled }: any) => (
    <div>
      <label>{label}</label>
      <input name={name} disabled={disabled} data-testid={`zipcode-${name}`} />
    </div>
  ),
}));

vi.mock('@/components/ui/separator', () => ({
  Separator: () => <div data-testid="separator"></div>,
}));

// Mock the Redux hooks
vi.mock('@/redux/features/country/country.api', () => ({
  useGetCountryListQuery: vi.fn(() => ({
    data: [
      { country_id: 1, name: 'USA' },
      { country_id: 2, name: 'Canada' },
    ],
  })),
  useGetStateByCountryQuery: vi.fn(({ countryId }) => ({
    data:
      countryId === 1
        ? [
            { state_id: 1, code: 'NY' },
            { state_id: 2, code: 'CA' },
          ]
        : [
            { state_id: 3, code: 'ON' },
            { state_id: 4, code: 'BC' },
          ],
    isFetching: false,
  })),
}));

vi.mock('../new-location', () => ({
  default: ({ open }: any) =>
    open ? <div data-testid="new-location-dialog">NewLocations</div> : null,
}));

// Mock the icons
vi.mock('lucide-react', () => ({
  MapPinPlus: vi.fn(() => <div>MapPinPlus</div>),
  TrashIcon: vi.fn(() => <div>TrashIcon</div>),
}));

describe('ShippingInfo Component', () => {
  const TestComponent = ({ defaultValues = {} }: { defaultValues?: any }) => {
    const methods = useForm({
      defaultValues: {
        shipTo: {
          shipLocation: null,
          phone: null,
          contact: '',
          contactEmail: '',
          contactPhone: '',
          address1: '',
          city: '',
          state: '',
          stateId: '',
          address2: '',
          zipCode: '',
          countryId: 1,
          additionalInstructions: '',
          ...defaultValues.shipTo,
        },
        isDeleted: false,
        orderEntryReadOnly: false,
        ...defaultValues,
      },
    });

    return (
      <FormProvider {...methods}>
        <ShippingInfo />
      </FormProvider>
    );
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the component with default values', () => {
    render(<TestComponent />);
    expect(screen.getByText('Ship To')).toBeInTheDocument();
    expect(screen.getByText('Location (Lookup)')).toBeInTheDocument();
    expect(screen.getByText('Phone (Lookup)')).toBeInTheDocument();
    expect(screen.getByText('Contact')).toBeInTheDocument();
    expect(screen.getByText('Contact E-mail')).toBeInTheDocument();
    expect(screen.getByText('Contact Phone')).toBeInTheDocument();
    expect(screen.getByText('Address')).toBeInTheDocument();
    expect(screen.getByText('Address Line 2')).toBeInTheDocument();
    expect(screen.getByText('City')).toBeInTheDocument();
    expect(screen.getByText('State')).toBeInTheDocument();
    expect(screen.getByText('Zip Code')).toBeInTheDocument();
    expect(screen.getByText('Country')).toBeInTheDocument();
    expect(screen.getByText('Additional Instructions')).toBeInTheDocument();
  });

  it('displays action menu buttons', async () => {
    render(<TestComponent />);
    const actionButton = screen.getByText('Actions');
    fireEvent.click(actionButton);
    expect(screen.getByText('New Location')).toBeInTheDocument();
    expect(screen.getByText('Clear Info')).toBeInTheDocument();
  });

  it('opens new location dialog when "New Location" is clicked', async () => {
    render(<TestComponent />);
    const actionButton = screen.getByText('Actions');
    fireEvent.click(actionButton);
    const newLocationButton = screen.getByText('New Location');
    fireEvent.click(newLocationButton);
    await waitFor(() => {
      expect(screen.getByTestId('new-location-dialog')).toBeInTheDocument();
    });
  });

  it('clears ship to info when "Clear Info" is clicked', async () => {
    const defaultValues = {
      shipTo: {
        contact: 'John Doe',
        contactEmail: '<EMAIL>',
        address1: '123 Main St',
      },
    };
    render(<TestComponent defaultValues={defaultValues} />);
    const actionButton = screen.getByText('Actions');
    fireEvent.click(actionButton);
    const clearInfoButton = screen.getByText('Clear Info');
    fireEvent.click(clearInfoButton);
    await waitFor(() => {
      expect(screen.getByTestId('input-shipTo.contact')).toHaveValue('');
      expect(screen.getByTestId('input-shipTo.contactEmail')).toHaveValue('');
      expect(screen.getByTestId('input-shipTo.address1')).toHaveValue('');
    });
  });

  it('disables fields when isDeleted is true', () => {
    render(<TestComponent defaultValues={{ isDeleted: true }} />);
    expect(screen.getByTestId('input-shipTo.contact')).toBeDisabled();
    expect(screen.getByTestId('input-shipTo.contactEmail')).toBeDisabled();
    expect(
      screen.getByTestId('auto-complete-shipTo.shipLocation')
    ).toBeDisabled();
    expect(screen.getByText('Actions')).not.toBeDisabled();
  });

  it('disables fields when orderEntryReadOnly is true', () => {
    render(<TestComponent defaultValues={{ orderEntryReadOnly: true }} />);
    expect(screen.getByTestId('input-shipTo.contact')).toBeDisabled();
    expect(screen.getByTestId('input-shipTo.contactEmail')).toBeDisabled();
    expect(
      screen.getByTestId('auto-complete-shipTo.shipLocation')
    ).toBeDisabled();
    expect(screen.getByText('Actions')).not.toBeDisabled();
  });

  it('handles country change correctly', async () => {
    render(<TestComponent />);
    const countrySelect = screen.getByTestId('select-shipTo.countryId');
    fireEvent.change(countrySelect, { target: { value: '2' } });
    await waitFor(() => {
      expect(countrySelect).toHaveValue('2');
    });
  });
});
