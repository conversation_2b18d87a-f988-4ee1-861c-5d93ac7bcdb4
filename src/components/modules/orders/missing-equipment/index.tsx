import EditPencilIcon from '@/assets/icons/EditPencilIcon';
import SaveIcon from '@/assets/icons/SaveIcon';
import ActionArea from '@/components/common/action-area';
import AppButton from '@/components/common/app-button';
import AppDataTable from '@/components/common/app-data-table';
import AppTableContextProvider from '@/components/common/app-data-table/AppTableContext';
import BreadcrumbDialogRenderer from '@/components/common/BreadcrumbDialogRenderer';
import ActionColumnMenu from '@/components/common/data-tables/ActionColumn';
import { ORDERS_API_ROUTES } from '@/constants/api-constants';
import { formatDate, getQueryParam, updateQueryParam } from '@/lib/utils';
import {
  MISSING_EQUIPMENT,
  MISSING_EQUIPMENT_BREADCRUMB,
  MISSING_EQUIPMENT_TABS,
} from '@/types/order.types';
import { useCallback, useMemo, useState } from 'react';
import MissingItems from './missing-items';

interface MissingEquipmentProps {
  orderNo: string;
  customerName: string;
  customerId: number;
  open: boolean;
  setOpen: () => void;
}
const MissingEquipment = ({
  open,
  setOpen,
  orderNo,
  customerId,
  customerName,
}: MissingEquipmentProps) => {
  const orderId = getQueryParam('id') as string;
  const [activeTab, setActiveTab] = useState<MISSING_EQUIPMENT_TABS>(
    MISSING_EQUIPMENT.UPDATE
  );
  const [activeBreadcrumb, setActiveBreadcrumb] =
    useState<MISSING_EQUIPMENT_BREADCRUMB>(
      MISSING_EQUIPMENT.CREATE_MISSING_QUIPMENT
    );

  const isUpdateExisting = activeTab === MISSING_EQUIPMENT.UPDATE;

  // add edit missing items
  const handleChangeBreadcrumb = useCallback((orderId: string) => {
    setActiveBreadcrumb(MISSING_EQUIPMENT.MISSING_ITEMS);
    updateQueryParam(orderId, 'meId');
  }, []);

  const onOpenChange = useCallback(() => {
    setOpen();
    setActiveBreadcrumb(MISSING_EQUIPMENT.CREATE_MISSING_QUIPMENT);
    updateQueryParam(null, 'meId');
  }, [setOpen]);

  // handle on edit missing items
  const handleEditMissingItem = useCallback(
    (missingItemId?: number) => {
      if (missingItemId) {
        handleChangeBreadcrumb(missingItemId?.toString());
      }
    },
    [handleChangeBreadcrumb]
  );

  // coumns list
  const columns = useMemo(() => {
    return [
      {
        accessorKey: 'orderNo',
        header: 'M/E #',
        enableSorting: true,
        size: 80,
      },
      {
        accessorKey: 'dateOfUseFrom',
        header: 'Date of Use',
        enableSorting: true,
        size: 100,
        cell: ({ row }: any) => formatDate(row?.original?.dateOfUseFrom),
      },
      {
        accessorKey: 'missingItemInfo',
        header: 'Description',
        enableSorting: true,
        size: 250,
        maxSize: 250,
      },
      {
        id: 'action',
        size: 110,
        header: 'Actions',
        cell: ({ row }: any) => (
          <ActionColumnMenu
            label="Edit Missing Item"
            icon={EditPencilIcon}
            onEdit={() => handleEditMissingItem(row?.original?.id)}
          />
        ),
      },
    ];
  }, [handleEditMissingItem]);

  // missing equipment item list table & tab list
  const TabList = useMemo(() => {
    const filters = isUpdateExisting
      ? [
          {
            name: 'originOrderId',
            value: orderId,
            operator: 'Equals',
          },
        ]
      : [
          {
            name: 'customerId',
            value: customerId?.toString(),
            operator: 'Equals',
          },
        ];

    const content = (
      <div className="grid grid-cols-1">
        <AppTableContextProvider defaultSort={[{ id: 'orderNo', desc: true }]}>
          <AppDataTable
            url={ORDERS_API_ROUTES.MISSING_EQUIPMENT}
            filter={filters}
            tableClassName="max-h-[280px] 2xl:max-h-[400px] overflow-y-auto"
            columns={columns}
          />
        </AppTableContextProvider>
      </div>
    );
    return [
      {
        value: MISSING_EQUIPMENT.UPDATE,
        label: `Update M/E - Order #${orderNo}`,
        content,
      },
      {
        value: MISSING_EQUIPMENT.UPDATE_FOR_CUSTOMER,
        label: `Update M/E - ${customerName}`,
        content,
      },
    ];
  }, [columns, customerId, customerName, isUpdateExisting, orderId, orderNo]);

  // handle tab change
  const handleTabChange = useCallback(
    (value: string) => {
      setActiveTab(value as MISSING_EQUIPMENT_TABS);
      handleEditMissingItem();
    },
    [handleEditMissingItem]
  );

  const handleOnCancel = useCallback(() => {
    setActiveBreadcrumb(MISSING_EQUIPMENT.CREATE_MISSING_QUIPMENT);
    updateQueryParam(null, 'meId');
  }, []);

  // breadcrumb list for the missing item view
  const BreadcrumbList = useMemo(() => {
    return [
      {
        label: 'Create Missing Equipment',
        value: MISSING_EQUIPMENT.CREATE_MISSING_QUIPMENT,
        content: (
          <>
            <div className="flex justify-end pb-2">
              <AppButton
                label="Create New M/E"
                icon={SaveIcon}
                className="bg-brand-teal-Default hover:bg-brand-teal-Default"
                onClick={() => handleChangeBreadcrumb(orderId)}
              />
            </div>
            <ActionArea
              tabs={TabList}
              defaultValue={activeTab}
              onValueChange={handleTabChange}
            />
          </>
        ),
      },
      {
        label: 'Missing Items',
        value: MISSING_EQUIPMENT.MISSING_ITEMS,
        content: (
          <MissingItems onClick={onOpenChange} onCancel={handleOnCancel} />
        ),
      },
    ];
  }, [
    TabList,
    activeTab,
    handleChangeBreadcrumb,
    handleOnCancel,
    handleTabChange,
    onOpenChange,
    orderId,
  ]);

  return (
    <div className="px-6">
      <BreadcrumbDialogRenderer
        listItems={BreadcrumbList}
        activeTab={activeBreadcrumb}
        isOpen={open}
        setActiveTab={handleOnCancel}
        onOpenChange={onOpenChange}
        className="min-w-[90%] md:min-w-[70%] 2xl:max-w-[58%]"
        contentClassName="h-[500px] 2xl:h-[630px]"
      />
    </div>
  );
};
export default MissingEquipment;
