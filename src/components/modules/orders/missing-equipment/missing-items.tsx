import AppButton from '@/components/common/app-button';
import DataTable from '@/components/common/data-tables';
import NumberInputField from '@/components/forms/number-input-field';
import { REQUIRED_TEXT } from '@/constants/validation-constants';
import { convertToFloat, getQueryParam, updateQueryParam } from '@/lib/utils';
import {
  useGetMissingItemsListQuery,
  useSaveItemDetailsMutation,
} from '@/redux/features/orders/item-details.api';
import { useCreateMissingEquipmentItemsMutation } from '@/redux/features/orders/order.api';
import { SortingStateType } from '@/types/common.types';
import { MissingItemsFormTyps, MissingItemsTyp } from '@/types/order.types';
import { ColumnDef } from '@tanstack/react-table';
import isEqual from 'lodash/isEqual';
import { BadgeCheck } from 'lucide-react';
import { memo, useCallback, useEffect, useMemo, useState } from 'react';
import {
  Submit<PERSON>and<PERSON>,
  useFieldArray,
  useForm,
  UseFormReturn,
} from 'react-hook-form';

interface MissingItemsProps {
  onClick: () => void;
  onCancel?: () => void;
  existingOrderId?: string;
}

const MissingItems = ({ onClick, onCancel }: MissingItemsProps) => {
  const originalOrderId = getQueryParam('id') as string;
  const orderId = getQueryParam('meId') as string;
  const isNewMissingOrder = originalOrderId === orderId;

  // Sorting State
  const [sorting, setSorting] = useState<SortingStateType[]>([
    { id: 'itemId', desc: true },
  ]);

  // Fetch missing items only when orderId exists
  const { data: missingItemData, isLoading } = useGetMissingItemsListQuery(
    orderId,
    { skip: !orderId }
  );
  const orderItemList = missingItemData?.data;

  // Mutations for API interactions
  const [createNewMissingItems, { isLoading: addUpdateLoading }] =
    useCreateMissingEquipmentItemsMutation();
  const [updateMissingItems, { isLoading: updateLoading }] =
    useSaveItemDetailsMutation();

  // Memoized default values
  const defaultValues = useMemo(
    () => ({
      items:
        orderItemList?.map((item: MissingItemsFormTyps) => ({
          ...item,
          itemIdString: item?.itemIdString,
          missingQty: item?.missingQty || '',
          replacementCharge: item?.replacementCharge || '',
        })) || [],
    }),
    [orderItemList]
  );

  // Form setup
  const form: UseFormReturn<MissingItemsTyp> = useForm<MissingItemsTyp>({
    defaultValues,
    mode: 'onChange',
    resolver: async (data) => {
      const errors: any = { items: [] };
      data.items.forEach((item, index) => {
        const isRowFilled = item.missingQty || item.replacementCharge; // Check if any value is entered

        if (isRowFilled) {
          const itemErrors: any = {};

          if (!item?.missingQty) {
            itemErrors.missingQty = { message: REQUIRED_TEXT };
          }
          if (!item.replacementCharge) {
            itemErrors.replacementCharge = { message: REQUIRED_TEXT };
          }

          if (Object.keys(itemErrors).length > 0) {
            errors.items[index] = itemErrors;
          }
        }
      });

      return {
        values: data,
        errors: Object.keys(errors?.items)?.length ? errors : {},
      };
    },
  });

  // Field Array for Form
  const { fields } = useFieldArray<MissingItemsTyp>({
    control: form.control,
    name: 'items',
  });

  // Reset form when defaultValues change
  useEffect(() => {
    form.reset(defaultValues);
  }, [defaultValues, form]);

  // Handle form submission
  const onSubmit: SubmitHandler<MissingItemsTyp> = useCallback(
    async (formData) => {
      try {
        // filter out Missing Quantity & Replace Price value one
        const missingItemList = formData?.items?.filter(
          (item) => item.missingQty && item.replacementCharge
        );

        const missingItems = missingItemList?.map(
          ({ id, missingQty, replacementCharge }) => ({
            id,
            missingQty,
            replacementCharge,
            total: missingQty * replacementCharge,
          })
        );

        const payload = { orderId, items: missingItems };

        const { data }: any = isNewMissingOrder
          ? await createNewMissingItems({ body: payload })
          : await updateMissingItems({
              orderId: orderId,
              body: missingItemList?.map(
                ({ missingQty, replacementCharge, ...item }) => ({
                  ...item,
                  id: null,
                  price: replacementCharge,
                  quantity: missingQty,
                })
              ),
            });

        if (data?.statusCode === 200) {
          const missingOrderId = data.data?.id || orderId;
          updateQueryParam('', 'meId');
          updateQueryParam(missingOrderId);
          onClick();
        }
      } catch (error) {
        // console.error('Error submitting form:', error);
      }
    },
    [
      createNewMissingItems,
      isNewMissingOrder,
      onClick,
      orderId,
      updateMissingItems,
    ]
  );

  // Column Definitions (memoized)
  const columns: ColumnDef<MissingItemsFormTyps>[] = useMemo(
    () => [
      {
        accessorKey: 'itemIdString',
        header: 'Item ID',
        enableSorting: true,
        size: 200,
        maxSize: 200,
      },
      {
        accessorKey: 'serial',
        header: 'Serial #',
        enableSorting: true,
        size: 110,
      },
      { accessorKey: 'quantity', header: 'Quantity', size: 100 },
      {
        accessorKey: 'description',
        header: 'Description',
        size: 200,
        maxSize: 200,
      },
      {
        accessorKey: 'missingQty',
        header: 'Missing Quantity',
        size: 170,
        cell: ({ row }) => {
          const quantity = form.watch(`items.${row.index}.quantity`);
          return (
            <NumberInputField
              name={`items.${row.index}.missingQty`}
              form={form}
              placeholder="Missing Quantity"
              maxLength={5}
              decimalScale={0}
              maxValue={quantity}
              // errorMessage={
              //   errors?.items?.[row.index]?.missingQty?.message ?? ''
              // }
            />
          );
        },
      },
      {
        accessorKey: 'replacementCharge',
        header: 'Replace Price',
        size: 150,
        cell: ({ row }) => (
          <NumberInputField
            name={`items.${row.index}.replacementCharge`}
            form={form}
            placeholder="Replace Price"
            maxLength={11}
            decimalScale={2}
            fixedDecimalScale
            prefix="$"
            thousandSeparator=","
          />
        ),
      },
      {
        accessorKey: 'total',
        header: 'Total',
        size: 100,
        cell: ({ row }) => {
          const { missingQty, replacementCharge } = form.watch(
            `items.${row.index}`
          );
          return convertToFloat({
            value: missingQty * replacementCharge,
            prefix: '$',
          });
        },
      },
    ],
    [form]
  );

  // Check if form is modified
  const isFormModified = isEqual(form.watch('items'), defaultValues?.items);

  return (
    <div className="grid grid-cols-1">
      <DataTable
        columns={columns}
        data={fields || []}
        isLoading={isLoading}
        sorting={sorting}
        setSorting={setSorting}
        enablePagination={false}
        tableClassName="max-h-[380px] 2xl:max-h-[510px] overflow-y-auto"
      />
      <div className="flex items-center gap-4 absolute bottom-5 right-6">
        <AppButton
          label="Submit"
          onClick={form.handleSubmit(onSubmit)}
          icon={BadgeCheck}
          className="w-32"
          disabled={isFormModified}
          isLoading={addUpdateLoading || updateLoading}
        />
        <AppButton
          label="Cancel"
          variant="neutral"
          onClick={onCancel}
          className="w-32"
        />
      </div>
    </div>
  );
};

export default memo(MissingItems);
