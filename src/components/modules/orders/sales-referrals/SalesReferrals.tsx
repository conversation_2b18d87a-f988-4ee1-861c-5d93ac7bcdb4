'use client';

import { EyeIcon } from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';

import AppButton from '@/components/common/app-button';
import AppSpinner from '@/components/common/app-spinner';
import SwitchField from '@/components/common/switch';
import DatePicker from '@/components/forms/date-picker';
import InputField from '@/components/forms/input-field';
import NumberInputField from '@/components/forms/number-input-field';
import SelectWidget from '@/components/forms/select';

import {
  SalesReferralsCustomerTypes,
  SalesReferralsType,
  StoreSettings,
} from '@/types/order.types';
import { SwitchFieldConfig } from '@/types/store.types';

import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import CustomerLookup from '@/components/common/lookups/customer-lookup';
import { CUSTOMER_API_ROUTES } from '@/constants/api-constants';
import {
  formatPhoneNumber,
  generateLabel<PERSON><PERSON>uePairs,
  getQueryParam,
  searchPayload,
} from '@/lib/utils';
import { useGetAllMutation } from '@/redux/features/common-api/common.api';
import { useSalesPersonQuery } from '@/redux/features/customers/choices.api';
import {
  useGetSalesCustomerSearchMutation,
  useGetSalesPersonSalesReferralsMutation,
} from '@/redux/features/orders/order.api';
import { useGetSalesReferralsQuery } from '@/redux/features/orders/item-details.api';
import { CustomerDetailTypes } from '@/types/customer.types';
import { UseFormReturn } from 'react-hook-form';

interface SalesReferralsProps {
  form: UseFormReturn<SalesReferralsType>;
  userDefaultStoreInfo: StoreSettings;
}

const SalesReferrals = ({
  form,
  userDefaultStoreInfo,
}: SalesReferralsProps) => {
  const orderId = getQueryParam('id');
  const [showConfirmDialog, setShowConfirmDialog] = useState<boolean>(false);
  const [viewSalesCommission, setViewSalesCommission] = useState<boolean>(true);

  // Sales person details
  const { data: salespersonData, isLoading: salespersonLoading } =
    useSalesPersonQuery();

  // Customer details
  const url = CUSTOMER_API_ROUTES.ALL;
  const [
    getAllDataTable,
    { data: getAllData = { data: [] }, isLoading: isCustomerLoading },
  ] = useGetAllMutation();

  const [getSalesPersonCommision] = useGetSalesPersonSalesReferralsMutation();
  const [getSalesCustomerDetails] = useGetSalesCustomerSearchMutation();

  const { data, isLoading: isLoadingSalesReferrals } =
    useGetSalesReferralsQuery({ orderId });

  const disableFields = Boolean(form.watch('isApplyDiscountAsReferral'));

  const salesPersonList = generateLabelValuePairs({
    data: salespersonData?.data || [],
    labelKey: 'name',
    valueKey: 'id',
  });

  const customerList = generateLabelValuePairs({
    data: getAllData?.data || [],
    labelKey: 'full_name',
    valueKey: 'customer_id',
  });

  const phoneList = useMemo(() => {
    const list = generateLabelValuePairs({
      data: getAllData?.data || [],
      labelKey: 'tel1',
      valueKey: 'tel1',
    });
    return (
      list
        ?.filter((option) => option?.label?.replace('+1', ''))
        ?.map((items) => ({
          ...items,
          label: formatPhoneNumber(items?.label),
        })) || []
    );
  }, [getAllData?.data]);

  const switchFields: SwitchFieldConfig<SalesReferralsType>[] = [
    { label: 'Apply Discount as Referral', name: 'isApplyDiscountAsReferral' },
  ];

  const defaultValues = useMemo(() => {
    return {
      ...data?.data,
    };
  }, [data?.data]);

  const handleSalesPerson = useCallback(
    async (value: string) => {
      if (!value) return;

      const response = await getSalesPersonCommision(value);
      if (response?.data) {
        form.setValue(
          'salesCommissionPercentage',
          response?.data?.data?.salesCommission ?? 0
        );
      }
    },
    [form, getSalesPersonCommision]
  );

  const clearCustomerDetails = useCallback(() => {
    form.reset({
      ...form.getValues(),
      custReferralCommissionPercentage: null,
      customerReferralDTO: {
        address1: null,
        address2: null,
        city: null,
        customer_id: null,
        tel1: null,
        zipcode: null,
        country: null,
        state: null,
      },
      custId: null,
    });
  }, [form]);

  const handleChangePhone = (phone: string) => {
    if (!phone) {
      clearCustomerDetails();
      return;
    }

    const customerData = getAllData.data.find(
      (customer: SalesReferralsCustomerTypes) => customer?.tel1 === phone
    );

    if (customerData?.customer_id) {
      handleChangeCustomer(customerData.customer_id);
    }
  };

  const handleChangeCustomer = useCallback(
    async (customer: CustomerDetailTypes | string | number) => {
      if (!customer) {
        clearCustomerDetails();
        return;
      }
      const customerId =
        typeof customer === 'number' || typeof customer === 'string'
          ? customer
          : customer.customer_id;

      if (customerId) {
        const response = await getSalesCustomerDetails(customerId);
        const customerData = response?.data?.data;

        if (customerData) {
          Object.entries(customerData).forEach(
            ([key, value]: [string, any]) => {
              form.setValue(
                `customerReferralDTO.${key}` as `customerReferralDTO.${keyof SalesReferralsCustomerTypes}`,
                value
              );
            }
          );
          form.setValue(
            'custReferralCommissionPercentage',
            customerData?.referralcommpct || 0
          );

          form.setValue('custId', customerId);
        }
      }
    },
    [clearCustomerDetails, form, getSalesCustomerDetails]
  );

  // Handlers toggle
  const toggleConfirmDialog = () => {
    // Check if name or phone has values before clearing
    const nameHasValue = !!form.getValues('customerReferralDTO.customer_id');
    const phoneHasValue = !!form.getValues('customerReferralDTO.address1');
    const discountReferral = !!form.getValues('isApplyDiscountAsReferral');

    if (nameHasValue && phoneHasValue && discountReferral) {
      setShowConfirmDialog(true);
    }
  };

  const confirmDiscountReferral = () => {
    form.reset({
      ...form.getValues(),
      isApplyDiscountAsReferral: true,
      custReferralCommissionPercentage: 0,
      customerReferralDTO: {
        address1: '',
        address2: '',
        city: '',
        customer_id: 0,
        tel1: undefined,
        zipcode: '',
        country: '',
        state: '',
      },
      custId: 0,
    });
    setShowConfirmDialog(false);
  };

  const cancelDiscountReferral = () => {
    form.setValue('isApplyDiscountAsReferral', false);
    setShowConfirmDialog(false);
  };

  // Effect for search payload and data fetching
  useEffect(() => {
    const payload = searchPayload({
      pageNumber: -1,
      pageSize: -1,
      sortBy: '',
      sortAscending: false,
      filters: [{ field: '', value: '', operator: '' }],
    });

    getAllDataTable({ url, type: 'POST', body: payload });
  }, [getAllDataTable, url]);

  useEffect(() => {
    if (data?.data) {
      form.reset(defaultValues);
    }
  }, [data?.data, defaultValues, form]);

  const orderTotal = form.watch('orderCommissionTotal');
  const custPercentage = form.watch('custReferralCommissionPercentage');
  const salesPercentage = form.watch('salesCommissionPercentage');

  useEffect(() => {
    const calculatedCommissionCus =
      +orderTotal * (Number(custPercentage) / 100);
    const calculatedCommissionSales = +orderTotal * (+salesPercentage / 100);

    form.setValue('custReferralCommissionAmount', calculatedCommissionCus);
    form.setValue('salesCommissionAmount', calculatedCommissionSales);
  }, [orderTotal, custPercentage, salesPercentage, handleSalesPerson, form]);

  return (
    <>
      {/* Header */}
      <div className="flex items-center justify-between gap-3">
        <h3 className="text-xl font-semibold text-text-Default">
          Sales & Referrals
        </h3>
        <div className="flex items-center gap-x-2">
          {switchFields.map((field) => (
            <div key={field.name} className="mt-4">
              <SwitchField
                {...field}
                form={form}
                className="bg-white border border-border-Default rounded-lg p-2 mb-4"
                onChange={() => {
                  if (form.watch('isApplyDiscountAsReferral'))
                    toggleConfirmDialog();
                }}
              />
            </div>
          ))}
        </div>
      </div>

      {/* Form Layout */}
      <div className="flex gap-6 p-4 border border-border-Default rounded-lg">
        {/* Left Column: Referral Info */}
        <div className="grid grid-cols-1 gap-6 w-2/3">
          <h3 className="text-xl font-semibold text-text-Default">
            Order Referred By
          </h3>

          <div className="flex gap-5">
            <div className="w-[80%]">
              <SelectWidget
                name="customerReferralDTO.customer_id"
                label="Name"
                form={form}
                placeholder="Select Customer Name"
                optionsList={customerList}
                isClearable
                disabled={disableFields}
                onSelectChange={handleChangeCustomer}
              />
            </div>
            <div className="w-[20%] relative">
              <CustomerLookup
                handleOk={(customer) => handleChangeCustomer(customer)}
                className="w-full absolute bottom-0 right-0"
                disabled={disableFields}
              />
            </div>
          </div>

          <SelectWidget
            name="customerReferralDTO.tel1"
            label="Phone"
            form={form}
            placeholder="Select Phone"
            optionsList={phoneList}
            isClearable
            disabled={disableFields}
            onSelectChange={handleChangePhone}
          />
          <InputField
            name="customerReferralDTO.address1"
            label="Address Line 1"
            placeholder="Add Address Line 1"
            form={form}
            disabled
          />
          <InputField
            name="customerReferralDTO.address2"
            label="Address Line 2"
            placeholder="Add Address Line 2"
            form={form}
            disabled
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <InputField
              name="customerReferralDTO.city"
              label="City"
              placeholder="Add City"
              form={form}
              disabled
            />
            <InputField
              name="customerReferralDTO.state"
              label="State"
              placeholder="Add State"
              form={form}
              disabled
            />
            <InputField
              name="customerReferralDTO.zipcode"
              label="Zip Code"
              placeholder="Add Zip Code"
              form={form}
              disabled
            />
            <InputField
              name="customerReferralDTO.country"
              label="Country"
              placeholder="Add Country"
              form={form}
              disabled
            />
          </div>

          <InputField
            name="custReferredBy"
            label="Referred By"
            placeholder="Enter Referred By"
            form={form}
            maxLength={24}
          />

          <h3 className="text-xl font-semibold text-text-Default mt-11">
            Order Sold By
          </h3>

          <SelectWidget
            name="salespersonId"
            label="Salesperson"
            form={form}
            placeholder="Select Salesperson"
            optionsList={salesPersonList}
            isClearable
            disabled={userDefaultStoreInfo?.lockSalePersonField}
            onSelectChange={handleSalesPerson}
          />
        </div>

        {/* Right Column: Commission Info */}
        <div className="grid grid-cols-1 gap-6 w-1/3">
          <h3 className="text-xl font-semibold text-text-Default">
            Commission Info
          </h3>

          <NumberInputField
            name="orderCommissionTotal"
            label="Total for commission"
            prefix="$"
            form={form}
            disabled
          />
          <NumberInputField
            name="custReferralCommissionPercentage"
            label="Referral Commission Percent"
            suffix="%"
            maxLength={8}
            form={form}
            disabled={disableFields}
            fixedDecimalScale
          />
          <NumberInputField
            name="custReferralCommissionAmount"
            label="Referral Commission"
            prefix="$"
            form={form}
            disabled
            fixedDecimalScale
          />
          <InputField
            name="custPaymentReference"
            label="Referral Reference #"
            placeholder="Enter Referral Reference"
            form={form}
            maxLength={35}
          />
          <DatePicker
            name="custPaymentDate"
            label="Referral Payment Date"
            placeholder="Select Date"
            enableInput
            form={form}
          />
          {viewSalesCommission ? (
            <>
              <NumberInputField
                name="salesCommissionPercentage"
                label="Sales Commission Percent"
                form={form}
                suffix="%"
                maxLength={8}
                fixedDecimalScale
              />
              <NumberInputField
                name="salesCommissionAmount"
                label="Sales Commission"
                prefix="$"
                form={form}
                disabled
                fixedDecimalScale
              />
            </>
          ) : (
            <>
              <AppButton
                label="View Sales Commission"
                icon={EyeIcon}
                iconClassName="w-5"
                className="bg-brand-teal-Default hover:bg-brand-teal-hover border-border-brand-teal-Default text-white mt-[30px]"
                onClick={() => {
                  setViewSalesCommission(true);
                }}
              />
              <div className="h-[70px]"></div>
            </>
          )}
          <InputField
            name="salesPaymentReference"
            label="Sales Reference #"
            placeholder="Enter Sales Reference"
            form={form}
            maxLength={35}
          />
          <DatePicker
            name="salesPaymentDate"
            label="Sales Payment Date"
            placeholder="Select Date"
            enableInput
            form={form}
          />
        </div>
      </div>

      {/* Confirmation Dialog */}
      <AppConfirmationModal
        title={'Warning'}
        open={showConfirmDialog}
        description={
          <div>
            <p>
              This will replace the current referral customer on this order. Do
              you want to continue?
            </p>
          </div>
        }
        handleSubmit={confirmDiscountReferral}
        submitLabel="Ok"
        cancelLabel="Cancel"
        handleCancel={cancelDiscountReferral}
      />

      {/* Loader */}
      <AppSpinner
        overlay
        isLoading={
          salespersonLoading || isLoadingSalesReferrals || isCustomerLoading
        }
      />
    </>
  );
};

export default SalesReferrals;
