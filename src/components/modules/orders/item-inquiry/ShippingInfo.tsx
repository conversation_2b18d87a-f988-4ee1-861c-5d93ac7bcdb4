import AppButton from '@/components/common/app-button';
import Input<PERSON>ield from '@/components/forms/input-field';
import PhoneInputWidget from '@/components/forms/phone-input-mask';
import SelectWidget from '@/components/forms/select';
import ZipCodeInput from '@/components/forms/zipcode-input';
import { TEXT_VALIDATION_RULE } from '@/constants/validation-constants';
import { generateLabelValuePairs } from '@/lib/utils';
import { useGetStateByCountryQuery } from '@/redux/features/country/country.api';
import { ShippingInfoProps } from '@/types/item-inquiry.types';
import { shipToType } from '@/types/order.types';
import { useCallback, useEffect, useMemo } from 'react';
import { useForm, UseFormReturn } from 'react-hook-form';

const ShippingInfo = ({ info, onChange, onCancel }: ShippingInfoProps) => {
  const defaultValues = useMemo(() => {
    return {
      contactPhone: info?.contactPhone ?? '',
      address1: info?.address1 ?? '',
      stateId: info?.stateId,
      city: info?.city ?? '',
      zipCode: info?.zipCode ?? '',
    };
  }, [
    info?.address1,
    info?.city,
    info?.contactPhone,
    info?.stateId,
    info?.zipCode,
  ]);

  const form: UseFormReturn<shipToType> = useForm<shipToType>({
    defaultValues,
  });

  useEffect(() => {
    if (defaultValues) {
      form.reset(defaultValues);
    }
  }, [defaultValues, form]);

  // state list base on the country id
  const { data: statesData = [], isFetching: stateIsLoading } =
    useGetStateByCountryQuery({ countryId: 1 });

  // state list
  const stateList = generateLabelValuePairs({
    data: statesData,
    labelKey: 'code',
    valueKey: 'state_id',
  });

  const onSubmit = useCallback(
    (formData: shipToType) => {
      onChange({ ...formData, stateId: formData?.stateId?.toString() ?? '' });
      onCancel();
    },
    [onCancel, onChange]
  );

  return (
    <div className="grid grid-cols-2 gap-y-3 gap-x-5">
      <PhoneInputWidget
        form={form}
        name="contactPhone"
        label="Contact Phone"
        validation={TEXT_VALIDATION_RULE}
      />
      <InputField
        name="address1"
        form={form}
        label="Address"
        placeholder="Enter Address"
        validation={TEXT_VALIDATION_RULE}
        isShowError={true}
      />
      <InputField
        name="city"
        form={form}
        label="City"
        placeholder="Enter City"
        validation={TEXT_VALIDATION_RULE}
      />
      <SelectWidget
        name="stateId"
        form={form}
        placeholder="Select State"
        label="State"
        isClearable={false}
        optionsList={stateList}
        isLoading={stateIsLoading}
        validation={TEXT_VALIDATION_RULE}
        menuPosition="absolute"
      />

      <ZipCodeInput
        name="zipCode"
        isUSA={true}
        form={form}
        label="Zip Code"
        validation={TEXT_VALIDATION_RULE}
      />
      <div className="col-span-2 mt-5 flex justify-end">
        <AppButton
          label="Submit"
          onClick={form.handleSubmit(onSubmit)}
          className="w-32"
        />
      </div>
    </div>
  );
};

export default ShippingInfo;
