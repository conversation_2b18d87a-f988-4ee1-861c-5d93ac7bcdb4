import CheveronLeft from '@/assets/icons/CheveronLeft';
import AppButton from '@/components/common/app-button';
import CustomDialog from '@/components/common/dialog';
import IconButton from '@/components/common/icon-button';
import AutoCompleteDropdown from '@/components/forms/auto-complete-dropdown';
import DatePicker from '@/components/forms/date-picker';
import InputField from '@/components/forms/input-field';
import MultiCheckboxDropdown from '@/components/forms/MulitCheckbox';
import {
  CATEGORY_API_ROUTES,
  CUSTOMER_API_ROUTES,
  DELIVERY_LOCATION_API_ROUTES,
  ITEMS_API_ROUTES,
} from '@/constants/api-constants';
import { EMAIL_VALIDATION_RULEs } from '@/constants/auth-constants';
import { ROUTES } from '@/constants/routes-constants';
import { TEXT_VALIDATION_RULE } from '@/constants/validation-constants';
import useOptionList from '@/hooks/useOptionList';

import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import { formatDateWithTimezone } from '@/lib/formatDateWithTimezone';
import {
  cn,
  DATE_FORMAT_YYYYMMDD,
  formatDate,
  getDayInitial,
  getPaginationObject,
  getStorageValue,
} from '@/lib/utils';
import { useGetListItemsMutation } from '@/redux/features/common-api/common.api';
import {
  useDateCalculationMutation,
  useLazyGetBusyStatusByDateQuery,
  useSaveOrderEnquiryMutation,
} from '@/redux/features/orders/order.api';
import {
  ItemInquiryFormType,
  ItemInquiryItemsType,
  LocationTyps,
  RequireShippingInfoTypes,
} from '@/types/item-inquiry.types';
import { RowSelectionState } from '@tanstack/react-table';
import dayjs from 'dayjs';
import { ArrowDownToLine, PackagePlus, X } from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { Path, useFieldArray, useForm, UseFormReturn } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import ItemInquiryItems from './ItemInquiryItems';
import ShippingInfo from './ShippingInfo';
import debounce from 'lodash/debounce';
import { NumberInputField } from '@/components/forms';

const ItemInquiry = () => {
  const navigation = useNavigate();
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const [requireShippingInfo, setRequireShippingInfo] =
    useState<boolean>(false);
  // Flag to indicate whether the "Use From Date" is marked as busy on the calendar
  const [isUseFromDateBusy, setIsUseFromDateBusy] = useState<boolean>(false);
  const [showPastDateWarningModal, setShowPastDateWarningModal] =
    useState<boolean>(false);

  const [warningModal, setWarningModal] = useState<{
    message: string;
    isOpen: boolean;
  }>({ message: '', isOpen: false });

  const tz = getStorageValue('timeZoneOffset') || '';
  const currentDate = formatDateWithTimezone({ tz });
  const defaultLocationId = getStorageValue('defaultLocationId') || '';

  // save new order
  const [saveOrderEnquiry, { isLoading: saveLoading }] =
    useSaveOrderEnquiryMutation();

  // item list api
  const [getItems, { data: itemsData, isLoading }] = useGetListItemsMutation();
  const ItemsList = useMemo(() => {
    return itemsData?.data?.map((item: ItemInquiryItemsType) => ({
      ...item,
      itemInquiryId: item?.id,
    }));
  }, [itemsData?.data]);

  // Fetch busy status for the "Use From" date; skips the call if the date is not available
  const [getBusyStatusByDate] = useLazyGetBusyStatusByDateQuery();

  // date calculation
  const [dateCalculation] = useDateCalculationMutation();

  const defaultValues = useMemo(() => {
    return {
      itemId: { label: '', value: '' },
      category: '',
      dateOfUseFrom: currentDate,
      shipOrder: {
        shipDate: currentDate,
        returnArrivalDate: dayjs(currentDate)
          .add(1, 'day')
          .format(DATE_FORMAT_YYYYMMDD),
      },
      items: [],
      storeItems: [],
      rentDays: 1,
    };
  }, [currentDate]);

  const form: UseFormReturn<ItemInquiryFormType> =
    useForm<ItemInquiryFormType>();

  const { fields } = useFieldArray<ItemInquiryFormType>({
    control: form.control,
    name: 'items',
  });
  // store item fields
  const { fields: storeItemFields, remove } =
    useFieldArray<ItemInquiryFormType>({
      control: form.control,
      name: 'storeItems',
    });

  useEffect(() => {
    if (defaultValues) {
      form.reset(defaultValues);
    }
  }, [defaultValues, form]);

  const itemInquiryInfo = form.watch();
  const category = itemInquiryInfo?.category?.toString();
  const description = itemInquiryInfo?.description;
  const itemId = itemInquiryInfo?.itemId?.label;

  const fetchItems = useCallback(async () => {
    const category = form.watch('category');
    const itemId = form.watch('itemId');
    const description = form.watch('description');

    const payload = getPaginationObject({
      pagination: { pageIndex: -1, pageSize: -1 },
      sorting: [{ id: 'itemId', desc: true }],
      filters: [
        {
          field: 'category',
          value: category?.toString(),
          operator: 'Equals',
        },
        {
          field: 'filter',
          value: itemId?.label,
          operator: 'Equals',
        },
        {
          field: 'description',
          value: description ?? '',
          operator: 'Contains',
        },
        { field: 'isActive', value: 'true', operator: 'boolean' },
      ],
    });

    const { data } = await getItems({
      url: ITEMS_API_ROUTES.ALL,
      data: payload,
    });

    setRowSelection({});
    const transformedItems =
      data?.data?.map((item: ItemInquiryItemsType) => ({
        ...item,
        itemInquiryId: item?.id,
      })) ?? [];
    form.setValue('items', transformedItems);
  }, [form, getItems]);

  const debouncedFetchItems = useMemo(
    () => debounce(fetchItems, 500),
    [fetchItems]
  );

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      debouncedFetchItems.cancel();
    };
  }, [debouncedFetchItems]);

  // navigate to back
  const navigateToOrder = useCallback(() => {
    navigation(ROUTES.ORDERS);
  }, [navigation]);

  // category List
  const { options: categoryList, optionLoading } = useOptionList({
    url: CATEGORY_API_ROUTES.ALL,
    labelKey: 'catDesc',
    valueKey: 'catDesc',
    sortBy: 'catDesc',
  });

  const SelectedId = useMemo(
    () => Object.keys(rowSelection)?.map(Number),
    [rowSelection]
  );

  // handle add items to store items
  const handleStoreItemWQty = useCallback(() => {
    const newStoreItems = itemInquiryInfo?.items?.filter((storeItem) =>
      SelectedId?.includes(storeItem?.itemInquiryId)
    );

    form.setValue('items', ItemsList);
    setRowSelection({});
    form.setValue('storeItems', [...storeItemFields, ...newStoreItems]);
  }, [ItemsList, SelectedId, form, itemInquiryInfo?.items, storeItemFields]);

  // toggle require ShippingInfo
  const togglesRequireShippingInfo = useCallback(() => {
    setRequireShippingInfo((prev) => !prev);
  }, []);

  // Check required location fields; show popup if any are missing.
  const handleCheckRequireShippingInfo = useCallback(
    (info: RequireShippingInfoTypes) => {
      const hasMissingValue = Object.values(info)?.some((value) => !value);
      if (hasMissingValue) {
        togglesRequireShippingInfo();
      }
      return hasMissingValue;
    },
    [togglesRequireShippingInfo]
  );

  // handle add items to store / save new order items
  const onSubmit = useCallback(
    async (formData: ItemInquiryFormType) => {
      const hasMissingValue = handleCheckRequireShippingInfo({
        contactPhone: formData?.shipTo?.contactPhone,
        address1: formData?.shipTo?.address1,
        stateId: formData?.shipTo?.stateId?.toString() ?? '',
        city: formData?.shipTo?.city,
        zipCode: formData?.shipTo?.zipCode,
      });
      if (hasMissingValue) return;
      const { dateOfUseFrom, billTo, shipTo, storeItems, shipOrder, rentDays } =
        formData;
      const payload = {
        storeLocationId: 1,
        orderType: 'RENTAL_ORDER',
        eventFrequency: 'ONE_TIME',
        dateOfUseFrom: formatDate(dateOfUseFrom, DATE_FORMAT_YYYYMMDD),
        dayOfUseFrom: getDayInitial({ date: dateOfUseFrom }),
        rentDays,
        shipOrder: {
          shipDate: formatDate(shipOrder.shipDate, DATE_FORMAT_YYYYMMDD),
          returnArrivalDate: formatDate(
            shipOrder.returnArrivalDate,
            DATE_FORMAT_YYYYMMDD
          ),
          shipDay: getDayInitial({ date: shipOrder.shipDate }),
        },
        billTo: {
          customerId: billTo?.customerId?.value,
          orderEmail: billTo?.orderEmail,
        },
        shipTo: {
          ...shipTo,
          shipLocation: shipTo?.shipLocation?.value,
          countryId: 1,
        },
        items: storeItems?.map((item) => ({
          id: null,
          itemId: item?.itemInquiryId,
          description: item?.description,
          serialNumber: item?.serialNumber,
          type: item?.itemType,
          subRental: item?.subRental,
          quantity: item?.quantity,
          price: item?.unitPrice,
        })),
      };

      const { data } = await saveOrderEnquiry(payload).unwrap();
      const orderId = data?.id;
      if (orderId) {
        navigation(`${ROUTES.EDIT_ORDERS}?id=${orderId}&tab=information`);
      }
    },
    [handleCheckRequireShippingInfo, navigation, saveOrderEnquiry]
  );

  // handle remove stored item
  const handleRemoveStoreItem = useCallback(
    (index: number) => {
      remove(index);
    },
    [remove]
  );

  // handle change customer
  const handleChangeCustomer = useCallback(
    (option: { item?: { orderEmail: string } }) => {
      form.clearErrors('billTo.customerId');
      form.setValue('billTo.orderEmail', option?.item?.orderEmail ?? '');
    },
    [form]
  );

  // set Require Shipping Info
  const handleSetRequireShippingInfo = useCallback(
    (info: RequireShippingInfoTypes) => {
      form.setValue('shipTo.contactPhone', info?.contactPhone);
      form.setValue('shipTo.address1', info?.address1);
      form.setValue('shipTo.stateId', info?.stateId);
      form.setValue('shipTo.city', info?.city);
      form.setValue('shipTo.zipCode', info?.zipCode);
    },
    [form]
  );

  // handle change location
  const handleChangeLocation = useCallback(
    (option: { item?: LocationTyps }) => {
      const { contactPhone, location, stateId, town, zipcode } =
        option?.item || {};
      const info = {
        contactPhone: contactPhone ?? '',
        address1: location ?? '',
        stateId: stateId ?? '',
        city: town ?? '',
        zipCode: zipcode ?? '',
      };
      handleSetRequireShippingInfo(info);
      // Check required location fields; show popup if any are missing.
      handleCheckRequireShippingInfo(info);
    },
    [handleCheckRequireShippingInfo, handleSetRequireShippingInfo]
  );

  const onRowsSelectedChanges = useCallback(
    (selectedRow: ItemInquiryItemsType[]) => {
      const currentItems = form.getValues('items') || [];
      const originalItems = ItemsList;

      const selectedIdsSet = new Set(
        selectedRow.map((item) => item.itemInquiryId)
      );

      const updatedItems = currentItems.map((item) => {
        const isSelected = selectedIdsSet.has(item.itemInquiryId);
        // If item is selected, leave it unchanged
        if (isSelected) return item;
        // If item is unselected, reset its values to original
        const originalItem = originalItems.find(
          (ori: ItemInquiryItemsType) =>
            ori.itemInquiryId === item.itemInquiryId
        );
        // If no original item found (shouldn’t happen), return item unchanged
        if (!originalItem) return item;
        return {
          ...item,
          quantity: originalItem.quantity ?? '',
          unitPrice: originalItem.unitPrice ?? '',
        };
      });

      form.setValue('items', updatedItems);
    },
    [ItemsList, form]
  );

  // check selected item
  const isAllSelectedItemsValid = () => {
    if (!SelectedId?.length) return true;
    const items = form.watch('items');
    const selectedItems = items?.filter((item) =>
      SelectedId?.includes(item?.itemInquiryId)
    );

    if (!selectedItems?.length) return true;
    return !selectedItems?.every((item) => item?.quantity && item?.unitPrice);
  };

  const disableStoreItems = isAllSelectedItemsValid();
  //Create Order with Store Items
  const validateAllStoreItem = () => {
    const currentItems = form.getValues('storeItems');
    if (!currentItems?.length) return true;
    return !currentItems?.every((item) => item?.quantity && item?.unitPrice);
  };

  const isCreateOrderItems = validateAllStoreItem();

  // handle date of use base date calculations
  const onDateChange = useCallback(
    async (date?: string | Date) => {
      try {
        const payload = {
          deliveryTypeId: 1,
          dateOfUseFrom: formatDate(date ?? '', DATE_FORMAT_YYYYMMDD),
          dateOfUseThru: null,
          storeLocationId: defaultLocationId,
          rentDays: 0,
          orderType: 'RENTAL_ORDER',
          orderCategory: 'SHIP',
        };

        const { data } = await dateCalculation({
          body: payload,
        }).unwrap();
        Object.entries(data).forEach(([key, value]) =>
          form.setValue(
            `shipOrder.${key}` as Path<ItemInquiryFormType>,
            value as string
          )
        );
      } catch (error) {
        // console.error('Error in date calculation:', error);
      }
    },
    [dateCalculation, defaultLocationId, form]
  );

  // Checks if the provided date is marked as busy from the server.
  const checkFromDateBusyStatus = useCallback(
    async (date?: Date | string) => {
      if (date) {
        const { data } = await getBusyStatusByDate(
          formatDate(date, DATE_FORMAT_YYYYMMDD)
        );

        if (data?.data?.busy) {
          setIsUseFromDateBusy(true);
        }
      }
    },
    [getBusyStatusByDate]
  );

  const pickupDate = form.watch('shipOrder.shipDate');
  const returnArrivalDate = form.watch('shipOrder.returnArrivalDate');

  const handleWarningOnBlur = useCallback(
    (
      date: Date | string | undefined,
      fieldName: 'shipDate' | 'returnArrivalDate'
    ) => {
      if (!date) return;
      const selectedDate = dayjs(date);
      const dateOfUseFrom = dayjs(itemInquiryInfo?.dateOfUseFrom);
      if (!selectedDate.isValid() || !dateOfUseFrom.isValid()) return;
      let message = '';
      if (
        fieldName === 'shipDate' &&
        selectedDate.isAfter(dateOfUseFrom, 'day')
      ) {
        form.setValue('shipOrder.shipDate', pickupDate);
        message = 'The Delivery Date cannot be later than the Date of Use.';
      }
      if (
        fieldName === 'returnArrivalDate' &&
        selectedDate.isBefore(dateOfUseFrom, 'day')
      ) {
        form.setValue('shipOrder.returnArrivalDate', returnArrivalDate);
        message = 'The Pickup Date cannot be earlier than the Date of Use.';
      }
      if (message) {
        setWarningModal({ message, isOpen: true });
      }
    },
    [form, itemInquiryInfo?.dateOfUseFrom, pickupDate, returnArrivalDate]
  );

  // Header section
  const Header = () => {
    return (
      <div className="flex gap-x-4 items-center justify-between sticky top-16 pt-3 pb-2 bg-white z-20 mb-4">
        <div className="flex items-center gap-3">
          <IconButton onClick={navigateToOrder}>
            <CheveronLeft />
          </IconButton>
          <h1 className="text-2xl font-semibold">Item Inquiry</h1>
        </div>

        <AppButton
          label="Cancel"
          icon={X}
          onClick={navigateToOrder}
          iconClassName="w-4 h-4"
          variant="neutral"
        />
      </div>
    );
  };

  return (
    <div className="px-6 mb-5">
      <Header />
      <div className="p-4 border rounded-md flex flex-wrap justify-between">
        <div className="grid grid-cols-3 space-x-4 flex-wrap">
          <AutoCompleteDropdown
            name="itemId"
            label="Item ID"
            placeholder="Select item ID"
            form={form}
            url={ITEMS_API_ROUTES.ALL}
            labelKey="itemId"
            valueKey="id"
            sortBy="itemId"
            className="z-[11] w-60"
            isClearable={true}
            onSelectChange={() => fetchItems()}
            filterBy={[
              { field: 'isActive', value: 'true', operator: 'boolean' },
            ]}
          />
          <MultiCheckboxDropdown
            name="category"
            form={form}
            optionsList={categoryList ?? []}
            placeholder={'Select Categories'}
            label="Categories"
            isLoading={optionLoading}
            className="w-full"
            onChange={() => fetchItems()}
          />

          <InputField
            name="description"
            form={form}
            label="Description"
            placeholder="Enter Description"
            maxLength={65}
            pClassName="w-[220px]"
            callOnChangeOnBlur={false}
            onChange={() => {
              debouncedFetchItems();
            }}
          />
        </div>
        <div className="mt-8 flex justify-end gap-4 flex-wrap">
          <AppButton
            label="Store Items W/Qty"
            icon={ArrowDownToLine}
            iconClassName="w-5 h-5"
            onClick={handleStoreItemWQty}
            disabled={disableStoreItems}
          />
          <AppButton
            label="Create Order with Store Items"
            icon={PackagePlus}
            iconClassName="w-5 h-5"
            onClick={form.handleSubmit(onSubmit)}
            disabled={isCreateOrderItems}
            isLoading={saveLoading}
          />
        </div>
      </div>

      <div className="w-full grid grid-cols-4 2xl:grid-cols-5 gap-5 mt-2 2xl:mt-5">
        <div className="p-4 h-fit border rounded-md space-y-4 mt-2">
          <DatePicker
            form={form}
            name="dateOfUseFrom"
            label="Date of Use"
            placeholder="Select Date"
            enableInput
            onDateChange={(date) => {
              if (dayjs(date).isValid()) {
                onDateChange(date);
                checkFromDateBusyStatus(date);

                if (dayjs(date).isBefore(dayjs(currentDate), 'day')) {
                  setShowPastDateWarningModal(true);
                }
              }
            }}
            isRenderFirst={false}
            validation={TEXT_VALIDATION_RULE}
          />

          <DatePicker
            form={form}
            name="shipOrder.shipDate"
            label="Delivery Date"
            placeholder="Select Date"
            enableInput
            className="w-full"
            handleBlur={(date) => handleWarningOnBlur(date, 'shipDate')}
            validation={TEXT_VALIDATION_RULE}
          />
          <DatePicker
            form={form}
            name="shipOrder.returnArrivalDate"
            label="Pickup Date"
            placeholder="Select Date"
            enableInput
            handleBlur={(date) =>
              handleWarningOnBlur(date, 'returnArrivalDate')
            }
            validation={TEXT_VALIDATION_RULE}
          />

          <div>
            <AutoCompleteDropdown
              label="Customer"
              placeholder="Select Customer"
              name="billTo.customerId"
              form={form}
              onSelectChange={handleChangeCustomer}
              url={CUSTOMER_API_ROUTES.ALL}
              labelKey="full_name"
              valueKey="customer_id"
              sortBy="first_name"
              showItem
              labelComponent={(value, item) => (
                <div className={cn(!item?.isactive && 'text-red-500')}>
                  {value} {item?.isactive ? '' : '(Inactive)'}
                </div>
              )}
              validation={TEXT_VALIDATION_RULE}
            />
          </div>
          {itemInquiryInfo?.billTo?.customerId && (
            <InputField
              name="billTo.orderEmail"
              form={form}
              label="Order E-mail"
              placeholder="Enter Order E-Mail"
              validation={EMAIL_VALIDATION_RULEs}
              autoComplete="email"
            />
          )}
          <div>
            <AutoCompleteDropdown
              label="Location"
              placeholder="Select Location"
              name="shipTo.shipLocation"
              form={form}
              onSelectChange={handleChangeLocation}
              url={DELIVERY_LOCATION_API_ROUTES.ALL}
              labelKey="location"
              valueKey="location"
              showItem
              sortBy="location"
              validation={TEXT_VALIDATION_RULE}
              allowCustomEntry
            />
          </div>
          <NumberInputField
            form={form}
            label="Rent Days"
            placeholder="Rent Days"
            name="rentDays"
            maxLength={3}
            decimalScale={0}
          />
        </div>
        <div className="col-span-3 2xl:col-span-4 space-y-3 2xl:space-y-5">
          <ItemInquiryItems
            form={form}
            data={category || itemId || description ? fields : []}
            fieldName="items"
            rowSelection={rowSelection}
            setRowSelection={setRowSelection}
            onRowsSelected={onRowsSelectedChanges}
            enableRowSelection
            isLoading={isLoading}
          />
          <ItemInquiryItems
            form={form}
            fieldName="storeItems"
            data={storeItemFields}
            handleRemove={handleRemoveStoreItem}
          />
        </div>
      </div>
      <CustomDialog
        open={requireShippingInfo}
        onOpenChange={togglesRequireShippingInfo}
        description=""
        title="Require Shipping Info"
        className="min-w-[40%] 2xl:min-w-[35%]"
        contentClassName="p-6"
      >
        <ShippingInfo
          info={itemInquiryInfo?.shipTo}
          onChange={handleSetRequireShippingInfo}
          onCancel={togglesRequireShippingInfo}
        />
      </CustomDialog>
      <AppConfirmationModal
        title={'Warning'}
        open={showPastDateWarningModal}
        description={
          <div>
            The Date of Use just Entered is prior to today. Please be make sure
            {''}
            <span className="font-bold"> this is the correct date</span>
          </div>
        }
        handleSubmit={() => setShowPastDateWarningModal(false)}
        submitLabel="Ok"
      />
      <AppConfirmationModal
        title={'Warning'}
        open={isUseFromDateBusy}
        description={
          <div>
            The delivery date of{' '}
            <span className="font-bold">
              {formatDate(itemInquiryInfo?.dateOfUseFrom)}
            </span>{' '}
            is flagged as busy. Please check with a supervisor before booking
            for this day.
          </div>
        }
        handleSubmit={() => setIsUseFromDateBusy(false)}
        submitLabel="Ok"
      />
      <AppConfirmationModal
        title={'Warning'}
        description={<div>{warningModal.message}</div>}
        open={warningModal.isOpen}
        handleCancel={() => setWarningModal({ message: '', isOpen: false })}
        cancelLabel={'Ok'}
      />
    </div>
  );
};

export default ItemInquiry;
