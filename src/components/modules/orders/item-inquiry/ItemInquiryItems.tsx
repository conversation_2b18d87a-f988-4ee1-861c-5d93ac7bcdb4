import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import DataTable from '@/components/common/data-tables';
import ActionColumnMenu from '@/components/common/data-tables/ActionColumn';
import NumberInputField from '@/components/forms/number-input-field';
import { convertToFloat } from '@/lib/utils';
import { SortingStateType } from '@/types/common.types';
import { ItemInquiryItemsType } from '@/types/item-inquiry.types';
import {
  ColumnDef,
  OnChangeFn,
  RowSelectionState,
} from '@tanstack/react-table';
import { useCallback, useMemo, useState } from 'react';
import { FieldValues, Path, UseFormReturn } from 'react-hook-form';
interface ItemInquiryItemsTypes<T extends FieldValues> {
  form: UseFormReturn<T>;
  data: ItemInquiryItemsType[];
  fieldName: 'items' | 'storeItems';
  isLoading?: boolean;
  enableRowSelection?: boolean;
  handleRemove?: (index: number) => void;
  rowSelection?: RowSelectionState;
  setRowSelection?: OnChangeFn<RowSelectionState>;
  onRowsSelected?: (selectedRows: ItemInquiryItemsType[]) => void;
}
const ItemInquiryItems = <T extends FieldValues>({
  form,
  data,
  fieldName,
  isLoading,
  enableRowSelection,
  handleRemove,
  rowSelection,
  setRowSelection,
  onRowsSelected,
}: ItemInquiryItemsTypes<T>) => {
  const [sorting, setSorting] = useState<SortingStateType[]>([
    { id: 'itemId', desc: true },
  ]);
  const [openDelete, setOpenDelete] = useState<boolean>(false);
  const [selectedItem, setSelectedItem] = useState<{
    itemId: string;
    index: number | null;
  }>({ itemId: '', index: null });

  // toggle open delete
  const toggleDelete = useCallback((itemId?: string, index?: number) => {
    setOpenDelete((prev) => !prev);
    setSelectedItem({ itemId: itemId ?? '', index: index ?? null });
  }, []);

  // handle remove item
  const handleRemoveItem = useCallback(() => {
    selectedItem?.index !== null && handleRemove?.(selectedItem?.index);
    toggleDelete();
  }, [handleRemove, selectedItem?.index, toggleDelete]);

  const columns: ColumnDef<ItemInquiryItemsType>[] = useMemo(() => {
    return [
      {
        accessorKey: 'itemId',
        header: 'Item ID',
        size: 200,
        enableSorting: true,
        invertSorting: true,
      },
      {
        accessorKey: 'description',
        header: 'Description',
        enableSorting: true,
        size: 250,
        maxSize: 250,
        invertSorting: true,
      },
      {
        accessorKey: 'quantity',
        header: 'Quantity',
        maxSize: 150,
        cell: ({ row }) => (
          <NumberInputField
            name={`${fieldName}.${row.index}.quantity`}
            form={form}
            placeholder="Quantity"
            maxLength={5}
            decimalScale={0}
            className="h-8"
            pClassName="py-1"
          />
        ),
      },

      {
        accessorKey: 'unitPrice',
        header: 'Price',
        size: 200,
        maxSize: 200,
        cell: ({ row }) => (
          <NumberInputField
            name={`${fieldName}.${row.index}.unitPrice`}
            form={form}
            placeholder="Price"
            maxLength={14}
            fixedDecimalScale
            prefix="$"
            className="h-8"
            pClassName="py-1"
            thousandSeparator=","
          />
        ),
      },
      {
        accessorKey: 'total',
        header: 'Total',
        size: 200,
        cell: ({ row }) => {
          const quantity = Number(
            form.watch(`${fieldName}.${row.index}.quantity` as Path<T>)
          );
          const price = Number(
            form.watch(`${fieldName}.${row.index}.unitPrice` as Path<T>)
          );
          return convertToFloat({
            value: quantity * price,
            prefix: '$',
          });
        },
      },
      ...(enableRowSelection
        ? []
        : [
            {
              id: 'action',
              size: 80,
              header: 'Actions',
              cell: ({ row }: any) => (
                <ActionColumnMenu
                  onDelete={() =>
                    toggleDelete(row?.original?.itemId, row.index)
                  }
                  contentClassName="w-fit"
                />
              ),
            },
          ]),
    ];
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [enableRowSelection, fieldName, form, toggleDelete, data]);

  return (
    <>
      <DataTable
        columns={columns}
        data={data ?? []}
        totalItems={data?.length}
        isLoading={isLoading}
        sorting={sorting}
        setSorting={setSorting}
        customToolBar
        enablePagination={false}
        manualSorting={false}
        tableClassName="max-h-[250px] 2xl:max-h-[350px] overflow-auto"
        enableRowSelection={enableRowSelection}
        enableMultiRowSelection={enableRowSelection}
        disableSelectAllCheckbox={enableRowSelection}
        rowSelection={rowSelection}
        onRowSelectionChange={setRowSelection}
        onRowsSelected={onRowsSelected}
        bindingKey="itemInquiryId"
      />
      <AppConfirmationModal
        description={
          <div>
            Are you sure you want to delete
            <strong> {selectedItem?.itemId ?? ''}</strong> ?
          </div>
        }
        open={openDelete}
        handleCancel={() => toggleDelete()}
        handleSubmit={handleRemoveItem}
      />
    </>
  );
};
export default ItemInquiryItems;
