import AppButton from '@/components/common/app-button';
import { setDialog } from '@/redux/features/customers/noteSlice';
import { PlusIcon } from 'lucide-react';
import { useDispatch } from 'react-redux';
import QuickNotes from '../../customers/new-customer/files-notes-tabs/quick-notes';
import QuickNotesDialog from '../../customers/new-customer/files-notes-tabs/quick-notes/QuickNotesDialog';

const Notes = () => {
  const dispatch = useDispatch();

  const handleAddNew = () => {
    // Open the appropriate dialog based on the active tab
    dispatch(setDialog({ name: 'quick-notes', state: true, id: null }));
  };
  return (
    <div>
      <div className="flex justify-end mb-1">
        <AppButton
          variant="neutral"
          onClick={handleAddNew}
          label="Add New"
          icon={PlusIcon}
          iconClassName="w-5 h-5"
        />
      </div>

      <QuickNotes />
      <QuickNotesDialog />
    </div>
  );
};

export default Notes;
