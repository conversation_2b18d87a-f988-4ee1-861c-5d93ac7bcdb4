import AppButton from '@/components/common/app-button';
import AppSpinner from '@/components/common/app-spinner';
import InputField from '@/components/forms/input-field';
import PhoneInputWidget from '@/components/forms/phone-input-mask';
import SelectDropDown from '@/components/forms/select-dropdown';
import ZipCodeInput from '@/components/forms/zipcode-input';
import { UseToast } from '@/components/ui/toast/ToastContainer';
import {
  CUSTOMER_TYPE_API_ROUTES,
  STORE_OPTIONS_API_ROUTES,
} from '@/constants/api-constants';
import { EMAIL_VALIDATION_RULEOptional } from '@/constants/auth-constants';
import { statusList } from '@/constants/common-constants';
import { TEXT_VALIDATION_RULE } from '@/constants/validation-constants';
import useOptionList from '@/hooks/useOptionList';
import {
  booleanToString,
  generateLabelValue<PERSON>airs,
  getQueryParam,
  isValueMatching,
} from '@/lib/utils';
import {
  useGetCountryListQuery,
  useGetStateByCountryQuery,
} from '@/redux/features/country/country.api';
import {
  usePaymentTermsQuery,
  useSalesPersonQuery,
} from '@/redux/features/customers/choices.api';
import {
  useCreateCustomerMutation,
  useGetCustomerByIdQuery,
  useUpdateCustomerMutation,
} from '@/redux/features/customers/customer.api';
import { useGetSalesTaxCodeQuery } from '@/redux/features/sales-tax-code/sales-tax-code.api';
import { RootState } from '@/redux/store';
import { CustomerDetailTypes } from '@/types/customer.types';
import { BadgeCheck } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { useSelector } from 'react-redux';

interface NewCustomerProps {
  handleOk?: (data: CustomerDetailTypes) => void;
  handleCancel?: () => void;
  customerId?: string;
  firstName?: string;
  lastName?: string;
}

const Information = ({
  handleOk,
  handleCancel,
  firstName,
  lastName,
}: NewCustomerProps) => {
  const customerId = getQueryParam('customerId');
  const toast = UseToast();
  const profile = useSelector((state: RootState) => state.loginProfile.profile);
  const [selectedCountry, setSelectedCountry] = useState(1);
  const [stateList, setStateList] = useState<
    Array<{ label: string; value: string }>
  >([]);

  // get the customer detaails
  const { data: customerData, isFetching } = useGetCustomerByIdQuery(
    customerId ?? '',
    {
      skip: !customerId,
    }
  );
  // update customer details
  const [updateCustomer, { isLoading: updateLoading }] =
    useUpdateCustomerMutation();

  const defaultValues = useMemo(() => {
    const dataValue = customerData?.data || {};
    return {
      ...dataValue,
      first_name: dataValue?.first_name ?? firstName ?? '',
      last_name: dataValue?.last_name ?? lastName ?? '',
      country: dataValue?.country ?? 'USA',
      customer_id: dataValue?.customer_id ?? 0,
      customerType: dataValue?.custtype_id || '',
      tel1: dataValue?.tel1 ?? '',
      isactive: booleanToString(dataValue?.isactive ?? 'true'),
      defstorelocation: dataValue?.defstorelocation ?? '',
      defstorelocationno:
        (dataValue?.defstorelocationno ?? profile.defaultLocationId)
          ? Number(profile.defaultLocationId)
          : '',
      telfax: dataValue?.telfax ?? '',
    };
  }, [customerData?.data, firstName, lastName, profile.defaultLocationId]);

  const form = useForm<CustomerDetailTypes>({
    defaultValues,
    mode: 'onChange',
  });

  useEffect(() => {
    if (defaultValues) {
      form.reset(defaultValues);
    }
  }, [defaultValues, form]);

  const [createCustomer, { isLoading: createCustomerLoader }] =
    useCreateCustomerMutation();

  const { data, isLoading } = usePaymentTermsQuery();
  const { data: salesTaxCodeData, isLoading: salesTaxCodLoading } =
    useGetSalesTaxCodeQuery();
  const { data: salespersonData, isLoading: salespersonLoading } =
    useSalesPersonQuery();

  const { data: countryData = [] } = useGetCountryListQuery();
  const {
    data: statesData = [],
    isFetching: stateIsLoading,
    refetch,
  } = useGetStateByCountryQuery({ countryId: selectedCountry });

  // Memoized country list
  const countryList = useMemo(
    () =>
      generateLabelValuePairs({
        data: countryData,
        labelKey: 'name',
        valueKey: 'name',
      }),
    [countryData]
  );

  const handleCountryChange = (value: string) => {
    form.setValue('country', value);
    const newCountry = countryData.find(
      (element: { name: string }) => element.name === value
    );
    setSelectedCountry(newCountry?.country_id ?? 1); // Set selected country
    form.setValue('state', ''); // Reset state when country changes
    form.setValue('zipcode', '');
    form.clearErrors('zipcode');
  };

  useEffect(() => {
    // Refetch states whenever selectedCountry changes
    if (selectedCountry) {
      refetch();
    }
  }, [selectedCountry, refetch]); // Dependency on selectedCountry and refetch

  // Ensure states data is used to generate state list
  useEffect(() => {
    if (statesData?.length) {
      const newStateList = generateLabelValuePairs({
        data: statesData,
        labelKey: 'code',
        valueKey: 'code',
      });
      setStateList(newStateList);
    }
  }, [statesData]); // Only re-run when statesData changes

  // Determine if current country is USA for validation
  const isUSA = form.watch('country') === 'USA';

  const { options: customerTypeList, optionLoading: customerTypesLoadingList } =
    useOptionList({
      url: CUSTOMER_TYPE_API_ROUTES.ALL,
      valueKey: 'custtype_id',
      labelKey: 'description',
      sortBy: 'code',
    });

  const { options: storeLocationList, optionLoading } = useOptionList({
    url: STORE_OPTIONS_API_ROUTES.ALL,
    labelKey: 'locationName',
    valueKey: 'id',
    sortBy: 'locationName',
  });

  const salesPersonList = generateLabelValuePairs({
    data: salespersonData?.data,
    labelKey: 'name',
    valueKey: 'id',
  });

  const salesTaxCodeList = generateLabelValuePairs({
    data: salesTaxCodeData?.data,
    labelKey: 'salestaxcode',
    valueKey: 'salestaxcode_id',
  });

  const paymentTermList = data?.data?.map((item: any) => {
    const key = Object.keys(item)[0];
    const value = item[key];
    return {
      label: key,
      value: value,
    };
  });

  const { handleSubmit } = form;

  const onSubmit: SubmitHandler<CustomerDetailTypes> = async (data) => {
    try {
      const payload = {
        ...data,
        isactive: isValueMatching({ value: data?.isactive?.toString() }),
      };

      if (customerId) {
        await updateCustomer({
          customerId: Number(customerId),
          customerData: payload as any,
        })
          .unwrap()
          .then((response) => {
            toast.success(response?.message);
            const customerData = response?.data;
            handleOk?.(customerData);
          });
      } else {
        await createCustomer(payload)
          .unwrap()
          .then((response) => {
            toast.success(response?.message);
            const customerData = response?.data;
            handleOk?.(customerData);
          });
      }
    } catch (error) {
      // toast.error('Failed to create customer');
    }
  };

  return (
    <div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 border rounded-lg p-4">
        <InputField
          form={form}
          name="first_name"
          label="First Name"
          placeholder="Enter First Name"
          validation={TEXT_VALIDATION_RULE}
          disabled={!!customerId}
        />
        <InputField
          form={form}
          name="last_name"
          label="Last Name"
          placeholder="Enter Last Name"
          validation={TEXT_VALIDATION_RULE}
          disabled={!!customerId}
        />
        <SelectDropDown
          form={form}
          name="custtype_id"
          label="Customer Type"
          placeholder="Select Customer Type"
          optionsList={customerTypeList ?? []}
          allowClear={false}
          isLoading={customerTypesLoadingList}
          validation={TEXT_VALIDATION_RULE}
        />
        <SelectDropDown
          form={form}
          name="defstorelocationno"
          label="Store Location"
          placeholder="Select Store Location"
          allowClear={false}
          optionsList={storeLocationList}
          isLoading={optionLoading}
          validation={TEXT_VALIDATION_RULE}
        />
        <SelectDropDown
          form={form}
          name="isactive"
          label="Status"
          placeholder="Select Status"
          optionsList={statusList}
          validation={TEXT_VALIDATION_RULE}
        />
        <InputField
          form={form}
          name="address1"
          label="Address Line 1"
          placeholder="Enter Address"
        />
        <InputField
          form={form}
          name="address2"
          label="Address Line 2"
          placeholder="Enter Address"
        />
        <InputField
          form={form}
          name="contact"
          label="Contact"
          placeholder="Enter Contact"
        />
        <InputField
          form={form}
          name="emailaddress"
          label="E-mail"
          placeholder="Enter Email"
          validation={EMAIL_VALIDATION_RULEOptional}
        />
        <PhoneInputWidget form={form} name="tel1" label="Phone" />
        <PhoneInputWidget form={form} name="tel2" label="Phone 2" />
        <InputField
          form={form}
          name="city"
          label="City"
          placeholder="Enter City"
        />
        <SelectDropDown
          name="state"
          form={form}
          placeholder="Select State"
          label="State"
          allowClear={true}
          optionsList={stateList}
          isLoading={stateIsLoading}
        />
        <ZipCodeInput
          name="zipcode"
          isUSA={isUSA}
          form={form}
          label="Zip Code"
        />
        <SelectDropDown
          form={form}
          name="country"
          label="Country"
          placeholder="Select Country"
          allowClear={false}
          optionsList={countryList}
          onChange={handleCountryChange}
        />
        <div className="col-span-2 border-t border-gray-300 my-2"></div>
        <SelectDropDown
          form={form}
          name="salesPersonId"
          label="Salesperson"
          placeholder="Select Sales Person"
          allowClear={true}
          optionsList={salesPersonList}
          isLoading={salespersonLoading}
        />
        <SelectDropDown
          form={form}
          name="salestaxcode_id"
          label="Default Sales Tax Code"
          placeholder="Select Default Sales Tax Code"
          allowClear={true}
          optionsList={salesTaxCodeList}
          isLoading={salesTaxCodLoading}
        />
        <InputField
          form={form}
          name="exemptno"
          label="Exempt #"
          placeholder="Enter Exempt"
          maxLength={20}
        />
        <InputField
          form={form}
          name="licenseno"
          label="License #"
          placeholder="Enter License"
          maxLength={25}
        />
        <SelectDropDown
          form={form}
          name="paymentterm_id"
          label="Payment Terms"
          placeholder="Select Payment Terms"
          allowClear={true}
          optionsList={paymentTermList ?? []}
          isLoading={isLoading}
        />
      </div>
      <div className="flex justify-end gap-4 mt-3 sticky bottom-0 py-2 bg-white px-4">
        <AppButton
          type="button"
          onClick={handleSubmit(onSubmit)}
          label={customerId ? 'Add Customer to Order' : 'Submit'}
          className="min-w-28"
          icon={customerId ? BadgeCheck : undefined}
          isLoading={createCustomerLoader || updateLoading}
        />
        <AppButton
          type="button"
          onClick={handleCancel}
          label="Cancel"
          variant="neutral"
          className="w-28"
        />
      </div>
      <AppSpinner overlay isLoading={isFetching} />
    </div>
  );
};

export default Information;
