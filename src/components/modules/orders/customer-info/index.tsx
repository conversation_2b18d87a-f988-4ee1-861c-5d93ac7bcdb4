import AppTabsVertical from '@/components/common/app-tabs-vertical';
import CustomDialog from '@/components/common/dialog';
import { cn } from '@/lib/utils';
import { CustomerDetailTypes } from '@/types/customer.types';
import { useCallback, useState } from 'react';
import AdditionalContacts from '../../customers/new-customer/other-info/additional-contacts';
import Information from './Information';
import Notes from './notes';

interface CustomerInfoTabTyps {
  open: boolean;
  onOpenChange: () => void;
  showTabMenu?: boolean;
  handleOk?: (id: string) => void;
  firstName?: string;
  lastName?: string;
}
const CustomerInfoTabs = ({
  open,
  onOpenChange,
  showTabMenu,
  handleOk,
  firstName,
  lastName,
}: CustomerInfoTabTyps) => {
  const [activeTab, setActiveTab] = useState<string>('information');

  // handle on change tab
  const handleTabChange = useCallback((value: string) => {
    setActiveTab(value);
  }, []);

  const handleAddUpdateCustomer = useCallback(
    (customer: CustomerDetailTypes) => {
      handleOk?.(customer?.customer_id?.toString());
      onOpenChange();
    },
    [handleOk, onOpenChange]
  );

  const itemTabList = [
    {
      value: 'information',
      label: 'Customer Information',
      content: (
        <Information
          firstName={firstName}
          lastName={lastName}
          handleCancel={onOpenChange}
          handleOk={handleAddUpdateCustomer}
        />
      ),
    },
    {
      value: 'note',
      label: 'Notes',
      content: <Notes />,
    },
    {
      value: 'contact-info',
      label: 'Contact Info',
      content: <AdditionalContacts heading=" " tableClassName="h-[380px]" />,
    },
  ];

  return (
    <CustomDialog
      onOpenChange={onOpenChange}
      description=""
      open={open}
      className={cn(
        'max-w-[90%] md:w-[80%] 2xl:w-[60%]',
        !showTabMenu && 'max-w-[60%]'
      )}
      title={showTabMenu ? 'Customer Info' : 'New Customer'}
    >
      <AppTabsVertical
        tabs={itemTabList}
        activeTab={activeTab}
        onTabChange={handleTabChange}
        showTabMenu={showTabMenu}
        className="px-4 pb-3"
        contentClassName={cn('h-[500px] overflow-y-auto p-0 border-none')}
      />
    </CustomDialog>
  );
};

export default CustomerInfoTabs;
