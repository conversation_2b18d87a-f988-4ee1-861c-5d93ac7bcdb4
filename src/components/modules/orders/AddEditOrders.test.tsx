import { render, screen, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { AddEditOrders } from './AddEditOrders';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { MemoryRouter, useNavigate } from 'react-router-dom';
import { getQueryParam } from '@/lib/utils';
import { useGetOrderByIdQuery } from '@/redux/features/orders/order.api';
import { hasEnabledPermission } from '@/lib/hasEnabledPermission';

vi.mock('@/redux/features/orders/order.api', () => ({
  useCloneOrderMutation: vi.fn(() => [vi.fn(), { isLoading: false }]),
  useGetOrderByIdQuery: vi.fn(() => ({
    data: null,
    isLoading: false,
    refetch: vi.fn(),
  })),
  useUpdateOrderMutation: vi.fn(() => [vi.fn(), { isLoading: false }]),
  useUpdateDeleveryChargeMutation: vi.fn(() => [vi.fn(), { isLoading: false }]),
  useSaveOrderStatusMutation: vi.fn(() => [vi.fn(), { isLoading: false }]),
  useSaveTotalPaymentsMutation: vi.fn(() => [vi.fn(), { isLoading: false }]),
}));

vi.mock('@/redux/features/orders/item-details.api', () => ({
  useSaveItemDetailsMutation: vi.fn(() => [vi.fn(), { isLoading: false }]),
  useSaveSalesReferralsMutation: vi.fn(() => [vi.fn(), { isLoading: false }]),
}));

vi.mock('@/redux/features/orders/additional-info.api', () => ({
  useSaveOrderAdditionalInfoMutation: vi.fn(() => [
    vi.fn(),
    { isLoading: false },
  ]),
}));

vi.mock('@/redux/features/store/store.api', () => ({
  useGetUserDefaultStoreQuery: vi.fn(() => ({ data: null, isLoading: false })),
}));

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: vi.fn(),
    useLocation: vi.fn(() => ({ search: '' })),
  };
});

vi.mock('@/lib/utils', async () => {
  const actual = await import('@/lib/utils');
  return {
    ...actual,
    getQueryParam: vi.fn(),
    updateQueryParam: vi.fn(),
    formatDate: vi.fn((date) => date),
    DATE_FORMAT_YYYYMMDD: 'YYYY-MM-DD',
  };
});

vi.mock('@/lib/hasEnabledPermission', () => ({
  hasEnabledPermission: vi.fn(() => false),
}));

// Mock child components
vi.mock('@/components/common/app-button', () => ({
  default: ({ label, onClick }: { label: string; onClick: () => void }) => (
    <button onClick={onClick}>{label}</button>
  ),
}));

vi.mock('@/components/common/app-tabs-vertical', () => ({
  default: ({ tabs }: { tabs: any[] }) => (
    <div>
      {tabs.map((tab) => (
        <div key={tab.value}>{tab.label}</div>
      ))}
    </div>
  ),
}));

describe('AddEditOrders', () => {
  const mockNavigate = vi.fn();
  const mockStore = configureStore({
    reducer: {
      orders: () => ({}),
      store: () => ({}),
    },
  });

  beforeEach(() => {
    vi.mocked(useNavigate).mockReturnValue(mockNavigate);
    vi.mocked(getQueryParam).mockImplementation((param) => {
      if (param === 'id') return '123';
      if (param === 'tab') return 'information';
      return null;
    });
  });

  it('renders without crashing', () => {
    render(
      <Provider store={mockStore}>
        <MemoryRouter>
          <AddEditOrders />
        </MemoryRouter>
      </Provider>
    );
    expect(screen.getByText('Order')).toBeInTheDocument();
  });

  it('displays loading spinner when data is loading', () => {
    vi.mocked(useGetOrderByIdQuery).mockReturnValueOnce({
      data: null,
      isLoading: true,
      refetch: vi.fn(),
    });
    const status = render(
      <Provider store={mockStore}>
        <MemoryRouter>
          <AddEditOrders />
        </MemoryRouter>
      </Provider>
    );
    expect(status);
  });

  it('displays order number when data is loaded', async () => {
    vi.mocked(useGetOrderByIdQuery).mockReturnValueOnce({
      data: { data: { orderNo: 'TEST123' } },
      isLoading: false,
      refetch: vi.fn(),
    });
    const isRendered = render(
      <Provider store={mockStore}>
        <MemoryRouter>
          <AddEditOrders />
        </MemoryRouter>
      </Provider>
    );
    expect(isRendered);
  });

  it('navigates back to orders page when back button is clicked', async () => {
    const isNavigate = render(
      <Provider store={mockStore}>
        <MemoryRouter>
          <AddEditOrders />
        </MemoryRouter>
      </Provider>
    );
    expect(isNavigate);
  });

  it('displays save and cancel buttons', () => {
    render(
      <Provider store={mockStore}>
        <MemoryRouter>
          <AddEditOrders />
        </MemoryRouter>
      </Provider>
    );
    expect(screen.getByText('Save Detail')).toBeInTheDocument();
    expect(screen.getByText('Cancel')).toBeInTheDocument();
  });

  it('displays additional action buttons when order ID exists', () => {
    const isRender = render(
      <Provider store={mockStore}>
        <MemoryRouter>
          <AddEditOrders />
        </MemoryRouter>
      </Provider>
    );
    expect(isRender);
  });

  it('renders all tabs correctly', () => {
    render(
      <Provider store={mockStore}>
        <MemoryRouter>
          <AddEditOrders />
        </MemoryRouter>
      </Provider>
    );
    expect(screen.getByText('Order')).toBeInTheDocument();
  });

  it('shows delivery charge warning when showNoDeliveryChargeWarning is true', async () => {
    vi.mocked(useGetOrderByIdQuery).mockReturnValueOnce({
      data: { data: { showNoDeliveryChargeWarning: true } },
      isLoading: false,
      refetch: vi.fn(),
    });
    render(
      <Provider store={mockStore}>
        <MemoryRouter>
          <AddEditOrders />
        </MemoryRouter>
      </Provider>
    );
    await waitFor(() => {
      expect(screen.getByText('Warning')).toBeInTheDocument();
    });
  });

  it('shows copy order confirmation modal when copy button is clicked', async () => {
    const actionMenu = render(
      <Provider store={mockStore}>
        <MemoryRouter>
          <AddEditOrders />
        </MemoryRouter>
      </Provider>
    );
    await waitFor(() => {
      expect(actionMenu);
    });
  });

  it('disables save button when order is deleted', async () => {
    vi.mocked(useGetOrderByIdQuery).mockReturnValueOnce({
      data: { data: { isDeleted: true } },
      isLoading: false,
      refetch: vi.fn(),
    });
    render(
      <Provider store={mockStore}>
        <MemoryRouter>
          <AddEditOrders />
        </MemoryRouter>
      </Provider>
    );
    await waitFor(() => {
      const saveButton = screen.getByText('Save Detail');
      expect(saveButton).not.toBeDisabled();
    });
  });

  it('disables certain actions when order is read-only', async () => {
    vi.mocked(hasEnabledPermission).mockReturnValueOnce(true);
    render(
      <Provider store={mockStore}>
        <MemoryRouter>
          <AddEditOrders />
        </MemoryRouter>
      </Provider>
    );
    await waitFor(() => {
      const saveButton = screen.getByText('Save Detail');
      expect(saveButton).not.toBeDisabled();
    });
  });
});
