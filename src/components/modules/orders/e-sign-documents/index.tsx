import CheveronLeft from '@/assets/icons/CheveronLeft';
import AppButton from '@/components/common/app-button';
import BreadcrumbDialogRenderer from '@/components/common/BreadcrumbDialogRenderer';
import DataTable from '@/components/common/data-tables';
import ActionColumnMenu from '@/components/common/data-tables/ActionColumn';
import IconButton from '@/components/common/icon-button';
import DatePicker from '@/components/forms/date-picker';
import RadioField from '@/components/forms/radio-field';
import { UseToast } from '@/components/ui/toast/ToastContainer';
import { ORDERS_API_ROUTES } from '@/constants/api-constants';
import { ROUTES } from '@/constants/routes-constants';
import { useDownloadFile } from '@/hooks/useDownloadFile';
import { formatDateWithTimezone } from '@/lib/formatDateWithTimezone';
import {
  DATE_FORMAT_YYYYMMDD,
  DATE_TIME_FORMAT,
  formatDate,
  getStorageValue,
} from '@/lib/utils';
import {
  useCancelESignLinkMutation,
  useGetESignDocumentsQuery,
  useLazyRefreshESignQuery,
  useSendRemainderMutation,
} from '@/redux/features/orders/order.api';
import { PaginationType, SortingStateType } from '@/types/common.types';
import { ESignDocumentsTypes } from '@/types/orders/e-sign-documents.types';
import { ColumnDef } from '@tanstack/react-table';
import dayjs from 'dayjs';
import { Award, Mail, MailX, RefreshCcw, X } from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import OpenFolder from './OpenFolder';

interface ESignDocumentsFormTypes {
  type: string;
  useFrom: string;
  toDate: string;
}

const ESignDocuments = () => {
  const navigation = useNavigate();
  const toast = UseToast();

  const [pagination, setPagination] = useState<PaginationType>({
    pageSize: 10,
    pageIndex: 0,
  });
  const [sorting, setSorting] = useState<SortingStateType[]>([
    { id: 'id', desc: true },
  ]);
  const [activeTab, setActiveTab] = useState<string>('');
  const [openModal, setOpenModal] = useState<boolean>(false);
  const [documentId, setDocumentId] = useState<number | null>(null);

  const handleOnChange = (tab?: string, id?: number) => {
    setActiveTab(tab ?? '');
    setDocumentId(id ?? null);
    setOpenModal((prev) => !prev);
  };
  const tz = getStorageValue('timeZoneOffset') || '';
  const currentDate = formatDateWithTimezone({ tz });

  const form = useForm<ESignDocumentsFormTypes>();
  const defaultValues = useMemo(() => {
    return {
      type: '1',
      useFrom: currentDate,
      toDate: dayjs(currentDate).add(1, 'day').format('YYYY-MM-DD'),
    };
  }, [currentDate]);

  const info = form.watch();
  const isAll = info.type === '0';

  useEffect(() => {
    if (defaultValues) {
      form.reset(defaultValues);
    }
  }, [defaultValues, form]);

  // navigate to back
  const navigateToOrder = useCallback(() => {
    navigation(ROUTES.ORDERS);
  }, [navigation]);

  //E-sign Documents API
  const [sendRemainder] = useSendRemainderMutation();
  const [cancelLink] = useCancelESignLinkMutation();
  const { downloadFile } = useDownloadFile();
  const [refreshESign, { isFetching: refreshLoader }] =
    useLazyRefreshESignQuery();

  const {
    data: ESignDocumentData,
    isFetching: isLoading,
    isSuccess,
    refetch,
  } = useGetESignDocumentsQuery(
    {
      pageNumber: pagination.pageIndex + 1,
      pageSize: pagination.pageSize,
      sortBy: sorting[0].id,
      sortAscending: sorting[0].desc,
      filters: [
        {
          field: 'type',
          value: info?.type,
          operator: '',
        },
        ...(isAll
          ? [
              {
                field: 'dateofusefrom',
                value: formatDate(info.useFrom, DATE_FORMAT_YYYYMMDD),
                operator: '',
              },
              {
                field: 'dateofusethru',
                value: formatDate(info.toDate, DATE_FORMAT_YYYYMMDD),
                operator: '',
              },
            ]
          : []),
      ]?.filter((item) => item?.value),
    },
    {
      skip: !info.type,
    }
  );

  const documentsList = (isSuccess && ESignDocumentData?.data) || [];

  // handle send reminder
  const handleSendReminder = useCallback(
    async (row: ESignDocumentsTypes) => {
      const { flag, linkId, orderId, status } = row;
      try {
        const response = sendRemainder({
          flag: flag ?? '',
          linkId,
          orderId,
          status,
        });
        toast.promise(response, {
          loading: 'Sending reminder...',
        });
      } catch (error) {}
    },
    [sendRemainder, toast]
  );

  // handle cancel the sended reminder
  const handleCancelLink = useCallback(
    async (row: ESignDocumentsTypes) => {
      const { flag, linkId, orderId, status } = row;
      try {
        const response = cancelLink({
          flag: flag ?? '',
          linkId,
          orderId,
          status,
        });
        toast.promise(response, {
          loading: 'Cancelling link...',
        });
        const data = await response;
        if (data?.data?.statusCode === 200) {
          refetch();
        }
      } catch (error) {}
    },
    [cancelLink, refetch, toast]
  );

  //handle Download Certificate
  const handleDownloadCertificate = useCallback(
    async (row: ESignDocumentsTypes) => {
      const { agreementId, linkId, orderId, status } = row;
      const response = downloadFile({
        url: ORDERS_API_ROUTES.E_SIGN_DOWNLOAD_CERTIFICATE,
        method: 'POST',
        body: { linkId, orderId, status, agreementId },
      });
      toast.promise(response, {
        loading: 'Downloading Certificate...',
        success: 'Certificate downloaded successfully.',
        error: 'Failed to download Certificate',
      });
    },
    [downloadFile, toast]
  );

  // Header section
  const Header = () => {
    return (
      <div className="flex gap-x-4 items-center justify-between sticky top-16 pt-3 pb-2 bg-white z-20 mb-4">
        <div className="flex items-center gap-3">
          <IconButton onClick={navigateToOrder}>
            <CheveronLeft />
          </IconButton>
          <h1 className="text-2xl font-semibold">E-sign Documents</h1>
        </div>
        <AppButton
          label="Cancel"
          icon={X}
          onClick={navigateToOrder}
          iconClassName="w-4 h-4"
          variant="neutral"
        />
      </div>
    );
  };

  const enableSorting = documentsList?.length > 0;
  // columns
  const columns: ColumnDef<ESignDocumentsTypes>[] = useMemo(() => {
    return [
      {
        accessorKey: 'orderId',
        header: 'Order #',
        enableSorting: enableSorting,
      },
      {
        accessorKey: 'revNo',
        header: 'Revision #',
        enableSorting: enableSorting,
      },
      {
        accessorKey: 'customerFullName',
        header: 'Customer',
        enableSorting: enableSorting,
        size: 200,
        maxSize: 200,
      },
      {
        accessorKey: 'description',
        header: 'File',
        enableSorting: enableSorting,
        size: 150,
        maxSize: 200,
      },
      {
        accessorKey: 'lastUpdate',
        header: 'Last Status Change',
        size: 210,
        enableSorting: enableSorting,
        cell: ({ row }) =>
          formatDate(row.original?.lastUpdate, DATE_TIME_FORMAT),
      },
      {
        accessorKey: 'status',
        header: 'Status',
        enableSorting: enableSorting,
        cell: ({ row }) => row.original?.statusText,
      },
      {
        id: 'action',
        size: 110,
        header: 'Actions',
        cell: ({ row }) => {
          const status = row?.original?.status;
          const isSendReminder = ['VIEWED', 'SENT'].includes(status);
          const isCancelLink = [
            'VIEWED',
            'SENT',
            'NON_SIGNED',
            'DECLINED',
          ].includes(status);
          const isDownloadCertificate = ['SIGNED'].includes(status);
          return (
            <ActionColumnMenu
              dropdownMenuList={[
                {
                  label: 'Send Reminder',
                  icon: <Mail className="w-5 h-5" />,
                  onClick: () => handleSendReminder(row?.original),
                  disabled: !isSendReminder,
                },
                {
                  label: 'Cancel Link',
                  icon: <MailX className="w-5 h-5" />,
                  onClick: () => handleCancelLink(row?.original),
                  disabled: !isCancelLink,
                },
                {
                  label: 'Download Certificate',
                  icon: <Award className="w-5 h-5" />,
                  onClick: () => handleDownloadCertificate(row?.original),
                  disabled: !isDownloadCertificate,
                },
              ]}
              contentClassName="p-3 w-full"
            />
          );
        },
      },
    ];
  }, [
    enableSorting,
    handleCancelLink,
    handleDownloadCertificate,
    handleSendReminder,
  ]);

  //Handles the date change for Date of Use From and Date of Use Thru.
  const handleOnDateChange = useCallback(
    (newDate: Date | string, fieldName: 'useFrom' | 'toDate') => {
      const updatedDate = dayjs(newDate);
      const currentDate = dayjs(form.watch(fieldName));
      if (
        (fieldName === 'toDate' && updatedDate?.isAfter(currentDate)) ||
        (fieldName === 'useFrom' && updatedDate?.isBefore(currentDate))
      ) {
        form.setValue(fieldName, updatedDate?.format('YYYY-MM-DD'));
      }
    },
    [form]
  );

  const handleRefreshDocuments = async () => {
    const response = await refreshESign();
    if (response?.data?.statusCode === 200) {
      refetch();
    }
  };

  return (
    <div className="px-6 space-y-4">
      <Header />
      <div className="p-4 border rounded-md flex item gap-6">
        <RadioField
          form={form}
          name="type"
          options={[
            { label: 'Outstanding', value: '1' },
            {
              label: 'All',
              value: '0',
            },
          ]}
          optionsPerRow={1}
          className="me-5"
        />

        <DatePicker
          name="useFrom"
          label="Date of Use From"
          form={form}
          enableInput
          disabled={!isAll}
          onDateChange={(date) => handleOnDateChange(date ?? '', 'toDate')}
        />
        <DatePicker
          name="toDate"
          label="Date of Use Thru"
          form={form}
          enableInput
          disabled={!isAll}
          onDateChange={(date) => handleOnDateChange(date ?? '', 'useFrom')}
        />
        <AppButton
          icon={RefreshCcw}
          label=""
          className="mt-8"
          tooltip="Refresh"
          iconClassName="w-5 h-5"
          onClick={handleRefreshDocuments}
          isLoading={refreshLoader}
          disabled={!documentsList?.length}
        />
      </div>

      <DataTable
        data={documentsList}
        columns={columns}
        totalItems={ESignDocumentData?.pagination?.totalCount}
        isLoading={isLoading || refreshLoader}
        tableClassName="max-h-[400px] 2xl:max-h-[500px] overflow-auto"
        enablePagination={true}
        sorting={sorting}
        setSorting={setSorting}
        pagination={pagination}
        setPagination={setPagination}
        manualSorting={false}
      />
      <BreadcrumbDialogRenderer
        activeTab={activeTab}
        isOpen={openModal}
        listItems={[
          {
            label: 'Open Folder',
            value: 'open-folder',
            content: <OpenFolder documentId={documentId} />,
          },
        ]}
        onOpenChange={() => handleOnChange()}
        setActiveTab={handleOnChange}
        className="max-w-[70%] 2xl:max-w-[60%] overflow-x-auto"
        contentClassName="h-[400px] 2xl:h-[450px] overflow-y-auto"
      />
    </div>
  );
};
export default ESignDocuments;
