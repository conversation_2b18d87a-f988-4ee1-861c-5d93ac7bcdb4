import TrailingIcon from '@/assets/icons/TrailingIcon';
import AppButton from '@/components/common/app-button';
import DataTable from '@/components/common/data-tables';
import { UseToast } from '@/components/ui/toast/ToastContainer';
import { FILES_API } from '@/constants/api-constants';
import { useDownloadFile } from '@/hooks/useDownloadFile';
import { formatDate } from '@/lib/utils';
import { SortingStateType } from '@/types/common.types';
import { ColumnDef } from '@tanstack/react-table';
import { Download } from 'lucide-react';
import { useCallback, useState } from 'react';

interface UserUploadType {
  id: number;
  fileName: string;
  createdAt: string;
  owner: string;
}

const OpenFolder = ({ documentId }: { documentId?: number | null }) => {
  const [sorting, setSorting] = useState<SortingStateType[]>([]);
  const { downloadFile, isLoading: isDownloading } = useDownloadFile();
  const toast = UseToast();

  const handleDownload = useCallback(
    (id: number) => {
      const response = downloadFile({
        url: FILES_API.DOWNLOAD(id ?? documentId),
      });
      toast.promise(response, {
        loading: 'Downloading file...',
        success: 'File downloaded successfully.',
        error: 'Failed to download file',
      });
    },
    [documentId, downloadFile, toast]
  );
  const data = [
    {
      id: 1,
      fileName: 'test.txt',
      createdAt: '01/01/2025',
      owner: 'Test1',
    },
    { id: 1, fileName: 'xyz.pdf', createdAt: '04/01/2025', owner: 'ADM' },
  ];
  // columns
  const columns: ColumnDef<UserUploadType>[] = [
    {
      accessorKey: 'icon',
      header: 'Icon',
      size: 40,
      cell: () => (
        <div className="flex justify-center w-fit">
          <TrailingIcon />
        </div>
      ),
    },
    {
      accessorKey: 'fileName',
      header: 'File Name',
      size: 150,
      maxSize: 150,
      enableSorting: true,
    },
    {
      accessorKey: 'createdAt',
      header: 'Date Created',
      cell: ({ row }) => formatDate(row?.original?.createdAt),
      enableSorting: true,
    },
    {
      accessorKey: 'owner',
      header: 'Owner',
      size: 90,
      enableSorting: true,
    },
    {
      id: 'action',
      size: 110,
      header: 'Actions',
      cell: ({ row }) => (
        <AppButton
          onClick={() => handleDownload(row?.original?.id)}
          variant="neutral"
          label="Download"
          icon={Download}
          disabled={isDownloading}
        />
      ),
    },
  ];

  return (
    <div>
      <DataTable
        data={data || []}
        columns={columns}
        totalItems={data?.length}
        // isLoading={}
        tableClassName="max-h-[350px] overflow-y-auto"
        enablePagination={false}
        manualSorting={false}
        sorting={sorting}
        setSorting={setSorting}
      />
    </div>
  );
};

export default OpenFolder;
