import AppButton from '@/components/common/app-button';
import BreadcrumbDialogRenderer from '@/components/common/BreadcrumbDialogRenderer';
import DataTable from '@/components/common/data-tables';
import NumberInputField from '@/components/forms/number-input-field';
import { getQueryParam, updateQueryParam } from '@/lib/utils';
import { useGetItemListQuery } from '@/redux/features/orders/item-details.api';
import { useReturnOrderMutation } from '@/redux/features/orders/order.api';
import { MissingItemsFormTyps, MissingItemsTyp } from '@/types/order.types';
import { ColumnDef } from '@tanstack/react-table';
import isEqual from 'lodash/isEqual';
import { BadgeCheck } from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import {
  SubmitHand<PERSON>,
  useFieldArray,
  useForm,
  UseFormReturn,
} from 'react-hook-form';

const ReturnQuantity = ({ open, onOpenChange }: any) => {
  const orderId = getQueryParam('id') as string;
  const [activeTab, setActiveTab] = useState('return-qty');

  // Fetch missing items only when orderId exists
  const { data: returnItemData, isLoading } = useGetItemListQuery(
    { orderId },
    { skip: !orderId, refetchOnMountOrArgChange: true }
  );

  const [createReturnOrder, { isLoading: returnOrderLoading }] =
    useReturnOrderMutation();

  // Memoized default values
  const defaultValues = useMemo(
    () => ({
      items:
        returnItemData?.data?.orderItemList.map(
          (item: MissingItemsFormTyps) => ({
            ...item,
            returnQty: item?.returnQty || '',
          })
        ) || [],
    }),
    [returnItemData?.data]
  );

  // Form setup
  const form: UseFormReturn<MissingItemsTyp> = useForm<MissingItemsTyp>({
    defaultValues,
    mode: 'onChange',
  });

  // Field Array for Form
  const { fields } = useFieldArray<MissingItemsTyp>({
    control: form.control,
    name: 'items',
  });

  // Reset form when defaultValues change
  useEffect(() => {
    form.reset(defaultValues);
  }, [defaultValues, form]);

  // Column Definitions (memoized)
  const columns: ColumnDef<MissingItemsFormTyps>[] = useMemo(
    () => [
      {
        accessorKey: 'itemIdString',
        header: 'Item ID',
        enableSorting: true,
        size: 150,
      },
      {
        accessorKey: 'serial',
        header: 'Serial #',
        enableSorting: true,
        size: 110,
      },
      { accessorKey: 'quantity', header: 'Quantity', size: 100 },
      {
        accessorKey: 'description',
        header: 'Description',
        size: 200,
        maxSize: 200,
      },
      {
        accessorKey: 'returnQuantity',
        header: 'Return Quantity',
        size: 100,
        cell: ({ row }) => {
          const quantity = form.watch(`items.${row.index}.quantity`);
          return (
            <NumberInputField
              name={`items.${row.index}.returnQty`}
              form={form}
              placeholder="Return Quantity"
              maxLength={5}
              maxValue={quantity}
              decimalScale={0}
              pClassName="p-[1px]"
            />
          );
        },
      },
    ],
    [form]
  );

  // Handle form submission
  const onSubmit: SubmitHandler<MissingItemsTyp> = useCallback(
    async (formData) => {
      try {
        const payload = {
          orderId,
          items: formData?.items?.map(({ id, returnQty }) => ({
            id,
            returnQty,
          })),
        };
        const { data } = await createReturnOrder(payload);
        if (data?.statusCode === 200) {
          const orderId = data.data?.id;
          updateQueryParam('information', 'tab');
          updateQueryParam(orderId);
          onOpenChange();
        }
      } catch (error) {
        // console.error('Error submitting form:', error);
      }
    },
    [createReturnOrder, onOpenChange, orderId]
  );

  // Check if form is modified
  const isFormModified = isEqual(form.watch('items'), defaultValues?.items);

  const dialogTabs = useMemo(
    () => [
      {
        label: 'Select Quantities',
        value: 'return-qty',
        content: (
          <div>
            <DataTable
              columns={columns}
              data={fields || []}
              totalItems={returnItemData?.data?.pagination?.totalCount}
              isLoading={isLoading}
              enablePagination={false}
              tableClassName="max-h-[300px] 2xl:max-h-[400px] overflow-y-auto"
            />
            <div className="flex items-center gap-4 absolute bottom-4 right-6">
              <AppButton
                label="Submit"
                onClick={form.handleSubmit(onSubmit)}
                icon={BadgeCheck}
                className="w-32"
                disabled={isFormModified}
                isLoading={returnOrderLoading}
              />
              <AppButton
                label="Cancel"
                variant="neutral"
                onClick={onOpenChange}
                className="w-32"
              />
            </div>
          </div>
        ),
      },
    ],
    [
      columns,
      fields,
      form,
      isFormModified,
      isLoading,
      onOpenChange,
      onSubmit,
      returnItemData?.data?.pagination?.totalCount,
      returnOrderLoading,
    ]
  );

  return (
    <BreadcrumbDialogRenderer
      listItems={dialogTabs}
      activeTab={activeTab}
      isOpen={open}
      onOpenChange={onOpenChange}
      setActiveTab={setActiveTab}
      className="min-w-[80%] md:min-w-[70%] 2xl:min-w-[55%]"
      contentClassName="h-[400px] 2xl:h-[500px]"
    />
  );
};

export default ReturnQuantity;
