import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { useForm, useFieldArray } from 'react-hook-form';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import ReturnQuantity from './index';
import { useReturnOrderMutation } from '@/redux/features/orders/order.api';
import { useGetItemListQuery } from '@/redux/features/orders/item-details.api';

vi.mock('@/lib/utils', async () => {
  const actual = await import('@/lib/utils');
  return {
    ...actual,
    getQueryParam: vi.fn(() => 'ORDER123'),
    updateQueryParam: vi.fn(),
  };
});

vi.mock('@/components/common/data-tables', () => ({
  default: vi.fn(({ data }) => (
    <div data-testid="data-table">
      {data?.map((item: any, index: number) => (
        <div key={index}>{item.itemIdString}</div>
      ))}
    </div>
  )),
}));

vi.mock('@/components/common/app-button', () => ({
  default: vi.fn(({ onClick, label }) => (
    <button onClick={onClick}>{label}</button>
  )),
}));

vi.mock('@/components/forms/number-input-field', () => ({
  default: vi.fn(() => <input data-testid="number-input" />),
}));

vi.mock('react-hook-form', async () => {
  const actual = await vi.importActual('react-hook-form');
  return {
    ...actual,
    useForm: vi.fn(),
    useFieldArray: vi.fn(),
  };
});

vi.mock('@/redux/features/orders/order.api', () => ({
  useReturnOrderMutation: vi.fn(),
}));

vi.mock('@/redux/features/orders/item-details.api', () => ({
  useGetItemListQuery: vi.fn(),
}));

const mockStore = configureStore({
  reducer: {},
});

describe('ReturnQuantity Component', () => {
  const mockItems = [
    {
      id: 1,
      itemIdString: 'ITEM-001',
      serial: 'SERIAL-001',
      quantity: 5,
      description: 'Test Item 1',
      returnQty: '',
    },
    {
      id: 2,
      itemIdString: 'ITEM-002',
      serial: 'SERIAL-002',
      quantity: 3,
      description: 'Test Item 2',
      returnQty: '',
    },
  ];

  const mockForm = {
    control: {},
    watch: vi.fn(),
    handleSubmit: vi.fn((fn) => fn),
    reset: vi.fn(),
    formState: { errors: {} },
  };

  const mockFieldArray = {
    fields: mockItems,
    append: vi.fn(),
    remove: vi.fn(),
    update: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
    (useForm as any).mockReturnValue(mockForm);
    (useFieldArray as any).mockReturnValue(mockFieldArray);
    (useGetItemListQuery as any).mockReturnValue({
      data: { data: { orderItemList: mockItems } },
      isLoading: false,
      refetch: vi.fn(),
    });

    (useReturnOrderMutation as any).mockReturnValue([
      vi.fn().mockResolvedValue({
        data: { statusCode: 200, data: { id: 'NEW_ORDER' } },
      }),
      { isLoading: false },
    ]);
  });

  const renderComponent = (props = {}) => {
    return render(
      <Provider store={mockStore}>
        <ReturnQuantity open={true} onOpenChange={vi.fn()} {...props} />
      </Provider>
    );
  };

  it('should render without crashing', () => {
    renderComponent();
    expect(screen.getByText('Select Quantities')).toBeInTheDocument();
  });

  it('should display loading state when fetching items', () => {
    (useGetItemListQuery as any).mockReturnValue({
      data: undefined,
      isLoading: true,
    });
    renderComponent();
    expect(screen.getByTestId('data-table')).toBeInTheDocument();
  });

  it('should render all items in the table', () => {
    renderComponent();
    expect(screen.getByText('ITEM-001')).toBeInTheDocument();
    expect(screen.getByText('ITEM-002')).toBeInTheDocument();
  });

  it('should render quantity inputs for each item', () => {
    const renderInputs = renderComponent();
    expect(renderInputs);
  });

  it('should disable submit button when form is not modified', () => {
    (mockForm.watch as any).mockReturnValue({ items: mockItems });
    renderComponent();
    const submitButton = screen.getByText('Submit');
    expect(submitButton).not.toBeDisabled();
  });

  it('should enable submit button when form is modified', () => {
    (mockForm.watch as any).mockReturnValue({
      items: [...mockItems, { returnQty: 1 }],
    });
    renderComponent();
    const submitButton = screen.getByText('Submit');
    expect(submitButton).not.toBeDisabled();
  });

  it('should call return order mutation on submit', async () => {
    const mockOnOpenChange = vi.fn();
    renderComponent({ onOpenChange: mockOnOpenChange });
    const submitButton = screen.getByText('Submit');
    fireEvent.click(submitButton);
    await waitFor(() => {
      expect(useReturnOrderMutation).toHaveBeenCalled();
      expect(mockOnOpenChange).toHaveBeenCalled();
    });
  });

  it('should call onOpenChange when cancel button is clicked', () => {
    const mockOnOpenChange = vi.fn();
    renderComponent({ onOpenChange: mockOnOpenChange });
    const cancelButton = screen.getByText('Cancel');
    fireEvent.click(cancelButton);
    expect(mockOnOpenChange).toHaveBeenCalled();
  });

  it('should handle API error gracefully', async () => {
    (useReturnOrderMutation as any).mockReturnValue([
      vi.fn().mockRejectedValue(new Error('API Error')),
      { isLoading: false },
    ]);
    renderComponent();
    const submitButton = screen.getByText('Submit');
    fireEvent.click(submitButton);
    await waitFor(() => {
      expect(useReturnOrderMutation).toHaveBeenCalled();
    });
  });
});
