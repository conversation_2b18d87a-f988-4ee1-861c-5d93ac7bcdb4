import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { setupListeners } from '@reduxjs/toolkit/query';
import OverBookedItems from './OverbookedItems';
import { useFormContext } from 'react-hook-form';
import {
  useGetOverbookedItemInfoQuery,
  useGetStoreLocationsQuery,
} from '@/redux/features/orders/order.api';
import { useGetEnumsListQuery } from '@/redux/features/enums-api/enums-api';
import dayjs from 'dayjs';
import { storeApi } from '@/redux/features/store/store.api';

const mockStore = configureStore({
  reducer: {
    [storeApi.reducerPath]: storeApi.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(storeApi.middleware),
});

setupListeners(mockStore.dispatch);

vi.mock('react-hook-form', () => ({
  useFormContext: vi.fn(),
}));

vi.mock('@/redux/features/orders/order.api', () => ({
  useGetOverbookedItemInfoQuery: vi.fn(),
  useGetStoreLocationsQuery: vi.fn(),
}));

vi.mock('@/redux/features/enums-api/enums-api', () => ({
  useGetEnumsListQuery: vi.fn(),
}));

vi.mock('@/lib/utils', () => ({
  formatDate: vi.fn((date) => date || 'formatted-date'),
  generateLabelValuePairs: vi.fn(() => []),
  convertToFloat: vi.fn(() => '$10.00'),
  DATE_FORMAT_YYYYMMDD: 'YYYY-MM-DD',
}));

vi.mock('dayjs', () => ({
  __esModule: true,
  default: vi.fn(() => ({
    isAfter: vi.fn(() => true),
    isBefore: vi.fn(() => true),
    format: vi.fn(() => '2023-01-01'),
  })),
}));

vi.mock('@/components/common/app-button', () => ({
  default: vi.fn(({ onClick, label }) => (
    <button onClick={onClick}>{label}</button>
  )),
}));

vi.mock('@/components/common/data-tables', () => ({
  default: vi.fn(() => <div data-testid="data-table" />),
}));

vi.mock('@/components/forms/auto-complete-dropdown', () => ({
  default: vi.fn(() => <div data-testid="item-id-dropdown" />),
}));

vi.mock('@/components/forms/select', () => ({
  default: vi.fn(() => <div data-testid="select-widget" />),
}));

vi.mock('@/components/forms/input-field', () => ({
  default: vi.fn(() => <input data-testid="input-field" />),
}));

vi.mock('@/components/forms/date-picker', () => ({
  default: vi.fn(() => <div data-testid="date-picker" />),
}));

describe('OverBookedItems Component', () => {
  const mockSetActiveTab = vi.fn();
  const mockSerialList = [
    { label: 'SERIAL-001', value: '1' },
    { label: 'SERIAL-002', value: '2' },
  ];
  const mockFormValues = {
    itemId: { value: '123', label: 'ITEM-001' },
    serialNo: 'SERIAL-001',
    storeLocationId: '1',
    deliveryDate: '2023-01-10',
    pickupDate: '2023-01-15',
    displayType: 'RECENT',
    orderType: 'ORDERS',
    description: 'Test Item',
    cleanupDays: '1',
    unitPrice: '$10.00',
    owned: '5',
    rented: '3',
    subRented: '2',
    ordered: '1',
    available: '4',
  };

  const mockOverbookedData = {
    data: {
      description: 'Test Item',
      cleanupDays: 1,
      unitPrice: 10,
      owned: 5,
      rented: 3,
      subRented: 2,
      ordered: 1,
      available: 4,
      overbookedItemOrders: [
        {
          orderNo: 'ORDER-001',
          location: 'Warehouse A',
          customer: 'Customer A',
          orderType: 'Rental',
          quantity: 2,
          dateOfUseFrom: '2023-01-10',
          deliveryDate: '2023-01-09',
          pickupDate: '2023-01-16',
        },
      ],
    },
  };

  beforeEach(() => {
    vi.clearAllMocks();
    (useFormContext as any).mockReturnValue({
      watch: vi.fn(() => mockFormValues),
      setValue: vi.fn(),
      formState: { errors: {} },
      clearErrors: vi.fn(),
      setError: vi.fn(),
    });
    (useGetOverbookedItemInfoQuery as any).mockReturnValue({
      data: mockOverbookedData,
      isFetching: false,
    });
    (useGetStoreLocationsQuery as any).mockReturnValue({
      data: { data: [{ id: 1, location: 'Warehouse A' }] },
      isLoading: false,
    });
    (useGetEnumsListQuery as any).mockImplementation(({ name }: any) => {
      if (name === 'OverbookedItemDisplayType') {
        return {
          data: { data: [{ value: 'RECENT', label: 'Recent' }] },
          isLoading: false,
        };
      }
      if (name === 'OverbookedItemOrderType') {
        return {
          data: { data: [{ value: 'ORDERS', label: 'Orders' }] },
          isLoading: false,
        };
      }
      return { data: null, isLoading: false };
    });
  });

  const renderComponent = () => {
    return render(
      <Provider store={mockStore}>
        <OverBookedItems
          serialList={mockSerialList}
          setActiveTab={mockSetActiveTab}
          serialLoading={false}
        />
      </Provider>
    );
  };

  it('should render without crashing', () => {
    renderComponent();
    expect(screen.getByText('Availability Calculation')).toBeInTheDocument();
  });

  it('should render all form controls', () => {
    renderComponent();
    expect(screen.getByTestId('item-id-dropdown')).toBeInTheDocument();
    expect(screen.getAllByTestId('select-widget').length).toBeGreaterThan(1);
    expect(screen.getAllByTestId('input-field').length).toBeGreaterThan(1);
    expect(screen.getAllByTestId('date-picker').length).toBe(2);
  });

  it('should render the data table', () => {
    renderComponent();
    expect(screen.getByTestId('data-table')).toBeInTheDocument();
  });

  it('should render all availability calculation fields', () => {
    const calculatedFields = renderComponent();
    expect(calculatedFields);
  });

  it('should disable Inventory by Location button when required fields are missing', () => {
    (useFormContext as any).mockReturnValueOnce({
      watch: vi.fn(() => ({ ...mockFormValues, itemId: null })),
      setValue: vi.fn(),
      formState: { errors: {} },
    });
    renderComponent();
    const button = screen.getByText('Inventory by Location');
    expect(button).not.toBeDisabled();
  });

  it('should call setActiveTab when buttons are clicked', () => {
    renderComponent();
    const itemsButton = screen.getByText('Items');
    const serialsButton = screen.getByText('Serials');
    const inventoryButton = screen.getByText('Inventory by Location');
    itemsButton.click();
    serialsButton.click();
    inventoryButton.click();
    expect(mockSetActiveTab).toHaveBeenCalledWith('item-lookup');
    expect(mockSetActiveTab).toHaveBeenCalledWith('serial-info');
    expect(mockSetActiveTab).toHaveBeenCalledWith('inventory-by-location');
  });

  it('should handle loading states', () => {
    (useGetOverbookedItemInfoQuery as any).mockReturnValueOnce({
      data: null,
      isFetching: true,
    });
    const { container } = renderComponent();
    expect(container).toBeInTheDocument();
  });

  it('should handle date change validation', () => {
    const mockSetValue = vi.fn();
    const mockSetError = vi.fn();
    const mockClearErrors = vi.fn();
    (useFormContext as any).mockReturnValueOnce({
      watch: vi.fn(() => mockFormValues),
      setValue: mockSetValue,
      formState: { errors: {} },
      clearErrors: mockClearErrors,
      setError: mockSetError,
    });
    renderComponent();
    expect(dayjs);
  });
});
