import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen } from '@testing-library/react';
import { useForm, FormProvider, useFormContext } from 'react-hook-form';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { setupListeners } from '@reduxjs/toolkit/query';
import OverbookedItemInfo from './index';
import BreadcrumbDialogRenderer from '@/components/common/BreadcrumbDialogRenderer';
import { useGetSerialInfoByItemQuery } from '@/redux/features/orders/order.api';
import { OverBookedItemInfoTypes } from '@/types/order.types';
import React from 'react';

vi.mock('react-hook-form', async () => {
  const actual = await import('react-hook-form');
  return {
    ...actual,
    useFormContext: vi.fn(),
  };
});

vi.mock('@/components/common/BreadcrumbDialogRenderer', () => ({
  default: vi.fn(({ listItems }) => (
    <div data-testid="breadcrumb-dialog">
      {listItems.map((item: any) => (
        <div key={item.value}>{item.content}</div>
      ))}
    </div>
  )),
}));

vi.mock('@/redux/features/orders/order.api', () => ({
  useGetSerialInfoByItemQuery: vi.fn(),
}));

vi.mock('./OverbookedItems', () => ({
  default: vi.fn(() => <div>OverbookedItems Component</div>),
}));

vi.mock('./ItemLookup', () => ({
  default: vi.fn(() => <div>ItemLookup Component</div>),
}));

vi.mock('./SerialInfo', () => ({
  default: vi.fn(() => <div>SerialInfo Component</div>),
}));

vi.mock('./InventoryByLocation', () => ({
  default: vi.fn(() => <div>InventoryByLocation Component</div>),
}));

vi.mock('@/lib/utils', () => ({
  formatDate: vi.fn(() => '2023-01-01'),
  convertToFloat: vi.fn(() => '$0.00'),
  DATE_FORMAT_YYYYMMDD: 'YYYY-MM-DD',
}));

const mockStore = configureStore({
  reducer: {},
});

setupListeners(mockStore.dispatch);

const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const formMethods = useForm<OverBookedItemInfoTypes>();
  return <FormProvider {...formMethods}>{children}</FormProvider>;
};

describe('OverbookedItemInfo Component', () => {
  const mockOnOpenChange = vi.fn();
  const mockItemId = { label: 'ITEM-001', value: '123' };
  const mockOrderFormValues = {
    deliveryOrder: {
      id: '1',
      deliveryDate: '2023-01-10',
      pickupDate: '2023-01-15',
    },
    shipOrder: {},
    willCallOrder: {},
  };

  beforeEach(() => {
    vi.clearAllMocks();
    (useFormContext as any).mockImplementation(() => ({
      getValues: vi.fn(() => mockOrderFormValues),
      watch: vi.fn(),
      setValue: vi.fn(),
    }));
    (useGetSerialInfoByItemQuery as any).mockReturnValue({
      data: {
        data: [{ serialNo: 'SERIAL-001' }, { serialNo: 'SERIAL-002' }],
      },
      isFetching: false,
    });
  });

  const renderComponent = (props = {}) => {
    return render(
      <Provider store={mockStore}>
        <TestWrapper>
          <OverbookedItemInfo
            open={true}
            onOpenChange={mockOnOpenChange}
            itemId={mockItemId}
            {...props}
          />
        </TestWrapper>
      </Provider>
    );
  };

  it('should render without crashing', () => {
    renderComponent();
    expect(screen.getByTestId('breadcrumb-dialog')).toBeInTheDocument();
  });

  it('should initialize with default values', () => {
    renderComponent();
    expect(screen.getByText('OverbookedItems Component')).toBeInTheDocument();
  });

  it('should render in modal mode', () => {
    renderComponent({ isModal: true });
    expect(BreadcrumbDialogRenderer).toHaveBeenCalledWith(
      expect.objectContaining({
        isOpen: true,
      }),
      expect.anything()
    );
  });

  it('should render in non-modal mode', () => {
    renderComponent({ isModal: false });
    expect(screen.getAllByText('OverbookedItems Component'));
  });

  it('should handle itemId prop correctly', () => {
    renderComponent({ itemId: mockItemId });
    expect(useGetSerialInfoByItemQuery).toHaveBeenCalledWith('123', {
      skip: false,
    });
  });

  it('should skip serial info query when no itemId', () => {
    renderComponent({ itemId: undefined });
    expect(useGetSerialInfoByItemQuery).toHaveBeenCalledWith(undefined, {
      skip: true,
    });
  });

  it('should use delivery order dates when deliveryOrder exists', () => {
    renderComponent();
    expect(useGetSerialInfoByItemQuery).toHaveBeenCalled();
  });

  it('should use ship order dates when shipOrder exists', () => {
    (useFormContext as any).mockImplementation(() => ({
      getValues: vi.fn(() => ({
        deliveryOrder: {},
        shipOrder: {
          id: '2',
          shipDate: '2023-01-12',
          returnArrivalDate: '2023-01-18',
        },
        willCallOrder: {},
      })),
    }));
    renderComponent();
    expect(useGetSerialInfoByItemQuery).toHaveBeenCalled();
  });

  it('should use willCall order dates when willCallOrder exists', () => {
    (useFormContext as any).mockImplementation(() => ({
      getValues: vi.fn(() => ({
        deliveryOrder: {},
        shipOrder: {},
        willCallOrder: {
          id: '3',
          pickupDate: '2023-01-14',
          returnDate: '2023-01-20',
        },
      })),
    }));
    renderComponent();
    expect(useGetSerialInfoByItemQuery).toHaveBeenCalled();
  });

  it('should use current date when no order exists', () => {
    (useFormContext as any).mockImplementation(() => ({
      getValues: vi.fn(() => ({
        deliveryOrder: {},
        shipOrder: {},
        willCallOrder: {},
      })),
    }));
    renderComponent();
    expect(useGetSerialInfoByItemQuery).toHaveBeenCalled();
  });

  it('should filter tabs correctly based on activeTab', () => {
    renderComponent();
    expect(BreadcrumbDialogRenderer).toHaveBeenCalledWith(
      expect.objectContaining({
        listItems: expect.arrayContaining([
          expect.objectContaining({ value: 'overbooked-item' }),
        ]),
      }),
      expect.anything()
    );
  });
});
