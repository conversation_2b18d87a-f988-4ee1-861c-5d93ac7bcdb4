import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen } from '@testing-library/react';
import InventoryByLocation from './InventoryByLocation';
import { useGetInventoryBylocationQuery } from '@/redux/features/orders/order.api';
import { useFormContext } from 'react-hook-form';

// Mock dependencies
vi.mock('@/redux/features/orders/order.api', () => ({
  useGetInventoryBylocationQuery: vi.fn(),
}));

vi.mock('react-hook-form', () => ({
  useFormContext: vi.fn(),
}));

vi.mock('@/lib/utils', () => ({
  formatDate: vi.fn((date) => date || 'formatted-date'),
  DATE_FORMAT_YYYYMMDD: 'YYYY-MM-DD',
}));

vi.mock('@/components/common/data-tables', () => ({
  default: vi.fn(() => <div data-testid="data-table" />),
}));

vi.mock('@/components/common/app-button', () => ({
  default: vi.fn(({ onClick, label }) => (
    <button onClick={onClick}>{label}</button>
  )),
}));

vi.mock('@/components/ui/input', () => ({
  Input: vi.fn(({ value, placeholder }) => (
    <input value={value} placeholder={placeholder} data-testid="input-field" />
  )),
}));

vi.mock('@/components/common/switch', () => ({
  default: vi.fn(() => <div data-testid="switch-field" />),
}));

vi.mock('@/components/forms/Label', () => ({
  default: vi.fn(({ label }) => <label>{label}</label>),
}));

describe('InventoryByLocation Component', () => {
  const mockHandleChange = vi.fn();
  const mockFormValues = {
    itemId: { value: '123', label: 'ITEM-001' },
    deliveryDate: '2023-01-10',
    pickupDate: '2023-01-15',
    includeQuotes: 'true',
  };

  beforeEach(() => {
    vi.clearAllMocks();
    (useFormContext as any).mockReturnValue({
      watch: vi.fn(() => mockFormValues),
      getValues: vi.fn(() => mockFormValues),
    });
    (useGetInventoryBylocationQuery as any).mockReturnValue({
      data: {
        data: [
          { location: 'Warehouse A', owned: 10, rented: 5, available: 5 },
          { location: 'Warehouse B', owned: 15, rented: 3, available: 12 },
        ],
      },
      isLoading: false,
    });
  });

  it('should render without crashing', () => {
    render(<InventoryByLocation handleChange={mockHandleChange} />);
    expect(screen.getByTestId('data-table')).toBeInTheDocument();
  });

  it('should display correct item information', () => {
    render(<InventoryByLocation handleChange={mockHandleChange} />);
    expect(screen.getAllByTestId('input-field')[0]).toHaveValue('ITEM-001');
    expect(screen.getAllByTestId('input-field')[1]).toHaveValue('2023-01-10');
    expect(screen.getAllByTestId('input-field')[2]).toHaveValue('2023-01-15');
  });

  it('should call useGetInventoryBylocationQuery with correct payload', () => {
    render(<InventoryByLocation handleChange={mockHandleChange} />);
    expect(useGetInventoryBylocationQuery).toHaveBeenCalledWith({
      itemId: '123',
      deliveryDate: '2023-01-10',
      pickupDate: '2023-01-15',
      includeQuotes: 'true',
    });
  });

  it('should render DataTable with correct props', () => {
    render(<InventoryByLocation handleChange={mockHandleChange} />);
    expect(screen.getByTestId('data-table')).toBeInTheDocument();
  });

  it('should render SwitchField for includeQuotes', () => {
    render(<InventoryByLocation handleChange={mockHandleChange} />);
    expect(screen.getByTestId('switch-field')).toBeInTheDocument();
  });

  it('should call handleChange when Cancel button is clicked', () => {
    render(<InventoryByLocation handleChange={mockHandleChange} />);
    const cancelButton = screen.getByText('Cancel');
    cancelButton.click();
    expect(mockHandleChange).toHaveBeenCalled();
  });

  it('should handle loading state', () => {
    (useGetInventoryBylocationQuery as any).mockReturnValue({
      data: null,
      isLoading: true,
    });
    const isLoading = render(
      <InventoryByLocation handleChange={mockHandleChange} />
    );
    expect(isLoading);
  });

  it('should handle empty data state', () => {
    (useGetInventoryBylocationQuery as any).mockReturnValue({
      data: { data: [] },
      isLoading: false,
    });
    const isRendered = render(
      <InventoryByLocation handleChange={mockHandleChange} />
    );
    expect(isRendered);
  });
});
