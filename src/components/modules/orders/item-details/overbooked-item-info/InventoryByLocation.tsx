import AppButton from '@/components/common/app-button';
import DataTable from '@/components/common/data-tables';
import <PERSON><PERSON><PERSON>ield from '@/components/common/switch';
import Labels from '@/components/forms/Label';
import { Input } from '@/components/ui/input';
import { DATE_FORMAT_YYYYMMDD, formatDate } from '@/lib/utils';
import { useGetInventoryBylocationQuery } from '@/redux/features/orders/order.api';
import { SortingStateType } from '@/types/common.types';
import { OverBookedItemInfoTypes } from '@/types/order.types';
import { useCallback, useMemo, useState } from 'react';
import { useFormContext } from 'react-hook-form';

const InventoryByLocation = ({
  handleChange,
}: {
  handleChange: () => void;
}) => {
  const form = useFormContext<OverBookedItemInfoTypes>();
  const info = form.watch();
  const [sorting, setSorting] = useState<SortingStateType[]>([
    { id: 'location', desc: true },
  ]);

  const payload = useMemo(() => {
    const itemId = info?.itemId?.value;
    const deliveryDate = formatDate(info?.deliveryDate, DATE_FORMAT_YYYYMMDD);
    const pickupDate = formatDate(info?.pickupDate, DATE_FORMAT_YYYYMMDD);
    const includeQuotes = info.includeQuotes;
    return {
      itemId,
      deliveryDate,
      pickupDate,
      includeQuotes,
    };
  }, [
    info?.deliveryDate,
    info.includeQuotes,
    info?.itemId?.value,
    info?.pickupDate,
  ]);

  const { data, isLoading } = useGetInventoryBylocationQuery(payload);

  // Table columns
  const columns = useMemo(() => {
    return [
      {
        accessorKey: 'location',
        header: 'Location',
        size: 80,
        enableSorting: true,
        invertSorting: true,
      },
      {
        accessorKey: 'owned',
        header: 'Owned',
        maxSize: 250,
        enableSorting: true,
        invertSorting: true,
      },
      {
        accessorKey: 'rented',
        header: 'Rented',
        size: 80,
        enableSorting: true,
        invertSorting: true,
      },
      {
        accessorKey: 'available',
        header: 'Available',
        size: 80,
        enableSorting: true,
        invertSorting: true,
      },
    ];
  }, []);

  const InputFiled = useCallback(
    ({ value, label }: { value: string; label: string }) => {
      return (
        <div>
          <Labels label={label} className="pb-1" />
          <Input value={value} placeholder={label} disabled />
        </div>
      );
    },
    []
  );

  return (
    <div className="px-2">
      <div className="w-full flex justify-end">
        <div className="flex justify-end items-center gap-4">
          <InputFiled label="Item ID" value={info?.itemId?.label} />
          <InputFiled
            label="Delivery Date"
            value={formatDate(info?.deliveryDate)}
          />
          <InputFiled
            label="Pickup Date"
            value={formatDate(info?.pickupDate)}
          />
          <SwitchField
            form={form}
            name="includeQuotes"
            label="Include Quotes"
            className="mt-8 w-fit"
          />
        </div>
      </div>
      <div className="py-4">
        <DataTable
          data={data?.data ?? []}
          columns={columns}
          isLoading={isLoading}
          totalItems={data?.data?.length}
          enablePagination={false}
          tableClassName="max-h-[280px] 2xl:max-h-[400px]"
          setSorting={setSorting}
          sorting={sorting}
          manualSorting={false}
        />
      </div>

      <div className="absolute bottom-6 right-7">
        <AppButton
          label="Cancel"
          onClick={() => handleChange()}
          variant="neutral"
          className="w-32"
        />
      </div>
    </div>
  );
};

export default InventoryByLocation;
