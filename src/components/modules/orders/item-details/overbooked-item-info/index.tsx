import BreadcrumbDialogRenderer from '@/components/common/BreadcrumbDialogRenderer';
import { convertToFloat, DATE_FORMAT_YYYYMMDD, formatDate } from '@/lib/utils';
import { useGetSerialInfoByItemQuery } from '@/redux/features/orders/order.api';
import {
  OrderInformationTypes,
  OverBookedItemInfoTypes,
} from '@/types/order.types';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { FormProvider, useForm, useFormContext } from 'react-hook-form';
import InventoryByLocation from './InventoryByLocation';
import ItemLookup from './ItemLookup';
import OverBookedItems from './OverbookedItems';
import SerialInfo from './SerialInfo';

const OverbookedItemInfo = ({
  open = false,
  onOpenChange = () => {},
  itemId,
  isModal = true,
}: {
  open?: boolean;
  onOpenChange?: () => void;
  itemId?: { label?: string; value?: string };
  isModal?: boolean;
}) => {
  const [activeTab, setActiveTab] = useState('overbooked-item');

  const handleSetActiveTab = useCallback((tab?: string) => {
    setActiveTab(tab ?? 'overbooked-item');
  }, []);

  const orderForm = useFormContext<OrderInformationTypes>();
  const { deliveryOrder, shipOrder, willCallOrder } =
    orderForm?.getValues() || {};

  //  Retrieves the delivery date and pickup date based on the order type.
  const { deliveryDate, pickupDate } = useMemo(() => {
    const currentDate = formatDate(new Date(), DATE_FORMAT_YYYYMMDD);
    if (deliveryOrder?.id) {
      return {
        deliveryDate: deliveryOrder?.deliveryDate || '',
        pickupDate: deliveryOrder?.pickupDate || '',
      };
    }
    if (shipOrder?.id) {
      return {
        deliveryDate: shipOrder?.shipDate || '',
        pickupDate: shipOrder?.returnArrivalDate || '',
      };
    }
    if (willCallOrder?.id) {
      return {
        deliveryDate: willCallOrder?.pickupDate || '',
        pickupDate: willCallOrder?.returnDate || '',
      };
    }
    return { deliveryDate: currentDate, pickupDate: currentDate };
  }, [
    deliveryOrder?.id,
    deliveryOrder?.deliveryDate,
    deliveryOrder?.pickupDate,
    shipOrder?.id,
    shipOrder?.shipDate,
    shipOrder?.returnArrivalDate,
    willCallOrder?.id,
    willCallOrder?.pickupDate,
    willCallOrder?.returnDate,
  ]);

  const form = useForm<OverBookedItemInfoTypes>();
  const itemIdValue = form.watch('itemId')?.value;
  const defaultValues = useMemo(() => {
    return {
      itemId: itemId,
      displayType: 'RECENT',
      orderType: 'ORDERS_QUOTES',
      storeLocationId: '0',
      deliveryDate: deliveryDate,
      pickupDate: pickupDate,
      unitPrice: convertToFloat({
        value: 0,
        prefix: '$',
      }),
    };
  }, [deliveryDate, itemId, pickupDate]);

  useEffect(() => {
    if (defaultValues) {
      form.reset(defaultValues);
    }
  }, [defaultValues, form]);

  // get Serial # by item id
  const { data: serialData, isFetching: serialLoading } =
    useGetSerialInfoByItemQuery(itemIdValue, {
      skip: !itemIdValue,
    });

  // serial # list details
  const { serailData, serialList } = useMemo(() => {
    const serailData = serialData?.data;
    const serialList =
      serialData?.data?.map((item: { serialNo: string }) => ({
        label: item?.serialNo,
        value: item?.serialNo,
      })) || [];
    return { serialList, serailData };
  }, [serialData?.data]);

  // tab list
  const listItems = useMemo(() => {
    const tabList = [
      {
        label: 'Overbooked Item Info',
        value: 'overbooked-item',
        content: (
          <OverBookedItems
            serialList={serialList ?? []}
            setActiveTab={handleSetActiveTab}
            serialLoading={serialLoading}
          />
        ),
      },
      {
        label: 'Item Lookup',
        value: 'item-lookup',
        content: (
          <ItemLookup handleChange={() => handleSetActiveTab()} form={form} />
        ),
      },
      {
        label: 'Serial #',
        value: 'serial-info',
        content: (
          <SerialInfo data={serailData} handleChange={handleSetActiveTab} />
        ),
      },
      {
        label: 'Inventory By Location',
        value: 'inventory-by-location',
        content: <InventoryByLocation handleChange={handleSetActiveTab} />,
      },
    ];
    return tabList?.filter((tab) =>
      ['overbooked-item', activeTab]?.includes(tab?.value)
    );
  }, [
    activeTab,
    form,
    handleSetActiveTab,
    serailData,
    serialList,
    serialLoading,
  ]);

  return (
    <FormProvider {...form}>
      {isModal ? (
        <BreadcrumbDialogRenderer
          activeTab={activeTab}
          isOpen={open}
          listItems={listItems}
          onOpenChange={onOpenChange}
          setActiveTab={handleSetActiveTab}
          className="max-w-[95%] 2xl:max-w-[75%]"
          contentClassName="h-[470px] 2xl:h-[680px] overflow-y-auto"
        />
      ) : (
        <>
          <OverBookedItems
            serialList={serialList ?? []}
            setActiveTab={handleSetActiveTab}
            serialLoading={serialLoading}
          />
          <BreadcrumbDialogRenderer
            activeTab={activeTab}
            isOpen={
              !isModal &&
              ['item-lookup', 'serial-info', 'inventory-by-location']?.includes(
                activeTab
              )
            }
            listItems={listItems}
            onOpenChange={() => setActiveTab('')}
            setActiveTab={handleSetActiveTab}
            className="max-w-[70%] 2xl:max-w-[60%]"
            contentClassName="h-[480px] 2xl:h-[680px] overflow-y-auto"
          />
        </>
      )}
    </FormProvider>
  );
};

export default OverbookedItemInfo;
