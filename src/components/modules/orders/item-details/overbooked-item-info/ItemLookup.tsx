import AppButton from '@/components/common/app-button';
import DataTable from '@/components/common/data-tables';
import MultiCheckboxDropdown from '@/components/forms/MulitCheckbox';
import {
  CATEGORY_API_ROUTES,
  ITEMS_API_ROUTES,
} from '@/constants/api-constants';
import useOptionList from '@/hooks/useOptionList';
import { convertToFloat, getPaginationObject } from '@/lib/utils';
import { useGetItemLookupQuery } from '@/redux/features/items/item.api';
import { PaginationType, SortingStateType } from '@/types/common.types';
import { RowSelectionState } from '@tanstack/react-table';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';

const ItemLookup = ({
  handleChange,
  form,
}: {
  handleChange: (item?: { label: string; value: string }) => void;
  form: any;
}) => {
  const [search, setSearch] = useState<string>('');
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [pagination, setPagination] = useState<PaginationType>({
    pageSize: 10,
    pageIndex: 0,
  });
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});

  const [sorting, setSorting] = useState<SortingStateType[]>([
    { id: 'itemId', desc: false },
  ]);
  const [isFetchingMore, setIsFetchingMore] = useState<boolean>(false);

  // const form = useFormContext<OverBookedItemInfoTypes>();

  const payload = useMemo(() => {
    return getPaginationObject({
      pagination: pagination,
      sorting: sorting,
      filters: [
        { field: 'filter', value: search, operator: 'Contains' },
        {
          field: 'category',
          value: selectedCategories.join(','),
          operator: 'Contains',
        },
      ],
    });
  }, [pagination, search, selectedCategories, sorting]);

  const {
    data,
    isFetching: isLoading,
    isFetching,
    isSuccess,
  } = useGetItemLookupQuery({
    url: ITEMS_API_ROUTES.ALL,
    body: payload,
  });
  const ItemList = useMemo(
    () => (isSuccess && data?.data ? data?.data : []),
    [data?.data, isSuccess]
  );

  const { options: categoryList, optionLoading } = useOptionList({
    url: CATEGORY_API_ROUTES.ALL,
    labelKey: 'catDesc',
    valueKey: 'catDesc',
    sortBy: 'catDesc',
  });

  // Form
  const categoryForm = useForm();

  const selectedItemId = Object.keys(rowSelection)?.at(0);

  const handleClickOk = useCallback(() => {
    const item = ItemList?.find((item) => item?.id === Number(selectedItemId));
    form.setValue('itemId', {
      label: item?.itemId ?? '',
      value: item?.id?.toString() ?? '',
    });
    handleChange();
    setRowSelection({});
  }, [ItemList, form, handleChange, selectedItemId]);

  // Table columns
  const columns = useMemo(() => {
    return [
      {
        accessorKey: 'itemId',
        header: 'Item ID',
        size: 80,
        enableSorting: true,
      },
      {
        accessorKey: 'description',
        header: 'Item Description',
        maxSize: 250,
        enableSorting: true,
      },
      {
        accessorKey: 'unitPrice',
        header: 'Price',
        size: 80,
        enableSorting: true,
        cell: ({ row }: any) =>
          convertToFloat({ value: row.original.unitPrice, prefix: '$' }),
      },
    ];
  }, []);

  // Custom toolbar for category selection
  const CustomToolbar = (
    <MultiCheckboxDropdown
      name="category"
      form={categoryForm}
      optionsList={categoryList ?? []}
      placeholder={'Select Categories'}
      onChange={(value) => setSelectedCategories(value)}
      isLoading={optionLoading}
    />
  );

  const fetchMore = useCallback(() => {
    if (isFetchingMore) return; // Avoid duplicate calls

    setIsFetchingMore(true); // Set flag to prevent further calls
    setPagination((prev) => ({ ...prev, pageSize: prev.pageSize + 10 }));
  }, [isFetchingMore]);

  // Reset `isFetchingMore` only after new data arrives
  useEffect(() => {
    if (!isFetching) {
      setIsFetchingMore(false);
    }
  }, [isFetching]);

  return (
    <div className="py-2">
      <DataTable
        data={ItemList}
        pagination={pagination}
        setPagination={setPagination}
        columns={columns}
        isLoading={isLoading}
        totalItems={data?.pagination?.totalCount}
        search={search}
        setSearch={setSearch}
        enableSearch
        sorting={sorting}
        setSorting={setSorting}
        customToolBar={CustomToolbar}
        enablePagination={false}
        enableRowSelection
        onRowSelectionChange={setRowSelection}
        rowSelection={rowSelection}
        bindingKey="id"
        tableClassName="max-h-[300px] 2xl:max-h-[400px] overflow-auto"
        isInfiniteScroll
        fetchMore={fetchMore}
      />
      <div className="flex justify-end gap-4 absolute bottom-6 right-6">
        <AppButton
          label="Ok"
          onClick={handleClickOk}
          iconClassName="w-4 h-4"
          variant="primary"
          disabled={!selectedItemId}
          className="w-32"
        />
        <AppButton
          label="Cancel"
          onClick={() => handleChange()}
          variant="neutral"
          className="w-32"
        />
      </div>
    </div>
  );
};

export default ItemLookup;
