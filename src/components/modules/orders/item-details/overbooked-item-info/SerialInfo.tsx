import AppButton from '@/components/common/app-button';
import DataTable from '@/components/common/data-tables';
import { SortingStateType } from '@/types/common.types';
import { OverBookedItemInfoTypes } from '@/types/order.types';
import { RowSelectionState } from '@tanstack/react-table';
import { useCallback, useMemo, useState } from 'react';
import { useFormContext } from 'react-hook-form';

const SerialInfo = ({
  handleChange,
  data,
}: {
  handleChange: () => void;
  data: any[];
}) => {
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const form = useFormContext<OverBookedItemInfoTypes>();
  const [sorting, setSorting] = useState<SortingStateType[]>([
    { id: 'location', desc: true },
  ]);
  const selectedId = Object.keys(rowSelection)?.at(0);
  const handleClickOk = useCallback(() => {
    const seraiNo = data?.find(
      (_, index) => index === Number(selectedId)
    )?.serialNo;
    form.setValue('serialNo', seraiNo);
    handleChange();
  }, [data, form, handleChange, selectedId]);

  // Table columns
  const columns: any = useMemo(() => {
    return [
      {
        accessorKey: 'location',
        header: 'Location',
        size: 80,
        enableSorting: true,
        invertSorting: true,
      },
      {
        accessorKey: 'serialNo',
        header: 'Serial #',
        maxSize: 250,
        enableSorting: true,
        invertSorting: true,
        sortingFn: 'alphanumeric',
      },
      {
        accessorKey: 'quality',
        header: 'Quality',
        size: 80,
        enableSorting: true,
        invertSorting: true,
      },
    ];
  }, []);

  return (
    <div className="py-2">
      <DataTable
        data={data ?? []}
        columns={columns}
        totalItems={data?.length}
        sorting={sorting}
        setSorting={setSorting}
        enableRowSelection
        onRowSelectionChange={setRowSelection}
        rowSelection={rowSelection}
        enablePagination={false}
        manualSorting={false}
      />
      <div className="flex justify-end gap-4 absolute bottom-6 right-6">
        <AppButton
          label="Ok"
          onClick={handleClickOk}
          iconClassName="w-4 h-4"
          variant="primary"
          disabled={!selectedId}
          className="w-32"
        />
        <AppButton
          label="Cancel"
          onClick={() => handleChange()}
          variant="neutral"
          className="w-32"
        />
      </div>
    </div>
  );
};

export default SerialInfo;
