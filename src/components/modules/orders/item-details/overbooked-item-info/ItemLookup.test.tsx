import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import ItemLookup from './ItemLookup';
import { useGetItemLookupQuery } from '@/redux/features/items/item.api';
import { ITEMS_API_ROUTES } from '@/constants/api-constants';

// Mock dependencies
vi.mock('@/redux/features/items/item.api', () => ({
  useGetItemLookupQuery: vi.fn(() => ({
    data: {
      data: [
        {
          id: 1,
          itemId: 'ITEM-001',
          description: 'Test Item 1',
          unitPrice: 10,
        },
        {
          id: 2,
          itemId: 'ITEM-002',
          description: 'Test Item 2',
          unitPrice: 20,
        },
      ],
      pagination: { totalCount: 2 },
    },
    isFetching: false,
    isLoading: false,
    isSuccess: true,
  })),
}));

vi.mock('@/hooks/useOptionList', () => ({
  default: vi.fn(() => ({
    options: [
      { catDesc: 'Category 1', value: '1' },
      { catDesc: 'Category 2', value: '2' },
    ],
    optionLoading: false,
  })),
}));

vi.mock('react-hook-form', () => ({
  useForm: vi.fn(() => ({
    control: {},
    watch: vi.fn(),
  })),
}));

vi.mock('@/components/common/data-tables', () => ({
  default: vi.fn(({ onRowSelectionChange }) => (
    <div data-testid="data-table">
      <button onClick={() => onRowSelectionChange({ '1': true })}>
        Select Row
      </button>
    </div>
  )),
}));

vi.mock('@/components/common/app-button', () => ({
  default: vi.fn(({ onClick, label, disabled }) => (
    <button onClick={onClick} disabled={disabled}>
      {label}
    </button>
  )),
}));

vi.mock('@/components/forms/MulitCheckbox', () => ({
  default: vi.fn(() => <div data-testid="category-filter" />),
}));

vi.mock('@/lib/utils', () => ({
  convertToFloat: vi.fn(() => '$10.00'),
  getPaginationObject: vi.fn(() => ({})),
}));

describe('ItemLookup Component', () => {
  const mockHandleChange = vi.fn();
  const mockForm = {
    setValue: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render without crashing', () => {
    render(<ItemLookup handleChange={mockHandleChange} form={mockForm} />);
    expect(screen.getByTestId('data-table')).toBeInTheDocument();
  });

  it('should render category filter and data table', () => {
    render(<ItemLookup handleChange={mockHandleChange} form={mockForm} />);
    expect(screen.getByTestId('data-table')).toBeInTheDocument();
  });

  it('should render Ok and Cancel buttons', () => {
    render(<ItemLookup handleChange={mockHandleChange} form={mockForm} />);
    expect(screen.getByText('Ok')).toBeInTheDocument();
    expect(screen.getByText('Cancel')).toBeInTheDocument();
  });

  it('should disable Ok button when no item is selected', () => {
    render(<ItemLookup handleChange={mockHandleChange} form={mockForm} />);
    const okButton = screen.getByText('Ok');
    expect(okButton).toBeDisabled();
  });

  it('should enable Ok button when item is selected', () => {
    render(<ItemLookup handleChange={mockHandleChange} form={mockForm} />);
    const selectButton = screen.getByText('Select Row');
    fireEvent.click(selectButton);
    const okButton = screen.getByText('Ok');
    expect(okButton).not.toBeDisabled();
  });

  it('should call handleChange with selected item when Ok is clicked', () => {
    render(<ItemLookup handleChange={mockHandleChange} form={mockForm} />);
    const selectButton = screen.getByText('Select Row');
    fireEvent.click(selectButton);
    const okButton = screen.getByText('Ok');
    fireEvent.click(okButton);
    expect(mockForm.setValue).toHaveBeenCalledWith('itemId', {
      label: 'ITEM-001',
      value: '1',
    });
    expect(mockHandleChange).toHaveBeenCalled();
  });

  it('should call handleChange without arguments when Cancel is clicked', () => {
    render(<ItemLookup handleChange={mockHandleChange} form={mockForm} />);
    const cancelButton = screen.getByText('Cancel');
    fireEvent.click(cancelButton);
    expect(mockHandleChange).toHaveBeenCalledWith();
  });

  it('should fetch items with correct filters', () => {
    render(<ItemLookup handleChange={mockHandleChange} form={mockForm} />);
    expect(useGetItemLookupQuery).toHaveBeenCalledWith({
      url: ITEMS_API_ROUTES.ALL,
      body: expect.any(Object),
    });
  });

  it('should show loading state when data is fetching', () => {
    vi.mocked(useGetItemLookupQuery).mockReturnValue({
      data: undefined,
      isFetching: true,
      isLoading: true,
      isSuccess: false,
    } as any);

    const isFechingData = render(
      <ItemLookup handleChange={mockHandleChange} form={mockForm} />
    );
    expect(isFechingData);
  });
});
