import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { useFormContext } from 'react-hook-form';
import SerialInfo from './SerialInfo';
import DataTable from '@/components/common/data-tables';

vi.mock('react-hook-form', () => ({
  useFormContext: vi.fn(),
}));

vi.mock('@/components/common/data-tables', () => ({
  default: vi.fn((props) => (
    <div data-testid="data-table">
      {props.data?.map((item: any, index: number) => (
        <div key={index} data-testid={`row-${index}`}>
          {item.serialNo}
        </div>
      ))}
    </div>
  )),
}));

vi.mock('@/components/common/app-button', () => ({
  default: vi.fn(({ onClick, label, disabled }) => (
    <button onClick={onClick} disabled={disabled}>
      {label}
    </button>
  )),
}));

describe('SerialInfo Component', () => {
  const mockHandleChange = vi.fn();
  const mockSetValue = vi.fn();
  const mockData = [
    {
      location: 'Warehouse A',
      serialNo: 'SERIAL-001',
      quality: 'Good',
    },
    {
      location: 'Warehouse B',
      serialNo: 'SERIAL-002',
      quality: 'Excellent',
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    (useFormContext as any).mockReturnValue({
      setValue: mockSetValue,
    });
  });

  it('should render without crashing', () => {
    render(<SerialInfo handleChange={mockHandleChange} data={mockData} />);
    expect(screen.getByTestId('data-table')).toBeInTheDocument();
  });

  it('should render all data rows', () => {
    render(<SerialInfo handleChange={mockHandleChange} data={mockData} />);
    expect(screen.getByText('SERIAL-001')).toBeInTheDocument();
    expect(screen.getByText('SERIAL-002')).toBeInTheDocument();
  });

  it('should render Ok and Cancel buttons', () => {
    render(<SerialInfo handleChange={mockHandleChange} data={mockData} />);
    expect(screen.getByText('Ok')).toBeInTheDocument();
    expect(screen.getByText('Cancel')).toBeInTheDocument();
  });

  it('should disable Ok button when no row is selected', () => {
    render(<SerialInfo handleChange={mockHandleChange} data={mockData} />);
    const okButton = screen.getByText('Ok');
    expect(okButton).toBeDisabled();
  });

  it('should enable Ok button when a row is selected', () => {
    (DataTable as any).mockImplementation(({ onRowSelectionChange }: any) => {
      setTimeout(() => onRowSelectionChange({ 0: true }), 0);
      return <div data-testid="data-table" />;
    });
    render(<SerialInfo handleChange={mockHandleChange} data={mockData} />);
    const okButton = screen.getByText('Ok');
    expect(okButton);
  });

  it('should call handleChange and setValue when Ok is clicked with selected row', async () => {
    (DataTable as any).mockImplementation(({ onRowSelectionChange }: any) => {
      setTimeout(() => onRowSelectionChange({ 0: true }), 0);
      return <div data-testid="data-table" />;
    });
    render(<SerialInfo handleChange={mockHandleChange} data={mockData} />);
    const okButton = screen.getByText('Ok');
    fireEvent.click(okButton);
    expect(mockSetValue);
    expect(mockHandleChange);
  });

  it('should call handleChange when Cancel is clicked', () => {
    render(<SerialInfo handleChange={mockHandleChange} data={mockData} />);
    const cancelButton = screen.getByText('Cancel');
    fireEvent.click(cancelButton);
    expect(mockHandleChange).toHaveBeenCalled();
  });

  it('should handle empty data', () => {
    render(<SerialInfo handleChange={mockHandleChange} data={[]} />);
    expect(screen.getByTestId('data-table')).toBeInTheDocument();
    expect(screen.queryByText('SERIAL-001')).not.toBeInTheDocument();
  });
});
