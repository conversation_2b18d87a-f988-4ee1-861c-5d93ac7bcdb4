import AppButton from '@/components/common/app-button';
import DataTable from '@/components/common/data-tables';
import AutoCompleteDropdown from '@/components/forms/auto-complete-dropdown';
import DatePicker from '@/components/forms/date-picker';
import InputField from '@/components/forms/input-field';
import SelectWidget from '@/components/forms/select';
import { ITEMS_API_ROUTES } from '@/constants/api-constants';
import {
  convertToFloat,
  DATE_FORMAT_YYYYMMDD,
  formatDate,
  generateLabelValuePairs,
} from '@/lib/utils';
import { useGetStoreLocationsQuery } from '@/redux/features/store/store.api';
import { useGetEnumsListQuery } from '@/redux/features/enums-api/enums-api';
import { useGetOverbookedItemInfoQuery } from '@/redux/features/orders/order.api';
import { SortingStateType } from '@/types/common.types';
import { ItemIdTypes, OverBookedItemInfoTypes } from '@/types/order.types';
import dayjs from 'dayjs';
import { MapPin, Search } from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useFormContext } from 'react-hook-form';

interface AvailabilityCalculationTypes {
  name:
    | 'cleanupDays'
    | 'unitPrice'
    | 'subRented'
    | 'owned'
    | 'ordered'
    | 'rented'
    | 'available';
  label: string;
}

const OverBookedItems = ({
  serialList,
  setActiveTab,
  serialLoading,
}: {
  serialList: ItemIdTypes[] | any[];
  setActiveTab: (value: string) => void;
  serialLoading: boolean;
}) => {
  // const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const [sorting, setSorting] = useState<SortingStateType[]>([
    { id: 'orderNo', desc: true },
  ]);
  const form = useFormContext<OverBookedItemInfoTypes>();

  const overbookedInfo = form.watch();
  const ItemIdValue = overbookedInfo?.itemId?.value;
  const disabledInventoryBylocation =
    !ItemIdValue ||
    !overbookedInfo?.deliveryDate ||
    !overbookedInfo?.pickupDate;

  const payload = useMemo(() => {
    return {
      itemId: Number(ItemIdValue),
      serialNo: overbookedInfo?.serialNo,
      storeLocationId: Number(overbookedInfo?.storeLocationId),
      deliveryDate: formatDate(
        overbookedInfo?.deliveryDate,
        DATE_FORMAT_YYYYMMDD
      ),
      pickupDate: formatDate(overbookedInfo?.pickupDate, DATE_FORMAT_YYYYMMDD),
      displayType: overbookedInfo?.displayType,
      orderType: overbookedInfo?.orderType,
    };
  }, [
    ItemIdValue,
    overbookedInfo?.deliveryDate,
    overbookedInfo?.displayType,
    overbookedInfo?.orderType,
    overbookedInfo?.pickupDate,
    overbookedInfo?.serialNo,
    overbookedInfo?.storeLocationId,
  ]);

  // get overbooked item info
  const { data: overbookedData, isFetching: isLoading } =
    useGetOverbookedItemInfoQuery(payload, {
      skip: disabledInventoryBylocation,
    });

  const defaultValues = useMemo(() => {
    const overBookedInfo = overbookedData?.data;
    return {
      description: overBookedInfo?.description,
      cleanupDays: overBookedInfo?.cleanupDays,
      unitPrice: convertToFloat({
        value: overBookedInfo?.unitPrice,
        prefix: '$',
      }),
      owned: overBookedInfo?.owned,
      rented: overBookedInfo?.rented,
      subRented: overBookedInfo?.subRented,
      ordered: overBookedInfo?.ordered,
      available: overBookedInfo?.available,
    };
  }, [overbookedData?.data]);

  useEffect(() => {
    if (defaultValues) {
      form.setValue('description', defaultValues?.description ?? '');
      form.setValue('cleanupDays', defaultValues?.cleanupDays ?? '0');
      form.setValue('unitPrice', defaultValues?.unitPrice ?? '0');
      form.setValue('owned', defaultValues?.owned ?? '0');
      form.setValue('rented', defaultValues?.rented ?? '0');
      form.setValue('subRented', defaultValues?.subRented ?? '0');
      form.setValue('ordered', defaultValues?.ordered ?? '0');
      form.setValue('available', defaultValues?.available ?? '0');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [defaultValues]);

  const { data: locationData, isLoading: locationLoading } =
    useGetStoreLocationsQuery();

  const { data: displayList, isLoading: displayLoading } = useGetEnumsListQuery(
    {
      name: 'OverbookedItemDisplayType',
    }
  );
  const { data: orderType, isLoading: orderTypeLoading } = useGetEnumsListQuery(
    {
      name: 'OverbookedItemOrderType',
    }
  );

  const columns: any = useMemo(
    () => [
      {
        accessorKey: 'orderNo',
        header: 'Order #',
        enableSorting: true,
        invertSorting: true,
        sortingFn: 'alphanumeric',
      },
      {
        accessorKey: 'location',
        header: 'Location',
        enableSorting: true,
        size: 200,
        maxSize: 200,
        invertSorting: true,
      },
      {
        accessorKey: 'customer',
        header: 'Customer',
        size: 200,
        maxSize: 200,
        enableSorting: true,
        invertSorting: true,
      },
      {
        accessorKey: 'orderType',
        header: 'Order Type',
        enableSorting: true,
        invertSorting: true,
      },
      {
        accessorKey: 'quantity',
        header: 'Quantity',
        enableSorting: true,
        invertSorting: true,
      },
      {
        accessorKey: 'dateOfUseFrom',
        header: 'Date of Use',
        cell: ({ row }: any) => formatDate(row?.original?.dateOfUseFrom),
        enableSorting: true,
        sortingFn: 'datetime',
        invertSorting: true,
      },
      {
        accessorKey: 'deliveryDate',
        header: 'Delivery Date',
        cell: ({ row }: any) => formatDate(row?.original?.deliveryDate),
        enableSorting: true,
        size: 160,
        invertSorting: true,
        sortingFn: 'datetime',
      },
      {
        accessorKey: 'pickupDate',
        header: 'Pickup Date',
        cell: ({ row }: any) => formatDate(row?.original?.pickupDate),
        enableSorting: true,
        invertSorting: true,
        sortingFn: 'datetime',
      },
    ],
    []
  );

  const storeLocaitonList = generateLabelValuePairs({
    data: locationData?.data,
    labelKey: 'location',
    valueKey: 'id',
  });

  const locationList = storeLocaitonList?.length
    ? [...[{ label: 'All Locations', value: '0' }], ...storeLocaitonList]
    : [];

  // Availability Calculation
  const AvailabilityCalculation: AvailabilityCalculationTypes[] = [
    {
      name: 'cleanupDays',
      label: 'Clean Up Days',
    },
    {
      name: 'unitPrice',
      label: 'Unit Price',
    },
    {
      name: 'owned',
      label: 'Owned',
    },
    {
      name: 'rented',
      label: 'Rented',
    },
    {
      name: 'subRented',
      label: 'Sub Rented',
    },
    {
      name: 'ordered',
      label: 'Ordered',
    },
    {
      name: 'available',
      label: 'Available',
    },
  ];

  //Handles the date change for delivery and pickup dates.
  const handleOnDateChange = useCallback(
    (newDate: Date | string, fieldName: 'deliveryDate' | 'pickupDate') => {
      const updatedDate = dayjs(newDate);
      const currentDate = dayjs(form.watch(fieldName));
      if (
        (fieldName === 'pickupDate' && updatedDate?.isAfter(currentDate)) ||
        (fieldName === 'deliveryDate' && updatedDate?.isBefore(currentDate))
      ) {
        form.setValue(fieldName, updatedDate?.format('YYYY-MM-DD'));
      }
      const dateFieldName =
        fieldName === 'pickupDate' ? 'deliveryDate' : 'pickupDate';
      const hasErrorsAndValue = !!(
        Object.keys(form.formState.errors).length && newDate
      );

      if (!newDate) {
        form.setError(dateFieldName, { message: 'Required' });
      } else if (hasErrorsAndValue) {
        form.clearErrors(dateFieldName);
      }
    },
    [form]
  );

  return (
    <div className="mt-[-10px]">
      <div className="p-4 rounded-md border grid  md:grid-cols-4 gap-4">
        <div className="flex items-start gap-2">
          <AutoCompleteDropdown
            name="itemId"
            label="Item ID"
            placeholder="Select itemID"
            form={form}
            url={ITEMS_API_ROUTES.ALL}
            labelKey="itemId"
            valueKey="id"
            sortBy="itemId"
          />
          <AppButton
            label="Items"
            className="mt-8"
            icon={Search}
            iconClassName="w-5 h-5"
            onClick={() => setActiveTab('item-lookup')}
          />
        </div>
        <div className="flex items-start gap-2">
          <SelectWidget
            form={form}
            optionsList={serialList}
            name="serialNo"
            label="Serial #"
            placeholder="Serial"
            isLoading={serialLoading}
            menuPosition="absolute"
            disabled={!ItemIdValue}
          />
          <AppButton
            label="Serials"
            className="mt-8"
            icon={Search}
            iconClassName="w-5 h-5"
            onClick={() => setActiveTab('serial-info')}
            disabled={!ItemIdValue}
          />
        </div>

        <SelectWidget
          form={form}
          optionsList={displayList?.data ?? []}
          name="displayType"
          label="Display"
          placeholder="Select Display"
          isClearable={false}
          isLoading={displayLoading}
          menuPosition="absolute"
        />
        <SelectWidget
          form={form}
          optionsList={orderType?.data ?? []}
          isLoading={orderTypeLoading}
          name="orderType"
          label="Order Type"
          placeholder="Select Order Type"
          isClearable={false}
          menuPosition="absolute"
        />
        <InputField
          name="description"
          form={form}
          label="Item Description"
          placeholder="Enter Item Description"
          disabled
        />
        <SelectWidget
          form={form}
          optionsList={locationList ?? []}
          name="storeLocationId"
          label="Location"
          placeholder="Select Location"
          isLoading={locationLoading}
          menuPosition="absolute"
          isClearable={false}
        />
        <DatePicker
          form={form}
          name="deliveryDate"
          label="Delivery Date"
          placeholder="Select Date"
          enableInput
          onDateChange={(date) => handleOnDateChange(date ?? '', 'pickupDate')}
        />
        <DatePicker
          form={form}
          name="pickupDate"
          label="Pickup Date"
          placeholder="Select Date"
          enableInput
          onDateChange={(date) =>
            handleOnDateChange(date ?? '', 'deliveryDate')
          }
        />
      </div>
      <div className="flex justify-between items-center mt-5 my-2">
        <p className="text-xl 1xl:text-1xl font-semibold text-text-Default">
          Availability Calculation
        </p>
        <div className="flex justify-end gap-3">
          <AppButton
            type="button"
            label={'Inventory by Location'}
            className="min-w-28"
            icon={MapPin}
            iconClassName="w-4"
            onClick={() => setActiveTab('inventory-by-location')}
            disabled={disabledInventoryBylocation}
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="grid grid-cols-1 md:grid-cols-2 p-3 gap-4 border border-border-Default rounded-lg h-fit">
          {AvailabilityCalculation?.map((item, index) => (
            <InputField
              name={item?.name}
              form={form}
              label={item?.label}
              placeholder="______"
              disabled
              key={`${item?.name}-${index}`}
            />
          ))}
        </div>
        <div className="col-span-3">
          <DataTable
            data={overbookedData?.data?.overbookedItemOrders ?? []}
            columns={columns}
            // enableRowSelection
            totalItems={overbookedData?.data?.overbookedItemOrders?.length}
            isLoading={isLoading}
            // rowSelection={rowSelection}
            // onRowSelectionChange={setRowSelection}
            tableClassName="max-h-[350px] 2xl:max-h-[370px] overflow-auto min-w-[80%] md:min-w-[60%] 2xl:md:min-w-[70%] z-0"
            enablePagination={false}
            sorting={sorting}
            setSorting={setSorting}
            manualSorting={false}
          />
        </div>
      </div>
      <h2 className="text-1xl my-3">
        <span className="font-medium">Note :</span> The rented total may not
        equal the sum of the quantities for the selected orders as items retumed
        from some orders could be used to fill other selected orders.
      </h2>
    </div>
  );
};

export default OverBookedItems;
