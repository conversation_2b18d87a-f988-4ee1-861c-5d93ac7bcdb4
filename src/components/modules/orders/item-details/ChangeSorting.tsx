import CustomDialog from '@/components/common/dialog';
import FormActionButtons from '@/components/common/FormActionButtons';
import SelectDropDown from '@/components/forms/select-dropdown';
import { cn, getQueryParam } from '@/lib/utils';
import { useGetEnumsListQuery } from '@/redux/features/enums-api/enums-api';
import { useChangeSortingMutation } from '@/redux/features/orders/item-details.api';
import { OrderInformationTypes } from '@/types/order.types';
import { ChangeSortingTypes } from '@/types/orders/order-item-details.types';
import { useEffect, useMemo } from 'react';
import { SubmitHandler, useForm, useFormContext } from 'react-hook-form';
import { OpenDialogType } from './item-list';

interface ChangeSortingProps {
  open: OpenDialogType;
  onOpenChange: () => void;
}

const ChangeSorting = ({ open, onOpenChange }: ChangeSortingProps) => {
  const orderId = getQueryParam('id') as string;
  const orderForm = useFormContext<OrderInformationTypes>();
  const [changeSorting, { isLoading }] = useChangeSortingMutation();

  const defaultValues = useMemo(() => {
    return {
      orderId: Number(orderId),
      ItemPrintSequence: orderForm.watch('itemPrintSequence'),
      KitComponentPrintSeq:
        orderForm.watch('kitComponentPrintSeq') === null
          ? 'ITEM'
          : orderForm.watch('kitComponentPrintSeq'),
    };
  }, [orderForm, orderId]);

  const form = useForm<ChangeSortingTypes>();
  const { data: itemPrintSequenceData, isLoading: itemLoading } =
    useGetEnumsListQuery({
      name: 'ItemPrintSequence',
    });
  const { data: kitComponentPrintSeqData, isLoading: kitComponentLoading } =
    useGetEnumsListQuery({
      name: 'KitComponentPrintSeq',
    });

  const kitComponentPrintSeqList = kitComponentPrintSeqData?.data;
  const itemPrintSequenceList = itemPrintSequenceData?.data;

  useEffect(() => {
    if (defaultValues) {
      form.reset(defaultValues);
    }
  }, [defaultValues, form]);

  const handleItemPrintSequence = (value: string) => {
    form.setValue('ItemPrintSequence', value);
    if (value === 'PRINT_AS_ENTERED') {
      form.setValue('KitComponentPrintSeq', 'ITEM');
    }
  };

  const onSubmit: SubmitHandler<ChangeSortingTypes> = async (formData) => {
    const { ItemPrintSequence, KitComponentPrintSeq, orderId } = formData;
    const kitCompoPrintSeq =
      ItemPrintSequence === 'PRINT_AS_ENTERED' ? null : KitComponentPrintSeq;
    const payload = {
      orderId,
      ItemPrintSequence: ItemPrintSequence,
      KitComponentPrintSeq: kitCompoPrintSeq,
    };
    await changeSorting({ body: payload });
    orderForm.setValue('itemPrintSequence', ItemPrintSequence);
    orderForm.setValue('kitComponentPrintSeq', KitComponentPrintSeq);
    onOpenChange();
  };

  return (
    <CustomDialog
      onOpenChange={onOpenChange}
      description=""
      open={open.state}
      className={cn('max-w-[50%] md:w-[40%] 2xl:w-[30%]')}
      contentClassName={cn(
        'h-[300px] 2xl:h-[300px] overflow-y-auto p-0 border-none p-4'
      )}
      title={'Change Item'}
    >
      <div className="flex flex-col gap-4">
        <SelectDropDown
          form={form}
          name="ItemPrintSequence"
          label="Item Print Sequence"
          optionsList={itemPrintSequenceList}
          isLoading={itemLoading}
          onChange={handleItemPrintSequence}
          allowClear={false}
        />
        <SelectDropDown
          form={form}
          name="KitComponentPrintSeq"
          label="Kit Component Print Sequence"
          optionsList={kitComponentPrintSeqList}
          isLoading={kitComponentLoading}
          disabled={form.watch('ItemPrintSequence') === 'PRINT_AS_ENTERED'}
          allowClear={false}
        />
        <FormActionButtons
          className="fixed bottom-0 left-0 right-0 bg-white px-4 py-2 pt-0"
          onSubmit={form.handleSubmit(onSubmit)}
          submitLabel={'Ok'}
          onCancel={onOpenChange}
          isLoading={isLoading}
          cancelLabel="Cancel"
        />
      </div>
    </CustomDialog>
  );
};

export default ChangeSorting;
