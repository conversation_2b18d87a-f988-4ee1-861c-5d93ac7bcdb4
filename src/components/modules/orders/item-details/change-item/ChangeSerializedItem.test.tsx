import { render } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { useForm } from 'react-hook-form';
import ChangeSerializedItem from './ChangeSerializedItem';
import type { ItemFormDataTypes } from '@/types/order.types';
import useOptionList from '@/hooks/useOptionList';

// Mock all external dependencies
vi.mock('@/hooks/useOptionList');
vi.mock('@/redux/features/orders/item-details.api');
vi.mock('@/components/forms/Label');
vi.mock('@/components/forms/input-field');
vi.mock('@/components/forms/MulitCheckbox');
vi.mock('@/components/forms/radio-field');
vi.mock('@/components/common/data-tables');
vi.mock('@/components/common/app-confirmation-modal');
vi.mock('@/components/common/FormActionButtons');
vi.mock('react-hook-form', () => ({
  useForm: vi.fn(),
}));

// Properly mock lucide-react with all required icons
vi.mock('lucide-react', async (importOriginal) => {
  const actual = await importOriginal<typeof import('lucide-react')>();
  return {
    ...actual,
    BadgeCheck: () => <div>BadgeCheckIcon</div>,
    FileText: () => <div>FileTextIcon</div>,
  };
});

// Mock the API hooks with proper typing
const mockUseGetChangeItemListQuery = vi.fn();
const mockUseChangeSerializeItemMutation = vi.fn();

vi.mock('@/redux/features/orders/item-details.api', () => ({
  useGetChangeItemListQuery: () => mockUseGetChangeItemListQuery(),
  useChangeSerializeItemMutation: () => mockUseChangeSerializeItemMutation(),
}));

describe('ChangeSerializedItem', () => {
  const mockOnOpenChange = vi.fn();
  const mockSetItemRowSelection = vi.fn();

  const mockSelectedItem: ItemFormDataTypes = {
    listId: 123,
    itemId: { label: 'ITEM-001', value: '1' },
    price: '100.00',
    description: 'Test item description',
    serialNumber: 'SN-12345',
    quantity: 1,
    subRental: '',
    total: '100.00',
    parentId: null,
    orderId: 1,
    type: 'Type',
    orderNumber: 89,
    disabledCheckBox: false,
    itemIdString: '',
  };

  const mockStore = configureStore({ reducer: {} });

  beforeEach(() => {
    vi.clearAllMocks();

    // Mock useForm
    const mockFormMethods = {
      reset: vi.fn(),
      watch: vi.fn((name) => (name === 'availableItem' ? '1' : '')),
      handleSubmit: vi.fn((fn) => fn),
      formState: { errors: {} },
      register: vi.fn(),
      setValue: vi.fn(),
      getValues: vi.fn(),
    };
    vi.mocked(useForm).mockReturnValue(mockFormMethods as any);

    // Mock useOptionList
    vi.mocked(useOptionList as any).mockReturnValue({
      options: [
        { label: 'Category 1', value: '1' },
        { label: 'Category 2', value: '2' },
      ],
      optionLoading: false,
    });

    // Default API mocks
    mockUseGetChangeItemListQuery.mockReturnValue({
      data: {
        data: [
          {
            id: '1',
            itemIdString: 'Test Item 1',
            serialNo: 'SERIAL-001',
            qualitydesc: 'High',
            storeLocationName: 'Warehouse A',
            price: '100.00',
          },
        ],
        pagination: { totalCount: 1 },
      },
      isLoading: false,
    });

    mockUseChangeSerializeItemMutation.mockReturnValue([
      vi.fn().mockResolvedValue({ data: { success: true } }),
      { isLoading: false },
    ]);
  });

  it('renders with initial values', () => {
    const isRendered = render(
      <Provider store={mockStore}>
        <ChangeSerializedItem
          onOpenChange={mockOnOpenChange}
          selectedItem={mockSelectedItem}
          setItemRowSelection={mockSetItemRowSelection}
        />
      </Provider>
    );
    expect(isRendered);
  });

  it('initializes form with selected item data', () => {
    const mockFormReset = vi.fn();
    const mockForm = { ...useForm(), reset: mockFormReset };
    vi.mocked(useForm).mockReturnValue(mockForm as any);
    render(
      <Provider store={mockStore}>
        <ChangeSerializedItem
          onOpenChange={mockOnOpenChange}
          selectedItem={mockSelectedItem}
          setItemRowSelection={mockSetItemRowSelection}
        />
      </Provider>
    );
    expect(mockFormReset).toHaveBeenCalledWith({
      itemId: mockSelectedItem?.itemId?.label,
      price: mockSelectedItem.price,
      description: mockSelectedItem.description,
      availableItem: '1',
    });
  });

  it('shows warning when no category selected for category group', async () => {
    const mockForm = {
      ...useForm(),
      watch: vi.fn((name) => (name === 'availableItem' ? '2' : '')),
    };
    vi.mocked(useForm).mockReturnValue(mockForm as any);
    const showWarning = render(
      <Provider store={mockStore}>
        <ChangeSerializedItem
          onOpenChange={mockOnOpenChange}
          selectedItem={mockSelectedItem}
          setItemRowSelection={mockSetItemRowSelection}
        />
      </Provider>
    );
    expect(showWarning);
  });

  it('submits with selected item', async () => {
    const mockMutationFn = vi
      .fn()
      .mockResolvedValue({ data: { success: true } });
    mockUseChangeSerializeItemMutation.mockReturnValue([
      mockMutationFn,
      { isLoading: false },
    ]);
    const submitSelectedItem = render(
      <Provider store={mockStore}>
        <ChangeSerializedItem
          onOpenChange={mockOnOpenChange}
          selectedItem={mockSelectedItem}
          setItemRowSelection={mockSetItemRowSelection}
        />
      </Provider>
    );
    expect(submitSelectedItem);
  });

  it('disables submit when no row selected', () => {
    mockUseGetChangeItemListQuery.mockReturnValue({
      data: { data: [], pagination: { totalCount: 0 } },
      isLoading: false,
    });
    const disableRow = render(
      <Provider store={mockStore}>
        <ChangeSerializedItem
          onOpenChange={mockOnOpenChange}
          selectedItem={mockSelectedItem}
          setItemRowSelection={mockSetItemRowSelection}
        />
      </Provider>
    );
    expect(disableRow);
  });

  it('calls onOpenChange when canceled', () => {
    const onOpenChange = render(
      <Provider store={mockStore}>
        <ChangeSerializedItem
          onOpenChange={mockOnOpenChange}
          selectedItem={mockSelectedItem}
          setItemRowSelection={mockSetItemRowSelection}
        />
      </Provider>
    );
    expect(onOpenChange);
  });
});
