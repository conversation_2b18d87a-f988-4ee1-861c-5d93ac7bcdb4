import CustomDialog from '@/components/common/dialog';
import { cn } from '@/lib/utils';
import { OpenDialogType } from '../item-list';
import ChangeSerializedItem from './ChangeSerializedItem';
import { ItemFormDataTypes } from '@/types/order.types';

interface ChangeItemProps {
  open: OpenDialogType;
  onOpenChange: () => void;
  selectedItem?: ItemFormDataTypes;
  setRowSelection: React.Dispatch<
    React.SetStateAction<Record<string, boolean>>
  >;
}

const ChangeItem = ({
  open,
  onOpenChange,
  selectedItem,
  setRowSelection,
}: ChangeItemProps) => {
  return (
    <CustomDialog
      onOpenChange={onOpenChange}
      description=""
      open={open.state}
      className={cn('max-w-[90%] md:w-[80%] 2xl:w-[60%]')}
      contentClassName={cn(
        'h-[530px] 2xl:h-[700px] overflow-y-auto p-0 border-none'
      )}
      title={'Change Item'}
    >
      <ChangeSerializedItem
        onOpenChange={onOpenChange}
        selectedItem={selectedItem}
        setItemRowSelection={setRowSelection}
      />
    </CustomDialog>
  );
};

export default ChangeItem;
