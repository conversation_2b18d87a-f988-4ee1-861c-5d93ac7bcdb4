import { render } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import ChangeItem from './index';
import CustomDialog from '@/components/common/dialog';
import ChangeSerializedItem from './ChangeSerializedItem';

// Mock child components
vi.mock('@/components/common/dialog', () => ({
  default: vi.fn(({ children }) => <div>{children}</div>),
}));

vi.mock('./ChangeSerializedItem', () => ({
  default: vi.fn(() => <div>ChangeSerializedItem Mock</div>),
}));

describe('ChangeItem Component', () => {
  const mockProps = {
    open: { state: true, action: '' },
    onOpenChange: vi.fn(),
    selectedItem: {
      id: '1',
      name: 'Test Item',
      serialNumber: '12345',
    } as any,
    setRowSelection: vi.fn(),
  };

  it('renders the CustomDialog with correct props', () => {
    render(
      <ChangeItem
        onOpenChange={mockProps.onOpenChange}
        open={mockProps.open}
        setRowSelection={mockProps.setRowSelection}
        selectedItem={mockProps.selectedItem}
      />
    );
    expect(CustomDialog).toHaveBeenCalledWith(
      expect.objectContaining({
        open: true,
        title: 'Change Item',
        description: '',
        className: expect.stringContaining(
          'max-w-[90%] md:w-[80%] 2xl:w-[60%]'
        ),
        contentClassName: expect.stringContaining('h-[530px] 2xl:h-[700px]'),
        onOpenChange: mockProps.onOpenChange,
      }),
      expect.anything()
    );
  });

  it('passes the correct props to ChangeSerializedItem', () => {
    render(<ChangeItem {...mockProps} />);
    expect(ChangeSerializedItem).toHaveBeenCalledWith(
      {
        onOpenChange: mockProps.onOpenChange,
        selectedItem: mockProps.selectedItem,
        setItemRowSelection: mockProps.setRowSelection,
      },
      expect.anything()
    );
  });

  it('does not render when dialog is not open', () => {
    render(
      <ChangeItem
        onOpenChange={mockProps.onOpenChange}
        open={mockProps.open}
        setRowSelection={mockProps.setRowSelection}
        selectedItem={mockProps.selectedItem}
      />
    );
    expect(CustomDialog);
  });

  it('renders with empty selectedItem', () => {
    render(
      <ChangeItem
        onOpenChange={mockProps.onOpenChange}
        open={mockProps.open}
        setRowSelection={mockProps.setRowSelection}
        selectedItem={mockProps.selectedItem}
      />
    );
    expect(ChangeSerializedItem);
  });
});
