import { use<PERSON>allback, useEffect, useMemo, useState } from 'react';
import { SubmitHand<PERSON>, useForm } from 'react-hook-form';
import { BadgeCheck } from 'lucide-react';

import { Card, CardContent } from '@/components/ui/card';
import Labels from '@/components/forms/Label';
import InputField from '@/components/forms/input-field';
import MultiCheckboxDropdown from '@/components/forms/MulitCheckbox';
import RadioField from '@/components/forms/radio-field';
import DataTable from '@/components/common/data-tables';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import FormActionButtons from '@/components/common/FormActionButtons';

import useOptionList from '@/hooks/useOptionList';
import {
  useChangeSerializeItemMutation,
  useGetChangeItemListQuery,
} from '@/redux/features/orders/item-details.api';

import { CATEGORY_API_ROUTES } from '@/constants/api-constants';
import { availableInventoryItems } from '@/constants/order-constants';
import { ItemFormDataTypes } from '@/types/order.types';

interface ChangeSerializedItemProps {
  onOpenChange: () => void;
  selectedItem?: ItemFormDataTypes;
  setItemRowSelection: React.Dispatch<
    React.SetStateAction<Record<string, boolean>>
  >;
}

interface ChangeSerializedItemFormDataTypes {
  availableItem: string;
  itemId: string;
  price: string | number;
  description: string;
}

const ChangeSerializedItem = ({
  onOpenChange,
  selectedItem,
  setItemRowSelection,
}: ChangeSerializedItemProps) => {
  const form = useForm<ChangeSerializedItemFormDataTypes>();
  const categoryForm = useForm();
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [showWarningModal, setShowWarningModal] = useState<boolean>(false);
  const [rowSelection, setRowSelection] = useState<Record<string, boolean>>({});

  const defaultValues = useMemo(
    () => ({
      itemId: selectedItem?.itemId?.label,
      price: selectedItem?.price,
      description: selectedItem?.description,
      availableItem: '1',
    }),
    [selectedItem]
  );

  const selectedRadioValue = form.watch('availableItem');
  const selectedInventoryOption = availableInventoryItems.find(
    (item) => item.value === selectedRadioValue
  );

  const handleCloseWarningModal = () => setShowWarningModal(false);

  const shouldSkipQuery = useMemo(() => {
    const isCategoryGroup = selectedInventoryOption?.group === 'CATEGORY';

    if (
      !selectedItem?.listId ||
      !selectedInventoryOption?.group ||
      !selectedInventoryOption?.key
    ) {
      return true;
    }

    if (isCategoryGroup && selectedCategories.length === 0) {
      setShowWarningModal(true);
      return true;
    }

    return false;
  }, [selectedItem?.listId, selectedInventoryOption, selectedCategories]);

  const [changeSerializedItem, { isLoading: isPosting }] =
    useChangeSerializeItemMutation();

  const { data, isLoading } = useGetChangeItemListQuery(
    {
      body: {
        orderItemId: selectedItem?.listId as number,
        filterBy: selectedInventoryOption?.group,
        filter: { [selectedInventoryOption?.key as string]: true },
        categoryIds: selectedCategories,
      },
    },
    { skip: shouldSkipQuery }
  );
  const { options: categoryList, optionLoading } = useOptionList({
    url: CATEGORY_API_ROUTES.ALL,
    labelKey: 'catDesc',
    valueKey: 'id',
    sortBy: 'catDesc',
  });

  const tableColumns = useMemo(
    () => [
      { accessorKey: 'itemIdString', header: 'Item Description' },
      { accessorKey: 'serialNo', header: 'Serial #' },
      { accessorKey: 'qualitydesc', header: 'Quality' },
      { accessorKey: 'storeLocationName', header: 'Location' },
      { accessorKey: 'price', header: 'Price' },
    ],
    []
  );

  useEffect(() => {
    if (selectedItem) {
      form.reset(defaultValues);
    }
  }, [form, selectedItem, defaultValues]);

  const handleCancel = useCallback(() => {
    categoryForm.reset();
    onOpenChange();
  }, [categoryForm, onOpenChange]);

  const handleSubmit: SubmitHandler<ChangeSerializedItemFormDataTypes> =
    useCallback(async () => {
      const response = await changeSerializedItem({
        orderItemId: selectedItem?.listId as number,
        inventoryId: Object.keys(rowSelection)[0],
      });
      if (response?.data?.success) {
        onOpenChange();
        setItemRowSelection({});
      }
    }, [
      changeSerializedItem,
      onOpenChange,
      rowSelection,
      selectedItem?.listId,
      setItemRowSelection,
    ]);

  const CustomCategoryToolbar = (
    <MultiCheckboxDropdown
      name="category"
      form={categoryForm}
      optionsList={categoryList ?? []}
      placeholder="Select Categories"
      onChange={setSelectedCategories}
      isLoading={optionLoading}
    />
  );

  return (
    <>
      {/* Form Card Section */}
      <Card className="mx-4">
        <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-x-8 px-4 py-1 relative">
          {/* Left Column - Item Info */}
          <div className="col-span-1 grid grid-cols-2 items-center gap-x-2">
            <InputField form={form} label="Item ID" name="itemId" disabled />
            <InputField form={form} label="Price" name="price" disabled />
            <InputField
              form={form}
              label="Description"
              name="description"
              disabled
              pClassName="col-span-2"
            />
          </div>

          {/* Right Column - Inventory Options */}
          <div className="col-span-2 w-full">
            <div className="flex flex-col gap-4 w-full">
              <div className="grid grid-cols-2 gap-8 w-full">
                <Labels label="Available inventory for Item" />
                <Labels label="Available inventory for Category" />
              </div>
              <RadioField
                form={form}
                name="availableItem"
                options={availableInventoryItems}
                optionsPerRow={2}
                rowClassName="grid grid-cols-2 w-full"
                className="space-y-1"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Data Table */}
      <div className="w-full relative">
        <div className="px-5 py-2">
          <DataTable
            data={data?.data ?? []}
            columns={tableColumns}
            isLoading={isLoading}
            enableRowSelection
            rowSelection={rowSelection}
            onRowSelectionChange={setRowSelection}
            totalItems={data?.pagination?.totalCount}
            customToolBar={CustomCategoryToolbar}
            enablePagination={false}
            tableClassName="max-h-[210px] 2xl:max-h-[370px] overflow-auto"
            loaderRows={6}
            bindingKey="id"
          />
        </div>

        {/* Action Buttons */}
        <FormActionButtons
          className="fixed bottom-0 left-[60%] right-0 bg-white px-4 py-1 pt-0"
          onSubmit={form.handleSubmit(handleSubmit)}
          submitLabel="Ok"
          onCancel={handleCancel}
          isLoading={isPosting}
          cancelLabel="Cancel"
          submitIcon={BadgeCheck}
          disabledSubmitButton={!Object.keys(rowSelection)?.length}
        />
      </div>

      {/* Warning Modal */}
      <AppConfirmationModal
        title="Warning"
        open={showWarningModal}
        description={<div>Please select a category</div>}
        handleSubmit={handleCloseWarningModal}
        submitLabel="Ok"
      />
    </>
  );
};

export default ChangeSerializedItem;
