import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import CustomerLook from './CustomerLook';
import DataTable from '@/components/common/data-tables';
import { useEffect } from 'react';

// Mock the dependencies
vi.mock('@/components/common/data-tables', () => ({
  default: vi.fn(() => <div>DataTable Mock</div>),
}));

vi.mock('@/components/common/FormActionButtons', () => ({
  default: vi.fn(() => <div>FormActionButtons Mock</div>),
}));

vi.mock('@/components/forms/input-field', () => ({
  default: vi.fn(() => <div>InputField Mock</div>),
}));

vi.mock('@/components/forms/phone-input-mask', () => ({
  default: vi.fn(() => <div>PhoneInputWidget Mock</div>),
}));

vi.mock('@/components/forms/radio-field', () => ({
  default: vi.fn(() => <div>RadioField Mock</div>),
}));

vi.mock('@/components/forms/select', () => ({
  default: vi.fn(() => <div>SelectWidget Mock</div>),
}));

vi.mock('@/redux/features/sub-rental/subRental.api', () => ({
  useCustomerVendorSearchQuery: vi.fn(() => ({
    data: {
      data: [],
      pagination: { totalCount: 0 },
    },
    isFetching: false,
  })),
}));

vi.mock('lodash/debounce', () => ({
  default: vi.fn((fn) => {
    const debouncedFn = vi.fn(fn) as any;
    debouncedFn.cancel = vi.fn();
    return debouncedFn;
  }),
}));

describe('CustomerLook Component', () => {
  const mockHandleOk = vi.fn();
  const mockSetActiveTab = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders without crashing', () => {
    render(
      <CustomerLook handleOk={mockHandleOk} setActiveTab={mockSetActiveTab} />
    );
    expect(screen.getByText('FormActionButtons Mock')).toBeInTheDocument();
    expect(screen.getByText('DataTable Mock')).toBeInTheDocument();
  });

  it('renders all filter controls', () => {
    render(
      <CustomerLook handleOk={mockHandleOk} setActiveTab={mockSetActiveTab} />
    );
    expect(screen.getAllByText('SelectWidget Mock').length).toBe(2);
    expect(screen.getByText('RadioField Mock')).toBeInTheDocument();
  });

  it('switches between name and phone input based on searchType', async () => {
    render(
      <CustomerLook handleOk={mockHandleOk} setActiveTab={mockSetActiveTab} />
    );
    expect(screen.getByText('InputField Mock')).toBeInTheDocument();
  });

  it('calls handleOk with selected customer when OK is clicked', async () => {
    vi.mocked(DataTable).mockImplementation(({ onRowsSelected }) => {
      useEffect(() => {
        onRowsSelected?.([
          {
            fullName: 'Test Customer',
            phone: '1234567890',
            city: 'Test City',
            state: 'TS',
          },
        ]);
      }, [onRowsSelected]);
      return <div>DataTable Mock</div>;
    });

    render(
      <CustomerLook handleOk={mockHandleOk} setActiveTab={mockSetActiveTab} />
    );
    fireEvent.click(screen.getByText('FormActionButtons Mock'));
    await waitFor(() => {
      expect(mockHandleOk);
    });
  });

  it('calls setActiveTab when cancel is clicked', () => {
    render(
      <CustomerLook handleOk={mockHandleOk} setActiveTab={mockSetActiveTab} />
    );
    fireEvent.click(screen.getByText('FormActionButtons Mock'));
    expect(mockSetActiveTab);
  });

  it('applies filters when form values change', async () => {
    const applyFilters = render(
      <CustomerLook handleOk={mockHandleOk} setActiveTab={mockSetActiveTab} />
    );
    expect(applyFilters);
  });
});
