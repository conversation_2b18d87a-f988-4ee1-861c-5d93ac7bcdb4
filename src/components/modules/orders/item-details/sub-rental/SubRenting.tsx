import { ColumnDef, RowSelectionState } from '@tanstack/react-table';
import { useCallback, useMemo, useState } from 'react';
import { Submit<PERSON>and<PERSON>, useFormContext } from 'react-hook-form';

import AppButton from '@/components/common/app-button';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import DataTable from '@/components/common/data-tables';
import ActionColumnMenu from '@/components/common/data-tables/ActionColumn';
import InputField from '@/components/forms/input-field';
import NumberInputField from '@/components/forms/number-input-field';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

import { useDeleteSubRentItemMutation } from '@/redux/features/orders/item-details.api';

import { TEXT_VALIDATION_RULE } from '@/constants/validation-constants';
import { DEFAULT_FORMAT, formatDate, getQueryParam } from '@/lib/utils';
import { SortingStateType } from '@/types/common.types';
import {
  OrderSubRentsTypes,
  SubRentItemListTypes,
} from '@/types/orders/order-item-details.types';
import { FilePlus2, KeyRound } from 'lucide-react';

type SubRentingProps = {
  orderItemId: number;
  isLoading: boolean;
  sorting: SortingStateType[];
  setSorting: React.Dispatch<React.SetStateAction<SortingStateType[]>>;
  orderSubRents?: OrderSubRentsTypes[];
  toggleNewSubRental: () => Promise<void>;
  handleUpdate: SubmitHandler<SubRentItemListTypes>;
  setSelectedRows: React.Dispatch<React.SetStateAction<OrderSubRentsTypes[]>>;
  isUpdating: boolean;
  rowSelection: RowSelectionState;
  setRowSelection: React.Dispatch<React.SetStateAction<RowSelectionState>>;
};

interface DeleteDialogState {
  isOpen: boolean;
  id: number | null;
}

const SubRenting = ({
  orderItemId,
  isLoading,
  sorting,
  setSorting,
  orderSubRents,
  toggleNewSubRental,
  handleUpdate,
  isUpdating,
  setSelectedRows,
  rowSelection,
  setRowSelection,
}: SubRentingProps) => {
  const orderId = getQueryParam('id') as string;

  const form = useFormContext<SubRentItemListTypes>();

  const [deleteDialogState, setDeleteDialogState] = useState<DeleteDialogState>(
    { isOpen: false, id: null }
  );

  const [deleteItem, { isLoading: isDeleteLoading }] =
    useDeleteSubRentItemMutation();

  const toggleDelete = useCallback((id: number | null) => {
    setDeleteDialogState((prev) => ({
      isOpen: !prev.isOpen,
      id,
    }));
  }, []);

  const handleDeleteItem = async () => {
    if (deleteDialogState.id) {
      await deleteItem({
        orderId,
        orderItemId: orderItemId,
      });
    }
    setDeleteDialogState({ isOpen: false, id: null });
  };

  const columns: ColumnDef<OrderSubRentsTypes>[] = useMemo(
    () => [
      { accessorKey: 'id', header: 'SR#', size: 50, enableSorting: true },
      { accessorKey: 'updatedBy', header: 'SP', size: 50, enableSorting: true },
      {
        accessorKey: 'resvNo',
        header: 'Reservation #',
        size: 100,
        enableSorting: true,
      },
      {
        accessorKey: 'name',
        header: 'Equipment Form',
        size: 250,
        enableSorting: true,
      },
      {
        accessorKey: 'pickupDate',
        header: 'Pickup Date',
        size: 100,
        enableSorting: true,
        cell: ({ row }: any) =>
          formatDate(row?.original?.pickupDate, DEFAULT_FORMAT),
      },
      {
        accessorKey: 'returnDate',
        header: 'Return Date',
        size: 100,
        enableSorting: true,
        cell: ({ row }: any) => (
          <div className="flex items-center justify-between">
            <span>{formatDate(row?.original?.returnDate, DEFAULT_FORMAT)}</span>
            <ActionColumnMenu
              onDelete={() => toggleDelete(row?.original?.id)}
              contentClassName="z-[99] w-fit"
            />
          </div>
        ),
      },
    ],
    [toggleDelete]
  );

  return (
    <div>
      <Card className="border rounded-md shadow-sm h-fit">
        <CardHeader className="py-2">
          <CardTitle className="text-xl 2xl:text-2xl font-semibold text-text-Default">
            Item Lookup
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-2">
            <InputField
              name="itemName"
              form={form}
              label="Item ID"
              placeholder="Enter Item ID"
              disabled
            />
            <NumberInputField
              name="quantity"
              form={form}
              label="Quantity (Adds to current total)"
              placeholder="______"
              maxLength={5}
              validation={TEXT_VALIDATION_RULE}
            />
            <InputField
              name="currentSubRentedQty"
              form={form}
              label="Current Sub-Rented Qty"
              placeholder="______"
              disabled
            />
            <InputField
              name="itemDescription"
              form={form}
              label="Item Description"
              placeholder="Enter Description"
            />
            <NumberInputField
              name="unitPrice"
              form={form}
              label="Cost (Replaces current cost)"
              placeholder="$______.__"
              prefix="$"
              maxLength={10}
              fixedDecimalScale
              thousandSeparator=","
              validation={TEXT_VALIDATION_RULE}
            />
            <InputField
              name="currentSubRentedCost"
              form={form}
              label="Current Sub-Rented Cost"
              placeholder="$______.__"
              disabled
            />
          </div>
        </CardContent>
      </Card>

      <div className="flex justify-end gap-4 mt-3 bg-white px-4 mb-2">
        <AppButton
          type="button"
          label="New Sub Rental"
          className="min-w-28"
          icon={KeyRound}
          iconClassName="w-4"
          onClick={form.handleSubmit(() => {
            // Only proceed if form is valid
            toggleNewSubRental();
          })}
        />
        <AppButton
          type="button"
          label="Add to Selected"
          variant="neutral"
          className="min-w-30"
          icon={FilePlus2}
          iconClassName="w-4"
          onClick={form.handleSubmit(handleUpdate)}
          isLoading={isUpdating}
          disabled={!Object.keys(rowSelection)?.length}
        />
      </div>

      <DataTable
        data={orderSubRents ?? []}
        isLoading={isLoading}
        columns={columns}
        sorting={sorting}
        setSorting={setSorting}
        enableRowSelection
        rowSelection={rowSelection}
        bindingKey="id"
        onRowSelectionChange={setRowSelection}
        onRowsSelected={setSelectedRows}
        tableClassName="max-h-[200px] 2xl:max-h-[300px] overflow-auto min-w-[80%] md:min-w-[60%] 2xl:md:min-w-[70%]"
        enablePagination={false}
        totalItems={orderSubRents?.length}
      />

      <AppConfirmationModal
        description={<div>Are you sure you want to delete this record?</div>}
        open={deleteDialogState.isOpen}
        handleCancel={() => setDeleteDialogState({ isOpen: false, id: null })}
        handleSubmit={handleDeleteItem}
        disabled={isDeleteLoading}
        isLoading={isDeleteLoading}
      />
    </div>
  );
};

export default SubRenting;
