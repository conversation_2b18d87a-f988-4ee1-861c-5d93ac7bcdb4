import BreadcrumbDialogRenderer from '@/components/common/BreadcrumbDialogRenderer';
import { getQueryParam } from '@/lib/utils';
import {
  useAddSubRentMutation,
  useSubRentItemQuery,
} from '@/redux/features/orders/item-details.api';
import { SortingStateType } from '@/types/common.types';
import { SubRentingTabEnum } from '@/types/order.types';
import {
  CustomerVendorLookupTypes,
  OrderSubRentsTypes,
  SubRentItemListTypes,
  SubRentPayload,
} from '@/types/orders/order-item-details.types';
import { RowSelectionState } from '@tanstack/react-table';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { FormProvider, SubmitHandler, useForm } from 'react-hook-form';
import CustomerLook from './CustomerLook';
import ItemNewSubRent from './ItemNewSubRent';
import SubRenting from './SubRenting';
const mapItemsToFormValues = (items: SubRentItemListTypes) => ({
  itemName: items?.itemName ?? '',
  quantity: items?.quantity ?? '',
  currentSubRentedQty: items?.currentSubRentedQty ?? '',
  itemDescription: items?.itemDescription ?? '',
  currentSubRentedCost: items?.currentSubRentedCost ?? '',
  itemId: items?.itemId ?? '',
  unitPrice: items?.unitPrice || '',
});

const SubRental = ({ open, onOpenChange, orderItemId }: any) => {
  const orderId = getQueryParam('id') as string;
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});

  const [activeTab, setActiveTab] = useState('sub-renting');
  const [selectedRows, setSelectedRows] = useState<OrderSubRentsTypes[]>([]);
  const [customerData, setCustomerData] =
    useState<CustomerVendorLookupTypes | null>(null);
  const form = useForm<SubRentItemListTypes>();
  // update sub rent item
  const [updateSubRent, { isLoading: isUpdating }] = useAddSubRentMutation();

  const [sorting, setSorting] = useState<SortingStateType[]>([
    { id: 'id', desc: true },
  ]);

  const { data, isLoading } = useSubRentItemQuery(
    {
      itemId: orderItemId?.toString(),
      orderId,
      body: { sortBy: sorting[0].id, sortAscending: sorting[0].desc },
    },
    { skip: !orderItemId }
  );

  useEffect(() => {
    if (data?.data) form.reset(mapItemsToFormValues(data?.data));
  }, [data?.data, form]);

  const handleOk = (value: CustomerVendorLookupTypes) => {
    setActiveTab(SubRentingTabEnum.NEW_SUB_RENT);
    setCustomerData(value);
  };

  const toggleNewSubRental = useCallback(async () => {
    setActiveTab(SubRentingTabEnum.NEW_SUB_RENT);
  }, [setActiveTab]);

  const handleUpdate: SubmitHandler<SubRentItemListTypes> = useCallback(
    async (formData) => {
      const {
        id,
        specInst1,
        specInst2,
        contactTypeValue,
        custContact,
        resvNo,
        pickupDate,
        returnDate,
        customerId,
      } = selectedRows[0];
      const { itemDescription, itemId, unitPrice, quantity } = formData;

      const payload: SubRentPayload = {
        id: id ?? 0,
        orderId: Number(orderId),
        pickupDate,
        returnDate,
        rentedFromId: customerId,
        contactType: contactTypeValue,
        resvNo,
        custContact,
        specInst1,
        specInst2,
        OrderSubrentItems: [
          {
            orderSubrentId: id ?? 0,
            orderItemId: Number(orderItemId),
            itemId,
            itemDescription,
            quantity: Number(quantity) ?? 0,
            unitPrice: unitPrice ? Number(unitPrice) : 0,
            rowNo: 0,
          },
        ],
      };

      const response = await updateSubRent({ body: payload, orderItemId });

      if (response?.data?.success) {
        onOpenChange();
      }
    },
    [onOpenChange, orderId, orderItemId, selectedRows, updateSubRent]
  );

  // Sample tab content for the dialog
  const dialogTabs = useMemo(
    () => [
      {
        label: 'Sub-Renting',
        value: SubRentingTabEnum.SUB_RENTING,
        content: (
          <SubRenting
            orderItemId={orderItemId}
            isLoading={isLoading}
            orderSubRents={data?.data?.orderSubrents}
            sorting={sorting}
            setSorting={setSorting}
            toggleNewSubRental={toggleNewSubRental}
            handleUpdate={handleUpdate}
            isUpdating={isUpdating}
            setSelectedRows={setSelectedRows}
            rowSelection={rowSelection}
            setRowSelection={setRowSelection}
          />
        ),
      },
      {
        label: 'New Sub Rental',
        value: SubRentingTabEnum.NEW_SUB_RENT,
        content: (
          <ItemNewSubRent
            customerData={customerData}
            setActiveTab={setActiveTab}
            orderItemId={orderItemId}
          />
        ),
      },
      {
        label: 'Customers/Vendors Search',
        value: SubRentingTabEnum.CUSTOMER_LOOKUP,
        content: (
          <CustomerLook handleOk={handleOk} setActiveTab={setActiveTab} />
        ),
      },
    ],
    [
      orderItemId,
      isLoading,
      data?.data?.orderSubrents,
      sorting,
      toggleNewSubRental,
      handleUpdate,
      isUpdating,
      rowSelection,
      customerData,
    ]
  );

  useEffect(() => {
    if (customerData) {
      setCustomerData(null);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeTab]);

  return (
    <FormProvider {...form}>
      <BreadcrumbDialogRenderer
        listItems={dialogTabs}
        activeTab={activeTab}
        isOpen={open}
        onOpenChange={onOpenChange}
        setActiveTab={setActiveTab}
        className="max-w-[95%] 2xl:max-w-[65%]"
        contentClassName="h-[550px] 2xl:h-[700px]"
      />
    </FormProvider>
  );
};

export default SubRental;
