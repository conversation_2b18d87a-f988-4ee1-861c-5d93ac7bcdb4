import AppButton from '@/components/common/app-button';
import FormActionButtons from '@/components/common/FormActionButtons';
import DatePicker from '@/components/forms/date-picker';
import InputField from '@/components/forms/input-field';
import NumberInputField from '@/components/forms/number-input-field';
import { TEXT_VALIDATION_RULE } from '@/constants/validation-constants';
import { DATE_FORMAT_YYYYMMDD, formatDate, getQueryParam } from '@/lib/utils';
import {
  useAddSubRentMutation,
  useSubRentDetailQuery,
} from '@/redux/features/orders/item-details.api';
import { SubRentingTabEnum } from '@/types/order.types';
import {
  CustomerVendorLookupTypes,
  NewSubRent,
  SubRentItemListTypes,
  SubRentPayload,
} from '@/types/orders/order-item-details.types';
import { SearchIcon } from 'lucide-react';
import { useCallback, useEffect } from 'react';
import { SubmitHand<PERSON>, useForm, useFormContext } from 'react-hook-form';

type ItemNewSubRentProps = {
  setActiveTab: (tab: SubRentingTabEnum) => void;
  customerData: CustomerVendorLookupTypes | null;
  orderItemId: number;
};

export const mapItemsToFormValues = (items: any) => ({
  pickupDate: items?.pickupDate ?? '',
  returnDate: items?.returnDate ?? '',
  orderId: items.orderId,
});

const ItemNewSubRent = ({
  setActiveTab,
  customerData,
  orderItemId,
}: ItemNewSubRentProps) => {
  const orderId = getQueryParam('id') as string;
  const form = useForm<NewSubRent>();
  const subRentform = useFormContext<SubRentItemListTypes>();
  const { data } = useSubRentDetailQuery({ orderId });
  const [addSubRent, { isLoading }] = useAddSubRentMutation();

  useEffect(() => {
    if (data?.data) {
      form.reset(mapItemsToFormValues(data?.data));
    }
  }, [data?.data, form]);

  const toggleNewSubRental = useCallback(() => {
    setActiveTab(SubRentingTabEnum.CUSTOMER_LOOKUP);
  }, [setActiveTab]);

  useEffect(() => {
    if (customerData) {
      form.setValue('customerId', customerData?.id);
      form.setValue('rentedFrom', customerData?.fullName);
      form.setValue('contactTypeValue', customerData?.contactType);
    }
  }, [customerData, form]);

  const onSubmit: SubmitHandler<NewSubRent> = async (formData) => {
    const {
      pickupDate,
      returnDate,
      custContact,
      resvNo,
      specInst1,
      specInst2,
    } = formData;
    const payload: SubRentPayload = {
      orderId: Number(orderId),
      pickupDate: formatDate(pickupDate, DATE_FORMAT_YYYYMMDD),
      returnDate: formatDate(returnDate, DATE_FORMAT_YYYYMMDD),
      custContact,
      resvNo,
      specInst1,
      specInst2,
      contactType: formData?.contactTypeValue,
      rentedFromId: formData?.customerId,
      OrderSubrentItems: [
        {
          itemId: subRentform?.watch('itemId'),
          itemDescription: subRentform?.watch('itemDescription'),
          orderItemId: Number(orderItemId),
          orderSubrentId: 0,
          quantity: Number(subRentform?.watch('quantity')) || 0,
          unitPrice: Number(subRentform?.watch('unitPrice')) || 0,
          rowNo: 0,
        },
      ],
    };
    const response = await addSubRent({ body: payload, orderItemId });
    if (response?.data?.success) {
      setActiveTab(SubRentingTabEnum.SUB_RENTING);
    }
  };
  return (
    <div className="relative flex flex-col gap-2 h-full">
      <div className="grid grid-cols-3 gap-4 ">
        <NumberInputField
          name="subRental"
          form={form}
          label="Sub Rental #"
          placeholder="______"
          disabled
        />
        <InputField
          name="rentedFrom"
          form={form}
          label="Rented From"
          placeholder="Enter Rented From"
          disabled
          validation={TEXT_VALIDATION_RULE}
        />
        <AppButton
          type="button"
          label="Customer"
          variant="neutral"
          icon={SearchIcon}
          onClick={toggleNewSubRental}
          iconClassName="w-4"
          className="mt-8 w-1/2 cursor-pointer"
        />
        <InputField
          name="custContact"
          form={form}
          label="Contact"
          placeholder="Enter Contact"
        />
        <InputField
          name="resvNo"
          form={form}
          label="Reservation #"
          placeholder="Enter Reservation"
        />
        <NumberInputField
          name="orderId"
          form={form}
          label="PTW Order #"
          placeholder="Enter PTW Order"
        />
        <DatePicker
          form={form}
          name="pickupDate"
          label="Pickup Date"
          placeholder="Select Date"
          enableInput
        />
        <InputField
          name="specInst1"
          form={form}
          label="Info"
          placeholder="Enter Info"
        />
        <div></div>
        <DatePicker
          form={form}
          name="returnDate"
          label="Return Date"
          placeholder="Select Date"
          enableInput
        />
        <InputField
          name="specInst2"
          form={form}
          label="Info"
          placeholder="Enter Info"
        />
      </div>

      <FormActionButtons
        className="fixed bottom-0 left-[50%] right-0 bg-white p-4 "
        onSubmit={form.handleSubmit(onSubmit)}
        onCancel={() => setActiveTab(SubRentingTabEnum.SUB_RENTING)}
        isLoading={isLoading}
      />
    </div>
  );
};

export default ItemNewSubRent;
