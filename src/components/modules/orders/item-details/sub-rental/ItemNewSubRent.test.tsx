import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import ItemNewSubRent from './ItemNewSubRent';
import { useForm, useFormContext } from 'react-hook-form';
import { SubRentingTabEnum } from '@/types/order.types';
import {
  useAddSubRentMutation,
  useSubRentDetailQuery,
} from '@/redux/features/orders/item-details.api';

vi.mock('react-hook-form', () => ({
  useForm: vi.fn(),
  useFormContext: vi.fn(),
}));

vi.mock('@/components/common/app-button', () => ({
  default: ({ label, onClick }: { label: string; onClick: () => void }) => (
    <button onClick={onClick}>{label}</button>
  ),
}));

vi.mock('@/components/common/FormActionButtons', () => ({
  default: ({
    onSubmit,
    onCancel,
  }: {
    onSubmit: () => void;
    onCancel: () => void;
  }) => (
    <div>
      <button onClick={onSubmit}>Submit</button>
      <button onClick={onCancel}>Cancel</button>
    </div>
  ),
}));

vi.mock('@/components/forms/date-picker', () => ({
  default: ({ label }: { label: string }) => <div>{label}</div>,
}));

vi.mock('@/components/forms/input-field', () => ({
  default: ({ label, name }: { label: string; name: string }) => (
    <div>
      <label>{label}</label>
      <input name={name} />
    </div>
  ),
}));

vi.mock('@/components/forms/number-input-field', () => ({
  default: ({ label, name }: { label: string; name: string }) => (
    <div>
      <label>{label}</label>
      <input type="number" name={name} />
    </div>
  ),
}));

vi.mock('@/redux/features/orders/item-details.api', () => ({
  useAddSubRentMutation: vi.fn(() => [
    vi.fn(),
    { isLoading: false, data: { data: { success: true } } },
  ]),
  useSubRentDetailQuery: vi.fn(() => ({ data: { data: {} } })),
}));

vi.mock('@/lib/utils', () => ({
  getQueryParam: vi.fn(() => '123'),
  formatDate: vi.fn(() => '2023-01-01'),
  DATE_FORMAT_YYYYMMDD: 'yyyy-MM-dd',
}));

describe('ItemNewSubRent', () => {
  const mockSetActiveTab = vi.fn();
  const mockCustomerData = {
    id: 1,
    fullName: 'Test Customer',
    contactType: 'Email',
  } as any;
  const mockOrderItemId = 456;

  const mockForm = {
    watch: vi.fn(),
    setValue: vi.fn(),
    reset: vi.fn(),
    handleSubmit: vi.fn((callback) => () => callback({})),
    formState: { errors: {} },
  };

  const mockSubRentForm = {
    watch: vi.fn((field) => {
      const values: Record<string, any> = {
        itemId: 789,
        itemDescription: 'Test Item',
        quantity: 2,
        unitPrice: 100,
      };
      return values[field];
    }),
  };

  beforeEach(() => {
    vi.mocked(useForm).mockReturnValue(mockForm as any);
    vi.mocked(useFormContext).mockReturnValue(mockSubRentForm as any);
  });

  it('renders the component with all fields', () => {
    const isRender = render(
      <ItemNewSubRent
        setActiveTab={mockSetActiveTab}
        customerData={mockCustomerData}
        orderItemId={mockOrderItemId}
      />
    );
    expect(isRender);
  });

  it('sets customer data when provided', () => {
    render(
      <ItemNewSubRent
        setActiveTab={mockSetActiveTab}
        customerData={mockCustomerData}
        orderItemId={mockOrderItemId}
      />
    );
    expect(mockForm.setValue).toHaveBeenCalledWith(
      'customerId',
      mockCustomerData.id
    );
    expect(mockForm.setValue).toHaveBeenCalledWith(
      'rentedFrom',
      mockCustomerData.fullName
    );
    expect(mockForm.setValue).toHaveBeenCalledWith(
      'contactTypeValue',
      mockCustomerData.contactType
    );
  });

  it('handles customer lookup button click', () => {
    render(
      <ItemNewSubRent
        setActiveTab={mockSetActiveTab}
        customerData={mockCustomerData}
        orderItemId={mockOrderItemId}
      />
    );
    fireEvent.click(screen.getByText('Customer'));
    expect(mockSetActiveTab).toHaveBeenCalledWith(
      SubRentingTabEnum.CUSTOMER_LOOKUP
    );
  });

  it('submits the form with correct payload', async () => {
    const mockAddSubRent = vi
      .fn()
      .mockResolvedValue({ data: { success: true } });
    vi.mocked(useAddSubRentMutation as any).mockReturnValue([
      mockAddSubRent,
      { isLoading: false },
    ]);
    render(
      <ItemNewSubRent
        setActiveTab={mockSetActiveTab}
        customerData={mockCustomerData}
        orderItemId={mockOrderItemId}
      />
    );
    fireEvent.click(screen.getByText('Submit'));
    await waitFor(() => {
      expect(mockAddSubRent);
      expect(mockSetActiveTab);
    });
  });

  it('handles cancel button click', () => {
    render(
      <ItemNewSubRent
        setActiveTab={mockSetActiveTab}
        customerData={mockCustomerData}
        orderItemId={mockOrderItemId}
      />
    );
    fireEvent.click(screen.getByText('Cancel'));
    expect(mockSetActiveTab).toHaveBeenCalledWith(
      SubRentingTabEnum.SUB_RENTING
    );
  });

  it('resets form when data is available', () => {
    const mockData = {
      data: {
        pickupDate: '2023-01-01',
        returnDate: '2023-01-02',
        orderId: 123,
      },
    };
    vi.mocked(useSubRentDetailQuery as any).mockReturnValue({ data: mockData });
    render(
      <ItemNewSubRent
        setActiveTab={mockSetActiveTab}
        customerData={mockCustomerData}
        orderItemId={mockOrderItemId}
      />
    );
    expect(mockForm.reset).toHaveBeenCalledWith({
      pickupDate: mockData.data.pickupDate,
      returnDate: mockData.data.returnDate,
      orderId: mockData.data.orderId,
    });
  });
});
