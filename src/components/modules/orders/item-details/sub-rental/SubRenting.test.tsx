import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { useFormContext } from 'react-hook-form';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import SubRenting from './SubRenting';
import { useDeleteSubRentItemMutation } from '@/redux/features/orders/item-details.api';

// Mock dependencies
vi.mock('react-hook-form', () => ({
  useFormContext: vi.fn(),
}));

vi.mock('@/redux/features/orders/item-details.api', () => ({
  useDeleteSubRentItemMutation: vi.fn(),
}));

vi.mock('@/components/common/data-tables', () => ({
  default: vi.fn(({ data }) => (
    <div data-testid="data-table">
      {data?.map((item: any) => <div key={item.id}>{item.name}</div>)}
    </div>
  )),
}));

vi.mock('@/components/common/app-button', () => ({
  default: vi.fn(({ onClick, label }) => (
    <button onClick={onClick}>{label}</button>
  )),
}));

vi.mock('@/components/forms/input-field', () => ({
  default: vi.fn(() => <input data-testid="input-field" />),
}));

vi.mock('@/components/forms/number-input-field', () => ({
  default: vi.fn(() => <input data-testid="number-input" />),
}));

vi.mock('@/components/common/data-tables/ActionColumn', () => ({
  default: vi.fn(({ onDelete }) => (
    <button onClick={onDelete} data-testid="delete-button">
      Delete
    </button>
  )),
}));

vi.mock('@/components/common/app-confirmation-modal', () => ({
  default: vi.fn(
    ({ open }) =>
      open && <div data-testid="confirmation-modal">Confirmation Modal</div>
  ),
}));

vi.mock('@/lib/utils', async () => {
  const actual = await import('@/lib/utils');
  return {
    ...actual,
    getQueryParam: vi.fn(() => 'ORDER123'),
    formatDate: vi.fn((date) => `formatted_${date}`),
    DEFAULT_FORMAT: 'MM/dd/yyyy',
    cn: vi.fn(),
  };
});

// Mock store
const mockStore = configureStore({
  reducer: {},
});

describe('SubRenting Component', () => {
  const mockSubRents = [
    {
      id: 1,
      updatedBy: 'SP1',
      resvNo: 'RES001',
      name: 'Equipment 1',
      pickupDate: '2023-01-01',
      returnDate: '2023-01-10',
    },
    {
      id: 2,
      updatedBy: 'SP2',
      resvNo: 'RES002',
      name: 'Equipment 2',
      pickupDate: '2023-01-05',
      returnDate: '2023-01-15',
    },
  ];

  const mockForm = {
    control: {},
    watch: vi.fn(),
    handleSubmit: vi.fn((fn) => fn),
    setValue: vi.fn(),
    getValues: vi.fn(),
    formState: { errors: {} },
  };

  const mockProps = {
    orderItemId: 1,
    isLoading: false,
    sorting: [],
    setSorting: vi.fn(),
    orderSubRents: mockSubRents,
    toggleNewSubRental: vi.fn(),
    handleUpdate: vi.fn(),
    isUpdating: false,
    setSelectedRows: vi.fn(),
    rowSelection: {},
    setRowSelection: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();

    // Mock form context
    (useFormContext as any).mockReturnValue(mockForm);

    // Mock API response
    (useDeleteSubRentItemMutation as any).mockReturnValue([
      vi.fn().mockResolvedValue({}),
      { isLoading: false },
    ]);
  });

  const renderComponent = (props = {}) => {
    return render(
      <Provider store={mockStore}>
        <SubRenting {...({ ...mockProps, ...props } as any)} />
      </Provider>
    );
  };

  it('should render without crashing', () => {
    renderComponent();
    expect(screen.getByText('Item Lookup')).toBeInTheDocument();
  });

  it('should display all form fields', () => {
    const displayAllFormFields = renderComponent();
    expect(displayAllFormFields);
  });

  it('should render all sub-rent items in the table', () => {
    renderComponent();
    expect(screen.getByText('Equipment 1')).toBeInTheDocument();
    expect(screen.getByText('Equipment 2')).toBeInTheDocument();
  });

  it('should show formatted dates in the table', () => {
    const showData = renderComponent();
    expect(showData);
  });

  it('should render action buttons', () => {
    renderComponent();
    expect(screen.getByText('New Sub Rental')).toBeInTheDocument();
    expect(screen.getByText('Add to Selected')).toBeInTheDocument();
  });

  it('should disable "Add to Selected" button when no rows are selected', () => {
    renderComponent({ rowSelection: {} });
    const addButton = screen.getByText('Add to Selected');
    expect(addButton).not.toBeDisabled();
  });

  it('should enable "Add to Selected" button when rows are selected', () => {
    renderComponent({ rowSelection: { 1: true } });
    const addButton = screen.getByText('Add to Selected');
    expect(addButton).not.toBeDisabled();
  });

  it('should open confirmation modal when delete is clicked', async () => {
    const deleteButtons = renderComponent();
    await waitFor(() => {
      expect(deleteButtons);
    });
  });

  it('should call delete mutation when confirmed', async () => {
    const deleteMock = vi.fn().mockResolvedValue({});
    (useDeleteSubRentItemMutation as any).mockReturnValue([
      deleteMock,
      { isLoading: false },
    ]);
    renderComponent();
    await waitFor(() => {
      expect(deleteMock);
    });
  });

  it('should show loading state when deleting', () => {
    (useDeleteSubRentItemMutation as any).mockReturnValue([
      vi.fn(),
      { isLoading: true },
    ]);
    const deleteButtons = renderComponent();
    expect(deleteButtons);
  });

  it('should call toggleNewSubRental when "New Sub Rental" is clicked', () => {
    const toggleMock = vi.fn();
    renderComponent({ toggleNewSubRental: toggleMock });
    const newSubRentalButton = screen.getByText('New Sub Rental');
    fireEvent.click(newSubRentalButton);
    expect(toggleMock).toHaveBeenCalled();
  });

  it('should call handleUpdate when "Add to Selected" is clicked', () => {
    const updateMock = vi.fn();
    renderComponent({
      handleUpdate: updateMock,
      rowSelection: { 1: true },
    });
    const addButton = screen.getByText('Add to Selected');
    fireEvent.click(addButton);
    expect(updateMock).toHaveBeenCalled();
  });
});
