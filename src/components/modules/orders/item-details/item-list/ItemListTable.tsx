import DataTable from '@/components/common/data-tables';
import { ItemFormDataTypes } from '@/types/order.types';
import { ColumnDef } from '@tanstack/react-table';
import { useEffect, useRef } from 'react';

interface ItemListTableProps {
  fields: ItemFormDataTypes[];
  columns: ColumnDef<ItemFormDataTypes>[];
  rowSelection: Record<string, boolean>;
  isLoading: boolean;
  setRowSelection: React.Dispatch<
    React.SetStateAction<Record<string, boolean>>
  >;
}

const ItemListTable = ({
  fields,
  columns,
  rowSelection,
  setRowSelection,
  isLoading,
}: ItemListTableProps) => {
  const onScrollRef = useRef<HTMLTableElement>(null);

  useEffect(() => {
    if (onScrollRef.current) {
      // Scroll to the bottom of the table when the fields change
      onScrollRef.current.scrollTop = onScrollRef.current.scrollHeight;
    }
  }, [fields]); // Trigger scroll when fields change

  return (
    <DataTable
      onScrollRef={onScrollRef}
      data={fields || []}
      columns={columns}
      enablePagination={false}
      tableClassName="max-h-[580px] overflow-auto"
      enableRowSelection
      rowSelection={rowSelection}
      onRowSelectionChange={setRowSelection}
      isLoading={isLoading}
      highlightKey="isOverbooked"
      highlightClassName="bg-red-200 hover:bg-red-200"
      selectedHighlightClassName="data-[state=selected]:bg-red-100"
    />
  );
};

export default ItemListTable;
