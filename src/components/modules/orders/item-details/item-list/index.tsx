import AppButton from '@/components/common/app-button';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import { getQueryParam } from '@/lib/utils';
import { useGetEnumsListQuery } from '@/redux/features/enums-api/enums-api';
import {
  useDeleteOrderItemMutation,
  useGetItemDetailsMutation,
  useGetItemListQuery,
} from '@/redux/features/orders/item-details.api';
import {
  ItemListTypes,
  ItemTypeEnum,
  OrderInformationTypes,
  OrderItemTypes,
} from '@/types/order.types';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { UseFormReturn, useFieldArray, useFormContext } from 'react-hook-form';
import AdditionalItemInfo from '../additional-item-info';
import ChangeItem from '../change-item';
import KitItemInfo from '../kit-item';
import OverbookedItemInfo from '../overbooked-item-info';
import ReturnQuantity from '../return-quantity';
import SubRental from '../sub-rental';
import AvailabilityCalendar from './availability-calendar';
import { mapItemsToFormValues } from './itemListHelper';
import ItemListTable from './ItemListTable';
import ItemToolbar from './ItemToolbar';
import OverbookedAlert from './OverbookedAlert';
import { useItemListColumns } from './useItemListColumn';
import { useItemListHandlers } from './useItemListHandler';
import ChangeSorting from '../ChangeSorting';
import {
  useLazyGetAdditionalInfoShippingInfoBoxQuery,
  useLazyGetAdditionalInfoShippingInfoItemQuery,
} from '@/redux/features/orders/additional-info.api';

interface DeleteDialogState {
  isOpen: boolean;
  itemId: number | null;
  index: number | null;
}

const initialDeleteDialogState: DeleteDialogState = {
  isOpen: false,
  itemId: null,
  index: null,
};

interface ItemListProps {
  form: UseFormReturn<ItemListTypes>;
  onDefaultValuesLoaded?: (defaults: ItemListTypes) => void;
}

export interface OpenDialogType {
  state: boolean;
  action: string;
  value?: string | number | null;
}
const ItemList = ({ form, onDefaultValuesLoaded }: ItemListProps) => {
  const orderId = getQueryParam('id') as string;
  const deletedItemForm = useFormContext<OrderInformationTypes>();
  const isDelete = deletedItemForm?.watch('isDeleted');
  const isOrderEntryReadOnly = deletedItemForm?.watch('orderEntryReadOnly');
  const [triggerShippingInfoItem] =
    useLazyGetAdditionalInfoShippingInfoItemQuery();

  const [triggerShippingInfoBox] =
    useLazyGetAdditionalInfoShippingInfoBoxQuery();

  const [rowSelection, setRowSelection] = useState<Record<string, boolean>>({});
  const [deleteDialogState, setDeleteDialogState] = useState<DeleteDialogState>(
    initialDeleteDialogState
  );
  const [open, setOpen] = useState<OpenDialogType>({
    state: false,
    action: '',
    value: null,
  });
  const { data, isLoading } = useGetItemListQuery({ orderId });
  const [deleteItem, { isLoading: isDeleteLoading }] =
    useDeleteOrderItemMutation();
  const [getItemBriefData] = useGetItemDetailsMutation();
  const { data: itemType, isLoading: itemTypeLoading } = useGetEnumsListQuery({
    name: 'ItemType',
  });

  const orderForm = useFormContext<OrderInformationTypes>();
  const userDefaultStoreInfo = orderForm.getValues('userDefaultStoreInfo');
  const showSerialNumberColumn = userDefaultStoreInfo?.showSerialNumberColumn;

  const defaultValues = useMemo(
    () => mapItemsToFormValues(data?.data as OrderItemTypes),
    [data?.data]
  );
  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'items',
  });

  // Check if any item is overbooked
  const isOverbooked = useMemo(() => data?.data?.isOverbooked, [data?.data]);

  const onOpenChange = () => {
    setOpen({ state: false, action: '', value: null });
  };

  const selectedItemIndex = Object.keys(rowSelection).at(0);
  // console.log(defaultValues?.items);
  const selectedItem = defaultValues?.items?.find(
    (_, index: number) => index === Number(selectedItemIndex)
  );

  const renderDialogContent = (action: string) => {
    switch (action) {
      case ItemTypeEnum.SUB_RENTAL:
        return (
          <SubRental
            open={open}
            onOpenChange={onOpenChange}
            orderItemId={selectedItem?.listId}
          />
        );
      case ItemTypeEnum.RETURN_QTY:
        return <ReturnQuantity open={open} onOpenChange={onOpenChange} />;
      case ItemTypeEnum.OVERBOOKED_ITEM:
        return (
          <OverbookedItemInfo
            open={open.state}
            onOpenChange={onOpenChange}
            itemId={{
              label: selectedItem?.itemId?.label,
              value: selectedItem?.itemId?.value,
            }}
          />
        );
      case ItemTypeEnum.AVAILABILITY_INFO:
        return (
          <AvailabilityCalendar
            open={open.state}
            onOpenChange={onOpenChange}
            itemId={{
              label: selectedItem?.itemId?.label,
              value: selectedItem?.itemId?.value,
            }}
          />
        );
      case ItemTypeEnum.KIT_ITEM:
        if (selectedItem?.type === 'KIT_ITEM') {
          return (
            <KitItemInfo
              open={open.state}
              onOpenChange={onOpenChange}
              // itemId={{
              //   label: selectedItem?.itemId?.label,
              //   value: selectedItem?.itemId?.value,
              // }}
              orderItemId={selectedItem?.listId}
            />
          );
        }
        return null;
      case ItemTypeEnum.ADDITIONAL_ITEM_INFO:
        return (
          <AdditionalItemInfo
            open={open}
            onOpenChange={onOpenChange}
            orderItemId={open.value ? open.value : selectedItem?.listId}
          />
        );
      case ItemTypeEnum.CHANGE_ITEM:
        return (
          <ChangeItem
            open={open}
            onOpenChange={onOpenChange}
            selectedItem={selectedItem}
            setRowSelection={setRowSelection}
          />
        );
      case ItemTypeEnum.CHANGE_SORTING:
        return <ChangeSorting open={open} onOpenChange={onOpenChange} />;
      default:
        return null;
    }
  };

  useEffect(() => {
    if (defaultValues) {
      onDefaultValuesLoaded?.(defaultValues as ItemListTypes);
      form.reset(defaultValues);
    }
  }, [defaultValues, form, onDefaultValuesLoaded]);

  const toggleDelete = useCallback(
    (id: number | null, index: number | null) => {
      setDeleteDialogState((prev) => ({
        isOpen: !prev.isOpen,
        itemId: id,
        index,
      }));
    },
    []
  );

  const handleDeleteItem = async () => {
    if (deleteDialogState.itemId) {
      await deleteItem({ orderItemId: deleteDialogState.itemId });
      await triggerShippingInfoBox(orderId);
      await triggerShippingInfoItem(orderId);
    } else if (deleteDialogState.index !== null) {
      remove(deleteDialogState.index);
    }
    setDeleteDialogState(initialDeleteDialogState);
    setRowSelection({});
  };

  const { handleItemIdChange, handlePriceChange, handleQuantityChange } =
    useItemListHandlers({ form, orderId, getItemBriefData });

  const columns = useItemListColumns({
    form,
    itemType,
    itemTypeLoading,
    handleItemIdChange,
    handlePriceChange,
    handleQuantityChange,
    showSerialNumberColumn,
    toggleDelete,
    setOpen,
  });

  const handleAddNewItem = useCallback(() => {
    const lastItem =
      form.getValues('items')[form.getValues('items').length - 1];

    if (lastItem?.itemId?.value || fields?.length === 0) {
      append({
        listId: null,
        itemId: null,
        description: '',
        quantity: '',
        price: '0',
        subRental: 'No',
        total: 0,
        serialNumber: null,
        parentId: null,
        // isModified: true,
        disabledCheckBox: true,
      });
    }
  }, [append, fields?.length, form]);

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between w-full">
        <h1 className="text-2xl font-semibold text-[#181A1D] min-w-32 gap-2">
          Item List
        </h1>
        <ItemToolbar
          append={append}
          setOpen={setOpen}
          rowSelection={rowSelection}
        />
      </div>
      <div>
        <ItemListTable
          fields={fields}
          columns={columns}
          rowSelection={rowSelection}
          setRowSelection={setRowSelection}
          isLoading={isLoading}
        />
        <div className="w-full p-2 border-grayScale-20 border-b border-x rounded-b-md">
          <AppButton
            label="+ Add New"
            className=" bg-brand-teal-Default hover:bg-brand-teal-secondary"
            onClick={handleAddNewItem}
            disabled={isDelete || isOrderEntryReadOnly}
          />
        </div>
      </div>
      <div className="w-fit flex gap-6 justify-between items-center px-4 py-3 rounded-lg border border-gray-300 my-2">
        <div className="flex gap-x-8">
          <span className="font-semibold">Total Weight</span>
          <span>{form.getValues('totalWeight') ?? 0}</span>
        </div>
        <div className="w-[2px] h-6 bg-gray-200"></div>
        <div className="flex gap-x-8">
          <span className="font-semibold">Total Cube</span>
          <span>{form.getValues('totalCube') ?? 0}</span>
        </div>
      </div>

      <OverbookedAlert setOpen={setOpen} isOverbooked={isOverbooked} />
      <AppConfirmationModal
        description={<div>Are you sure you want to delete this record?</div>}
        open={deleteDialogState.isOpen}
        handleCancel={() => setDeleteDialogState(initialDeleteDialogState)}
        handleSubmit={handleDeleteItem}
        disabled={isDeleteLoading}
        isLoading={isDeleteLoading}
      />
      {open && renderDialogContent(open.action)}
    </div>
  );
};

export default ItemList;
