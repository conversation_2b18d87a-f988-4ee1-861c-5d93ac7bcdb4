import AppButton from '@/components/common/app-button';
import { ItemTypeEnum } from '@/types/order.types';
import { AlertTriangle, Info } from 'lucide-react';
import { memo } from 'react';
import { OpenDialogType } from '.';

interface OverbookedAlertProps {
  setOpen: React.Dispatch<React.SetStateAction<OpenDialogType>>;
  isOverbooked: boolean;
}
const OverbookedAlert = ({ setOpen, isOverbooked }: OverbookedAlertProps) => {
  const handleOpen = () => {
    setOpen({
      state: true,
      action: ItemTypeEnum.OVERBOOKED_ITEM,
    });
  };

  return (
    isOverbooked && (
      <div className="flex flex-col gap-4 border border-border-Default rounded-lg p-6 bg-[#F5F5F5] w-full">
        <div className="flex items-center gap-2 text-neutral-600">
          <Info className="w-5 h-5" />
          <span className=" text-xl">Info</span>
        </div>

        <p className="text-neutral-500 text-lg">
          Some of items are overbooked. Please add Sub-rentals / remove them to
          proceed.
        </p>

        <AppButton
          className="w-fit"
          variant="neutral"
          icon={AlertTriangle}
          label="View Overbooked Items"
          onClick={handleOpen}
        />
      </div>
    )
  );
};

export default memo(OverbookedAlert);
