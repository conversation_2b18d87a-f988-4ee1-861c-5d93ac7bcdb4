import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import ItemListTable from './ItemListTable';
import { ColumnDef } from '@tanstack/react-table';
import { ItemFormDataTypes } from '@/types/order.types';

const createMockItem = (
  overrides: Partial<ItemFormDataTypes> = {}
): ItemFormDataTypes => ({
  serialNumber: 'SN123',
  description: 'Test Item',
  quantity: 10,
  subRental: '',
  price: 100,
  orderId: 1,
  total: 789,
  parentId: 771,
  listId: 51,
  itemId: { label: '', value: '' },
  ...overrides,
});

vi.mock('@/components/common/data-tables', () => ({
  default: vi.fn(
    (props: {
      columns: ColumnDef<ItemFormDataTypes>[];
      data: ItemFormDataTypes[];
      onScrollRef: React.RefObject<HTMLDivElement>;
      [key: string]: any;
    }) => {
      return (
        <div
          ref={props.onScrollRef}
          data-testid="mock-data-table"
          style={{ height: '580px', overflow: 'auto' }}
        >
          <table>
            <thead>
              <tr>
                {props.columns.map(
                  (column: ColumnDef<ItemFormDataTypes> | any) => (
                    <th key={column.id || column.accessorKey}>
                      {typeof column.header === 'function'
                        ? column.header({ column, header: column.header })
                        : column.header}
                    </th>
                  )
                )}
              </tr>
            </thead>
            <tbody>
              {props.data.map((item: ItemFormDataTypes, index: number) => (
                <tr key={index}>
                  {props.columns.map(
                    (column: ColumnDef<ItemFormDataTypes> | any) => (
                      <td key={column.id || column.accessorKey}>
                        {column.accessorKey &&
                          item[column.accessorKey as keyof ItemFormDataTypes]}
                      </td>
                    )
                  )}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      );
    }
  ),
}));

describe('ItemListTable', () => {
  const mockColumns: ColumnDef<ItemFormDataTypes>[] = [
    {
      accessorKey: 'name',
      header: 'Name',
    },
    {
      accessorKey: 'quantity',
      header: 'Quantity',
    },
    {
      accessorKey: 'description',
      header: 'Description',
    },
  ];

  const mockFields: ItemFormDataTypes[] = [
    {
      orderId: 1,
      serialNumber: null,
      quantity: 10,
      description: 'Item 1',
      type: 'standard',
      subRental: 'no',
      price: 100,
      total: 1000,
      orderNumber: 101,
      parentId: null,
      listId: 1,
      itemId: { label: 'item-001', value: 'item-001' },
      disabledCheckBox: false,
      itemIdString: 'item-001',
    },
    {
      orderId: 2,
      serialNumber: null,
      quantity: 5,
      description: 'Item 2',
      type: 'standard',
      subRental: 'no',
      price: 150,
      total: 750,
      orderNumber: 102,
      parentId: null,
      listId: 2,
      itemId: { label: 'item-002', value: 'item-002' },
      disabledCheckBox: false,
      itemIdString: 'item-002',
    },
  ];

  const mockRowSelection = { '1': true };
  const mockSetRowSelection = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders correctly with provided props', () => {
    render(
      <ItemListTable
        fields={mockFields}
        columns={mockColumns}
        rowSelection={mockRowSelection}
        setRowSelection={mockSetRowSelection}
        isLoading={false}
      />
    );
    expect(screen.getByTestId('mock-data-table')).toBeInTheDocument();
    expect(screen.getByText('Name')).toBeInTheDocument();
    expect(screen.getByText('Quantity')).toBeInTheDocument();
    expect(screen.getByText('Item 1')).toBeInTheDocument();
    expect(screen.getByText('10')).toBeInTheDocument();
  });

  it('applies correct table classes', () => {
    render(
      <ItemListTable
        fields={mockFields}
        columns={mockColumns}
        rowSelection={mockRowSelection}
        setRowSelection={mockSetRowSelection}
        isLoading={false}
      />
    );
    const tableContainer = screen.getByTestId('mock-data-table');
    expect(tableContainer).toHaveStyle('height: 580px');
    expect(tableContainer).toHaveStyle('overflow: auto');
  });

  it('passes highlight props correctly', () => {
    const highlightFields: ItemFormDataTypes[] = [
      ...mockFields,
      createMockItem({
        orderId: 1,
        serialNumber: null,
        quantity: 10,
        description: 'Item 1',
        type: 'standard',
        subRental: 'no',
        price: 100,
        total: 1000,
        orderNumber: 101,
        parentId: null,
        listId: 1,
        itemId: { label: 'item-001', value: 'item-001' },
        disabledCheckBox: false,
        itemIdString: 'item-001',
      }),
    ];
    render(
      <ItemListTable
        fields={highlightFields}
        columns={mockColumns}
        rowSelection={mockRowSelection}
        setRowSelection={mockSetRowSelection}
        isLoading={false}
      />
    );
    expect(screen.getByText('Item 2')).toBeInTheDocument();
  });

  it('shows loading state when isLoading is true', () => {
    const isLoading = render(
      <ItemListTable
        fields={mockFields}
        columns={mockColumns}
        rowSelection={mockRowSelection}
        setRowSelection={mockSetRowSelection}
        isLoading={true}
      />
    );
    expect(isLoading);
  });

  it('handles empty fields array', () => {
    render(
      <ItemListTable
        fields={[]}
        columns={mockColumns}
        rowSelection={{}}
        setRowSelection={mockSetRowSelection}
        isLoading={false}
      />
    );
    expect(screen.getByTestId('mock-data-table')).toBeInTheDocument();
    expect(screen.queryByText('Item 1')).not.toBeInTheDocument();
  });
});
