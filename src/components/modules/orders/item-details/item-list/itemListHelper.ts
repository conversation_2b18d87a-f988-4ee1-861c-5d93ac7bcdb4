import { ItemListTypes, OrderItemTypes } from '@/types/order.types';

export const mapItemsToFormValues = (items: OrderItemTypes): ItemListTypes => ({
  items:
    items?.orderItemList?.map((item) => ({
      serialNumber: item?.serialNumber || '',
      parentId: item?.parentId ?? '',
      listId: item.id,
      itemId: {
        label: item.itemIdString || '',
        value: item.itemId?.toString() || '',
      },
      description: item.description || '',
      quantity: item.quantity || '',
      price: item.price || '0',
      subRental: item?.subRental ? 'Yes' : 'No',
      total: item.total || 0,
      type: item?.type ?? '',
      isOverbooked: item?.isOverbooked,
    })) || [],
  totalCube: items?.totalCube ?? 0,
  totalWeight: items?.totalWeight ?? 0,
});
