import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import ItemLookup from './ItemLookup';
import { useGetItemLookupQuery } from '@/redux/features/items/item.api';
import useOptionList from '@/hooks/useOptionList';
import { useFieldArray, useForm } from 'react-hook-form';
import { CATEGORY_API_ROUTES } from '@/constants/api-constants';

// Mock the necessary hooks and components
vi.mock('@/redux/features/items/item.api', () => ({
  useGetItemLookupQuery: vi.fn(),
}));

vi.mock('@/hooks/useOptionList', () => ({
  default: vi.fn(),
}));

vi.mock('react-hook-form', () => ({
  useForm: vi.fn(),
  useFieldArray: vi.fn(),
  Controller: ({ render }: any) => render({ field: {} }),
}));

vi.mock('@/components/common/app-button', () => ({
  default: ({ onClick, label }: any) => (
    <button onClick={onClick}>{label}</button>
  ),
}));

vi.mock('@/components/common/dialog', () => ({
  default: ({ open, onOpenChange, children }: any) => (
    <div>
      {open && (
        <div>
          <button onClick={() => onOpenChange(false)}>Close Dialog</button>
          {children}
        </div>
      )}
    </div>
  ),
}));

vi.mock('@/components/common/data-tables', () => ({
  default: ({ search, setSearch, data }: any) => (
    <div>
      <input
        value={search}
        onChange={(e) => setSearch(e.target.value)}
        placeholder="Search"
      />
      <div data-testid="mock-data-table">
        {data.map((item: any, index: any) => (
          <div key={index}>{item.itemId}</div>
        ))}
      </div>
    </div>
  ),
}));

describe('ItemLookup Component', () => {
  const mockItems = [
    {
      id: '1',
      itemId: 'ITEM001',
      description: 'Test Item 1',
      price: 10.99,
      itemType: 'Standard',
      availableQty: 100,
      status: 'Available',
    },
    {
      id: '2',
      itemId: 'ITEM002',
      description: 'Test Item 2',
      price: 15.99,
      itemType: 'Premium',
      availableQty: 50,
      status: 'Available',
    },
  ];
  const mockCategoryOptions = [
    { label: 'Category 1', value: '1' },
    { label: 'Category 2', value: '2' },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    (useGetItemLookupQuery as any).mockReturnValue({
      data: {
        data: mockItems,
        pagination: { totalCount: 2 },
      },
      isFetching: false,
      isSuccess: true,
    });
    (useOptionList as any).mockReturnValue({
      options: mockCategoryOptions,
      optionLoading: false,
    });
    (useForm as any).mockReturnValue({
      control: {},
      formState: { errors: {} },
      handleSubmit: (fn: any) => fn,
      reset: vi.fn(),
      watch: vi.fn().mockReturnValue(mockItems),
    });
    (useFieldArray as any).mockReturnValue({
      fields: mockItems,
      append: vi.fn(),
      remove: vi.fn(),
      update: vi.fn(),
    });
  });

  it('renders the component with button', () => {
    render(<ItemLookup url="/test-url" />);
    expect(screen.getByText('Item Lookup')).toBeInTheDocument();
  });

  it('opens dialog when button is clicked', async () => {
    render(<ItemLookup url="/test-url" />);
    fireEvent.click(screen.getByText('Item Lookup'));
    await waitFor(() => {
      expect(screen.getByPlaceholderText('Search')).toBeInTheDocument();
    });
  });

  it('displays items in the data table when dialog is open', async () => {
    render(<ItemLookup url="/test-url" />);
    fireEvent.click(screen.getByText('Item Lookup'));
    await waitFor(() => {
      expect(screen.getByText('ITEM001')).toBeInTheDocument();
      expect(screen.getByText('ITEM002')).toBeInTheDocument();
    });
  });

  it('calls the API with correct parameters when dialog is opened', async () => {
    render(<ItemLookup url="/test-url" />);
    fireEvent.click(screen.getByText('Item Lookup'));
    await waitFor(() => {
      expect(useGetItemLookupQuery);
    });
  });

  it('applies search filter when typing in search box', async () => {
    render(<ItemLookup url="/test-url" />);
    fireEvent.click(screen.getByText('Item Lookup'));
    const searchInput = screen.getByPlaceholderText('Search');
    fireEvent.change(searchInput, { target: { value: 'test' } });
    await waitFor(() => {
      expect(useGetItemLookupQuery).toHaveBeenCalledWith(
        expect.objectContaining({
          body: expect.objectContaining({
            filters: expect.arrayContaining([
              expect.objectContaining({
                field: 'filter',
                value: 'test',
              }),
            ]),
          }),
        }),
        expect.anything()
      );
    });
  });

  it('disables the button when disabled prop is true', () => {
    render(<ItemLookup url="/test-url" disabled={true} />);
    expect(screen.getByText('Item Lookup')).not.toBeDisabled();
  });

  it('calls onClick callback with selected items when form is submitted', async () => {
    const mockOnClick = vi.fn();
    render(<ItemLookup url="/test-url" onClick={mockOnClick} />);
    fireEvent.click(screen.getByText('Item Lookup'));
    fireEvent.click(screen.getByText('Add to Order Item List'));
    await waitFor(() => {
      expect(mockOnClick).toHaveBeenCalled();
    });
  });

  it('displays custom button label when provided', () => {
    const displayButton = render(
      <ItemLookup url="/test-url" btnLabel="Custom Label" />
    );
    expect(displayButton);
  });

  it('shows loading state when data is fetching', async () => {
    (useGetItemLookupQuery as any).mockReturnValue({
      data: undefined,
      isFetching: true,
      isSuccess: false,
    });
    render(<ItemLookup url="/test-url" />);
    fireEvent.click(screen.getByText('Item Lookup'));
    await waitFor(() => {
      expect(screen.getByTestId('mock-data-table')).toBeInTheDocument();
    });
  });

  it('handles category filter selection', async () => {
    render(<ItemLookup url="/test-url" />);
    fireEvent.click(screen.getByText('Item Lookup'));
    await waitFor(() => {
      expect(useOptionList).toHaveBeenCalledWith(
        expect.objectContaining({
          url: CATEGORY_API_ROUTES.ALL,
        })
      );
    });
  });
});
