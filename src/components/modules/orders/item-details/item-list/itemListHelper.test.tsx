import { describe, it, expect } from 'vitest';
import { OrderItemTypes } from '@/types/order.types';

describe('mapItemsToFormValues', () => {
  it('should map order items to form values correctly', () => {
    const mockOrderItems: OrderItemTypes = {
      orderItemList: [
        {
          id: 123,
          itemId: 456,
          itemIdString: 'ITEM-456',
          serialNumber: 'SN-001',
          parentId: 789,
          description: 'Test Item',
          quantity: 10,
          price: 100.0,
          subRental: true,
          total: 1000,
          type: 'Rental',
          isOverbooked: false,
        },
      ],
      totalCube: 2.5,
      totalWeight: 100,
    };
    const result = mockOrderItems;
    expect(result);
  });

  it('should return defaults when values are missing', () => {
    const mockOrderItems: OrderItemTypes = {
      orderItemList: [
        {
          id: 1,
          itemId: null as unknown as number,
          itemIdString: null as unknown as string,
          serialNumber: null as unknown as string,
          parentId: null as unknown as number,
          description: null as unknown as string,
          quantity: null as unknown as number,
          price: null as unknown as number,
          subRental: false,
          total: null as unknown as number,
          type: null as unknown as string,
          isOverbooked: undefined as any,
        },
      ],
      totalCube: null as unknown as number,
      totalWeight: null as unknown as number,
    };
    const result = mockOrderItems;
    expect(result);
  });
});
