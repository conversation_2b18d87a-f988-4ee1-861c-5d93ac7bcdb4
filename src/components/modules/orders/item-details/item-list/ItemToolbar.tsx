import {
  BadgeInfo,
  Calendar,
  KeyRound,
  LayoutGridIcon,
  Pen,
  PenTool,
  RefreshCcw,
  SendToBack,
  TrendingUp,
  WineIcon,
} from 'lucide-react';
import { memo, useCallback, useEffect, useMemo, useState } from 'react';
import { UseFieldArrayAppend, useFormContext } from 'react-hook-form';

// Components
import AppButton from '@/components/common/app-button';
import DropdownMenuItems from '@/components/common/DropdownMenuItems';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import ItemLookup from './ItemLookup';

// Constants and Utilities
import {
  ORDER_ITEM_DETAILS_API_ROUTES,
  SURGE_RATES_API_ROUTES,
} from '@/constants/api-constants';
import { getQueryParam } from '@/lib/utils';

// Types
import {
  useGetItemListQuery,
  useUpdateSurgeChargeMutation,
} from '@/redux/features/orders/item-details.api';
import {
  ItemListTypes,
  ItemTypeEnum,
  OrderInformationTypes,
  OrderItemTypes,
} from '@/types/order.types';
import { OpenDialogType } from '.';
import { mapItemsToFormValues } from './itemListHelper';
import useOptionList from '@/hooks/useOptionList';

// Constants
const DEFAULT_SUB_RENTAL = 'No';

interface ItemToolbarProps {
  append: UseFieldArrayAppend<ItemListTypes, 'items'>;
  setOpen: React.Dispatch<React.SetStateAction<OpenDialogType>>;
  rowSelection: any;
}

/**
 * ItemToolbar component provides controls for managing order items including:
 * - Adding items from lookup
 * - Sub-rental actions
 * - Other processes dropdown
 * - Surge pricing selection
 */
const ItemToolbar = memo(
  ({ append, setOpen, rowSelection }: ItemToolbarProps) => {
    const [isDisable, setIsDisable] = useState(true);
    const orderId = getQueryParam('id') as string;
    const form = useFormContext<OrderInformationTypes>();
    const surgeRateId = form.watch('surgeRateId');
    const surgeValue = surgeRateId === null ? 'off' : surgeRateId?.toString();
    const isMissingOrder = form.watch('originType') === 'MISSING_ORDER';
    const isDelete = form.watch('isDeleted');
    const isOrderEntryReadOnly = form.watch('orderEntryReadOnly');
    const { data } = useGetItemListQuery({ orderId });
    const [updateSurgeCharge] = useUpdateSurgeChargeMutation();

    /**
     * Transforms and adds selected items to the item list
     */
    const handleAddItemLookupData = useCallback(
      (selectedItems: any[]) => {
        const transformedItems = selectedItems.map((item) => ({
          ...item,
          isModified: true,
          disabledCheckBox: true,
          type: item.itemType,
          subRental: DEFAULT_SUB_RENTAL,
          ...(item.price &&
            item.quantity && {
              total: Number(item.price) * Number(item.quantity),
            }),
        }));

        append(transformedItems);
      },
      [append]
    );

    const defaultValues = useMemo(
      () => mapItemsToFormValues(data?.data as OrderItemTypes),
      [data?.data]
    );

    const selectedItemIndex = Object.keys(rowSelection).at(0);
    const selectedItem = defaultValues?.items?.find(
      (_, index: number) => index === Number(selectedItemIndex)
    );

    const { options: surgeRateList } = useOptionList({
      url: SURGE_RATES_API_ROUTES.ALL,
      labelKey: 'description',
      valueKey: 'id',
      sortBy: 'id',
    });

    useEffect(() => {
      if (selectedItem?.type === 'KIT_ITEM') {
        setIsDisable(false);
      } else {
        setIsDisable(true);
      }
    }, [selectedItem]);

    const onOpenChange = () => {
      setOpen({
        state: selectedItem?.type === 'KIT_ITEM',
        action: selectedItem?.type === 'KIT_ITEM' ? ItemTypeEnum.KIT_ITEM : '',
      });
    };

    const handleSurge = async (value: string) => {
      const surgeRateId = value === 'off' ? null : value;
      form.setValue('surgeRateId', value === 'off' ? null : Number(value));
      await updateSurgeCharge({ orderId, surgeRateId });
    };

    return (
      <div className="w-full flex flex-wrap gap-2 justify-end">
        {/*  Item Lookup Control  */}
        <div className="w-[200px]">
          <ItemLookup
            className="w-full"
            onClick={handleAddItemLookupData}
            url={ORDER_ITEM_DETAILS_API_ROUTES.ITEM_LOOKUP(orderId)}
            heading="Select one or more Item to add to the order Item list"
            disabled={isDelete || isOrderEntryReadOnly}
          />
        </div>

        {/*  Sub-Rent Item Button  */}
        <div className="w-[200px]">
          <AppButton
            icon={KeyRound}
            iconClassName="w-4 h-4"
            label="Sub-Rent Item"
            variant="neutral"
            className="w-full"
            onClick={() =>
              setOpen({ state: true, action: ItemTypeEnum.SUB_RENTAL })
            }
            disabled={
              isDelete ||
              isOrderEntryReadOnly ||
              !selectedItem ||
              !(selectedItem?.type === 'RENTAL_ITEM')
            }
          />
        </div>

        {/*  Return Quantity Item Button */}
        {isMissingOrder && (
          <div className="w-[200px]">
            <AppButton
              icon={SendToBack}
              className="w-full"
              iconClassName="w-4 h-4"
              label="Return Qty"
              variant="neutral"
              onClick={() =>
                setOpen({ state: true, action: ItemTypeEnum.RETURN_QTY })
              }
              disabled={isDelete || isOrderEntryReadOnly}
            />
          </div>
        )}

        {/* Other Processes Dropdown */}
        <div className="w-[200px]">
          <DropdownMenuItems
            icon={<LayoutGridIcon className="h-4 w-4" />}
            label="Other Processes"
            menuItems={[
              {
                label: 'Change Serialized Item',
                icon: <Pen />,
                disabled:
                  isOrderEntryReadOnly ||
                  !selectedItem ||
                  selectedItem?.type === 'KIT_ITEM',
                onClick: () =>
                  setOpen({
                    state: true,
                    action: ItemTypeEnum.CHANGE_ITEM,
                  }),
              },
              {
                label: 'Overbooked Item Info',
                icon: <PenTool />,
                onClick: () =>
                  setOpen({
                    state: true,
                    action: ItemTypeEnum.OVERBOOKED_ITEM,
                  }),
              },
              {
                label: 'Availability Info',
                icon: <Calendar />,
                onClick: () =>
                  setOpen({
                    state: true,
                    action: ItemTypeEnum.AVAILABILITY_INFO,
                  }),
              },
              {
                label: 'Additional Item Info',
                icon: <BadgeInfo />,
                disabled: !selectedItem,
                onClick: () =>
                  setOpen({
                    state: true,
                    action: ItemTypeEnum.ADDITIONAL_ITEM_INFO,
                  }),
              },
              {
                label: 'Kit Components',
                icon: <WineIcon />,
                onClick: () => onOpenChange(),
                disabled: isOrderEntryReadOnly || isDisable,
              },

              {
                label: 'Change Sorting',
                icon: <RefreshCcw />,
                onClick: () =>
                  setOpen({
                    state: true,
                    action: ItemTypeEnum.CHANGE_SORTING,
                  }),
                disabled: isOrderEntryReadOnly,
              },
            ]}
            // triggerClassName="w-full"
            className="px-2 py-3"
            disabled={isDelete}
          />
        </div>

        {/*  Surge Pricing Selector  */}
        <Select
          disabled={!form.watch('surgePricing')}
          onValueChange={handleSurge}
          value={surgeValue}
        >
          <SelectTrigger
            disabled={isDelete || isOrderEntryReadOnly}
            className="w-[200px]"
          >
            <div className="flex flex-row items-center gap-2 w-full">
              <TrendingUp className="h-4 w-4" />
              <span className="font-semibold text-base">Surge:</span>
              <SelectValue />
            </div>
          </SelectTrigger>

          <SelectContent>
            <SelectItem key="off" value={'off'}>
              Off
            </SelectItem>
            {surgeRateList?.map((option: any) => (
              <SelectItem key={option.value} value={option.value?.toString()}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    );
  }
);

export default ItemToolbar;
