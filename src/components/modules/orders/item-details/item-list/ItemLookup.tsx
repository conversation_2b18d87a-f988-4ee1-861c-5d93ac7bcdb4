import { Bad<PERSON><PERSON>heck, SearchIcon } from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { Submit<PERSON>and<PERSON>, useFieldArray, useForm } from 'react-hook-form';

// Components
import AppButton from '@/components/common/app-button';
import DataTable from '@/components/common/data-tables';
import CustomDialog from '@/components/common/dialog';
import MultiCheckboxDropdown from '@/components/forms/MulitCheckbox';
import NumberInputField from '@/components/forms/number-input-field';

// Hooks and Utilities
import FormActionButtons from '@/components/common/FormActionButtons';
import { CATEGORY_API_ROUTES } from '@/constants/api-constants';
import useOptionList from '@/hooks/useOptionList';
import { cn, getPaginationObject } from '@/lib/utils';
import { useGetItemLookupQuery } from '@/redux/features/items/item.api';
import { PaginationType, SortingStateType } from '@/types/common.types';
import {
  OrderItemLookupFormTypes,
  OrderItemLookupTypes,
} from '@/types/order.types';

/**
 * ItemLookup Component
 * Provides a dialog-based interface for searching and selecting items
 * with quantity and price input capabilities.
 */

interface ItemLookupProps {
  onClick?: (value: OrderItemLookupFormTypes[]) => void;
  className?: string;
  btnLabel?: string;
  url: string;
  heading?: string;
  disabled?: boolean;
  extraFilters?: { field: string; value: string; operator?: string }[];
}

const ItemLookup = ({
  onClick,
  className,
  btnLabel = 'Add to Order Item List',
  url,
  heading = 'Select one or more Item to add to the package list',
  disabled,
  extraFilters = [],
}: ItemLookupProps) => {
  // State Management
  const [openLookup, setOpenLookup] = useState(false);
  const [search, setSearch] = useState('');
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [pagination, setPagination] = useState<PaginationType>({
    pageSize: 10,
    pageIndex: 0,
  });
  const [sorting, setSorting] = useState<SortingStateType[]>([
    { id: 'itemId', desc: true },
  ]);
  const [isFetchingMore, setIsFetchingMore] = useState<boolean>(false);

  // API Hooks
  const payload = useMemo(() => {
    return getPaginationObject({
      pagination,
      sorting,
      filters: [
        { field: 'filter', value: search, operator: 'Contains' },
        {
          field: 'categoryId',
          value: selectedCategories.join(','),
          operator: 'Equals',
        },
        ...extraFilters?.map((filter) => ({
          field: filter.field,
          value: filter.value,
          operator: filter.operator ?? 'Equals', // default fallback
        })),
      ],
    });
  }, [extraFilters, pagination, search, selectedCategories, sorting]);

  const {
    data,
    isFetching: isLoading,
    isFetching,
    isSuccess,
  } = useGetItemLookupQuery(
    {
      url,
      body: payload,
    },
    { skip: !openLookup }
  );
  const ItemList = useMemo(
    () => (isSuccess && data?.data ? data?.data : []),
    [data?.data, isSuccess]
  );

  const { options: categoryList, optionLoading } = useOptionList({
    url: CATEGORY_API_ROUTES.ALL,
    labelKey: 'catDesc',
    valueKey: 'id',
    sortBy: 'catDesc',
  });

  const categoryForm = useForm();

  const defaultValues = useMemo(() => {
    return {
      items:
        ItemList?.map((item: any) => ({
          id: item?.id,
          itemId: item?.itemId,
          description: item?.description,
          price: item?.price,
          itemType: item?.itemType,
        })) || [],
    };
  }, [ItemList]);

  // Form Management
  const form = useForm<{ items: OrderItemLookupTypes[] }>({
    defaultValues,
    mode: 'onChange',
  });

  const { fields } = useFieldArray({
    control: form.control,
    name: 'items',
  });

  /**
   * Reset form with fresh data when response changes
   */
  useEffect(() => {
    if (defaultValues) {
      form.reset(defaultValues);
    }
  }, [defaultValues, form]);

  /**
   * Handle dialog toggle with form reset
   */
  const toggleDialog = useCallback(() => {
    form.reset();
    setSelectedCategories([]);
    setSearch('');
    categoryForm.reset();
    setOpenLookup((prev) => !prev);
  }, [categoryForm, form]);

  /**
   * Form submission handler
   */
  const handleSubmit: SubmitHandler<{ items: OrderItemLookupTypes[] }> =
    useCallback(
      (formData) => {
        const payload = formData.items
          ?.filter((item) => Number(item?.quantity) > 0)
          ?.map((item) => ({
            id: null,
            childItemId: item.id,
            description: item.description,
            quantity: Number(item.quantity),
            itemId: { label: item.itemId, value: item.id?.toString() ?? '' },
            price: item.price,
            itemType: item.itemType,
          }));

        onClick?.(payload);
        toggleDialog();
      },
      [onClick, toggleDialog]
    );

  /**
   * Reset form to initial data state
   */
  const handleReset = useCallback(() => {
    form.reset({
      items:
        data?.data?.map((item) => ({
          ...item,
          quantity: '',
        })) || [],
    });
  }, [data?.data, form]);

  // Table configuration
  const columns = useMemo(
    () => [
      {
        accessorKey: 'itemId',
        header: 'Item ID',
        size: 80,
        enableSorting: true,
      },
      {
        accessorKey: 'description',
        header: 'Description',
        maxSize: 350,
        enableSorting: true,
      },
      {
        accessorKey: 'availableQty',
        header: 'Qty Avail',
        size: 80,
        enableSorting: true,
      },
      {
        accessorKey: 'status',
        header: 'Availability',
        size: 80,
        enableSorting: true,

        cell: ({ row }: any) => (
          <span
            className={
              row.original.status === 'Available'
                ? 'text-green-600'
                : 'text-red-600'
            }
          >
            {row.original.status}
          </span>
        ),
      },
      {
        accessorKey: 'quantity',
        header: 'Quantity',
        size: 100,
        cell: ({ row }: any) => (
          <NumberInputField
            key={row.original.id}
            form={form}
            name={`items.${row.index}.quantity`}
            placeholder="Quantity"
            className="w-28 h-8"
            maxLength={5}
            decimalScale={0}
          />
        ),
      },
      {
        accessorKey: 'price',
        header: 'Price',
        size: 80,
        cell: ({ row }: any) => (
          <NumberInputField
            key={row.original.id}
            form={form}
            name={`items.${row.index}.price`}
            placeholder="$______.__"
            prefix="$"
            className="w-28 h-8"
            maxLength={11}
            fixedDecimalScale
            thousandSeparator=","
          />
        ),
      },
    ],
    [form]
  );

  // Derived state
  const hasSelectedItems = form
    .watch('items')
    ?.some((item) => Number(item?.quantity) > 0);

  // Custom toolbar for category selection
  const CustomToolbar = (
    <MultiCheckboxDropdown
      name="category"
      form={categoryForm}
      optionsList={categoryList ?? []}
      placeholder={'Select Categories'}
      onChange={(value) => setSelectedCategories(value)}
      isLoading={optionLoading}
    />
  );

  const fetchMore = useCallback(() => {
    if (isFetchingMore) return; // Avoid duplicate calls

    setIsFetchingMore(true); // Set flag to prevent further calls
    setPagination((prev) => ({ ...prev, pageSize: prev.pageSize + 10 }));
  }, [isFetchingMore]);

  // Reset `isFetchingMore` only after new data arrives
  useEffect(() => {
    if (!isFetching) {
      setIsFetchingMore(false);
    }
  }, [isFetching]);

  return (
    <div className="w-full">
      <div className="flex justify-end">
        <AppButton
          label="Item Lookup"
          variant="primary"
          icon={SearchIcon}
          iconClassName="w-4 h-4"
          className={cn(
            'bg-brand-teal-Default hover:bg-brand-teal-Default',
            className
          )}
          onClick={toggleDialog}
          disabled={disabled}
        />
      </div>

      <CustomDialog
        open={openLookup}
        onOpenChange={toggleDialog}
        title="Item Lookup"
        className={cn(
          (className = 'overflow-auto max-w-[80%] 2xl:max-w-[55%]'),
          className
        )}
        contentClassName="h-[450px] 2xl:h-[600px]"
      >
        <div className="w-full relative">
          <div className="px-5 py-2">
            <DataTable
              data={fields ?? []}
              pagination={pagination}
              setPagination={setPagination}
              columns={columns}
              isLoading={isLoading}
              totalItems={data?.pagination?.totalCount}
              search={search}
              setSearch={setSearch}
              enableSearch
              sorting={sorting}
              setSorting={setSorting}
              customToolBar={CustomToolbar}
              heading={<div className="font-normal text-base">{heading}</div>}
              enablePagination={false}
              tableClassName="max-h-[310px] 2xl:max-h-[450px] overflow-auto"
              loaderRows={9}
              isInfiniteScroll
              fetchMore={fetchMore}
            />
          </div>

          <FormActionButtons
            className="fixed bottom-0 left-0 right-0 bg-white px-4 py-2 pt-0"
            onSubmit={form.handleSubmit(handleSubmit)}
            submitLabel={btnLabel}
            onCancel={handleReset}
            isLoading={false}
            cancelLabel="Reset"
            submitIcon={BadgeCheck}
            disabledSubmitButton={!hasSelectedItems}
            disabledCancelButton={!hasSelectedItems}
          />
        </div>
      </CustomDialog>
    </div>
  );
};

export default ItemLookup;
