import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import OverbookedAlert from './OverbookedAlert';
import { ItemTypeEnum } from '@/types/order.types';

vi.mock('@/components/common/app-button', () => ({
  default: vi.fn(({ onClick, label, variant, icon: Icon }) => (
    <button onClick={onClick} data-variant={variant}>
      {label}
      {Icon && <span data-testid="mock-icon">[Icon]</span>}
    </button>
  )),
}));

describe('OverbookedAlert Component', () => {
  const mockSetOpen = vi.fn();

  vi.mock('@/assets/svg/info.svg', () => ({
    ReactComponent: vi.fn(() => <svg data-testid="info-icon" />),
  }));

  it('does not render when isOverbooked is false', () => {
    const { container } = render(
      <OverbookedAlert setOpen={mockSetOpen} isOverbooked={false} />
    );
    expect(container).toBeEmptyDOMElement();
  });

  it('renders correctly when isOverbooked is true', () => {
    render(<OverbookedAlert setOpen={mockSetOpen} isOverbooked={true} />);
    expect(screen.getByText('Info')).toBeInTheDocument();
    expect(
      screen.getByText(
        'Some of items are overbooked. Please add Sub-rentals / remove them to proceed.'
      )
    ).toBeInTheDocument();
    expect(screen.getByText('View Overbooked Items')).toBeInTheDocument();
    expect(screen.getByTestId('mock-icon')).toBeInTheDocument();
  });

  it('calls setOpen with correct parameters when button is clicked', () => {
    render(<OverbookedAlert setOpen={mockSetOpen} isOverbooked={true} />);
    fireEvent.click(screen.getByText('View Overbooked Items'));
    expect(mockSetOpen).toHaveBeenCalledWith({
      state: true,
      action: ItemTypeEnum.OVERBOOKED_ITEM,
    });
  });
});
