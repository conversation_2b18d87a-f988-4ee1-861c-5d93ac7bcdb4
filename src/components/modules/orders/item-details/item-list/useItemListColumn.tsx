import EyeIcon from '@/assets/icons/EyeIcon';
import AppButton from '@/components/common/app-button';
import ActionColumnMenu from '@/components/common/data-tables/ActionColumn';
import ReactSelect from '@/components/common/ReactSelect';
import Input<PERSON>ield from '@/components/forms/input-field';
import NumberInput<PERSON>ield from '@/components/forms/number-input-field';
import SelectWidget from '@/components/forms/select';
import { ORDER_ITEM_DETAILS_API_ROUTES } from '@/constants/api-constants';
import {
  QUANTITY_VALIDATION_RULE,
  SERIAL_VALIDATION_RULE,
  TEXT_VALIDATION_RULE,
} from '@/constants/validation-constants';
import { cn, convertToFloat, getQueryParam } from '@/lib/utils';
import {
  ItemListTypes,
  ItemTypeEnum,
  OrderInformationTypes,
} from '@/types/order.types';
import { useMemo } from 'react';
import { useFormContext, UseFormReturn } from 'react-hook-form';
import { OpenDialogType } from '.';

interface UseItemListColumnsProps {
  form: UseFormReturn<ItemListTypes>;
  itemType: any;
  itemTypeLoading: boolean;
  handleItemIdChange: (
    value: { value: string },
    rowIndex: number
  ) => Promise<void>;
  handlePriceChange: (price: string | number, index: number) => void;
  handleQuantityChange: (quantity: string | number, index: number) => void;
  toggleDelete: (id: number | null, index: number | null) => void;
  showSerialNumberColumn: boolean;
  setOpen: React.Dispatch<React.SetStateAction<OpenDialogType>>;
}

export const useItemListColumns = ({
  form,
  itemType,
  itemTypeLoading,
  handleItemIdChange,
  handlePriceChange,
  handleQuantityChange,
  toggleDelete,
  showSerialNumberColumn,
  setOpen,
}: UseItemListColumnsProps) => {
  const orderId = getQueryParam('id') as string;
  const orderForm = useFormContext<OrderInformationTypes>();
  const isDeleted = orderForm.watch('isDeleted');
  const isOrderEntryReadOnly = orderForm.watch('orderEntryReadOnly');

  return useMemo(
    () => [
      {
        accessorKey: 'itemId',
        header: 'Item ID',
        size: 100,
        enableSorting: true,
        cell: ({ row }: any) => (
          <ReactSelect
            placeholder="Select Item ID"
            className="w-[180px] bg-white"
            name={`items.${row.index}.itemId`}
            form={form}
            onSelectChange={(value) => handleItemIdChange(value, row.index)}
            url={ORDER_ITEM_DETAILS_API_ROUTES.ITEM_LOOKUP(orderId)}
            labelKey="itemId"
            valueKey="id"
            maxMenuHeight={250}
            disabled={
              isDeleted ||
              isOrderEntryReadOnly ||
              !!form.watch(`items.${row.index}.listId`)
            }
            validation={TEXT_VALIDATION_RULE}
          />
        ),
      },
      ...(showSerialNumberColumn
        ? [
            {
              accessorKey: 'serialNumber',
              header: 'Serial #',
              size: 120,
            },
          ]
        : []),

      {
        accessorKey: 'quantity',
        header: 'Qty',
        size: 120,
        cell: ({ row }: any) => {
          const itemId = form.watch(`items.${row.index}.itemId`);
          const serialNumber = form.watch(`items.${row.index}.serialNumber`);
          const isOverbooked = row.original.isOverbooked;
          const validation = serialNumber
            ? SERIAL_VALIDATION_RULE
            : itemId?.value
              ? QUANTITY_VALIDATION_RULE
              : {};

          return (
            <NumberInputField
              form={form}
              placeholder="Quantity"
              name={`items.${row.index}.quantity`}
              className={cn('w-36 h-8', isOverbooked && 'text-red-600')}
              maxLength={5}
              decimalScale={0}
              onValueChange={(value) => handleQuantityChange(value, row.index)}
              disabled={!itemId?.value || isDeleted || isOrderEntryReadOnly}
              validation={validation}
            />
          );
        },
      },
      {
        accessorKey: 'description',
        header: 'Item Description',
        size: 220,
        cell: ({ row }: any) => {
          const itemId = form.watch(`items.${row.index}.itemId`);
          return (
            <InputField
              form={form}
              placeholder="Description"
              name={`items.${row.index}.description`}
              disabled={!itemId?.value || isDeleted || isOrderEntryReadOnly}
              className="h-8"
            />
          );
        },
      },
      {
        accessorKey: 'type',
        header: 'Type',
        size: 160,
        cell: ({ row }: any) => {
          return (
            <SelectWidget
              name={`items.${row.index}.type`}
              optionsList={itemType?.data ?? []}
              parentClassName="w-[150px] bg-white"
              placeholder="Select Item Type"
              form={form}
              isLoading={itemTypeLoading}
              isClearable={false}
              disabled={true || isDeleted || isOrderEntryReadOnly}
            />
          );
        },
      },
      {
        accessorKey: 'subRental',
        header: 'SR',
        size: 60,
      },
      {
        accessorKey: 'price',
        header: 'Price',
        size: 120,
        cell: ({ row }: any) => {
          const itemId = form.watch(`items.${row.index}.itemId`);
          const validation = itemId?.value
            ? {
                validate: (val: any) =>
                  (val !== null && val !== '') || 'Required',
              }
            : {};

          return (
            <NumberInputField
              form={form}
              name={`items.${row.index}.price`}
              placeholder=""
              maxLength={9}
              prefix="$"
              className="w-28 h-8"
              fixedDecimalScale
              thousandSeparator=","
              disabled={!itemId?.value || isDeleted || isOrderEntryReadOnly}
              onValueChange={(value) => handlePriceChange(value, row.index)}
              validation={validation}
            />
          );
        },
      },
      {
        accessorKey: 'total',
        header: 'Total',
        size: 120,
        cell: ({ row }: any) =>
          convertToFloat({
            value: form.watch(`items.${row.index}.total`) ?? 0,
            prefix: '$',
          }),
      },
      {
        size: 50,
        id: 'action',
        header: 'Actions',
        cell: ({ row }: any) => (
          <ActionColumnMenu
            customEdit={
              <AppButton
                label="View Details"
                variant="neutral"
                icon={EyeIcon}
                disabled={
                  isDeleted || isOrderEntryReadOnly || !row?.original?.listId
                }
                onClick={() =>
                  setOpen({
                    state: true,
                    action: ItemTypeEnum.ADDITIONAL_ITEM_INFO,
                    value: row?.original?.listId,
                  })
                }
              />
            }
            disabled={isDeleted || isOrderEntryReadOnly}
            onDelete={() => toggleDelete(row.original.listId, row.index)}
          />
        ),
      },
    ],
    [
      showSerialNumberColumn,
      form,
      orderId,
      isDeleted,
      isOrderEntryReadOnly,
      handleItemIdChange,
      handleQuantityChange,
      itemType?.data,
      itemTypeLoading,
      handlePriceChange,
      setOpen,
      toggleDelete,
    ]
  );
};
