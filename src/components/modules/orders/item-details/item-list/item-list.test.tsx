import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import ItemList from './index';
import { useFieldArray, useFormContext, UseFormReturn } from 'react-hook-form';
import {
  useGetItemListQuery,
  useDeleteOrderItemMutation,
  useGetItemDetailsMutation,
} from '@/redux/features/orders/item-details.api';
import { useGetEnumsListQuery } from '@/redux/features/enums-api/enums-api';
import {
  useLazyGetAdditionalInfoShippingInfoBoxQuery,
  useLazyGetAdditionalInfoShippingInfoItemQuery,
} from '@/redux/features/orders/additional-info.api';
import { ItemListTypes } from '@/types/order.types';

// Mock dependencies
vi.mock('react-hook-form', () => ({
  useFormContext: vi.fn(),
  useFieldArray: vi.fn(),
}));

vi.mock('@/lib/utils', () => ({
  getQueryParam: vi.fn(() => 'order-123'),
}));

vi.mock('@/redux/features/orders/item-details.api', () => ({
  useGetItemListQuery: vi.fn(),
  useDeleteOrderItemMutation: vi.fn(),
  useGetItemDetailsMutation: vi.fn(),
}));

vi.mock('@/redux/features/enums-api/enums-api', () => ({
  useGetEnumsListQuery: vi.fn(),
}));

vi.mock('@/redux/features/orders/additional-info.api', () => ({
  useLazyGetAdditionalInfoShippingInfoBoxQuery: vi.fn(),
  useLazyGetAdditionalInfoShippingInfoItemQuery: vi.fn(),
}));

vi.mock('./ItemListTable', () => ({
  default: vi.fn(() => <div>Mocked ItemListTable</div>),
}));

vi.mock('./ItemToolbar', () => ({
  default: vi.fn(() => <div>Mocked ItemToolbar</div>),
}));

vi.mock('./OverbookedAlert', () => ({
  default: vi.fn(() => <div>Mocked OverbookedAlert</div>),
}));

vi.mock('@/components/common/app-button', () => ({
  default: vi.fn(({ onClick, label, disabled }) => (
    <button onClick={onClick} disabled={disabled}>
      {label}
    </button>
  )),
}));

vi.mock('@/components/common/app-confirmation-modal', () => ({
  default: vi.fn(
    ({ open, handleSubmit, handleCancel }) =>
      open && (
        <div>
          <button onClick={handleSubmit}>Confirm Delete</button>
          <button onClick={handleCancel}>Cancel Delete</button>
        </div>
      )
  ),
}));

describe('ItemList Component', () => {
  const mockForm = {
    control: {},
    watch: vi.fn(),
    getValues: vi.fn(),
    setValue: vi.fn(),
    reset: vi.fn(),
    formState: { errors: {} },
  };

  const mockOrderForm = {
    watch: vi.fn((field) => {
      if (field === 'isDeleted') return false;
      if (field === 'orderEntryReadOnly') return false;
      if (field === 'userDefaultStoreInfo')
        return { showSerialNumberColumn: true };
      return undefined;
    }),
    getValues: vi.fn(() => ({
      userDefaultStoreInfo: { showSerialNumberColumn: true },
    })),
  };

  const mockFields = [
    {
      id: '1',
      itemId: { value: 'item-1' },
      description: 'Item 1',
      quantity: 1,
      price: 10,
      total: 10,
    },
    {
      id: '2',
      itemId: null,
      description: '',
      quantity: '',
      price: '0',
      total: 0,
    },
  ];

  const mockFieldArray = {
    fields: mockFields,
    append: vi.fn(),
    remove: vi.fn(),
  };

  const mockItemData = {
    data: [
      {
        listId: 1,
        itemId: { value: 'item-1', label: 'ITEM-1' },
        description: 'Item 1',
        quantity: 1,
        price: 10,
        total: 10,
        type: 'STANDARD',
      },
    ],
    isOverbooked: false,
    totalWeight: 5,
    totalCube: 2,
  };

  beforeEach(() => {
    vi.clearAllMocks();

    // Mock useFormContext
    (useFormContext as any)
      .mockReturnValueOnce(mockOrderForm) // First call for deletedItemForm
      .mockReturnValue(mockOrderForm); // Subsequent calls

    // Mock useFieldArray
    (useFieldArray as any).mockReturnValue(mockFieldArray);

    // Mock API hooks
    (useGetItemListQuery as any).mockReturnValue({
      data: mockItemData,
      isLoading: false,
    });

    (useDeleteOrderItemMutation as any).mockReturnValue([
      vi.fn(),
      { isLoading: false },
    ]);

    (useGetItemDetailsMutation as any).mockReturnValue([vi.fn(), {}]);

    (useGetEnumsListQuery as any).mockReturnValue({
      data: [{ value: 'STANDARD', label: 'Standard' }],
      isLoading: false,
    });

    (useLazyGetAdditionalInfoShippingInfoBoxQuery as any).mockReturnValue([
      vi.fn(),
      {},
    ]);
    (useLazyGetAdditionalInfoShippingInfoItemQuery as any).mockReturnValue([
      vi.fn(),
      {},
    ]);
  });

  it('renders the component with all main elements', () => {
    render(<ItemList form={mockForm as UseFormReturn<ItemListTypes> | any} />);
    expect(screen.getByText('Item List')).toBeInTheDocument();
    expect(screen.getByText('Mocked ItemToolbar')).toBeInTheDocument();
    expect(screen.getByText('Mocked ItemListTable')).toBeInTheDocument();
    expect(screen.getByText('+ Add New')).toBeInTheDocument();
    expect(screen.getByText('Total Weight')).toBeInTheDocument();
    expect(screen.getByText('Total Cube')).toBeInTheDocument();
  });

  it('disables "Add New" button when order is deleted', () => {
    (mockOrderForm.watch as any).mockImplementation((field: any) =>
      field === 'isDeleted' ? true : false
    );
    render(<ItemList form={mockForm as UseFormReturn<ItemListTypes> | any} />);
    expect(screen.getByText('+ Add New')).toBeDisabled();
  });

  it('disables "Add New" button when order is read-only', () => {
    (mockOrderForm.watch as any).mockImplementation((field: any) =>
      field === 'orderEntryReadOnly' ? true : false
    );
    render(<ItemList form={mockForm as UseFormReturn<ItemListTypes> | any} />);
    expect(screen.getByText('+ Add New')).toBeDisabled();
  });

  it('calls append when "Add New" is clicked and last item has values', () => {
    render(<ItemList form={mockForm as UseFormReturn<ItemListTypes> | any} />);
    fireEvent.click(screen.getByText('+ Add New'));
    expect(mockFieldArray.append);
  });

  it('handles delete confirmation flow', async () => {
    const mockDeleteItem = vi.fn().mockResolvedValue({});
    (useDeleteOrderItemMutation as any).mockReturnValue([
      mockDeleteItem,
      { isLoading: false },
    ]);
    render(<ItemList form={mockForm as UseFormReturn<ItemListTypes> | any} />);
    fireEvent.click(screen.getByText('Mocked ItemListTable'), {
      target: { getAttribute: () => 'delete-button' },
    });
    await waitFor(() => {
      expect(mockDeleteItem);
    });
  });

  it('resets form with default values when data loads', () => {
    render(<ItemList form={mockForm as UseFormReturn<ItemListTypes> | any} />);
    expect(mockForm.reset);
  });

  it('renders OverbookedAlert when items are overbooked', () => {
    (useGetItemListQuery as any).mockReturnValue({
      data: { ...mockItemData, isOverbooked: true },
      isLoading: false,
    });
    render(<ItemList form={mockForm as UseFormReturn<ItemListTypes> | any} />);
    expect(screen.getByText('Mocked OverbookedAlert')).toBeInTheDocument();
  });

  it('calls onDefaultValuesLoaded when provided', () => {
    const mockOnDefaultsLoaded = vi.fn();
    render(
      <ItemList
        form={mockForm as UseFormReturn<ItemListTypes> | any}
        onDefaultValuesLoaded={mockOnDefaultsLoaded}
      />
    );
    expect(mockOnDefaultsLoaded);
  });

  it('shows loading state when data is loading', () => {
    (useGetItemListQuery as any).mockReturnValue({
      data: undefined,
      isLoading: true,
    });
    render(<ItemList form={mockForm as UseFormReturn<ItemListTypes> | any} />);
    expect(screen.getByText('Mocked ItemListTable')).toBeInTheDocument();
  });
});
