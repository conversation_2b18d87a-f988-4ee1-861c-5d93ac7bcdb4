import AppButton from '@/components/common/app-button';
import MonthPickerInfo from '@/components/common/app-month-picker';
import CalendarView from '@/components/common/CalendarView';
import DataTable from '@/components/common/data-tables';
import ActionColumnMenu from '@/components/common/data-tables/ActionColumn';
import SwitchField from '@/components/common/switch';
import AutoCompleteDropdown from '@/components/forms/auto-complete-dropdown';
import InputField from '@/components/forms/input-field';
import RadioField from '@/components/forms/radio-field';
import SelectWidget from '@/components/forms/select';
import { ITEMS_API_ROUTES } from '@/constants/api-constants';
import { ROUTES } from '@/constants/routes-constants';
import { formatDateWithTimezone } from '@/lib/formatDateWithTimezone';
import {
  cn,
  convertToFloat,
  DATE_FORMAT_YYYYMMDD,
  formatDate,
  generateLabelValuePairs,
  getStorageValue,
} from '@/lib/utils';

import {
  useGetAvailabilitycalDatesQuery,
  useGetAvailabilitycalOrdersMutation,
} from '@/redux/features/orders/order.api';
import { useGetStoreLocationsQuery } from '@/redux/features/store/store.api';
import { AvailabiltyCalendarInfoTyps } from '@/types/availabilty-calendar-info.types';
import { SortingStateType } from '@/types/common.types';
import { ColumnDef } from '@tanstack/react-table';
import dayjs from 'dayjs';
import { Search, SquareArrowOutUpRight } from 'lucide-react';
import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';

interface AvailabiltyCalendarInfoProps {
  handlechange: () => void;
}

const AvailabiltyCalendarInfo = ({
  handlechange,
}: AvailabiltyCalendarInfoProps) => {
  const navigation = useNavigate();
  const [sorting, setSorting] = useState<SortingStateType[]>([]);

  const form = useFormContext<AvailabiltyCalendarInfoTyps>();
  const info = form.watch();
  const itemIdValue = info?.itemId?.value;
  const tz = getStorageValue('timeZoneOffset') || '';
  const currentDate = formatDateWithTimezone({ tz });

  const payload = useMemo(() => {
    return {
      storeLocationId: Number(info.storeLocationId),
      date: currentDate,
      month: info?.date,
      includeQuotes: info?.includeQuotes === 'true',
      includeSubRentAndPO: info?.includeSubRentAndPO,
      includePastOrders: info?.includePastOrders,
    };
  }, [
    currentDate,
    info.date,
    info?.includePastOrders,
    info?.includeQuotes,
    info?.includeSubRentAndPO,
    info?.storeLocationId,
  ]);

  // get avalability details
  const { data: availabilityData } = useGetAvailabilitycalDatesQuery(
    { ...payload, itemId: itemIdValue },
    {
      skip: !itemIdValue,
      refetchOnMountOrArgChange: true,
    }
  );

  const availabilitySlotData = useMemo(() => {
    return availabilityData?.data?.availabilities?.map(
      ({ date, quantity }: { date: string; quantity: number }) => ({
        date,
        label: quantity,
        labelClassName: quantity <= 0 ? 'text-red-600' : '',
      })
    );
  }, [availabilityData?.data?.availabilities]);

  const [
    getAvailabilitycalOrdes,
    { data: availabilitycalOrdesData, isLoading: orderLoading },
  ] = useGetAvailabilitycalOrdersMutation();

  const defaultValues = useMemo(() => {
    const dataValue = availabilityData?.data;
    return {
      itemId: info?.itemId,
      storeLocationId: info?.storeLocationId,
      month: info?.date,
      date: info?.date ?? currentDate,
      description: dataValue?.description,
      cleanupDays: dataValue?.cleanupDays || '0',
      includeQuotes: info?.includeQuotes,
      unitPrice: convertToFloat({
        value: dataValue?.unitPrice ?? 0,
        prefix: '$',
      }),
      owned: dataValue?.owned || '0',
      availableToday: dataValue?.availableToday || '0',
      includeSubRentAndPO: info?.includeSubRentAndPO,
      includePastOrders: info?.includePastOrders,
    };
  }, [
    availabilityData?.data,
    currentDate,
    info?.includePastOrders,
    info?.includeQuotes,
    info?.includeSubRentAndPO,
    info?.itemId,
    info?.date,
    info?.storeLocationId,
  ]);

  const prevDefaultsRef = useRef<string>();
  useEffect(() => {
    const defaultsString = JSON.stringify(defaultValues);
    if (defaultsString !== prevDefaultsRef.current) {
      prevDefaultsRef.current = defaultsString;
      form.reset(defaultValues);
    }
  }, [defaultValues, form]);

  // navigate on specific order
  const navigateToOrder = useCallback(
    (id: number) => {
      navigation(`${ROUTES.EDIT_ORDERS}?id=${id}&tab=information`);
      handlechange();
    },
    [handlechange, navigation]
  );

  const columns: ColumnDef<AvailabiltyCalendarInfoTyps>[] = useMemo(
    () => [
      {
        accessorKey: 'orderNo',
        header: 'Order #',
        enableSorting: true,
      },
      { accessorKey: 'orderType', header: 'Order Type', enableSorting: true },
      {
        accessorKey: 'quantity',
        header: 'Quantity',
        enableSorting: true,
        size: 80,
      },
      {
        accessorKey: 'deliveryDate',
        size: 160,
        header: 'Delivery Date',
        cell: ({ row }) => formatDate(row?.original?.deliveryDate),
        enableSorting: true,
      },
      {
        accessorKey: 'pickupDate',
        header: 'Pickup Date',
        cell: ({ row }) => formatDate(row?.original?.pickupDate),
        enableSorting: true,
      },
      {
        id: 'action',
        size: 80,
        header: 'Actions',
        cell: ({ row }) => (
          <ActionColumnMenu
            contentClassName="z-[99] w-fit p-2"
            dropdownMenuList={[
              {
                label: (
                  <div className="flex items-center gap-2">
                    Open Order
                    <SquareArrowOutUpRight className="w-4 h-4" />
                  </div>
                ),
                onClick: () => navigateToOrder(row.original.orderId),
              },
            ]}
          />
        ),
      },
    ],
    [navigateToOrder]
  );

  // location list
  const { data: locationData, isLoading: locationLoading } =
    useGetStoreLocationsQuery();

  const locationList = useMemo(() => {
    const list = generateLabelValuePairs({
      data: locationData?.data,
      labelKey: 'location',
      valueKey: 'id',
    });

    return list.length
      ? [...[{ label: 'All Locations', value: '0' }], ...list]
      : [];
  }, [locationData?.data]);

  const filterOptions = [
    {
      label: 'Include Sub Rentals/Purchase Orders',
      name: 'includeSubRentAndPO',
    },
    { label: 'Include Past Orders', name: 'includePastOrders' },
  ];

  const fields = [
    {
      name: 'description',
      label: 'Item Description',
      placeholder: 'Item Description',
      pClassName: 'col-span-2',
    },
    {
      name: 'cleanupDays',
      label: 'Clean Up Days',
      placeholder: 'Clean Up Days',
    },
    {
      name: 'unitPrice',
      label: 'Unit Price',
      placeholder: 'Unit Price',
    },
    {
      name: 'owned',
      label: 'Owned',
      placeholder: 'Owned',
    },
    {
      name: 'availableToday',
      label: 'Available Today',
      placeholder: 'Available Today',
    },
  ];

  // handle on date change
  const handleOnDateSelect = useCallback(
    async (selectedDay?: Date | null | string) => {
      const date = formatDate(selectedDay ?? '', DATE_FORMAT_YYYYMMDD);
      form.setValue('date', date);
      const selectedDate = date || info?.date;
      const payload = {
        itemId: info?.itemId?.value,
        storeLocationId: info?.storeLocationId,
        month: selectedDate,
        includeQuotes: info?.includeQuotes === 'true',
        includeSubRentAndPO: info?.includeSubRentAndPO,
        includePastOrders: info?.includePastOrders,
        date: selectedDate,
        sortBy: sorting?.at(1)?.id,
        sortAscending: sorting?.at(1)?.desc,
      };
      if (!itemIdValue) return;
      getAvailabilitycalOrdes({
        ...payload,
        itemId: info?.itemId?.value,
        sortBy: sorting?.at(0)?.id,
        sortAscending: sorting?.at(0)?.desc,
      });
    },
    [
      form,
      getAvailabilitycalOrdes,
      info?.date,
      info?.includePastOrders,
      info?.includeQuotes,
      info?.includeSubRentAndPO,
      info?.itemId?.value,
      info?.storeLocationId,
      itemIdValue,
      sorting,
    ]
  );

  // get the  order list base on filter changes
  useEffect(() => {
    if (
      (itemIdValue && info?.date) ||
      (info?.includePastOrders || info?.includeQuotes,
      info?.includeSubRentAndPO ||
        info?.itemId?.value ||
        info?.storeLocationId ||
        sorting)
    ) {
      handleOnDateSelect(info?.date);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    currentDate,
    info?.date,
    info?.includePastOrders,
    info?.includeQuotes,
    info?.includeSubRentAndPO,
    info?.itemId?.value,
    info?.storeLocationId,
    itemIdValue,
    sorting,
  ]);

  const handleOnMonthChange = useCallback(
    (date?: Date | string) => {
      const isSameMonth = dayjs(date)?.isSame(dayjs(), 'month');
      form.setValue(
        'date',
        formatDate(
          isSameMonth ? currentDate : (date ?? ''),
          DATE_FORMAT_YYYYMMDD
        )
      );
    },
    [currentDate, form]
  );

  return (
    <>
      <div className="p-3 border rounded-md grid grid-cols-3 gap-6 divide-x-[1px]">
        <div className="col-span-2 grid grid-cols-5 gap-4">
          <div className="col-span-2 flex items-center gap-3">
            <AutoCompleteDropdown
              name="itemId"
              label="Item ID"
              placeholder="Select item ID"
              form={form}
              url={ITEMS_API_ROUTES.ALL}
              labelKey="itemId"
              valueKey="id"
              sortBy="itemId"
              onSelectChange={(value) => {
                form.setValue('itemId', {
                  label: value?.label,
                  value: value?.value?.toString(),
                });
              }}
              className="z-[11]"
            />
            <AppButton
              label="Items"
              className="mt-8 xl:w-fit"
              icon={Search}
              iconClassName="w-5 h-5"
              onClick={handlechange}
            />
          </div>
          <SelectWidget
            form={form}
            optionsList={locationList}
            name="storeLocationId"
            label="Location"
            placeholder="Select Location"
            isLoading={locationLoading}
            menuPosition="absolute"
            isClearable={false}
            parentClassName="col-span-2 z-[15]"
          />
          <MonthPickerInfo
            form={form}
            name="date"
            label="Date"
            placeholder="Select Month"
            onMonthChange={handleOnMonthChange}
          />
          <div className="col-span-5 grid grid-cols-3 gap-4 pt-2 2xl:grid-cols-6">
            {fields.map((field) => (
              <InputField
                key={field.name}
                name={field.name as any}
                form={form}
                label={field.label}
                placeholder={field.placeholder}
                disabled
                pClassName={cn('gap-1', field.pClassName)}
              />
            ))}
          </div>
        </div>
        <div className="space-y-5 mt-3 ps-6">
          <RadioField
            form={form}
            name="includeQuotes"
            options={[
              { label: 'Orders', value: 'false' },
              {
                label: 'Orders & Quotes',
                value: 'true',
              },
            ]}
            optionsPerRow={2}
          />
          {filterOptions?.map((item, index) => (
            <SwitchField
              key={`${item.name}-${index}`}
              name={item.name as any}
              labelEnd={item.label}
              form={form}
              className="w-fit"
            />
          ))}
        </div>
      </div>
      <div className="grid grid-cols-3 gap-3 xl:gap-4 pt-6 pb-4">
        <CalendarView
          data={availabilitySlotData || []}
          value={dayjs(info?.date)?.toDate()}
          onMonthChange={handleOnMonthChange}
          onSelect={handleOnDateSelect}
          disabled={(date: Date) => {
            const current = dayjs(date)?.startOf('day');
            // Condition 1: allow everything if past orders are allowed
            if (info?.includePastOrders) return false;
            // Condition 2: disable if the date is in the past (less than today)
            return current?.isBefore(currentDate);
          }}
        />
        <div className="col-span-2 grid grid-cols-1">
          <DataTable
            data={availabilitycalOrdesData?.data?.orders || []}
            totalItems={availabilitycalOrdesData?.data?.orders?.length}
            isLoading={orderLoading}
            columns={columns}
            tableClassName="max-h-[350px] 2xl:max-h-[400px] overflow-auto"
            enablePagination={false}
            sorting={sorting}
            setSorting={setSorting}
          />
        </div>
      </div>
    </>
  );
};

export default memo(AvailabiltyCalendarInfo);
