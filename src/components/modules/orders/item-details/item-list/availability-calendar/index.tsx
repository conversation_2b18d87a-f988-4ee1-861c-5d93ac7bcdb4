import BreadcrumbDialogRenderer from '@/components/common/BreadcrumbDialogRenderer';
import { formatDateWithTimezone } from '@/lib/formatDateWithTimezone';
import {
  AvailabiltyCalendarInfoTyps,
  ItemType,
} from '@/types/availabilty-calendar-info.types';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import ItemLookup from '../../overbooked-item-info/ItemLookup';
import AvailabiltyCalendarInfo from './availabilty-calendar-info';
import { getStorageValue } from '@/lib/utils';

const AvailabilityCalendar = ({
  open = false,
  onOpenChange = () => {},
  itemId,
  isModal = true,
}: {
  open?: boolean;
  onOpenChange?: () => void;
  itemId?: ItemType;
  isModal?: boolean;
}) => {
  const [activeTab, setActiveTab] = useState('availability-calendar');
  const handleChangeTab = useCallback((tab?: string) => {
    setActiveTab(tab ?? 'availability-calendar');
  }, []);

  const form = useForm<AvailabiltyCalendarInfoTyps>();
  const tz = getStorageValue('timeZoneOffset') || '';

  const currentDate = formatDateWithTimezone({ tz });
  const defaultValues = useMemo(() => {
    return {
      itemId: { label: itemId?.label ?? '', value: itemId?.value ?? '' },
      storeLocationId: '0',
      month: currentDate,
      date: currentDate,
      includeQuotes: 'true',
      includeSubRentAndPO: true,
      includePastOrders: false,
    };
  }, [currentDate, itemId?.label, itemId?.value]);

  useEffect(() => {
    if (defaultValues) {
      form.reset(defaultValues);
    }
  }, [defaultValues, form, itemId]);

  // tab list
  const listItems = useMemo(() => {
    return [
      {
        label: 'Availability Calendar',
        value: 'availability-calendar',
        content: (
          <AvailabiltyCalendarInfo
            handlechange={() => handleChangeTab('item-lookup')}
          />
        ),
      },
      {
        label: 'Item Lookup',
        value: 'item-lookup',
        content: (
          <ItemLookup handleChange={() => handleChangeTab()} form={form} />
        ),
      },
    ];
  }, [form, handleChangeTab]);

  return (
    <FormProvider {...form}>
      {isModal ? (
        <BreadcrumbDialogRenderer
          activeTab={activeTab}
          isOpen={open}
          listItems={listItems}
          onOpenChange={onOpenChange}
          setActiveTab={handleChangeTab}
          className="max-w-[85%] 2xl:max-w-[70%] overflow-x-auto"
          contentClassName="h-[470px] 2xl:h-[680px] overflow-y-auto"
        />
      ) : (
        <>
          <AvailabiltyCalendarInfo
            handlechange={() => handleChangeTab('item-lookup')}
          />
          <BreadcrumbDialogRenderer
            activeTab={activeTab}
            isOpen={!isModal && ['item-lookup'].includes(activeTab)}
            listItems={[
              {
                label: 'Item Lookup',
                value: 'item-lookup',
                content: (
                  <ItemLookup
                    handleChange={() => setActiveTab('')}
                    form={form}
                  />
                ),
              },
            ]}
            onOpenChange={() => setActiveTab('')}
            setActiveTab={() => setActiveTab('')}
            className="max-w-[70%] 2xl:max-w-[60%] overflow-x-auto"
            contentClassName="h-[470px] 2xl:h-[680px] overflow-y-auto"
          />
        </>
      )}
    </FormProvider>
  );
};

export default AvailabilityCalendar;
