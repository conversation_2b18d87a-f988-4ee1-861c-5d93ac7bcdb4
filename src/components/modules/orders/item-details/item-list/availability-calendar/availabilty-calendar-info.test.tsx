import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import AvailabiltyCalendarInfo from './availabilty-calendar-info';
import { useFormContext } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import {
  useGetAvailabilitycalDatesQuery,
  useGetAvailabilitycalOrdersMutation,
} from '@/redux/features/orders/order.api';
import { useGetStoreLocationsQuery } from '@/redux/features/store/store.api';

// Mock all the external dependencies
vi.mock('react-hook-form', () => ({
  useFormContext: vi.fn(),
}));

vi.mock('react-router-dom', () => ({
  useNavigate: vi.fn(),
}));

vi.mock('@/components/common/app-button', () => ({
  default: vi.fn(({ onClick }) => (
    <button onClick={onClick}>Mock Button</button>
  )),
}));

vi.mock('@/components/common/app-month-picker', () => ({
  default: vi.fn(() => <div>Month Picker</div>),
}));

vi.mock('@/components/common/CalendarView', () => ({
  default: vi.fn(({ onSelect }) => (
    <div onClick={() => onSelect(new Date())}>Calendar View</div>
  )),
}));

vi.mock('@/components/common/data-tables', () => ({
  default: vi.fn(() => <div>Data Table</div>),
}));

vi.mock('@/components/common/switch', () => ({
  default: vi.fn(() => <div>Switch</div>),
}));

vi.mock('@/components/forms/auto-complete-dropdown', () => ({
  default: vi.fn(() => <div>AutoComplete Dropdown</div>),
}));

vi.mock('@/components/forms/input-field', () => ({
  default: vi.fn(() => <div>Input Field</div>),
}));

vi.mock('@/components/forms/radio-field', () => ({
  default: vi.fn(() => <div>Radio Field</div>),
}));

vi.mock('@/components/forms/select', () => ({
  default: vi.fn(() => <div>Select Widget</div>),
}));

// Mock API hooks
vi.mock('@/redux/features/orders/order.api', () => ({
  useGetAvailabilitycalDatesQuery: vi.fn(),
  useGetAvailabilitycalOrdersMutation: vi.fn(),
}));

vi.mock('@/redux/features/store/store.api', () => ({
  useGetStoreLocationsQuery: vi.fn(),
}));

// Mock utility functions
vi.mock('@/lib/formatDateWithTimezone', () => ({
  formatDateWithTimezone: vi.fn(() => '2023-01-01'),
}));

vi.mock('@/lib/utils', () => ({
  cn: vi.fn(),
  convertToFloat: vi.fn(),
  DATE_FORMAT_YYYYMMDD: 'YYYY-MM-DD',
  formatDate: vi.fn(),
  generateLabelValuePairs: vi.fn(() => []),
  getStorageValue: vi.fn(() => ''),
}));

describe('AvailabiltyCalendarInfo', () => {
  const mockHandleChange = vi.fn();
  const mockNavigate = vi.fn();
  const mockForm = {
    watch: vi.fn(),
    reset: vi.fn(),
    setValue: vi.fn(),
    getValues: vi.fn(),
  };

  beforeEach(() => {
    // Reset all mocks before each test
    vi.clearAllMocks();

    // Setup default mock implementations
    (useNavigate as any).mockReturnValue(mockNavigate);
    (useFormContext as any).mockReturnValue(mockForm);

    mockForm.watch.mockImplementation(() => ({
      itemId: { value: '123', label: 'Test Item' },
      storeLocationId: '1',
      date: '2023-01-01',
      includeQuotes: 'false',
      includeSubRentAndPO: false,
      includePastOrders: false,
    }));

    // Mock API responses
    (useGetAvailabilitycalDatesQuery as any).mockReturnValue({
      data: {
        data: {
          availabilities: [
            { date: '2023-01-01', quantity: 5 },
            { date: '2023-01-02', quantity: 0 },
          ],
          description: 'Test Description',
          cleanupDays: '2',
          unitPrice: 100,
          owned: '10',
          availableToday: '5',
        },
      },
    });

    (useGetAvailabilitycalOrdersMutation as any).mockReturnValue([
      vi.fn(),
      {
        data: {
          data: {
            orders: [
              {
                orderNo: 'ORD-123',
                orderType: 'Rental',
                quantity: 2,
                deliveryDate: '2023-01-01',
                pickupDate: '2023-01-10',
                orderId: 123,
              },
            ],
          },
        },
        isLoading: false,
      },
    ]);

    (useGetStoreLocationsQuery as any).mockReturnValue({
      data: {
        data: [
          { id: 1, location: 'Location 1' },
          { id: 2, location: 'Location 2' },
        ],
      },
      isLoading: false,
    });
  });

  it('renders without crashing', () => {
    const isRendered = render(
      <AvailabiltyCalendarInfo handlechange={mockHandleChange} />
    );
    expect(isRendered);
  });

  it('displays item information when data is available', async () => {
    render(<AvailabiltyCalendarInfo handlechange={mockHandleChange} />);
    await waitFor(() => {
      expect(mockForm.reset).toHaveBeenCalled();
    });
  });

  it('calls handleOnDateSelect when date is selected', async () => {
    render(<AvailabiltyCalendarInfo handlechange={mockHandleChange} />);
    const calendar = screen.getByText('Calendar View');
    fireEvent.click(calendar);
    await waitFor(() => {
      expect(mockForm.setValue);
    });
  });

  it('calls handlechange when Items button is clicked', () => {
    render(<AvailabiltyCalendarInfo handlechange={mockHandleChange} />);
    const button = screen.getByText('Mock Button');
    fireEvent.click(button);
    expect(mockHandleChange).toHaveBeenCalled();
  });

  it('displays loading state when location data is loading', () => {
    (useGetStoreLocationsQuery as any).mockReturnValue({
      isLoading: true,
    });
    render(<AvailabiltyCalendarInfo handlechange={mockHandleChange} />);
    expect(screen.getByText('Select Widget')).toBeInTheDocument();
  });

  it('handles API errors gracefully', async () => {
    (useGetAvailabilitycalDatesQuery as any).mockReturnValue({
      error: new Error('API Error'),
    });
    const handledApiError = render(
      <AvailabiltyCalendarInfo handlechange={mockHandleChange} />
    );
    expect(handledApiError);
  });

  it('navigates to order when action is clicked', async () => {
    render(<AvailabiltyCalendarInfo handlechange={mockHandleChange} />);
    await waitFor(() => {
      expect(mockNavigate).not.toHaveBeenCalled();
    });
  });

  it('updates form values when month changes', () => {
    render(<AvailabiltyCalendarInfo handlechange={mockHandleChange} />);
    expect(mockForm.setValue).toHaveBeenCalled();
  });

  it('disables past dates when includePastOrders is false', () => {
    render(<AvailabiltyCalendarInfo handlechange={mockHandleChange} />);
    expect(screen.getByText('Calendar View')).toBeInTheDocument();
  });
});
