import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import AvailabilityCalendar from './index';
import { useForm } from 'react-hook-form';
import { formatDateWithTimezone } from '@/lib/formatDateWithTimezone';
import { getStorageValue } from '@/lib/utils';

// Mock child components
vi.mock('@/components/common/BreadcrumbDialogRenderer', () => ({
  default: vi.fn(({ isOpen, listItems }) => (
    <div>
      {isOpen && (
        <div>
          {listItems.map((item: any) => (
            <div key={item.value}>{item.content}</div>
          ))}
        </div>
      )}
    </div>
  )),
}));

vi.mock('./availabilty-calendar-info', () => ({
  default: vi.fn(({ handlechange }) => (
    <div>
      Availability Calendar Info
      <button onClick={() => handlechange()}>Switch to Item Lookup</button>
    </div>
  )),
}));

vi.mock('../../overbooked-item-info/ItemLookup', () => ({
  default: vi.fn(({ handleChange }) => (
    <div>
      Item Lookup
      <button onClick={() => handleChange()}>Switch to Calendar</button>
    </div>
  )),
}));

// Mock utility functions
vi.mock('@/lib/formatDateWithTimezone', () => ({
  formatDateWithTimezone: vi.fn(() => '2023-01-01'),
}));

vi.mock('@/lib/utils', () => ({
  getStorageValue: vi.fn(() => 'UTC'),
}));

// Mock react-hook-form
vi.mock('react-hook-form', async () => {
  const actual = await vi.importActual('react-hook-form');
  return {
    ...actual,
    useForm: vi.fn(),
    FormProvider: vi.fn(({ children }) => children),
  };
});

describe('AvailabilityCalendar', () => {
  const mockOnOpenChange = vi.fn();
  const mockFormMethods = {
    watch: vi.fn(),
    reset: vi.fn(),
    setValue: vi.fn(),
    getValues: vi.fn(),
    handleSubmit: vi.fn(),
    formState: { errors: {} },
  };

  beforeEach(() => {
    vi.clearAllMocks();
    (useForm as any).mockReturnValue(mockFormMethods);
    mockFormMethods.watch.mockImplementation(() => ({}));
  });

  it('renders in modal mode with default tab', () => {
    render(
      <AvailabilityCalendar
        open={true}
        onOpenChange={mockOnOpenChange}
        isModal={true}
      />
    );
    expect(screen.getByText('Availability Calendar Info')).toBeInTheDocument();
  });

  it('switches between tabs when handleChange is called', async () => {
    render(
      <AvailabilityCalendar
        open={true}
        onOpenChange={mockOnOpenChange}
        isModal={true}
      />
    );
    expect(screen.getByText('Availability Calendar Info')).toBeInTheDocument();
    fireEvent.click(screen.getByText('Switch to Item Lookup'));
    await waitFor(() => {
      expect(screen.getByText('Item Lookup')).toBeInTheDocument();
    });
  });

  it('initializes form with default values', () => {
    const itemId = { label: 'Test Item', value: '123' };
    render(
      <AvailabilityCalendar
        open={true}
        onOpenChange={mockOnOpenChange}
        itemId={itemId}
        isModal={true}
      />
    );
    expect(mockFormMethods.reset).toHaveBeenCalledWith({
      itemId: { label: 'Test Item', value: '123' },
      storeLocationId: '0',
      month: '2023-01-01',
      date: '2023-01-01',
      includeQuotes: 'true',
      includeSubRentAndPO: true,
      includePastOrders: false,
    });
  });

  it('resets form when itemId prop changes', () => {
    const { rerender } = render(
      <AvailabilityCalendar
        open={true}
        onOpenChange={mockOnOpenChange}
        itemId={{ label: 'Item 1', value: '1' }}
        isModal={true}
      />
    );
    rerender(
      <AvailabilityCalendar
        open={true}
        onOpenChange={mockOnOpenChange}
        itemId={{ label: 'Item 2', value: '2' }}
        isModal={true}
      />
    );
    expect(mockFormMethods.reset).toHaveBeenCalledTimes(2);
  });

  it('renders in non-modal mode correctly', () => {
    render(
      <AvailabilityCalendar
        open={true}
        onOpenChange={mockOnOpenChange}
        isModal={false}
      />
    );
    expect(screen.getByText('Availability Calendar Info')).toBeInTheDocument();
    expect(screen.queryByText('Item Lookup')).not.toBeInTheDocument();
  });

  it('handles tab switching in non-modal mode', async () => {
    render(
      <AvailabilityCalendar
        open={true}
        onOpenChange={mockOnOpenChange}
        isModal={false}
      />
    );
    fireEvent.click(screen.getByText('Switch to Item Lookup'));
    await waitFor(() => {
      expect(screen.getByText('Item Lookup')).toBeInTheDocument();
    });
    fireEvent.click(screen.getByText('Switch to Calendar'));
    await waitFor(() => {
      expect(screen.queryByText('Item Lookup')).not.toBeInTheDocument();
    });
  });

  it('uses current date from timezone', () => {
    (formatDateWithTimezone as any).mockReturnValue('2023-06-15');
    (getStorageValue as any).mockReturnValue('America/New_York');
    render(
      <AvailabilityCalendar
        open={true}
        onOpenChange={mockOnOpenChange}
        isModal={true}
      />
    );
    expect(formatDateWithTimezone).toHaveBeenCalledWith({
      tz: 'America/New_York',
    });
    expect(mockFormMethods.reset).toHaveBeenCalledWith(
      expect.objectContaining({
        month: '2023-06-15',
        date: '2023-06-15',
      })
    );
  });

  it('does not render dialog when open is false in modal mode', () => {
    render(
      <AvailabilityCalendar
        open={false}
        onOpenChange={mockOnOpenChange}
        isModal={true}
      />
    );
    expect(
      screen.queryByText('Availability Calendar Info')
    ).not.toBeInTheDocument();
  });
});
