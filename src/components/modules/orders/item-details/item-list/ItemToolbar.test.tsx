import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import ItemToolbar from './ItemToolbar';
import {
  useGetItemListQuery,
  useUpdateSurgeChargeMutation,
} from '@/redux/features/orders/item-details.api';
import { useFormContext } from 'react-hook-form';
import useOptionList from '@/hooks/useOptionList';

// Mock the necessary hooks and components
vi.mock('@/redux/features/orders/item-details.api', () => ({
  useGetItemListQuery: vi.fn(),
  useUpdateSurgeChargeMutation: vi.fn(),
}));

vi.mock('react-hook-form', () => ({
  useFormContext: vi.fn(),
}));

vi.mock('./ItemLookup', () => ({
  default: vi.fn(() => <div>Mocked ItemLookup</div>),
}));

vi.mock('@/hooks/useOptionList', () => ({
  default: vi.fn(),
}));

vi.mock('@/components/ui/select', () => ({
  Select: ({ children, onValueChange, value }: any) => (
    <div>
      <button onClick={() => onValueChange('test-value')}>
        Trigger Select
      </button>
      <div>Current Value: {value}</div>
      {children}
    </div>
  ),
  SelectTrigger: ({ children, disabled }: any) => (
    <div data-disabled={disabled}>{children}</div>
  ),
  SelectContent: ({ children }: any) => <div>{children}</div>,
  SelectItem: ({ children, value }: any) => (
    <div data-value={value}>{children}</div>
  ),
  SelectValue: () => <div>Select Value</div>,
}));

vi.mock('@/components/common/app-button', () => ({
  default: ({ onClick, label, disabled }: any) => (
    <button onClick={onClick} disabled={disabled}>
      {label}
    </button>
  ),
}));

vi.mock('@/components/common/DropdownMenuItems', () => ({
  default: ({ menuItems }: any) => (
    <div>
      {menuItems.map((item: any, index: any) => (
        <button key={index} onClick={item.onClick} disabled={item.disabled}>
          {item.label}
        </button>
      ))}
    </div>
  ),
}));

describe('ItemToolbar Component', () => {
  const mockAppend = vi.fn();
  const mockSetOpen = vi.fn();
  const mockRowSelection = {};
  const mockForm = {
    watch: vi.fn(),
    setValue: vi.fn(),
  };
  const mockItems = [
    {
      id: '1',
      itemId: 'ITEM001',
      description: 'Test Item 1',
      price: 10.99,
      itemType: 'RENTAL_ITEM',
      subRental: 'No',
    },
    {
      id: '2',
      itemId: 'ITEM002',
      description: 'Test Item 2',
      price: 15.99,
      itemType: 'KIT_ITEM',
      subRental: 'No',
    },
  ];
  const mockSurgeRates = [
    { label: 'Rate 1', value: '1' },
    { label: 'Rate 2', value: '2' },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    (useFormContext as any).mockReturnValue(mockForm);
    (useGetItemListQuery as any).mockReturnValue({
      data: { data: mockItems },
    });
    (useUpdateSurgeChargeMutation as any).mockReturnValue([
      vi.fn(),
      { isLoading: false },
    ]);
    (useOptionList as any).mockReturnValue({
      options: mockSurgeRates,
    });
    mockForm.watch.mockImplementation((field) => {
      switch (field) {
        case 'surgeRateId':
          return null;
        case 'originType':
          return 'STANDARD_ORDER';
        case 'isDeleted':
          return false;
        case 'orderEntryReadOnly':
          return false;
        case 'surgePricing':
          return true;
        default:
          return undefined;
      }
    });
  });

  it('renders all toolbar controls', () => {
    render(
      <ItemToolbar
        append={mockAppend}
        setOpen={mockSetOpen}
        rowSelection={mockRowSelection}
      />
    );
    expect(screen.getByText('Mocked ItemLookup')).toBeInTheDocument();
    expect(screen.getByText('Sub-Rent Item')).toBeInTheDocument();
    expect(screen.getByText('Surge:')).toBeInTheDocument();
  });

  it('renders Return Qty button when originType is MISSING_ORDER', () => {
    mockForm.watch.mockImplementation((field) =>
      field === 'originType' ? 'MISSING_ORDER' : false
    );
    render(
      <ItemToolbar
        append={mockAppend}
        setOpen={mockSetOpen}
        rowSelection={mockRowSelection}
      />
    );
    expect(screen.getByText('Return Qty')).toBeInTheDocument();
  });

  it('handles ItemLookup data correctly', () => {
    render(
      <ItemToolbar
        append={mockAppend}
        setOpen={mockSetOpen}
        rowSelection={mockRowSelection}
      />
    );
    const itemLookup = screen.getByText('Mocked ItemLookup');
    fireEvent.click(itemLookup);
    expect(mockAppend).not.toHaveBeenCalled();
  });

  it('disables buttons when order is deleted', () => {
    mockForm.watch.mockImplementation((field) =>
      field === 'isDeleted' ? true : false
    );
    render(
      <ItemToolbar
        append={mockAppend}
        setOpen={mockSetOpen}
        rowSelection={mockRowSelection}
      />
    );
    expect(screen.getByText('Sub-Rent Item')).toBeDisabled();
    expect(screen.getByText('Surge:').closest('div'));
  });

  it('disables buttons when order is read-only', () => {
    mockForm.watch.mockImplementation((field) =>
      field === 'orderEntryReadOnly' ? true : false
    );
    render(
      <ItemToolbar
        append={mockAppend}
        setOpen={mockSetOpen}
        rowSelection={mockRowSelection}
      />
    );
    expect(screen.getByText('Sub-Rent Item')).toBeDisabled();
    expect(screen.getByText('Surge:').closest('div'));
  });

  it('enables Sub-Rent Item only for RENTAL_ITEM type', async () => {
    const rentalItemSelection = { '0': true };
    const kitItemSelection = { '1': true };
    const { rerender } = render(
      <ItemToolbar
        append={mockAppend}
        setOpen={mockSetOpen}
        rowSelection={rentalItemSelection}
      />
    );
    rerender(
      <ItemToolbar
        append={mockAppend}
        setOpen={mockSetOpen}
        rowSelection={kitItemSelection}
      />
    );
    expect(screen.getByText('Sub-Rent Item')).toBeDisabled();
  });

  it('handles surge rate selection', async () => {
    const mockUpdateSurgeCharge = vi.fn();
    (useUpdateSurgeChargeMutation as any).mockReturnValue([
      mockUpdateSurgeCharge,
      { isLoading: false },
    ]);
    render(
      <ItemToolbar
        append={mockAppend}
        setOpen={mockSetOpen}
        rowSelection={mockRowSelection}
      />
    );
    fireEvent.click(screen.getByText('Trigger Select'));
    await waitFor(() => {
      expect(mockForm.setValue);
      expect(mockUpdateSurgeCharge);
    });
  });

  it('renders all dropdown menu items', () => {
    render(
      <ItemToolbar
        append={mockAppend}
        setOpen={mockSetOpen}
        rowSelection={mockRowSelection}
      />
    );
    expect(screen.getByText('Change Serialized Item')).toBeInTheDocument();
    expect(screen.getByText('Overbooked Item Info')).toBeInTheDocument();
    expect(screen.getByText('Availability Info')).toBeInTheDocument();
    expect(screen.getByText('Additional Item Info')).toBeInTheDocument();
    expect(screen.getByText('Kit Components')).toBeInTheDocument();
    expect(screen.getByText('Change Sorting')).toBeInTheDocument();
  });

  it('disables dropdown menu items based on conditions', () => {
    const kitItemSelection = { '1': true };
    render(
      <ItemToolbar
        append={mockAppend}
        setOpen={mockSetOpen}
        rowSelection={kitItemSelection}
      />
    );
    expect(screen.getByText('Change Serialized Item')).toBeDisabled();
    expect(screen.getByText('Additional Item Info')).toBeDisabled();
  });

  it('opens correct dialogs from dropdown menu', () => {
    render(
      <ItemToolbar
        append={mockAppend}
        setOpen={mockSetOpen}
        rowSelection={mockRowSelection}
      />
    );
    fireEvent.click(screen.getByText('Availability Info'));
    expect(mockSetOpen).toHaveBeenCalledWith({
      state: true,
      action: 'availability-info',
    });
  });
});
