import { useCallback } from 'react';
import { ItemType } from '@/types/item.types';
import { UseFormReturn } from 'react-hook-form';
import { ItemListTypes } from '@/types/order.types';

interface UseItemListHandlersProps {
  form: UseFormReturn<ItemListTypes>;
  orderId: string;
  getItemBriefData: (params: {
    orderId: string;
    itemId: string;
  }) => Promise<any>;
}

export const useItemListHandlers = ({
  form,
  orderId,
  getItemBriefData,
}: UseItemListHandlersProps) => {
  const handleItemIdChange = useCallback(
    async (value: { value: string }, rowIndex: number) => {
      const itemData = await getItemBriefData({ orderId, itemId: value.value });
      if (itemData?.data) {
        form.setValue(`items.${rowIndex}.itemId`, {
          label: itemData?.data?.data?.itemId,
          value: itemData?.data?.data?.id?.toString(),
        });
        form.setValue(`items.${rowIndex}.type`, itemData?.data?.data?.itemType);
        form.setValue(`items.${rowIndex}.quantity`, 1);
        form.setValue(
          `items.${rowIndex}.price`,
          itemData?.data?.data?.unitPrice?.toString()
        );
        form.setValue(
          `items.${rowIndex}.description`,
          itemData?.data?.data?.description ?? ''
        );
      }
    },
    [getItemBriefData, orderId, form]
  );

  const handlePriceChange = useCallback(
    (price: string | number, index: number) => {
      form.clearErrors(`items[${index}].price` as any);
      const quantity = form.getValues(`items.${index}.quantity`);
      const priceNumber = Number(price);
      const quantityNumber = Number(quantity);
      const total =
        !isNaN(priceNumber) && !isNaN(quantityNumber)
          ? priceNumber * quantityNumber
          : 0;
      form.setValue(`items.${index}.total`, Number(total));
      form.setValue(`items.${index}.price`, price);
    },
    [form]
  );

  const handleQuantityChange = useCallback(
    (quantity: string | number, index: number) => {
      form.clearErrors(`items[${index}].quantity` as any);
      const price = form.getValues(`items.${index}.price`);
      const quantityNumber = Number(quantity);
      const priceNumber = Number(price);
      const total =
        !isNaN(quantityNumber) && !isNaN(priceNumber)
          ? quantityNumber * priceNumber
          : 0;
      form.setValue(`items.${index}.total`, Number(total));
    },
    [form]
  );

  const handleItemTypeChange = useCallback(
    (value: string, index: number, itemTypeValue?: string) => {
      if (itemTypeValue === ItemType.KIT_ITEM) {
        form.setValue(`items.${index}.type`, itemTypeValue);
      } else if (itemTypeValue !== value && value !== ItemType.KIT_ITEM) {
        form.setValue(`items.${index}.type`, value);
      } else {
        form.setValue(`items.${index}.type`, itemTypeValue);
      }
    },
    [form]
  );

  return {
    handleItemIdChange,
    handlePriceChange,
    handleQuantityChange,
    handleItemTypeChange,
    // handleDescriptionChange,
  };
};
