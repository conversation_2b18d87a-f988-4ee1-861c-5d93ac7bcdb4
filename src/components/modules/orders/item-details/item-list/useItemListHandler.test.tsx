import { renderHook } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { useItemListHandlers } from './useItemListHandler';
import { ItemType } from '@/types/item.types';

describe('useItemListHandlers', () => {
  const mockForm = {
    setValue: vi.fn(),
    getValues: vi.fn(),
    clearErrors: vi.fn(),
    watch: vi.fn(),
  };
  const mockGetItemBriefData = vi.fn();
  const orderId = 'order-123';
  const defaultProps = {
    form: mockForm,
    orderId,
    getItemBriefData: mockGetItemBriefData,
  } as any;

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should handle item ID change correctly', async () => {
    const mockItemData = {
      data: {
        data: {
          id: 'item-456',
          itemId: 'ITEM456',
          itemType: 'STANDARD',
          unitPrice: '19.99',
          description: 'Test Item',
        },
      },
    };
    mockGetItemBriefData.mockResolvedValue(mockItemData);
    const { result } = renderHook(() => useItemListHandlers(defaultProps));
    await result.current.handleItemIdChange({ value: 'item-456' }, 0);
    expect(mockGetItemBriefData).toHaveBeenCalledWith({
      orderId: 'order-123',
      itemId: 'item-456',
    });
    expect(mockForm.setValue).toHaveBeenCalledWith('items.0.itemId', {
      label: 'ITEM456',
      value: 'item-456',
    });
    expect(mockForm.setValue).toHaveBeenCalledWith('items.0.type', 'STANDARD');
    expect(mockForm.setValue).toHaveBeenCalledWith('items.0.quantity', 1);
    expect(mockForm.setValue).toHaveBeenCalledWith('items.0.price', '19.99');
    expect(mockForm.setValue).toHaveBeenCalledWith(
      'items.0.description',
      'Test Item'
    );
  });

  it('should handle price change correctly', () => {
    mockForm.getValues.mockImplementation((field) => {
      if (field === 'items.0.quantity') return 2;
      return undefined;
    });
    const { result } = renderHook(() => useItemListHandlers(defaultProps));
    result.current.handlePriceChange('10.50', 0);
    expect(mockForm.clearErrors).toHaveBeenCalledWith('items[0].price');
    expect(mockForm.setValue).toHaveBeenCalledWith('items.0.total', 21);
    expect(mockForm.setValue).toHaveBeenCalledWith('items.0.price', '10.50');
  });

  it('should handle invalid price change gracefully', () => {
    mockForm.getValues.mockImplementation((field) => {
      if (field === 'items.0.quantity') return 'invalid';
      return undefined;
    });
    const { result } = renderHook(() => useItemListHandlers(defaultProps));
    result.current.handlePriceChange('invalid', 0);
    expect(mockForm.setValue).toHaveBeenCalledWith('items.0.total', 0);
  });

  it('should handle quantity change correctly', () => {
    mockForm.getValues.mockImplementation((field) => {
      if (field === 'items.0.price') return 5.99;
      return undefined;
    });
    const { result } = renderHook(() => useItemListHandlers(defaultProps));
    result.current.handleQuantityChange(3, 0);
    expect(mockForm.clearErrors).toHaveBeenCalledWith('items[0].quantity');
    expect(mockForm.setValue).toHaveBeenCalledWith('items.0.total', 17.97);
  });

  it('should handle invalid quantity change gracefully', () => {
    mockForm.getValues.mockImplementation((field) => {
      if (field === 'items.0.price') return 'invalid';
      return undefined;
    });
    const { result } = renderHook(() => useItemListHandlers(defaultProps));
    result.current.handleQuantityChange('invalid', 0);
    expect(mockForm.setValue).toHaveBeenCalledWith('items.0.total', 0);
  });

  it('should handle item type change for KIT_ITEM', () => {
    const { result } = renderHook(() => useItemListHandlers(defaultProps));
    result.current.handleItemTypeChange('STANDARD', 0, ItemType.KIT_ITEM);
    expect(mockForm.setValue).toHaveBeenCalledWith(
      'items.0.type',
      ItemType.KIT_ITEM
    );
  });

  it('should handle item type change for non-KIT_ITEM when types differ', () => {
    const { result } = renderHook(() => useItemListHandlers(defaultProps));
    result.current.handleItemTypeChange('STANDARD', 0, 'RENTAL');
    expect(mockForm.setValue).toHaveBeenCalledWith('items.0.type', 'STANDARD');
  });

  it('should handle item type change when types match', () => {
    const { result } = renderHook(() => useItemListHandlers(defaultProps));
    result.current.handleItemTypeChange('STANDARD', 0, 'STANDARD');
    expect(mockForm.setValue).toHaveBeenCalledWith('items.0.type', 'STANDARD');
  });

  it('should return all handler functions', () => {
    const { result } = renderHook(() => useItemListHandlers(defaultProps));
    expect(result.current).toEqual({
      handleItemIdChange: expect.any(Function),
      handlePriceChange: expect.any(Function),
      handleQuantityChange: expect.any(Function),
      handleItemTypeChange: expect.any(Function),
    });
  });
});
