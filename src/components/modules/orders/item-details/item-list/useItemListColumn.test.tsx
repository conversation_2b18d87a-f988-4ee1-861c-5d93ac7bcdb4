import { renderHook } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { useItemListColumns } from './useItemListColumn';
import { useFormContext } from 'react-hook-form';
import { ItemTypeEnum } from '@/types/order.types';
import {
  QUANTITY_VALIDATION_RULE,
  SERIAL_VALIDATION_RULE,
} from '@/constants/validation-constants';

// Mock dependencies
vi.mock('react-hook-form', () => ({
  useFormContext: vi.fn(),
}));

vi.mock('@/lib/utils', () => ({
  convertToFloat: vi.fn(({ value }) => `$${value}`),
  getQueryParam: vi.fn(() => '123'),
  cn: vi.fn((...args) => args.join(' ')),
}));

describe('useItemListColumns', () => {
  const mockForm = {
    watch: vi.fn(),
    control: {},
    formState: { errors: {} },
  };
  const mockOrderForm = {
    watch: vi.fn((field) => {
      if (field === 'isDeleted') return false;
      if (field === 'orderEntryReadOnly') return false;
      return undefined;
    }),
  };
  const defaultProps = {
    form: mockForm,
    itemType: { data: [{ value: 'type1', label: 'Type 1' }] },
    itemTypeLoading: false,
    handleItemIdChange: vi.fn(),
    handlePriceChange: vi.fn(),
    handleQuantityChange: vi.fn(),
    toggleDelete: vi.fn(),
    showSerialNumberColumn: true,
    setOpen: vi.fn(),
  } as any;
  beforeEach(() => {
    vi.clearAllMocks();
    (useFormContext as any).mockReturnValue(mockOrderForm);
    mockForm.watch.mockImplementation((field) => {
      if (field?.includes('itemId')) return { value: 'item123' };
      if (field?.includes('serialNumber')) return 'SN123';
      if (field?.includes('listId')) return null;
      if (field?.includes('total')) return 100;
      return undefined;
    });
  });
  it('should return the correct column structure', () => {
    const { result } = renderHook(() => useItemListColumns(defaultProps));
    expect(result.current).toHaveLength(9);
    expect(result.current[0].accessorKey).toBe('itemId');
    expect(result.current[1].accessorKey).toBe('serialNumber');
    expect(result.current[2].accessorKey).toBe('quantity');
    expect(result.current[3].accessorKey).toBe('description');
    expect(result.current[4].accessorKey).toBe('type');
    expect(result.current[5].accessorKey).toBe('subRental');
    expect(result.current[6].accessorKey).toBe('price');
    expect(result.current[7].accessorKey).toBe('total');
    expect(result.current[8].id).toBe('action');
  });

  it('should not include serialNumber column when showSerialNumberColumn is false', () => {
    const { result } = renderHook(() =>
      useItemListColumns({ ...defaultProps, showSerialNumberColumn: false })
    );
    expect(result.current).toHaveLength(8);
    expect(
      result.current.some((col: any) => col.accessorKey === 'serialNumber')
    ).toBe(false);
  });

  it('should disable fields when order is deleted', () => {
    (mockOrderForm.watch as any).mockImplementation((field: any) =>
      field === 'isDeleted' ? true : false
    );
    const { result } = renderHook(() =>
      useItemListColumns(defaultProps)
    ) as any;
    const itemIdCell = result.current[0].cell({ row: { index: 0 } });
    expect(itemIdCell.props.disabled).toBe(true);
    const quantityCell = result.current[2].cell({
      row: { index: 0, original: {} },
    });
    expect(quantityCell.props.disabled).toBe(true);
  });

  it('should disable fields when order is read-only', () => {
    (mockOrderForm.watch as any).mockImplementation((field: any) =>
      field === 'orderEntryReadOnly' ? true : false
    );
    const { result } = renderHook(() =>
      useItemListColumns(defaultProps)
    ) as any;
    const priceCell = result.current[6].cell({ row: { index: 0 } }) as any;
    expect(priceCell.props.disabled).toBe(true);
  });

  it('should apply correct validation rules based on conditions', () => {
    const { result } = renderHook(() =>
      useItemListColumns(defaultProps)
    ) as any;
    mockForm.watch.mockImplementation((field) =>
      field?.includes('serialNumber') ? 'SN123' : undefined
    );
    const quantityCellWithSN = result.current[2].cell({
      row: { index: 0, original: {} },
    }) as any;
    expect(quantityCellWithSN.props.validation).toEqual(SERIAL_VALIDATION_RULE);
    mockForm.watch.mockImplementation((field) =>
      field?.includes('itemId') ? { value: 'item123' } : undefined
    );
    const quantityCellWithItem = result.current[2].cell({
      row: { index: 0, original: {} },
    }) as any;
    expect(quantityCellWithItem.props.validation).toEqual(
      QUANTITY_VALIDATION_RULE
    );
    const priceCell = result.current[6].cell({ row: { index: 0 } });
    expect(priceCell.props.validation).toEqual({
      validate: expect.any(Function),
    });
  });

  it('should format total column correctly', () => {
    const { result } = renderHook(() =>
      useItemListColumns(defaultProps)
    ) as any;

    const totalCell = result.current[7].cell({ row: { index: 0 } });
    expect(totalCell).toBe('$100');
  });

  it('should handle action column click events', () => {
    const mockToggleDelete = vi.fn();
    const mockSetOpen = vi.fn();
    const { result } = renderHook(() =>
      useItemListColumns({
        ...defaultProps,
        toggleDelete: mockToggleDelete,
        setOpen: mockSetOpen,
      })
    ) as any;
    const actionCell = result.current[8].cell({
      row: {
        index: 0,
        original: { listId: 123 },
      },
    });
    actionCell.props.onDelete();
    expect(mockToggleDelete).toHaveBeenCalledWith(123, 0);
    actionCell.props.customEdit.props.onClick();
    expect(mockSetOpen).toHaveBeenCalledWith({
      state: true,
      action: ItemTypeEnum.ADDITIONAL_ITEM_INFO,
      value: 123,
    });
  });

  it('should disable action buttons when listId is not present', () => {
    mockForm.watch.mockImplementation((field) =>
      field?.includes('listId') ? null : undefined
    );
    const { result } = renderHook(() =>
      useItemListColumns(defaultProps)
    ) as any;
    const actionCell = result.current[8].cell({
      row: {
        index: 0,
        original: {},
      },
    });
    expect(actionCell.props.customEdit.props.disabled).toBe(true);
  });

  it('should show red text for overbooked items', () => {
    const { result } = renderHook(() =>
      useItemListColumns(defaultProps)
    ) as any;
    const quantityCell = result.current[2].cell({
      row: {
        index: 0,
        original: { isOverbooked: true },
      },
    });
    expect(quantityCell.props.className).toContain('text-red-600');
  });
});
