import { useCallback, useMemo } from 'react';
import { Path, UseFormReturn } from 'react-hook-form';

import CheckboxField from '@/components/forms/checkbox';
import InputField from '@/components/forms/input-field';

import { emailValidation } from '@/constants/validation-constants';
import { cn } from '@/lib/utils';
import { AdditionalEmailInfoTypes } from '@/types/orders/order-item-details.types';
import ActionColumnMenu from '@/components/common/data-tables/ActionColumn';

interface UseItemListColumnsProps {
  form: UseFormReturn<AdditionalEmailInfoTypes>;
  toggleDelete: (index: number) => void;
  rowSelection: Record<string, boolean>;
}

export const useEmailListColumns = ({
  form,
  toggleDelete,
  rowSelection,
}: UseItemListColumnsProps) => {
  const renderCheckbox = useCallback(
    (name: Path<AdditionalEmailInfoTypes>) => (
      <CheckboxField
        control={form.control}
        name={name}
        className={cn(
          !form.watch(name) && 'border-grayScale-400 border-2',
          'w-5 h-5'
        )}
      />
    ),
    [form]
  );

  return useMemo(
    () => [
      {
        accessorKey: 'to',
        header: 'To',
        size: 80,
        cell: ({ row }: { row: any }) =>
          renderCheckbox(`emails.${row.index}.to`),
      },
      {
        accessorKey: 'cc',
        header: 'Cc',
        size: 80,
        cell: ({ row }: { row: any }) =>
          renderCheckbox(`emails.${row.index}.cc`),
      },
      {
        accessorKey: 'bcc',
        header: 'Bcc',
        size: 80,
        cell: ({ row }: { row: any }) =>
          renderCheckbox(`emails.${row.index}.bcc`),
      },
      {
        accessorKey: 'name',
        header: 'Name',
        size: 160,
        cell: ({ row }: { row: any }) => (
          <InputField
            key={row?.original?.listId}
            name={`emails.${row.index}.name`}
            placeholder="Name"
            form={form}
            pClassName="p-1"
          />
        ),
      },
      {
        accessorKey: 'email',
        header: 'E-mail',
        size: 60,
        cell: ({ row }: { row: any }) => {
          const isSelected =
            rowSelection[row?.original?.listId?.toString()] ?? false;
          return (
            <div className="flex items-center">
              <InputField
                key={row?.original?.listId}
                name={`emails.${row.index}.email`}
                placeholder="Email"
                form={form}
                validation={
                  isSelected ? emailValidation(true) : emailValidation(false)
                }
                pClassName="p-1"
              />
              <ActionColumnMenu
                onDelete={() => toggleDelete(row?.index)}
                contentClassName="z-[99] w-fit"
              />
            </div>
          );
        },
      },
    ],
    [form, renderCheckbox, rowSelection, toggleDelete]
  );
};
