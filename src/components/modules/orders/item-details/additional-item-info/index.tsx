import { useCallback, useEffect, useMemo, useState } from 'react';
import { useForm, useFormContext } from 'react-hook-form';

import AppTabsVertical from '@/components/common/app-tabs-vertical';
import CustomDialog from '@/components/common/dialog';

import { cn } from '@/lib/utils';
import { generateAdditionalItemInfoTabs } from '@/constants/order-item-details-constants';
import { AdditionalEmailInfoTypes } from '@/types/orders/order-item-details.types';
import { OpenDialogType } from '../item-list';
import { OrderInformationTypes } from '@/types/order.types';
import uniqueId from 'lodash/uniqueId';
import { useGetEmailsListQuery } from '@/redux/features/orders/additional-item-info.api';

interface AdditionalItemInfoProps {
  open: OpenDialogType;
  onOpenChange: () => void;
  orderItemId?: number | string | null;
}

const AdditionalItemInfo = ({
  open,
  onOpenChange,
  orderItemId,
}: AdditionalItemInfoProps) => {
  const form = useForm<AdditionalEmailInfoTypes>({
    mode: 'onChange',
  });
  const orderForm = useFormContext<OrderInformationTypes>();
  const customerId = orderForm.getValues('billTo.customerId.value');
  const [activeTab, setActiveTab] = useState<string>('information');

  const { data } = useGetEmailsListQuery(customerId, {
    skip: !customerId,
    refetchOnMountOrArgChange: true,
  });

  const defaultValues = useMemo(
    () => ({
      emails: data?.data?.map((email) => {
        return {
          name: email.name,
          email: email.email,
          to: email.primary ? true : false,
          cc: false,
          bcc: false,
          listId: uniqueId(),
        };
      }),
    }),
    [data?.data]
  );

  useEffect(() => {
    if (data?.data) {
      form.reset(defaultValues);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data?.data, form]);

  // handle on change tab
  const handleTabChange = useCallback((value: string) => {
    setActiveTab(value);
  }, []);

  return (
    <CustomDialog
      onOpenChange={onOpenChange}
      open={open.state}
      className={cn('max-w-[90%] md:w-[80%] 2xl:w-[60%]')}
      title={'Additional Item Info'}
    >
      <AppTabsVertical
        tabs={generateAdditionalItemInfoTabs({
          form,
          orderItemId,
          onOpenChange,
        })}
        activeTab={activeTab}
        onTabChange={handleTabChange}
        className="px-4 pb-3"
        contentClassName={cn(
          'h-[500px] 2xl:h-[600px] overflow-y-auto p-0 border-none'
        )}
      />
    </CustomDialog>
  );
};

export default AdditionalItemInfo;
