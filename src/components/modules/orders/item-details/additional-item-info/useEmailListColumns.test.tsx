import { renderHook } from '@testing-library/react';
import { useEmailListColumns } from './useEmailListColumns';
import { UseFormReturn } from 'react-hook-form';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { AdditionalEmailInfoTypes } from '@/types/orders/order-item-details.types';

describe('useEmailListColumns', () => {
  const mockForm = {
    control: {},
    watch: vi.fn().mockReturnValue(false),
    getValues: vi.fn(),
    setValue: vi.fn(),
  } as unknown as UseFormReturn<AdditionalEmailInfoTypes>;

  const mockToggleDelete = vi.fn();
  const mockRowSelection = {};

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should return the correct column structure', () => {
    const { result } = renderHook(() =>
      useEmailListColumns({
        form: mockForm,
        toggleDelete: mockToggleDelete,
        rowSelection: mockRowSelection,
      })
    );
    const columns = result.current;
    expect(columns).toHaveLength(5);
    expect(columns[0].accessorKey).toBe('to');
    expect(columns[1].accessorKey).toBe('cc');
    expect(columns[2].accessorKey).toBe('bcc');
    expect(columns[3].accessorKey).toBe('name');
    expect(columns[4].accessorKey).toBe('email');
  });

  it('should render checkbox fields for to/cc/bcc columns', () => {
    const { result } = renderHook(() =>
      useEmailListColumns({
        form: mockForm,
        toggleDelete: mockToggleDelete,
        rowSelection: mockRowSelection,
      })
    );
    const columns = result.current;
    const toCell = columns[0].cell({ row: { index: 0 } });
    const ccCell = columns[1].cell({ row: { index: 0 } });
    const bccCell = columns[2].cell({ row: { index: 0 } });
    expect(toCell).toBeDefined();
    expect(ccCell).toBeDefined();
    expect(bccCell).toBeDefined();
  });

  it('should render input field for name column', () => {
    const { result } = renderHook(() =>
      useEmailListColumns({
        form: mockForm,
        toggleDelete: mockToggleDelete,
        rowSelection: mockRowSelection,
      })
    );
    const columns = result.current;
    const nameCell = columns[3].cell({
      row: { index: 0, original: { listId: '123' } },
    });
    expect(nameCell).toBeDefined();
  });

  it('should render email input with delete button', () => {
    const { result } = renderHook(() =>
      useEmailListColumns({
        form: mockForm,
        toggleDelete: mockToggleDelete,
        rowSelection: mockRowSelection,
      })
    );
    const columns = result.current;
    const emailCell = columns[4].cell({
      row: { index: 0, original: { listId: '123' } },
    });
    expect(emailCell).toBeDefined();
    expect(emailCell.props.children[1].type.displayName);
  });

  it('should apply email validation when row is selected', () => {
    const selectedRowSelection = { '123': true };
    const { result } = renderHook(() =>
      useEmailListColumns({
        form: mockForm,
        toggleDelete: mockToggleDelete,
        rowSelection: selectedRowSelection,
      })
    );
    const columns = result.current;
    const emailCell = columns[4].cell({
      row: { index: 0, original: { listId: '123' } },
    });
    expect(emailCell.props.children[0].props.validation).toBeDefined();
  });

  it('should call toggleDelete when delete action is triggered', () => {
    const { result } = renderHook(() =>
      useEmailListColumns({
        form: mockForm,
        toggleDelete: mockToggleDelete,
        rowSelection: mockRowSelection,
      })
    );
    const columns = result.current;
    const emailCell = columns[4].cell({
      row: { index: 0, original: { listId: '123' } },
    });
    emailCell.props.children[1].props.onDelete();
    expect(mockToggleDelete).toHaveBeenCalledWith(0);
  });
});
