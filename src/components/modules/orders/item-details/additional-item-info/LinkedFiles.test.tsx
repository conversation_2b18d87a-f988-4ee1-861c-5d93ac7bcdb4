import { render, screen } from '@testing-library/react';
import { describe, expect, it, vi, beforeEach } from 'vitest';
import LinkedFilesTab from './LinkedFiles';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { useGetLinkedFilesListQuery } from '@/redux/features/orders/additional-item-info.api';

vi.mock('@/components/common/LinkedFiles', () => ({
  default: vi.fn((props) => (
    <div data-testid="linked-files-mock">
      <div>Data: {JSON.stringify(props.data)}</div>
      <div>isFetching: {props.isFetching.toString()}</div>
      <button onClick={props.refetch}>Refetch</button>
    </div>
  )),
}));

// Mock the API hooks
vi.mock('@/redux/features/orders/additional-item-info.api', () => ({
  useGetLinkedFilesListQuery: vi.fn(),
  useDeleteLinkedFileMutation: vi.fn(() => [vi.fn(), { isLoading: false }]),
  useDefaultLinkedFileMutation: vi.fn(() => [vi.fn(), { isLoading: false }]),
}));

const mockFilesData = {
  data: [
    { id: 'file1', name: 'Document.pdf', isDefault: true },
    { id: 'file2', name: 'Image.jpg', isDefault: false },
  ],
};

describe('LinkedFilesTab', () => {
  const mockRefetch = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();

    (useGetLinkedFilesListQuery as any).mockReturnValue({
      data: mockFilesData,
      isLoading: false,
      isFetching: false,
      refetch: mockRefetch,
    });
  });

  const createStore = () => {
    return configureStore({
      reducer: {},
    });
  };

  it('should render loading state when loading', () => {
    (useGetLinkedFilesListQuery as any).mockReturnValue({
      data: undefined,
      isLoading: true,
      isFetching: true,
      refetch: mockRefetch,
    });
    render(
      <Provider store={createStore()}>
        <LinkedFilesTab orderItemId="123" />
      </Provider>
    );
    expect(screen.getByText('isFetching: true')).toBeInTheDocument();
  });

  it('should render with data after loading', () => {
    render(
      <Provider store={createStore()}>
        <LinkedFilesTab orderItemId="123" />
      </Provider>
    );
    expect(
      screen.getByText(`Data: ${JSON.stringify(mockFilesData.data)}`)
    ).toBeInTheDocument();
  });

  it('should not fetch data when orderItemId is not provided', () => {
    render(
      <Provider store={createStore()}>
        <LinkedFilesTab orderItemId={null} />
      </Provider>
    );
    expect(useGetLinkedFilesListQuery).toHaveBeenCalledWith(null, {
      skip: true,
    });
  });

  it('should handle refetch action', async () => {
    render(
      <Provider store={createStore()}>
        <LinkedFilesTab orderItemId="123" />
      </Provider>
    );
    screen.getByText('Refetch').click();
    expect(mockRefetch).toHaveBeenCalled();
  });
});
