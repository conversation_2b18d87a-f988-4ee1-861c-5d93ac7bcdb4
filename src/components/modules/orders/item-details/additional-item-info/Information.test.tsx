import { render, screen, waitFor } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import Information from './Information';
import { useForm } from 'react-hook-form';
import { useAdditionalItemInfoQuery } from '@/redux/features/orders/additional-item-info.api';

// Mock the hooks and components
vi.mock('react-hook-form', () => ({
  useForm: vi.fn(() => ({
    reset: vi.fn(),
    formState: { errors: {} },
    register: vi.fn(),
    control: {},
  })),
}));

vi.mock('@/redux/features/orders/additional-item-info.api', () => ({
  useAdditionalItemInfoQuery: vi.fn(),
}));

vi.mock('@/lib/utils', () => ({
  getQueryParam: vi.fn(() => '123'),
}));

vi.mock('@/components/forms/input-field', () => ({
  default: ({ label }: { label: string }) => (
    <div data-testid="input-field">{label}</div>
  ),
}));

vi.mock('@/components/forms/text-area', () => ({
  default: ({ label }: { label: string }) => (
    <div data-testid="text-area">{label}</div>
  ),
}));

vi.mock('@/components/forms/number-input-field', () => ({
  default: ({ label }: { label: string }) => (
    <div data-testid="number-input">{label}</div>
  ),
}));

vi.mock('@/components/ui/card', () => ({
  Card: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="card">{children}</div>
  ),
  CardContent: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="card-content">{children}</div>
  ),
}));

vi.mock('@/components/common/app-spinner', () => ({
  default: ({ isLoading }: { isLoading: boolean }) => (
    <div data-testid="spinner">{isLoading ? 'Loading...' : 'Not Loading'}</div>
  ),
}));

describe('Information Component', () => {
  const mockData = {
    data: {
      itemId: 'item-123',
      description: 'Test description',
      itemlocation: 'Warehouse A',
      officelocation: 'Office B',
      qualitydetails: 'Good',
      replacementcharge: 100,
      itemInfo: 'Additional item information',
    },
  };

  it('renders without crashing', () => {
    (useAdditionalItemInfoQuery as any).mockReturnValue({
      data: null,
      isLoading: false,
    });
    render(<Information orderItemId="123" />);
    expect(screen.getByTestId('card')).toBeInTheDocument();
  });

  it('displays loading spinner when data is loading', () => {
    (useAdditionalItemInfoQuery as any).mockReturnValue({
      data: null,
      isLoading: true,
    });
    render(<Information orderItemId="123" />);
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('calls useAdditionalItemInfoQuery with correct parameters', () => {
    (useAdditionalItemInfoQuery as any).mockReturnValue({
      data: null,
      isLoading: false,
    });
    render(<Information orderItemId="123" />);
    expect(useAdditionalItemInfoQuery).toHaveBeenCalledWith(
      { orderId: '123', orderItemId: '123' },
      { skip: false }
    );
  });

  it('does not call API when orderItemId is not provided', () => {
    (useAdditionalItemInfoQuery as any).mockReturnValue({
      data: null,
      isLoading: false,
    });
    render(<Information orderItemId={null} />);
    expect(useAdditionalItemInfoQuery);
  });

  it('resets form with data when loaded', async () => {
    const mockReset = vi.fn();
    (useForm as any).mockReturnValueOnce({
      reset: mockReset,
      formState: { errors: {} },
      register: vi.fn(),
      control: {},
    });
    (useAdditionalItemInfoQuery as any).mockReturnValue({
      data: mockData,
      isLoading: false,
    });
    render(<Information orderItemId="123" />);
    await waitFor(() => {
      expect(mockReset).toHaveBeenCalledWith(mockData.data);
    });
  });

  it('renders all input fields with correct labels', () => {
    (useAdditionalItemInfoQuery as any).mockReturnValue({
      data: mockData,
      isLoading: false,
    });
    render(<Information orderItemId="123" />);
    expect(screen.getByText('Item ID')).toBeInTheDocument();
    expect(screen.getByText('Item Description')).toBeInTheDocument();
    expect(screen.getByText('Item Location')).toBeInTheDocument();
    expect(screen.getByText('Office Location')).toBeInTheDocument();
    expect(screen.getByText('Quality')).toBeInTheDocument();
    expect(screen.getByText('Replacement Charge')).toBeInTheDocument();
    expect(screen.getByText('Item Information')).toBeInTheDocument();
  });

  it('renders correct grid layout', () => {
    (useAdditionalItemInfoQuery as any).mockReturnValue({
      data: mockData,
      isLoading: false,
    });
    render(<Information orderItemId="123" />);
    const cardContent = screen.getByTestId('card-content');
    expect(cardContent);
  });
});
