import { render, screen } from '@testing-library/react';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import AdditionalItemInfo from './index';
import { useForm, useFormContext } from 'react-hook-form';
import { useGetEmailsListQuery } from '@/redux/features/orders/additional-item-info.api';

// Mock the dependencies
vi.mock('react-hook-form', () => ({
  useForm: vi.fn(),
  useFormContext: vi.fn(),
}));

vi.mock('@/components/common/app-tabs-vertical', () => ({
  default: ({ tabs }: { tabs: any[] }) => (
    <div data-testid="app-tabs-vertical">
      {tabs.map((tab) => (
        <div key={tab.value}>{tab.label}</div>
      ))}
    </div>
  ),
}));

vi.mock('@/components/common/dialog', () => ({
  default: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="custom-dialog">{children}</div>
  ),
}));

vi.mock('@/redux/features/orders/additional-item-info.api', () => ({
  useGetEmailsListQuery: vi.fn().mockReturnValue({ data: undefined }),
}));

vi.mock('@/lib/utils', () => ({
  cn: (...args: string[]) => args.join(' '),
}));

describe('AdditionalItemInfo', () => {
  const mockUseForm = {
    reset: vi.fn(),
    formState: { isValid: true },
    handleSubmit: vi.fn(),
    watch: vi.fn(),
    getValues: vi.fn(),
    setValue: vi.fn(),
  };

  const mockUseFormContext = {
    getValues: vi.fn().mockReturnValue({
      billTo: {
        customerId: {
          value: '123',
        },
      },
    }),
  };

  beforeEach(() => {
    (useForm as any).mockReturnValue(mockUseForm);
    (useFormContext as any).mockReturnValue(mockUseFormContext);
    (useGetEmailsListQuery as any).mockReturnValue({ data: undefined });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('renders the dialog with correct title', () => {
    render(
      <AdditionalItemInfo
        open={{ state: true, action: '' }}
        onOpenChange={vi.fn()}
        orderItemId="1"
      />
    );
    expect(screen.getByTestId('custom-dialog')).toBeInTheDocument();
  });

  it('renders the vertical tabs with correct tab labels', () => {
    render(
      <AdditionalItemInfo
        open={{ state: true, action: '' }}
        onOpenChange={vi.fn()}
        orderItemId="1"
      />
    );
    const tabs = screen.getByTestId('app-tabs-vertical');
    expect(tabs).toBeInTheDocument();
  });

  it('sets the active tab to "information" by default', () => {
    render(
      <AdditionalItemInfo
        open={{ state: true, action: '' }}
        onOpenChange={vi.fn()}
        orderItemId="1"
      />
    );
    expect(screen.getByText('Information')).toBeInTheDocument();
  });
});
