import { useEffect } from 'react';
import { Path, useForm } from 'react-hook-form';

import InputField from '@/components/forms/input-field';
import Text<PERSON>reaField from '@/components/forms/text-area';
import { Card, CardContent } from '@/components/ui/card';

import { getQueryParam } from '@/lib/utils';
import { useAdditionalItemInfoQuery } from '@/redux/features/orders/additional-item-info.api';
import { AdditionalItemInfoTypes } from '@/types/orders/order-item-details.types';
import AppSpinner from '@/components/common/app-spinner';
import NumberInputField from '@/components/forms/number-input-field';

interface FormField {
  label: string;
  name: Path<AdditionalItemInfoTypes>;
}

interface ColumnFields {
  column: 'left' | 'right';
  fields: FormField[];
}

const formFields: ColumnFields[] = [
  // Left Column Fields
  {
    column: 'left',
    fields: [
      { label: 'Item ID', name: 'itemId' },
      { label: 'Item Description', name: 'description' },
      { label: 'Item Location', name: 'itemlocation' },
      { label: 'Office Location', name: 'officelocation' },
    ],
  },
  // Right Column Fields
  // {
  //   column: 'right',
  //   fields: [
  //     { label: 'Quality', name: 'qualitydetails' },
  //     { label: 'Replacement Charge', name: 'replacementcharge' },
  //   ],
  // },
];

type InformationProps = {
  orderItemId?: number | string | null;
};

const Information = ({ orderItemId }: InformationProps) => {
  const orderId = getQueryParam('id') as string;
  const form = useForm<AdditionalItemInfoTypes>();

  const { data, isLoading } = useAdditionalItemInfoQuery(
    {
      orderId,
      orderItemId: orderItemId as string,
    },
    {
      skip: !orderItemId,
    }
  );

  useEffect(() => {
    if (data?.data) {
      form.reset(data?.data);
    }
  }, [data?.data, form]);

  return (
    <>
      <Card>
        <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4">
          {formFields.map(({ column, fields }) => (
            <div key={column} className="space-y-4">
              {fields.map(({ label, name }) => (
                <InputField
                  key={name}
                  form={form}
                  label={label}
                  name={name}
                  disabled
                />
              ))}
            </div>
          ))}
          <div>
            <div>
              <InputField
                name="qualitydetails"
                form={form}
                label="Quality"
                disabled
              />
            </div>
            <div className="mt-4">
              <NumberInputField
                name="replacementcharge"
                form={form}
                label="Replacement Charge"
                prefix="$"
                disabled
                fixedDecimalScale
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Full Width Item Information */}
      <div className="mt-4">
        <TextAreaField
          name="itemInfo"
          form={form}
          label="Item Information"
          disabled
          rows={6}
        />
      </div>
      <AppSpinner overlay isLoading={isLoading} />
    </>
  );
};

export default Information;
