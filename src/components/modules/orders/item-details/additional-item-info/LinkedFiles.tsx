import LinkedFiles from '@/components/common/LinkedFiles';
import { ORDER_ITEM_INFO_API } from '@/constants/api-constants';
import {
  useDefaultLinkedFileMutation,
  useDeleteLinkedFileMutation,
  useGetLinkedFilesListQuery,
} from '@/redux/features/orders/additional-item-info.api';

const LinkedFilesTab = ({
  orderItemId,
}: {
  orderItemId?: number | string | null;
}) => {
  const { data, isLoading, refetch } = useGetLinkedFilesListQuery(
    orderItemId as string,
    {
      skip: !orderItemId,
    }
  );
  const [deleteFile, { isLoading: isDeleteLoading }] =
    useDeleteLinkedFileMutation();
  const [defaultFile, { isLoading: isDefaultLoading }] =
    useDefaultLinkedFileMutation();

  return (
    <LinkedFiles
      data={data?.data}
      isFetching={isLoading}
      refetch={refetch}
      deleteFile={deleteFile}
      defaultFile={defaultFile}
      isDeleteLoading={isDeleteLoading}
      isDefaultLoading={isDefaultLoading}
      uploadUrl={ORDER_ITEM_INFO_API.UPLOAD(orderItemId as string)}
      downloadUrl={ORDER_ITEM_INFO_API.DOWNLOAD}
      isReuploadBtn={false}
    />
  );
};

export default LinkedFilesTab;
