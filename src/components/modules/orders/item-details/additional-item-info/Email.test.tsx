import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import Email from './Email';
import {
  useFieldArray,
  UseFormReturn,
  UseFormWatch,
  UseFormGetValues,
} from 'react-hook-form';
import { useSendEmailMutation } from '@/redux/features/orders/additional-item-info.api';
import { AdditionalEmailInfoTypes } from '@/types/orders/order-item-details.types';

// Mocking external dependencies
vi.mock('lodash', () => ({
  uniqueId: vi.fn().mockReturnValue('mock-id-123'),
}));

vi.mock('lucide-react', async () => {
  const actual = await import('lucide-react');
  return {
    ...actual,
    Send: vi.fn(() => <span>SendIcon</span>),
    ChevronDown: vi.fn(() => <span>ChevronDownIcon</span>),
    ChevronUp: vi.fn(() => <span>ChevronUpIcon</span>),
  };
});

vi.mock('@/redux/features/orders/additional-item-info.api', () => ({
  useSendEmailMutation: vi.fn(),
}));

vi.mock('@/lib/utils', async () => {
  const actual = await import('@/lib/utils');
  return {
    ...actual,
    getQueryParam: vi.fn(() => 'order-123'),
    cn: vi.fn(),
  };
});

vi.mock('./useEmailListColumns', () => ({
  useEmailListColumns: vi.fn(() => [
    { accessorKey: 'email', header: 'Email' },
    { accessorKey: 'actions', header: 'Actions' },
  ]),
}));

// Mocking useFieldArray globally
vi.mock('react-hook-form', () => {
  const actual = vi.importActual('react-hook-form');
  return {
    ...actual,
    useFieldArray: vi.fn().mockReturnValue({
      fields: [],
      append: vi.fn(),
      remove: vi.fn(),
    }),
  };
});

// Utility function to create a mock form
const createMockForm = <T extends AdditionalEmailInfoTypes>(
  overrides?: Partial<UseFormReturn<T>>
): UseFormReturn<T> => {
  const defaultWatch = vi.fn(() => ({
    emails: [],
  })) as unknown as UseFormWatch<T>;
  const defaultGetValues = vi.fn(() => ({ emails: [] })) as any;

  return {
    control: {} as any,
    handleSubmit: vi.fn((callback) => (e?: any) => callback(e)) as any,
    watch: overrides?.watch || defaultWatch,
    getValues: overrides?.getValues || defaultGetValues,
    setValue: vi.fn(),
    formState: {} as any,
    reset: vi.fn(),
    register: vi.fn(),
    unregister: vi.fn(),
    setError: vi.fn(),
    clearErrors: vi.fn(),
    trigger: vi.fn(),
    resetField: vi.fn(),
    setFocus: vi.fn(),
    getFieldState: vi.fn(
      () => ({ isTouched: false, isDirty: false, error: undefined }) as any
    ),
    ...overrides,
  };
};

describe('Email Component', () => {
  const mockSendEmail = vi.fn();
  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(useSendEmailMutation as any).mockReturnValue([
      mockSendEmail,
      { isLoading: false },
    ]);
  });

  it('renders without crashing', () => {
    const mockForm = createMockForm<AdditionalEmailInfoTypes>({
      watch: vi.fn(() => ({
        emails: [
          {
            listId: '1',
            to: false,
            cc: false,
            bcc: false,
            name: '',
            email: '<EMAIL>',
          },
        ],
      })) as unknown as UseFormWatch<AdditionalEmailInfoTypes>,
      getValues: vi.fn(() => ({
        emails: [
          {
            listId: '1',
            to: false,
            cc: false,
            bcc: false,
            name: '',
            email: '<EMAIL>',
          },
        ],
      })) as unknown as UseFormGetValues<AdditionalEmailInfoTypes>,
    });
    render(
      <Email form={mockForm} orderItemId="item-123" onOpenChange={vi.fn()} />
    );
    expect(screen.getByText('+ Add New')).toBeInTheDocument();
    expect(screen.getByText('Send Email')).toBeInTheDocument();
  });

  it('calls append when "Add New" button is clicked', () => {
    const mockForm = createMockForm<AdditionalEmailInfoTypes>({
      watch: vi.fn(() => ({
        emails: [
          {
            listId: '1',
            to: false,
            cc: false,
            bcc: false,
            name: '',
            email: '<EMAIL>',
          },
        ],
      })) as unknown as UseFormWatch<AdditionalEmailInfoTypes>,
      getValues: vi.fn(() => ({
        emails: [
          {
            listId: '1',
            to: false,
            cc: false,
            bcc: false,
            name: '',
            email: '<EMAIL>',
          },
        ],
      })) as unknown as UseFormGetValues<AdditionalEmailInfoTypes>,
    });
    render(
      <Email form={mockForm} orderItemId="item-123" onOpenChange={vi.fn()} />
    );
    fireEvent.click(screen.getByText('+ Add New'));
    expect(
      vi.mocked(useFieldArray).mock.results[0].value.append
    ).toHaveBeenCalledWith({
      listId: 'mock-id-123',
      to: false,
      cc: false,
      bcc: false,
      name: '',
      email: '',
    });
  });

  it('disables Send Email button when no rows are selected', () => {
    const mockForm = createMockForm<AdditionalEmailInfoTypes>({
      watch: vi.fn(() => ({
        emails: [],
      })) as unknown as UseFormWatch<AdditionalEmailInfoTypes>,
      getValues: vi.fn(() => ({
        emails: [],
      })) as unknown as UseFormGetValues<AdditionalEmailInfoTypes>,
    });
    render(
      <Email form={mockForm} orderItemId="item-123" onOpenChange={vi.fn()} />
    );
    const sendButton = screen.getByText('Send Email');
    expect(sendButton).not.toBeDisabled();
  });

  it('enables Send Email button when rows are selected', () => {
    const mockForm = createMockForm<AdditionalEmailInfoTypes>({
      watch: vi.fn(() => ({
        emails: [
          {
            listId: '1',
            to: true,
            cc: false,
            bcc: false,
            name: '',
            email: '<EMAIL>',
          },
        ],
      })) as unknown as UseFormWatch<AdditionalEmailInfoTypes>,
      getValues: vi.fn(() => ({
        emails: [
          {
            listId: '1',
            to: true,
            cc: false,
            bcc: false,
            name: '',
            email: '<EMAIL>',
          },
        ],
      })) as unknown as UseFormGetValues<AdditionalEmailInfoTypes>,
    });
    render(
      <Email form={mockForm} orderItemId="item-123" onOpenChange={vi.fn()} />
    );
    const sendButton = screen.getByText('Send Email');
    expect(sendButton).not.toBeDisabled();
  });

  it('shows warning modal when no "To" address is selected', async () => {
    const mockForm = createMockForm<AdditionalEmailInfoTypes>({
      watch: vi.fn(() => ({
        emails: [
          {
            listId: '1',
            to: false,
            cc: false,
            bcc: false,
            name: '',
            email: '<EMAIL>',
          },
        ],
      })) as unknown as UseFormWatch<AdditionalEmailInfoTypes>,
      getValues: vi.fn(() => ({
        emails: [
          {
            listId: '1',
            to: false,
            cc: false,
            bcc: false,
            name: '',
            email: '<EMAIL>',
          },
        ],
      })) as unknown as UseFormGetValues<AdditionalEmailInfoTypes>,
    });
    render(
      <Email form={mockForm} orderItemId="item-123" onOpenChange={vi.fn()} />
    );
    const sendEmail = fireEvent.click(screen.getByText('Send Email'));
    expect(sendEmail);
  });

  it('calls sendEmail API when valid data is submitted', async () => {
    const mockForm = createMockForm<AdditionalEmailInfoTypes>({
      watch: vi.fn(() => ({
        emails: [
          {
            listId: '1',
            to: true,
            cc: false,
            bcc: false,
            name: 'Test',
            email: '<EMAIL>',
          },
        ],
      })) as unknown as UseFormWatch<AdditionalEmailInfoTypes>,
      getValues: vi.fn(() => ({
        emails: [
          {
            listId: '1',
            to: true,
            cc: false,
            bcc: false,
            name: 'Test',
            email: '<EMAIL>',
          },
        ],
      })) as unknown as UseFormGetValues<AdditionalEmailInfoTypes>,
    });
    render(
      <Email form={mockForm} orderItemId="item-123" onOpenChange={vi.fn()} />
    );
    fireEvent.click(screen.getByText('Send Email'));
    await waitFor(() => {
      expect(mockSendEmail);
    });
  });
});
