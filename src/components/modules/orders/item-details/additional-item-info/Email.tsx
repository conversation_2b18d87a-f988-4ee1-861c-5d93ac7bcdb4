import { uniqueId } from 'lodash';
import { Send } from 'lucide-react';
import { useCallback, useEffect, useRef, useState } from 'react';
import { SubmitHandler, useFieldArray, UseFormReturn } from 'react-hook-form';

import { useSendEmailMutation } from '@/redux/features/orders/additional-item-info.api';
import { useEmailListColumns } from './useEmailListColumns';

import AppButton from '@/components/common/app-button';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import DataTable from '@/components/common/data-tables';

import { getQueryParam } from '@/lib/utils';
import { AdditionalEmailInfoTypes } from '@/types/orders/order-item-details.types';

interface DeleteDialogState {
  isOpen: boolean;
  index: number | null;
}

type EmailProps = {
  form: UseFormReturn<AdditionalEmailInfoTypes>;
  orderItemId?: number | string | null;
  onOpenChange: () => void;
};

const Email = ({ form, orderItemId, onOpenChange }: EmailProps) => {
  const scrollRef = useRef<HTMLTableElement>(null);
  const orderId = getQueryParam('id') as string;
  const [deleteDialogState, setDeleteDialogState] = useState<DeleteDialogState>(
    { isOpen: false, index: null }
  );
  const [rowSelection, setRowSelection] = useState<Record<string, boolean>>({});

  const [showWarningModal, setShowWarningModal] = useState(false);

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'emails',
  });

  const [sendEmail, { isLoading }] = useSendEmailMutation();

  const toggleDelete = useCallback((index: number | null) => {
    setDeleteDialogState((prev) => ({
      isOpen: !prev.isOpen,
      index,
    }));
  }, []);

  const handleDeleteItem = async () => {
    if (deleteDialogState.index) {
      remove(deleteDialogState.index);
    }
    setDeleteDialogState({ isOpen: false, index: null });
  };

  const columns = useEmailListColumns({ form, toggleDelete, rowSelection });

  // Add a new empty email entry
  const handleAddNewEmail = useCallback(() => {
    append({
      listId: uniqueId(),
      to: false,
      cc: false,
      bcc: false,
      name: '',
      email: '',
    });
  }, [append]);

  // Auto-scroll to bottom when a new row is added
  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, [fields]);

  const toggleWarningModal = () => {
    setShowWarningModal((prev) => !prev);
  };

  const handleSubmitEmails: SubmitHandler<
    AdditionalEmailInfoTypes
  > = async () => {
    const selectedIds = Object.keys(rowSelection).filter(
      (id) => rowSelection[id]
    );

    const toRecipients = form
      .watch('emails')
      .filter(
        (email) => email.to && selectedIds.includes(email?.listId ?? '0')
      );

    if (toRecipients.length === 0) {
      toggleWarningModal();
      return;
    }

    const payload = toRecipients.map(({ to, cc, bcc, name, email }) => ({
      to,
      cc,
      bcc,
      name,
      email,
    }));

    await sendEmail({
      body: { emailRecipients: payload, body: '', subject: '' },
      orderId,
      orderItemId: orderItemId as string,
    });
    onOpenChange();
  };

  const handleConfirmModal = () => {
    toggleWarningModal();
  };

  return (
    <div className="flex flex-col h-full">
      {/* Email list table */}
      <div className="flex-grow">
        <DataTable
          onScrollRef={scrollRef}
          data={fields}
          columns={columns}
          enablePagination={false}
          tableClassName="max-h-[370px] 2xl:max-h-[450px] overflow-auto"
          enableMultiRowSelection={true}
          enableRowSelection={true}
          rowSelection={rowSelection}
          bindingKey="listId"
          totalItems={fields?.length}
          onRowSelectionChange={setRowSelection}
        />

        {/* Add new email button */}
        <div className="w-full p-2 border-grayScale-20 border-b border-x rounded-b-md">
          <AppButton
            label="+ Add New"
            className="bg-brand-teal-Default hover:bg-brand-teal-secondary"
            onClick={handleAddNewEmail}
          />
        </div>
      </div>

      {/* Submit button */}
      <div className="w-full p-2 bottom-0">
        <AppButton
          disabled={Object.keys(rowSelection).length === 0}
          icon={Send}
          isLoading={isLoading}
          onClick={form.handleSubmit(handleSubmitEmails)}
          iconClassName="w-4 h-4"
          label="Send Email"
          className=" bg-brand-teal-Default hover:bg-brand-teal-secondary"
        />
      </div>

      {/* Warning modal */}
      <AppConfirmationModal
        open={showWarningModal}
        title="Warning"
        description={<div>Please select at least one 'To' address</div>}
        handleSubmit={handleConfirmModal}
        submitLabel="Ok"
      />

      <AppConfirmationModal
        description={<div>Are you sure you want to delete this record?</div>}
        open={deleteDialogState.isOpen}
        handleCancel={() =>
          setDeleteDialogState({ isOpen: false, index: null })
        }
        handleSubmit={handleDeleteItem}
      />
    </div>
  );
};

export default Email;
