import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import KitToolbar from './KitToolbar';

// Mock the AppButton component
vi.mock('@/components/common/app-button', () => ({
  default: vi.fn(({ icon: Icon, onClick, disabled, tooltip }) => (
    <button
      onClick={onClick}
      disabled={disabled}
      aria-label={tooltip}
      data-testid={`toolbar-button-${tooltip.toLowerCase().replace(/\s+/g, '-')}`}
    >
      {Icon && (
        <Icon
          data-testid={`icon-${tooltip.toLowerCase().replace(/\s+/g, '-')}`}
        />
      )}
    </button>
  )),
}));

describe('KitToolbar Component', () => {
  const mockSetActiveTab = vi.fn();
  const mockSetKitItemId = vi.fn();
  it('renders all toolbar buttons with icons', () => {
    render(
      <KitToolbar
        setActiveTab={mockSetActiveTab}
        setKitItemId={mockSetKitItemId}
        selectedItem={null}
      />
    );
    expect(
      screen.getByTestId('toolbar-button-item-search')
    ).toBeInTheDocument();
    expect(screen.getByTestId('icon-item-search')).toBeInTheDocument();
    expect(
      screen.getByTestId('toolbar-button-sub-rent-item')
    ).toBeInTheDocument();
    expect(screen.getByTestId('icon-sub-rent-item')).toBeInTheDocument();
    expect(
      screen.getByTestId('toolbar-button-change-serialized-item')
    ).toBeInTheDocument();
    expect(
      screen.getByTestId('icon-change-serialized-item')
    ).toBeInTheDocument();
  });

  it('calls setActiveTab with correct tab when buttons are clicked', () => {
    render(
      <KitToolbar
        setActiveTab={mockSetActiveTab}
        setKitItemId={mockSetKitItemId}
        selectedItem={null}
      />
    );
    fireEvent.click(screen.getByTestId('toolbar-button-item-search'));
    expect(mockSetActiveTab).toHaveBeenCalledWith('kit-item-lookup');
    fireEvent.click(screen.getByTestId('toolbar-button-sub-rent-item'));
    expect(mockSetActiveTab);
    fireEvent.click(
      screen.getByTestId('toolbar-button-change-serialized-item')
    );
    expect(mockSetActiveTab).toHaveBeenCalledWith('kit-item-lookup');
  });

  it('disables buttons based on selectedItem', () => {
    const { rerender } = render(
      <KitToolbar
        setActiveTab={mockSetActiveTab}
        setKitItemId={mockSetKitItemId}
        selectedItem={null}
      />
    );
    expect(screen.getByTestId('toolbar-button-sub-rent-item')).toBeDisabled();
    expect(
      screen.getByTestId('toolbar-button-change-serialized-item')
    ).toBeDisabled();
    expect(
      screen.getByTestId('toolbar-button-additional-item-info')
    ).toBeDisabled();
    rerender(
      <KitToolbar
        setActiveTab={mockSetActiveTab}
        setKitItemId={mockSetKitItemId}
        selectedItem={{ type: 'NON_RENTAL' }}
      />
    );
    expect(screen.getByTestId('toolbar-button-sub-rent-item')).toBeDisabled();
    expect(
      screen.getByTestId('toolbar-button-change-serialized-item')
    ).not.toBeDisabled();
    expect(
      screen.getByTestId('toolbar-button-additional-item-info')
    ).not.toBeDisabled();
    rerender(
      <KitToolbar
        setActiveTab={mockSetActiveTab}
        setKitItemId={mockSetKitItemId}
        selectedItem={{ type: 'RENTAL_ITEM' }}
      />
    );
    expect(
      screen.getByTestId('toolbar-button-sub-rent-item')
    ).not.toBeDisabled();
    expect(
      screen.getByTestId('toolbar-button-change-serialized-item')
    ).not.toBeDisabled();
    expect(
      screen.getByTestId('toolbar-button-additional-item-info')
    ).not.toBeDisabled();
  });

  it('applies correct styling to buttons', () => {
    render(
      <KitToolbar
        setActiveTab={mockSetActiveTab}
        setKitItemId={mockSetKitItemId}
        selectedItem={null}
      />
    );
    const buttons = screen.getAllByRole('button');
    expect(buttons);
  });

  it('does not disable buttons that are always enabled', () => {
    render(
      <KitToolbar
        setActiveTab={mockSetActiveTab}
        setKitItemId={mockSetKitItemId}
        selectedItem={null}
      />
    );
    expect(screen.getByTestId('toolbar-button-item-search')).not.toBeDisabled();
    expect(
      screen.getByTestId('toolbar-button-overbooked-item-info')
    ).not.toBeDisabled();
    expect(
      screen.getByTestId('toolbar-button-availability-info')
    ).not.toBeDisabled();
  });
});
