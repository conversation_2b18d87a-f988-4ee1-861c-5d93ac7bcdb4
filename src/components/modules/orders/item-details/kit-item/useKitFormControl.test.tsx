import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook } from '@testing-library/react';
import { useKitItemFormControl } from './useKitFormControl';
import { useForm, useFieldArray } from 'react-hook-form';
import { formatDate } from '@/lib/utils';
import { DATE_FORMAT_YYYYMMDD } from '@/lib/utils';

// Mock react-hook-form
vi.mock('react-hook-form', () => ({
  useForm: vi.fn(),
  useFieldArray: vi.fn(),
}));

describe('useKitItemFormControl', () => {
  const mockUseForm = {
    control: {},
    reset: vi.fn(),
    formState: { errors: {} },
  };

  const mockUseFieldArray = {
    fields: [],
    append: vi.fn(),
    remove: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
    (useForm as any)
      .mockReturnValueOnce(mockUseForm)
      .mockReturnValueOnce(mockUseForm)
      .mockReturnValueOnce(mockUseForm)
      .mockReturnValueOnce({ ...mockUseForm, formState: { errors: {} } });
    (useFieldArray as any).mockReturnValue(mockUseFieldArray);
  });

  it('should initialize all forms with correct defaults', () => {
    const { result } = renderHook(() => useKitItemFormControl());
    expect(useForm).toHaveBeenCalledTimes(4);
    expect(useFieldArray).toHaveBeenCalledWith({
      control: mockUseForm.control,
      name: 'items',
    });
    expect(result.current).toEqual({
      kitForm: mockUseForm,
      additionalItemInfoForm: { ...mockUseForm, formState: { errors: {} } },
      availabilityForm: mockUseForm,
      subRentForm: mockUseForm,
      fields: mockUseFieldArray.fields,
      append: mockUseFieldArray.append,
      remove: mockUseFieldArray.remove,
    });
  });

  it('should set up availability form with default values when selectedItem is provided', () => {
    const mockSelectedItem = {
      itemId: { value: '123', label: 'Test Item' },
    } as any;
    const currentDate = formatDate(new Date(), DATE_FORMAT_YYYYMMDD);
    const expectedDefaultValues = {
      itemId: mockSelectedItem.itemId,
      storeLocationId: '0',
      month: currentDate,
      date: currentDate,
      includeQuotes: 'false',
      includeSubRentAndPO: false,
      includePastOrders: false,
    };
    const { result } = renderHook(() =>
      useKitItemFormControl(mockSelectedItem)
    );
    expect(useForm).toHaveBeenCalledTimes(4);
    expect(result.current.availabilityForm.reset).toHaveBeenCalledWith(
      expectedDefaultValues
    );
  });

  it('should initialize additionalItemInfoForm with onChange mode', () => {
    renderHook(() => useKitItemFormControl());
    expect(useForm).toHaveBeenCalledWith({
      mode: 'onChange',
    });
  });

  it('should return field array methods for kit items', () => {
    const { result } = renderHook(() => useKitItemFormControl());
    expect(result.current.fields).toBe(mockUseFieldArray.fields);
    expect(result.current.append).toBe(mockUseFieldArray.append);
    expect(result.current.remove).toBe(mockUseFieldArray.remove);
  });

  it('should not reset availability form if no selectedItem is provided', () => {
    const { result } = renderHook(() => useKitItemFormControl());
    expect(result.current.availabilityForm.reset);
  });
});
