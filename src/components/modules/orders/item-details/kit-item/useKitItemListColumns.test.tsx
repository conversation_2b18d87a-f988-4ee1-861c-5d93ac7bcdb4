import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook } from '@testing-library/react';
import { useKitItemListColumns } from './useKitItemListColumns';
import { useFormContext, UseFormReturn } from 'react-hook-form';
import { KitItemsFormDataTypes } from '@/types/orders/order-item-details.types';

// Mock dependencies
vi.mock('react-hook-form', () => ({
  useFormContext: vi.fn(),
}));

vi.mock('@/components/common/ReactSelect', () => ({
  default: vi.fn(() => <div data-testid="react-select" />),
}));

vi.mock('@/components/forms/number-input-field', () => ({
  default: vi.fn(() => <input data-testid="number-input" />),
}));

vi.mock('@/components/forms/input-field', () => ({
  default: vi.fn(() => <input data-testid="text-input" />),
}));

vi.mock('@/components/forms/select', () => ({
  default: vi.fn(() => <select data-testid="select" />),
}));

vi.mock('@/components/common/app-button', () => ({
  default: vi.fn(({ onClick }) => (
    <button onClick={onClick} data-testid="view-details-button">
      View Details
    </button>
  )),
}));

vi.mock('@/components/common/data-tables/ActionColumn', () => ({
  default: vi.fn(({ onDelete }) => (
    <div data-testid="action-column">
      <button onClick={onDelete} data-testid="delete-button">
        Delete
      </button>
    </div>
  )),
}));

describe('useKitItemListColumns', () => {
  const mockForm = {
    watch: vi.fn(),
    control: {},
  };

  const mockOrderForm = {
    watch: vi.fn((field) => (field === 'isDeleted' ? false : undefined)),
  };

  const mockProps = {
    form: mockForm as unknown as UseFormReturn<KitItemsFormDataTypes>,
    itemType: { data: [{ value: '1', label: 'Type 1' }] },
    itemTypeLoading: false,
    handleItemIdChange: vi.fn(),
    setActiveTab: vi.fn(),
    setKitItemId: vi.fn(),
    toggleDelete: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
    (useFormContext as any).mockReturnValue(mockOrderForm);
    mockForm.watch.mockImplementation((field) => {
      if (field?.includes('itemId')) return { value: '123' };
      if (field?.includes('serialNumber')) return null;
      if (field?.includes('listId')) return 1;
      return undefined;
    });
  });

  it('should return an array of column definitions', () => {
    const { result } = renderHook(() => useKitItemListColumns(mockProps));
    expect(Array.isArray(result.current)).toBe(true);
    expect(result.current.length).toBe(6);
    expect(result.current[0].accessorKey).toBe('itemId');
    expect(result.current[1].accessorKey).toBe('quantity');
    expect(result.current[2].accessorKey).toBe('description');
    expect(result.current[3].accessorKey).toBe('type');
    expect(result.current[4].accessorKey).toBe('subRental');
    expect(result.current[5].id).toBe('action');
  });

  it('should render ReactSelect for itemId column', () => {
    const { result } = renderHook(() => useKitItemListColumns(mockProps));
    const itemIdColumn = result.current[0];
    expect(itemIdColumn.cell).toBeDefined();
  });

  it('should render NumberInputField for quantity column with proper validation', () => {
    const { result } = renderHook(() => useKitItemListColumns(mockProps));
    const quantityColumn = result.current[1];
    expect(quantityColumn.cell).toBeDefined();
  });

  it('should disable quantity input when no itemId is selected', () => {
    mockForm.watch.mockImplementation((field) => {
      if (field?.includes('itemId')) return null;
      return undefined;
    });
    const { result } = renderHook(() => useKitItemListColumns(mockProps));
    expect(result);
  });

  it('should render InputField for description column', () => {
    const { result } = renderHook(() => useKitItemListColumns(mockProps));
    const descriptionColumn = result.current[2];

    expect(descriptionColumn.cell).toBeDefined();
  });

  it('should render SelectWidget for type column with itemType options', () => {
    const { result } = renderHook(() => useKitItemListColumns(mockProps));
    const typeColumn = result.current[3];
    expect(typeColumn.cell).toBeDefined();
  });

  it('should render ActionColumnMenu with proper callbacks', () => {
    const { result } = renderHook(() => useKitItemListColumns(mockProps));
    const actionColumn = result.current[5];
    expect(actionColumn.cell).toBeDefined();
  });

  it('should disable actions when order is deleted', () => {
    (useFormContext as any).mockReturnValue({
      watch: vi.fn((field) => (field === 'isDeleted' ? true : undefined)),
    });
    const { result } = renderHook(() => useKitItemListColumns(mockProps));
    const actionColumn = result.current[5];
    expect(actionColumn);
  });

  it('should call setActiveTab and setKitItemId when View Details is clicked', () => {
    const { result } = renderHook(() => useKitItemListColumns(mockProps));
    const actionColumn = result.current[5];
    expect(actionColumn);
  });

  it('should call toggleDelete when delete is clicked', () => {
    const mockToggleDelete = vi.fn();
    const { result } = renderHook(() =>
      useKitItemListColumns({ ...mockProps, toggleDelete: mockToggleDelete })
    );
    const actionColumn = result.current[5];
    expect(actionColumn);
  });

  it('should memoize the columns array', () => {
    const { result, rerender } = renderHook(
      (props) => useKitItemListColumns(props),
      { initialProps: mockProps }
    );
    const firstResult = result.current;
    rerender(mockProps);
    expect(result.current).toBe(firstResult);
  });
});
