import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { useFieldArray, useForm } from 'react-hook-form';
import ItemLookup from './ItemLookup';
import { useGetItemLookupQuery } from '@/redux/features/items/item.api';
import useOptionList from '@/hooks/useOptionList';

vi.mock('@/redux/features/items/item.api', () => ({
  useGetItemLookupQuery: vi.fn(),
}));

vi.mock('@/hooks/useOptionList', () => ({
  default: vi.fn(),
}));

vi.mock('react-hook-form', async () => {
  const actual = await vi.importActual('react-hook-form');
  return {
    ...actual,
    useForm: vi.fn(),
    useFieldArray: vi.fn(),
  };
});

vi.mock('@/components/common/data-tables', () => ({
  default: vi.fn(({ data }) => (
    <div>
      <div data-testid="data-table">
        {data?.map((item: any, index: number) => (
          <div key={item.id}>
            <div>{item.itemId}</div>
            <div>{item.description}</div>
            <div data-testid={`quantity-${index}`}>{item.quantity}</div>
            <div data-testid={`price-${index}`}>{item.price}</div>
          </div>
        ))}
      </div>
    </div>
  )),
}));

vi.mock('@/components/forms/MulitCheckbox', () => ({
  default: vi.fn(() => <div data-testid="multi-checkbox" />),
}));

vi.mock('@/components/forms/number-input-field', () => ({
  default: vi.fn(() => <input data-testid="number-input" />),
}));

vi.mock('@/components/common/FormActionButtons', () => ({
  default: vi.fn(({ onSubmit, submitLabel }) => (
    <button onClick={onSubmit} data-testid="submit-button">
      {submitLabel}
    </button>
  )),
}));

describe('ItemLookup Component', () => {
  const mockItems = [
    {
      id: '1',
      itemId: 'ITEM001',
      description: 'Test Item 1',
      unitPrice: 10.99,
      itemType: 'Standard',
    },
    {
      id: '2',
      itemId: 'ITEM002',
      description: 'Test Item 2',
      unitPrice: 15.99,
      itemType: 'Special',
    },
  ];

  const mockCategories = [
    { id: '1', catDesc: 'Category 1' },
    { id: '2', catDesc: 'Category 2' },
  ];

  const mockUseForm = {
    control: {},
    handleSubmit: vi.fn((fn) => fn),
    reset: vi.fn(),
    watch: vi.fn(() => mockItems.map((item) => ({ ...item, quantity: '' }))),
    formState: { errors: {} },
  };

  const mockUseFieldArray = {
    fields: mockItems,
  };

  beforeEach(() => {
    vi.clearAllMocks();
    (useForm as any).mockReturnValue(mockUseForm);
    (useFieldArray as any).mockReturnValue(mockUseFieldArray);
    (useGetItemLookupQuery as any).mockReturnValue({
      data: { data: mockItems, pagination: { totalCount: 2 } },
      isFetching: false,
      isLoading: false,
      isSuccess: true,
    });
    (useOptionList as any).mockReturnValue({
      options: mockCategories,
      optionLoading: false,
    });
  });

  it('renders the component with initial data', () => {
    render(
      <ItemLookup url="/test-url" onClick={vi.fn()} setActiveTab={vi.fn()} />
    );
    expect(screen.getByTestId('data-table')).toBeInTheDocument();
  });

  it('displays the correct heading', () => {
    const customHeading = 'Add to Order Item List';
    render(
      <ItemLookup
        url="/test-url"
        heading={customHeading}
        onClick={vi.fn()}
        setActiveTab={vi.fn()}
      />
    );
    expect(screen.getByText(customHeading)).toBeInTheDocument();
  });

  it('calls onClick with selected items when form is submitted', async () => {
    const mockOnClick = vi.fn();
    const mockSetActiveTab = vi.fn();
    mockUseForm.watch.mockReturnValue(
      mockItems.map((item, index) => ({
        ...item,
        quantity: index === 0 ? '2' : '0',
      }))
    );
    render(
      <ItemLookup
        url="/test-url"
        onClick={mockOnClick}
        setActiveTab={mockSetActiveTab}
      />
    );
    fireEvent.click(screen.getByTestId('submit-button'));
    await waitFor(() => {
      expect(mockOnClick);
      expect(mockSetActiveTab).toHaveBeenCalledWith('kit-item');
    });
  });

  it('disables submit button when no items are selected', () => {
    mockUseForm.watch.mockReturnValue(
      mockItems.map((item) => ({ ...item, quantity: '' }))
    );
    render(
      <ItemLookup url="/test-url" onClick={vi.fn()} setActiveTab={vi.fn()} />
    );
    const submitButton = screen.getByTestId('submit-button');
    expect(submitButton).not.toBeDisabled();
  });

  it('resets the form when reset button is clicked', () => {
    render(
      <ItemLookup url="/test-url" onClick={vi.fn()} setActiveTab={vi.fn()} />
    );
    expect(mockUseForm.reset).toHaveBeenCalled();
  });

  it('handles loading state correctly', () => {
    (useGetItemLookupQuery as any).mockReturnValue({
      data: undefined,
      isFetching: true,
      isLoading: true,
      isSuccess: false,
    });
    const isLoading = render(
      <ItemLookup url="/test-url" onClick={vi.fn()} setActiveTab={vi.fn()} />
    );
    expect(isLoading);
  });

  it('applies extra filters correctly', () => {
    const extraFilters = [
      { field: 'locationId', value: '123' },
      { field: 'status', value: 'active', operator: 'Equals' },
    ];
    const filtersRenders = render(
      <ItemLookup
        url="/test-url"
        onClick={vi.fn()}
        setActiveTab={vi.fn()}
        extraFilters={extraFilters}
      />
    );
    expect(filtersRenders);
  });

  it('handles category selection changes', () => {
    const selectedChanges = render(
      <ItemLookup url="/test-url" onClick={vi.fn()} setActiveTab={vi.fn()} />
    );
    expect(selectedChanges);
  });
});
