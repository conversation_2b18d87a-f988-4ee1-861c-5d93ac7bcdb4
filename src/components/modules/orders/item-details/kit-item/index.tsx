import { useCallback, useEffect, useMemo, useState } from 'react';
import BreadCrumb from './BreadCrumb';
import { FormProvider, UseFormReturn } from 'react-hook-form';

import { generateKitItemsTree } from '@/constants/kit-components-constants';
import { getQueryParam } from '@/lib/utils';
import { useGetKitListQuery } from '@/redux/features/orders/item-details.api';
import {
  KitItemListTypes,
  KitItemsFormDataTypes,
  SubRentItemListTypes,
} from '@/types/orders/order-item-details.types';
import { useKitItemFormControl } from './useKitFormControl';
import { useKitSubRental } from './useKitSubRental';
import { useOverBookedData } from './useOverBookedData';
import { useTabManagement } from './useTabManagement';
import { OverBookedItemInfoTypes } from '@/types/order.types';

const mapItemsToFormValues = (
  items: KitItemListTypes[]
): KitItemsFormDataTypes => ({
  items:
    items?.map((item) => ({
      serialNumber: item?.serialNumber || '',
      parentId: item?.parentId ?? null,
      listId: item.id,
      itemId: {
        label: item.itemIdString || '',
        value: item.itemId?.toString() || '',
      },
      description: item.description || '',
      quantity: item.quantity || '',
      price: item.price || '0',
      subRental: item?.subRental ? 'Yes' : 'No',
      total: item.total || 0,
      type: item?.type ?? '',
      isOverbooked: item?.isOverbooked,
    })) || [],
});

interface KitItemInfoPropsType {
  open: boolean;
  onOpenChange: () => void;
  orderItemId: string | number | null;
  // itemId: ItemType;
}

const KitItemInfo = ({
  open,
  onOpenChange,
  orderItemId,
}: KitItemInfoPropsType) => {
  const orderId = getQueryParam('id') as string;
  const { activeTab, handleSetActiveTab } = useTabManagement('kit-item');
  const [activeInfoTab, setActiveInfoTab] = useState('information');
  const [kitItemId, setKitItemId] = useState<number | null>(null);
  const [rowSelection, setRowSelection] = useState<Record<string, boolean>>({});

  // Fetch data for kit items
  const { data, isLoading } = useGetKitListQuery(String(orderItemId), {
    skip: !orderItemId,
  });

  const defaultValues = useMemo(
    () => mapItemsToFormValues(data?.data as KitItemListTypes[]),
    [data?.data]
  );

  const selectedItemId = useMemo(
    () => Object.keys(rowSelection).at(0),
    [rowSelection]
  );

  const selectedItem = useMemo(() => {
    return defaultValues?.items?.find(
      (item) => item?.listId === Number(selectedItemId)
    );
  }, [defaultValues?.items, selectedItemId]);

  // Form initialization
  const {
    kitForm,
    additionalItemInfoForm,
    availabilityForm,
    fields,
    append,
    remove,
  } = useKitItemFormControl(selectedItem);

  // Handle data for overbooked items
  const { serialData, serialList, serialLoading, overBookedForm } =
    useOverBookedData(selectedItem);

  const {
    customerData,
    setCustomerData,
    rowSelection: subRentalRowSelection,
    setRowSelection: setSubRentalRowSelection,
    handleUpdateSubRent,
    isUpdating,
    selectedSubRents,
    sorting,
    setSorting,
    handleNewSubRental,
    setSelectedSubRents,
    orderSubRents,
    isLoading: isSubRentLoading,
    form: subRentForm,
  } = useKitSubRental({
    setActiveTab: handleSetActiveTab,
    orderItemId: Number(Object.keys(rowSelection)[0]),
  });

  // Update kit form when data changes
  useEffect(() => {
    if (data?.data) {
      kitForm.reset(defaultValues);
    }
  }, [data?.data, defaultValues, kitForm]);

  // Handle tab changes for additional info
  const handleTabChange = useCallback((value: string) => {
    setActiveInfoTab(value);
  }, []);

  // Transform and add selected items to the list
  const handleAddItemLookupData = useCallback(
    (selectedItems: any[]) => {
      const transformedItems = selectedItems.map((item) => ({
        ...item,
        isModified: true,
        disabledCheckBox: true,
        type: item.itemType,
        subRental: 'No',
        ...(item.price &&
          item.quantity && {
            total: Number(item.price) * Number(item.quantity),
          }),
      }));

      append(transformedItems);
    },
    [append]
  );

  // Generate component tree
  const treeRoot = useMemo(() => {
    return generateKitItemsTree({
      orderId,
      kitForm,
      additionalItemInfoForm,
      availabilityForm,
      overBookedForm,
      customerData,
      serialData,
      serialList,
      serialLoading,
      fields,
      append,
      remove,
      onOpenChange,
      setActiveTab: handleSetActiveTab,
      handleAddItemLookupData,
      activeInfoTab,
      handleTabChange,
      rowSelection,
      setRowSelection,
      kitItemId: kitItemId as number,
      setKitItemId,
      isLoading,
      selectedItem,
      orderItemId,
      setCustomerData,
      subRentingForm: subRentForm,
      subRentalRowSelection,
      setSubRentalRowSelection,
      handleUpdateSubRent,
      isUpdating,
      selectedSubRents: selectedSubRents,
      sorting,
      setSorting,
      handleNewSubRental,
      setSelectedSubRents,
      orderSubRents,
      isSubRentLoading,
    });
  }, [
    orderId,
    kitForm,
    additionalItemInfoForm,
    availabilityForm,
    subRentForm,
    overBookedForm,
    customerData,
    serialData,
    serialList,
    serialLoading,
    fields,
    append,
    remove,
    onOpenChange,
    handleSetActiveTab,
    handleAddItemLookupData,
    activeInfoTab,
    handleTabChange,
    rowSelection,
    kitItemId,
    isLoading,
    selectedItem,
    orderItemId,
    setCustomerData,
    subRentalRowSelection,
    setSubRentalRowSelection,
    handleUpdateSubRent,
    isUpdating,
    selectedSubRents,
    sorting,
    setSorting,
    handleNewSubRental,
    setSelectedSubRents,
    orderSubRents,
    isSubRentLoading,
  ]);

  type CombinedFormType = SubRentItemListTypes | OverBookedItemInfoTypes;

  const currentForm: UseFormReturn<CombinedFormType> | any = [
    'sub-renting',
    'new-sub-rent',
    'customer-lookup',
  ].includes(activeTab)
    ? subRentForm
    : ['overbooked-item', 'item-lookup'].includes(activeTab)
      ? overBookedForm
      : availabilityForm;

  return (
    <FormProvider {...currentForm}>
      <BreadCrumb
        treeRoot={treeRoot}
        activeTab={activeTab}
        isOpen={open}
        onOpenChange={onOpenChange}
        setActiveTab={handleSetActiveTab}
        className="max-w-[95%] 2xl:max-w-[70%]"
        contentClassName="h-[530px] 2xl:h-[700px] overflow-y-auto"
      />
    </FormProvider>
  );
};

export default KitItemInfo;
