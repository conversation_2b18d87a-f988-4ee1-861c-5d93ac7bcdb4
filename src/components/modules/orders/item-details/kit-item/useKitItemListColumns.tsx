import EyeIcon from '@/assets/icons/EyeIcon';
import AppButton from '@/components/common/app-button';
import ActionColumnMenu from '@/components/common/data-tables/ActionColumn';
import ReactSelect from '@/components/common/ReactSelect';
import In<PERSON><PERSON>ield from '@/components/forms/input-field';
import NumberInput<PERSON>ield from '@/components/forms/number-input-field';
import SelectWidget from '@/components/forms/select';
import { PURCHASE_API_ROUTES } from '@/constants/api-constants';
import {
  QUANTITY_VALIDATION_RULE,
  SERIAL_VALIDATION_RULE,
  TEXT_VALIDATION_RULE,
} from '@/constants/validation-constants';
import { OrderInformationTypes } from '@/types/order.types';
import { KitItemsFormDataTypes } from '@/types/orders/order-item-details.types';
import { useMemo } from 'react';
import { useFormContext, UseFormReturn } from 'react-hook-form';

interface UseItemListColumnsProps {
  form: UseFormReturn<KitItemsFormDataTypes>;
  itemType: any;
  itemTypeLoading: boolean;
  handleItemIdChange: (
    value: { value: string },
    rowIndex: number
  ) => Promise<void>;
  setActiveTab: any;
  setKitItemId: React.Dispatch<React.SetStateAction<number | null>>;
  toggleDelete: (id: number | null, index: number | null) => void;
}

export const useKitItemListColumns = ({
  form,
  handleItemIdChange,
  setActiveTab,
  setKitItemId,
  toggleDelete,
  itemType,
  itemTypeLoading,
}: UseItemListColumnsProps) => {
  const orderForm = useFormContext<OrderInformationTypes>();
  const isDeleted = orderForm.watch('isDeleted');

  return useMemo(
    () => [
      {
        accessorKey: 'itemId',
        header: 'Item ID',
        size: 100,
        enableSorting: true,
        cell: ({ row }: any) => (
          <ReactSelect
            placeholder="Select Item ID"
            className="w-[180px] bg-white"
            name={`items.${row.index}.itemId`}
            form={form}
            onSelectChange={(value) => handleItemIdChange(value, row.index)}
            url={PURCHASE_API_ROUTES.GENERIC_ITEM_LOOKUP}
            labelKey="itemId"
            valueKey="id"
            disabled={isDeleted || !!form.watch(`items.${row.index}.listId`)}
            validation={TEXT_VALIDATION_RULE}
            extraFilters={[{ field: 'isKitAllowded', value: 'false' }]}
          />
        ),
      },

      {
        accessorKey: 'quantity',
        header: 'Qty',
        size: 120,
        cell: ({ row }: any) => {
          const itemId = form.watch(`items.${row.index}.itemId`);
          const serialNumber = form.watch(`items.${row.index}.serialNumber`);
          const validation = serialNumber
            ? SERIAL_VALIDATION_RULE
            : itemId?.value
              ? QUANTITY_VALIDATION_RULE
              : {};

          return (
            <NumberInputField
              form={form}
              placeholder="Quantity"
              name={`items.${row.index}.quantity`}
              className="w-36 h-8"
              pClassName="p-1"
              maxLength={5}
              decimalScale={0}
              disabled={!itemId?.value || isDeleted}
              validation={validation}
            />
          );
        },
      },
      {
        accessorKey: 'description',
        header: 'Item Description',
        size: 220,
        cell: ({ row }: any) => {
          const itemId = form.watch(`items.${row.index}.itemId`);
          return (
            <InputField
              form={form}
              placeholder="Description"
              name={`items.${row.index}.description`}
              disabled={!itemId?.value || isDeleted}
              className="h-8"
              pClassName="p-1"
            />
          );
        },
      },
      {
        accessorKey: 'type',
        header: 'Type',
        size: 160,
        cell: ({ row }: any) => {
          return (
            <SelectWidget
              name={`items.${row.index}.type`}
              optionsList={itemType?.data ?? []}
              parentClassName="w-[150px] bg-white"
              placeholder="Select Item Type"
              form={form}
              isLoading={itemTypeLoading}
              isClearable={false}
              disabled={true || isDeleted}
            />
          );
        },
      },
      {
        accessorKey: 'subRental',
        header: 'SR',
        size: 60,
      },

      {
        size: 50,
        id: 'action',
        header: 'Actions',
        cell: ({ row }: any) => (
          <ActionColumnMenu
            customEdit={
              <AppButton
                label="View Details"
                variant="neutral"
                icon={EyeIcon}
                disabled={isDeleted || !row?.original?.listId}
                onClick={() => {
                  setActiveTab('additional-item-info');
                  setKitItemId(row?.original?.listId);
                }}
              />
            }
            disabled={isDeleted}
            onDelete={() => toggleDelete(row?.original?.listId, row?.index)}
          />
        ),
      },
    ],
    [
      form,
      handleItemIdChange,
      isDeleted,
      itemType?.data,
      itemTypeLoading,
      setActiveTab,
      setKitItemId,
      toggleDelete,
    ]
  );
};
