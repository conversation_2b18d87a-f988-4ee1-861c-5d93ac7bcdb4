import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook } from '@testing-library/react';
import { useOverBookedData } from './useOverBookedData';
import { useGetSerialInfoByItemQuery } from '@/redux/features/orders/order.api';
import { useForm, useFormContext } from 'react-hook-form';

// Mock dependencies
vi.mock('@/lib/utils', () => ({
  formatDate: vi.fn(() => '2023-01-01'),
  convertToFloat: vi.fn(() => 0),
  DATE_FORMAT_YYYYMMDD: 'YYYY-MM-DD',
}));

vi.mock('react-hook-form', () => ({
  useForm: vi.fn(() => ({
    reset: vi.fn(),
    watch: vi.fn(() => ({ value: '123' })),
  })),
  useFormContext: vi.fn(),
}));

vi.mock('@/redux/features/orders/order.api', () => ({
  useGetSerialInfoByItemQuery: vi.fn(),
}));

describe('useOverBookedData', () => {
  const mockSelectedItem = {
    itemId: { value: '123', label: 'Test Item' },
  } as any;

  const mockOrderFormValues = {
    deliveryOrder: {
      id: 1,
      deliveryDate: '2023-01-10',
      pickupDate: '2023-01-15',
    },
    shipOrder: {
      id: 2,
      shipDate: '2023-01-20',
      returnArrivalDate: '2023-01-25',
    },
    willCallOrder: {
      id: 3,
      pickupDate: '2023-01-30',
      returnDate: '2023-02-05',
    },
  };

  const mockSerialData = {
    data: {
      data: [{ serialNo: 'SERIAL-001' }, { serialNo: 'SERIAL-002' }],
    },
  };

  beforeEach(() => {
    vi.clearAllMocks();
    (useFormContext as any).mockReturnValue({
      getValues: vi.fn(() => mockOrderFormValues),
    });
    (useGetSerialInfoByItemQuery as any).mockReturnValue({
      data: mockSerialData,
      isFetching: false,
    });
  });

  it('should initialize with default values', () => {
    const { result } = renderHook(() => useOverBookedData(mockSelectedItem));
    expect(result.current.serialList).toEqual([
      { label: 'SERIAL-001', value: 'SERIAL-001' },
      { label: 'SERIAL-002', value: 'SERIAL-002' },
    ]);
    expect(result.current.serialLoading).toBe(false);
    expect(result.current.deliveryDate).toBe('2023-01-10');
    expect(result.current.pickupDate).toBe('2023-01-15');
  });

  it('should use deliveryOrder dates when deliveryOrder exists', () => {
    (useFormContext as any).mockReturnValueOnce({
      getValues: vi.fn(() => ({
        deliveryOrder: {
          id: 1,
          deliveryDate: '2023-01-10',
          pickupDate: '2023-01-15',
        },
      })),
    });
    const { result } = renderHook(() => useOverBookedData(mockSelectedItem));
    expect(result.current.deliveryDate).toBe('2023-01-10');
    expect(result.current.pickupDate).toBe('2023-01-15');
  });

  it('should use shipOrder dates when shipOrder exists', () => {
    (useFormContext as any).mockReturnValueOnce({
      getValues: vi.fn(() => ({
        shipOrder: {
          id: 2,
          shipDate: '2023-01-20',
          returnArrivalDate: '2023-01-25',
        },
      })),
    });
    const { result } = renderHook(() => useOverBookedData(mockSelectedItem));
    expect(result.current.deliveryDate).toBe('2023-01-20');
    expect(result.current.pickupDate).toBe('2023-01-25');
  });

  it('should use willCallOrder dates when willCallOrder exists', () => {
    (useFormContext as any).mockReturnValueOnce({
      getValues: vi.fn(() => ({
        willCallOrder: {
          id: 3,
          pickupDate: '2023-01-30',
          returnDate: '2023-02-05',
        },
      })),
    });
    const { result } = renderHook(() => useOverBookedData(mockSelectedItem));
    expect(result.current.deliveryDate).toBe('2023-01-30');
    expect(result.current.pickupDate).toBe('2023-02-05');
  });

  it('should use current date when no order type exists', () => {
    (useFormContext as any).mockReturnValueOnce({
      getValues: vi.fn(() => ({})),
    });
    const { result } = renderHook(() => useOverBookedData(mockSelectedItem));
    expect(result.current.deliveryDate).toBe('2023-01-01');
    expect(result.current.pickupDate).toBe('2023-01-01');
  });

  it('should reset form with default values', () => {
    const mockReset = vi.fn();
    (useForm as any).mockReturnValueOnce({
      reset: mockReset,
      watch: vi.fn(() => ({ value: '123' })),
    });
    renderHook(() => useOverBookedData(mockSelectedItem));
    expect(mockReset).toHaveBeenCalledWith({
      itemId: mockSelectedItem.itemId,
      displayType: 'RECENT',
      orderType: 'ORDERS_QUOTES',
      storeLocationId: '0',
      deliveryDate: '2023-01-10',
      pickupDate: '2023-01-15',
      unitPrice: 0,
    });
  });

  it('should fetch serial data when itemId has value', () => {
    renderHook(() => useOverBookedData(mockSelectedItem));
    expect(useGetSerialInfoByItemQuery).toHaveBeenCalledWith('123', {
      skip: false,
    });
  });

  it('should not fetch serial data when itemId has no value', () => {
    (useForm as any).mockReturnValueOnce({
      reset: vi.fn(),
      watch: vi.fn(() => null),
    });
    renderHook(() => useOverBookedData(mockSelectedItem));
    expect(useGetSerialInfoByItemQuery).toHaveBeenCalledWith(undefined, {
      skip: true,
    });
  });

  it('should handle empty serial data', () => {
    (useGetSerialInfoByItemQuery as any).mockReturnValueOnce({
      data: null,
      isFetching: false,
    });
    const { result } = renderHook(() => useOverBookedData(mockSelectedItem));
    expect(result.current.serialList).toEqual([]);
  });

  it('should handle loading state for serial data', () => {
    (useGetSerialInfoByItemQuery as any).mockReturnValueOnce({
      data: null,
      isFetching: true,
    });
    const { result } = renderHook(() => useOverBookedData(mockSelectedItem));
    expect(result.current.serialLoading).toBe(true);
  });
});
