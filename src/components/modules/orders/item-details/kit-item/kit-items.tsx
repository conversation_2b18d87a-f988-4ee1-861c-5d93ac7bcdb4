import AppButton from '@/components/common/app-button';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import DataTable from '@/components/common/data-tables';
import { getQueryParam } from '@/lib/utils';
import { useGetEnumsListQuery } from '@/redux/features/enums-api/enums-api';
import {
  useDeleteOrderItemMutation,
  useGetItemDetailsMutation,
  useSaveItemDetailsMutation,
} from '@/redux/features/orders/item-details.api';
import { ItemFormDataTypes, SubRentingTabEnum } from '@/types/order.types';
import { KitItemsFormDataTypes } from '@/types/orders/order-item-details.types';
import { isEqual } from 'lodash';
import { FC, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import {
  FieldArrayWithId,
  SubmitHandler,
  UseFormReturn,
  useWatch,
} from 'react-hook-form';
import KitToolbar from './KitToolbar';
import { useKitItemListColumns } from './useKitItemListColumns';
import { useKitItemListHandlers } from './useKitItemListHandler';
import { getModifiedItems } from '@/lib/getModifiedItems';

interface KitItemsProps {
  onOpenChange: () => void;
  setActiveTab: (tab: SubRentingTabEnum | string) => void;
  form: UseFormReturn<KitItemsFormDataTypes>;
  append: any;
  remove: any;
  fields: FieldArrayWithId<KitItemsFormDataTypes, 'items', 'id'>[];
  rowSelection: Record<string, boolean>;
  setRowSelection: React.Dispatch<
    React.SetStateAction<Record<string, boolean>>
  >;
  setKitItemId: React.Dispatch<React.SetStateAction<number | null>>;
  isLoading: boolean;
  orderItemId: any;
}

interface DeleteDialogState {
  isOpen: boolean;
  itemId: number | null;
  index: number | null;
}

const initialDeleteDialogState: DeleteDialogState = {
  isOpen: false,
  itemId: null,
  index: null,
};

const KitItems: FC<KitItemsProps> = ({
  onOpenChange,
  setActiveTab,
  form,
  append,
  remove,
  fields,
  rowSelection,
  setRowSelection,
  setKitItemId,
  isLoading,
  orderItemId,
}) => {
  const id = getQueryParam('id') as string;
  const [deleteDialogState, setDeleteDialogState] = useState<DeleteDialogState>(
    initialDeleteDialogState
  );

  const watchedItems = useWatch({
    control: form.control,
    name: 'items',
  });

  const { data: itemType, isLoading: itemTypeLoading } = useGetEnumsListQuery({
    name: 'ItemType',
  });

  const [saveItemDetails, { isLoading: isSaveItemDetailsLoading }] =
    useSaveItemDetailsMutation();

  const [getItemBriefData] = useGetItemDetailsMutation();
  const [deleteItem, { isLoading: isDeleteLoading }] =
    useDeleteOrderItemMutation();

  const selectedItemId = Object.keys(rowSelection).at(0);
  const selectedItem = fields?.find(
    (item) => item?.listId === Number(selectedItemId)
  );

  const cleanedFields = fields.map(({ id, ...rest }) => ({
    ...rest,
    quantity: Number(rest.quantity),
  }));
  const isItemModified = useMemo(() => {
    const cleanedWatchedItems = watchedItems.map(({ ...rest }) => ({
      ...rest,
      quantity: Number(rest.quantity),
    }));

    return !isEqual(cleanedFields, cleanedWatchedItems);
  }, [cleanedFields, watchedItems]);

  // Auto-scroll on new items
  const tableRef = useRef<HTMLTableElement>(null);
  useEffect(() => {
    if (tableRef.current) {
      tableRef.current.scrollTop = tableRef.current.scrollHeight;
    }
  }, [fields.length]);

  const toggleDelete = useCallback(
    (id: number | null, index: number | null) => {
      setDeleteDialogState((prev) => ({
        isOpen: !prev.isOpen,
        itemId: id,
        index,
      }));
    },
    []
  );

  const handleDeleteItem = async () => {
    if (deleteDialogState.itemId) {
      await deleteItem({ orderItemId: deleteDialogState?.itemId });
    } else if (deleteDialogState.index !== null) {
      remove(deleteDialogState.index);
    }
    setDeleteDialogState(initialDeleteDialogState);
  };

  // Handlers and columns
  const { handleItemIdChange } = useKitItemListHandlers({
    form,
    orderId: id,
    getItemBriefData,
  });

  const columns = useKitItemListColumns({
    form,
    itemType,
    itemTypeLoading,
    handleItemIdChange,
    setActiveTab,
    setKitItemId,
    toggleDelete,
  });

  const addNewKitItem = useCallback(() => {
    const lastItem =
      form.getValues('items')[form.getValues('items').length - 1];
    if (lastItem?.itemId?.value || fields?.length === 0) {
      append({
        listId: null,
        itemId: null,
        description: '',
        quantity: '',
        price: '0',
        subRental: 'No',
        total: 0,
        serialNumber: null,
        parentId: null,
        disabledCheckBox: true,
      });
    }
  }, [append, fields?.length, form]);

  const onSubmit: SubmitHandler<KitItemsFormDataTypes> = async (formData) => {
    const currentItems = formData?.items || [];
    const defaultItems = cleanedFields || [];

    // Extract only modified or new items.
    const modifiedOrderItems = getModifiedItems(currentItems, defaultItems);
    const payload = modifiedOrderItems
      ?.filter((item) => item?.itemId?.value)
      .map((item: ItemFormDataTypes) => {
        const {
          listId,
          itemId,
          description,
          subRental,
          price,
          type,
          quantity,
        } = item;
        return {
          id: typeof listId === 'number' ? listId : null,
          itemId: Number(itemId?.value),
          orderId: Number(id),
          quantity: Number(quantity) ?? null,
          description,
          type,
          subRental: subRental === 'Yes' ? true : false,
          price: Number(price) || 0,
          parentId: orderItemId,
        };
      });
    if (payload.length > 0) {
      await saveItemDetails({ orderId: id, body: payload });
    }
  };

  return (
    <>
      {/* Toolbar */}
      <KitToolbar
        setActiveTab={setActiveTab}
        setKitItemId={setKitItemId}
        selectedItem={selectedItem}
      />

      {/* Data table */}
      <DataTable
        data={fields}
        columns={columns}
        onScrollRef={tableRef}
        enablePagination={false}
        tableClassName="max-h-[580px] overflow-auto"
        enableRowSelection
        rowSelection={rowSelection}
        onRowSelectionChange={setRowSelection}
        isLoading={isLoading}
        bindingKey="listId"
        highlightKey="isOverbooked"
        highlightClassName="bg-red-100 hover:bg-red-100"
        selectedHighlightClassName="data-[state=selected]:bg-red-100"
      />

      {/* Actions footer */}
      <div className="w-full p-2 border-grayScale-20 border-b border-x rounded-b-md">
        <AppButton
          label="+ Add New"
          onClick={addNewKitItem}
          className="bg-brand-teal-Default hover:bg-brand-teal-secondary"
        />
      </div>

      {/* Submit/Cancel */}
      <div className="fixed bottom-0 w-full flex justify-end space-x-3 pr-12 pt-1 pb-4 text-right">
        <AppButton
          label="Submit"
          className="w-40"
          onClick={form.handleSubmit(onSubmit)}
          isLoading={isSaveItemDetailsLoading}
          disabled={!isItemModified}
        />
        <AppButton
          label="Cancel"
          variant="neutral"
          className="w-40"
          onClick={onOpenChange}
        />
      </div>

      {/* Delete confirmation */}
      <AppConfirmationModal
        description={<div>Are you sure you want to delete this record?</div>}
        open={deleteDialogState.isOpen}
        handleCancel={() => setDeleteDialogState(initialDeleteDialogState)}
        handleSubmit={handleDeleteItem}
        disabled={isDeleteLoading}
        isLoading={isDeleteLoading}
      />
    </>
  );
};

export default KitItems;
