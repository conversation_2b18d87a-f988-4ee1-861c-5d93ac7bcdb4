import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen } from '@testing-library/react';
import KitItemInfo from './index';
import { useGetKitListQuery } from '@/redux/features/orders/item-details.api';
import { useTabManagement } from './useTabManagement';
import { FormProvider } from 'react-hook-form';
import BreadCrumb from './BreadCrumb';

// Mock dependencies
vi.mock('@/redux/features/orders/item-details.api', () => ({
  useGetKitListQuery: vi.fn(),
}));

vi.mock('./useTabManagement', () => ({
  useTabManagement: vi.fn(() => ({
    activeTab: 'kit-item',
    handleSetActiveTab: vi.fn(),
  })),
}));

vi.mock('./useKitFormControl', () => ({
  useKitItemFormControl: vi.fn(() => ({
    kitForm: {
      reset: vi.fn(),
      watch: vi.fn(),
      getValues: vi.fn(),
      setValue: vi.fn(),
      handleSubmit: vi.fn(),
      formState: {},
      control: {},
    },
    additionalItemInfoForm: {
      reset: vi.fn(),
      watch: vi.fn(),
      getValues: vi.fn(),
      setValue: vi.fn(),
      handleSubmit: vi.fn(),
      formState: {},
      control: {},
    },
    availabilityForm: {
      reset: vi.fn(),
      watch: vi.fn(),
      getValues: vi.fn(),
      setValue: vi.fn(),
      handleSubmit: vi.fn(),
      formState: {},
      control: {},
    },
    fields: [],
    append: vi.fn(),
    remove: vi.fn(),
  })),
}));

vi.mock('./useOverBookedData', () => ({
  useOverBookedData: vi.fn(() => ({
    serialData: null,
    serialList: [],
    serialLoading: false,
    overBookedForm: {
      reset: vi.fn(),
      watch: vi.fn(),
      getValues: vi.fn(),
      setValue: vi.fn(),
      handleSubmit: vi.fn(),
      formState: {},
      control: {},
    },
  })),
}));

vi.mock('./useKitSubRental', () => ({
  useKitSubRental: vi.fn(() => ({
    customerData: null,
    setCustomerData: vi.fn(),
    rowSelection: {},
    setRowSelection: vi.fn(),
    handleUpdateSubRent: vi.fn(),
    isUpdating: false,
    selectedSubRents: [],
    sorting: [],
    setSorting: vi.fn(),
    handleNewSubRental: vi.fn(),
    setSelectedSubRents: vi.fn(),
    orderSubRents: [],
    isLoading: false,
    form: {
      reset: vi.fn(),
      watch: vi.fn(),
      getValues: vi.fn(),
      setValue: vi.fn(),
      handleSubmit: vi.fn(),
      formState: {},
      control: {},
    },
  })),
}));

vi.mock('@/lib/utils', async () => {
  const actual = await import('@/lib/utils');
  return {
    ...actual,
    getQueryParam: vi.fn(() => '123'),
  };
});

vi.mock('./BreadCrumb', () => ({
  default: vi.fn(() => <div data-testid="breadcrumb" />),
}));

vi.mock('react-hook-form', () => ({
  ...vi.importActual('react-hook-form'),
  FormProvider: vi.fn(({ children }) => children),
}));

describe('KitItemInfo Component', () => {
  const mockProps = {
    open: true,
    onOpenChange: vi.fn(),
    orderItemId: '456',
  };

  beforeEach(() => {
    vi.clearAllMocks();
    (useGetKitListQuery as any).mockReturnValue({
      data: { data: [] },
      isLoading: false,
    });
  });

  it('should render without crashing', () => {
    render(<KitItemInfo {...mockProps} />);
    expect(screen.getByTestId('breadcrumb')).toBeInTheDocument();
  });

  it('should initialize with default tab', () => {
    render(<KitItemInfo {...mockProps} />);
    expect(useTabManagement).toHaveBeenCalledWith('kit-item');
  });

  it('should fetch kit list data when orderItemId is provided', () => {
    render(<KitItemInfo {...mockProps} />);
    expect(useGetKitListQuery).toHaveBeenCalledWith('456', { skip: false });
  });

  it('should not fetch kit list data when orderItemId is null', () => {
    render(<KitItemInfo {...mockProps} orderItemId={null} />);
    expect(useGetKitListQuery).toHaveBeenCalledWith('null', { skip: true });
  });

  it('should pass correct props to BreadCrumb component', () => {
    render(<KitItemInfo {...mockProps} />);
    expect(BreadCrumb).toHaveBeenCalledWith(
      expect.objectContaining({
        treeRoot: expect.any(Object),
        activeTab: 'kit-item',
        isOpen: true,
        onOpenChange: mockProps.onOpenChange,
        className: 'max-w-[95%] 2xl:max-w-[70%]',
        contentClassName: 'h-[530px] 2xl:h-[700px] overflow-y-auto',
      }),
      {}
    );
  });

  it('should select the correct form provider based on activeTab', () => {
    (useTabManagement as any).mockReturnValueOnce({
      activeTab: 'sub-renting',
      handleSetActiveTab: vi.fn(),
    });
    const { rerender } = render(<KitItemInfo {...mockProps} />);
    expect(FormProvider).toHaveBeenCalledWith(expect.objectContaining({}), {});
    (useTabManagement as any).mockReturnValueOnce({
      activeTab: 'overbooked-item',
      handleSetActiveTab: vi.fn(),
    });
    rerender(<KitItemInfo {...mockProps} />);
    expect(FormProvider).toHaveBeenCalledWith(expect.objectContaining({}), {});
    (useTabManagement as any).mockReturnValueOnce({
      activeTab: 'other-tab',
      handleSetActiveTab: vi.fn(),
    });
    rerender(<KitItemInfo {...mockProps} />);
    expect(FormProvider).toHaveBeenCalledWith(expect.objectContaining({}), {});
  });

  it('should handle loading state', () => {
    (useGetKitListQuery as any).mockReturnValue({
      data: null,
      isLoading: true,
    });
    render(<KitItemInfo {...mockProps} />);
    expect(screen.getByTestId('breadcrumb')).toBeInTheDocument();
  });
});
