import BreadcrumbWithCustomSeparator from '@/components/common/BreadCrumbWithSeparator';
import CustomDialog from '@/components/common/dialog';
import React from 'react';

export type TreeListItem = {
  label: string;
  value: string;
  content: React.ReactNode;
  children?: TreeListItem[];
};

const findBreadcrumbPath = (
  node: TreeListItem,
  targetValue: string,
  path: TreeListItem[] = []
): TreeListItem[] | null => {
  if (node.value === targetValue) return [...path, node];

  if (node.children) {
    for (let child of node.children) {
      const result = findBreadcrumbPath(child, targetValue, [...path, node]);
      if (result) return result;
    }
  }
  return null;
};

interface BreadcrumbDialogRendererTreeProps {
  treeRoot: TreeListItem;
  activeTab: string;
  isOpen: boolean;
  className?: string;
  contentClassName?: string;
  onOpenChange: (isOpen?: boolean) => void;
  setActiveTab: (tabValue: string) => void;
  autoFocus?: boolean;
}

const BreadCrumb = ({
  treeRoot,
  activeTab,
  isOpen,
  onOpenChange,
  className,
  contentClassName,
  setActiveTab,
  autoFocus = true,
}: BreadcrumbDialogRendererTreeProps) => {
  // Recursively find breadcrumb path to active tab
  const breadcrumbs = React.useMemo(() => {
    return findBreadcrumbPath(treeRoot, activeTab) ?? [];
  }, [activeTab, treeRoot]);

  const activeNode = breadcrumbs[breadcrumbs.length - 1];
  const dialogTitle = activeNode?.label || '';
  const activeTabContent = activeNode?.content || null;

  return (
    <CustomDialog
      onOpenChange={onOpenChange}
      description=""
      open={isOpen}
      className={className}
      contentClassName={contentClassName}
      title={dialogTitle}
      autoFocus={autoFocus}
    >
      <div className="pl-6 pr-6 flex flex-col gap-4 h-full w-full">
        {/* Breadcrumb navigation */}
        <BreadcrumbWithCustomSeparator
          breadcrumbs={
            breadcrumbs.length > 1
              ? breadcrumbs.map(({ label, value }) => ({ label, value }))
              : []
          }
          goBack={setActiveTab}
          activeValue={activeTab}
        />

        {/* Content of the active tab */}
        <div className="grid grid-cols-1">{activeTabContent}</div>
      </div>
    </CustomDialog>
  );
};

export default BreadCrumb;
