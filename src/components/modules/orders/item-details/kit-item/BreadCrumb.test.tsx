import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import BreadCrumb, { TreeListItem } from './BreadCrumb';

describe('BreadCrumb Component', () => {
  const mockTreeRoot: TreeListItem = {
    label: 'Root',
    value: 'root',
    content: <div data-testid="root-content">Root Content</div>,
    children: [
      {
        label: 'Parent',
        value: 'parent',
        content: <div data-testid="parent-content">Parent Content</div>,
        children: [
          {
            label: 'Child',
            value: 'child',
            content: <div data-testid="child-content">Child Content</div>,
          },
        ],
      },
      {
        label: 'Sibling',
        value: 'sibling',
        content: <div data-testid="sibling-content">Sibling Content</div>,
      },
    ],
  };

  const defaultProps = {
    treeRoot: mockTreeRoot,
    activeTab: 'root',
    isOpen: true,
    onOpenChange: vi.fn(),
    setActiveTab: vi.fn(),
  };

  it('renders the dialog with root content when activeTab is root', () => {
    render(<BreadCrumb {...defaultProps} />);
    expect(screen.getByText('Root')).toBeInTheDocument();
    expect(screen.getByTestId('root-content')).toBeInTheDocument();
    expect(screen.queryByRole('navigation'));
  });

  it('renders breadcrumbs and content for nested child', () => {
    render(<BreadCrumb {...defaultProps} activeTab="child" />);
    const breadcrumbs = screen.getByRole('navigation');
    expect(breadcrumbs).toBeInTheDocument();
    expect(screen.getByText('Root')).toBeInTheDocument();
    expect(screen.getByText('Parent')).toBeInTheDocument();
  });

  it('calls setActiveTab when clicking on breadcrumb items', () => {
    render(<BreadCrumb {...defaultProps} activeTab="child" />);
    const parentBreadcrumb = screen.getByText('Parent');
    fireEvent.click(parentBreadcrumb);
    expect(defaultProps.setActiveTab).toHaveBeenCalledWith('parent');
  });

  it('does not render breadcrumbs when only one level deep', () => {
    render(<BreadCrumb {...defaultProps} activeTab="sibling" />);
    expect(screen.queryByRole('navigation'));
    expect(screen.getByTestId('sibling-content')).toBeInTheDocument();
  });

  it('calls onOpenChange when dialog is closed', () => {
    render(<BreadCrumb {...defaultProps} />);
    const closeButton = screen.getByText('Close');
    fireEvent.click(closeButton);
    expect(defaultProps.onOpenChange).toHaveBeenCalledWith(false);
  });

  it('handles missing active tab gracefully', () => {
    render(<BreadCrumb {...defaultProps} activeTab="nonexistent" />);
    expect(screen.queryByTestId('root-content')).not.toBeInTheDocument();
    expect(screen.queryByRole('navigation'));
  });

  it('applies custom className and contentClassName', () => {
    render(
      <BreadCrumb
        {...defaultProps}
        className="custom-dialog"
        contentClassName="custom-content"
      />
    );
    const dialog = screen.getByRole('dialog');
    expect(dialog).toHaveClass('custom-dialog');
    const contentContainer = screen.getByTestId('root-content').parentElement;
    expect(contentContainer).toHaveClass('grid grid-cols-1');
  });

  it('does not auto-focus when autoFocus is false', () => {
    render(<BreadCrumb {...defaultProps} autoFocus={false} />);
    const dialog = screen.getByRole('dialog');
    expect(document.activeElement).not.toBe(dialog);
  });
});
