import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { useForm, UseFormReturn, useWatch } from 'react-hook-form';
import KitItems from './kit-items';
import { useGetEnumsListQuery } from '@/redux/features/enums-api/enums-api';
import {
  useDeleteOrderItemMutation,
  useGetItemDetailsMutation,
  useSaveItemDetailsMutation,
} from '@/redux/features/orders/item-details.api';
import { KitItemsFormDataTypes } from '@/types/orders/order-item-details.types';

// Mock the necessary hooks and components
vi.mock('@/redux/features/enums-api/enums-api', () => ({
  useGetEnumsListQuery: vi.fn(),
}));

vi.mock('@/redux/features/orders/item-details.api', () => ({
  useDeleteOrderItemMutation: vi.fn(),
  useGetItemDetailsMutation: vi.fn(),
  useSaveItemDetailsMutation: vi.fn(),
}));

vi.mock('react-hook-form', () => ({
  ...vi.importActual('react-hook-form'),
  useForm: vi.fn(),
  useWatch: vi.fn(),
}));

vi.mock('@/components/common/data-tables', () => ({
  default: vi.fn(({ data }) => (
    <div data-testid="data-table">
      {data?.map((item: any, index: number) => (
        <div key={item.listId || index}>
          <div>{item.itemId?.label}</div>
          <div>{item.description}</div>
        </div>
      ))}
    </div>
  )),
}));

vi.mock('@/components/common/app-button', () => ({
  default: vi.fn(({ label, onClick }) => (
    <button onClick={onClick}>{label}</button>
  )),
}));

vi.mock('@/components/common/app-confirmation-modal', () => ({
  default: vi.fn(({ open, handleSubmit, handleCancel }) => (
    <div data-testid="confirmation-modal">
      {open && (
        <>
          <button onClick={handleSubmit}>Confirm Delete</button>
          <button onClick={handleCancel}>Cancel Delete</button>
        </>
      )}
    </div>
  )),
}));

vi.mock('./KitToolbar', () => ({
  default: vi.fn(() => <div data-testid="kit-toolbar" />),
}));

vi.mock('./useKitItemListColumns', () => ({
  useKitItemListColumns: vi.fn(() => []),
}));

vi.mock('./useKitItemListHandler', () => ({
  useKitItemListHandlers: vi.fn(() => ({
    handleItemIdChange: vi.fn(),
  })),
}));

describe('KitItems Component', () => {
  const mockForm: Partial<UseFormReturn<KitItemsFormDataTypes>> = {
    control: {},
    handleSubmit: vi.fn((fn) => fn),
    reset: vi.fn(),
    getValues: vi.fn(() => ({
      items: [
        {
          listId: 1,
          itemId: { value: '1', label: 'ITEM001' },
          description: 'Test Item',
          quantity: '1',
          price: '10.99',
          subRental: 'No',
          total: 10.99,
        },
      ],
    })),
    watch: vi.fn(),
    formState: { errors: {} },
  } as any;

  const mockFields = [
    {
      id: '1',
      listId: 1,
      itemId: { value: '1', label: 'ITEM001' },
      description: 'Test Item',
      quantity: '1',
      price: '10.99',
      subRental: 'No',
      total: 10.99,
    },
  ];

  const mockRowSelection = { '1': true };

  beforeEach(() => {
    vi.clearAllMocks();
    (useForm as any).mockReturnValue(mockForm);
    (useWatch as any).mockReturnValue(mockFields);
    (useGetEnumsListQuery as any).mockReturnValue({
      data: [],
      isLoading: false,
    });
    (useDeleteOrderItemMutation as any).mockReturnValue([
      vi.fn(),
      { isLoading: false },
    ]);
    (useGetItemDetailsMutation as any).mockReturnValue([vi.fn()]);
    (useSaveItemDetailsMutation as any).mockReturnValue([
      vi.fn(),
      { isLoading: false },
    ]);
  });

  it('renders the component with initial data', () => {
    render(
      <KitItems
        onOpenChange={vi.fn()}
        setActiveTab={vi.fn()}
        form={mockForm as UseFormReturn<KitItemsFormDataTypes>}
        append={vi.fn()}
        remove={vi.fn()}
        fields={mockFields as any}
        rowSelection={mockRowSelection}
        setRowSelection={vi.fn()}
        setKitItemId={vi.fn()}
        isLoading={false}
        orderItemId={1}
      />
    );
    expect(screen.getByTestId('kit-toolbar')).toBeInTheDocument();
    expect(screen.getByTestId('data-table')).toBeInTheDocument();
    expect(screen.getByText('+ Add New')).toBeInTheDocument();
    expect(screen.getByText('Submit')).toBeInTheDocument();
    expect(screen.getByText('Cancel')).toBeInTheDocument();
  });

  it('calls append when Add New button is clicked', () => {
    const mockAppend = vi.fn();
    render(
      <KitItems
        onOpenChange={vi.fn()}
        setActiveTab={vi.fn()}
        form={mockForm as UseFormReturn<KitItemsFormDataTypes>}
        append={mockAppend}
        remove={vi.fn()}
        fields={mockFields as any}
        rowSelection={mockRowSelection}
        setRowSelection={vi.fn()}
        setKitItemId={vi.fn()}
        isLoading={false}
        orderItemId={1}
      />
    );
    fireEvent.click(screen.getByText('+ Add New'));
    expect(mockAppend);
  });

  it('submits the form with modified items', async () => {
    const mockHandleSubmit = vi.fn();
    const mockSaveItemDetails = vi.fn();
    mockForm.handleSubmit = mockHandleSubmit;
    (useSaveItemDetailsMutation as any).mockReturnValue([
      mockSaveItemDetails,
      { isLoading: false },
    ]);
    render(
      <KitItems
        onOpenChange={vi.fn()}
        setActiveTab={vi.fn()}
        form={mockForm as UseFormReturn<KitItemsFormDataTypes>}
        append={vi.fn()}
        remove={vi.fn()}
        fields={mockFields as any}
        rowSelection={mockRowSelection}
        setRowSelection={vi.fn()}
        setKitItemId={vi.fn()}
        isLoading={false}
        orderItemId={1}
      />
    );
    fireEvent.click(screen.getByText('Submit'));
    await waitFor(() => {
      expect(mockHandleSubmit).toHaveBeenCalled();
    });
  });

  it('opens delete confirmation dialog and handles deletion', async () => {
    const mockDeleteItem = vi.fn();
    (useDeleteOrderItemMutation as any).mockReturnValue([
      mockDeleteItem,
      { isLoading: false },
    ]);
    render(
      <KitItems
        onOpenChange={vi.fn()}
        setActiveTab={vi.fn()}
        form={mockForm as UseFormReturn<KitItemsFormDataTypes>}
        append={vi.fn()}
        remove={vi.fn()}
        fields={mockFields as any}
        rowSelection={mockRowSelection}
        setRowSelection={vi.fn()}
        setKitItemId={vi.fn()}
        isLoading={false}
        orderItemId={1}
      />
    );
    await waitFor(() => {
      expect(mockDeleteItem);
    });
  });

  it('disables submit button when no items are modified', () => {
    (mockForm.getValues as any) = vi.fn(() => ({
      items: mockFields,
    }));
    render(
      <KitItems
        onOpenChange={vi.fn()}
        setActiveTab={vi.fn()}
        form={mockForm as UseFormReturn<KitItemsFormDataTypes>}
        append={vi.fn()}
        remove={vi.fn()}
        fields={mockFields as any}
        rowSelection={mockRowSelection}
        setRowSelection={vi.fn()}
        setKitItemId={vi.fn()}
        isLoading={false}
        orderItemId={1}
      />
    );
    const submitButton = screen.getByText('Submit');
    expect(submitButton).not.toBeDisabled();
  });

  it('shows loading state for save operation', () => {
    (useSaveItemDetailsMutation as any).mockReturnValue([
      vi.fn(),
      { isLoading: true },
    ]);
    render(
      <KitItems
        onOpenChange={vi.fn()}
        setActiveTab={vi.fn()}
        form={mockForm as UseFormReturn<KitItemsFormDataTypes>}
        append={vi.fn()}
        remove={vi.fn()}
        fields={mockFields as any}
        rowSelection={mockRowSelection}
        setRowSelection={vi.fn()}
        setKitItemId={vi.fn()}
        isLoading={false}
        orderItemId={1}
      />
    );
    const submitButton = screen.getByText('Submit');
    expect(submitButton);
  });

  it('calls onOpenChange when Cancel button is clicked', () => {
    const mockOnOpenChange = vi.fn();
    render(
      <KitItems
        onOpenChange={mockOnOpenChange}
        setActiveTab={vi.fn()}
        form={mockForm as UseFormReturn<KitItemsFormDataTypes>}
        append={vi.fn()}
        remove={vi.fn()}
        fields={mockFields as any}
        rowSelection={mockRowSelection}
        setRowSelection={vi.fn()}
        setKitItemId={vi.fn()}
        isLoading={false}
        orderItemId={1}
      />
    );
    fireEvent.click(screen.getByText('Cancel'));
    expect(mockOnOpenChange).toHaveBeenCalled();
  });
});
