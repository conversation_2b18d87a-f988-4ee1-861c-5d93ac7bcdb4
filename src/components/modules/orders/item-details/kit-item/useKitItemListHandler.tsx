import { KitItemsFormDataTypes } from '@/types/orders/order-item-details.types';
import { useCallback } from 'react';
import { UseFormReturn } from 'react-hook-form';

interface UseItemListHandlersProps {
  form: UseFormReturn<KitItemsFormDataTypes>;
  orderId: string;
  getItemBriefData: (params: {
    orderId: string;
    itemId: string;
  }) => Promise<any>;
}

export const useKitItemListHandlers = ({
  form,
  orderId,
  getItemBriefData,
}: UseItemListHandlersProps) => {
  const handleItemIdChange = useCallback(
    async (value: { value: string }, rowIndex: number) => {
      const itemData = await getItemBriefData({ orderId, itemId: value.value });
      if (itemData?.data) {
        form.setValue(`items.${rowIndex}.itemId`, {
          label: itemData?.data?.data?.itemId,
          value: itemData?.data?.data?.id?.toString(),
        });
        form.setValue(`items.${rowIndex}.type`, itemData?.data?.data?.itemType);
        form.setValue(`items.${rowIndex}.quantity`, 1);
        form.setValue(
          `items.${rowIndex}.price`,
          itemData?.data?.data?.unitPrice?.toString()
        );
        form.setValue(
          `items.${rowIndex}.description`,
          itemData?.data?.data?.description ?? ''
        );
      }
    },
    [getItemBriefData, orderId, form]
  );

  return {
    handleItemIdChange,
  };
};
