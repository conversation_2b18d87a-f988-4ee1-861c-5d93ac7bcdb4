import AppButton from '@/components/common/app-button';
import { SubRentingTabEnum } from '@/types/order.types';
import {
  BadgeInfo,
  Calendar,
  KeyRound,
  Pen,
  PenTool,
  Search,
} from 'lucide-react';

const KitToolbar = ({
  setActiveTab,
  selectedItem,
}: {
  setActiveTab: (tab: SubRentingTabEnum | string) => void;
  setKitItemId: React.Dispatch<React.SetStateAction<number | null>>;
  selectedItem: any;
}) => {
  // Toolbar configuration
  const toolbarActions = [
    {
      title: 'Item Search',
      icon: Search,
      onClick: () => setActiveTab('kit-item-lookup'),
    },
    {
      title: 'Sub-Rent Item',
      icon: KeyRound,
      onClick: () => setActiveTab(SubRentingTabEnum.SUB_RENTING),
      disabled: !selectedItem || !(selectedItem?.type === 'RENTAL_ITEM'),
    },
    {
      title: 'Change Serialized Item',
      icon: Pen,
      onClick: () => setActiveTab('change-item'),
      disabled: !selectedItem,
    },
    {
      title: 'Overbooked Item Info',
      icon: PenTool,
      onClick: () => setActiveTab('overbooked-item'),
    },
    {
      title: 'Availability Info',
      icon: Calendar,
      onClick: () => setActiveTab('availability-calendar'),
    },
    {
      title: 'Additional Item Info',
      icon: BadgeInfo,
      onClick: () => setActiveTab('additional-item-info'),
      disabled: !selectedItem,
    },
  ];
  return (
    <div className="flex justify-end gap-3 mb-5 mt-[-10px]">
      {toolbarActions.map(
        ({ title, icon: Icon, onClick, disabled = false }) => (
          <AppButton
            key={title}
            label=""
            icon={Icon}
            onClick={onClick}
            className="rounded-full  p-2 bg-brand-teal-Default text-white hover:bg-brand-teal-secondary"
            tooltip={title}
            disabled={disabled}
          />
        )
      )}
    </div>
  );
};

export default KitToolbar;
