import { getQueryParam } from '@/lib/utils';
import {
  useAddSubRentMutation,
  useSubRentItemQuery,
} from '@/redux/features/orders/item-details.api';
import { SortingStateType } from '@/types/common.types';
import { SubRentingTabEnum } from '@/types/order.types';
import {
  CustomerVendorLookupTypes,
  OrderSubRentsTypes,
  SubRentItemListTypes,
  SubRentPayload,
} from '@/types/orders/order-item-details.types';
import { RowSelectionState } from '@tanstack/react-table';
import { useCallback, useEffect, useState } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';

type UseKitSubRentalParams = {
  setActiveTab: (tab: string) => void;
  orderItemId: number;
};

const mapItemsToFormValues = (items: SubRentItemListTypes) => ({
  itemName: items?.itemName ?? '',
  quantity: items?.quantity ?? '',
  currentSubRentedQty: items?.currentSubRentedQty ?? '',
  itemDescription: items?.itemDescription ?? '',
  currentSubRentedCost: items?.currentSubRentedCost ?? '',
  itemId: items?.itemId ?? '',
  unitPrice: items?.unitPrice || '',
});

export const useKitSubRental = ({
  setActiveTab,
  orderItemId,
}: UseKitSubRentalParams) => {
  const orderId = getQueryParam('id') as string;
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const [selectedSubRents, setSelectedSubRents] = useState<
    OrderSubRentsTypes[]
  >([]);
  const [customerData, setCustomerData] =
    useState<CustomerVendorLookupTypes | null>(null);
  const [sorting, setSorting] = useState<SortingStateType[]>([
    { id: 'id', desc: true },
  ]);

  const form = useForm<SubRentItemListTypes>();
  const [updateSubRent, { isLoading: isUpdating }] = useAddSubRentMutation();

  const { data: subRentItemData, isLoading: isLoadingSubRentItems } =
    useSubRentItemQuery(
      {
        itemId: orderItemId.toString(),
        orderId,
        body: { sortBy: sorting[0].id, sortAscending: sorting[0].desc },
      },
      { skip: !orderItemId }
    );

  useEffect(() => {
    if (subRentItemData?.data) {
      form.reset(mapItemsToFormValues(subRentItemData.data));
    }
  }, [subRentItemData?.data, form]);

  const handleNewSubRental = useCallback(async () => {
    setActiveTab(SubRentingTabEnum.NEW_SUB_RENT);
  }, [setActiveTab]);

  const createSubRentPayload = useCallback(
    (formData: SubRentItemListTypes): SubRentPayload => {
      const selectedSubRent = selectedSubRents[0];

      return {
        id: selectedSubRent?.id ?? 0,
        orderId: Number(orderId),
        pickupDate: selectedSubRent?.pickupDate,
        returnDate: selectedSubRent?.returnDate,
        rentedFromId: selectedSubRent?.customerId,
        contactType: selectedSubRent?.contactTypeValue,
        resvNo: selectedSubRent?.resvNo,
        custContact: selectedSubRent?.custContact,
        specInst1: selectedSubRent?.specInst1,
        specInst2: selectedSubRent?.specInst2,
        OrderSubrentItems: [
          {
            orderSubrentId: selectedSubRent?.id ?? 0,
            orderItemId: Number(orderItemId),
            itemId: formData.itemId,
            itemDescription: formData.itemDescription,
            quantity: Number(formData.quantity) ?? 0,
            unitPrice: formData.unitPrice ? Number(formData.unitPrice) : 0,
            rowNo: 0,
          },
        ],
      };
    },
    [orderId, orderItemId, selectedSubRents]
  );

  const handleUpdateSubRent: SubmitHandler<SubRentItemListTypes> = useCallback(
    async (formData) => {
      try {
        const payload = createSubRentPayload(formData);
        const response = await updateSubRent({ body: payload, orderItemId });

        if (response.data?.success) {
          setActiveTab('kit-item');
          setCustomerData(null);
        }
      } catch (error) {}
    },
    [createSubRentPayload, orderItemId, setActiveTab, updateSubRent]
  );

  useEffect(() => () => setCustomerData(null), []);

  return {
    // State
    rowSelection,
    selectedSubRents,
    customerData,
    sorting,
    isLoading: isLoadingSubRentItems,
    isUpdating,
    form,
    orderSubRents: subRentItemData?.data?.orderSubrents,

    // State Handlers
    setSorting,
    setCustomerData,
    setRowSelection,
    setSelectedSubRents,

    // Actions
    handleUpdateSubRent,
    handleNewSubRental,
  };
};
