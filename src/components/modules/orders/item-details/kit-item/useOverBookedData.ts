import { convertToFloat, DATE_FORMAT_YYYYMMDD, formatDate } from '@/lib/utils';
import { useGetSerialInfoByItemQuery } from '@/redux/features/orders/order.api';
import { OverBookedItemInfoTypes } from '@/types/order.types';
import { KitItemTypes } from '@/types/orders/order-item-details.types';
import { useEffect, useMemo } from 'react';
import { useForm, useFormContext } from 'react-hook-form';

export const useOverBookedData = (selectedItem?: KitItemTypes) => {
  const overBookedForm = useForm<OverBookedItemInfoTypes>();

  const orderForm = useFormContext();
  const { deliveryOrder, shipOrder, willCallOrder } =
    orderForm?.getValues() || {};

  // Get delivery and pickup dates from order data
  const { deliveryDate, pickupDate } = useMemo(() => {
    const currentDate = formatDate(new Date(), DATE_FORMAT_YYYYMMDD);
    if (deliveryOrder?.id) {
      return {
        deliveryDate: deliveryOrder?.deliveryDate || '',
        pickupDate: deliveryOrder?.pickupDate || '',
      };
    }
    if (shipOrder?.id) {
      return {
        deliveryDate: shipOrder?.shipDate || '',
        pickupDate: shipOrder?.returnArrivalDate || '',
      };
    }
    if (willCallOrder?.id) {
      return {
        deliveryDate: willCallOrder?.pickupDate || '',
        pickupDate: willCallOrder?.returnDate || '',
      };
    }
    return { deliveryDate: currentDate, pickupDate: currentDate };
  }, [deliveryOrder, shipOrder, willCallOrder]);

  // Set default values for overbooked form
  const defaultValues = useMemo(
    () => ({
      itemId: selectedItem?.itemId as any,
      displayType: 'RECENT',
      orderType: 'ORDERS_QUOTES',
      storeLocationId: '0',
      deliveryDate,
      pickupDate,
      unitPrice: convertToFloat({ value: 0, prefix: '$' }),
    }),
    [deliveryDate, pickupDate, selectedItem?.itemId]
  );

  useEffect(() => {
    if (defaultValues) {
      overBookedForm.reset(defaultValues);
    }
  }, [defaultValues, overBookedForm]);

  // Get serial data
  const itemIdValue = overBookedForm.watch('itemId')?.value;
  const { data: serialData, isFetching: serialLoading } =
    useGetSerialInfoByItemQuery(itemIdValue, { skip: !itemIdValue });

  // Process serial data
  const { serialList } = useMemo(() => {
    const serialList =
      serialData?.data?.data?.map((item: any) => ({
        label: item?.serialNo,
        value: item?.serialNo,
      })) || [];

    return { serialList };
  }, [serialData?.data]);

  return {
    serialData: serialData?.data,
    serialList,
    serialLoading,
    deliveryDate,
    pickupDate,
    overBookedForm,
  };
};
