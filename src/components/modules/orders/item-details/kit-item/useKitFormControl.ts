import { DATE_FORMAT_YYYYMMDD, formatDate } from '@/lib/utils';
import { AvailabiltyCalendarInfoTyps } from '@/types/availabilty-calendar-info.types';
import {
  AdditionalEmailInfoTypes,
  KitItemsFormDataTypes,
  KitItemTypes,
  SubRentItemListTypes,
} from '@/types/orders/order-item-details.types';
import { useEffect, useMemo } from 'react';
import { useFieldArray, useForm } from 'react-hook-form';

export const useKitItemFormControl = (selectedItem?: KitItemTypes) => {
  const kitForm = useForm<KitItemsFormDataTypes>();
  const subRentForm = useForm<SubRentItemListTypes>();
  const availabilityForm = useForm<AvailabiltyCalendarInfoTyps>();
  const additionalItemInfoForm = useForm<AdditionalEmailInfoTypes>({
    mode: 'onChange',
  });

  const { fields, append, remove } = useFieldArray({
    control: kitForm.control,
    name: 'items',
  });

  // Set up initial values for availability form
  const availabilityDefaultValues = useMemo(() => {
    const currentDate = formatDate(new Date(), DATE_FORMAT_YYYYMMDD);
    return {
      itemId: selectedItem?.itemId as any,
      storeLocationId: '0',
      month: currentDate,
      date: currentDate,
      includeQuotes: 'false',
      includeSubRentAndPO: false,
      includePastOrders: false,
    };
  }, [selectedItem?.itemId]);

  useEffect(() => {
    if (availabilityDefaultValues) {
      availabilityForm.reset(availabilityDefaultValues);
    }
  }, [availabilityDefaultValues, availabilityForm]);

  return {
    kitForm,
    additionalItemInfoForm,
    availabilityForm,
    subRentForm,
    fields,
    append,
    remove,
  };
};
