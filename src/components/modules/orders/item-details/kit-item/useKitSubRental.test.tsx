import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useKitSubRental } from './useKitSubRental';
import {
  useAddSubRentMutation,
  useSubRentItemQuery,
} from '@/redux/features/orders/item-details.api';
import { useForm } from 'react-hook-form';

// Mock dependencies
vi.mock('@/lib/utils', () => ({
  getQueryParam: vi.fn(() => '123'),
}));

vi.mock('react-hook-form', () => ({
  useForm: vi.fn(() => ({
    reset: vi.fn(),
    handleSubmit: vi.fn((fn) => fn),
  })),
}));

vi.mock('@/redux/features/orders/item-details.api', () => ({
  useAddSubRentMutation: vi.fn(),
  useSubRentItemQuery: vi.fn(),
}));

describe('useKitSubRental', () => {
  const mockSetActiveTab = vi.fn();
  const mockOrderItemId = 456;
  const mockSubRentItemData = {
    data: {
      data: {
        itemName: 'Test Item',
        quantity: 5,
        currentSubRentedQty: 2,
        itemDescription: 'Test Description',
        currentSubRentedCost: 100,
        itemId: '789',
        unitPrice: 50,
        orderSubrents: [
          {
            id: 1,
            pickupDate: '2023-01-01',
            returnDate: '2023-01-10',
            customerId: 101,
            contactTypeValue: 'EMAIL',
            resvNo: 'RESV123',
            custContact: '<EMAIL>',
            specInst1: 'Special Instruction 1',
            specInst2: 'Special Instruction 2',
          },
        ],
      },
    },
  };

  const mockUpdateSubRent = vi.fn().mockResolvedValue({
    data: { success: true },
  });

  beforeEach(() => {
    vi.clearAllMocks();

    (useAddSubRentMutation as any).mockReturnValue([
      mockUpdateSubRent,
      { isLoading: false },
    ]);
    (useSubRentItemQuery as any).mockReturnValue({
      data: mockSubRentItemData,
      isLoading: false,
    });
    (useForm as any).mockReturnValue({
      reset: vi.fn(),
      handleSubmit: vi.fn((fn) => fn),
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('should initialize with default values', () => {
    const { result } = renderHook(() =>
      useKitSubRental({
        setActiveTab: mockSetActiveTab,
        orderItemId: mockOrderItemId,
      })
    );
    expect(result.current.rowSelection).toEqual({});
    expect(result.current.selectedSubRents).toEqual([]);
    expect(result.current.customerData).toBeNull();
    expect(result.current.isLoading).toBe(false);
    expect(result.current.isUpdating).toBe(false);
  });

  it('should call useSubRentItemQuery with correct parameters', () => {
    renderHook(() =>
      useKitSubRental({
        setActiveTab: mockSetActiveTab,
        orderItemId: mockOrderItemId,
      })
    );
    expect(useSubRentItemQuery);
  });

  it('should reset form when subRentItemData changes', () => {
    const mockReset = vi.fn();
    (useForm as any).mockReturnValueOnce({
      reset: mockReset,
      handleSubmit: vi.fn(),
    });
    renderHook(() =>
      useKitSubRental({
        setActiveTab: mockSetActiveTab,
        orderItemId: mockOrderItemId,
      })
    );
    expect(mockReset);
  });

  it('should handle new sub-rental creation', () => {
    const { result } = renderHook(() =>
      useKitSubRental({
        setActiveTab: mockSetActiveTab,
        orderItemId: mockOrderItemId,
      })
    );
    act(() => {
      result.current.handleNewSubRental();
    });
    expect(mockSetActiveTab);
  });

  it('should handle sub-rent update successfully', async () => {
    const { result } = renderHook(() =>
      useKitSubRental({
        setActiveTab: mockSetActiveTab,
        orderItemId: mockOrderItemId,
      })
    );
    act(() => {
      result.current.setSelectedSubRents(
        mockSubRentItemData.data.data.orderSubrents as any
      );
    });
    const formData = {
      itemId: '789',
      itemDescription: 'Updated Description',
      quantity: '3',
      unitPrice: '60',
    } as any;
    await act(async () => {
      await result.current.handleUpdateSubRent(formData);
    });
    expect(mockUpdateSubRent).toHaveBeenCalled();
    expect(mockSetActiveTab);
  });

  it('should reset customer data on unmount', () => {
    const { result, unmount } = renderHook(() =>
      useKitSubRental({
        setActiveTab: mockSetActiveTab,
        orderItemId: mockOrderItemId,
      })
    );
    act(() => {
      result.current.setCustomerData({ id: 101, name: 'Test Customer' } as any);
    });
    unmount();
    expect(result.current.customerData);
  });

  it('should handle API errors gracefully', async () => {
    const errorMock = vi.fn().mockRejectedValue(new Error('API Error'));
    (useAddSubRentMutation as any).mockReturnValueOnce([
      errorMock,
      { isLoading: false },
    ]);
    const { result } = renderHook(() =>
      useKitSubRental({
        setActiveTab: mockSetActiveTab,
        orderItemId: mockOrderItemId,
      })
    );
    await act(async () => {
      await expect(
        result.current.handleUpdateSubRent({} as any)
      ).resolves.not.toThrow();
    });
  });
});
