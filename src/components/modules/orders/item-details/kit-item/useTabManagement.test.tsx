import { describe, it, expect } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useTabManagement } from './useTabManagement';

describe('useTabManagement', () => {
  it('should initialize with the default tab', () => {
    const { result } = renderHook(() => useTabManagement());
    expect(result.current.activeTab).toBe('kit-item');
  });

  it('should initialize with custom initial tab when provided', () => {
    const { result } = renderHook(() => useTabManagement('custom-tab'));
    expect(result.current.activeTab).toBe('custom-tab');
  });

  it('should update the active tab', () => {
    const { result } = renderHook(() => useTabManagement());
    act(() => {
      result.current.handleSetActiveTab('new-tab');
    });
    expect(result.current.activeTab).toBe('new-tab');
  });

  it('should fall back to initial tab when undefined is passed', () => {
    const { result } = renderHook(() => useTabManagement('fallback-tab'));
    act(() => {
      result.current.handleSetActiveTab(undefined as any);
    });
    expect(result.current.activeTab).toBe('fallback-tab');
  });

  it('should memoize the handleSetActiveTab function', () => {
    const { result, rerender } = renderHook(() => useTabManagement());
    const firstHandler = result.current.handleSetActiveTab;
    rerender();
    expect(result.current.handleSetActiveTab).toBe(firstHandler);
  });

  it('should create new handler when initialTab changes', () => {
    const { result, rerender } = renderHook(
      ({ initialTab }) => useTabManagement(initialTab),
      { initialProps: { initialTab: 'tab-1' } }
    );
    const firstHandler = result.current.handleSetActiveTab;
    rerender({ initialTab: 'tab-2' });
    expect(result.current.handleSetActiveTab).not.toBe(firstHandler);
  });
});
