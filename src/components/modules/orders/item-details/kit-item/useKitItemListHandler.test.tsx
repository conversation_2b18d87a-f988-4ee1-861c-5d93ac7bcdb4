import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook } from '@testing-library/react';
import { useKitItemListHandlers } from './useKitItemListHandler';
import { UseFormReturn } from 'react-hook-form';
import { KitItemsFormDataTypes } from '@/types/orders/order-item-details.types';

describe('useKitItemListHandlers', () => {
  const mockItemData = {
    data: {
      data: {
        id: '123',
        itemId: 'ITEM-001',
        itemType: 'RENTAL',
        unitPrice: 99.99,
        description: 'Test Item Description',
      },
    },
  };
  const mockForm = {
    setValue: vi.fn(),
  } as unknown as UseFormReturn<KitItemsFormDataTypes>;
  const mockGetItemBriefData = vi.fn().mockResolvedValue(mockItemData);
  const props = {
    form: mockForm,
    orderId: 'ORDER-123',
    getItemBriefData: mockGetItemBriefData,
  };
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should return handleItemIdChange function', () => {
    const { result } = renderHook(() => useKitItemListHandlers(props));
    expect(result.current).toHaveProperty('handleItemIdChange');
    expect(typeof result.current.handleItemIdChange).toBe('function');
  });

  it('should call getItemBriefData with correct parameters', async () => {
    const { result } = renderHook(() => useKitItemListHandlers(props));
    const testValue = { value: '123' };
    const testRowIndex = 0;
    await result.current.handleItemIdChange(testValue, testRowIndex);
    expect(mockGetItemBriefData).toHaveBeenCalledTimes(1);
    expect(mockGetItemBriefData).toHaveBeenCalledWith({
      orderId: props.orderId,
      itemId: testValue.value,
    });
  });

  it('should set form values correctly when data is returned', async () => {
    const { result } = renderHook(() => useKitItemListHandlers(props));
    const testRowIndex = 1;
    await result.current.handleItemIdChange({ value: '123' }, testRowIndex);
    expect(mockForm.setValue).toHaveBeenCalledTimes(5);
    expect(mockForm.setValue).toHaveBeenCalledWith(
      `items.${testRowIndex}.itemId`,
      {
        label: mockItemData.data.data.itemId,
        value: mockItemData.data.data.id.toString(),
      }
    );
    expect(mockForm.setValue).toHaveBeenCalledWith(
      `items.${testRowIndex}.type`,
      mockItemData.data.data.itemType
    );
    expect(mockForm.setValue).toHaveBeenCalledWith(
      `items.${testRowIndex}.quantity`,
      1
    );
    expect(mockForm.setValue).toHaveBeenCalledWith(
      `items.${testRowIndex}.price`,
      mockItemData.data.data.unitPrice.toString()
    );
    expect(mockForm.setValue).toHaveBeenCalledWith(
      `items.${testRowIndex}.description`,
      mockItemData.data.data.description ?? ''
    );
  });

  it('should not set form values when no data is returned', async () => {
    const emptyDataMock = vi.fn().mockResolvedValue({ data: null });
    const { result } = renderHook(() =>
      useKitItemListHandlers({ ...props, getItemBriefData: emptyDataMock })
    );
    await result.current.handleItemIdChange({ value: '123' }, 0);
    expect(emptyDataMock).toHaveBeenCalled();
    expect(mockForm.setValue).not.toHaveBeenCalled();
  });

  it('should handle errors from getItemBriefData gracefully', async () => {
    const errorMock = vi.fn().mockRejectedValue(new Error('API Error'));
    renderHook(() =>
      useKitItemListHandlers({ ...props, getItemBriefData: errorMock })
    );
    expect(errorMock);
    expect(mockForm.setValue).not.toHaveBeenCalled();
  });

  it('should memoize the handleItemIdChange function', () => {
    const { result, rerender } = renderHook(
      (props) => useKitItemListHandlers(props),
      { initialProps: props }
    );
    const firstHandle = result.current.handleItemIdChange;
    rerender(props);
    expect(result.current.handleItemIdChange).toBe(firstHandle);
  });

  it('should update handler when dependencies change', () => {
    const { result, rerender } = renderHook(
      (props) => useKitItemListHandlers(props),
      { initialProps: props }
    );
    const firstHandle = result.current.handleItemIdChange;
    const newProps = { ...props, orderId: 'NEW-ORDER' };
    rerender(newProps);
    expect(result.current.handleItemIdChange).not.toBe(firstHandle);
  });
});
