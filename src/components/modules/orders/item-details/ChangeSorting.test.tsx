import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import ChangeSorting from './ChangeSorting';

// Mock the hooks and components
vi.mock('@/lib/utils', () => ({
  cn: () => 'mocked-class',
  getQueryParam: vi.fn(() => '123'),
}));

vi.mock('@/components/common/dialog', () => ({
  default: ({ children, open }: any) => (open ? <div>{children}</div> : null),
}));

vi.mock('@/components/common/FormActionButtons', () => ({
  default: ({ onSubmit, onCancel }: any) => (
    <div>
      <button onClick={onSubmit}>Ok</button>
      <button onClick={onCancel}>Cancel</button>
    </div>
  ),
}));

vi.mock('@/components/forms/select-dropdown', () => ({
  default: ({ form, name, optionsList, onChange, disabled }: any) => (
    <select
      data-testid={name}
      disabled={disabled}
      onChange={(e) => {
        form.setValue(name, e.target.value);
        onChange?.(e.target.value);
      }}
      value={form.watch(name)}
    >
      {optionsList?.map((option: any) => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  ),
}));

// Mock the Redux hooks
const mockEnumsData = {
  ItemPrintSequence: [
    { value: 'PRINT_AS_ENTERED', label: 'Print As Entered' },
    { value: 'GROUP_BY_ITEM', label: 'Group By Item' },
  ],
  KitComponentPrintSeq: [
    { value: 'ITEM', label: 'Item' },
    { value: 'KIT', label: 'Kit' },
  ],
} as any;

vi.mock('@/redux/features/enums-api/enums-api', () => ({
  useGetEnumsListQuery: vi.fn(({ name }) => ({
    data: { data: mockEnumsData[name] },
    isLoading: false,
  })),
}));

const mockChangeSorting = vi.fn();
vi.mock('@/redux/features/orders/item-details.api', () => ({
  useChangeSortingMutation: vi.fn(() => [
    mockChangeSorting,
    { isLoading: false },
  ]),
}));

// Mock react-hook-form's useFormContext
const mockUseFormContext = vi.fn();
vi.mock('react-hook-form', async () => {
  const actual = await vi.importActual('react-hook-form');
  return {
    ...actual,
    useFormContext: () => mockUseFormContext(),
  };
});

describe('ChangeSorting', () => {
  const mockOnOpenChange = vi.fn();
  const mockOrderForm = {
    watch: vi.fn((field) => {
      if (field === 'itemPrintSequence') return 'GROUP_BY_ITEM';
      if (field === 'kitComponentPrintSeq') return 'KIT';
      return null;
    }),
    setValue: vi.fn(),
  };

  beforeEach(() => {
    mockUseFormContext.mockReturnValue(mockOrderForm);
    mockChangeSorting.mockReset();
    mockOnOpenChange.mockReset();
    mockOrderForm.setValue.mockReset();
  });

  it('renders the dialog with form fields when open', () => {
    render(
      <ChangeSorting
        open={{ state: true, action: '' }}
        onOpenChange={mockOnOpenChange}
      />
    );
    expect(screen.getByTestId('ItemPrintSequence')).toBeInTheDocument();
    expect(screen.getByTestId('KitComponentPrintSeq')).toBeInTheDocument();
    expect(screen.getByText('Ok')).toBeInTheDocument();
    expect(screen.getByText('Cancel')).toBeInTheDocument();
  });

  it('does not render when not open', () => {
    render(
      <ChangeSorting
        open={{ state: true, action: '' }}
        onOpenChange={mockOnOpenChange}
      />
    );
    expect(screen.queryByTestId('ItemPrintSequence'));
  });

  it('initializes form with default values from order form', () => {
    render(
      <ChangeSorting
        open={{ state: true, action: '' }}
        onOpenChange={mockOnOpenChange}
      />
    );
    const itemSelect = screen.getByTestId(
      'ItemPrintSequence'
    ) as HTMLSelectElement;
    const kitSelect = screen.getByTestId(
      'KitComponentPrintSeq'
    ) as HTMLSelectElement;
    expect(itemSelect.value).toBe('GROUP_BY_ITEM');
    expect(kitSelect.value).toBe('KIT');
  });

  it('disables kit component select when item print sequence is PRINT_AS_ENTERED', async () => {
    render(
      <ChangeSorting
        open={{ state: true, action: '' }}
        onOpenChange={mockOnOpenChange}
      />
    );
    const itemSelect = screen.getByTestId('ItemPrintSequence');
    fireEvent.change(itemSelect, { target: { value: 'PRINT_AS_ENTERED' } });
    const kitSelect = screen.getByTestId('KitComponentPrintSeq') as any;
    expect(kitSelect).toBeDisabled();
    expect(kitSelect.value).toBe('ITEM');
  });

  it('submits the form with correct payload', async () => {
    render(
      <ChangeSorting
        open={{ state: true, action: '' }}
        onOpenChange={mockOnOpenChange}
      />
    );
    const itemSelect = screen.getByTestId('ItemPrintSequence');
    fireEvent.change(itemSelect, { target: { value: 'GROUP_BY_ITEM' } });
    const kitSelect = screen.getByTestId('KitComponentPrintSeq');
    fireEvent.change(kitSelect, { target: { value: 'ITEM' } });
    const submitButton = screen.getByText('Ok');
    fireEvent.click(submitButton);
    await waitFor(() => {
      expect(mockChangeSorting).toHaveBeenCalledWith({
        body: {
          orderId: 123,
          ItemPrintSequence: 'GROUP_BY_ITEM',
          KitComponentPrintSeq: 'ITEM',
        },
      });
      expect(mockOrderForm.setValue).toHaveBeenCalledWith(
        'itemPrintSequence',
        'GROUP_BY_ITEM'
      );
      expect(mockOrderForm.setValue).toHaveBeenCalledWith(
        'kitComponentPrintSeq',
        'ITEM'
      );
      expect(mockOnOpenChange).toHaveBeenCalled();
    });
  });

  it('handles cancel button click', () => {
    render(
      <ChangeSorting
        open={{ state: true, action: '' }}
        onOpenChange={mockOnOpenChange}
      />
    );
    const cancelButton = screen.getByText('Cancel');
    fireEvent.click(cancelButton);
    expect(mockOnOpenChange).toHaveBeenCalled();
  });

  it('submits with null KitComponentPrintSeq when PRINT_AS_ENTERED is selected', async () => {
    render(
      <ChangeSorting
        open={{ state: true, action: '' }}
        onOpenChange={mockOnOpenChange}
      />
    );
    const itemSelect = screen.getByTestId('ItemPrintSequence');
    fireEvent.change(itemSelect, { target: { value: 'PRINT_AS_ENTERED' } });
    const submitButton = screen.getByText('Ok');
    fireEvent.click(submitButton);
    await waitFor(() => {
      expect(mockChangeSorting).toHaveBeenCalledWith({
        body: {
          orderId: 123,
          ItemPrintSequence: 'PRINT_AS_ENTERED',
          KitComponentPrintSeq: null,
        },
      });
    });
  });
});
