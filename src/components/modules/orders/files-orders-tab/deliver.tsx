import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import DatePicker from '@/components/forms/date-picker';
import InputField from '@/components/forms/input-field';
import SelectWidget from '@/components/forms/select';
import { TEXT_VALIDATION_RULE } from '@/constants/validation-constants';
import { DEFAULT_FORMAT, getDayInitial } from '@/lib/utils';
import { OrderInformationTypes, OrderTypeTabs } from '@/types/order.types';
import { useCallback, useEffect, useState } from 'react';
import { useFormContext } from 'react-hook-form';

const Deliver = ({ setupList, takedownList, loading }: OrderTypeTabs) => {
  const form = useFormContext<OrderInformationTypes>();
  const storeDateCalculationConfig = form.getValues('storeDateCalculation');
  // const includeWeekends =
  //   storeDateCalculationConfig.shipDatesCalculation === 'EXLUDE_WEEKEND';
  const isDeleted = form.watch('isDeleted') || false;
  const isOrderEntryReadOnly = form.watch('orderEntryReadOnly');
  const [modalMessage, setModalMessage] = useState<string>('');
  const [openModal, setOpenModal] = useState(false);
  const [confirmation, setConfirmation] = useState(false);
  const [activeDateSelection, setActiveDateSelection] = useState<
    'pickupDay' | 'deliveryDay' | 'arrivalDay' | 'returnShipDay'
  >('deliveryDay');
  const [prevDatesRef, setPrevDatesRef] = useState<{
    arrivalDate?: any;
    pickupDate?: any;
    deliveryDate?: any;
    returnShipDate?: any;
  }>({});
  const deliveryDate = form.watch('deliveryOrder.deliveryDate');
  const pickupDate = form.watch('deliveryOrder.pickupDate');
  const arrivalDate = form.watch('deliveryOrder.arrivalDate');
  const returnShipDate = form.watch('deliveryOrder.returnShipDate');
  const updateData = form.watch('updateData');
  const dateOfUseFrom = form.watch('dateOfUseFrom');

  // Update references dynamically when form values change
  useEffect(() => {
    if (updateData)
      setPrevDatesRef({
        arrivalDate: form.getValues('deliveryOrder.arrivalDate'),
        pickupDate: form.getValues('deliveryOrder.pickupDate'),
        deliveryDate: form.getValues('deliveryOrder.deliveryDate'),
        returnShipDate: form.getValues('deliveryOrder.returnShipDate'),
      });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [updateData]);

  useEffect(() => {
    if (
      storeDateCalculationConfig.allowEditingShipDate &&
      !prevDatesRef.deliveryDate &&
      !prevDatesRef.arrivalDate &&
      !prevDatesRef.pickupDate &&
      !prevDatesRef.returnShipDate
    ) {
      setPrevDatesRef({
        ...prevDatesRef,
        returnShipDate: returnShipDate || dateOfUseFrom,
        arrivalDate: arrivalDate || dateOfUseFrom,
        pickupDate: pickupDate || dateOfUseFrom,
        deliveryDate: deliveryDate || dateOfUseFrom,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    deliveryDate,
    arrivalDate,
    pickupDate,
    returnShipDate,
    dateOfUseFrom,
    storeDateCalculationConfig.allowEditingShipDate,
  ]);

  const toggleModal = useCallback(() => {
    setOpenModal((prev) => !prev);
    // Restore previous values when toggling modal
    if (activeDateSelection === 'arrivalDay')
      form.setValue('deliveryOrder.arrivalDate', prevDatesRef?.arrivalDate);
    if (activeDateSelection === 'pickupDay')
      form.setValue('deliveryOrder.pickupDate', prevDatesRef.pickupDate);
    if (activeDateSelection === 'deliveryDay')
      form.setValue('deliveryOrder.deliveryDate', prevDatesRef.deliveryDate);

    if (activeDateSelection === 'returnShipDay')
      form.setValue(
        'deliveryOrder.returnShipDate',
        prevDatesRef.returnShipDate
      );
    if (confirmation) setConfirmation(false);
  }, [
    activeDateSelection,
    form,
    prevDatesRef?.arrivalDate,
    prevDatesRef.deliveryDate,
    prevDatesRef.pickupDate,
    prevDatesRef.returnShipDate,
    confirmation,
  ]);

  const formatTime = (timeStr: any) => {
    if (!timeStr) return '';
    let [hour, minute] = timeStr?.split(':');
    let period = hour >= 12 ? 'PM' : 'AM';
    hour = hour % 12 || 12;
    return `${hour}:${minute} ${period}`;
  };

  const handleWarningOnBlur = useCallback(
    (
      date: Date | string | undefined,
      name: 'pickupDay' | 'deliveryDay' | 'arrivalDay' | 'returnShipDay'
    ) => {
      if (!date) return;
      // if (updateData) return;
      const selectedDate = new Date(date).getTime();
      const selectedDateDay = new Date(date);
      const day = getDayInitial({ date: selectedDateDay });

      const dateOfUseFrom = new Date(form.getValues('dateOfUseFrom')).getTime();
      // const dateOfUseThru = new Date(form.getValues('dateOfUseThru'));
      const today = new Date().getTime();
      // today.setHours(0, 0, 0, 0); // Normalize time to midnight for accurate comparison

      let errorMessage = '';
      const { maxDaysBeforeDelivery, allowArrivalDate } =
        storeDateCalculationConfig;

      switch (name) {
        case 'deliveryDay':
          if (selectedDate > dateOfUseFrom) {
            errorMessage =
              'The delivery date cannot be later than the date of use.';
          }
          // else if (selectedDate < today) {
          //   errorMessage = 'Delivery date cannot be in the past';
          // }
          break;

        case 'pickupDay':
          if (selectedDate < dateOfUseFrom) {
            errorMessage =
              'The pickup date cannot be earlier than the date of use';
          }
          //  else if (selectedDate < today) {
          //   errorMessage = 'The pickup date cannot be earlier than the Today';
          // }
          break;

        case 'arrivalDay':
          if (allowArrivalDate) {
            if (selectedDate > dateOfUseFrom) {
              errorMessage = 'Arrival date must be before Date of Use';
            }
            // else if (selectedDate < today) {
            //   errorMessage = 'Arrival date cannot be in the past';
            // }
          } else {
            if (selectedDate > dateOfUseFrom) {
              errorMessage = 'Arrival date must be before Date of Use';
            } else if (selectedDate === dateOfUseFrom) {
              errorMessage = 'Arrival date must be before Date of Use';
            }
            // else if (selectedDate < today) {
            //   errorMessage = 'Arrival date cannot be in the past';
            // }
          }
          break;

        case 'returnShipDay':
          if (selectedDate < dateOfUseFrom) {
            errorMessage =
              'The return ship date cannot be earlier than the date of use.';
          }
          // else if (selectedDate < today) {
          //   errorMessage =
          //     'The return ship date cannot be earlier than the date of use.';
          // }
          break;
      }

      // Additional validation for maximum past days before delivery
      if (name === 'deliveryDay' && maxDaysBeforeDelivery) {
        const pastDays = Math.floor(
          (today - selectedDate) / (1000 * 60 * 60 * 24)
        );
        if (pastDays > maxDaysBeforeDelivery) {
          setConfirmation(true);
          errorMessage = `The delivery date should not be earlier than ${maxDaysBeforeDelivery} days in the past. Are you sure you want to use this delivery date?`;
        }
      }

      if (errorMessage) {
        setModalMessage(errorMessage);
        setOpenModal(true);
        return;
      }
      if (name === 'deliveryDay') {
        setPrevDatesRef({
          ...prevDatesRef,
          deliveryDate: date,
        });
      }

      if (name === 'pickupDay') {
        setPrevDatesRef({
          ...prevDatesRef,
          pickupDate: date,
        });
      }
      if (name === 'arrivalDay') {
        setPrevDatesRef({
          ...prevDatesRef,
          arrivalDate: date,
        });
      }
      if (name === 'returnShipDay') {
        setPrevDatesRef({
          ...prevDatesRef,
          returnShipDate: date,
        });
      }
      setActiveDateSelection(name);
      // Update form value only if validation passes
      form.setValue(`deliveryOrder.${name}`, day);
    },
    [form, prevDatesRef, storeDateCalculationConfig]
  );
  const onDateChange = useCallback(
    (
      date: Date | string | undefined,
      name: 'pickupDay' | 'deliveryDay' | 'arrivalDay' | 'returnShipDay'
    ) => {
      if (!date && name) return;
      setActiveDateSelection(name);
    },
    []
  );

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div className="grid grid-cols-3 gap-4">
        <DatePicker
          form={form}
          name="deliveryOrder.deliveryDate"
          label="Delivery Date"
          placeholder="Select Date"
          pClassName="col-span-2"
          handleBlur={(date) => handleWarningOnBlur(date, 'deliveryDay')}
          onDateChange={(date) => onDateChange(date, 'deliveryDay')}
          enableInput
          validation={TEXT_VALIDATION_RULE}
          disabled={isDeleted || isOrderEntryReadOnly}
          // includeWeekends={includeWeekends}
        />
        <InputField
          name="deliveryOrder.deliveryDay"
          form={form}
          label="Day"
          placeholder="Day"
          disabled
        />
      </div>
      <InputField
        name="deliveryOrder.deliveryInfo"
        form={form}
        label="Delivery Info"
        placeholder="Enter Delivery Info"
        pClassName="col-span-1"
        disabled={isDeleted || isOrderEntryReadOnly}
      />
      <SelectWidget
        form={form}
        name="deliveryOrder.orderSetupId"
        label="Setup"
        placeholder="Select Setup"
        isClearable={false}
        optionsList={setupList}
        isLoading={loading}
        disabled={isDeleted || isOrderEntryReadOnly}
      />
      <div className="grid grid-cols-2 gap-4">
        <InputField
          name="deliveryOrder.deliveryTimeIn"
          form={form}
          label="Delivery Time Window"
          onChange={formatTime}
          type="time"
          disabled={isDeleted || isOrderEntryReadOnly}
        />
        <InputField
          name="deliveryOrder.deliveryTimeOut"
          form={form}
          label=""
          onChange={formatTime}
          type="time"
          pClassName="mt-8"
          disabled={isDeleted || isOrderEntryReadOnly}
        />
      </div>
      <div className="grid grid-cols-3 gap-4">
        <DatePicker
          form={form}
          name="deliveryOrder.pickupDate"
          label="Pickup Date"
          placeholder="Select Date"
          handleBlur={(date) => handleWarningOnBlur(date, 'pickupDay')}
          onDateChange={(date) => onDateChange(date, 'pickupDay')}
          pClassName="col-span-2"
          enableInput
          validation={TEXT_VALIDATION_RULE}
          disabled={isDeleted || isOrderEntryReadOnly}
          // includeWeekends={includeWeekends}
        />
        <InputField
          name="deliveryOrder.pickupDay"
          form={form}
          label="Day"
          placeholder="Day"
          disabled
        />
      </div>
      <InputField
        name="deliveryOrder.pickupInfo"
        form={form}
        label="Pickup Info"
        placeholder="Enter Pickup Info"
        disabled={isDeleted || isOrderEntryReadOnly}
      />
      <SelectWidget
        form={form}
        name="deliveryOrder.takedownId"
        label="Takedown"
        placeholder="Select Takedown"
        isClearable={false}
        optionsList={takedownList}
        isLoading={loading}
        disabled={isDeleted || isOrderEntryReadOnly}
      />
      <div className="grid grid-cols-2 gap-4">
        <InputField
          name="deliveryOrder.pickupTimeIn"
          form={form}
          label="Pickup Time Windows"
          onChange={formatTime}
          type="time"
          disabled={isDeleted || isOrderEntryReadOnly}
        />
        <InputField
          name="deliveryOrder.pickupTimeOut"
          form={form}
          label=""
          onChange={formatTime}
          type="time"
          pClassName="mt-8"
          disabled={isDeleted || isOrderEntryReadOnly}
        />
      </div>
      <div className="grid grid-cols-3 gap-4">
        <DatePicker
          form={form}
          name="deliveryOrder.arrivalDate"
          label="Arrival Date"
          placeholder={DEFAULT_FORMAT}
          handleBlur={(date) => handleWarningOnBlur(date, 'arrivalDay')}
          onDateChange={(date) => onDateChange(date, 'arrivalDay')}
          pClassName="col-span-2"
          enableInput
          disabled={
            isDeleted ||
            isOrderEntryReadOnly ||
            !storeDateCalculationConfig.allowEditingShipDate
          }
          // includeWeekends={includeWeekends}
          // validation={TEXT_VALIDATION_RULE}
        />
        <InputField
          form={form}
          name="deliveryOrder.arrivalDay"
          label="Day"
          placeholder="Day"
          disabled
        />
      </div>
      <div className="grid grid-cols-3 gap-4">
        <DatePicker
          form={form}
          name="deliveryOrder.returnShipDate"
          label="Return Ship Date"
          placeholder={DEFAULT_FORMAT}
          handleBlur={(date) => handleWarningOnBlur(date, 'returnShipDay')}
          onDateChange={(date) => onDateChange(date, 'returnShipDay')}
          pClassName="col-span-2"
          enableInput
          disabled={
            isDeleted ||
            isOrderEntryReadOnly ||
            !storeDateCalculationConfig.allowEditingShipDate
          }
          // validation={TEXT_VALIDATION_RULE}
          // includeWeekends={includeWeekends}
        />
        <InputField
          form={form}
          name="deliveryOrder.returnShipDay"
          label="Day"
          placeholder="Day"
          disabled
        />
      </div>

      <AppConfirmationModal
        title={'Warning'}
        description={
          <div>
            {modalMessage}
            {/* <span className="font-semibold text-base text-text-Default"></span> */}
          </div>
        }
        open={openModal}
        handleCancel={toggleModal}
        handleSubmit={
          confirmation
            ? () => {
                setOpenModal((prev) => !prev);
                setConfirmation(false);
                setPrevDatesRef({
                  deliveryDate: form.getValues('deliveryOrder.deliveryDate'),
                });
              }
            : undefined
        }
        cancelLabel={confirmation ? 'No' : 'Ok'}
        submitLabel={confirmation ? 'Yes' : ''}
      />
    </div>
  );
};

export default Deliver;
