import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import DatePicker from '@/components/forms/date-picker';
import InputField from '@/components/forms/input-field';
import SelectWidget from '@/components/forms/select';
import { TEXT_VALIDATION_RULE } from '@/constants/validation-constants';
import { DEFAULT_FORMAT, getDayInitial } from '@/lib/utils';
import { OrderInformationTypes, OrderTypeTabs } from '@/types/order.types';
import { useCallback, useEffect, useState } from 'react';
import { useFormContext } from 'react-hook-form';

const WillCall = ({ setupList, takedownList, loading }: OrderTypeTabs) => {
  const form = useFormContext<OrderInformationTypes>();
  const storeDateCalculationConfig = form.getValues('storeDateCalculation');
  const isDeleted = form.watch('isDeleted') || false;
  const isOrderEntryReadOnly = form.watch('orderEntryReadOnly');
  const [modalMessage, setModalMessage] = useState<string>('');
  const [openModal, setOpenModal] = useState(false);
  const [activeDateSelection, setActiveDateSelection] = useState<
    'pickupDay' | 'returnDay' | 'arrivalDay' | 'returnShipDay'
  >('pickupDay');
  const [prevDatesRef, setPrevDatesRef] = useState<{
    arrivalDate?: any;
    pickupDate?: any;
    returnDate?: any;
    returnShipDate?: any;
  }>({});

  const updateData = form.watch('updateData');
  const returnDate = form.watch('willCallOrder.returnDate');
  const pickupDate = form.watch('willCallOrder.pickupDate');
  const arrivalDate = form.watch('willCallOrder.arrivalDate');
  const returnShipDate = form.watch('willCallOrder.returnShipDate');
  const dateOfUseFrom = form.watch('dateOfUseFrom');

  // Initialize previous dates reference
  useEffect(() => {
    if (updateData) {
      setPrevDatesRef({
        arrivalDate: form.getValues('willCallOrder.arrivalDate'),
        pickupDate: form.getValues('willCallOrder.pickupDate'),
        returnDate: form.getValues('willCallOrder.returnDate'),
        returnShipDate: form.getValues('willCallOrder.returnShipDate'),
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [updateData]);

  // Set initial previous dates when values exist
  useEffect(() => {
    if (
      storeDateCalculationConfig.allowEditingShipDate &&
      !prevDatesRef.returnDate &&
      !prevDatesRef.arrivalDate &&
      !prevDatesRef.pickupDate &&
      !prevDatesRef.returnShipDate
    ) {
      setPrevDatesRef((prev) => ({
        ...prev,
        returnDate: returnDate || dateOfUseFrom,
        pickupDate: pickupDate || dateOfUseFrom,
        arrivalDate: arrivalDate || dateOfUseFrom,
        returnShipDate: returnShipDate || dateOfUseFrom,
      }));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    returnDate,
    pickupDate,
    arrivalDate,
    returnShipDate,
    dateOfUseFrom,
    storeDateCalculationConfig.allowEditingShipDate,
  ]);

  const toggleModal = useCallback(() => {
    setOpenModal((prev) => !prev);

    // Restore specific previous value based on active selection
    if (activeDateSelection === 'arrivalDay')
      form.setValue('willCallOrder.arrivalDate', prevDatesRef.arrivalDate);
    if (activeDateSelection === 'pickupDay')
      form.setValue('willCallOrder.pickupDate', prevDatesRef.pickupDate);
    if (activeDateSelection === 'returnDay')
      form.setValue('willCallOrder.returnDate', prevDatesRef.returnDate);
    if (activeDateSelection === 'returnShipDay')
      form.setValue(
        'willCallOrder.returnShipDate',
        prevDatesRef.returnShipDate
      );
  }, [
    activeDateSelection,
    form,
    prevDatesRef?.arrivalDate,
    prevDatesRef.returnDate,
    prevDatesRef.pickupDate,
    prevDatesRef.returnShipDate,
  ]);

  useEffect(() => {
    const currentPickupInfo = form.getValues('willCallOrder.pickupInfo');
    const currentReturnInfo = form.getValues('willCallOrder.returnInfo');

    if (!currentPickupInfo) {
      form.setValue('willCallOrder.pickupInfo', 'CPU');
    }

    if (!currentReturnInfo) {
      form.setValue('willCallOrder.returnInfo', 'CR');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const formatTime = (timeStr: any) => {
    if (!timeStr) return '';
    let [hour, minute] = timeStr?.split(':');
    let period = hour >= 12 ? 'PM' : 'AM';
    hour = hour % 12 || 12;
    return `${hour}:${minute} ${period}`;
  };
  // Rest of the component remains the same until handleWarningOnBlur...

  const handleWarningOnBlur = useCallback(
    (
      date: Date | string | undefined,
      name: 'pickupDay' | 'returnDay' | 'arrivalDay' | 'returnShipDay'
    ) => {
      if (!date) return;

      const selectedDate = new Date(date);
      const day = getDayInitial({ date: selectedDate });
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const dateOfUseFrom = new Date(form.getValues('dateOfUseFrom'));

      let errorMessage = '';
      const { allowArrivalDate } = storeDateCalculationConfig;

      switch (name) {
        case 'pickupDay':
          if (selectedDate > dateOfUseFrom) {
            errorMessage =
              'The delivery date cannot be later than the date of use.';
          }
          // else if (selectedDate < today) {
          //   errorMessage = 'Delivery date cannot be in the past';
          // }
          break;

        case 'returnDay':
          if (selectedDate < dateOfUseFrom) {
            errorMessage =
              'The pickup date cannot be earlier than the date of use';
          }
          // else if (selectedDate < today) {
          //   errorMessage = 'The pickup date cannot be earlier than the Today';
          // }
          break;

        case 'arrivalDay':
          if (allowArrivalDate) {
            if (selectedDate > dateOfUseFrom) {
              errorMessage = 'Arrival date must be before Date of Use';
            }
            //  else if (selectedDate < today) {
            //   errorMessage = 'Arrival date cannot be in the past';
            // }
          } else {
            if (selectedDate > dateOfUseFrom) {
              errorMessage = 'Arrival date must be before Date of Use';
            } else if (selectedDate === dateOfUseFrom) {
              errorMessage = 'Arrival date must be before Date of Use';
            }
            // else if (selectedDate < today) {
            //   errorMessage = 'Arrival date cannot be in the past';
            // }
          }
          break;

        case 'returnShipDay':
          if (selectedDate < dateOfUseFrom) {
            errorMessage =
              'The return ship date cannot be earlier than the date of use.';
          }
          //  else if (selectedDate < today) {
          //   errorMessage =
          //     'The return ship date cannot be earlier than the date of use.';
          // }
          break;
      }

      if (errorMessage) {
        setModalMessage(errorMessage);
        setOpenModal(true);
        return;
      }

      // Update previous dates reference
      setPrevDatesRef((prev) => ({
        ...prev,
        ...(name === 'pickupDay' && { pickupDate: date }),
        ...(name === 'returnDay' && { returnDate: date }),
        ...(name === 'arrivalDay' && { arrivalDate: date }),
        ...(name === 'returnShipDay' && { returnShipDate: date }),
      }));
      setActiveDateSelection(name);
      // Update form value
      form.setValue(`willCallOrder.${name}`, day);
    },
    [form, storeDateCalculationConfig]
  );

  const onDateChange = useCallback(
    (
      date: Date | string | undefined,
      name: 'pickupDay' | 'returnDay' | 'arrivalDay' | 'returnShipDay'
    ) => {
      if (date && name) {
        setActiveDateSelection(name);
      }
    },
    []
  );

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div className="grid grid-cols-3 gap-4">
        <DatePicker
          form={form}
          name="willCallOrder.pickupDate"
          label="Pickup Date"
          placeholder="Select Date"
          onDateChange={(date) => onDateChange(date, 'pickupDay')}
          handleBlur={(date) => handleWarningOnBlur(date, 'pickupDay')}
          validation={TEXT_VALIDATION_RULE}
          pClassName="col-span-2"
          enableInput
          disabled={isDeleted || isOrderEntryReadOnly}
        />
        <InputField
          name="willCallOrder.pickupDay"
          form={form}
          label="Day"
          placeholder="Day"
          disabled
        />
      </div>
      <InputField
        name="willCallOrder.pickupInfo"
        form={form}
        label="Pickup Info"
        placeholder="Enter Pickup Info"
        disabled={isDeleted || isOrderEntryReadOnly}
      />

      <SelectWidget
        form={form}
        name="willCallOrder.orderSetupId"
        label="Setup"
        placeholder="Select Setup"
        isClearable={false}
        optionsList={setupList}
        isLoading={loading}
        disabled={isDeleted || isOrderEntryReadOnly}
      />

      <div className="grid grid-cols-2 gap-4">
        <InputField
          name="willCallOrder.pickupTimeIn"
          form={form}
          label="Pickup Time Window"
          onChange={formatTime}
          type="time"
          disabled={isDeleted || isOrderEntryReadOnly}
        />
        <InputField
          name="willCallOrder.pickupTimeOut"
          form={form}
          onChange={formatTime}
          type="time"
          className="mt-8"
          disabled={isDeleted || isOrderEntryReadOnly}
        />
      </div>
      <div className="grid grid-cols-3 gap-4">
        <DatePicker
          form={form}
          name="willCallOrder.returnDate"
          label="Return Date"
          placeholder="Select Date"
          onDateChange={(date) => onDateChange(date, 'returnDay')}
          handleBlur={(date) => handleWarningOnBlur(date, 'returnDay')}
          pClassName="col-span-2"
          validation={TEXT_VALIDATION_RULE}
          enableInput
          disabled={isDeleted || isOrderEntryReadOnly}
        />
        <InputField
          name="willCallOrder.returnDay"
          form={form}
          label="Day"
          placeholder="Day"
          disabled
        />
      </div>
      <InputField
        name="willCallOrder.returnInfo"
        form={form}
        label="Return Info"
        placeholder="Enter Return Info"
        disabled={isDeleted || isOrderEntryReadOnly}
      />

      <SelectWidget
        form={form}
        name="willCallOrder.takedownId"
        label="Takedown"
        placeholder="Select Takedown"
        isClearable={false}
        optionsList={takedownList}
        isLoading={loading}
        disabled={isDeleted || isOrderEntryReadOnly}
      />
      <div className="grid grid-cols-2 gap-4">
        <InputField
          name="willCallOrder.returnTimeIn"
          form={form}
          label="Return Time Windows"
          onChange={formatTime}
          type="time"
          disabled={isDeleted || isOrderEntryReadOnly}
        />
        <InputField
          name="willCallOrder.returnTimeOut"
          form={form}
          onChange={formatTime}
          type="time"
          className="mt-8"
          disabled={isDeleted || isOrderEntryReadOnly}
        />
      </div>

      <div className="col-span-2 grid grid-cols-6 gap-4">
        <DatePicker
          form={form}
          name="willCallOrder.arrivalDate"
          label="Arrival Date"
          onDateChange={(date) => onDateChange(date, 'arrivalDay')}
          handleBlur={(date) => handleWarningOnBlur(date, 'arrivalDay')}
          placeholder={DEFAULT_FORMAT}
          disabled={
            isDeleted ||
            isOrderEntryReadOnly ||
            !storeDateCalculationConfig.allowEditingShipDate
          }
          pClassName="col-span-2"
        />
        <InputField
          name="willCallOrder.arrivalDay"
          form={form}
          label="Day"
          placeholder="Day"
          disabled
          pClassName="col-span-1"
        />
        <DatePicker
          form={form}
          name="willCallOrder.returnShipDate"
          label="Return Ship Date"
          onDateChange={(date) => onDateChange(date, 'returnShipDay')}
          handleBlur={(date) => handleWarningOnBlur(date, 'returnShipDay')}
          placeholder={DEFAULT_FORMAT}
          disabled={
            isDeleted ||
            isOrderEntryReadOnly ||
            !storeDateCalculationConfig.allowEditingShipDate
          }
          pClassName="col-span-2"
        />
        <InputField
          name="willCallOrder.returnShipDay"
          form={form}
          label="Day"
          placeholder="Day"
          disabled
          pClassName="col-span-1"
        />
      </div>
      <AppConfirmationModal
        title={'Warning'}
        description={
          <div>
            {modalMessage}
            {/* <span className="font-semibold text-base text-text-Default"></span> */}
          </div>
        }
        open={openModal}
        handleCancel={toggleModal}
        cancelLabel={'Ok'}
      />
    </div>
  );
};

export default WillCall;
