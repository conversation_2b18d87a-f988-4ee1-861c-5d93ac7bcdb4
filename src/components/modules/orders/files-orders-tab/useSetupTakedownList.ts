import { SETUP_TAKEDOWN_API_ROUTES } from '@/constants/api-constants';
import useOptionList from '@/hooks/useOptionList';
import { generateLabelValuePairs } from '@/lib/utils';
import { useCallback, useEffect, useMemo } from 'react';
import { FieldValues, UseFormReturn } from 'react-hook-form';

interface useSetupTakedownListType<T extends FieldValues> {
  form: UseFormReturn<T> | any;
  name: 'deliveryOrder' | 'shipOrder' | 'willCallOrder';
}
const useSetupTakedownList = <T extends FieldValues>({
  form,
  name,
}: useSetupTakedownListType<T>) => {
  const { options: setupTakedownList, optionLoading: loading } = useOptionList({
    url: SETUP_TAKEDOWN_API_ROUTES.ALL,
    valueKey: 'type',
    labelKey: 'description',
    sortBy: 'description',
    isOption: false,
  });

  // find out & return the setup / takedown default selected value
  const getDefaultId = useCallback(
    (type: 1 | 2) => {
      const listItem = setupTakedownList?.find(
        (item: any) => item?.isDefault && item?.type === type
      );
      return listItem?.id;
    },
    [setupTakedownList]
  );

  const setupId = getDefaultId(1);
  const takedownId = getDefaultId(2);

  const orderSetupIdValue = form.watch(`${name}.orderSetupId`);
  const takedownIdValue = form.watch(`${name}.takedownId`);
  const setDefaultValues = !(orderSetupIdValue || takedownIdValue);

  useEffect(() => {
    if (setDefaultValues) {
      form.setValue(`${name}.orderSetupId`, setupId);
      form.setValue(`${name}.takedownId`, takedownId);
    }
  }, [form, name, setDefaultValues, setupId, takedownId]);

  const setupList = useMemo(() => {
    const list = generateLabelValuePairs({
      data: setupTakedownList?.filter((item: any) => item.type === 1),
      labelKey: 'description',
      valueKey: 'id',
    });
    return list?.filter((item) => item?.label);
  }, [setupTakedownList]);

  const takedownList = useMemo(() => {
    const list = generateLabelValuePairs({
      data: setupTakedownList?.filter((item: any) => item.type === 2),
      labelKey: 'description',
      valueKey: 'id',
    });
    return list?.filter((item) => item?.label);
  }, [setupTakedownList]);

  return { setupList, takedownList, loading };
};

export default useSetupTakedownList;
