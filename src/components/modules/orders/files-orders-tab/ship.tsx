import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import DatePicker from '@/components/forms/date-picker';
import InputField from '@/components/forms/input-field';
import SelectWidget from '@/components/forms/select';
import { TEXT_VALIDATION_RULE } from '@/constants/validation-constants';
import { DEFAULT_FORMAT, getDayInitial } from '@/lib/utils';
import { OrderInformationTypes, OrderTypeTabs } from '@/types/order.types';
import { useCallback, useEffect, useState } from 'react';
import { useFormContext } from 'react-hook-form';

const Ship = ({ setupList, takedownList, loading }: OrderTypeTabs) => {
  const form = useFormContext<OrderInformationTypes>();
  const isDeleted = form.watch('isDeleted');
  const isOrderEntryReadOnly = form.watch('orderEntryReadOnly');
  const [modalMessage, setModalMessage] = useState<string>('');
  const [openModal, setOpenModal] = useState(false);
  const storeDateCalculationConfig = form.getValues('storeDateCalculation');
  const includeWeekends =
    storeDateCalculationConfig.shipDatesCalculation === 'EXLUDE_WEEKEND';
  const [activeDateSelection, setActiveDateSelection] = useState<
    'shipDay' | 'returnArrivalDay'
  >('shipDay');
  const [prevDatesRef, setPrevDatesRef] = useState<{
    shipDate?: any;
    returnArrivalDate?: any;
  }>({});
  const updateData = form.watch('updateData');

  useEffect(() => {
    if (updateData) {
      setPrevDatesRef({
        shipDate: form.getValues('shipOrder.shipDate'),
        returnArrivalDate: form.getValues('shipOrder.returnArrivalDate'),
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [updateData]);

  const formatTime = (timeStr: any) => {
    if (!timeStr) return '';
    let [hour, minute] = timeStr?.split(':');
    let period = hour >= 12 ? 'PM' : 'AM';
    hour = hour % 12 || 12;
    return `${hour}:${minute} ${period}`;
  };

  const toggleModal = useCallback(() => {
    setOpenModal((prev) => !prev);
    if (activeDateSelection === 'shipDay')
      form.setValue('shipOrder.shipDate', prevDatesRef.shipDate);
    if (activeDateSelection === 'returnArrivalDay')
      form.setValue(
        'shipOrder.returnArrivalDate',
        prevDatesRef.returnArrivalDate
      );
  }, [
    activeDateSelection,
    form,
    prevDatesRef.returnArrivalDate,
    prevDatesRef.shipDate,
  ]);

  //on date change get the initial day of the date
  const handleWarningOnBlur = useCallback(
    (date: Date | string | undefined, name: 'shipDay' | 'returnArrivalDay') => {
      if (!date) return;

      const day = getDayInitial({ date });
      const selectedDate = new Date(date);
      const dateOfUseFrom = new Date(form.getValues('dateOfUseFrom'));
      let errorMessage = '';
      if (name === 'shipDay') {
        if (selectedDate > dateOfUseFrom) {
          errorMessage =
            'The Delivery Date can not be later than the date of use';
        }
      } else if (name === 'returnArrivalDay') {
        if (selectedDate < dateOfUseFrom) {
          errorMessage =
            'The Pickup Date can not be earlier than the date of use';
        }
      }
      if (errorMessage) {
        setModalMessage(errorMessage);
        setOpenModal(true);
        return;
      }
      setActiveDateSelection(name);
      form.setValue(`shipOrder.${name}`, day);
    },
    [form]
  );
  const onDateChange = useCallback(
    (date: Date | string | undefined, name: 'shipDay' | 'returnArrivalDay') => {
      if (!date && name) return;
      setActiveDateSelection(name);
    },
    []
  );

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div className="grid grid-cols-3 gap-4">
        <DatePicker
          form={form}
          name="shipOrder.shipDate"
          label="Ship Date"
          placeholder="Select Date"
          onDateChange={(date) => onDateChange(date, 'shipDay')}
          handleBlur={(date) => handleWarningOnBlur(date, 'shipDay')}
          pClassName="col-span-2"
          enableInput
          validation={TEXT_VALIDATION_RULE}
          disabled={isDeleted || isOrderEntryReadOnly}
          includeWeekends={includeWeekends}
        />
        <InputField
          name="shipOrder.shipDay"
          form={form}
          label="Day"
          placeholder="Day"
          disabled
        />
      </div>
      <InputField
        name="shipOrder.shipInfo"
        form={form}
        label="Ship Info"
        placeholder="Enter Ship Info"
        disabled={isDeleted || isOrderEntryReadOnly}
      />
      <SelectWidget
        form={form}
        name="shipOrder.orderSetupId"
        label="Setup"
        placeholder="Select Setup"
        isClearable={false}
        optionsList={setupList}
        isLoading={loading}
        disabled={isDeleted || isOrderEntryReadOnly}
      />
      <div className="grid grid-cols-2 gap-4">
        <InputField
          name="shipOrder.shipTimeIn"
          form={form}
          label="Ship Time Window"
          onChange={formatTime}
          type="time"
          disabled={isDeleted || isOrderEntryReadOnly}
        />
        <InputField
          name="shipOrder.shipTimeOut"
          form={form}
          onChange={formatTime}
          type="time"
          className="mt-8"
          disabled={isDeleted || isOrderEntryReadOnly}
        />
      </div>
      <div className="grid grid-cols-3 gap-4">
        <DatePicker
          form={form}
          name="shipOrder.returnArrivalDate"
          label="Return Arrival Date"
          placeholder="Select Date"
          onDateChange={(date) => onDateChange(date, 'returnArrivalDay')}
          handleBlur={(date) => handleWarningOnBlur(date, 'returnArrivalDay')}
          pClassName="col-span-2"
          enableInput
          // validation={TEXT_VALIDATION_RULE}
          disabled={isDeleted || isOrderEntryReadOnly}
          includeWeekends={includeWeekends}
        />
        <InputField
          name="shipOrder.returnArrivalDay"
          form={form}
          onChange={formatTime}
          label="Day"
          placeholder="Day"
          disabled
        />
      </div>
      <InputField
        name="shipOrder.returnInfo"
        form={form}
        label="Return Info"
        placeholder="Enter Return Info"
        maxLength={15}
        disabled={isDeleted || isOrderEntryReadOnly}
      />

      <SelectWidget
        form={form}
        name="shipOrder.takedownId"
        label="Takedown"
        placeholder="Select Takedown"
        isClearable={false}
        optionsList={takedownList}
        isLoading={loading}
        disabled={isDeleted || isOrderEntryReadOnly}
      />
      <div className="grid grid-cols-2 gap-4">
        <InputField
          name="shipOrder.returnTimeIn"
          form={form}
          label="Return Time Windows"
          onChange={formatTime}
          type="time"
          disabled={isDeleted || isOrderEntryReadOnly}
        />
        <InputField
          name="shipOrder.returnTimeOut"
          form={form}
          onChange={formatTime}
          type="time"
          className="mt-8"
          disabled={isDeleted || isOrderEntryReadOnly}
        />
      </div>
      <div className="grid grid-cols-3 gap-4">
        <DatePicker
          form={form}
          name="shipOrder.arrivalDate"
          label="Arrival Date"
          placeholder={DEFAULT_FORMAT}
          pClassName="col-span-2"
          disabled
        />
        <InputField
          name="shipOrder.arrivalDay"
          form={form}
          label="Day"
          placeholder="Day"
          disabled
        />
      </div>
      <div className="grid grid-cols-3 gap-4">
        <DatePicker
          form={form}
          name="shipOrder.returnShipDate"
          label="Return Ship Date"
          placeholder={DEFAULT_FORMAT}
          disabled
          pClassName="col-span-2"
        />
        <InputField
          form={form}
          name="shipOrder.returnShipDay"
          label="Day"
          placeholder="Day"
          disabled
        />
      </div>

      <AppConfirmationModal
        title={'Warning'}
        description={<div>{modalMessage}</div>}
        open={openModal}
        handleCancel={toggleModal}
        cancelLabel={'Ok'}
      />
    </div>
  );
};

export default Ship;
