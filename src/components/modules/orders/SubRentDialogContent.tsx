import { DEFAULT_FORMAT, formatDate } from '@/lib/utils';
import { OrderSubRentsTypes } from '@/types/orders/order-item-details.types';
import { ColumnDef } from '@tanstack/react-table';
import { FC, useMemo } from 'react';
import DataTable from '@/components/common/data-tables';

interface Props {
  data: OrderSubRentsTypes[];
  isLoading: boolean;
}

const SubRentDialogContent: FC<Props> = ({ data, isLoading }) => {
  const columns: ColumnDef<OrderSubRentsTypes>[] = useMemo(() => {
    return [
      {
        accessorKey: 'id',
        header: 'SR#',
        size: 80,
      },
      {
        accessorKey: 'createdBy',
        header: 'SP',
        size: 80,
      },
      {
        accessorKey: 'resvNo',
        header: 'Reservation #',
      },
      {
        accessorKey: 'name',
        header: 'Equipment From',
      },
      {
        accessorKey: 'pickupDate',
        header: 'Pickup Date',
        size: 100,
        cell: ({ row }: any) =>
          formatDate(row?.original?.pickupDate, DEFAULT_FORMAT),
      },
      {
        accessorKey: 'returnDate',
        header: 'Return Date',
        size: 100,
        cell: ({ row }: any) =>
          formatDate(row?.original?.returnDate, DEFAULT_FORMAT),
      },
    ];
  }, []);

  return (
    <div className="flex flex-col gap-2 px-4 py-2">
      <p>This order required the following Sub-Rentals</p>
      <DataTable
        data={data}
        columns={columns}
        isLoading={isLoading}
        enableSearch={false}
        enablePagination={false}
        loaderRows={6}
        tableClassName="max-h-[300px] 2xl:max-h-[320px] overflow-auto"
      />
    </div>
  );
};

export default SubRentDialogContent;
