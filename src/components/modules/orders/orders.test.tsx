import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { MemoryRouter } from 'react-router-dom';
import Orders from './index';
import { useAddNewItemMutation } from '@/redux/features/common-api/common.api';
import { useGetListQuery } from '@/redux/features/list/category/list.api';
import { hasEnabledPermission } from '@/lib/hasEnabledPermission';

// Mock the necessary hooks and components
vi.mock('@/redux/features/list/category/list.api', () => ({
  useGetListQuery: vi.fn(),
}));

vi.mock('@/redux/features/common-api/common.api', () => ({
  useAddNewItemMutation: vi.fn(),
  useCheckInactiveItemsMutation: vi.fn(() => [vi.fn(), { data: null }]),
  useSubRentByOrderIdMutation: vi.fn(() => [
    vi.fn(),
    { data: null, isLoading: false },
  ]),
}));

vi.mock('@/hooks/useDownloadFile', () => ({
  useDownloadFile: () => ({
    downloadFile: vi.fn(),
  }),
}));

vi.mock('@/components/ui/toast/ToastContainer', () => ({
  UseToast: () => ({
    promise: vi.fn(),
  }),
}));

vi.mock('@/lib/hasEnabledPermission', () => ({
  hasEnabledPermission: vi.fn(() => false),
}));

vi.mock('@/lib/utils', () => ({
  getQueryParam: vi.fn(() => ''),
  updateQueryParam: vi.fn(),
  convertToFloat: vi.fn(({ value }) => `$${value}`),
  getPaginationObject: vi.fn(() => ({})),
  cn: vi.fn(() => ''),
}));

// Mock child components
vi.mock('@/components/common/app-data-table', () => ({
  __esModule: true,
  default: ({ heading, customToolBar }: any) => (
    <div>
      <h1>{heading}</h1>
      <div>{customToolBar}</div>
      <div>DataTable Content</div>
    </div>
  ),
}));

vi.mock('@/components/common/app-button', () => ({
  __esModule: true,
  default: ({ label, onClick }: any) => (
    <button onClick={onClick}>{label}</button>
  ),
}));

vi.mock('@/components/common/app-spinner', () => ({
  __esModule: true,
  default: ({ isLoading }: any) => (isLoading ? <div>Loading...</div> : null),
}));

vi.mock('@/components/common/dialog', () => ({
  __esModule: true,
  default: ({ open, children }: any) => (open ? <div>{children}</div> : null),
}));

describe('Orders Component', () => {
  const mockStore = configureStore({
    reducer: {
      orders: () => ({
        filters: [],
      }),
    },
  });

  beforeEach(() => {
    // Reset all mocks before each test
    vi.clearAllMocks();

    // Mock the useGetListQuery hook
    (useGetListQuery as any).mockReturnValue({
      data: {
        data: [
          {
            accessorKey: 'orderNumber',
            header: 'Order #',
            enabled: true,
            component: '',
            enableSorting: true,
            size: 100,
          },
          {
            accessorKey: 'status',
            header: 'Status',
            enabled: true,
            component: 'StatusBadge',
          },
        ],
      },
      refetch: vi.fn(),
      isFetching: false,
    });
    (useAddNewItemMutation as any).mockReturnValue([
      vi.fn(),
      { isLoading: false },
    ]);
  });

  const renderComponent = () => {
    return render(
      <Provider store={mockStore}>
        <MemoryRouter>
          <Orders />
        </MemoryRouter>
      </Provider>
    );
  };

  it('should render the component with heading', () => {
    renderComponent();
    expect(screen.getByText('Orders')).toBeInTheDocument();
  });

  it('should render the DataTable component', () => {
    renderComponent();
    expect(screen.getByText('DataTable Content')).toBeInTheDocument();
  });

  it('should render the New Order button', () => {
    renderComponent();
    expect(screen.getByText('New Order')).toBeInTheDocument();
  });

  it('should render the Other Processes dropdown', () => {
    renderComponent();
    expect(screen.getByText('Other Processes')).toBeInTheDocument();
  });

  it('should disable New Order button when readOnlyPermission is true', () => {
    vi.mocked(hasEnabledPermission as any).mockReturnValueOnce(true);
    renderComponent();
    const newOrderButton = screen.getByText('New Order');
    expect(newOrderButton).toBeInTheDocument();
  });

  it('should call navigate when New Order button is clicked', () => {
    renderComponent();
    const newOrderButton = screen.getByText('New Order');
    const isClicked = fireEvent.click(newOrderButton);
    expect(isClicked);
  });

  it('should show loading spinner when isFetching is true', () => {
    (useGetListQuery as any).mockReturnValueOnce({
      data: null,
      refetch: vi.fn(),
      isFetching: true,
    });
    const isLoading = renderComponent();
    expect(isLoading);
  });

  it('should handle column ordering dialog', async () => {
    renderComponent();
  });

  it('should handle sub-rental dialog', async () => {
    const isrendered = renderComponent();
    expect(isrendered);
  });

  it('should handle inactive items dialog', async () => {
    const isrendered = renderComponent();
    expect(isrendered);
  });
});
