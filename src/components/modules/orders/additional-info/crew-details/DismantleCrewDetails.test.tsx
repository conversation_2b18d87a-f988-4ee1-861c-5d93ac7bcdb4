import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import DismantleCrewDetails from './DismantleCrewDetails';
import {
  useGetInstallationCrewDetailsQuery,
  useUpdateCrewDetailsMutation,
} from '@/redux/features/orders/additional-info.api';
import { useFieldArray, useForm } from 'react-hook-form';

// Mock the hooks and components
vi.mock('@/redux/features/orders/additional-info.api', () => ({
  useGetInstallationCrewDetailsQuery: vi.fn(),
  useUpdateCrewDetailsMutation: vi.fn(),
}));

vi.mock('react-hook-form', () => ({
  useForm: vi.fn(),
  useFieldArray: vi.fn(),
}));

vi.mock('@/components/common/app-button', () => ({
  default: ({ label, onClick }: { label: string; onClick: () => void }) => (
    <button onClick={onClick}>{label}</button>
  ),
}));

vi.mock('@/components/common/data-tables', () => ({
  default: ({ data }: { data: any[] }) => (
    <div>
      {data?.map((item, index) => <div key={index}>{item.listId}</div>)}
    </div>
  ),
}));

vi.mock('@/components/common/dialog', () => ({
  default: ({ children }: { children: React.ReactNode }) => (
    <div>{children}</div>
  ),
}));

vi.mock('@/components/forms/select-dropdown', () => ({
  default: () => (
    <select data-testid="department-select">
      <option value="1">Department 1</option>
    </select>
  ),
}));

vi.mock('@/lib/utils', () => ({
  cn: (...args: string[]) => args.join(' '),
  getQueryParam: () => '123',
}));

describe('DismantleCrewDetails', () => {
  const mockOnClose = vi.fn();
  const mockReset = vi.fn();
  const mockGetValues = vi.fn();
  const mockWatch = vi.fn();
  const mockSetValue = vi.fn();

  const mockUseForm = {
    control: {},
    reset: mockReset,
    getValues: mockGetValues,
    watch: mockWatch,
    setValue: mockSetValue,
    formState: { errors: {} },
    handleSubmit: (fn: any) => fn,
  };

  const mockDepartmentForm = {
    watch: vi.fn().mockReturnValue('1'),
    formState: { errors: {} },
  };

  beforeEach(() => {
    vi.clearAllMocks();
    (useForm as any).mockReturnValueOnce(mockDepartmentForm);
    (useForm as any).mockReturnValueOnce(mockUseForm);
    (useFieldArray as any).mockReturnValue({
      fields: [
        {
          id: 1,
          listId: 1,
          people: 2,
          hours: 8,
          date: '2023-01-01',
          weekDay: 'Monday',
        },
        {
          id: 2,
          listId: 2,
          people: 3,
          hours: 6,
          date: '2023-01-02',
          weekDay: 'Tuesday',
        },
      ],
      append: vi.fn(),
      remove: vi.fn(),
    });
    (useGetInstallationCrewDetailsQuery as any).mockReturnValue({
      data: {
        data: [
          { id: 1, people: 2, hours: 8, date: '2023-01-01', weekDay: 'Monday' },
          {
            id: 2,
            people: 3,
            hours: 6,
            date: '2023-01-02',
            weekDay: 'Tuesday',
          },
        ],
      },
      isLoading: false,
    });
    (useUpdateCrewDetailsMutation as any).mockReturnValue([
      vi.fn().mockResolvedValue({}),
      { isLoading: false },
    ]);
    mockWatch.mockReturnValue({
      department: '1',
      rows: [
        {
          id: 1,
          listId: 1,
          people: 2,
          hours: 8,
          date: '2023-01-01',
          weekDay: 'Monday',
        },
        {
          id: 2,
          listId: 2,
          people: 3,
          hours: 6,
          date: '2023-01-02',
          weekDay: 'Tuesday',
        },
      ],
    });
    mockGetValues.mockReturnValue({
      department: '1',
      rows: [
        {
          id: 1,
          listId: 1,
          people: 2,
          hours: 8,
          date: '2023-01-01',
          weekDay: 'Monday',
        },
        {
          id: 2,
          listId: 2,
          people: 3,
          hours: 6,
          date: '2023-01-02',
          weekDay: 'Tuesday',
        },
      ],
    });
  });

  it('renders the component with title and department select', () => {
    const isRendered = render(<DismantleCrewDetails onClose={mockOnClose} />);
    expect(isRendered);
  });

  it('displays loading state when data is loading', () => {
    (useGetInstallationCrewDetailsQuery as any).mockReturnValueOnce({
      isLoading: true,
    });
    render(<DismantleCrewDetails onClose={mockOnClose} />);
    expect(screen.getByText('1')).toBeInTheDocument();
  });

  it('displays crew data when loaded', async () => {
    render(<DismantleCrewDetails onClose={mockOnClose} />);
    await waitFor(() => {
      expect(screen.getByText('1')).toBeInTheDocument();
      expect(screen.getByText('2')).toBeInTheDocument();
    });
  });

  it('calls onClose when cancel button is clicked', () => {
    render(<DismantleCrewDetails onClose={mockOnClose} />);
    fireEvent.click(screen.getByText('Cancel'));
    expect(mockOnClose).toHaveBeenCalled();
  });

  it('calls saveCrewDetails when save button is clicked', async () => {
    const mockSave = vi.fn().mockResolvedValue({});
    (useUpdateCrewDetailsMutation as any).mockReturnValueOnce([
      mockSave,
      { isLoading: false },
    ]);
    render(<DismantleCrewDetails onClose={mockOnClose} />);
    fireEvent.click(screen.getByText('Save'));
    await waitFor(() => {
      expect(mockSave);
    });
  });

  it('disables save button when no changes are made', () => {
    render(<DismantleCrewDetails onClose={mockOnClose} />);
    const saveButton = screen.getByText('Save');
    expect(saveButton).not.toBeDisabled();
  });

  it('enables save button when changes are made', () => {
    mockWatch.mockReturnValueOnce({
      department: '1',
      rows: [
        {
          id: 1,
          listId: 1,
          people: 3,
          hours: 8,
          date: '2023-01-01',
          weekDay: 'Monday',
        },
        {
          id: 2,
          listId: 2,
          people: 3,
          hours: 6,
          date: '2023-01-02',
          weekDay: 'Tuesday',
        },
      ],
    });
    render(<DismantleCrewDetails onClose={mockOnClose} />);
    const saveButton = screen.getByText('Save');
    expect(saveButton).not.toBeDisabled();
  });

  it('resets form with new data when department changes', () => {
    const mockDepartmentWatch = vi
      .fn()
      .mockReturnValueOnce('1')
      .mockReturnValueOnce('2');
    (useForm as any).mockReturnValueOnce({
      watch: mockDepartmentWatch,
      formState: { errors: {} },
    });
    render(<DismantleCrewDetails onClose={mockOnClose} />);
    fireEvent.change(screen.getByTestId('department-select'), {
      target: { value: '2' },
    });
    expect(useGetInstallationCrewDetailsQuery);
  });
});
