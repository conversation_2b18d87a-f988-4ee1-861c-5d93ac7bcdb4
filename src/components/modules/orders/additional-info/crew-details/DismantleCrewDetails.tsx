import { useEffect, useMemo } from 'react';
import { useFieldArray, useForm } from 'react-hook-form';

import AppButton from '@/components/common/app-button';
import DataTable from '@/components/common/data-tables';
import CustomDialog from '@/components/common/dialog';
import SelectDropDown from '@/components/forms/select-dropdown';

import { departmentOptions } from '@/constants/order-constants';
import { cn, getQueryParam } from '@/lib/utils';

import {
  useGetInstallationCrewDetailsQuery,
  useUpdateCrewDetailsMutation,
} from '@/redux/features/orders/additional-info.api';

import { getModifiedItems } from '@/lib/getModifiedItems';
import { CrewDetailsTypes } from '@/types/order.types';
import { isEqual } from 'lodash';
import { useColumns } from './useColumns';

export interface CrewDetailsProps {
  onClose: () => void;
}

const DismantleCrewDetails = ({ onClose }: CrewDetailsProps) => {
  const id = getQueryParam('id') as string;
  const departmentForm = useForm({
    defaultValues: {
      department: '1',
    },
  });

  const form = useForm<CrewDetailsTypes>({
    defaultValues: {
      department: '1',
      rows: [],
    },
  });

  const { control, reset, getValues, watch, setValue } = form;
  const { fields } = useFieldArray({
    name: 'rows',
    control,
  });

  const department = departmentForm.watch('department');

  const { data, isLoading } = useGetInstallationCrewDetailsQuery(
    {
      departmentId: department ?? '1',
      leadType: '2',
      orderId: id,
    },
    {
      skip: !department,
    }
  );

  const [saveCrewDetails, { isLoading: isSaving }] =
    useUpdateCrewDetailsMutation();

  const defaultValues = useMemo(() => {
    return {
      departmentId: '1',
      rows: data?.data?.map((item: any) => {
        return { ...item, listId: item?.id };
      }),
    };
  }, [data?.data]);

  const formValues = watch();

  const isItemsModified = useMemo(() => {
    return !isEqual(defaultValues.rows ?? [], formValues.rows ?? []);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [JSON.stringify(defaultValues.rows), JSON.stringify(formValues.rows)]);

  const handleChange = (
    name: 'people' | 'hours',
    value: string,
    index: number
  ) => {
    setValue(`rows.${index}.${name}`, Number(value));
  };

  const columns = useColumns({
    form,
    handleChange,
  });

  const handleOnSubmit = async () => {
    const formValues = getValues();
    const modified = getModifiedItems(
      formValues.rows ?? [],
      defaultValues.rows ?? []
    );

    if (modified.length === 0) return;

    const payload = modified.map((row) => ({
      id: row.id,
      people: row.people,
      hours: row.hours,
      date: row.date,
      weekDay: row.weekDay,
      lead: 2,
      departmentId: row.departmentId,
      orderId: row.orderId,
    }));

    try {
      await saveCrewDetails({
        orderId: id,
        body: payload,
      });
    } catch (error) {}
  };

  useEffect(() => {
    if (data?.data) {
      reset(defaultValues);
    }
  }, [data?.data, defaultValues, reset]);

  return (
    <CustomDialog
      onOpenChange={onClose}
      description=""
      open={true}
      className={cn('2xl:max-w-[45%] max-w-[55%]')}
      contentClassName={cn('h-[500px] overflow-y-auto p-0 border-none p-4')}
      title="Dismantle Crew Details"
    >
      <>
        <div className="space-y-4">
          <div className="flex justify-end w-full">
            <div className="w-[220px]">
              <SelectDropDown
                name="department"
                form={departmentForm}
                placeholder="Select Department"
                label="Department"
                optionsList={departmentOptions}
                allowClear={false}
              />
            </div>
          </div>
          <DataTable
            data={fields ?? []}
            columns={columns}
            enablePagination={false}
            isLoading={isLoading}
            noDataPlaceholder="No crew data found."
            tableClassName="max-h-[320px]"
            bindingKey="listId"
            loaderRows={5}
          />
        </div>
        <div className="flex justify-end gap-4 fixed bottom-0 right-6 bg-white pb-4">
          <AppButton
            label="Save"
            onClick={handleOnSubmit}
            className="w-20"
            isLoading={isSaving}
            disabled={!isItemsModified || isSaving}
          />
          <AppButton
            label="Cancel"
            onClick={onClose}
            className="w-20 hover:bg-slate-100"
            variant="neutral"
            disabled={isSaving}
          />
        </div>
      </>
    </CustomDialog>
  );
};

export default DismantleCrewDetails;
