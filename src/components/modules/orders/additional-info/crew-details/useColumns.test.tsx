import { renderHook } from '@testing-library/react';
import { useColumns } from './useColumns';
import { describe, it, expect, vi } from 'vitest';
import { UseFormReturn } from 'react-hook-form';
import { CrewDetailsTypes } from '@/types/order.types';

describe('useColumns hook', () => {
  it('should return the correct column configuration', () => {
    const mockForm = {
      control: vi.fn(),
      register: vi.fn(),
      handleSubmit: vi.fn(),
      getValues: vi.fn(),
      setValue: vi.fn(),
      formState: { errors: {} },
      watch: vi.fn(),
    } as unknown as UseFormReturn<CrewDetailsTypes>;
    const mockHandleChange = vi.fn();
    const { result } = renderHook(() =>
      useColumns({ form: mockForm, handleChange: mockHandleChange })
    );
    const columns = result.current;
    expect(columns).toHaveLength(4);
    expect(columns[0].accessorKey).toBe('date');
    expect(columns[1].accessorKey).toBe('weekDay');
    expect(columns[2].accessorKey).toBe('people');
    expect(columns[3].accessorKey).toBe('hours');
    const dateCell = columns[0].cell?.({
      row: {
        original: { date: '2023-01-01' },
        index: 0,
      },
    });
    expect(dateCell).toMatch('01/01/2023');
    const peopleCell = columns[2].cell?.({
      row: {
        index: 0,
        original: {},
      },
    }) as any;
    expect(peopleCell).toBeDefined();
    if (peopleCell) {
      expect(peopleCell.props.name).toBe('rows.0.people');
      expect(peopleCell.props.placeholder).toBe('People');
    }
    const hoursCell = columns[3].cell?.({
      row: {
        index: 0,
        original: {},
      },
    }) as any;
    expect(hoursCell).toBeDefined();
    if (hoursCell) {
      expect(hoursCell.props.name).toBe('rows.0.hours');
      expect(hoursCell.props.placeholder).toBe('Hours');
    }
  });

  it('should call handleChange when NumberInputField values change', () => {
    const mockForm = {
      control: vi.fn(),
      register: vi.fn(),
      handleSubmit: vi.fn(),
      getValues: vi.fn(),
      setValue: vi.fn(),
      formState: { errors: {} },
      watch: vi.fn(),
    } as unknown as UseFormReturn<CrewDetailsTypes>;
    const mockHandleChange = vi.fn();
    const { result } = renderHook(() =>
      useColumns({ form: mockForm, handleChange: mockHandleChange })
    );
    const columns = result.current;
    const peopleCell = columns[2].cell?.({
      row: {
        index: 1,
        original: {},
      },
    }) as any;
    if (peopleCell) {
      peopleCell.props.onValueChange('5');
      expect(mockHandleChange).toHaveBeenCalledWith('people', '5', 1);
    }
    const hoursCell = columns[3].cell?.({
      row: {
        index: 1,
        original: {},
      },
    }) as any;
    if (hoursCell) {
      hoursCell.props.onValueChange('8');
      expect(mockHandleChange).toHaveBeenCalledWith('hours', '8', 1);
    }
  });

  it('should memoize the columns array', () => {
    const mockForm = {
      control: vi.fn(),
      register: vi.fn(),
      handleSubmit: vi.fn(),
      getValues: vi.fn(),
      setValue: vi.fn(),
      formState: { errors: {} },
      watch: vi.fn(),
    } as unknown as UseFormReturn<CrewDetailsTypes>;
    const mockHandleChange = vi.fn();
    const { result, rerender } = renderHook(
      ({ form, handleChange }) => useColumns({ form, handleChange }),
      {
        initialProps: {
          form: mockForm,
          handleChange: mockHandleChange,
        },
      }
    );
    const firstResult = result.current;
    rerender({ form: mockForm, handleChange: mockHandleChange });
    expect(result.current).toBe(firstResult);
    const newMockForm = { ...mockForm };
    rerender({ form: newMockForm, handleChange: mockHandleChange });
    expect(result.current).not.toBe(firstResult);
  });
});
