import NumberInputField from '@/components/forms/number-input-field';
import { DEFAULT_FORMAT, formatDate } from '@/lib/utils';
import { CrewDetailsTypes } from '@/types/order.types';
import { useMemo } from 'react';
import { UseFormReturn } from 'react-hook-form';

interface UseItemListColumnsProps {
  form: UseFormReturn<CrewDetailsTypes>;
  handleChange: (
    name: 'people' | 'hours',
    value: string,
    index: number
  ) => void;
}

export const useColumns = ({ form, handleChange }: UseItemListColumnsProps) => {
  return useMemo(
    () => [
      {
        accessorKey: 'date',
        header: 'Date',
        size: 100,
        cell: ({ row }: any) => {
          return formatDate(row?.original?.date, DEFAULT_FORMAT);
        },
      },
      {
        accessorKey: 'weekDay',
        header: 'Weekdays',
        size: 100,
      },
      {
        accessorKey: 'people',
        header: 'People',
        size: 100,
        cell: ({ row }) => {
          return (
            <NumberInputField
              name={`rows.${row.index}.people`}
              form={form}
              placeholder="People"
              maxLength={4}
              pClassName="p-1"
              className="max-w-[100px] h-8"
              onValueChange={(value) =>
                handleChange('people', value, row.index)
              }
            />
          );
        },
      },
      {
        accessorKey: 'hours',
        header: 'Hours',
        size: 100,
        cell: ({ row }) => {
          return (
            <NumberInputField
              name={`rows.${row.index}.hours`}
              form={form}
              placeholder="Hours"
              maxLength={4}
              pClassName="p-1"
              className="max-w-[100px] h-8"
              onValueChange={(value) => handleChange('hours', value, row.index)}
            />
          );
        },
      },
    ],
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [form]
  );
};
