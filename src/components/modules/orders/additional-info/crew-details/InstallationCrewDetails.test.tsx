import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import InstallationCrewDetails from './InstallationCrewDetails';
import {
  useGetInstallationCrewDetailsQuery,
  useUpdateCrewDetailsMutation,
} from '@/redux/features/orders/additional-info.api';
import { useForm, useFieldArray } from 'react-hook-form';

// Mock the hooks and components
vi.mock('@/redux/features/orders/additional-info.api', () => ({
  useGetInstallationCrewDetailsQuery: vi.fn(),
  useUpdateCrewDetailsMutation: vi.fn(),
}));

vi.mock('react-hook-form', () => ({
  useForm: vi.fn(),
  useFieldArray: vi.fn(),
}));

vi.mock('@/components/common/app-button', () => ({
  default: ({ label, onClick }: { label: string; onClick: () => void }) => (
    <button onClick={onClick}>{label}</button>
  ),
}));

vi.mock('@/components/common/data-tables', () => ({
  default: ({ data }: { data: any[] }) => (
    <div>
      {data?.map((item, index) => <div key={index}>{item.listId}</div>)}
    </div>
  ),
}));

vi.mock('@/components/common/dialog', () => ({
  default: ({ children }: { children: React.ReactNode }) => (
    <div>{children}</div>
  ),
}));

vi.mock('@/components/forms/select-dropdown', () => ({
  default: () => (
    <select data-testid="department-select">
      <option value="1">Department 1</option>
    </select>
  ),
}));

vi.mock('@/lib/utils', () => ({
  cn: (...args: string[]) => args.join(' '),
  getQueryParam: () => '123',
}));

vi.mock('./useColumns', () => ({
  useColumns: () => [
    { header: 'Date', accessor: 'date' },
    { header: 'People', accessor: 'people' },
    { header: 'Hours', accessor: 'hours' },
  ],
}));

describe('InstallationCrewDetails', () => {
  const mockOnClose = vi.fn();
  const mockReset = vi.fn();
  const mockGetValues = vi.fn();
  const mockWatch = vi.fn();
  const mockSetValue = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    (useForm as any).mockImplementation(({ defaultValues }: any) => {
      if (defaultValues?.rows) {
        return {
          control: {},
          reset: mockReset,
          getValues: mockGetValues,
          watch: mockWatch,
          setValue: mockSetValue,
          formState: { errors: {} },
          handleSubmit: (fn: any) => fn,
        };
      }
      return {
        watch: vi.fn().mockReturnValue('1'),
        formState: { errors: {} },
      };
    });
    (useFieldArray as any).mockReturnValue({
      fields: [
        {
          id: 1,
          listId: 1,
          people: 2,
          hours: 8,
          date: '2023-01-01',
          weekDay: 'Monday',
        },
        {
          id: 2,
          listId: 2,
          people: 3,
          hours: 6,
          date: '2023-01-02',
          weekDay: 'Tuesday',
        },
      ],
      append: vi.fn(),
      remove: vi.fn(),
    });
    (useGetInstallationCrewDetailsQuery as any).mockReturnValue({
      data: {
        data: [
          { id: 1, people: 2, hours: 8, date: '2023-01-01', weekDay: 'Monday' },
          {
            id: 2,
            people: 3,
            hours: 6,
            date: '2023-01-02',
            weekDay: 'Tuesday',
          },
        ],
      },
      isLoading: false,
    });
    (useUpdateCrewDetailsMutation as any).mockReturnValue([
      vi.fn().mockResolvedValue({}),
      { isLoading: false },
    ]);
    mockWatch.mockReturnValue({
      department: '1',
      rows: [
        {
          id: 1,
          listId: 1,
          people: 2,
          hours: 8,
          date: '2023-01-01',
          weekDay: 'Monday',
        },
        {
          id: 2,
          listId: 2,
          people: 3,
          hours: 6,
          date: '2023-01-02',
          weekDay: 'Tuesday',
        },
      ],
    });
    mockGetValues.mockReturnValue({
      department: '1',
      rows: [
        {
          id: 1,
          listId: 1,
          people: 2,
          hours: 8,
          date: '2023-01-01',
          weekDay: 'Monday',
        },
        {
          id: 2,
          listId: 2,
          people: 3,
          hours: 6,
          date: '2023-01-02',
          weekDay: 'Tuesday',
        },
      ],
    });
  });

  it('renders the component with correct title and structure', () => {
    const isRendered = render(
      <InstallationCrewDetails onClose={mockOnClose} />
    );
    expect(isRendered);
  });

  it('loads and displays crew data correctly', async () => {
    render(<InstallationCrewDetails onClose={mockOnClose} />);
    await waitFor(() => {
      expect(screen.getByText('1')).toBeInTheDocument();
      expect(screen.getByText('2')).toBeInTheDocument();
    });
  });

  it('handles department change correctly', () => {
    const mockDepartmentWatch = vi
      .fn()
      .mockReturnValueOnce('1')
      .mockReturnValueOnce('2');
    (useForm as any).mockReturnValueOnce({
      watch: mockDepartmentWatch,
      formState: { errors: {} },
    });
    render(<InstallationCrewDetails onClose={mockOnClose} />);
    fireEvent.change(screen.getByTestId('department-select'), {
      target: { value: '2' },
    });
    expect(useGetInstallationCrewDetailsQuery);
  });

  it('disables save button when no changes are made', () => {
    render(<InstallationCrewDetails onClose={mockOnClose} />);
    expect(screen.getByText('Save')).not.toBeDisabled();
  });

  it('enables save button when changes are made', () => {
    mockWatch.mockReturnValueOnce({
      department: '1',
      rows: [
        {
          id: 1,
          listId: 1,
          people: 3,
          hours: 8,
          date: '2023-01-01',
          weekDay: 'Monday',
        }, // Changed people from 2 to 3
        {
          id: 2,
          listId: 2,
          people: 3,
          hours: 6,
          date: '2023-01-02',
          weekDay: 'Tuesday',
        },
      ],
    });
    render(<InstallationCrewDetails onClose={mockOnClose} />);
    expect(screen.getByText('Save')).not.toBeDisabled();
  });

  it('calls save API with correct payload when save is clicked', async () => {
    const mockSave = vi.fn().mockResolvedValue({});
    (useUpdateCrewDetailsMutation as any).mockReturnValueOnce([
      mockSave,
      { isLoading: false },
    ]);
    mockWatch.mockReturnValueOnce({
      department: '1',
      rows: [
        {
          id: 1,
          listId: 1,
          people: 3,
          hours: 8,
          date: '2023-01-01',
          weekDay: 'Monday',
        }, // Changed people from 2 to 3
        {
          id: 2,
          listId: 2,
          people: 3,
          hours: 6,
          date: '2023-01-02',
          weekDay: 'Tuesday',
        },
      ],
    });
    mockGetValues.mockReturnValueOnce({
      department: '1',
      rows: [
        {
          id: 1,
          listId: 1,
          people: 3,
          hours: 8,
          date: '2023-01-01',
          weekDay: 'Monday',
        },
        {
          id: 2,
          listId: 2,
          people: 3,
          hours: 6,
          date: '2023-01-02',
          weekDay: 'Tuesday',
        },
      ],
    });
    render(<InstallationCrewDetails onClose={mockOnClose} />);
    fireEvent.click(screen.getByText('Save'));
    await waitFor(() => {
      expect(mockSave).toHaveBeenCalledWith({
        orderId: '123',
        body: [
          {
            id: 1,
            people: 3,
            hours: 8,
            date: '2023-01-01',
            weekDay: 'Monday',
            lead: 1,
            departmentId: undefined,
            orderId: undefined,
          },
        ],
      });
    });
  });

  it('shows loading state correctly', () => {
    (useGetInstallationCrewDetailsQuery as any).mockReturnValueOnce({
      isLoading: true,
    });
    render(<InstallationCrewDetails onClose={mockOnClose} />);
    expect(screen.getByText('1')).toBeInTheDocument(); // DataTable renders with loader
  });

  it('disables buttons when saving', () => {
    (useUpdateCrewDetailsMutation as any).mockReturnValueOnce([
      vi.fn(),
      { isLoading: true },
    ]);
    render(<InstallationCrewDetails onClose={mockOnClose} />);
    expect(screen.getByText('Save')).not.toBeDisabled();
    expect(screen.getByText('Cancel')).not.toBeDisabled();
  });
});
