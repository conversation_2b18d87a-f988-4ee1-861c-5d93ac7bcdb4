import AppButton from '@/components/common/app-button';
import AppCheckbox from '@/components/common/app-checkbox/AppCheckbox';
import { useUpdateAssignOptionMutation } from '@/redux/features/orders/order.api';
import { Label } from '@radix-ui/react-label';
import { useCallback } from 'react';
import { useForm } from 'react-hook-form';

interface AssignTypes {
  state: boolean;
  action: string;
}
interface AssignOptionsProps {
  setOpen: React.Dispatch<React.SetStateAction<AssignTypes>>;
}

const AdditionalOptions = ({ setOpen }: AssignOptionsProps) => {
  // const id = getQueryParam('id');
  const form = useForm({
    defaultValues: {
      isChecked: false,
    },
  });

  // const { data, isLoading } = useGetAssignOptionQuery(id);
  const [updateOption] = useUpdateAssignOptionMutation();

  // useEffect(() => {
  //   if (data) {
  //     form.setValue('isChecked', data.data.isChecked);
  //   }
  // }, [data, form]);

  const handleCancel = useCallback(() => {
    setOpen({ state: false, action: '' });
  }, [setOpen]);

  const handleCheckboxChange = async (e: boolean) => {
    try {
      await updateOption({ isChecked: e }).unwrap();
    } catch (err) {
      return err;
    }
  };

  return (
    <div>
      <div className="flex gap-1 justify-start items-center">
        <AppCheckbox
          name="isChecked"
          control={form.control}
          className="data-[state=checked]:bg-[#04BCC2]"
          onChange={handleCheckboxChange}
        />
        <Label className="text-sm sm:text-base font-medium">
          E-mail return label
        </Label>
      </div>

      <div className="flex justify-end gap-4 fixed bottom-0 right-6 bg-white pb-4">
        <AppButton
          label="Cancel"
          onClick={handleCancel}
          className="w-28"
          variant="neutral"
        />
      </div>
      {/* <AppSpinner overlay isLoading={isLoading} /> */}
    </div>
  );
};

export default AdditionalOptions;
