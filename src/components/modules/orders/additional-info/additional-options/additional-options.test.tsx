import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import AdditionalOptions from './index';
import { useForm } from 'react-hook-form';
import { useUpdateAssignOptionMutation } from '@/redux/features/orders/order.api';

// Mock the hooks and components
vi.mock('react-hook-form', () => ({
  useForm: vi.fn(() => ({
    control: {},
    formState: {},
    handleSubmit: vi.fn(),
    setValue: vi.fn(),
    getValues: vi.fn(),
    watch: vi.fn(),
  })),
}));

vi.mock('@/redux/features/orders/order.api', () => ({
  useUpdateAssignOptionMutation: vi.fn(() => [
    vi.fn().mockResolvedValue({}),
    { isLoading: false },
  ]),
}));

vi.mock('@/components/common/app-button', () => ({
  default: ({ label, onClick, className, variant }: any) => (
    <button
      className={className}
      onClick={onClick}
      data-variant={variant}
      data-testid="app-button"
    >
      {label}
    </button>
  ),
}));

vi.mock('@/components/common/app-checkbox/AppCheckbox', () => ({
  default: ({ className, onChange }: any) => (
    <input
      type="checkbox"
      data-testid="app-checkbox"
      className={className}
      onChange={(e) => onChange(e.target.checked)}
    />
  ),
}));

describe('AdditionalOptions', () => {
  const mockSetOpen = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the component with all elements', () => {
    render(<AdditionalOptions setOpen={mockSetOpen} />);
    expect(screen.getByText('E-mail return label')).toBeInTheDocument();
    expect(screen.getByTestId('app-checkbox')).toBeInTheDocument();
    expect(screen.getByTestId('app-button')).toBeInTheDocument();
    expect(screen.getByTestId('app-button')).toHaveTextContent('Cancel');
  });

  it('calls setOpen when cancel button is clicked', () => {
    render(<AdditionalOptions setOpen={mockSetOpen} />);
    const cancelButton = screen.getByTestId('app-button');
    fireEvent.click(cancelButton);
    expect(mockSetOpen).toHaveBeenCalledWith({
      state: false,
      action: '',
    });
  });

  it('calls updateOption when checkbox is toggled', async () => {
    const mockUpdateOption = vi.fn().mockResolvedValue({});
    (useUpdateAssignOptionMutation as any).mockReturnValue([
      mockUpdateOption,
      { isLoading: false },
    ]);
    render(<AdditionalOptions setOpen={mockSetOpen} />);
    const checkbox = screen.getByTestId('app-checkbox');
    fireEvent.click(checkbox);
    await waitFor(() => {
      expect(mockUpdateOption).toHaveBeenCalledWith({ isChecked: true });
    });
    fireEvent.click(checkbox);
    await waitFor(() => {
      expect(mockUpdateOption).toHaveBeenCalledWith({ isChecked: false });
      expect(mockUpdateOption).toHaveBeenCalledTimes(2);
    });
  });

  it('handles updateOption error gracefully', async () => {
    const error = new Error('Update failed');
    const mockUpdateOption = vi.fn().mockRejectedValue(error);
    (useUpdateAssignOptionMutation as any).mockReturnValue([
      mockUpdateOption,
      { isLoading: false },
    ]);
    render(<AdditionalOptions setOpen={mockSetOpen} />);
    const checkbox = screen.getByTestId('app-checkbox');
    fireEvent.click(checkbox);
    await waitFor(() => {
      expect(mockUpdateOption).toHaveBeenCalled();
    });
  });

  it('initializes form with default values', () => {
    render(<AdditionalOptions setOpen={mockSetOpen} />);
    expect(useForm).toHaveBeenCalledWith({
      defaultValues: {
        isChecked: false,
      },
    });
  });
});
