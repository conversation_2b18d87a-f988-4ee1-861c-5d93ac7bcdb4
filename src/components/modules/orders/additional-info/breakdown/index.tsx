import AppButton from '@/components/common/app-button';
import DataTable from '@/components/common/data-tables';
import { BreakdownInfo } from '@/types/order.types';
import { ColumnDef, RowSelectionState } from '@tanstack/react-table';
import { useCallback, useMemo, useState } from 'react';

interface AssignTypes {
  state: boolean;
  action: string;
}

interface BreakdownProps {
  setOpen: React.Dispatch<React.SetStateAction<AssignTypes>>;
}

const Breakdown = ({ setOpen }: BreakdownProps) => {
  const [selectedRows, setSelectedRows] = useState<RowSelectionState>({});

  const selectedBreakdownId = useMemo(() => {
    return Object.keys(selectedRows)?.[0];
  }, [selectedRows]);

  const handleCancel = useCallback(() => {
    setSelectedRows({});
    setOpen({ state: false, action: '' });
  }, [setOpen]);

  const columns: ColumnDef<BreakdownInfo>[] = useMemo(
    () => [
      {
        accessorKey: 'employee',
        header: 'Employee',
        size: 80,
        enableSorting: true,
      },
      {
        accessorKey: 'date',
        header: 'Date',
        maxSize: 250,
        enableSorting: true,
      },
      {
        accessorKey: 'cost',
        header: 'Cost',
        maxSize: 250,
        enableSorting: true,
      },
    ],
    []
  );

  return (
    <div className="mb-5">
      <DataTable
        data={[]}
        columns={columns}
        enablePagination={false}
        enableRowSelection
        rowSelection={selectedRows}
        onRowSelectionChange={setSelectedRows}
        bindingKey="employee"
        tableClassName="max-h-[350px] overflow-y-auto"
      />

      <div className="flex justify-end gap-4 fixed bottom-0 right-6 bg-white pb-4">
        <AppButton
          label="OK"
          onClick={() => {}}
          disabled={!selectedBreakdownId}
          className="w-28"
        />
        <AppButton
          label="Cancel"
          onClick={handleCancel}
          className="w-28"
          variant="neutral"
        />
      </div>
    </div>
  );
};

export default Breakdown;
