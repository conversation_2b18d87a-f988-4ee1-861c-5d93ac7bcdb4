import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import AdditionalInfo from './AdditionalInfo';
import { UseFormReturn } from 'react-hook-form';
import {
  useGetAdditionalInfoQuery,
  useLazyGetAdditionalInfoAssignJobQuery,
} from '@/redux/features/orders/additional-info.api';
import { useGetOrderByIdQuery } from '@/redux/features/orders/order.api';
import {
  AdditionalInfoTypes,
  OrderInformationTypes,
} from '@/types/order.types';

// Create mock forms at the top level
const mockForm: UseFormReturn<AdditionalInfoTypes> = {
  watch: vi.fn(),
  setValue: vi.fn(),
  getValues: vi.fn(),
  handleSubmit: vi.fn((fn) => fn),
  register: vi.fn(),
  unregister: vi.fn(),
  formState: {
    isDirty: false,
    isLoading: false,
    isSubmitted: false,
    isSubmitSuccessful: false,
    isSubmitting: false,
    isValid: false,
    isValidating: false,
    submitCount: 0,
    dirtyFields: {},
    touchedFields: {},
    errors: {},
  },
  reset: vi.fn(),
  resetField: vi.fn(),
  setFocus: vi.fn(),
  trigger: vi.fn(),
  clearErrors: vi.fn(),
  setError: vi.fn(),
} as unknown as UseFormReturn<AdditionalInfoTypes>;

const mockFormInformation: UseFormReturn<OrderInformationTypes> = {
  watch: vi.fn(),
  setValue: vi.fn(),
  getValues: vi.fn(() => ({ selectedOrderType: 'SHIP' })),
  handleSubmit: vi.fn(),
  register: vi.fn(),
  unregister: vi.fn(),
  formState: {
    isDirty: false,
    isLoading: false,
    isSubmitted: false,
    isSubmitSuccessful: false,
    isSubmitting: false,
    isValid: false,
    isValidating: false,
    submitCount: 0,
    dirtyFields: {},
    touchedFields: {},
    errors: {},
  },
  reset: vi.fn(),
  resetField: vi.fn(),
  setFocus: vi.fn(),
  trigger: vi.fn(),
  clearErrors: vi.fn(),
  setError: vi.fn(),
} as unknown as UseFormReturn<OrderInformationTypes>;

// Mock the necessary dependencies
vi.mock('@/assets/icons/TrailingIcon', () => ({
  default: () => <div>TrailingIcon</div>,
}));

vi.mock('@/components/common/app-button', () => ({
  default: ({ label, onClick }: { label: string; onClick: () => void }) => (
    <button onClick={onClick}>{label}</button>
  ),
}));

vi.mock('@/components/common/app-confirmation-modal', () => ({
  default: ({
    open,
    description,
  }: {
    open: boolean;
    description: React.ReactNode;
  }) => (open ? <div>{description}</div> : null),
}));

vi.mock('@/components/common/BreadcrumbDialogRenderer', () => ({
  default: () => <div>BreadcrumbDialogRenderer</div>,
}));

vi.mock('@/components/common/data-tables', () => ({
  default: () => <div>DataTable</div>,
}));

vi.mock('@/components/forms/date-picker', () => ({
  default: ({ name, label }: { name: string; label: string }) => (
    <div>
      <label>{label}</label>
      <input name={name} placeholder={name} />
    </div>
  ),
}));

vi.mock('@/components/forms/input-field', () => ({
  default: ({ name, label }: { name: string; label: string }) => (
    <div>
      <label>{label}</label>
      <input name={name} placeholder={name} />
    </div>
  ),
}));

vi.mock('@/components/forms/number-input-field', () => ({
  default: ({ name, label }: { name: string; label: string }) => (
    <div>
      <label>{label}</label>
      <input name={name} type="number" />
    </div>
  ),
}));

vi.mock('@/components/forms/select', () => ({
  default: ({ name, label }: { name: string; label: string }) => (
    <div>
      <label>{label}</label>
      <select name={name} />
    </div>
  ),
}));

vi.mock('@/components/forms/text-area', () => ({
  default: ({ name, label }: { name: string; label: string }) => (
    <div>
      <label>{label}</label>
      <textarea name={name} />
    </div>
  ),
}));

vi.mock('@/redux/features/orders/additional-info.api', () => ({
  useGetAdditionalInfoQuery: vi.fn(() => ({
    data: null,
    isLoading: false,
  })),
  useLazyGetAdditionalInfoAssignJobQuery: vi.fn(() => [
    vi.fn(),
    { data: null, isLoading: false },
  ]),
  useSaveOrderAdditionalInfoForCrewMutation: vi.fn(() => [
    vi.fn(),
    { isLoading: false },
  ]),
}));

vi.mock('@/redux/features/orders/order.api', () => ({
  useGetOrderByIdQuery: vi.fn(() => ({
    data: null,
    isLoading: false,
  })),
}));

vi.mock('react-hook-form', async () => {
  const actual = await vi.importActual('react-hook-form');
  return {
    ...actual,
    useFormContext: vi.fn(() => mockFormInformation),
  };
});

describe('AdditionalInfo', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    Object.defineProperty(window, 'location', {
      value: {
        search: '?id=123',
      },
      writable: true,
    });

    // Setup mock implementations
    (mockForm.watch as any).mockImplementation(
      (field: keyof AdditionalInfoTypes) => {
        if (field === 'orderInfo') return { data: [], totalAmount: 0 };
        if (field === 'subRentInfo') return { data: [], totalAmount: 0 };
        if (field === 'installStartDate') return '2023-01-01';
        if (field === 'installEndDate') return '2023-01-10';
        if (field === 'dismantleStartDate') return '2023-02-01';
        if (field === 'dismantleEndDate') return '2023-02-10';
        return undefined;
      }
    );

    (mockForm.getValues as any).mockReturnValue({
      customerid: '456',
      jobNo: 123,
      description: '',
      totallabourcost: '',
      install: 'false',
      dismantle: 'false',
      buildingPermit: 'false',
      undergroundLocate: 'false',
      cadDrawing: 'false',
      isDeleted: 'false',
      installLeader: '',
      dismantleLeader: '',
      buildingPermitDate: null,
      undergroundDate: null,
      undergroundNo: '',
      cadDrawingFileLocation: '',
      tentLocation: '',
      arrivalDate: null,
      returnShipDate: null,
    });

    (mockFormInformation.getValues as any).mockReturnValue({
      selectedOrderType: 'SHIP',
    });
  });

  it('renders without crashing', () => {
    render(<AdditionalInfo form={mockForm} shippingManager={false} />);
    expect(screen.getByText('Additional Info')).toBeInTheDocument();
  });

  it('displays all main buttons', () => {
    render(<AdditionalInfo form={mockForm} shippingManager={false} />);
    expect(screen.getByText('Assign Job')).toBeInTheDocument();
    expect(screen.getByText('Assign Orders')).toBeInTheDocument();
    expect(screen.getByText('Breakdown')).toBeInTheDocument();
    expect(screen.getByText('Shipping Info')).toBeInTheDocument();
  });

  it('renders form fields correctly', () => {
    render(<AdditionalInfo form={mockForm} shippingManager={false} />);
    expect(screen.getByText('Job #')).toBeInTheDocument();
    expect(screen.getByText('Description')).toBeInTheDocument();
    expect(screen.getByText('Total Labor Cost')).toBeInTheDocument();
    expect(screen.getByText('Installation')).toBeInTheDocument();
  });

  it('handles job number blur event', async () => {
    const mockTrigger = vi.fn().mockResolvedValue({ data: { data: [] } });
    (useLazyGetAdditionalInfoAssignJobQuery as any).mockReturnValue([
      mockTrigger,
      { data: null, isLoading: false },
    ]);
    render(<AdditionalInfo form={mockForm} shippingManager={false} />);
    const jobInput = screen.getByRole('spinbutton');
    fireEvent.change(jobInput, { target: { value: '12345' } });
    await waitFor(() => {
      expect(mockTrigger);
    });
  });

  it('shows duplicate job warning when job number exists', async () => {
    const mockTrigger = vi.fn().mockResolvedValue({
      data: { data: [{ jobNo: 12345 }] },
    });
    (useLazyGetAdditionalInfoAssignJobQuery as any).mockReturnValue([
      mockTrigger,
      { data: null, isLoading: false },
    ]);
    render(<AdditionalInfo form={mockForm} shippingManager={false} />);
    const jobInput = screen.getByRole('spinbutton');
    fireEvent.change(jobInput, { target: { value: '12345' } });
    await waitFor(() => {
      expect(mockForm.setValue);
    });
  });

  it('displays order and sub-rental info sections', () => {
    render(<AdditionalInfo form={mockForm} shippingManager={false} />);
    expect(screen.getByText('Order Info')).toBeInTheDocument();
    expect(screen.getByText('Sub-Rental Info')).toBeInTheDocument();
    expect(screen.getAllByText('DataTable')).toHaveLength(2);
  });

  it('shows loading spinner when data is loading', () => {
    (useGetAdditionalInfoQuery as any).mockReturnValue({
      data: null,
      isLoading: true,
    });
    (useGetOrderByIdQuery as any).mockReturnValue({
      data: null,
      isLoading: true,
    });
    render(<AdditionalInfo form={mockForm} shippingManager={false} />);
    expect(screen.getByRole('status')).toBeInTheDocument();
  });

  it('handles date validation warnings', async () => {
    (mockForm.watch as any).mockImplementation(
      (field: keyof AdditionalInfoTypes) => {
        if (field === 'installStartDate') return '2023-01-01';
        if (field === 'installEndDate') return '2023-01-10';
        if (field === 'dismantleStartDate') return '2023-02-01';
        if (field === 'dismantleEndDate') return '2023-02-10';
        return undefined;
      }
    );
    render(<AdditionalInfo form={mockForm} shippingManager={false} />);
    const installStartInput = screen.getByPlaceholderText('installStartDate');
    fireEvent.change(installStartInput, {
      target: { value: '2023-01-15' },
    });
    fireEvent.blur(installStartInput);
    await waitFor(() => {
      const alert = screen.getByRole('alertdialog');
      expect(alert).toBeInTheDocument();
      expect(screen.getByTestId('spinner')).toBeInTheDocument();
    });
  });
});
