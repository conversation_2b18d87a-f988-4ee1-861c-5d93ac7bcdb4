import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import ShippingInfo from './index';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { setupListeners } from '@reduxjs/toolkit/query';
import {
  useAddReturnBoxMutation,
  useAddShippingBoxMutation,
  useSaveAdditionalInfoShippingInfoBoxMutation,
  useSaveAdditionalInfoShippingInfoItemMutation,
} from '@/redux/features/orders/additional-info.api';
import { useFormContext } from 'react-hook-form';

// Mock the necessary hooks and components
vi.mock('@/components/common/app-button', () => ({
  default: ({ label, onClick }: { label: string; onClick: () => void }) => (
    <button onClick={onClick}>{label}</button>
  ),
}));

vi.mock('@/components/common/data-tables', () => ({
  default: () => <div data-testid="data-table">DataTable</div>,
}));

vi.mock('@/redux/features/orders/additional-info.api', () => ({
  useGetAdditionalInfoShippingInfoItemQuery: vi.fn(() => ({
    data: {
      shipItems: [],
      shipStatus: 'NOT_SHIPPED',
      isCreatingME: false,
    },
    isLoading: false,
  })),
  useGetAdditionalInfoShippingInfoBoxQuery: vi.fn(() => ({
    data: [],
    isLoading: false,
  })),
  useSaveAdditionalInfoShippingInfoItemMutation: vi.fn(() => [
    vi.fn(),
    { isLoading: false },
  ]),
  useSaveAdditionalInfoShippingInfoBoxMutation: vi.fn(() => [
    vi.fn(),
    { isLoading: false },
  ]),
  useAddShippingBoxMutation: vi.fn(() => [vi.fn(), { isLoading: false }]),
  useAddReturnBoxMutation: vi.fn(() => [vi.fn(), { isLoading: false }]),
  useSplitRowInItemMutation: vi.fn(() => [vi.fn()]),
  useLazyGetTrackShippmentQuery: vi.fn(() => [vi.fn()]),
  useMissingEquMutation: vi.fn(() => [vi.fn(), { isLoading: false }]),
  useGetStatusForShippingInfoQuery: vi.fn(() => ({
    data: {
      data: [
        { id: 1, name: 'NOT_SHIPPED', description: 'Not Shipped' },
        { id: 2, name: 'SHIPPED', description: 'Shipped' },
      ],
    },
  })),
}));

vi.mock('@/redux/features/common-api/common.api', () => ({
  useGetAllMutation: vi.fn(() => [vi.fn(), { isLoading: false }]),
  useGetStatusForShippingInfoQuery: vi.fn(() => ({
    data: {
      data: [
        { id: 1, name: 'NOT_SHIPPED', description: 'Not Shipped' },
        { id: 2, name: 'SHIPPED', description: 'Shipped' },
      ],
    },
  })),
}));

vi.mock('react-hook-form', async () => {
  const actual = await import('react-hook-form');
  const Controller = ({ render }: any) =>
    render({ field: {}, fieldState: {}, formState: {} });

  return {
    ...actual,
    useForm: vi.fn(() => ({
      control: {},
      formState: { errors: {} },
      handleSubmit: vi.fn((fn) => fn),
      watch: vi.fn(),
      getValues: vi.fn(),
      setValue: vi.fn(),
      reset: vi.fn(),
      trigger: vi.fn(),
    })),
    useFormContext: vi.fn(() => ({
      watch: vi.fn(() => ({ status: 'INVOICED', dateOfUseFrom: '2023-01-01' })),
    })),
    useFieldArray: vi.fn(() => ({
      fields: [],
      append: vi.fn(),
    })),
    Controller,
  };
});

vi.mock('@/lib/utils', async () => {
  const actual = await import('@/lib/utils');
  return {
    ...actual,
    getQueryParam: vi.fn(() => '123'),
    formatDate: vi.fn(),
    cn: vi.fn(),
    generateLabelValuePairs: vi.fn(),
  };
});

describe('ShippingInfo Component', () => {
  const mockStore = configureStore({
    reducer: {},
  });

  setupListeners(mockStore.dispatch);

  const mockSetOpen = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders without crashing', () => {
    const isRender = render(
      <Provider store={mockStore}>
        <ShippingInfo setOpen={mockSetOpen} shippingManager={true} />
      </Provider>
    );
    expect(isRender);
  });

  it('displays action buttons when shippingManager is true', () => {
    const isRender = render(
      <Provider store={mockStore}>
        <ShippingInfo setOpen={mockSetOpen} shippingManager={true} />
      </Provider>
    );
    expect(isRender);
  });

  it('shows status dropdown with correct options', () => {
    render(
      <Provider store={mockStore}>
        <ShippingInfo setOpen={mockSetOpen} shippingManager={true} />
      </Provider>
    );
    expect(screen.getByText('Ship/Return Status')).toBeInTheDocument();
  });

  it('handles add shipping box button click', async () => {
    const mockAddShippingBox = vi.fn();
    vi.mocked(useAddShippingBoxMutation as any).mockReturnValueOnce([
      mockAddShippingBox,
      { isLoading: false },
    ]);
    render(
      <Provider store={mockStore}>
        <ShippingInfo setOpen={mockSetOpen} shippingManager={true} />
      </Provider>
    );
    await waitFor(() => {
      expect(mockAddShippingBox);
    });
  });

  it('handles add return box button click', async () => {
    const mockAddReturnBox = vi.fn();
    vi.mocked(useAddReturnBoxMutation as any).mockReturnValueOnce([
      mockAddReturnBox,
      { isLoading: false },
    ]);
    render(
      <Provider store={mockStore}>
        <ShippingInfo setOpen={mockSetOpen} shippingManager={true} />
      </Provider>
    );
    await waitFor(() => {
      expect(mockAddReturnBox);
    });
  });

  it('disables create M/E button when conditions are not met', () => {
    vi.mocked(useFormContext as any).mockReturnValueOnce({
      watch: vi.fn(() => ({ status: 'PENDING', dateOfUseFrom: '2023-01-01' })),
    });
    const meButton = render(
      <Provider store={mockStore}>
        <ShippingInfo setOpen={mockSetOpen} shippingManager={true} />
      </Provider>
    );
    expect(meButton);
  });

  it('opens confirmation modal when trying to create M/E with future invoice', async () => {
    vi.mocked(useFormContext as any).mockReturnValueOnce({
      watch: vi.fn(() => ({ status: 'INVOICED', dateOfUseFrom: '2099-01-01' })),
    });
    const isOpen = render(
      <Provider store={mockStore}>
        <ShippingInfo setOpen={mockSetOpen} shippingManager={true} />
      </Provider>
    );
    await waitFor(() => {
      expect(isOpen);
    });
  });

  it('handles save shipping items button click', async () => {
    const mockSaveShippingInfoItem = vi.fn();
    vi.mocked(
      useSaveAdditionalInfoShippingInfoItemMutation as any
    ).mockReturnValueOnce([mockSaveShippingInfoItem, { isLoading: false }]);

    render(
      <Provider store={mockStore}>
        <ShippingInfo setOpen={mockSetOpen} shippingManager={true} />
      </Provider>
    );
    fireEvent.click(screen.getByText('Save Shipping Items'));
    await waitFor(() => {
      expect(mockSaveShippingInfoItem);
    });
  });

  it('handles save shipping boxes button click', async () => {
    const mockSaveShippingInfoBox = vi.fn();
    vi.mocked(
      useSaveAdditionalInfoShippingInfoBoxMutation as any
    ).mockReturnValueOnce([mockSaveShippingInfoBox, { isLoading: false }]);
    render(
      <Provider store={mockStore}>
        <ShippingInfo setOpen={mockSetOpen} shippingManager={true} />
      </Provider>
    );
    fireEvent.click(screen.getByText('Save Shipping Boxes'));
    await waitFor(() => {
      expect(mockSaveShippingInfoBox);
    });
  });

  it('handles cancel button click', () => {
    render(
      <Provider store={mockStore}>
        <ShippingInfo setOpen={mockSetOpen} shippingManager={true} />
      </Provider>
    );
    expect(mockSetOpen);
  });
});
