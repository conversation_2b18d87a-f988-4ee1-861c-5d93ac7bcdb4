import TrailingIcon from '@/assets/icons/TrailingIcon';
import AppButton from '@/components/common/app-button';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import AppSpinner from '@/components/common/app-spinner';
import BreadcrumbDialogRenderer from '@/components/common/BreadcrumbDialogRenderer';
import DataTable from '@/components/common/data-tables';
import DatePicker from '@/components/forms/date-picker';
import InputField from '@/components/forms/input-field';
import NumberInputField from '@/components/forms/number-input-field';
import SelectWidget from '@/components/forms/select';
import Text<PERSON>reaField from '@/components/forms/text-area';
import { ADDTIONAL_INFO, selectYesNo } from '@/constants/order-constants';
import {
  cn,
  DATE_FORMAT_YYYYMMDD,
  formatDate,
  getQueryParam,
} from '@/lib/utils';
import {
  useGetAdditionalInfoQuery,
  useLazyGetAdditionalInfoAssignJobQuery,
  useSaveOrderAdditionalInfoForCrewMutation,
} from '@/redux/features/orders/additional-info.api';
import { useGetOrderByIdQuery } from '@/redux/features/orders/order.api';
import {
  AdditionalInfoOrderInfoTypes,
  AdditionalInfoSubRentalInfoTypes,
  AdditionalInfoTypes,
  AssignJobData,
  OrderInformationTypes,
  ORDERT_TYPE_TAB,
} from '@/types/order.types';
import { ColumnDef } from '@tanstack/react-table';
import dayjs from 'dayjs';
import { BookText, Box, CircleAlert, UserRoundCheck } from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { SubmitHandler, useFormContext, UseFormReturn } from 'react-hook-form';
import { OpenDialogType } from '../constants';
import AdditionalOptions from './additional-options';
import AssignJobs from './assign-jobs';
import AssignOrders from './assign-orders';
import Breakdown from './breakdown';
import DismantleCrewDetails from './crew-details/DismantleCrewDetails';
import InstallationCrewDetails from './crew-details/InstallationCrewDetails';
import ShippingInfo from './shipping-info';

interface AdditionalInfoProps {
  form: UseFormReturn<AdditionalInfoTypes>;
  shippingManager: boolean;
}

export default function AdditionalInfo({
  form,
  shippingManager,
}: AdditionalInfoProps) {
  const id = getQueryParam('id') as string;
  const [crewDetailsOpen, setCrewDetailsOpen] = useState(false);
  const [activeCrewButton, setActiveCrewButton] = useState('');
  const formInformation = useFormContext<OrderInformationTypes>();
  const orderInfo = form.watch('orderInfo');
  const subRentInfo = form.watch('subRentInfo');
  const activeTabOrderType = formInformation.getValues('selectedOrderType');
  // Save Additional Info
  const [saveAdditionalInfo] = useSaveOrderAdditionalInfoForCrewMutation();

  const [activeTab, setActiveTab] = useState<string>(ADDTIONAL_INFO.ASSIGN_JOB);
  const [open, setOpen] = useState<OpenDialogType>({
    state: false,
    action: '',
  });

  const [checkDuplicateJob, setCheckDuplicateJob] = useState<boolean>(false);
  const [dateWarning, setDateWarning] = useState({ open: false, message: '' });
  const onOpenChange = useCallback(() => {
    setOpen({ state: false, action: '' });
  }, []);

  const handleSetActiveTab = useCallback((tab: string) => {
    setActiveTab(tab ?? '');
  }, []);

  const [trigger] = useLazyGetAdditionalInfoAssignJobQuery();

  const { data: orderData, isLoading: orderIsLoading } = useGetOrderByIdQuery(
    id,
    {
      skip: !id,
      refetchOnMountOrArgChange: true,
    }
  );
  const orderDetails = orderData?.data;
  const customerId = orderDetails?.billTo?.customerId;

  form.setValue('customerid', customerId && String(customerId));

  const {
    data: defaultAdditionalInfoData,
    isLoading: isLoadingAdditionalInfoData,
  } = useGetAdditionalInfoQuery(
    { orderId: id },
    {
      skip: !id,
      refetchOnMountOrArgChange: true,
    }
  );
  let additionalInfoData = defaultAdditionalInfoData?.data;

  const calculateTotalOfOrder = (
    orderInfoDataList: AdditionalInfoOrderInfoTypes[]
  ) => {
    if (Array.isArray(orderInfoDataList)) {
      const total = orderInfoDataList?.reduce(
        (total, order) => (total += order?.amount),
        0
      );
      // form.setValue('orderTotal', total);
      return total.toFixed(2);
    }
    return 0;
  };

  const calculateTotalOfSubrental = (
    subRentalDataList: AdditionalInfoSubRentalInfoTypes[]
  ) => {
    if (Array.isArray(subRentalDataList)) {
      const total = subRentalDataList?.reduce(
        (total, subrental) => (total += subrental?.amount),
        0
      );
      // form.setValue('subRentalTotal', total);
      return total.toFixed(2);
    }
    return 0;
  };

  const columnsOrder: ColumnDef<AdditionalInfoOrderInfoTypes>[] = useMemo(
    () => [
      {
        accessorKey: 'orderId',
        header: 'Order #',
      },
      {
        accessorKey: 'amount',
        header: 'Total',
      },
    ],
    []
  );

  const columnsSubRental: ColumnDef<AdditionalInfoSubRentalInfoTypes>[] =
    useMemo(
      () => [
        {
          accessorKey: 'id',
          header: 'S/R #',
        },
        {
          accessorKey: 'amount',
          header: 'Total',
        },
      ],
      []
    );

  // tab list
  const listItems = useMemo(() => {
    const tabList = [
      {
        label: 'Assign Job',
        value: ADDTIONAL_INFO.ASSIGN_JOB,
        content: <AssignJobs setOpen={setOpen} additionalForm={form} />,
      },
      {
        label: 'Assign Orders',
        value: ADDTIONAL_INFO.ASSIGN_ORDERS,
        content: <AssignOrders setOpen={setOpen} additionalForm={form} />,
      },
      {
        label: 'Labor Cost Breakdown',
        value: ADDTIONAL_INFO.BREAKDOWN,
        content: <Breakdown setOpen={setOpen} />,
      },
      {
        label: 'Shipping Info',
        value: ADDTIONAL_INFO.SHIPPING_INFO,
        content: (
          <ShippingInfo setOpen={setOpen} shippingManager={shippingManager} />
        ),
      },
      {
        label: 'Additional Options',
        value: ADDTIONAL_INFO.ADDITIONAL_OPRIONS,
        content: <AdditionalOptions setOpen={setOpen} />,
      },
    ];
    return tabList?.filter((tab) => [activeTab]?.includes(tab?.value));
  }, [activeTab, form, shippingManager]);

  const handleOnBlur = useCallback(
    (event: React.FocusEvent<HTMLInputElement>) => {
      const jobNo = Number(event.target.value) as number;

      const customerid = form.getValues('customerid');
      if (customerid) {
        setTimeout(() => {
          trigger({ customerid }).then((res) => {
            let jobList: AssignJobData[] = res?.data?.data;
            const isMatched = jobList?.find((job) => job?.jobNo === jobNo);
            if (isMatched?.jobNo) {
              form.setValue('jobNo', isMatched?.jobNo as number);
              setCheckDuplicateJob(false);
            } else setCheckDuplicateJob(true);
          });
        }, 1000);
      }
    },
    [form, trigger]
  );

  // Submit handler for Additional info Details Tab
  const additionalInfoSave: SubmitHandler<AdditionalInfoTypes> = async (
    formData
  ) => {
    const {
      orderInfo,
      subRentInfo,
      customerid,
      buildingPermitDate,
      undergroundDate,
      installStartDate,
      installEndDate,
      dismantleStartDate,
      dismantleEndDate,
      install,
      dismantle,
      buildingPermit,
      undergroundLocate,
      cadDrawing,
      isDeleted,
      ...rest
    } = formData;

    const toBoolean = (value: unknown): boolean =>
      typeof value === 'boolean'
        ? value
        : String(value).toLowerCase() === 'true';

    const formatDateIfPresent = (date?: string | Date | null): string | null =>
      date && String(date).trim() !== ''
        ? formatDate(date, DATE_FORMAT_YYYYMMDD)
        : null;

    const formattedDates = {
      buildingPermitDate: formatDateIfPresent(buildingPermitDate),
      undergroundDate: formatDateIfPresent(undergroundDate),
      installStartDate: formatDateIfPresent(installStartDate),
      installEndDate: formatDateIfPresent(installEndDate),
      dismantleStartDate: formatDateIfPresent(dismantleStartDate),
      dismantleEndDate: formatDateIfPresent(dismantleEndDate),
    };

    const booleanFields = {
      install: toBoolean(install),
      dismantle: toBoolean(dismantle),
      buildingPermit: toBoolean(buildingPermit),
      undergroundLocate: toBoolean(undergroundLocate),
      cadDrawing: toBoolean(cadDrawing),
      isDeleted: toBoolean(isDeleted),
    };

    await saveAdditionalInfo({
      orderId: id,
      body: {
        ...rest,
        ...formattedDates,
        ...booleanFields,
        subRentInfo,
        orderInfo,
        customerid,
      },
    });
  };

  useEffect(() => {
    const { data: order } = orderData || {};
    const jobId = order?.orderNo;

    const willCall = order?.willCallOrder || {};
    const ship = order?.shipOrder || {};
    const delivery = order?.deliveryOrder || {};

    const isWillCall = activeTabOrderType === ORDERT_TYPE_TAB.WILL_CALL;
    const isShip = activeTabOrderType === ORDERT_TYPE_TAB.SHIP;

    const defaultDates = {
      pickupDate: willCall.pickupDate || delivery.pickupDate,
      returnDate: willCall.returnDate,
      shipDate: ship.shipDate,
      returnArrivalDate: ship.returnArrivalDate,
      deliveryDate: delivery.deliveryDate,
    };

    const parseValue = (value: any) =>
      typeof value === 'boolean' ? String(value) : value;

    const hasAdditionalInfo =
      additionalInfoData !== undefined &&
      Object.keys(additionalInfoData).length > 0;

    const setDates = (dates: Record<string, any>) => {
      Object.entries(dates).forEach(([key, value]) => {
        form.setValue(key as keyof AdditionalInfoTypes, value);
      });
    };

    if (hasAdditionalInfo && additionalInfoData) {
      Object.entries(additionalInfoData).forEach(([key, value]) => {
        form.setValue(key as keyof AdditionalInfoTypes, parseValue(value));
      });

      if (additionalInfoData.assignedJobNo) {
        form.setValue('jobNo', additionalInfoData.assignedJobNo);
      }

      const shouldUseDefaults =
        !additionalInfoData.arrivalDate && !additionalInfoData.returnShipDate;

      if (shouldUseDefaults) {
        if (isWillCall) {
          setDates({
            arrivalDate:
              additionalInfoData.arrivalDate ?? defaultDates.pickupDate,
            returnShipDate:
              additionalInfoData.returnShipDate ?? defaultDates.returnDate,
            installStartDate:
              additionalInfoData.installStartDate ?? defaultDates.pickupDate,
            installEndDate:
              additionalInfoData.installEndDate ?? defaultDates.pickupDate,
            dismantleStartDate:
              additionalInfoData.dismantleStartDate ?? defaultDates.returnDate,
            dismantleEndDate:
              additionalInfoData.dismantleEndDate ?? defaultDates.returnDate,
          });
        } else if (isShip) {
          setDates({
            arrivalDate:
              additionalInfoData.arrivalDate ?? defaultDates.shipDate,
            returnShipDate:
              additionalInfoData.returnShipDate ??
              defaultDates.returnArrivalDate,
            installStartDate:
              additionalInfoData.installStartDate ?? defaultDates.shipDate,
            installEndDate:
              additionalInfoData.installEndDate ?? defaultDates.shipDate,
            dismantleStartDate:
              additionalInfoData.dismantleStartDate ??
              defaultDates.returnArrivalDate,
            dismantleEndDate:
              additionalInfoData.dismantleEndDate ??
              defaultDates.returnArrivalDate,
          });
        } else {
          setDates({
            arrivalDate:
              additionalInfoData.arrivalDate ?? defaultDates.deliveryDate,
            returnShipDate:
              additionalInfoData.returnShipDate ?? defaultDates.pickupDate,
            installStartDate:
              additionalInfoData.installStartDate ?? defaultDates.deliveryDate,
            installEndDate:
              additionalInfoData.installEndDate ?? defaultDates.deliveryDate,
            dismantleStartDate:
              additionalInfoData.dismantleStartDate ?? defaultDates.pickupDate,
            dismantleEndDate:
              additionalInfoData.dismantleEndDate ?? defaultDates.pickupDate,
          });
        }
      }
    } else {
      [
        'install',
        'dismantle',
        'buildingPermit',
        'undergroundLocate',
        'cadDrawing',
      ].forEach((field) => {
        form.setValue(field as keyof AdditionalInfoTypes, 'false');
      });

      const commonDates = {
        arrivalDate: defaultDates.shipDate,
        returnShipDate: defaultDates.returnArrivalDate,
        installStartDate: defaultDates.shipDate,
        installEndDate: defaultDates.shipDate,
        dismantleStartDate: defaultDates.returnArrivalDate,
        dismantleEndDate: defaultDates.returnArrivalDate,
      };

      setDates(commonDates);
    }

    if (!hasAdditionalInfo && jobId) {
      form.setValue('jobNo', jobId);
    }
  }, [activeTabOrderType, additionalInfoData, form, orderData]);

  const installStartDate = form.watch('installStartDate');
  const installEndDate = form.watch('installEndDate');
  const dismantleStartDate = form.watch('dismantleStartDate');
  const dismantleEndDate = form.watch('dismantleEndDate');

  const handleWarningOnBlur = useCallback(
    (
      date: Date | string | undefined,
      fieldName:
        | 'installStartDate'
        | 'installEndDate'
        | 'dismantleStartDate'
        | 'dismantleEndDate'
    ) => {
      if (!date) return;
      const selectedDate = dayjs(date);
      const installEndDateWatch = dayjs(form.watch('installEndDate'));
      const dismantleEndDateWatch = dayjs(form.watch('dismantleEndDate'));

      if (
        !selectedDate.isValid() ||
        !installEndDateWatch.isValid() ||
        !dismantleEndDateWatch.isValid()
      )
        return;
      if (
        fieldName === 'installStartDate' &&
        selectedDate.isAfter(installEndDateWatch, 'day')
      ) {
        form.setValue('installStartDate', installStartDate);
        setDateWarning({
          open: true,
          message: 'The Start Date cannot be later than the End Date.',
        });
      } else if (
        fieldName === 'installEndDate' &&
        selectedDate.isBefore(form.watch('installStartDate'), 'day')
      ) {
        form.setValue('installEndDate', installEndDate);
        setDateWarning({
          open: true,
          message: 'The End Date cannot be earlier than the Start Date.',
        });
      }
      if (
        fieldName === 'dismantleStartDate' &&
        selectedDate.isAfter(dismantleEndDateWatch, 'day')
      ) {
        form.setValue('dismantleStartDate', dismantleStartDate);
        setDateWarning({
          open: true,
          message: 'The Start Date cannot be later than the End Date.',
        });
      } else if (
        fieldName === 'dismantleEndDate' &&
        selectedDate.isBefore(form.watch('dismantleStartDate'), 'day')
      ) {
        form.setValue('dismantleEndDate', dismantleEndDate);
        setDateWarning({
          open: true,
          message: 'The End Date cannot be earlier than the Start Date.',
        });
      }
    },
    [
      dismantleEndDate,
      dismantleStartDate,
      form,
      installEndDate,
      installStartDate,
    ]
  );

  const handleCrewDetails = async (name: string) => {
    setCrewDetailsOpen(true);
    setActiveCrewButton(name);

    // Save additional info and then open the crew modal
    await form.handleSubmit(additionalInfoSave)();
  };
  return (
    <>
      <div className="flex flex-row pb-8">
        <h3 className="text-xl font-semibold text-text-Default w-[200px] flex items-center">
          Additional Info
        </h3>

        <div className="flex flex-row flex-wrap gap-4">
          <AppButton
            label="Assign Job"
            icon={UserRoundCheck}
            iconClassName="w-4"
            spinnerClass="border-white-500 border-t-transparent animate-spin"
            className="bg-brand-teal-Default hover:bg-brand-teal-hover border border-border-brand-teal-Default min-w-[160px]"
            onClick={() => {
              setOpen({ state: true, action: ADDTIONAL_INFO.ASSIGN_JOB });
              handleSetActiveTab(ADDTIONAL_INFO.ASSIGN_JOB);
            }}
          />
          <AppButton
            label="Assign Orders"
            icon={TrailingIcon}
            iconClassName="w-5"
            spinnerClass="border-white-500 border-t-transparent animate-spin"
            className="bg-white hover:bg-white text-text-Default border border-border-Default min-w-[160px]"
            onClick={() => {
              setOpen({ state: true, action: ADDTIONAL_INFO.ASSIGN_ORDERS });
              handleSetActiveTab(ADDTIONAL_INFO.ASSIGN_ORDERS);
            }}
          />
          <AppButton
            label="Breakdown"
            icon={BookText}
            iconClassName="w-5"
            spinnerClass="border-white-500 border-t-transparent animate-spin"
            className="bg-white hover:bg-white text-text-Default border border-border-Default min-w-[160px]"
            onClick={() => {
              setOpen({ state: true, action: ADDTIONAL_INFO.BREAKDOWN });
              handleSetActiveTab(ADDTIONAL_INFO.BREAKDOWN);
            }}
          />
          <AppButton
            label="Shipping Info"
            icon={Box}
            iconClassName="w-5"
            spinnerClass="border-white-500 border-t-transparent animate-spin"
            className="bg-white hover:bg-white text-text-Default border border-border-Default min-w-[160px]"
            onClick={() => {
              setOpen({ state: true, action: ADDTIONAL_INFO.SHIPPING_INFO });
              handleSetActiveTab(ADDTIONAL_INFO.SHIPPING_INFO);
            }}
          />
          {/* <AppButton
            label="Additional Options"
            icon={LayoutGridIcon}
            iconClassName="w-5"
            spinnerClass="border-white-500 border-t-transparent animate-spin"
            className="bg-white w-fit hover:bg-white text-text-Default border border-border-Default"
            onClick={() => {
              setOpen({
                state: true,
                action: ADDTIONAL_INFO.ADDITIONAL_OPRIONS,
              });
              handleSetActiveTab(ADDTIONAL_INFO.ADDITIONAL_OPRIONS);
            }}
          /> */}
        </div>
      </div>

      <div className="grid grid-cols-3 gap-6 mb-4">
        <NumberInputField
          name="jobNo"
          form={form}
          label="Job #"
          placeholder="______"
          maxLength={6}
          onBlur={handleOnBlur}
        />
        <InputField
          name="description"
          form={form}
          label="Description"
          placeholder="Enter Description"
        />
        <InputField
          name="totallabourcost"
          form={form}
          label="Total Labor Cost"
          placeholder="$______.__"
          disabled
        />
      </div>
      <div className="grid grid-cols-2 gap-6 mb-4">
        <SelectWidget
          name="install"
          form={form}
          placeholder="Select Installation"
          label="Installation"
          isClearable={false}
          optionsList={selectYesNo}
        />
        <DatePicker
          name="arrivalDate"
          form={form}
          label={
            activeTabOrderType === ORDERT_TYPE_TAB.WILL_CALL
              ? 'Pickup Date'
              : activeTabOrderType === ORDERT_TYPE_TAB.SHIP
                ? 'Arrival Date'
                : 'Delivery Date'
          }
          placeholder="Enter Delivery Date"
          disabled
        />
        <InputField
          name="installLeader"
          form={form}
          label="Installation Lead"
          placeholder="Enter Installation Lead"
        />
        <DatePicker
          form={form}
          name="installStartDate"
          label="Start"
          placeholder="Select Date"
          enableInput
          handleBlur={(date) => handleWarningOnBlur(date, 'installStartDate')}
        />
        <div></div>
        <div className="grid grid-cols-1 md:grid-cols-8 gap-4">
          <div className="col-span-7">
            <DatePicker
              form={form}
              name="installEndDate"
              label="End"
              placeholder="Select Date"
              enableInput
              handleBlur={(date) => handleWarningOnBlur(date, 'installEndDate')}
            />
          </div>
          <AppButton
            label={''}
            icon={CircleAlert}
            spinnerClass={'border-white-500 border-t-transparent animate-spin'}
            className="bg-white border-[1px] hover:bg-white text-text-Default border-border-Default mt-8"
            iconClassName="w-5"
            onClick={() => handleCrewDetails('installation')}
            tooltip="Installation Crew Details"
          />
        </div>
        <SelectWidget
          name="dismantle"
          form={form}
          placeholder="Select Dismantle"
          label="Dismantle"
          isClearable={false}
          optionsList={selectYesNo}
        />
        <DatePicker
          name="returnShipDate"
          form={form}
          label={
            activeTabOrderType === ORDERT_TYPE_TAB.WILL_CALL
              ? 'Return Date'
              : activeTabOrderType === ORDERT_TYPE_TAB.SHIP
                ? 'Return Ship Date'
                : 'Pickup Date'
          }
          placeholder="Enter Pickup Date"
          disabled
        />
        <InputField
          name="dismantleLeader"
          form={form}
          label="Dismantle Lead"
          placeholder="Enter Dismantle Lead"
        />
        <DatePicker
          form={form}
          name="dismantleStartDate"
          label="Start"
          placeholder="Select Date"
          enableInput
          handleBlur={(date) => handleWarningOnBlur(date, 'dismantleStartDate')}
        />
        <div></div>
        <div className="grid grid-cols-1 md:grid-cols-8 gap-4">
          <div className="col-span-7">
            <DatePicker
              form={form}
              name="dismantleEndDate"
              label="End"
              placeholder="Select Date"
              enableInput
              handleBlur={(date) =>
                handleWarningOnBlur(date, 'dismantleEndDate')
              }
            />
          </div>
          <AppButton
            label={''}
            icon={CircleAlert}
            spinnerClass={'border-white-500 border-t-transparent animate-spin'}
            className="bg-white border-[1px] hover:bg-white text-text-Default border-border-Default mt-8"
            iconClassName="w-5"
            onClick={() => handleCrewDetails('dismantle')}
            tooltip="Dismantle Crew Details"
          />
        </div>
        <SelectWidget
          name="buildingPermit"
          form={form}
          placeholder="Select Building Permits"
          label="Building Permits"
          isClearable={false}
          optionsList={selectYesNo}
        />
        <DatePicker
          form={form}
          name="buildingPermitDate"
          label="Date Permits Received"
          placeholder="Select Date"
          enableInput
        />
        <SelectWidget
          name="undergroundLocate"
          form={form}
          placeholder="Select Underground Location"
          label="Underground Location"
          isClearable={false}
          optionsList={selectYesNo}
        />
        <DatePicker
          form={form}
          name="undergroundDate"
          label="Date Underground Service Location Performed"
          placeholder="Select Date"
          enableInput
        />
        <div className="col-span-2">
          <TextAreaField
            name="undergroundNo"
            form={form}
            label="Underground Location #"
          />
        </div>
        <SelectWidget
          name="cadDrawing"
          form={form}
          placeholder="Select CAD Drawing"
          label="CAD Drawing"
          isClearable={false}
          optionsList={selectYesNo}
        />
        <InputField
          name="cadDrawingFileLocation"
          form={form}
          label="CAD Drawing File Location"
          placeholder="Enter CAD Drawing File Location"
        />
        <div className="col-span-2">
          <TextAreaField
            name="tentLocation"
            form={form}
            label="Tent Location Information"
          />
        </div>
      </div>
      <div className="flex flex-col md:flex-row gap-6 justify-between border rounded-lg p-4 mb-4 w-full">
        <div className="flex-1 flex flex-col gap-6">
          <h3 className="text-xl font-semibold text-text-Default">
            Order Info
          </h3>
          <div className="w-full">
            <DataTable
              data={form.watch('orderInfo')?.data ?? []}
              columns={[...columnsOrder]}
              noDataPlaceholder="No Data Found."
              enablePagination={false}
              tableClassName="max-h-[200px] w-full"
            />
          </div>
          <div className="flex justify-end">
            Sum ={' '}
            {orderInfo?.totalAmount != null
              ? Number(orderInfo.totalAmount).toFixed(2)
              : calculateTotalOfOrder(orderInfo?.data ?? [])}
          </div>
        </div>

        {/* Divider - Hide on small screens */}
        <div className="hidden md:block w-[1px] border rounded-md bg-gray-100 text-gray-600"></div>

        <div className="flex-1 flex flex-col gap-6">
          <h3 className="text-xl font-semibold text-text-Default">
            Sub-Rental Info
          </h3>
          <div className="w-full">
            <DataTable
              data={form.watch('subRentInfo')?.data ?? []}
              columns={[...columnsSubRental]}
              noDataPlaceholder="No Data Found."
              enablePagination={false}
              tableClassName="max-h-[200px]"
            />
          </div>
          <div className="flex justify-end">
            Sum ={' '}
            {subRentInfo?.totalAmount != null
              ? Number(subRentInfo.totalAmount).toFixed(2)
              : calculateTotalOfSubrental(subRentInfo?.data ?? [])}
          </div>
        </div>
      </div>

      <BreadcrumbDialogRenderer
        activeTab={activeTab}
        isOpen={open.state}
        listItems={listItems}
        onOpenChange={onOpenChange}
        setActiveTab={handleSetActiveTab}
        className={cn(
          'max-w-[95%]',
          open.action === ADDTIONAL_INFO.SHIPPING_INFO
            ? '2xl:max-w-[80%]'
            : open.action === ADDTIONAL_INFO.ADDITIONAL_OPRIONS
              ? '2xl:max-w-[30%]'
              : open.action === ADDTIONAL_INFO.BREAKDOWN
                ? 'max-w-[60%] 2xl:max-w-[40%]'
                : 'max-w-[60%]  2xl:max-w-[45%]'
        )}
        contentClassName={cn(
          open.action === ADDTIONAL_INFO.SHIPPING_INFO
            ? 'h-[500px] 2xl:h-[800px] overflow-y-auto'
            : open.action === ADDTIONAL_INFO.ADDITIONAL_OPRIONS
              ? 'h-[180px] 2xl:h-[150px] overflow-y-auto'
              : 'h-[480px] 2xl:h-[550px] overflow-y-auto'
        )}
      />
      <AppSpinner
        overlay
        isLoading={isLoadingAdditionalInfoData || orderIsLoading}
      />
      {crewDetailsOpen &&
        (activeCrewButton === 'installation' ? (
          <InstallationCrewDetails onClose={() => setCrewDetailsOpen(false)} />
        ) : activeCrewButton === 'dismantle' ? (
          <DismantleCrewDetails onClose={() => setCrewDetailsOpen(false)} />
        ) : null)}
      <AppConfirmationModal
        title={'Warning'}
        description={
          <>
            <div>Job number doesn't exist.</div>
          </>
        }
        open={checkDuplicateJob}
        handleSubmit={() => {
          setCheckDuplicateJob(false);
        }}
        submitLabel="OK"
      />
      <AppConfirmationModal
        title="Warning"
        description={<div>{dateWarning?.message}</div>}
        open={dateWarning?.open}
        handleCancel={() => setDateWarning({ open: false, message: '' })}
        cancelLabel={'Ok'}
      />
    </>
  );
}
