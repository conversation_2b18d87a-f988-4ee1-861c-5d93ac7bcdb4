import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import AssignJobs from './index';
import {
  useLazyGetAdditionalInfoAssignJobQuery,
  useSaveAdditionalInfoAssignJobMutation,
} from '@/redux/features/orders/additional-info.api';
import { UseFormReturn } from 'react-hook-form';
import { AdditionalInfoTypes } from '@/types/order.types';

// Mock the necessary dependencies
vi.mock('@/lib/utils', () => ({
  getQueryParam: vi.fn(() => '123'),
  formatDate: vi.fn((date) => date),
  DEFAULT_FORMAT: 'mock-date-format',
}));

vi.mock('@/redux/features/orders/additional-info.api', () => ({
  useLazyGetAdditionalInfoAssignJobQuery: vi.fn(),
  useSaveAdditionalInfoAssignJobMutation: vi.fn(),
}));

vi.mock('@/components/common/app-button', () => ({
  default: ({ label, onClick, disabled, isLoading }: any) => (
    <button
      onClick={onClick}
      disabled={disabled}
      data-testid={label.toLowerCase()}
    >
      {isLoading ? 'Loading...' : label}
    </button>
  ),
}));

vi.mock('@/components/common/data-tables', () => ({
  default: ({ data, columns, rowSelection, onRowSelectionChange }: any) => (
    <div>
      <table>
        <thead>
          <tr>
            {columns.map((col: any) => (
              <th key={col.header}>{col.header}</th>
            ))}
          </tr>
        </thead>
        <tbody>
          {data?.map((row: any, index: number) => (
            <tr key={index}>
              <td>
                <input
                  type="checkbox"
                  checked={!!rowSelection[index]}
                  onChange={() => {
                    const newSelection = {
                      ...rowSelection,
                      [index]: !rowSelection[index],
                    };
                    onRowSelectionChange(newSelection);
                  }}
                  data-testid={`row-checkbox-${index}`}
                />
                {row.jobNo}
              </td>
              <td>{row.description}</td>
              <td>{row.installationDate}</td>
              <td>{row.dismantleDate}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  ),
}));

describe('AssignJobs Component', () => {
  const mockSetOpen = vi.fn();
  const mockAdditionalForm = {
    getValues: vi.fn((key) => {
      if (key === 'customerid') return 'cust-123';
      if (key === 'jobNo') return 'existing-job';
      if (key === 'assignedJobNo') return 'assigned-job';
      return null;
    }),
    setValue: vi.fn(),
  } as unknown as UseFormReturn<AdditionalInfoTypes>;

  const mockJobList = [
    {
      jobNo: 'JOB-001',
      description: 'Test Job 1',
      installationDate: '2023-01-01',
      dismantleDate: '2023-12-31',
    },
    {
      jobNo: 'JOB-002',
      description: 'Test Job 2',
      installationDate: '2023-02-01',
      dismantleDate: '2023-11-30',
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    (useLazyGetAdditionalInfoAssignJobQuery as any).mockReturnValue([
      vi.fn(),
      {
        data: { data: mockJobList },
        isLoading: false,
      },
    ]);

    (useSaveAdditionalInfoAssignJobMutation as any).mockReturnValue([
      vi.fn().mockResolvedValue({ data: {} }),
      { isLoading: false },
    ]);
  });

  it('renders correctly with job list', async () => {
    render(
      <AssignJobs setOpen={mockSetOpen} additionalForm={mockAdditionalForm} />
    );
    expect(
      screen.getByText('Choose a job to assign this order to')
    ).toBeInTheDocument();
    await waitFor(() => {
      expect(screen.getByText('JOB-001')).toBeInTheDocument();
      expect(screen.getByText('Test Job 1')).toBeInTheDocument();
      expect(screen.getByText('JOB-002')).toBeInTheDocument();
      expect(screen.getByText('Test Job 2')).toBeInTheDocument();
    });
    expect(screen.getByText('Job #')).toBeInTheDocument();
    expect(screen.getByText('Description')).toBeInTheDocument();
    expect(screen.getByText('Installation')).toBeInTheDocument();
    expect(screen.getByText('Dismantle')).toBeInTheDocument();
  });

  it('fetches jobs when customerid is available', async () => {
    const mockTrigger = vi.fn();
    (useLazyGetAdditionalInfoAssignJobQuery as any).mockReturnValue([
      mockTrigger,
      {
        data: { data: mockJobList },
        isLoading: false,
      },
    ]);
    render(
      <AssignJobs setOpen={mockSetOpen} additionalForm={mockAdditionalForm} />
    );
    await waitFor(() => {
      expect(mockTrigger).toHaveBeenCalledWith({ customerid: 'cust-123' });
    });
  });

  it('handles row selection', async () => {
    render(
      <AssignJobs setOpen={mockSetOpen} additionalForm={mockAdditionalForm} />
    );
    await waitFor(() => {
      const checkbox = screen.getByTestId('row-checkbox-0');
      fireEvent.click(checkbox);
    });
    const okButton = screen.getByTestId('ok');
    expect(okButton).not.toBeDisabled();
  });

  it('handles cancel action', async () => {
    render(
      <AssignJobs setOpen={mockSetOpen} additionalForm={mockAdditionalForm} />
    );
    const cancelButton = screen.getByTestId('cancel');
    fireEvent.click(cancelButton);

    expect(mockSetOpen).toHaveBeenCalledWith({ state: false, action: '' });
  });

  it('disables OK button when no row is selected', async () => {
    render(
      <AssignJobs setOpen={mockSetOpen} additionalForm={mockAdditionalForm} />
    );
    const okButton = screen.getByTestId('ok');
    expect(okButton).toBeDisabled();
  });

  it('shows loading state when saving', async () => {
    (useSaveAdditionalInfoAssignJobMutation as any).mockReturnValue([
      vi.fn(),
      { isLoading: true },
    ]);
    render(
      <AssignJobs setOpen={mockSetOpen} additionalForm={mockAdditionalForm} />
    );
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('shows loading state when fetching jobs', async () => {
    (useLazyGetAdditionalInfoAssignJobQuery as any).mockReturnValue([
      vi.fn(),
      { isLoading: true },
    ]);
    render(
      <AssignJobs setOpen={mockSetOpen} additionalForm={mockAdditionalForm} />
    );
    expect(screen.queryByText('JOB-001')).not.toBeInTheDocument();
  });
});
