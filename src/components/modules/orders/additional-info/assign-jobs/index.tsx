import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { ColumnDef, RowSelectionState } from '@tanstack/react-table';

import AppButton from '@/components/common/app-button';
import DataTable from '@/components/common/data-tables';
import { DEFAULT_FORMAT, formatDate, getQueryParam } from '@/lib/utils';
import {
  useLazyGetAdditionalInfoAssignJobQuery,
  useSaveAdditionalInfoAssignJobMutation,
} from '@/redux/features/orders/additional-info.api';
import { AdditionalInfoTypes, AssignJobData } from '@/types/order.types';
import { UseFormReturn } from 'react-hook-form';

interface AssignTypes {
  state: boolean;
  action: string;
}

interface AssignJobsProps {
  setOpen: React.Dispatch<React.SetStateAction<AssignTypes>>;
  additionalForm: UseFormReturn<AdditionalInfoTypes>;
}

const AssignJobs = ({ setOpen, additionalForm }: AssignJobsProps) => {
  const orderid = getQueryParam('id');
  const [selectedRows, setSelectedRows] = useState<RowSelectionState>({});

  const selectedJobId = useMemo(() => {
    return +Object.keys(selectedRows)?.[0] + 1;
  }, [selectedRows]);

  const customerid = additionalForm.getValues('customerid');

  const [
    trigger,
    { data: additionalJobInfoResponse, isLoading: isFetchingJobs },
  ] = useLazyGetAdditionalInfoAssignJobQuery();

  let jobList: AssignJobData[] = additionalJobInfoResponse?.data;

  const [saveJobMutation, { isLoading: isSavingJob }] =
    useSaveAdditionalInfoAssignJobMutation();

  const columns: ColumnDef<AssignJobData>[] = useMemo(
    () => [
      {
        accessorKey: 'jobNo',
        header: 'Job #',
        size: 80,
        enableSorting: true,
      },
      {
        accessorKey: 'description',
        header: 'Description',
        maxSize: 250,
        enableSorting: true,
      },
      {
        accessorKey: 'installationDate',
        header: 'Installation',
        maxSize: 250,
        enableSorting: true,
        cell: ({ row }) => {
          return formatDate(row?.original?.installationDate, DEFAULT_FORMAT);
        },
      },
      {
        accessorKey: 'dismantleDate',
        header: 'Dismantle',
        maxSize: 250,
        enableSorting: true,
        cell: ({ row }) => {
          return formatDate(row?.original?.dismantleDate, DEFAULT_FORMAT);
        },
      },
    ],
    []
  );

  const handleConfirm = useCallback(() => {
    const selectedJob = jobList.find(
      (__, index: number) => index + 1 === Number(selectedJobId)
    );

    const finalJob = selectedJob;

    if (finalJob) {
      const jobno = finalJob.jobNo || additionalForm.getValues('jobNo');
      const assignjobno = additionalForm.getValues('assignedJobNo');
      saveJobMutation({
        orderid,
        jobno: assignjobno,
        assignjobno: jobno,
      })
        .unwrap()
        .then((__) => {
          setSelectedRows({});
          setOpen({ state: false, action: '' });
        });
      additionalForm.setValue('jobNo', finalJob.jobNo);
    }
  }, [
    jobList,
    selectedJobId,
    additionalForm,
    saveJobMutation,
    orderid,
    setOpen,
  ]);

  const handleCancel = useCallback(() => {
    setSelectedRows({});
    setOpen({ state: false, action: '' });
  }, [setOpen]);

  useEffect(() => {
    if (customerid) {
      trigger({ customerid });
    }
  }, [trigger, customerid]);

  return (
    <div className="mb-5">
      <h1 className="mb-5">Choose a job to assign this order to</h1>
      <DataTable
        data={jobList ?? []}
        columns={columns}
        enablePagination={false}
        tableClassName="max-h-[300px] overflow-y-auto"
        enableRowSelection
        rowSelection={selectedRows}
        onRowSelectionChange={setSelectedRows}
        bindingKey="jobId"
        isLoading={isFetchingJobs}
      />

      <div className="flex justify-end gap-4 fixed bottom-0 right-6 bg-white pb-4">
        <AppButton
          label="OK"
          onClick={handleConfirm}
          disabled={!selectedJobId}
          className="w-28"
          isLoading={isSavingJob}
        />
        <AppButton
          label="Cancel"
          onClick={handleCancel}
          className="w-28"
          variant="neutral"
        />
      </div>
    </div>
  );
};

export default AssignJobs;
