import AppButton from '@/components/common/app-button';
import DataTable from '@/components/common/data-tables';
import { DEFAULT_FORMAT, formatDate, getQueryParam } from '@/lib/utils';
import {
  useLazyGetAdditionalInfoAssignOrdersQuery,
  useSaveAdditionalInfoAssignOrdersMutation,
} from '@/redux/features/orders/additional-info.api';
import { AdditionalInfoTypes, OrderInfo } from '@/types/order.types';
import { ColumnDef, RowSelectionState } from '@tanstack/react-table';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { UseFormReturn } from 'react-hook-form';

interface AssignTypes {
  state: boolean;
  action: string;
}

interface AssignOrdersProps {
  setOpen: React.Dispatch<React.SetStateAction<AssignTypes>>;
  additionalForm: UseFormReturn<AdditionalInfoTypes>;
}

const AssignOrders = ({ setOpen, additionalForm }: AssignOrdersProps) => {
  const orderid = getQueryParam('id');
  const [selectedRows, setSelectedRows] = useState<RowSelectionState>({});
  const [previousOrders, setPreviousOrders] = useState<OrderInfo[]>([]);

  const selectedOrderIds = useMemo(() => {
    return Object.keys(selectedRows).map(Number);
  }, [selectedRows]);

  const customerid = additionalForm.getValues('customerid');

  const [
    trigger,
    { data: additionalOrderInfoResponse, isLoading: isFetchingOrders },
  ] = useLazyGetAdditionalInfoAssignOrdersQuery();

  let ordersList: OrderInfo[] = additionalOrderInfoResponse?.data;

  // Preserve selected rows when data changes
  useEffect(() => {
    if (
      Array.isArray(ordersList) &&
      ordersList.length > 0 &&
      previousOrders.length === 0
    ) {
      setPreviousOrders(ordersList);
    }
  }, [ordersList, previousOrders]);

  const [saveOrderMutation, { isLoading: isSavingOrder }] =
    useSaveAdditionalInfoAssignOrdersMutation();

  const handleConfirm = useCallback(() => {
    // Get the actual selected orders from the current data
    const selectedOrders = ordersList
      .filter((_, index) => selectedRows[index] === true)
      ?.map((ele) => ele?.orderNo);

    const jobno = additionalForm.getValues('jobNo');
    if (selectedOrders.length) {
      saveOrderMutation({
        body: {
          orderIds: selectedOrders,
        },
        orderid,
        jobno,
      })
        .unwrap()
        .then((__) => {
          setSelectedRows({});
          setOpen({ state: false, action: '' });
        });
    }
  }, [
    additionalForm,
    orderid,
    ordersList,
    saveOrderMutation,
    selectedRows,
    setOpen,
  ]);

  const handleCancel = useCallback(() => {
    setSelectedRows({});
    setOpen({ state: false, action: '' });
  }, [setOpen]);

  const columns: ColumnDef<OrderInfo>[] = useMemo(() => {
    return [
      {
        accessorKey: 'orderNo',
        header: 'Order #',
        size: 80,
        enableSorting: true,
      },
      {
        accessorKey: 'dateOfUseFrom',
        header: 'Date Of Use',
        maxSize: 150,
        enableSorting: true,
        cell: ({ row }) => {
          return formatDate(row?.original?.dateOfUseFrom, DEFAULT_FORMAT);
        },
      },
      {
        accessorKey: 'eventDescription',
        header: 'Event Description',
        maxSize: 250,
        enableSorting: true,
      },
    ];
  }, []);

  useEffect(() => {
    if (customerid) {
      trigger({ customerid });
    }
  }, [trigger, customerid]);

  return (
    <div className="mb-5">
      <h1 className="mb-5">Select other orders to assign to this job</h1>
      <DataTable
        data={ordersList ?? []}
        columns={columns}
        enablePagination={false}
        enableRowSelection
        rowSelection={selectedRows}
        onRowSelectionChange={setSelectedRows}
        enableMultiRowSelection
        isLoading={isFetchingOrders}
        tableClassName="max-h-[300px] overflow-y-auto"
      />

      <div className="flex justify-end gap-4 fixed bottom-0 right-6 bg-white pb-4">
        <AppButton
          label="OK"
          onClick={handleConfirm}
          disabled={!selectedOrderIds.length}
          className="w-28"
          isLoading={isSavingOrder}
        />
        <AppButton
          label="Cancel"
          onClick={handleCancel}
          className="w-28"
          variant="neutral"
        />
      </div>
    </div>
  );
};

export default AssignOrders;
