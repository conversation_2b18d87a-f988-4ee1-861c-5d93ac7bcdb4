import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import AssignOrders from './index';
import { useForm } from 'react-hook-form';
import {
  useLazyGetAdditionalInfoAssignOrdersQuery,
  useSaveAdditionalInfoAssignOrdersMutation,
} from '@/redux/features/orders/additional-info.api';
import { AdditionalInfoTypes } from '@/types/order.types';

// Create a mock that matches the AdditionalInfoTypes structure
const mockAdditionalInfo: AdditionalInfoTypes = {
  customerid: 123,
  orderId: 'order-123',
  jobNo: 456,
  jobTitle: 'Test Job',
  jobManager: '<PERSON>',
  insuranceValue: 1000,
  siteInfo: 'Test Site',
  asInstall: 'Test Install',
  asTeardown: 'Test Teardown',
  description: 'Test Description',
  installLeader: 1,
  leadtype: 'Test Lead',
  buildingPermit: true,
  buildingPermitDate: '2023-01-01',
  cadDrawing: true,
  cadDrawingFileLocation: '/test/location',
  tentLocation: 'Test Location',
  undergroundLocate: true,
  undergroundDate: '2023-01-01',
  undergroundNo: '123',
  install: true,
  installStartDate: '2023-01-01',
  installEndDate: '2023-01-02',
  dismantleLeader: 2,
  dismantleStartDate: '2023-01-03',
  dismantleEndDate: '2023-01-04',
  totallabourcost: 5000,
  arrivalDate: '2023-01-01',
  returnShipDate: '2023-01-05',
  id: 1,
  isDeleted: false,
  buildingPermits: 'Test Permits',
  undergroundLocationDesc: 'Test Underground',
  dismantle: true,
  assignedJobNo: 789,
  orderInfo: {
    data: [],
    totalAmount: 0,
  },
  subRentInfo: {
    data: [],
    totalAmount: 0,
  },
};

// Mock dependencies
vi.mock('react-hook-form', () => ({
  useForm: vi.fn(() => ({
    control: {},
    formState: {},
    handleSubmit: vi.fn(),
    getValues: vi.fn((key?: keyof AdditionalInfoTypes) =>
      key ? mockAdditionalInfo[key] : mockAdditionalInfo
    ),
    setValue: vi.fn(),
    watch: vi.fn((key?: keyof AdditionalInfoTypes) =>
      key ? mockAdditionalInfo[key] : mockAdditionalInfo
    ),
  })),
}));

vi.mock('@/lib/utils', () => ({
  getQueryParam: vi.fn(() => '123'),
  formatDate: vi.fn((date) => `formatted-${date}`),
  DEFAULT_FORMAT: 'MM/dd/yyyy',
}));

// Create a proper mock for the mutation
const mockUnwrap = vi.fn();
const mockSaveOrderMutation = vi.fn(() => ({
  unwrap: mockUnwrap,
}));

vi.mock('@/redux/features/orders/additional-info.api', () => ({
  useLazyGetAdditionalInfoAssignOrdersQuery: vi.fn(() => [
    vi.fn(),
    { data: { data: [] }, isLoading: false },
  ]),
  useSaveAdditionalInfoAssignOrdersMutation: vi.fn(() => [
    mockSaveOrderMutation,
    { isLoading: false },
  ]),
}));

vi.mock('@/components/common/app-button', () => ({
  default: ({ label, onClick, disabled, isLoading }: any) => (
    <button
      onClick={onClick}
      disabled={disabled}
      data-testid={`app-button-${label.toLowerCase()}`}
      data-loading={isLoading}
    >
      {label}
    </button>
  ),
}));

vi.mock('@/components/common/data-tables', () => ({
  default: ({
    data,
    columns,
    rowSelection,
    onRowSelectionChange,
    isLoading,
  }: any) => (
    <div data-testid="data-table">
      <div data-testid="table-data">{JSON.stringify(data)}</div>
      <div data-testid="table-columns">{JSON.stringify(columns)}</div>
      <input
        type="checkbox"
        data-testid="row-selector"
        checked={Object.keys(rowSelection).length > 0}
        onChange={(e) =>
          onRowSelectionChange(e.target.checked ? { 0: true } : {})
        }
      />
      {isLoading && <div>Loading...</div>}
    </div>
  ),
}));

describe('AssignOrders', () => {
  const mockSetOpen = vi.fn();
  const mockAdditionalForm = useForm<AdditionalInfoTypes>();

  const mockOrders = [
    {
      orderNo: 'order-1',
      dateOfUseFrom: '2023-01-01',
      eventDescription: 'Test Event 1',
    },
    {
      orderNo: 'order-2',
      dateOfUseFrom: '2023-02-01',
      eventDescription: 'Test Event 2',
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    mockUnwrap.mockResolvedValue({});
    (useLazyGetAdditionalInfoAssignOrdersQuery as any).mockReturnValue([
      vi.fn(),
      { data: { data: mockOrders }, isLoading: false },
    ]);
    (useSaveAdditionalInfoAssignOrdersMutation as any).mockReturnValue([
      mockSaveOrderMutation,
      { isLoading: false },
    ]);
  });

  it('renders correctly with initial state', () => {
    render(
      <AssignOrders setOpen={mockSetOpen} additionalForm={mockAdditionalForm} />
    );
    expect(
      screen.getByText('Select other orders to assign to this job')
    ).toBeInTheDocument();
    expect(screen.getByTestId('data-table')).toBeInTheDocument();
    expect(screen.getByTestId('app-button-ok')).toBeInTheDocument();
    expect(screen.getByTestId('app-button-cancel')).toBeInTheDocument();
  });

  it('fetches orders when customerid is available', () => {
    const mockTrigger = vi.fn();
    (useLazyGetAdditionalInfoAssignOrdersQuery as any).mockReturnValue([
      mockTrigger,
      { data: { data: mockOrders }, isLoading: false },
    ]);
    render(
      <AssignOrders setOpen={mockSetOpen} additionalForm={mockAdditionalForm} />
    );
    expect(mockTrigger).toHaveBeenCalledWith({ customerid: 123 });
  });

  it('disables OK button when no rows are selected', () => {
    render(
      <AssignOrders setOpen={mockSetOpen} additionalForm={mockAdditionalForm} />
    );
    const okButton = screen.getByTestId('app-button-ok');
    expect(okButton).toBeDisabled();
  });

  it('enables OK button when rows are selected', async () => {
    render(
      <AssignOrders setOpen={mockSetOpen} additionalForm={mockAdditionalForm} />
    );
    const checkbox = screen.getByTestId('row-selector');
    fireEvent.click(checkbox);
    const okButton = screen.getByTestId('app-button-ok');
    await waitFor(() => expect(okButton).not.toBeDisabled());
  });

  it('calls setOpen when Cancel is clicked', () => {
    render(
      <AssignOrders setOpen={mockSetOpen} additionalForm={mockAdditionalForm} />
    );
    const cancelButton = screen.getByTestId('app-button-cancel');
    fireEvent.click(cancelButton);
    expect(mockSetOpen).toHaveBeenCalledWith({ state: false, action: '' });
  });

  it('shows loading state when fetching orders', () => {
    (useLazyGetAdditionalInfoAssignOrdersQuery as any).mockReturnValue([
      vi.fn(),
      { data: undefined, isLoading: true },
    ]);
    render(
      <AssignOrders setOpen={mockSetOpen} additionalForm={mockAdditionalForm} />
    );
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('shows loading state when saving orders', () => {
    (useSaveAdditionalInfoAssignOrdersMutation as any).mockReturnValue([
      mockSaveOrderMutation,
      { isLoading: true },
    ]);
    render(
      <AssignOrders setOpen={mockSetOpen} additionalForm={mockAdditionalForm} />
    );
    const checkbox = screen.getByTestId('row-selector');
    fireEvent.click(checkbox);
    const okButton = screen.getByTestId('app-button-ok');
    expect(okButton.getAttribute('data-loading')).toBe('true');
  });

  it('renders correct columns configuration', () => {
    render(
      <AssignOrders setOpen={mockSetOpen} additionalForm={mockAdditionalForm} />
    );
    const columnsText = screen.getByTestId('table-columns').textContent;
    const columns = columnsText ? JSON.parse(columnsText) : [];
    expect(columns).toHaveLength(3);
    expect(columns[0].accessorKey).toBe('orderNo');
    expect(columns[1].accessorKey).toBe('dateOfUseFrom');
    expect(columns[2].accessorKey).toBe('eventDescription');
  });

  it('calls saveOrderMutation with correct data when OK is clicked', async () => {
    render(
      <AssignOrders setOpen={mockSetOpen} additionalForm={mockAdditionalForm} />
    );
    const checkbox = screen.getByTestId('row-selector');
    fireEvent.click(checkbox);
    const okButton = screen.getByTestId('app-button-ok');
    fireEvent.click(okButton);

    await waitFor(() => {
      expect(mockSaveOrderMutation).toHaveBeenCalledWith({
        body: {
          orderIds: ['order-1'],
        },
        orderid: '123',
        jobno: 456,
      });
      expect(mockUnwrap).toHaveBeenCalled();
      expect(mockSetOpen).toHaveBeenCalledWith({ state: false, action: '' });
    });
  });
});
