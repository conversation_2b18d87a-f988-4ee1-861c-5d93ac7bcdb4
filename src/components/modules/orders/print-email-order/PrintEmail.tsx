import AppButton from '@/components/common/app-button';
import DataTable from '@/components/common/data-tables';
import <PERSON>Field from '@/components/forms/radio-field';
import { useGetEnumsListQuery } from '@/redux/features/enums-api/enums-api';
import { useGetPrintFormListQuery } from '@/redux/features/orders/item-details.api';
import { RowSelectionState } from '@tanstack/react-table';
import { File, Mail, Printer, PrinterCheck, Signature } from 'lucide-react';
import { useEffect, useMemo } from 'react';
import { useForm } from 'react-hook-form';

const PrintEmail = ({
  onOpenChange,
  setActiveTab,
  eSign,
  rowSelection,
  setRowSelection,
}: {
  onOpenChange: () => void;
  setActiveTab: React.Dispatch<React.SetStateAction<string>>;
  eSign?: boolean;
  rowSelection: RowSelectionState;
  setRowSelection: React.Dispatch<React.SetStateAction<RowSelectionState>>;
}) => {
  const form = useForm({
    defaultValues: {
      autoSelectedOption: 'DEFINVPKGLIST',
    },
  });

  const autoSelectedOption = form.watch('autoSelectedOption');
  const { data: listData, isLoading } = useGetPrintFormListQuery(
    autoSelectedOption,
    { refetchOnMountOrArgChange: true }
  );

  // Auto Select Options
  const { data: autoSelectionoOptions } = useGetEnumsListQuery({
    name: 'autoselectionoptions',
  });

  const autoSelectedOptions = useMemo(
    () =>
      autoSelectionoOptions?.data?.map((option: any) => ({
        label: option?.autoSelectOptionText,
        value: option?.autoSelectOptionVal,
      })) || [],
    [autoSelectionoOptions?.data]
  );

  // Toolbar configuration
  const toolbarActions = [
    {
      title: 'Print',
      icon: Printer,
      onClick: () => {},
    },
    {
      title: 'Fax',
      icon: PrinterCheck,
      onClick: () => {},
    },
    {
      title: 'Batch E-mail',
      icon: Mail,
      onClick: () => setActiveTab('batch-options'),
    },
    {
      title: 'PDF',
      icon: File,
      onClick: () => {},
    },
    ...(eSign
      ? [
          {
            title: 'E-sign',
            icon: Signature,
            onClick: () => setActiveTab('e-mail'),
            disabled: !(autoSelectedOption === 'ESIGN'),
          },
        ]
      : []),
  ];

  useEffect(() => {
    if (listData && listData?.data) {
      const initialRowSelection: RowSelectionState = {};
      listData.data.forEach((item, index) => {
        if (item.isChecked) {
          initialRowSelection[index] = true;
        }
      });

      setRowSelection(initialRowSelection);
    }
  }, [listData, setRowSelection]);
  return (
    <>
      <div className="flex justify-end gap-3 mb-3 mt-[-10px]">
        {toolbarActions.map(({ title, icon: Icon, onClick, disabled }) => (
          <AppButton
            key={title}
            label=""
            icon={Icon}
            onClick={onClick}
            className="rounded-full p-2 bg-brand-teal-Default text-white hover:bg-brand-teal-secondary"
            tooltip={title}
            disabled={disabled}
          />
        ))}
      </div>
      <DataTable
        columns={[
          {
            accessorKey: 'form',
            header: 'Form',
            size: 150,
          },
        ]}
        data={listData?.data || []}
        isLoading={isLoading}
        enablePagination={false}
        enableRowSelection
        tableClassName="max-h-[300px] 2xl:max-h-[400px] overflow-y-auto"
        rowSelection={rowSelection}
        onRowSelectionChange={setRowSelection}
        enableMultiRowSelection
      />
      <div className="pt-5">
        <RadioField
          label="Auto Select Options"
          form={form}
          name="autoSelectedOption"
          options={autoSelectedOptions}
          optionsPerRow={2}
          rowClassName="grid grid-cols-2"
          className="space-y-1"
        />
      </div>
      <div className="absolute bottom-6 right-6">
        <AppButton
          label="Cancel"
          onClick={onOpenChange}
          className="w-28"
          variant="neutral"
        />
      </div>
    </>
  );
};

export default PrintEmail;
