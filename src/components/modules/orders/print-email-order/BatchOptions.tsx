import AppButton from '@/components/common/app-button';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import CheckboxField from '@/components/forms/checkbox';
import RadioField from '@/components/forms/radio-field';
import { CircleAlert } from 'lucide-react';
import { useCallback, useState } from 'react';
import { useForm } from 'react-hook-form';

interface BatchOptionProps {
  onOpenChange: () => void;
}

const BatchOption = ({ onOpenChange }: BatchOptionProps) => {
  const [openDialog, setOpenDialog] = useState<boolean>(false);

  const toggleDialog = useCallback(() => {
    setOpenDialog((prevState) => !prevState);
    onOpenChange();
  }, [onOpenChange]);

  const form = useForm({
    defaultValues: {
      autoSelectedOption: 'option2',
    },
  });

  const defaultOptions = [
    { label: 'Order Contact E-mail', value: 'option1' },
    { label: 'Main Customer E-mail', value: 'option2' },
  ];

  return (
    <>
      <div className="flex flex-col gap-2 px-6">
        <RadioField
          label="E-mail To"
          form={form}
          name="autoSelectedOption"
          options={defaultOptions}
          optionsPerRow={1}
          rowClassName="grid grid-cols-2 w-3/4"
          className="space-y-1"
        />
        <div className="mt-4">
          <CheckboxField
            name="orderAlreadyInvoiced"
            control={form.control}
            label="Include order already invoiced"
          />
        </div>

        <div className="flex items-center gap-4 fixed bottom-0 right-6 bg-white mt-3 pb-4 rounded-b-lg">
          <AppButton
            label="Ok"
            className="w-full"
            onClick={form.handleSubmit(toggleDialog)}
          />
          <AppButton
            label="Cancel"
            onClick={onOpenChange}
            variant="neutral"
            className="w-28"
          />
        </div>
      </div>
      <AppConfirmationModal
        title="Confirmation"
        description={
          <div className="flex justify-start items-center space-x-2">
            <CircleAlert className="w-6 h-6" />
            <span>Orders batched for sending.</span>
          </div>
        }
        open={openDialog}
        onOpenChange={toggleDialog}
        handleSubmit={toggleDialog}
        submitLabel="Ok"
      />
    </>
  );
};

export default BatchOption;
