import { RowSelectionState } from '@tanstack/react-table';
import { useCallback, useMemo, useState } from 'react';
import BreadCrumb from '../item-details/kit-item/BreadCrumb';
import BatchOption from './BatchOptions';
import ESignEmail from './e-sign/Email';
import ESign from './e-sign/ESign';
import OnlinePaymentAmount from './e-sign/OnlinePaymentAmount';
import SelectPaymentAmount from './e-sign/SelectPaymentAmount';
import PrintEmail from './PrintEmail';

interface PrintEmailOrderProps {
  open: boolean;
  onOpenChange: () => void;
  eSign?: boolean;
}
export interface ESignRequestDataType {
  signerName?: string;
  signerEmail?: string;
  cc?: string;
  createPaymentLink?: boolean;
}

const PrintEmailOrder = ({
  open,
  onOpenChange,
  eSign,
}: PrintEmailOrderProps) => {
  const [activeTab, setActiveTab] = useState<string>('print-email');
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const handleSetActiveTab = useCallback((tab?: string) => {
    setActiveTab(tab ?? 'print-email');
  }, []);

  const [eSignRequestData, setESignRequestData] =
    useState<ESignRequestDataType>({
      signerName: '',
      signerEmail: '',
      cc: '',
      createPaymentLink: false,
    });

  // Sample tab content for the dialog
  const dialogTabs = useMemo(() => {
    return {
      label: 'Print / Email',
      value: 'print-email',
      content: (
        <PrintEmail
          onOpenChange={onOpenChange}
          setActiveTab={setActiveTab}
          eSign={eSign}
          rowSelection={rowSelection}
          setRowSelection={setRowSelection}
        />
      ),
      children: [
        {
          label: 'Batch Options',
          value: 'batch-options',
          content: <BatchOption onOpenChange={onOpenChange} />,
        },
        {
          label: 'E-mail',
          value: 'e-mail',
          content: (
            <ESignEmail
              onOpenChange={onOpenChange}
              setActiveTab={handleSetActiveTab}
              setESignRequestData={setESignRequestData}
            />
          ),
          children: [
            {
              label: 'E-sign',
              value: 'e-sign',
              content: (
                <ESign
                  esignDetails={eSignRequestData}
                  setActiveTab={handleSetActiveTab}
                  onOpenChange={onOpenChange}
                />
              ),
              children: [
                {
                  label: 'Online Payment Amount',
                  value: 'online-payment-amount',
                  content: (
                    <OnlinePaymentAmount setActiveTab={handleSetActiveTab} />
                  ),
                  children: [
                    {
                      label: 'Select Payment Amount',
                      value: 'select-payment-amount',
                      content: (
                        <SelectPaymentAmount
                          setActiveTab={handleSetActiveTab}
                        />
                      ),
                    },
                  ],
                },
              ],
            },
          ],
        },
      ],
    };
  }, [eSign, eSignRequestData, handleSetActiveTab, onOpenChange, rowSelection]);

  return (
    <BreadCrumb
      treeRoot={dialogTabs}
      activeTab={activeTab}
      isOpen={open}
      onOpenChange={onOpenChange}
      setActiveTab={handleSetActiveTab}
      className="max-w-[70%] 2xl:max-w-[60%]"
      contentClassName="h-[500px] 2xl:h-[650px] overflow-y-auto"
      autoFocus={false}
    />
  );
};

export default PrintEmailOrder;
