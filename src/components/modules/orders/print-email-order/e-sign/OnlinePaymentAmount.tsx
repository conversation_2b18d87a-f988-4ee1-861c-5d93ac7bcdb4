import AppButton from '@/components/common/app-button';
import Input<PERSON>ield from '@/components/forms/input-field';
import NumberInputField from '@/components/forms/number-input-field';
import SelectWidget from '@/components/forms/select';
import ZipCodeInput from '@/components/forms/zipcode-input';
import { generateLabelValuePairs, getQueryParam } from '@/lib/utils';
import { useGetStateByCountryQuery } from '@/redux/features/country/country.api';
import { useSendEsignMutation } from '@/redux/features/e-sign/e-sign.api';
import { DollarSign } from 'lucide-react';
import { useCallback, useMemo } from 'react';
import { useForm, UseFormReturn } from 'react-hook-form';

interface ESignFormType {
  charge: number;
  convenienceFee: number;
  tax: number;
  totalCharge: number;
  description: string;
  type: string;
  name: string;
  address: string;
  city: string;
  state: number;
  zipCode: string;
}

interface OnlinePaymentAmountProps {
  setActiveTab: (value?: string) => void;
}
const OnlinePaymentAmount = ({ setActiveTab }: OnlinePaymentAmountProps) => {
  const orderId = getQueryParam('id') ?? '';

  const [sendESignMail, { isLoading }] = useSendEsignMutation();

  const defaultValues = useMemo(() => {
    const dataValue: any = {};
    return {
      charge: dataValue?.charge ?? '0',
      convenienceFee: dataValue?.convenienceFee ?? '0',
      tax: dataValue?.tax ?? '0',
      totalCharge: dataValue?.totalCharge ?? '0',
      description: dataValue?.description,
      type: dataValue?.type,
      name: dataValue?.name,
      address: dataValue?.address,
      city: dataValue?.city,
      state: dataValue?.state,
      zipCode: dataValue?.zipCode,
    };
  }, []);

  const form: UseFormReturn<ESignFormType> = useForm<ESignFormType>({
    defaultValues,
  });

  const onSubmit = useCallback(
    async (formData: ESignFormType) => {
      try {
        await sendESignMail({ orderId, body: formData })?.unwrap();
        setActiveTab();
      } catch (error) {
        //   console.error('Error submitting form:', error);
      }
    },
    [orderId, sendESignMail, setActiveTab]
  );

  // state data
  const { data: statesData, isFetching: stateIsLoading } =
    useGetStateByCountryQuery({
      countryId: 1,
    });
  // states list
  const stateList = generateLabelValuePairs({
    data: statesData,
    labelKey: 'code',
    valueKey: 'code',
  });

  return (
    <div className="px-4 pt-2">
      <div className="text-right pb-3">
        <AppButton
          label="Select Payment Amount"
          icon={DollarSign}
          iconClassName="w-5 h-5"
          onClick={() => setActiveTab('select-payment-amount')}
        />
      </div>
      <div className="grid grid-cols-2 gap-5">
        <div className="grid grid-cols-2 gap-3 h-fit">
          <div className="col-span-2 font-medium pt-1 pb-6">Payment Info</div>
          <NumberInputField
            name="charge"
            label="Charge"
            placeholder="Charge"
            form={form}
            maxLength={12}
            prefix="$"
            thousandSeparator=","
            fixedDecimalScale
          />
          <NumberInputField
            name="convenienceFee"
            label="Convenience Fee"
            form={form}
            prefix="$"
            thousandSeparator=","
            disabled
            fixedDecimalScale
          />
          <NumberInputField
            name="tax"
            label="Tax"
            form={form}
            prefix="$"
            thousandSeparator=","
            disabled
            fixedDecimalScale
          />
          <NumberInputField
            name="totalCharge"
            label="Total Charge"
            form={form}
            prefix="$"
            thousandSeparator=","
            disabled
            fixedDecimalScale
          />
          <InputField
            name="description"
            label="Description"
            placeholder="Description"
            form={form}
          />
          <SelectWidget
            name="type"
            label="Type"
            placeholder="Type"
            form={form}
            optionsList={[]}
            menuPosition="absolute"
          />
        </div>
        <div className="grid grid-cols-2 gap-3">
          <div className="col-span-2 font-medium py-1">
            Cardholder Info
            <div className="text-sm font-normal">
              <span className="text-red-400 pr-1">*</span>Missing info may
              result in higher fees
            </div>
          </div>

          <InputField
            name="name"
            label="Name"
            placeholder="Name"
            form={form}
            pClassName="col-span-2"
          />
          <InputField
            name="address"
            label="Address"
            placeholder="Address"
            form={form}
            pClassName="col-span-2"
          />
          <InputField name="city" label="City" placeholder="City" form={form} />
          <SelectWidget
            name="state"
            form={form}
            placeholder="Select State"
            label="State"
            optionsList={stateList}
            isLoading={stateIsLoading}
            isClearable={false}
            menuPosition="absolute"
            menuPlacement="top"
          />
          <ZipCodeInput
            name="zipCode"
            isUSA={true}
            form={form}
            label="Zip Code"
          />
        </div>
      </div>
      <div className="flex justify-end gap-4 absolute bottom-5 right-6">
        <AppButton
          label="Submit"
          iconClassName="w-5 h-5"
          className="w-28"
          onClick={form.handleSubmit(onSubmit)}
          isLoading={isLoading}
        />
        <AppButton
          label="Cancel"
          variant="neutral"
          className="w-28"
          onClick={() => setActiveTab('e-sign')}
        />
      </div>
    </div>
  );
};

export default OnlinePaymentAmount;
