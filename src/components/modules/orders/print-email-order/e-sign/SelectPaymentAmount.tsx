import AppButton from '@/components/common/app-button';

const SelectPaymentAmount = ({
  setActiveTab,
}: {
  setActiveTab: (value?: string) => void;
}) => {
  return (
    <div>
      <div className="absolute bottom-6 right-6 flex gap-4">
        <AppButton label="Submit" className="w-28" />
        <AppButton
          label="Cancel"
          variant="neutral"
          className="w-28"
          onClick={() => setActiveTab('online-payment-amount')}
        />
      </div>
    </div>
  );
};

export default SelectPaymentAmount;
