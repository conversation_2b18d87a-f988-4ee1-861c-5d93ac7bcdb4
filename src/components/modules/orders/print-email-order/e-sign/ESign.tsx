import AppButton from '@/components/common/app-button';
import Input<PERSON>ield from '@/components/forms/input-field';
import {
  emailValidation,
  TEXT_VALIDATION_RULE,
} from '@/constants/validation-constants';
import { getQueryParam } from '@/lib/utils';
import { useSendEsignMutation } from '@/redux/features/e-sign/e-sign.api';
import { PlusIcon, Send, X } from 'lucide-react';
import { useCallback, useMemo } from 'react';
import { useForm, UseFormReturn } from 'react-hook-form';
import { ESignRequestDataType } from '..';

interface ESignFormType {
  signerName: string;
  signerEmail: string;
  cc: string;
  paymentLink: string;
  createPaymentLink: boolean;
  formTypes: string[];
}

interface ESignProps {
  setActiveTab: (value?: string) => void;
  esignDetails: ESignRequestDataType;
  onOpenChange: () => void;
}
const ESign = ({ setActiveTab, esignDetails, onOpenChange }: ESignProps) => {
  const orderId = getQueryParam('id') ?? '';

  const [sendESignMail, { isLoading }] = useSendEsignMutation();

  const defaultValues = useMemo(() => {
    return {
      signerName: esignDetails?.signerName,
      signerEmail: esignDetails?.signerEmail,
      cc: esignDetails?.cc,
      // paymentLink: '(None)',
      createPaymentLink: esignDetails?.createPaymentLink,
      formTypes: [
        'Rental Agreement',
        'Credit Card Authorization',
        'Primary Invoice w/Pricing',
      ],
    };
  }, [
    esignDetails?.cc,
    esignDetails?.createPaymentLink,
    esignDetails?.signerEmail,
    esignDetails?.signerName,
  ]);

  const form: UseFormReturn<ESignFormType> = useForm<ESignFormType>({
    defaultValues,
  });

  const onSubmit = useCallback(
    async (formData: ESignFormType) => {
      try {
        await sendESignMail({ orderId, body: formData })?.unwrap();
        setActiveTab();
        onOpenChange();
      } catch (error) {
        //   console.error('Error submitting form:', error);
      }
    },
    [onOpenChange, orderId, sendESignMail, setActiveTab]
  );

  return (
    <div className="px-4 pt-2">
      <div className="grid grid-cols-2 gap-3">
        <InputField
          name="signerName"
          label="Signer Name"
          placeholder="Signer Name"
          form={form}
          validation={TEXT_VALIDATION_RULE}
        />
        <InputField
          name="signerEmail"
          label="Signer Email"
          placeholder="Signer Email"
          form={form}
          validation={emailValidation(true)}
        />
        <InputField
          name="cc"
          label="Cc"
          placeholder="Cc"
          form={form}
          pClassName="col-span-2"
        />
        <div className="col-span-2 flex gap-3">
          <InputField
            name="paymentLink"
            label="Payment Link"
            placeholder="Payment Link"
            form={form}
            disabled
            pClassName="w-full"
          />
          <AppButton
            label=""
            icon={PlusIcon}
            iconClassName="w-5 h-5"
            className="mt-8"
            onClick={() => setActiveTab('online-payment-amount')}
            disabled={true || defaultValues?.createPaymentLink}
            tooltip="Add Payment Link"
          />
          <AppButton
            label=""
            icon={X}
            iconClassName="w-5 h-5"
            className="mt-8"
            variant="neutral"
            tooltip="Clear Payment Link"
            disabled
          />
        </div>
      </div>
      <div className="flex justify-end gap-4 absolute bottom-5 right-6">
        <AppButton
          label="E-mail"
          icon={Send}
          iconClassName="w-5 h-5"
          className="w-28"
          onClick={form.handleSubmit(onSubmit)}
          isLoading={isLoading}
        />
        <AppButton
          label="Cancel"
          variant="neutral"
          className="w-28"
          onClick={() => setActiveTab('e-mail')}
          disabled={isLoading}
        />
      </div>
    </div>
  );
};

export default ESign;
