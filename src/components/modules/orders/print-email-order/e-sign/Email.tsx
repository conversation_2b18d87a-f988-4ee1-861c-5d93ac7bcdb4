import AppButton from '@/components/common/app-button';
import DataTable from '@/components/common/data-tables';
import Checkbox<PERSON>ield from '@/components/forms/checkbox';
import InputField from '@/components/forms/input-field';
import {
  emailValidation,
  REQUIRED_TEXT,
} from '@/constants/validation-constants';
import { getQueryParam } from '@/lib/utils';
import {
  useAddAdditionalCustomerMutation,
  useGetCustomerContactsQuery,
} from '@/redux/features/e-sign/e-sign.api';
import { ColumnDef, RowSelectionState } from '@tanstack/react-table';
import { Mail, PlusIcon, UserRoundPlus } from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useFieldArray, useForm, UseFormReturn } from 'react-hook-form';
import { ESignRequestDataType } from '..';

interface EmailType {
  to?: boolean;
  cc?: boolean;
  contact_id?: number;
  name: string;
  email: string;
  phones?: string[];
  disabledCheckBox: boolean;
}
interface ESignEmailFormTypes {
  onlinePayments: boolean;
  items: EmailType[];
}

interface ESignEmailProps {
  onOpenChange: () => void;
  setActiveTab: (tab?: string) => void;
  setESignRequestData: (value: ESignRequestDataType) => void;
}

const ESignEmail = ({ setActiveTab, setESignRequestData }: ESignEmailProps) => {
  const orderId = getQueryParam('id') ?? '';
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});

  // get customer contact list
  const { data: customerContacts, isLoading } = useGetCustomerContactsQuery(
    orderId,
    { skip: !orderId, refetchOnMountOrArgChange: true }
  );

  const [addAdditionalCustomer, { isLoading: addContactLoading }] =
    useAddAdditionalCustomerMutation();

  const defaultValues = useMemo(() => {
    return {
      onlinePayments: customerContacts?.data?.onlinePayments,
      items: customerContacts?.data?.custContacts?.map((item: EmailType) => ({
        ...item,
        to: false,
        cc: false,
        phones: [],
        disabledCheckBox: true,
      })),
    };
  }, [customerContacts?.data]);

  const form: UseFormReturn<ESignEmailFormTypes> = useForm<ESignEmailFormTypes>(
    {
      defaultValues,
    }
  );

  const { fields, append } = useFieldArray<ESignEmailFormTypes>({
    control: form.control,
    name: 'items',
  });
  const itemsInfo = form.watch('items');

  useEffect(() => {
    if (defaultValues) {
      form.reset(defaultValues);
    }
  }, [defaultValues, form]);

  // selected row
  const selectedRow = useMemo(() => {
    return Object.keys(rowSelection)?.map(Number)?.at(0);
  }, [rowSelection]);

  // selected to mail list
  const selectedToEmailList = itemsInfo?.filter((item) => item?.to);

  // add new contact
  const handleAddNew = useCallback(() => {
    append({
      name: '',
      email: '',
      phones: [],
      disabledCheckBox: false,
    });
  }, [append]);

  // reset error when user unselect the row
  useEffect(() => {
    if (selectedRow === undefined || selectedRow === null || selectedRow) {
      form.clearErrors('items');
    }
  }, [selectedRow, form]);

  const onSubmit = useCallback(
    (action?: 'email') => async (formData: ESignEmailFormTypes) => {
      const customerList = formData?.items;
      if (action) {
        // selected cc mails
        const cc =
          customerList
            ?.filter((item) => item?.cc)
            ?.map((item) => item?.email)
            ?.join(', ') ?? '';
        const matchToCustomer = customerList?.find((item) => item?.to);

        setActiveTab('e-sign');
        setESignRequestData({
          signerName: matchToCustomer?.name,
          signerEmail: matchToCustomer?.email,
          cc,
          createPaymentLink: formData?.onlinePayments,
        });
        return null;
      }
      try {
        const payload = customerList
          ?.filter((_, index) => index === selectedRow)
          ?.at(0);

        await addAdditionalCustomer({ orderId, body: payload }).unwrap();
        setRowSelection({});
      } catch (error) {
        //   console.error('Error submitting form:', error);
      }
    },
    [
      addAdditionalCustomer,
      orderId,
      selectedRow,
      setActiveTab,
      setESignRequestData,
    ]
  );

  // handle selection of to
  const handleToSelection = useCallback(
    (checked: boolean, index: number) => {
      itemsInfo.forEach((_, i) => {
        form.setValue(`items.${i}.to`, i === index ? checked : false);
        i !== index && form.clearErrors('items');
      });
    },
    [form, itemsInfo]
  );

  const columns: ColumnDef<any>[] = useMemo(() => {
    return [
      {
        accessorKey: 'to',
        header: 'To',
        size: 60,
        cell: ({ row }) => (
          <CheckboxField
            control={form.control}
            name={`items.${row.index}.to`}
            className="border-grayScale-400 border-2 w-5 h-5"
            onClick={(checked) =>
              handleToSelection(checked ? true : false, row.index)
            }
          />
        ),
      },
      {
        accessorKey: 'cc',
        header: 'Cc',
        size: 60,
        cell: ({ row }) => (
          <CheckboxField
            control={form.control}
            name={`items.${row.index}.cc`}
            className="border-grayScale-400 border-2 w-5 h-5"
          />
        ),
      },
      {
        accessorKey: 'name',
        header: 'Name',
        maxSize: 250,
        cell: ({ row }) => {
          const isToSelected = form.watch(`items.${row.index}.to`);
          const required = row.index === selectedRow || isToSelected;
          return (
            <InputField
              name={`items.${row.index}.name`}
              form={form}
              placeholder="Name"
              maxLength={128}
              className="h-8"
              pClassName="py-1"
              autoComplete="off-nope"
              validation={{
                required: required ? REQUIRED_TEXT : '',
              }}
            />
          );
        },
      },
      {
        accessorKey: 'email',
        header: 'E-mail',
        maxSize: 250,
        cell: ({ row }) => {
          const isToSelected = form.watch(`items.${row.index}.to`);
          const required = row.index === selectedRow || isToSelected;
          return (
            <InputField
              name={`items.${row.index}.email`}
              form={form}
              placeholder="E-mail"
              maxLength={128}
              className="h-8"
              pClassName="py-1"
              validation={emailValidation(required)}
            />
          );
        },
      },
    ];
  }, [form, handleToSelection, selectedRow]);

  return (
    <div>
      <DataTable
        columns={columns}
        data={fields}
        isLoading={isLoading}
        enableRowSelection
        rowSelection={rowSelection}
        onRowSelectionChange={setRowSelection}
        enablePagination={false}
        tableClassName="max-h-[400px] 2xl:max-h-[500px] overflow-auto"
        loaderRows={6}
      />
      <div className="flex justify-end gap-4 my-4">
        <AppButton
          label="Add as Contact"
          icon={UserRoundPlus}
          iconClassName="w-5 h-5"
          variant="neutral"
          disabled={!selectedRow}
          onClick={form.handleSubmit(onSubmit())}
          isLoading={addContactLoading}
        />
        <AppButton
          label="New Row"
          variant="neutral"
          icon={PlusIcon}
          iconClassName="w-5 h-5"
          onClick={handleAddNew}
        />
      </div>
      <div className="flex justify-end gap-4 absolute bottom-5 right-6">
        <AppButton
          label="E-mail"
          icon={Mail}
          iconClassName="w-5 h-5"
          className="w-28"
          onClick={form.handleSubmit(onSubmit('email'))}
          disabled={!selectedToEmailList?.length}
        />
        <AppButton
          label="Cancel"
          variant="neutral"
          className="w-28"
          onClick={() => setActiveTab()}
        />
      </div>
    </div>
  );
};
export default ESignEmail;
