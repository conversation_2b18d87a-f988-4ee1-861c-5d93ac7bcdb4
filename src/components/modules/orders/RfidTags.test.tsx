import { render, screen } from '@testing-library/react';
import { describe, expect, it } from 'vitest';
import RfidTags from './RfidTags';

describe('RfidTags Component', () => {
  it('should render the component with correct title', () => {
    render(<RfidTags />);
    const titleElement = screen.getByText('RFID Tags');
    expect(titleElement).toBeInTheDocument();
    expect(titleElement).toHaveClass(
      'text-xl',
      'font-semibold',
      'text-text-Default'
    );
  });

  it('should render the DataTable component', () => {
    render(<RfidTags />);
    const dataTable = screen.getByRole('table');
    expect(dataTable).toBeInTheDocument();
  });

  it('should display all column headers correctly', () => {
    render(<RfidTags />);

    const headers = [
      'Item ID',
      'Description',
      'Location',
      'Qty',
      'Tag ID',
      'Returned',
      'Status',
      'last Order',
    ];
    headers.forEach((headerText) => {
      const header = screen.getByText(headerText);
      expect(header).toBeInTheDocument();
    });
  });

  it('should show "No Data Found" when there is no data', () => {
    render(<RfidTags />);
    const noDataPlaceholder = screen.getByText('No Data Found.');
    expect(noDataPlaceholder).toBeInTheDocument();
  });
});
