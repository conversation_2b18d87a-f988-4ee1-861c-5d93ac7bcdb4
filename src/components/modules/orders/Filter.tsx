import AppButton from '@/components/common/app-button';
import { AppTableContext } from '@/components/common/app-data-table/AppTableContext';
import DatePicker from '@/components/forms/date-picker';
import InputField from '@/components/forms/input-field';
import SelectDropDown from '@/components/forms/select-dropdown';
import { Separator } from '@/components/ui/separator';
import { TEXT_VALIDATION_RULE } from '@/constants/validation-constants';
import {
  formatDate,
  generateLabelValuePairs,
  getQueryParam,
  toCapitalize,
  updateQueryParam,
} from '@/lib/utils';
import { useGetStoreLocationsQuery } from '@/redux/features/store/store.api';
import { useGetEnumsListQuery } from '@/redux/features/enums-api/enums-api';
import {
  clearAllFilters,
  setFilter,
  updateFormValues,
} from '@/redux/features/orders/orderSlice';
import { RootState } from '@/redux/store';
import { memo, useCallback, useContext, useEffect } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import {
  dateTypeList,
  FILTER_CUSTOM,
  operatorType,
  rangeFilterList,
  searchByFilters,
} from './constants';

interface FilterFormValues {
  customer: string;
  eventDescription: string;
  total: string | null;
  shipLocation: string | null;
  address: string;
  address2: string;
  town: string;
  state: string;
  zipCode: string | null;
  orderedBy: string | null;
  purchaseOrderNo: string;
  salesperson: string;
  enteredBy: string | null;
  deliveryOption: string | null;
  customerType: string;
  webOrderNo: string;
  location: string | null;
  orderType: string | null;
  operator: string;
  range: string;
  searchBy: string;
  searchValue: string;
  dateOfUseFrom: string;
  dateOfUseThru: string;
  dateType: string;
}

const defaultValues: FilterFormValues = {
  customer: '',
  eventDescription: '',
  total: '',
  shipLocation: '',
  address: '',
  address2: '',
  town: '',
  state: '',
  zipCode: '',
  orderedBy: '',
  purchaseOrderNo: '',
  salesperson: '',
  enteredBy: '',
  deliveryOption: '',
  customerType: '',
  webOrderNo: '',
  location: '',
  orderType: '',
  operator: 'contains',
  range: '',
  searchBy: '',
  searchValue: '',
  dateOfUseFrom: '',
  dateOfUseThru: '',
  dateType: 'dateOfUseFrom',
};

interface FilterProps {
  setIsFilterOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const Filter: React.FC<FilterProps> = ({ setIsFilterOpen }) => {
  const search = getQueryParam('search') as string;
  const formValuesFromRedux = useSelector(
    (state: RootState) => state.orders.formValues
  );
  const dispatch = useDispatch();
  const { pagination, setPagination } = useContext(AppTableContext);
  // Initialize form with values from Redux
  const form = useForm<FilterFormValues>({
    defaultValues: formValuesFromRedux,
    mode: 'onChange',
  });

  // Watch form changes and update Redux state
  useEffect(() => {
    const subscription = form.watch((values) => {
      dispatch(updateFormValues(values));
    });

    return () => subscription.unsubscribe();
  }, [form, dispatch]);

  const { data: storeLocations, isLoading } = useGetStoreLocationsQuery();

  const storeLocationList = generateLabelValuePairs({
    data: storeLocations?.data,
    labelKey: 'location',
    valueKey: 'location',
  });
  const { data: defaultOrderType, isLoading: isdefaultOrderTypeLoading } =
    useGetEnumsListQuery({
      name: 'OrderTypeFilter',
    });

  //OrderTypeFilter
  const onSubmit: SubmitHandler<any> = (data) => {
    const key: any = data.searchBy;
    const filterLabel = searchByFilters?.find(
      ({ value }) => value == key
    )?.label;
    const isDeleted = data.orderType === 'DELETED_ORDER';
    const newFilterData: any[] = [
      {
        label: filterLabel,
        value: data.searchValue,
        name: key,
        tagValue: data.searchValue,
        operator: data.operator,
      },
      {
        label: 'Store Location',
        value: data.location,
        name: 'location',
        tagValue: data.location,
        operator: 'Equals',
      },
      {
        label: 'Order Type',
        value: isDeleted ? 'true' : data.orderType,
        name: isDeleted ? 'isDeleted' : 'orderType',
        tagValue: toCapitalize(data.orderType),
        operator: isDeleted ? 'Equals' : 'Contains',
      },
      {
        label: 'Range',
        value:
          range === FILTER_CUSTOM?.CUSTOM
            ? `${data.dateType},${form.getValues('dateOfUseFrom')},${form.getValues('dateOfUseThru')}`
            : data.range,
        name: range === FILTER_CUSTOM?.CUSTOM ? 'custom' : 'range',
        tagValue:
          range === FILTER_CUSTOM?.CUSTOM
            ? ` ${formatDate(form.getValues('dateOfUseFrom'))} - ${formatDate(form.getValues('dateOfUseThru'))}`
            : rangeFilterList?.find(({ value }) => value == data.range)?.label,
        operator: 'Contains',
      },
    ].filter((orders) => orders.value);

    dispatch(setFilter(newFilterData));
    setIsFilterOpen(false);
    setPagination({
      ...pagination,
      pageIndex: 0,
    });
  };

  const handleClear = () => {
    form.reset(defaultValues); // Reset form to default values
    dispatch(clearAllFilters()); // Clear all filters in Redux
    setIsFilterOpen(false); // Close the filter form
    if (search) {
      updateQueryParam(null, 'search');
    }
  };

  // Track if form values are different from the default values
  const isFormModified =
    JSON.stringify(form.watch()) !== JSON.stringify(defaultValues);

  useEffect(() => {
    if (!form.watch('searchBy')) {
      form.setValue('searchValue', '');
    }
  }, [form]);

  useEffect(() => {
    if (search) {
      form.setValue('searchBy', 'customer');
      form.setValue('searchValue', search);
      form.setValue('operator', 'equals');
    }
  }, [form, search]);

  const onDateChange = useCallback(
    async (
      date: Date | string | undefined,
      name: 'dateOfUseFrom' | 'dateOfUseThru'
    ) => {
      if (name === 'dateOfUseFrom' && (date === '' || !date)) {
        form.setValue('dateOfUseFrom', '');
        form.setValue('dateOfUseThru', '');
      }
    },
    [form]
  );

  const range = form.watch('range');

  return (
    <>
      <div className="px-3">
        <div className="text-normal py-2 font-semibold">Filters</div>
        <Separator
          orientation="horizontal"
          className="h-[1px] bg-border-Default mb-2"
        />
        <div className="grid col-span-1 md:grid-cols-1 gap-3">
          <SelectDropDown
            form={form}
            name="location"
            label="Store Location"
            placeholder="Select Store Location"
            optionsList={storeLocationList}
            isLoading={isLoading}
          />
          <SelectDropDown
            name="orderType"
            label="Order Type"
            placeholder="Select Order Type"
            optionsList={defaultOrderType?.data ?? []}
            isLoading={isdefaultOrderTypeLoading}
            form={form}
          />
          <SelectDropDown
            form={form}
            name="range"
            label="Range"
            placeholder="Select Range"
            optionsList={rangeFilterList}
          />
          {range === FILTER_CUSTOM?.CUSTOM && (
            <div className="grid grid-cols-3 gap-3">
              <div>
                <SelectDropDown
                  form={form}
                  name="dateType"
                  label="Type"
                  placeholder="Select Type"
                  optionsList={dateTypeList}
                  validation={TEXT_VALIDATION_RULE}
                />
              </div>
              <div>
                <DatePicker
                  form={form}
                  name="dateOfUseFrom"
                  label="Start Date"
                  placeholder="Select Date"
                  onDateChange={(data) => onDateChange(data, 'dateOfUseFrom')}
                  validation={TEXT_VALIDATION_RULE}
                  enableInput
                  pClassName="col-span-2"
                  isRenderFirst={true}
                />
              </div>
              <div>
                <DatePicker
                  form={form}
                  name="dateOfUseThru"
                  label="End Date"
                  placeholder="Select Date"
                  onDateChange={(data) => onDateChange(data, 'dateOfUseThru')}
                  validation={TEXT_VALIDATION_RULE}
                  enableInput
                  disablePastDate={form.watch('dateOfUseFrom')}
                  pClassName="col-span-2"
                  isRenderFirst={true}
                />
              </div>
            </div>
          )}
          <div className="grid grid-cols-2 gap-3">
            <SelectDropDown
              form={form}
              name="searchBy"
              label="Search By"
              placeholder="Select Search By"
              onChange={(value) => {
                if (value) {
                  form.setValue('searchValue', '');
                }
              }}
              optionsList={searchByFilters}
            />
            <SelectDropDown
              form={form}
              name="operator"
              label="Operator"
              allowClear={false}
              placeholder="Select Operator"
              optionsList={operatorType}
            />
          </div>
          <InputField
            form={form}
            name="searchValue"
            disabled={!form.watch('searchBy')}
            label="Value"
            placeholder="Enter Event Description"
          />
        </div>
        <div className="w-full h-full flex justify-between gap-3 mt-3 sticky -bottom-2 pt-1 pb-3 bg-white z-30">
          <AppButton
            className="w-full"
            onClick={form.handleSubmit(onSubmit)}
            label="Apply"
            disabled={!isFormModified}
          />
          <AppButton
            className="w-full"
            label="Clear"
            variant="neutral"
            onClick={handleClear}
            disabled={!isFormModified}
          />
        </div>
      </div>
    </>
  );
};

export default memo(Filter);
