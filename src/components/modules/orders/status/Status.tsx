import ActionArea from '@/components/common/action-area';
import AppButton from '@/components/common/app-button';
import AppSpinner from '@/components/common/app-spinner';
import SwitchField from '@/components/common/switch';
import DatePicker from '@/components/forms/date-picker';
import InputField from '@/components/forms/input-field';
import SelectWidget from '@/components/forms/select';
import { QUALITY_TYPES_API_ROUTES } from '@/constants/api-constants';
import { statusInfoTabList } from '@/constants/order-constants';
import useOptionList from '@/hooks/useOptionList';
import { generateLabelValuePairs, getQueryParam } from '@/lib/utils';
import { useSalesPersonQuery } from '@/redux/features/customers/choices.api';
import {
  useGetStatusDetailsQuery,
  useUpdateOrderStatusMutation,
} from '@/redux/features/orders/order.api';
import {
  OrderInformationTypes,
  StatusTypes,
  TabType,
} from '@/types/order.types';
import { BookText } from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useFormContext, UseFormReturn } from 'react-hook-form';

interface StatusProps {
  form: UseFormReturn<StatusTypes>;
}

export default function OrderStatus({ form }: StatusProps) {
  const id = getQueryParam('id') as string;
  const [activeTab, setActiveTab] = useState<TabType | string>(
    'DeliveryStatusInfo'
  );
  const orderForm = useFormContext<OrderInformationTypes>();
  const isDelete = orderForm.watch('isDeleted');
  const isOrderEntryReadOnly = orderForm.watch('orderEntryReadOnly');
  const { data: salespersonData } = useSalesPersonQuery();

  const { data, isLoading } = useGetStatusDetailsQuery(id);
  const [updateOrderStatus, { isLoading: isStatusLoading }] =
    useUpdateOrderStatusMutation();

  const salesPersonList = generateLabelValuePairs({
    data: salespersonData?.data,
    labelKey: 'name',
    valueKey: 'id',
  });

  const { options: qualityTypesList, optionLoading: qualityTypeLoading } =
    useOptionList({
      url: QUALITY_TYPES_API_ROUTES.ALL,
      labelKey: 'qualityDesc',
      valueKey: 'id',
    });

  const handleStatus = async () => {
    await updateOrderStatus(id);
  };

  // handle tab change
  const handleTabChange = useCallback((value: string) => {
    setActiveTab(value);
  }, []);

  const defaultValue = useMemo(() => {
    return {
      ...data?.data,
    };
  }, [data?.data]);

  useEffect(() => {
    if (data?.data) {
      form.reset(defaultValue);
    }
  }, [data?.data, defaultValue, form]);

  return (
    <>
      <div className="flex items-center justify-between gap-3 pb-4">
        <h3 className="text-xl font-semibold text-text-Default">Status</h3>
        <div className="flex items-center gap-x-2">
          <AppButton
            label={
              form.watch('status') === 'INVOICED'
                ? 'Un-Invoice Order'
                : 'Invoice Order'
            }
            isLoading={isStatusLoading}
            icon={BookText}
            iconClassName="w-4"
            spinnerClass={'border-white-500  border-t-transparent animate-spin'}
            className="bg-brand-teal-Default hover:bg-brand-teal-hover border-border-brand-teal-Default mx-1"
            onClick={handleStatus}
            disabled={isDelete || isOrderEntryReadOnly}
          />
        </div>
      </div>
      <div className="grid grid-cols-2 gap-6 border rounded-lg p-4 mb-4">
        <DatePicker
          name="dateOrdered"
          form={form}
          enableInput
          label="Date Ordered"
          placeholder="Select Date"
          disabled={isDelete || isOrderEntryReadOnly}
        />
        <SelectWidget
          name="enteredBy"
          form={form}
          label="Entered By"
          placeholder="Enter Entered By"
          optionsList={salesPersonList}
          disabled={isDelete || isOrderEntryReadOnly}
        />
        <InputField
          name="status"
          form={form}
          label="Order Status"
          placeholder="Enter Order Status"
          disabled
        />
        <div className="grid grid-cols-1 2xl:grid-cols-2 gap-6">
          <div className="mt-9">
            <SwitchField
              label="Signed"
              form={form}
              name="signed"
              disabled={isDelete || isOrderEntryReadOnly}
            />
          </div>
        </div>
        <div className="grid grid-cols-1 2xl:grid-cols-2 gap-6">
          <InputField
            name="deliveryOption"
            form={form}
            label="Delivery Option"
            placeholder="Enter Delivery Option"
            disabled
          />
          <InputField
            name="deliverPickupStatus"
            form={form}
            label="Delivery / Pickup Status"
            placeholder="Enter Status"
            disabled
          />
        </div>
        <div className="grid grid-cols-1 2xl:grid-cols-2 gap-6">
          <div className="mt-9">
            <SwitchField
              label="Approved for Shipment"
              form={form}
              name="approvedForShipment"
              disabled={
                isDelete ||
                isOrderEntryReadOnly ||
                !orderForm.watch('shippingManager')
              }
            />
          </div>
          <SelectWidget
            form={form}
            name="inventoryQuality"
            label="Inventory Quality"
            placeholder="Select Inventory Quality"
            isClearable={false}
            isLoading={qualityTypeLoading}
            optionsList={qualityTypesList}
            disabled={isDelete || isOrderEntryReadOnly}
          />
        </div>
      </div>
      <div className="grid grid-cols-1 gap-6">
        <ActionArea
          tabs={statusInfoTabList(form)}
          defaultValue={activeTab}
          onValueChange={handleTabChange}
          tabsContentClassName="p-4 gap-4 border border-border-Default rounded-lg"
        />
      </div>
      <AppSpinner overlay isLoading={isLoading} />
    </>
  );
}
