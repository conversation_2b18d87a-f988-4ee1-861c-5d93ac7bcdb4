import InputField from '@/components/forms/input-field';
import TextAreaField from '@/components/forms/text-area';
import { StatusTypes } from '@/types/order.types';
import { UseFormReturn } from 'react-hook-form';

interface StatusInfoProps {
  form: UseFormReturn<StatusTypes>;
}

const PickupStatus = ({ form }: StatusInfoProps) => {
  return (
    <div className="grid grid-cols-2 gap-6">
      <InputField
        name={`pickupStatus.vehicle`}
        form={form}
        label="Vehicle"
        placeholder="Enter Vehicle"
        disabled
      />
      <InputField
        name={`pickupStatus.driver`}
        form={form}
        label="Driver"
        placeholder="Enter Driver"
        disabled
      />
      <InputField
        name={`pickupStatus.routeDescription`}
        form={form}
        label="Route Description"
        placeholder="Enter Route Description"
        disabled
      />
      <InputField
        name={`pickupStatus.driverPhone`}
        form={form}
        label="Driver Phone"
        placeholder="Enter Driver Phone"
        disabled
      />
      <InputField
        name={`pickupStatus.startTime`}
        form={form}
        label="Start Time"
        placeholder="Enter Start Time"
        disabled
      />
      <InputField
        name={`pickupStatus.travelTime`}
        form={form}
        label="Travel Time (Hrs)"
        placeholder="Enter Travel Time"
        disabled
      />
      <div className="col-span-2">
        <TextAreaField
          name={`pickupStatus.comment`}
          form={form}
          label="Comment"
          disabled
        />
      </div>
      <div className="col-span-2">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <InputField
            name={`pickupStatus.estimatedDelivery`}
            form={form}
            label="Estimated Delivery"
            placeholder="Enter Estimated Delivery"
            disabled
          />
          <InputField
            name={`pickupStatus.setupTime`}
            form={form}
            label="Setup Time (Hrs)"
            placeholder="Enter Setup Time"
            disabled
          />
          <InputField
            name={`pickupStatus.actualDelivery`}
            form={form}
            label="Actual Delivery"
            placeholder="Enter Actual Delivery"
            disabled
          />
          <InputField
            name={`pickupStatus.actualSetup`}
            form={form}
            label="Actual Setup (Hrs)"
            placeholder="Enter Actual Setup"
            disabled
          />
        </div>
      </div>
    </div>
  );
};

export default PickupStatus;
