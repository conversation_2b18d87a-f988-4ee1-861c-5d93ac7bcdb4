// Components

// Constants
import { DELIVERY_LOCATION_API_ROUTES } from '@/constants/api-constants';

// Hooks and API
import {
  useGetCountryListQuery,
  useGetStateByCountryQuery,
} from '@/redux/features/country/country.api';
import { useAddNewItemMutation } from '@/redux/features/list/category/list.api';

// Types
import { CustomerDetailTypes } from '@/types/customer.types';
import { DeliveryLocationFormType } from '@/types/list.types';

// Utility functions
import AppButton from '@/components/common/app-button';
import CustomDialog from '@/components/common/dialog';
import {
  getDeliveryLocationFormFields,
  renderFormField,
} from '@/constants/list-constants';
import { cn, generateLabelValuePairs } from '@/lib/utils';
import { memo, useCallback, useEffect, useMemo, useState } from 'react';
import { Submit<PERSON><PERSON><PERSON>, useForm } from 'react-hook-form';

type StateTypes = {
  label: string;
  value: string;
};

interface DeliveryLocationsProps {
  open: boolean;
  addNewLocation?: (data: any) => void;
  onCancel?: () => void;
  className?: string;
}
const NewLocations = ({
  open,
  addNewLocation,
  onCancel,
  className,
}: DeliveryLocationsProps) => {
  const [selectedCountry, setSelectedCountry] = useState(1);
  const [stateList, setStateList] = useState<StateTypes[]>([]);

  const { data: countryData = [] } = useGetCountryListQuery();
  const {
    data: statesData = [],
    isFetching: stateIsLoading,
    refetch,
  } = useGetStateByCountryQuery({ countryId: selectedCountry });
  const [newLocation, { isLoading: newItemLoading }] = useAddNewItemMutation();

  const defaultValues = useMemo(() => {
    return {
      country: 'USA',
    };
  }, []);

  const form = useForm<DeliveryLocationFormType>({
    defaultValues,
    mode: 'onChange',
  });

  const handleOnCancel = useCallback(() => {
    onCancel?.();
    form.reset();
  }, [form, onCancel]);

  // Memoized country list
  const countryList = useMemo(
    () =>
      generateLabelValuePairs({
        data: countryData,
        labelKey: 'name',
        valueKey: 'name',
      }),
    [countryData]
  );

  // Effect to reset form when default values change
  useEffect(() => {
    if (defaultValues) form.reset(defaultValues);
  }, [defaultValues, form]);

  const handleCountryChange = (value: string) => {
    form.setValue('country', value);
    const newCountry = countryData.find(
      (element: { name: string }) => element.name === value
    );
    setSelectedCountry(newCountry?.country_id ?? 1); // Set selected country
    form.setValue('state', ''); // Reset state when country changes
    form.setValue('zipcode', '');
    form.clearErrors('zipcode');
  };

  useEffect(() => {
    // Refetch states whenever selectedCountry changes
    if (selectedCountry) {
      refetch();
    }
  }, [selectedCountry, refetch]); // Dependency on selectedCountry and refetch

  // Ensure states data is used to generate state list
  useEffect(() => {
    if (statesData?.length) {
      const newStateList = generateLabelValuePairs({
        data: statesData,
        labelKey: 'code',
        valueKey: 'code',
      });
      setStateList(newStateList);
    }
  }, [statesData]); // Only re-run when statesData changes

  const onSubmit: SubmitHandler<DeliveryLocationFormType> = useCallback(
    async (formData) => {
      try {
        const response = await newLocation({
          url: DELIVERY_LOCATION_API_ROUTES.CREATE,
          data: {
            ...formData,
            customerId: Number(formData.customerId) ?? null,
            deliveryFee: Number(formData?.deliveryFee) ?? null,
          },
        }).unwrap();
        onCancel?.();
        addNewLocation?.(response);
      } catch (error) {}
    },
    [addNewLocation, newLocation, onCancel]
  );

  // Determine if current country is USA for validation
  const isUSA = form.watch('country') === 'USA';

  // New handlers for "Ok" and "Cancel" buttons
  const handleOk = (selectedCustomer: CustomerDetailTypes) => {
    if (selectedCustomer) {
      form.setValue('customerId', Number(selectedCustomer?.customer_id));
      form.setValue(
        'customerName',
        `${selectedCustomer?.first_name} ${selectedCustomer?.last_name ?? ''}`
      );
    }
  };

  const formFields = useMemo(
    () =>
      getDeliveryLocationFormFields({
        stateOptions: stateList,
        countryOptions: countryList,
        isFetchingStates: stateIsLoading,
        isUSA,
      }),
    [stateList, countryList, stateIsLoading, isUSA]
  );

  return (
    <CustomDialog
      onOpenChange={handleOnCancel}
      description=""
      open={open}
      className={'min-w-[60%] 2xl:min-w-[50%] h-[70%] 2xl:[65%]'}
      title="New Delivery Location"
      contentClassName="overflow-y-auto p-0"
    >
      <>
        <div
          className={cn('grid grid-cols-2 gap-4 w-full px-6 mb-4', className)}
        >
          {formFields.map((field) => (
            <div key={field.name} className={`col-span-${field.colSpan}`}>
              {renderFormField({
                field,
                form,
                onCountryChange: handleCountryChange,
                onCustomerSelect: handleOk,
              })}
            </div>
          ))}
        </div>

        <div
          className={cn(
            'flex justify-end items-center gap-4 px-6 bg-white sticky bottom-0 pb-4 pt-1 rounded-b-lg'
          )}
        >
          <AppButton
            label={'Submit'}
            onClick={() => form.handleSubmit(onSubmit)()}
            isLoading={newItemLoading}
          />
          <AppButton
            variant="neutral"
            label="Cancel"
            onClick={handleOnCancel}
          />
        </div>
      </>
    </CustomDialog>
  );
};

export default memo(NewLocations);
