import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import Filter from './Filter';
import { AppTableContext } from '@/components/common/app-data-table/AppTableContext';
import { getQueryParam } from '@/lib/utils';

// Mock the Redux store
const mockStore = configureStore({
  reducer: {
    orders: () => ({
      formValues: {},
      filters: [],
    }),
  },
});

const mockUseFormReturn = {
  watch: vi.fn(() => ({
    searchBy: 'test',
    searchValue: 'test',
    unsubscribe: vi.fn(),
  })),
  reset: vi.fn(),
  register: vi.fn(),
  handleSubmit: vi.fn((fn) => fn),
  setValue: vi.fn(),
  getValues: vi.fn(() => ({})),
  setError: vi.fn(),
  clearErrors: vi.fn(),
  formState: {
    errors: {},
    isDirty: false,
    isValid: true,
    isSubmitting: false,
    isSubmitted: false,
    touchedFields: {},
    dirtyFields: {},
    submitCount: 0,
  },
  control: {},
  setFocus: vi.fn(),
};

// Mock the API calls
vi.mock('@/redux/features/store/store.api', () => ({
  useGetStoreLocationsQuery: vi.fn(() => ({
    data: { data: [{ location: 'Location 1' }, { location: 'Location 2' }] },
    isLoading: false,
  })),
}));

vi.mock('@/redux/features/enums-api/enums-api', () => ({
  useGetEnumsListQuery: vi.fn(() => ({
    data: { data: ['Type1', 'Type2'] },
    isLoading: false,
  })),
}));

vi.mock('react-hook-form', async () => {
  const actual = await vi.importActual('react-hook-form');

  return {
    __esModule: true,
    ...actual,
    useForm: vi.fn(() => mockUseFormReturn),
  };
});

// Mock other components and hooks
vi.mock('@/components/common/app-button', () => ({
  default: ({
    label,
    onClick,
    disabled,
  }: {
    label: string;
    onClick: () => void;
    disabled?: boolean;
  }) => (
    <button onClick={onClick} disabled={disabled}>
      {label}
    </button>
  ),
}));

vi.mock('@/components/forms/date-picker', () => ({
  default: vi.fn(
    ({
      label,
      onDateChange,
    }: {
      label: string;
      onDateChange?: (date: string, field: string) => void;
    }) => {
      if (label === 'Start Date' && onDateChange) {
        onDateChange('', 'dateOfUseFrom');
      }
      return <div>{label}</div>;
    }
  ),
}));

vi.mock('@/components/forms/input-field', () => ({
  default: ({ label }: { label: string }) => <div>{label}</div>,
}));

vi.mock('@/components/forms/select-dropdown', () => ({
  default: ({ label, name }: { label: string; name: string }) => (
    <div>
      {label} - {name}
    </div>
  ),
}));

vi.mock('@/components/ui/separator', () => ({
  Separator: () => <div>Separator</div>,
}));

// Mock utility functions
vi.mock('@/lib/utils', () => ({
  formatDate: vi.fn(),
  generateLabelValuePairs: vi.fn(({ data }) =>
    data.map((item: any) => ({ label: item.location, value: item.location }))
  ),
  getQueryParam: vi.fn(() => null),
  toCapitalize: vi.fn((str) => str),
  updateQueryParam: vi.fn(),
}));

describe('Filter Component', () => {
  const setIsFilterOpen = vi.fn();
  const mockPagination = { pageIndex: 0, pageSize: 10 };
  const setPagination = vi.fn();

  const renderComponent = (props = {}) => {
    return render(
      <Provider store={mockStore}>
        <AppTableContext.Provider
          value={{ pagination: mockPagination, setPagination } as any}
        >
          <Filter setIsFilterOpen={setIsFilterOpen} {...props} />
        </AppTableContext.Provider>
      </Provider>
    );
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render the component with all form fields', () => {
    renderComponent();
    expect(screen.getByText('Filters')).toBeInTheDocument();
    expect(screen.getByText('Separator')).toBeInTheDocument();
    expect(screen.getByText('Store Location - location')).toBeInTheDocument();
    expect(screen.getByText('Order Type - orderType')).toBeInTheDocument();
    expect(screen.getByText('Range - range')).toBeInTheDocument();
    expect(screen.getByText('Search By - searchBy')).toBeInTheDocument();
    expect(screen.getByText('Operator - operator')).toBeInTheDocument();
    expect(screen.getByText('Value')).toBeInTheDocument();
    expect(screen.getByText('Apply')).toBeInTheDocument();
    expect(screen.getByText('Clear')).toBeInTheDocument();
  });

  it('should not show date fields when range is not custom', () => {
    renderComponent();
    expect(screen.queryByText('Start Date')).not.toBeInTheDocument();
    expect(screen.queryByText('End Date')).not.toBeInTheDocument();
  });

  it('should disable Apply and Clear buttons when form is not modified', () => {
    renderComponent();
    expect(screen.getByText('Apply'));
    expect(screen.getByText('Clear'));
  });

  it('should call setIsFilterOpen when Clear button is clicked', async () => {
    renderComponent();
    fireEvent.click(screen.getByText('Clear'));
    await waitFor(() => {
      expect(setIsFilterOpen).toHaveBeenCalledWith(false);
    });
  });

  it('should update search fields when search query param exists', () => {
    (getQueryParam as any).mockReturnValueOnce('test-search');
    renderComponent();
    expect(mockUseFormReturn.setValue).toHaveBeenCalledWith(
      'searchBy',
      'customer'
    );
    expect(mockUseFormReturn.setValue).toHaveBeenCalledWith(
      'searchValue',
      'test-search'
    );
    expect(mockUseFormReturn.setValue).toHaveBeenCalledWith(
      'operator',
      'equals'
    );
  });

  it('should show date fields when range is custom', () => {
    mockUseFormReturn.watch.mockImplementation(() => ({
      range: 'custom',
      searchBy: '',
      searchValue: '',
      unsubscribe: vi.fn(),
    }));
    const isRendered = renderComponent();
    expect(isRendered);
  });

  it('should clear date fields when start date is cleared', async () => {
    mockUseFormReturn.watch.mockImplementation(() => ({
      range: 'custom',
      searchBy: '',
      searchValue: '',
      unsubscribe: vi.fn(),
    }));
    renderComponent();
    await waitFor(() => {
      expect(mockUseFormReturn.setValue);
      expect(mockUseFormReturn.setValue);
    });
  });
});
