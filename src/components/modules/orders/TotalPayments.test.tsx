import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import TotalPayments from './TotalPayments';
import { useForm, useFormContext } from 'react-hook-form';
import { TotalPaymentsTypes } from '@/types/order.types';
import {
  useGetPaymentDetailsQuery,
  useGetTaxCodeListMutation,
  useGetTotalPaymentQuery,
  useRecalCulateMutation,
  useRemovePaymentMutation,
  useTaxExemptMutation,
} from '@/redux/features/orders/order.api';
import { useGetSalesTaxCodeQuery } from '@/redux/features/sales-tax-code/sales-tax-code.api';
import {
  usePaymentTermsQuery,
  usePaymentTypeQuery,
} from '@/redux/features/customers/choices.api';
import { useGetUserDefaultStoreQuery } from '@/redux/features/store/store.api';

const mockFormValues: TotalPaymentsTypes = {
  orderId: 'ORDER123',
  amountSubjectToDiscount: '1000',
  discount: 100,
  subTotal: '900',
  deliveryCharge: '50',
  labour: '100',
  damageWaiver: '20',
  fuelSurcharge: '30',
  productionFee: '40',
  expeditedFee: '50',
  ccConvenienceFee: '10',
  tax: 100,
  grandTotal: 1250,
  dwPercent: 2,
  dwCalculation: 20,
  requiredDeposit: '200',
  isTaxExempted: false,
  chargeConvFee: false,
  paymentType: 'Credit',
  paymentTerms: [{ id: 3, term: 'NET30' }],
  taxCode: { code: 'TAX1', rate: 10, exempt: 'tax1' },
  orderTaxBreakDown: [
    {
      rateCode: 'STATE',
      state: 'CA',
      baseRate: 7.25,
      amount: 72.5,
    },
  ],
  compedOrder: 'false',
  writeOffDiscount: '0',
  amountPaid: '200',
  balanceDue: '1050',
  isDwEdited: false,
  isProdFeeEdited: false,
  isExpeditedFeeEdited: false,
  isFuelChargeEdited: false,
  salesTaxCodeId: '1',
  discountPercentage: '10',
  discountAmount: 100,
  total: '1000',
};

const mockForm = {
  control: {
    _subjects: {},
    _names: {
      names: [],
      isDirty: false,
      dirtyFields: {},
      touchedFields: {},
      isValidating: false,
      isValid: false,
      errors: {},
    },
    _options: {
      mode: 'onSubmit',
      reValidateMode: 'onChange',
      defaultValues: {},
      resolver: undefined,
      context: undefined,
      criteriaMode: 'firstError',
      shouldFocusError: true,
      shouldUnregister: false,
      shouldUseNativeValidation: false,
      delayError: undefined,
    },
    register: vi.fn(),
    unregister: vi.fn(),
    watch: vi.fn(),
    handleSubmit: vi.fn(),
    reset: vi.fn(),
    setError: vi.fn(),
    clearErrors: vi.fn(),
    setValue: vi.fn(),
    getValues: vi.fn(),
    trigger: vi.fn(),
    formState: {
      isDirty: false,
      isValid: false,
      isSubmitting: false,
      isSubmitted: false,
      isSubmitSuccessful: false,
      submitCount: 0,
      dirtyFields: {},
      touchedFields: {},
      errors: {},
      isValidating: false,
    },
  },
  watch: vi.fn(),
  getValues: vi.fn(() => mockFormValues),
  setValue: vi.fn(),
  reset: vi.fn(),
  handleSubmit: vi.fn((fn) => fn),
  register: vi.fn(),
  formState: {
    errors: {},
    isDirty: false,
    isLoading: false,
    isSubmitted: false,
    isSubmitSuccessful: false,
    submitCount: 0,
    touchedFields: {},
    dirtyFields: {},
    isSubmitting: false,
    isValidating: false,
    isValid: true,
    disabled: false,
    defaultValues: {},
  },
};

// Mock react-hook-form
vi.mock('react-hook-form', async () => {
  const actual = await vi.importActual('react-hook-form');
  return {
    ...actual,
    useForm: vi.fn(() => mockForm),
    useFormContext: vi.fn(() => mockForm),
    Controller: ({ render }: { render: any }) =>
      render({
        field: {
          onChange: vi.fn(),
          onBlur: vi.fn(),
          value: '',
          name: '',
          ref: vi.fn(),
        },
        fieldState: {
          invalid: false,
          isTouched: false,
          isDirty: false,
          error: null,
        },
      }),
    useWatch: vi.fn(),
    useController: vi.fn(),
  };
});

// Mock all the external dependencies
vi.mock('@/redux/features/orders/order.api', () => ({
  useGetPaymentDetailsQuery: vi.fn(),
  useGetTotalPaymentQuery: vi.fn(),
  useRecalCulateMutation: vi.fn(),
  useGetTaxCodeListMutation: vi.fn(),
  useTaxExemptMutation: vi.fn(),
  useRemovePaymentMutation: vi.fn(),
}));

vi.mock('@/redux/features/sales-tax-code/sales-tax-code.api', () => ({
  useGetSalesTaxCodeQuery: vi.fn(),
}));

vi.mock('@/redux/features/customers/choices.api', () => ({
  usePaymentTermsQuery: vi.fn(),
  usePaymentTypeQuery: vi.fn(),
}));

vi.mock('@/redux/features/store/store.api', () => ({
  useGetUserDefaultStoreQuery: vi.fn(),
}));

vi.mock('@/lib/utils', async () => {
  const actual = await import('@/lib/utils');
  return {
    ...actual,
    DEFAULT_FORMAT: 'MM/dd/yyyy',
    formatDate: (date: string) => `formatted_${date}`,
    convertToFloat: ({ value }: { value: number }) => `$${value.toFixed(2)}`,
    getQueryParam: () => '123',
    generateLabelValuePairs: () => [{ label: 'Tax1', value: '1' }],
    cn: vi.fn(),
  };
});

vi.mock('@/components/common/data-tables', () => ({
  default: ({ data }: { data: any[] }) => (
    <div data-testid="data-table">{JSON.stringify(data)}</div>
  ),
}));

describe('TotalPayments', () => {
  beforeEach(() => {
    // Reset all mocks before each test
    vi.clearAllMocks();

    // Setup mock implementations
    vi.mocked(useGetTotalPaymentQuery).mockReturnValue({
      data: { data: mockFormValues },
      isFetching: false,
      refetch: vi.fn(),
    } as any);

    vi.mocked(useGetPaymentDetailsQuery).mockReturnValue({
      data: {
        data: [
          {
            id: 1,
            paymentDate: '2023-01-01',
            cardType: 'VISA',
            amount: 200,
            paymentMethodName: 'Credit Card',
            reference: 'REF123',
            cardNumber: '****1234',
            authCode: 'AUTH123',
            email: '<EMAIL>',
            isDeleted: false,
          },
        ],
      },
      refetch: vi.fn(),
      isFetching: false,
    } as any);

    // Fixed: Properly mock RTK Query mutations with unwrap method
    // The mutation function should return an object with unwrap method
    const mockTaxExemptMutation = vi.fn().mockImplementation(() => ({
      unwrap: vi.fn().mockResolvedValue({
        data: { taxBreakDown: [], grandTotal: 1200, tax: 0 },
      }),
    }));

    vi.mocked(useTaxExemptMutation as any).mockReturnValue([
      mockTaxExemptMutation,
      { isLoading: false },
    ]);

    const mockRecalculateMutation = vi.fn().mockImplementation(() => ({
      unwrap: vi.fn().mockResolvedValue({
        data: { success: true, data: { damageWaiver: 25 } },
      }),
    }));

    vi.mocked(useRecalCulateMutation as any).mockReturnValue([
      mockRecalculateMutation,
      { isLoading: false },
    ]);

    const mockGetTaxCodeListMutation = vi.fn().mockImplementation(() => ({
      unwrap: vi.fn().mockResolvedValue({
        data: { labour: 100, damageWaiver: 20, tax: 100, grandTotal: 1250 },
      }),
    }));

    vi.mocked(useGetTaxCodeListMutation as any).mockReturnValue([
      mockGetTaxCodeListMutation,
      { isLoading: false },
    ]);

    const mockRemovePaymentMutation = vi.fn().mockImplementation(() => ({
      unwrap: vi.fn().mockResolvedValue({}),
    }));

    vi.mocked(useRemovePaymentMutation as any).mockReturnValue([
      mockRemovePaymentMutation,
      { isLoading: false },
    ]);

    vi.mocked(useGetSalesTaxCodeQuery).mockReturnValue({
      data: { data: [{ salestaxcode: 'TAX1', salestaxcode_id: '1' }] },
      isLoading: false,
    } as any);

    vi.mocked(usePaymentTypeQuery).mockReturnValue({
      data: { data: [{ Credit: '1' }] },
      isLoading: false,
    } as any);

    vi.mocked(usePaymentTermsQuery).mockReturnValue({
      data: { data: [{ NET30: '1' }] },
      isLoading: false,
    } as any);

    vi.mocked(useGetUserDefaultStoreQuery).mockReturnValue({
      data: { data: { storePayment: { creditCardConvenienceFees: '1.5' } } },
    } as any);

    // Mock form methods
    vi.mocked(useForm).mockReturnValue(mockForm as any);
    vi.mocked(useFormContext).mockReturnValue(mockForm as any);
  });

  it('renders the component with all main sections', () => {
    render(<TotalPayments form={mockForm as any} />);
    expect(screen.getByText('Total / Payments')).toBeInTheDocument();
    expect(screen.getByText('New Payment')).toBeInTheDocument();
    expect(screen.getByText('Saved Payments')).toBeInTheDocument();
    expect(screen.getByText('Extended Payment Info')).toBeInTheDocument();
  });

  it('displays payment data correctly', () => {
    const isRendered = render(<TotalPayments form={mockForm as any} />);
    expect(isRendered);
  });

  it('handles tax exempt switch change', async () => {
    render(<TotalPayments form={mockForm as any} />);
    const taxExemptSwitch = screen.getByText('Tax-Exempt');
    fireEvent.click(taxExemptSwitch);
    await waitFor(() => {
      expect(vi.mocked(useTaxExemptMutation as any)[0]);
    });
  });

  it('handles damage waiver recalculation', async () => {
    render(<TotalPayments form={mockForm as any} />);
    const recalculateButton = screen.getByText('Accept DW');
    const newValue = fireEvent.click(recalculateButton);
    await waitFor(() => {
      expect(newValue);
    });
  });

  it('handles labor input change', async () => {
    render(<TotalPayments form={mockForm as any} />);
    const laborInput = screen.getByText('Labor');
    await waitFor(() => {
      expect(laborInput);
    });
  });

  it('handles damage waiver input change', async () => {
    render(<TotalPayments form={mockForm as any} />);
    const dwInput = screen.getByText('Damage Waiver');
    await waitFor(() => {
      expect(dwInput);
    });
  });

  it('opens new payment dialog when button is clicked', async () => {
    render(<TotalPayments form={mockForm as any} />);
    const newPaymentButton = screen.getByText('New Payment');
    await waitFor(() => {
      expect(newPaymentButton);
    });
  });

  it('displays loading spinner when data is fetching', () => {
    vi.mocked(useGetTotalPaymentQuery).mockReturnValue({
      data: undefined,
      isFetching: true,
      refetch: vi.fn(),
    } as any);
    const isRenderProgressBar = render(
      <TotalPayments form={mockForm as any} />
    );
    expect(isRenderProgressBar);
  });

  it('displays payment details in the table', () => {
    const paymentTable = render(<TotalPayments form={mockForm as any} />);
    expect(paymentTable);
  });

  it('handles tax code change', async () => {
    render(<TotalPayments form={mockForm as any} />);
    const changeButton = screen.getByText('Change');
    const isClicked = fireEvent.click(changeButton);
    await waitFor(() => {
      expect(isClicked);
    });
  });
});
