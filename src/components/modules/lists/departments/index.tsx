import AppButton from '@/components/common/app-button';
import AppSpinner from '@/components/common/app-spinner';
import SwitchField from '@/components/common/switch';
import InputField from '@/components/forms/input-field';
import { DEPARTMENT_API_ROUTES } from '@/constants/api-constants';
import { TEXT_VALIDATION_RULE } from '@/constants/validation-constants';
import { getQueryParam } from '@/lib/utils';
import {
  useAddNewItemMutation,
  useGetListQuery,
  useUpdateItemMutation,
} from '@/redux/features/list/category/list.api';
import { DepartmentFormType } from '@/types/list.types';
import { useEffect, useMemo } from 'react';
import { SubmitHandler, useForm, UseFormReturn } from 'react-hook-form';

// CategoryForm Component
export const Departments = ({ toggle }: { toggle?: () => void }) => {
  const id = getQueryParam('id');

  const { data, isFetching } = useGetListQuery(
    {
      url: DEPARTMENT_API_ROUTES?.GET(id),
    },
    { skip: !id }
  );

  const [updateItem, { isLoading }] = useUpdateItemMutation();
  const [addNewItem, { isLoading: newItemLoading }] = useAddNewItemMutation();

  const defaultValues = useMemo(() => {
    const dataValue = data?.data;
    return {
      ...dataValue,
      department_id: dataValue?.department_id || 0,
      prtkitcomponinv: id ? dataValue?.prtkitcomponinv : true,
    };
  }, [data?.data, id]);

  const form: UseFormReturn<DepartmentFormType> = useForm<DepartmentFormType>({
    defaultValues,
    mode: 'onChange',
  });
  const { handleSubmit, reset } = form;

  useEffect(() => {
    if (defaultValues) {
      reset(defaultValues);
    }
  }, [defaultValues, reset]);

  const onSubmit: SubmitHandler<DepartmentFormType> = async (
    data: DepartmentFormType
  ) => {
    try {
      if (id) {
        await updateItem({
          url: DEPARTMENT_API_ROUTES.UPDATE(id),
          data,
        }).unwrap();
      } else {
        await addNewItem({
          url: DEPARTMENT_API_ROUTES.CREATE,
          data,
        }).unwrap();
      }
      toggle?.();
    } catch (error) {
      // Handle error (optional)
    }
  };

  return (
    <div>
      <div className="grid grid-cols-1 gap-4">
        <InputField
          form={form}
          name="dept"
          label="Department"
          placeholder="Department"
          validation={TEXT_VALIDATION_RULE}
          maxLength={2}
          disabled={Boolean(id)}
        />
        <InputField
          form={form}
          name="deptDesc"
          label="Description"
          placeholder="Description"
          validation={TEXT_VALIDATION_RULE}
        />

        <SwitchField
          className="w-fit py-2"
          form={form}
          name="prtKitCompOnInv"
          label="Print Kit Components on Invoices"
        />
      </div>

      <AppButton
        label="Submit"
        className="w-full col-span-2 mt-4"
        onClick={handleSubmit(onSubmit)}
        isLoading={isLoading || newItemLoading}
      />
      <AppSpinner overlay isLoading={isFetching} />
    </div>
  );
};
