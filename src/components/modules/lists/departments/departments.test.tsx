import { render, screen, fireEvent } from '@testing-library/react';
import { vi, describe, beforeEach, it, expect } from 'vitest';
import { Departments } from './index';
import {
  useGetListQuery,
  useAddNewItemMutation,
  useUpdateItemMutation,
} from '@/redux/features/list/category/list.api';
import { getQueryParam } from '@/lib/utils';

// Mock hooks
vi.mock('@/redux/features/list/category/list.api', () => ({
  useGetListQuery: vi.fn(),
  useAddNewItemMutation: vi.fn(),
  useUpdateItemMutation: vi.fn(),
}));

// Mocking utils
vi.mock('@/lib/utils', () => ({
  getQueryParam: vi.fn(),
  cn: vi.fn(),
}));

describe('Departments Component', () => {
  const mockToggle = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render the form for creating a new department', () => {
    (getQueryParam as any).mockReturnValue('');
    (useGetListQuery as any).mockReturnValue({ data: [], isFetching: false });
    (useAddNewItemMutation as any).mockReturnValue([
      vi.fn(),
      { isLoading: false },
    ]);
    (useUpdateItemMutation as any).mockReturnValue([
      vi.fn(),
      { isLoading: false },
    ]);

    render(<Departments toggle={mockToggle} />);
    expect(screen.getByLabelText('Department')).toBeInTheDocument();
    expect(screen.getByLabelText('Description')).toBeInTheDocument();
    expect(
      screen.getByText('Print Kit Components on Invoices')
    ).toBeInTheDocument();
  });

  it('should render the form for updating an existing department', () => {
    const mockDepartmentData = {
      dept: 'HR',
      deptDesc: 'Human Resources',
      prtkitcomponinv: true,
    };
    (getQueryParam as any).mockReturnValue('123');
    (useGetListQuery as any).mockReturnValue({
      data: { data: mockDepartmentData },
      isFetching: false,
    });
    (useUpdateItemMutation as any).mockReturnValue([
      vi.fn(),
      { isLoading: false },
    ]);

    render(<Departments toggle={mockToggle} />);
    expect(
      screen.getByDisplayValue(mockDepartmentData.dept)
    ).toBeInTheDocument();
    expect(
      screen.getByDisplayValue(mockDepartmentData.deptDesc)
    ).toBeInTheDocument();
  });

  it('should call the toggle function when form is submitted successfully for creating a department', async () => {
    const mockSubmit = vi.fn();
    (getQueryParam as any).mockReturnValue(null);
    (useAddNewItemMutation as any).mockReturnValue([
      mockSubmit,
      { isLoading: false },
    ]);

    render(<Departments toggle={mockToggle} />);

    fireEvent.change(screen.getByLabelText('Department'), {
      target: { value: 'Engineering' },
    });
    fireEvent.change(screen.getByLabelText('Description'), {
      target: { value: 'Handles all technical tasks' },
    });
    expect(mockSubmit);
  });

  it('should call the toggle function when form is submitted successfully for updating a department', async () => {
    const mockSubmit = vi.fn();
    const mockDepartmentData = {
      dept: 'Marketing',
      deptDesc: 'Marketing Team',
      prtkitcomponinv: true,
    };
    (getQueryParam as any).mockReturnValue('123');
    (useGetListQuery as any).mockReturnValue({
      data: { data: mockDepartmentData },
      isFetching: false,
    });
    (useUpdateItemMutation as any).mockReturnValue([
      mockSubmit,
      { isLoading: false },
    ]);

    render(<Departments toggle={mockToggle} />);

    fireEvent.change(screen.getByLabelText('Department'), {
      target: { value: 'Marketing' },
    });
    fireEvent.change(screen.getByLabelText('Description'), {
      target: { value: 'Updated Marketing Description' },
    });

    fireEvent.click(screen.getByText('Submit'));
    expect(mockSubmit);
  });

  it('should show loading spinner when fetching data', () => {
    (getQueryParam as any).mockReturnValue('123');
    (useGetListQuery as any).mockReturnValue({ data: null, isFetching: true });

    render(<Departments toggle={mockToggle} />);
    expect(screen.getByTestId('spinner')).toBeInTheDocument();
  });
});
