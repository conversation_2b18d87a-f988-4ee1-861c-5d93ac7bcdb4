import AppButton from '@/components/common/app-button';
import AppSpinner from '@/components/common/app-spinner';
import InputField from '@/components/forms/input-field';
import NumberInputField from '@/components/forms/number-input-field';
import { EMPLOYEE_API_ROUTES } from '@/constants/api-constants';
import { TEXT_VALIDATION_RULE } from '@/constants/validation-constants';
import { getQueryParam } from '@/lib/utils';
import {
  useAddNewItemMutation,
  useGetListQuery,
  useUpdateItemMutation,
} from '@/redux/features/list/category/list.api';
import { EmployeesType } from '@/types/list.types';
import { useEffect, useMemo } from 'react';
import { SubmitHandler, useForm, UseFormReturn } from 'react-hook-form';

// EmployeesForm Component
export const Employees = ({ toggle }: { toggle?: () => void }) => {
  const id = getQueryParam('id');

  const { data, isFetching } = useGetListQuery(
    {
      url: EMPLOYEE_API_ROUTES?.GET(id),
    },
    { skip: !id }
  );

  const [updateItem, { isLoading }] = useUpdateItemMutation();
  const [addNewItem, { isLoading: newItemLoading }] = useAddNewItemMutation();

  const defaultValues = useMemo(() => {
    const dataValue = data?.data;
    return {
      ...dataValue,
      employee_id: dataValue?.employee_id || 0,
    };
  }, [data]);

  const form: UseFormReturn<EmployeesType> = useForm<EmployeesType>({
    defaultValues,
    mode: 'onChange',
  });
  const { handleSubmit, reset } = form;

  useEffect(() => {
    if (defaultValues) {
      reset(defaultValues);
    }
  }, [defaultValues, reset]);

  const onSubmit: SubmitHandler<EmployeesType> = async (
    data: EmployeesType
  ) => {
    const payload = {
      ...data,
      doubleRate: data?.doubleRate ? Number(data.doubleRate) : null,
      halfRate: data.halfRate ? Number(data.halfRate) : null,
      employeeNo: data.employeeNo ? Number(data.employeeNo) : 0,
      rate: data.rate ? Number(data.rate) : null,
      employee_id: Number(data.employee_id) ?? 0,
    };
    try {
      if (id) {
        await updateItem({
          url: EMPLOYEE_API_ROUTES.UPDATE(id),
          data: payload,
        }).unwrap();
      } else {
        await addNewItem({
          url: EMPLOYEE_API_ROUTES.CREATE,
          data: payload,
        }).unwrap();
      }
      toggle?.();
    } catch (error) {
      // Handle error (optional)
    }
  };

  return (
    <div>
      <div className="grid grid-cols-2 gap-4">
        <InputField
          form={form}
          name="name"
          label="Employee Name"
          placeholder="Enter Employee Name"
          validation={TEXT_VALIDATION_RULE}
        />

        <NumberInputField
          name="employeeNo"
          form={form}
          placeholder="Enter Employee Number"
          label="Employee #"
          disabled={!!id}
          maxLength={9}
          validation={TEXT_VALIDATION_RULE}
        />

        <NumberInputField
          form={form}
          name="rate"
          label="Pay Rate"
          placeholder="Enter Pay Rate"
          maxLength={9}
          prefix="$"
          fixedDecimalScale
          thousandSeparator=","
        />

        <InputField
          name="halfRate"
          type="number"
          form={form}
          maxLength={3}
          placeholder="Enter Hour @ 1.5* Rate"
          label="Hour @ 1.5* Rate"
        />

        <InputField
          name="doubleRate"
          form={form}
          type="number"
          maxLength={3}
          placeholder="Enter Hour @ 2* Rate"
          label="Hour @ 2* Rate"
        />
      </div>

      <AppButton
        label="Submit"
        className="w-full col-span-2 mt-4"
        onClick={handleSubmit(onSubmit)}
        isLoading={isLoading || newItemLoading}
      />
      <AppSpinner overlay isLoading={isFetching} />
    </div>
  );
};
