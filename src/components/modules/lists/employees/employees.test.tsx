import { render, screen } from '@testing-library/react';
import { vi, describe, it, expect } from 'vitest';
import { Employees } from './index';
import {
  useAddNewItemMutation,
  useUpdateItemMutation,
  useGetListQuery,
} from '@/redux/features/list/category/list.api';
import { getQueryParam } from '@/lib/utils';

vi.mock('@/redux/features/list/category/list.api');
vi.mock('@/lib/utils', () => ({
  getQueryParam: vi.fn(),
  cn: vi.fn(),
}));

const toggleMock = vi.fn();

describe('Employees Form Component', () => {
  it('renders the form and submits data when creating a new employee', async () => {
    (getQueryParam as any).mockReturnValue(null);
    const addNewItemMock = vi.fn().mockResolvedValue({});
    (useAddNewItemMutation as any).mockReturnValue([
      addNewItemMock,
      { isLoading: false },
    ]);
    const updateItemMock = vi.fn().mockResolvedValue({});
    (useUpdateItemMutation as any).mockReturnValue([
      updateItemMock,
      { isLoading: false },
    ]);
    (useGetListQuery as any).mockReturnValue({
      data: { data: {} },
      isFetching: false,
    });

    render(<Employees toggle={toggleMock} />);
    expect(screen.getByText('Employee Name')).toBeInTheDocument();
    expect(screen.getByText('Employee #')).toBeInTheDocument();
    expect(screen.getByText('Pay Rate')).toBeInTheDocument();
    expect(screen.getByText('Hour @ 1.5* Rate')).toBeInTheDocument();
    expect(screen.getByText('Hour @ 2* Rate')).toBeInTheDocument();
    expect(addNewItemMock);
    expect(toggleMock);
  });

  it('renders the form and submits data when updating an employee', async () => {
    (getQueryParam as any).mockReturnValue('1');
    const addNewItemMock = vi.fn().mockResolvedValue({});
    (useAddNewItemMutation as any).mockReturnValue([
      addNewItemMock,
      { isLoading: false },
    ]);
    const updateItemMock = vi.fn().mockResolvedValue({});
    (useUpdateItemMutation as any).mockReturnValue([
      updateItemMock,
      { isLoading: false },
    ]);
    (useGetListQuery as any).mockReturnValue({
      data: { data: { employee_id: 1, name: 'John Doe', employeeNo: '12345' } },
      isFetching: false,
    });

    render(<Employees toggle={toggleMock} />);
    expect(updateItemMock);
    expect(toggleMock);
  });
});
