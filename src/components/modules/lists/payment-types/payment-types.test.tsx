import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect } from 'vitest';
import { PaymentTypes } from './index';
import * as utils from '@/lib/utils';
import {
  useGetListQuery,
  useAddNewItemMutation,
  useUpdateItemMutation,
} from '@/redux/features/list/category/list.api';
import { useGetListItemsMutation } from '@/redux/features/common-api/common.api';

vi.mock('@/redux/features/list/category/list.api', async () => {
  const actual = await import('@/redux/features/list/category/list.api');
  return {
    ...actual,
    useGetListQuery: vi.fn(),
    useAddNewItemMutation: vi.fn(),
    useUpdateItemMutation: vi.fn(),
  };
});

vi.mock('@/lib/utils', async () => {
  const actual = await import('@/lib/utils');
  return {
    ...actual,
    getQueryParam: vi.fn(),
    cn: vi.fn(),
    generateLabelValuePairs: vi.fn(),
  };
});

vi.mock('@/redux/features/common-api/common.api', () => ({
  useGetListItemsMutation: vi.fn(),
}));

describe('PaymentTypes Component', () => {
  it('should render form fields and submit for adding a new payment type', async () => {
    const mockAddNewItem = vi.fn();
    const mockToggle = vi.fn();

    (useGetListQuery as any).mockReturnValue({ data: null, isFetching: false });
    (useAddNewItemMutation as any).mockReturnValue([
      mockAddNewItem,
      { isLoading: false },
    ]);
    (useUpdateItemMutation as any).mockReturnValue([
      vi.fn(),
      { isLoading: false },
    ]);
    (useGetListItemsMutation as any).mockReturnValue([
      vi.fn(),
      { isLoading: false },
    ]);

    (utils.generateLabelValuePairs as any).mockReturnValue([
      { label: 'Bank A', value: 1 },
      { label: 'Bank B', value: 2 },
    ]);

    render(<PaymentTypes toggle={mockToggle} />);

    const paymentMethodInput = screen.getByPlaceholderText('Payment Type');
    expect(paymentMethodInput).toBeInTheDocument();

    fireEvent.change(paymentMethodInput, { target: { value: 'Credit Card' } });

    const submitButton = screen.getByText('Submit');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockAddNewItem);
      expect(mockToggle);
    });
  });

  it('should render form fields and submit for updating an existing payment type', async () => {
    const mockUpdateItem = vi.fn();
    const mockToggle = vi.fn();

    vi.spyOn(utils, 'getQueryParam').mockReturnValue('1');
    const mockData = { data: { paymentMethod: 'Cash', bankAccountId: 1 } };
    (useGetListQuery as any).mockReturnValue({
      data: mockData,
      isFetching: false,
    });
    (useUpdateItemMutation as any).mockReturnValue([
      mockUpdateItem,
      { isLoading: false },
    ]);
    (useGetListItemsMutation as any).mockReturnValue([
      vi.fn(),
      { isLoading: false },
    ]);

    (utils.generateLabelValuePairs as any).mockReturnValue([
      { label: 'Bank A' },
      { label: 'Bank B' },
    ]);

    render(<PaymentTypes toggle={mockToggle} />);

    const paymentMethodInput = screen.getByPlaceholderText('Payment Type');
    expect(paymentMethodInput).toHaveValue('Cash');

    fireEvent.change(paymentMethodInput, { target: { value: 'Debit Card' } });

    const submitButton = screen.getByText('Submit');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockUpdateItem);
      expect(mockToggle);
    });
  });

  it('should display loading spinner when data is fetching', () => {
    (useGetListQuery as any).mockReturnValue({ data: null, isFetching: true });
    (useGetListItemsMutation as any).mockReturnValue([
      vi.fn(),
      { isLoading: false },
    ]);

    render(<PaymentTypes toggle={() => {}} />);

    const spinner = screen.getByTestId('spinner');
    expect(spinner).toBeInTheDocument();
  });
});
