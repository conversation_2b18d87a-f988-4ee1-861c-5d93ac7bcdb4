import AppButton from '@/components/common/app-button';
import AppSpinner from '@/components/common/app-spinner';
import InputField from '@/components/forms/input-field';
import SelectDropDown from '@/components/forms/select-dropdown';
import {
  BANK_ACCOUNTS_API_ROUTES,
  PAYMENT_TYPE_API_ROUTES,
} from '@/constants/api-constants';
import {
  generateLabelValuePairs,
  getPaginationObject,
  getQueryParam,
} from '@/lib/utils';
import { useGetListItemsMutation } from '@/redux/features/common-api/common.api';
import {
  useAddNewItemMutation,
  useGetListQuery,
  useUpdateItemMutation,
} from '@/redux/features/list/category/list.api';
import { PaymentTypeFormType } from '@/types/list.types';
import { useEffect, useMemo } from 'react';
import { SubmitHandler, useForm, UseFormReturn } from 'react-hook-form';

// CategoryForm Component
export const PaymentTypes = ({ toggle }: { toggle?: () => void }) => {
  const id = getQueryParam('id');

  const { data, isFetching } = useGetListQuery(
    {
      url: PAYMENT_TYPE_API_ROUTES?.GET(id),
    },
    { skip: !id }
  );

  const [updateItem, { isLoading }] = useUpdateItemMutation();
  const [addNewItem, { isLoading: newItemLoading }] = useAddNewItemMutation();

  const defaultValues = useMemo(() => {
    const dataValue = data?.data;
    return { ...dataValue, id: dataValue?.id || 0 };
  }, [data]);

  const form: UseFormReturn<PaymentTypeFormType> = useForm<PaymentTypeFormType>(
    {
      defaultValues,
      mode: 'onChange',
    }
  );
  const [
    getBankAccount,
    { data: bankAccountData, isLoading: bankAccountLoading },
  ] = useGetListItemsMutation();

  const { handleSubmit, reset } = form;

  useEffect(() => {
    if (defaultValues) {
      reset(defaultValues);
    }
  }, [defaultValues, reset]);

  useEffect(() => {
    getBankAccount({
      url: BANK_ACCOUNTS_API_ROUTES?.ALL,
      data: getPaginationObject({
        pagination: { pageIndex: -1, pageSize: -1 },
        sorting: [{ id: '', desc: true }],
      }),
    });
  }, [getBankAccount]);

  const onSubmit: SubmitHandler<PaymentTypeFormType> = async (
    data: PaymentTypeFormType
  ) => {
    try {
      if (id) {
        await updateItem({
          url: PAYMENT_TYPE_API_ROUTES.UPDATE(id),
          data,
        }).unwrap();
      } else {
        await addNewItem({
          url: PAYMENT_TYPE_API_ROUTES.CREATE,
          data,
        }).unwrap();
      }
      toggle?.();
    } catch (error) {
      // Handle error (optional)
    }
  };

  const bankAccountList = generateLabelValuePairs({
    data: bankAccountData?.data,
    labelKey: 'account',
    valueKey: 'id',
  });

  return (
    <div>
      <div className="grid grid-cols-1 gap-4">
        <InputField
          form={form}
          name="paymentMethod"
          label="Payment Type"
          placeholder="Payment Type"
          disabled
        />
        <SelectDropDown
          form={form}
          name="bankAccountId"
          label="Bank Account"
          optionsList={bankAccountList}
          placeholder="Select Bank Account"
          isLoading={bankAccountLoading}
        />
      </div>

      <AppButton
        label="Submit"
        className="w-full col-span-2 mt-4"
        onClick={handleSubmit(onSubmit)}
        isLoading={isLoading || newItemLoading}
      />
      <AppSpinner overlay isLoading={isFetching} />
    </div>
  );
};
