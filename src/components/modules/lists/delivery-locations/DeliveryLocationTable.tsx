import AppButton from '@/components/common/app-button';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import AppDataTable from '@/components/common/app-data-table';
import AppTableContextProvider from '@/components/common/app-data-table/AppTableContext';
import { DELIVERY_LOCATION_API_ROUTES } from '@/constants/api-constants';
import {
  convertToFloat,
  formatPhoneNumber,
  getQueryParam,
  updateQueryParam,
} from '@/lib/utils';
import { useDeleteItemMutation } from '@/redux/features/list/category/list.api';
import { clearDeliveryLocationFilter } from '@/redux/features/list/delivery-location/deliveryLocationSlice';
import { RootState } from '@/redux/store';
import { ColumnDef } from '@tanstack/react-table';
import { PlusIcon } from 'lucide-react';
import { useCallback, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import DeliveryLocationFilter from './DeliveryLocationFilter';
import ActionColumnMenu from '@/components/common/data-tables/ActionColumn';

// Defining a type for the delivery location item
interface DeliveryLocation {
  id: number;
  location: string;
  phone: string;
  town: string;
  contact: string;
  state: string;
  zipcode: string;
  customerName: string;
  deliveryFee: number;
}

const DeliveryLocationTable = () => {
  const itemId = getQueryParam('id');
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState<boolean>(false);
  const [refresh, setRefresh] = useState<boolean>(false);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const filter = useSelector(
    (state: RootState) => state.deliveryLocation.filters
  );
  const [deleteItem, { isLoading: isDeleteLoading }] = useDeleteItemMutation();
  const [filterOpen, setFilterOpen] = useState(false);

  const toggleCustomDialog = useCallback(() => {
    navigate('add-delivery-location');
    if (itemId) {
      updateQueryParam(null);
    }
  }, [itemId, navigate]);

  const toggleDelete = useCallback((id?: number) => {
    setIsDeleteDialogOpen((prevState) => !prevState);
    updateQueryParam(id || null);
  }, []);

  const handleEdit = useCallback(
    (id?: number) => {
      navigate(`edit-delivery-location?id=${id}`);
    },
    [navigate]
  );

  const columns: ColumnDef<DeliveryLocation>[] = useMemo(
    () => [
      {
        accessorKey: 'location',
        header: 'Delivery Location',
        size: 200,
        enableSorting: true,
      },
      {
        accessorKey: 'phone',
        header: 'Phone',
        size: 160,
        enableSorting: true,
        cell: (info) => {
          const phone = info.getValue() as string;
          if (!phone || phone.length < 10) return '';

          return formatPhoneNumber(phone);
        },
      },
      { accessorKey: 'town', header: 'Town', enableSorting: true },
      { accessorKey: 'contact', header: 'Contact', enableSorting: true },
      { accessorKey: 'state', header: 'State', enableSorting: true },
      { accessorKey: 'zipcode', header: 'Zip Code', enableSorting: true },
      { accessorKey: 'customerName', header: 'Customer', enableSorting: true },
      {
        accessorKey: 'deliveryFee',
        header: 'Delivery Fee',
        cell: ({ row }: any) =>
          convertToFloat({ value: row.original.deliveryFee, prefix: '$' }),
        enableSorting: true,
        size: 160,
      },
      {
        id: 'action',
        size: 110,
        header: 'Actions',
        cell: ({ row }) => {
          const value = row.original?.id;
          return (
            <ActionColumnMenu
              onEdit={() => handleEdit(value)}
              onDelete={() => toggleDelete(value)}
              contentClassName="w-fit"
            />
          );
        },
      },
    ],
    [handleEdit, toggleDelete]
  );

  const handleDeleteCustomer = async () => {
    try {
      if (itemId) {
        await deleteItem({
          url: DELIVERY_LOCATION_API_ROUTES.DELETE ?? '',
          itemId: itemId || '',
        }).unwrap();
        toggleDelete();
        setRefresh(true);
        setTimeout(() => setRefresh(false), 500);
      }
    } catch (error) {
      // Handle error if needed
    }
  };

  const CustomToolbar = (
    <AppButton
      icon={PlusIcon}
      label="New Delivery Location"
      onClick={toggleCustomDialog}
    />
  );

  const handleClearFilter = useCallback(
    (key: string) => {
      dispatch(clearDeliveryLocationFilter(key));
      setFilterOpen(false);
    },
    [dispatch]
  );

  return (
    <AppTableContextProvider defaultSort={[{ id: 'location', desc: true }]}>
      <div className="flex flex-col p-6 gap-6">
        <AppDataTable
          heading="Delivery Locations"
          columns={columns}
          url={DELIVERY_LOCATION_API_ROUTES.ALL}
          enableSearch={false}
          customToolBar={CustomToolbar}
          enablePagination
          filter={filter}
          isFilterOpen={filterOpen}
          setIsFilterOpen={setFilterOpen}
          filterClassName="w-72"
          enableFilter
          refreshList={refresh}
          handleClearFilter={handleClearFilter}
          filterContent={
            <DeliveryLocationFilter setIsFilterOpen={setFilterOpen} />
          }
          tableClassName="max-h-[580px] overflow-auto"
        />

        <AppConfirmationModal
          description={<div>Are you sure you want to delete this record?</div>}
          open={isDeleteDialogOpen}
          onOpenChange={() => toggleDelete()}
          handleCancel={() => toggleDelete()}
          handleSubmit={handleDeleteCustomer}
          isLoading={isDeleteLoading}
        />
      </div>
    </AppTableContextProvider>
  );
};

export default DeliveryLocationTable;
