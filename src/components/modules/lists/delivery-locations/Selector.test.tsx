import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import Selector from './Selector';

// Mock dependencies
vi.mock('@/components/common/app-button', () => ({
  default: vi.fn(({ onClick, label }) => (
    <button data-testid={`AppButton-${label}`} onClick={onClick}>
      {label}
    </button>
  )),
}));

vi.mock('@/components/common/data-tables', () => ({
  default: vi.fn(({ data, columns }) => (
    <div data-testid="DataTable">
      {data.length > 0 ? (
        <table>
          <thead>
            <tr>
              {columns.map((col: any, idx: any) => (
                <th key={idx}>{col.header}</th>
              ))}
            </tr>
          </thead>
          <tbody>
            {data.map((row: any, idx: any) => (
              <tr key={idx}>
                {columns.map((col: any, colIdx: any) => (
                  <td key={colIdx}>{row[col.accessor]}</td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      ) : (
        <div>No Data</div>
      )}
    </div>
  )),
}));

vi.mock('@/components/common/dialog', () => ({
  default: vi.fn(({ open, title, children }) =>
    open ? (
      <div data-testid="CustomDialog">
        <h2>{title}</h2>
        {children}
      </div>
    ) : null
  ),
}));

describe('Selector Component', () => {
  const mockOnOpenChange = vi.fn();
  const mockOnOkClick = vi.fn();
  const mockOnCancelClick = vi.fn();
  const mockSetRowSelection = vi.fn();

  const defaultProps = {
    open: true,
    onOpenChange: mockOnOpenChange,
    filter: <div data-testid="Filter">Mocked Filter</div>,
    rowSelection: {},
    setRowSelection: mockSetRowSelection,
    columns: [
      { header: 'Name', accessor: 'name' },
      { header: 'Age', accessor: 'age' },
    ],
    data: [
      { name: 'John Doe', age: 30 },
      { name: 'Jane Doe', age: 25 },
    ],
    bindingKey: 'id',
    onOkClick: mockOnOkClick,
    onCancelClick: mockOnCancelClick,
    title: 'Test Selector',
  };

  it('renders correctly with provided props', () => {
    render(<Selector {...defaultProps} />);

    expect(screen.getByTestId('CustomDialog')).toBeInTheDocument();
    expect(screen.getByText('Test Selector')).toBeInTheDocument();
    expect(screen.getByTestId('Filter')).toBeInTheDocument();
    expect(screen.getByTestId('DataTable')).toBeInTheDocument();
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('Jane Doe')).toBeInTheDocument();
  });

  it('handles OK button click', () => {
    render(<Selector {...defaultProps} />);

    const okButton = screen.getByTestId('AppButton-OK');
    fireEvent.click(okButton);
    expect(mockOnOkClick).toHaveBeenCalledTimes(1);
  });

  it('handles Cancel button click', () => {
    render(<Selector {...defaultProps} />);

    const cancelButton = screen.getByTestId('AppButton-Cancel');
    fireEvent.click(cancelButton);
    expect(mockOnCancelClick).toHaveBeenCalledTimes(1);
  });

  it('displays no data message when data is empty', () => {
    render(<Selector {...defaultProps} data={[]} />);

    expect(screen.getByText('No Data')).toBeInTheDocument();
  });
});
