import AppButton from '@/components/common/app-button';
import Input<PERSON>ield from '@/components/forms/input-field';
import PhoneInputWidget from '@/components/forms/phone-input-mask';
import { Separator } from '@/components/ui/separator';
import {
  clearAllDeliveryLocationFilters,
  setDeliveryLocationFilters,
  updateDeliveryLocationFormValues,
} from '@/redux/features/list/delivery-location/deliveryLocationSlice';
import { RootState } from '@/redux/store';
import { useEffect } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';

// Define the interface for filter data
interface FilterFormValues {
  [key: string]: string; // Allow any string key with a string value
}

const defaultValues: FilterFormValues = {
  location: '',
  town: '',
  phone: '+1',
};

interface FilterProps {
  setIsFilterOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const DeliveryLocationFilter: React.FC<FilterProps> = ({ setIsFilterOpen }) => {
  const formValuesFromRedux = useSelector(
    (state: RootState) => state.deliveryLocation.formValues
  );
  const dispatch = useDispatch();
  // Initialize form with values from Redux
  const form = useForm<FilterFormValues>({
    defaultValues: formValuesFromRedux,
    mode: 'onChange',
  });

  // Watch form changes and update Redux state
  useEffect(() => {
    const subscription = form.watch((values) => {
      dispatch(updateDeliveryLocationFormValues(values));
    });

    return () => subscription.unsubscribe();
  }, [form, dispatch]);

  const handleClear = () => {
    form.reset(defaultValues); // Reset form to default values
    dispatch(clearAllDeliveryLocationFilters()); // Clear all filters in Redux
    setIsFilterOpen(false); // Close the filter form
  };

  const onSubmit: SubmitHandler<FilterFormValues> = (data) => {
    const newFilterData: any[] = [
      {
        label: 'Town',
        value: data.town,
        name: 'town',
        tagValue: data.town,
        operator: 'Contains',
      },

      {
        label: 'Delivery Location',
        value: data.location,
        name: 'location',
        tagValue: data.location,
        operator: 'Contains',
      },

      {
        label: 'Phone',
        value: data.phone?.slice(2),
        name: 'phone',
        tagValue: data.phone?.slice(2),
        operator: 'Contains',
      },
    ].filter((item) => item.value);

    dispatch(setDeliveryLocationFilters(newFilterData));
    setIsFilterOpen(false);
  };

  // Track if form values are different from the default values
  const isFormModified =
    JSON.stringify(form.watch()) !== JSON.stringify(defaultValues);

  return (
    <>
      <div className="px-3">
        <div className="text-normal py-2  font-semibold">Filters</div>
        <Separator
          orientation="horizontal"
          className="h-[1px] bg-border-Default mb-2"
        />
      </div>
      <div className="pl-3 pb-3 flex flex-col gap-2">
        <InputField
          form={form}
          name="town"
          label="Town"
          placeholder="Enter Town"
        />

        <InputField
          form={form}
          name="location"
          label="Delivery Location"
          placeholder="Enter Location"
        />

        <PhoneInputWidget form={form} name="phone" label="Phone No." />
      </div>
      <div className="w-full h-full flex justify-between gap-3 px-4 py-1 sticky bottom-0 bg-white z-30">
        <AppButton
          className="w-full"
          onClick={form.handleSubmit(onSubmit)}
          label="Apply"
          disabled={!isFormModified}
        />
        <AppButton
          className="w-full"
          label="Clear"
          variant="neutral"
          onClick={handleClear}
          disabled={!isFormModified}
        />
      </div>
    </>
  );
};

export default DeliveryLocationFilter;
