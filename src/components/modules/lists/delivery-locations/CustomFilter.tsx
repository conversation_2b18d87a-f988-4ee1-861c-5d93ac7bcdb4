import InputField from '@/components/forms/input-field';
import PhoneInputWidget from '@/components/forms/phone-input-mask';
import ShadcnSelect from '@/components/forms/ShadcnSelect';
import { useCallback, useRef } from 'react';

// Constants
const CUSTOMER_STATUS = [
  { label: 'Active', value: 'true' },
  { label: 'Inactive', value: 'false' },
];

const FILTER_LIST = [
  { label: 'Customer', value: 'name' },
  { label: 'Phone', value: 'tel1' },
];

const CustomerFilter: React.FC<{
  filterForm: any;
  debouncedSearch: (args: any) => void;
}> = ({ filterForm, debouncedSearch }) => {
  // Create a ref to track the last applied value
  const lastAppliedValueRef = useRef('');

  const performSearch = useCallback(
    (value: string, filterType: 'name' | 'tel1') => {
      const isactive = filterForm.getValues('isactive');

      // Only perform search if the value has actually changed
      if (value !== lastAppliedValueRef.current) {
        debouncedSearch({
          isactive,
          filterName: filterType,
          value,
        });

        // Update the last applied value
        lastAppliedValueRef.current = value;
      }
    },
    [debouncedSearch, filterForm]
  );

  const handleNameFilterChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const value = event.target.value;
      filterForm.setValue('name', value);
      performSearch(value, 'name');
    },
    [performSearch, filterForm]
  );

  const handlePhoneFilterChange = useCallback(
    (value: string) => {
      filterForm.setValue('tel1', value);
      performSearch(value.replace(/^\+1/, ''), 'tel1');
    },
    [performSearch, filterForm]
  );

  const handleStatusChange = useCallback(
    (value: string) => {
      filterForm.setValue('isactive', value);

      // For status change, perform immediate search without debounce
      const currentFilterType = filterForm.getValues('filterName');
      const currentFilterValue =
        currentFilterType === 'name'
          ? filterForm.getValues('name')
          : filterForm.getValues('tel1');

      // Use non-debounced search for status changes
      debouncedSearch({
        isactive: value,
        filterName: currentFilterType,
        value: currentFilterValue,
      });
    },
    [debouncedSearch, filterForm]
  );

  // Handle the filter change (name or phone)
  const handleFilterTypeChange = useCallback(
    (value: string) => {
      filterForm.setValue('filterName', value);

      // Clear the previously set filter's value when changing filter type
      if (value === 'name') {
        filterForm.setValue('tel1', '');
      } else {
        filterForm.setValue('name', '');
      }

      // Call debouncedSearch after changing filter type (to trigger the API call immediately)
      const currentFilterValue =
        value === 'name'
          ? filterForm.getValues('name')
          : filterForm.getValues('tel1');
      debouncedSearch({
        isactive: filterForm.getValues('isactive'),
        filterName: value,
        value: currentFilterValue,
      });
    },
    [debouncedSearch, filterForm]
  );

  return (
    <div className="flex flex-row gap-4 mb-2">
      <ShadcnSelect
        form={filterForm}
        name="isactive"
        parentClassName="w-48"
        label="Customer Status"
        optionsList={CUSTOMER_STATUS}
        allowClear={true}
        isClearable={true}
        placeholder="Customer Status"
        onChange={handleStatusChange}
      />
      <ShadcnSelect
        parentClassName="mt-7 w-48"
        form={filterForm}
        name="filterName"
        optionsList={FILTER_LIST}
        onChange={handleFilterTypeChange} // Update filter type
      />
      {filterForm.watch('filterName') === 'name' ? (
        <InputField
          form={filterForm}
          name="name"
          className="mt-7 w-48"
          placeholder="Enter Name"
          onChange={handleNameFilterChange}
        />
      ) : (
        <PhoneInputWidget
          className="mt-7 w-48"
          form={filterForm}
          name="tel1"
          onChange={handlePhoneFilterChange}
        />
      )}
    </div>
  );
};

export default CustomerFilter;
