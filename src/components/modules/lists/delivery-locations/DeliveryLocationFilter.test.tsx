import { render, screen, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import deliveryLocationReducer from '@/redux/features/list/delivery-location/deliveryLocationSlice';
import DeliveryLocationFilter from './DeliveryLocationFilter';
import { vi, describe, beforeEach, it, expect } from 'vitest';

const mockSetIsFilterOpen = vi.fn();

describe('DeliveryLocationFilter', () => {
  let store: any;

  beforeEach(() => {
    store = configureStore({
      reducer: {
        deliveryLocation: deliveryLocationReducer,
      },
      preloadedState: {
        deliveryLocation: {
          formValues: {
            location: '',
            town: '',
            phone: '+1',
          },
          filters: [],
        },
      },
    });
  });

  it('renders the component with initial state', () => {
    render(
      <Provider store={store}>
        <DeliveryLocationFilter setIsFilterOpen={mockSetIsFilterOpen} />
      </Provider>
    );

    expect(screen.getByLabelText('Town')).toBeInTheDocument();
    expect(screen.getByLabelText('Delivery Location')).toBeInTheDocument();
    expect(screen.getByText('Phone No.')).toBeInTheDocument();

    expect(screen.getByText('Apply'));
    expect(screen.getByText('Clear'));
  });

  it('updates form values and enables the Apply button', () => {
    render(
      <Provider store={store}>
        <DeliveryLocationFilter setIsFilterOpen={mockSetIsFilterOpen} />
      </Provider>
    );

    const townInput = screen.getByLabelText('Town');
    fireEvent.change(townInput, { target: { value: 'New York' } });

    expect(screen.getByText('Apply')).not.toBeDisabled();
  });

  it('dispatches setDeliveryLocationFilters on Apply', () => {
    render(
      <Provider store={store}>
        <DeliveryLocationFilter setIsFilterOpen={mockSetIsFilterOpen} />
      </Provider>
    );

    fireEvent.change(screen.getByLabelText('Town'), {
      target: { value: 'New York' },
    });
    fireEvent.change(screen.getByLabelText('Delivery Location'), {
      target: { value: 'Manhattan' },
    });
    fireEvent.click(screen.getByText('Apply'));
    const state = store.getState();
    expect(state.deliveryLocation.filters);
    expect(mockSetIsFilterOpen);
  });

  it('resets form values and dispatches clearAllDeliveryLocationFilters on Clear', () => {
    render(
      <Provider store={store}>
        <DeliveryLocationFilter setIsFilterOpen={mockSetIsFilterOpen} />
      </Provider>
    );
    fireEvent.change(screen.getByLabelText('Town'), {
      target: { value: 'New York' },
    });
    fireEvent.change(screen.getByLabelText('Delivery Location'), {
      target: { value: 'Manhattan' },
    });

    fireEvent.click(screen.getByText('Clear'));
    expect(screen.getByLabelText('Town')).toHaveValue('');
    expect(screen.getByLabelText('Delivery Location')).toHaveValue('');
    expect(screen.getByText('Phone No.')).toBeInTheDocument();
    const state = store.getState();
    expect(state.deliveryLocation.filters).toEqual([]);
    expect(mockSetIsFilterOpen).toHaveBeenCalledWith(false);
  });
});
