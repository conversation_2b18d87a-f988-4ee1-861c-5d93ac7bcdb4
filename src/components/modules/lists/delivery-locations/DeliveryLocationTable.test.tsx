import { describe, it, vi, expect } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
import { MemoryRouter } from 'react-router-dom';
import DeliveryLocationTable from './DeliveryLocationTable';
import { configureStore } from '@reduxjs/toolkit';
import deliveryLocationReducer from '@/redux/features/list/delivery-location/deliveryLocationSlice';

vi.mock('@/components/common/app-data-table', () => ({
  default: vi.fn(({ onClick }) => (
    <>
      <div data-testid="AppDataTable">Mocked DataTable</div>
      <button data-testid="AppButton" onClick={onClick}>
        New Delivery Location
      </button>
    </>
  )),
}));

vi.mock('@/components/common/app-button', () => ({
  default: vi.fn(({ onClick }) => (
    <button data-testid="AppButton" onClick={onClick}>
      New Delivery Location
    </button>
  )),
}));
vi.mock('@/components/common/app-confirmation-modal', () => ({
  default: vi.fn(
    ({ open, handleSubmit, handleCancel }) =>
      open && (
        <div data-testid="AppConfirmationModal">
          <button data-testid="confirmDelete" onClick={handleSubmit}>
            Confirm
          </button>
          <button data-testid="cancelDelete" onClick={handleCancel}>
            Cancel
          </button>
        </div>
      )
  ),
}));
vi.mock('@/components/delivery-locations/DeliveryLocationFilter', () => ({
  default: vi.fn(() => (
    <div data-testid="DeliveryLocationFilter">Mocked Filter</div>
  )),
}));
vi.mock('@/redux/features/list/category/list.api', () => ({
  useDeleteItemMutation: vi.fn(() => [
    vi.fn(() => Promise.resolve({})),
    { isLoading: false },
  ]),
}));
vi.mock('@/lib/utils', () => ({
  getQueryParam: vi.fn(() => '1'),
  updateQueryParam: vi.fn(),
  formatPhoneNumber: vi.fn((phone) => phone),
  convertToFloat: vi.fn(({ value }) => `$${value.toFixed(2)}`),
}));

const mockStore = configureStore({
  reducer: {
    deliveryLocation: deliveryLocationReducer,
  } as any,
  preloadedState: {
    deliveryLocation: {
      filters: {},
    },
  },
});

describe('DeliveryLocationTable Component', () => {
  it('renders the DeliveryLocationTable component', () => {
    render(
      <Provider store={mockStore}>
        <MemoryRouter>
          <DeliveryLocationTable />
        </MemoryRouter>
      </Provider>
    );

    expect(screen.getByTestId('AppDataTable')).toBeInTheDocument();
  });

  it('opens the delete confirmation modal when delete is triggered', async () => {
    render(
      <Provider store={mockStore}>
        <MemoryRouter>
          <DeliveryLocationTable />
        </MemoryRouter>
      </Provider>
    );

    const iClicked = fireEvent.click(screen.getByTestId('AppButton'));
    expect(iClicked).toBe(true);
  });
});
