// Components
import AppSpinner from '@/components/common/app-spinner';
import Header from '@/components/modules/customers/new-customer/header';

// Constants
import { DELIVERY_LOCATION_API_ROUTES } from '@/constants/api-constants';
import { ROUTES } from '@/constants/routes-constants';

// Hooks and API
import {
  useGetCountryListQuery,
  useGetStateByCountryQuery,
} from '@/redux/features/country/country.api';
import {
  useAddNewItemMutation,
  useGetListQuery,
  useUpdateItemMutation,
} from '@/redux/features/list/category/list.api';

// Types
import { CustomerDetailTypes } from '@/types/customer.types';
import { DeliveryLocationFormType } from '@/types/list.types';

// Utility functions
import {
  getDeliveryLocationFormFields,
  renderFormField,
} from '@/constants/list-constants';
import { generateLabelValuePairs, getQueryParam } from '@/lib/utils';
import { useEffect, useMemo, useState } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';

type StateTypes = {
  label: string;
  value: string;
};

const DeliveryLocations = () => {
  const id = getQueryParam('id');
  const navigate = useNavigate();
  const [selectedCountry, setSelectedCountry] = useState(1);
  const [stateList, setStateList] = useState<StateTypes[]>([]);

  const { data, isFetching } = useGetListQuery(
    { url: DELIVERY_LOCATION_API_ROUTES.GET(id) },
    { skip: !id }
  );
  const { data: countryData = [] } = useGetCountryListQuery();
  const {
    data: statesData = [],
    isFetching: stateIsLoading,
    refetch,
  } = useGetStateByCountryQuery({ countryId: selectedCountry });
  const [updateItem, { isLoading }] = useUpdateItemMutation();
  const [addNewItem, { isLoading: newItemLoading }] = useAddNewItemMutation();

  const defaultValues = useMemo(() => {
    if (!data?.data)
      return {
        country: data?.data?.country ?? 'USA',
      };
    return {
      ...data?.data,
      phone: data?.data?.phone ?? '',
      contactPhone: data?.data?.contactPhone ?? '',
      deliverylocation_id: data?.data?.deliverylocation_id ?? 0,
      customerId: data?.data?.customerId ?? '',
      country: data?.data?.country ?? 'USA',
    };
  }, [data]);

  const form = useForm<DeliveryLocationFormType>({
    defaultValues,
    mode: 'onChange',
  });

  // Memoized country list
  const countryList = useMemo(
    () =>
      generateLabelValuePairs({
        data: countryData,
        labelKey: 'name',
        valueKey: 'name',
      }),
    [countryData]
  );

  // Effect to reset form when default values change
  useEffect(() => {
    if (defaultValues) form.reset(defaultValues);
  }, [defaultValues, form]);

  const handleCountryChange = (value: string) => {
    form.setValue('country', value);
    const newCountry = countryData.find(
      (element: { name: string }) => element.name === value
    );
    setSelectedCountry(newCountry?.country_id ?? 1); // Set selected country
    form.setValue('state', ''); // Reset state when country changes
    form.setValue('zipcode', '');
    form.clearErrors('zipcode');
  };

  useEffect(() => {
    // Refetch states whenever selectedCountry changes
    if (selectedCountry) {
      refetch();
    }
  }, [selectedCountry, refetch]); // Dependency on selectedCountry and refetch

  // Ensure states data is used to generate state list
  useEffect(() => {
    if (statesData?.length) {
      const newStateList = generateLabelValuePairs({
        data: statesData,
        labelKey: 'code',
        valueKey: 'code',
      });
      setStateList(newStateList);
    }
  }, [statesData]); // Only re-run when statesData changes

  const onSubmit: SubmitHandler<DeliveryLocationFormType> = async (
    formData
  ) => {
    try {
      if (id) {
        await updateItem({
          url: DELIVERY_LOCATION_API_ROUTES.UPDATE(id),
          data: {
            ...formData,
            customerId: Number(formData.customerId) ?? null,
            deliverylocation_id: id,
            deliveryFee: Number(formData?.deliveryFee) ?? null,
          },
        }).unwrap();
      } else {
        await addNewItem({
          url: DELIVERY_LOCATION_API_ROUTES.CREATE,
          data: {
            ...formData,
            customerId: Number(formData.customerId) ?? null,
            deliveryFee: Number(formData?.deliveryFee) ?? null,
          },
        }).unwrap();
      }
      navigate(ROUTES.DELIVERY_LOCATION);
    } catch (error) {}
  };

  // Determine if current country is USA for validation
  const isUSA = form.watch('country') === 'USA';

  // New handlers for "Ok" and "Cancel" buttons
  const handleOk = (selectedCustomer: CustomerDetailTypes) => {
    if (selectedCustomer) {
      form.setValue('customerId', Number(selectedCustomer?.customer_id));
      form.setValue(
        'customerName',
        selectedCustomer?.first_name || selectedCustomer?.last_name
          ? `${selectedCustomer.first_name ?? ''} ${selectedCustomer.last_name ?? ''}`.trim()
          : undefined
      );
    }
  };

  const formFields = useMemo(
    () =>
      getDeliveryLocationFormFields({
        stateOptions: stateList,
        countryOptions: countryList,
        isFetchingStates: stateIsLoading,
        isUSA,
      }),
    [stateList, countryList, stateIsLoading, isUSA]
  );

  return (
    <>
      <Header
        isLoading={newItemLoading || isLoading}
        navigateTo={ROUTES.DELIVERY_LOCATION}
        onSave={() => form.handleSubmit(onSubmit)()}
        headerTitle={`${id ? 'Edit' : 'New'} Delivery Location`}
        className="sticky top-16 p-4 z-[20] bg-white"
      />

      <div className="pl-8 pr-8 w-4/5 grid grid-cols-2 gap-4 my-4">
        {formFields.map((field) => (
          <div key={field.name} className={`col-span-${field.colSpan}`}>
            {renderFormField({
              field,
              form,
              onCountryChange: handleCountryChange,
              onCustomerSelect: handleOk,
            })}
          </div>
        ))}
        <AppSpinner overlay isLoading={isFetching} />
      </div>
    </>
  );
};

export default DeliveryLocations;
