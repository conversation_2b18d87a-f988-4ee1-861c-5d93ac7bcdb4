import AppButton from '@/components/common/app-button';
import DataTable from '@/components/common/data-tables';
import CustomDialog from '@/components/common/dialog';
import { cn } from '@/lib/utils';
import { RowSelectionState } from '@tanstack/react-table';
import React, { ReactNode } from 'react';

// Define the types for the props
interface SelectorProps {
  open: boolean;
  onOpenChange: (open?: boolean) => void;
  filter?: React.ReactNode;
  rowSelection?: Record<string, boolean>;
  setRowSelection?: React.Dispatch<React.SetStateAction<RowSelectionState>>;
  columns: any[];
  isLoading?: boolean;
  data: any[];
  bindingKey?: string;
  onOkClick?: () => void;
  onCancelClick?: () => void;
  title?: string;
  enableMultiRowSelection?: boolean;
  className?: string;
  bottomContent?: ReactNode;
  enableRowSelection?: boolean;
  tableClassName?: string;
  contentClassName?: string;
}

const Selector: React.FC<SelectorProps> = ({
  open,
  onOpenChange,
  filter,
  rowSelection,
  setRowSelection,
  columns,
  isLoading,
  data,
  bindingKey,
  onOkClick,
  onCancelClick,
  title,
  className,
  enableMultiRowSelection = false,
  bottomContent,
  enableRowSelection,
  tableClassName,
  contentClassName,
}) => {
  return (
    <CustomDialog
      onOpenChange={onOpenChange}
      open={open}
      title={title}
      description=""
      className={cn(
        'max-h-[80%] overflow-auto min-w-[80%] md:min-w-[80%] lg:min-w-[80%] 2xl:min-w-[55%]',
        className
      )}
      contentClassName={contentClassName}
    >
      <div className="px-6">
        {/* Filter Section */}
        {filter && <div className="flex flex-row gap-4 mb-2">{filter}</div>}

        {/* DataTable Section */}
        <DataTable
          tableClassName={cn(
            'lg:max-h-[350px] 2xl:max-h-[500px] overflow-auto',
            tableClassName
          )}
          data={data ?? []}
          columns={columns}
          isLoading={isLoading}
          enableSearch={false}
          enableRowSelection={enableRowSelection}
          enableMultiRowSelection={enableMultiRowSelection}
          enablePagination={false}
          rowSelection={rowSelection}
          bindingKey={bindingKey}
          onRowSelectionChange={setRowSelection}
        />
        {bottomContent && bottomContent}

        {/* Action Buttons Section */}
        {(onOkClick || onCancelClick) && (
          <div className="mt-4 flex justify-end gap-4 mb-3">
            <div className="w-[30%] flex justify-end gap-4">
              {onOkClick && (
                <AppButton onClick={onOkClick} label="OK" className="w-20" />
              )}
              {onCancelClick && (
                <AppButton
                  onClick={onCancelClick}
                  label="Cancel"
                  className="w-20"
                  variant="neutral"
                />
              )}
            </div>
          </div>
        )}
      </div>
    </CustomDialog>
  );
};

export default Selector;
