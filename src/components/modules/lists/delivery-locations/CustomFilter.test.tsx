import { render } from '@testing-library/react';
import { vi, describe, beforeEach, it, expect } from 'vitest';
import CustomerFilter from './CustomFilter';
import { useForm } from 'react-hook-form';

describe('CustomerFilter Component', () => {
  const mockDebouncedSearch = vi.fn();
  const Wrapper = () => {
    const filterForm = useForm({
      defaultValues: {
        isactive: '',
        filterName: '',
        name: '',
        tel1: '',
      },
    });

    return (
      <CustomerFilter
        filterForm={filterForm}
        debouncedSearch={mockDebouncedSearch}
      />
    );
  };

  beforeEach(() => {
    vi.resetAllMocks();
  });

  it('should render phone input when "Phone" is selected as filter type', () => {
    const isRendered = render(<Wrapper />);

    expect(isRendered);
  });
});
