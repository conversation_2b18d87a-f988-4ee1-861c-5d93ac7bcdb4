import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import {
  useGetListQuery,
  useAddNewItemMutation,
  useUpdateItemMutation,
} from '@/redux/features/list/category/list.api';
import { BankAccounts } from './index';
import { REQUIRED_TEXT } from '@/constants/validation-constants';

// Mock Redux Store
vi.mock('@/redux/features/list/category/list.api', () => ({
  useGetListQuery: vi.fn(),
  useAddNewItemMutation: vi.fn(),
  useUpdateItemMutation: vi.fn(),
}));

const mockToggle = vi.fn();
const mockUseGetListQuery = useGetListQuery;
const mockUseAddNewItemMutation = useAddNewItemMutation;
const mockUseUpdateItemMutation = useUpdateItemMutation;

const renderWithProviders = (ui: any) => {
  const store = configureStore({ reducer: {} });
  return render(<Provider store={store}>{ui}</Provider>);
};

describe('BankAccounts Component', () => {
  beforeEach(() => {
    (mockUseGetListQuery as any).mockReturnValue({
      data: null,
      isFetching: false,
    });
    (mockUseAddNewItemMutation as any).mockReturnValue([
      vi.fn().mockResolvedValue({}),
      { isLoading: false },
    ]);
    (mockUseUpdateItemMutation as any).mockReturnValue([
      vi.fn().mockResolvedValue({}),
      { isLoading: false },
    ]);
  });

  it('renders the form fields correctly', () => {
    renderWithProviders(<BankAccounts toggle={mockToggle} />);

    expect(screen.getByText('Bank Account')).toBeInTheDocument();
    expect(
      screen.getByPlaceholderText('Enter Bank Account Number')
    ).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Submit' })).toBeInTheDocument();
  });

  it('shows validation error when required fields are empty', async () => {
    renderWithProviders(<BankAccounts toggle={mockToggle} />);

    fireEvent.click(screen.getByRole('button', { name: 'Submit' }));

    await waitFor(() => {
      expect(screen.getByText(REQUIRED_TEXT)).toBeInTheDocument();
    });
  });

  it('calls addNewItemMutation when no ID is provided', async () => {
    const addNewItem = vi.fn().mockResolvedValue({});
    (mockUseAddNewItemMutation as any).mockReturnValue([
      addNewItem,
      { isLoading: false },
    ]);

    renderWithProviders(<BankAccounts toggle={mockToggle} />);

    fireEvent.change(screen.getByPlaceholderText('Enter Bank Account Number'), {
      target: { value: '**********' },
    });

    const isClicked = fireEvent.click(
      screen.getByRole('button', { name: 'Submit' })
    );

    expect(isClicked);
  });

  it('calls updateItemMutation when ID is provided', async () => {
    const updateItem = vi.fn().mockResolvedValue({});
    (mockUseUpdateItemMutation as any).mockReturnValue([
      updateItem,
      { isLoading: false },
    ]);
    (mockUseGetListQuery as any).mockReturnValue({
      data: { data: { account: '**********', id: 1 } },
      isFetching: false,
    });

    renderWithProviders(<BankAccounts toggle={mockToggle} />);

    await waitFor(() => {
      expect(screen.getByDisplayValue('**********')).toBeInTheDocument();
    });

    fireEvent.change(screen.getByPlaceholderText('Enter Bank Account Number'), {
      target: { value: '**********' },
    });

    fireEvent.click(screen.getByRole('button', { name: 'Submit' }));

    await waitFor(() => {
      expect(updateItem);
      expect(mockToggle);
    });
  });

  it('shows a loading spinner when data is being fetched', () => {
    (mockUseGetListQuery as any).mockReturnValue({
      data: null,
      isFetching: true,
    });

    renderWithProviders(<BankAccounts toggle={mockToggle} />);

    expect(screen.getByTestId('spinner')).toBeInTheDocument();
  });
});
