import AppButton from '@/components/common/app-button';
import AppSpinner from '@/components/common/app-spinner';
import InputField from '@/components/forms/input-field';
import { BANK_ACCOUNTS_API_ROUTES } from '@/constants/api-constants';
import { REQUIRED_TEXT } from '@/constants/validation-constants';
import { getQueryParam } from '@/lib/utils';
import {
  useAddNewItemMutation,
  useGetListQuery,
  useUpdateItemMutation,
} from '@/redux/features/list/category/list.api';
import { BankAccountsFormType } from '@/types/list.types';
import { useEffect, useMemo } from 'react';
import { SubmitHandler, useForm, UseFormReturn } from 'react-hook-form';

// BankAccountsForm Component
export const BankAccounts = ({ toggle }: { toggle?: () => void }) => {
  const id = getQueryParam('id');

  const { data, isFetching } = useGetListQuery(
    {
      url: BANK_ACCOUNTS_API_ROUTES?.GET(id),
    },
    { skip: !id }
  );

  const [updateItem, { isLoading }] = useUpdateItemMutation();
  const [addNewItem, { isLoading: newItemLoading }] = useAddNewItemMutation();

  const defaultValues = useMemo(() => {
    const dataValue = data?.data;
    return { ...dataValue, id: dataValue?.id || 0 };
  }, [data]);

  const form: UseFormReturn<BankAccountsFormType> =
    useForm<BankAccountsFormType>({
      defaultValues,
      mode: 'onChange',
    });
  const { handleSubmit, reset } = form;

  useEffect(() => {
    if (defaultValues) {
      reset(defaultValues);
    }
  }, [defaultValues, reset]);

  const onSubmit: SubmitHandler<BankAccountsFormType> = async (
    data: BankAccountsFormType
  ) => {
    try {
      if (id) {
        await updateItem({
          url: BANK_ACCOUNTS_API_ROUTES.UPDATE(id),
          data,
        }).unwrap();
      } else {
        await addNewItem({
          url: BANK_ACCOUNTS_API_ROUTES.CREATE,
          data,
        }).unwrap();
      }
      toggle?.();
    } catch (error) {
      // Handle error (optional)
    }
  };

  return (
    <div>
      <div className="grid grid-cols-1 gap-4">
        <InputField
          name="account"
          form={form}
          maxLength={31}
          placeholder="Enter Bank Account Number"
          label="Bank Account"
          validation={{ required: REQUIRED_TEXT }}
        />
      </div>

      <AppButton
        label="Submit"
        className="w-full col-span-2 mt-4"
        onClick={handleSubmit(onSubmit)}
        isLoading={isLoading || newItemLoading}
      />
      <AppSpinner overlay isLoading={isFetching} />
    </div>
  );
};
