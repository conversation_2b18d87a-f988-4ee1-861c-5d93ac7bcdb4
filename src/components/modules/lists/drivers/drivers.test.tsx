import { render, screen, fireEvent } from '@testing-library/react';
import { vi, describe, beforeEach, it, expect } from 'vitest';
import { Drivers } from './index';
import {
  useGetListQuery,
  useAddNewItemMutation,
  useUpdateItemMutation,
} from '@/redux/features/list/category/list.api';
import { getQueryParam } from '@/lib/utils';

// Mock hooks
vi.mock('@/redux/features/list/category/list.api', () => ({
  useGetListQuery: vi.fn(),
  useAddNewItemMutation: vi.fn(),
  useUpdateItemMutation: vi.fn(),
}));

// Mocking utils
vi.mock('@/lib/utils', () => ({
  getQueryParam: vi.fn(),
  cn: vi.fn(),
}));

describe('Drivers Component', () => {
  const mockToggle = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render the form for creating a new driver', () => {
    (getQueryParam as any).mockReturnValue(null);
    (useGetListQuery as any).mockReturnValue({ data: null, isFetching: false });
    (useAddNewItemMutation as any).mockReturnValue([
      vi.fn(),
      { isLoading: false },
    ]);
    (useUpdateItemMutation as any).mockReturnValue([
      vi.fn(),
      { isLoading: false },
    ]);

    render(<Drivers toggle={mockToggle} />);
    expect(screen.getByLabelText('Driver')).toBeInTheDocument();
    expect(screen.getByText('Phone')).toBeInTheDocument();
    expect(screen.getByText('E-mail')).toBeInTheDocument();
    expect(screen.getByText('GPS Device Name')).toBeInTheDocument();
    expect(screen.getByText('Submit')).toBeInTheDocument();
  });

  it('should call onSubmit when the form is submitted for a new driver', async () => {
    const mockSubmit = vi.fn();
    (getQueryParam as any).mockReturnValue(null);
    (useGetListQuery as any).mockReturnValue({ data: null, isFetching: false });
    (useAddNewItemMutation as any).mockReturnValue([
      mockSubmit,
      { isLoading: false },
    ]);
    (useUpdateItemMutation as any).mockReturnValue([
      vi.fn(),
      { isLoading: false },
    ]);

    render(<Drivers toggle={mockToggle} />);

    fireEvent.change(screen.getByLabelText('Driver'), {
      target: { value: 'John Doe' },
    });

    fireEvent.click(screen.getByText('Submit'));

    expect(mockSubmit);
  });

  it('should show loading spinner when fetching data', () => {
    (getQueryParam as any).mockReturnValue('123');
    (useGetListQuery as any).mockReturnValue({ data: null, isFetching: true });
    (useAddNewItemMutation as any).mockReturnValue([
      vi.fn(),
      { isLoading: false },
    ]);
    (useUpdateItemMutation as any).mockReturnValue([
      vi.fn(),
      { isLoading: false },
    ]);

    render(<Drivers toggle={mockToggle} />);
    expect(screen.getByTestId('spinner')).toBeInTheDocument();
  });

  it('should render the form for updating an existing driver', () => {
    const mockDriverData = {
      drivers_id: 1,
      name: 'Jane Doe',
      phone: '9876543210',
      email: '<EMAIL>',
      gpsName: 'GPS-456',
    };

    (getQueryParam as any).mockReturnValue('1');
    (useGetListQuery as any).mockReturnValue({
      data: { data: mockDriverData },
      isFetching: false,
    });
    (useAddNewItemMutation as any).mockReturnValue([
      vi.fn(),
      { isLoading: false },
    ]);
    (useUpdateItemMutation as any).mockReturnValue([
      vi.fn(),
      { isLoading: false },
    ]);

    render(<Drivers toggle={mockToggle} />);
    expect(screen.getByDisplayValue(mockDriverData.name)).toBeInTheDocument();
  });
});
