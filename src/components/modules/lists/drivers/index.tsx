import AppButton from '@/components/common/app-button';
import AppSpinner from '@/components/common/app-spinner';
import InputField from '@/components/forms/input-field';
import PhoneInputWidget from '@/components/forms/phone-input-mask';
import { DRIVERS_API_ROUTES } from '@/constants/api-constants';
import { EMAIL_VALIDATION_RULEOptional } from '@/constants/auth-constants';
import { TEXT_VALIDATION_RULE } from '@/constants/validation-constants';
import { getQueryParam } from '@/lib/utils';
import {
  useAddNewItemMutation,
  useGetListQuery,
  useUpdateItemMutation,
} from '@/redux/features/list/category/list.api';
import { DriversFormType } from '@/types/list.types';
import { useEffect, useMemo } from 'react';
import { SubmitHandler, useForm, UseFormReturn } from 'react-hook-form';

// DriversForm Component
export const Drivers = ({ toggle }: { toggle?: () => void }) => {
  const id = getQueryParam('id');

  const { data, isFetching } = useGetListQuery(
    {
      url: DRIVERS_API_ROUTES?.GET(id),
    },
    { skip: !id }
  );

  const [updateItem, { isLoading }] = useUpdateItemMutation();
  const [addNewItem, { isLoading: newItemLoading }] = useAddNewItemMutation();

  const defaultValues = useMemo(() => {
    const dataValue = data?.data;
    return {
      ...dataValue,
      drivers_id: dataValue?.drivers_id || 0,
      phone: dataValue?.phone ?? '',
    };
  }, [data]);

  const form: UseFormReturn<DriversFormType> = useForm<DriversFormType>({
    defaultValues,
    mode: 'onChange',
  });
  const { handleSubmit, reset } = form;

  useEffect(() => {
    if (defaultValues) {
      reset(defaultValues);
    }
  }, [defaultValues, reset]);

  const onSubmit: SubmitHandler<DriversFormType> = async (
    data: DriversFormType
  ) => {
    try {
      if (id) {
        await updateItem({
          url: DRIVERS_API_ROUTES.UPDATE(id),
          data,
        }).unwrap();
      } else {
        await addNewItem({
          url: DRIVERS_API_ROUTES.CREATE,
          data,
        }).unwrap();
      }
      toggle?.();
    } catch (error) {
      // Handle error (optional)
    }
  };

  return (
    <div>
      <div className="grid grid-cols-1 gap-4">
        <InputField
          form={form}
          name="name"
          label="Driver"
          placeholder="Enter Driver"
          validation={TEXT_VALIDATION_RULE}
        />

        <PhoneInputWidget
          form={form}
          name="phone"
          label="Phone"
          placeholder="Enter Phone"
        />

        <InputField
          form={form}
          name="email"
          label="E-mail"
          placeholder="Enter Email"
          validation={EMAIL_VALIDATION_RULEOptional}
        />

        <InputField
          form={form}
          name="gpsName"
          label="GPS Device Name"
          placeholder="Enter GPS Device Name"
        />
      </div>

      <AppButton
        label="Submit"
        className="w-full col-span-2 mt-4"
        onClick={handleSubmit(onSubmit)}
        isLoading={isLoading || newItemLoading}
      />
      <AppSpinner overlay isLoading={isFetching} />
    </div>
  );
};
