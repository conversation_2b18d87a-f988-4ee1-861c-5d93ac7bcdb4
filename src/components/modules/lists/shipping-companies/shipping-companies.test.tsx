import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, expect, describe, beforeEach, it } from 'vitest';
import { ShippingCompanies } from './index';
import {
  useGetListQuery,
  useUpdateItemMutation,
  useAddNewItemMutation,
} from '@/redux/features/list/category/list.api';
import { useShippingCompanyTypeQuery } from '@/redux/features/customers/choices.api';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import * as utils from '@/lib/utils';

vi.mock('@/redux/features/list/category/list.api', () => ({
  useGetListQuery: vi.fn(),
  useUpdateItemMutation: vi.fn(),
  useAddNewItemMutation: vi.fn(),
}));

vi.mock('@/redux/features/customers/choices.api', () => ({
  useShippingCompanyTypeQuery: vi.fn(),
}));

vi.mock('@/components/common/app-spinner', () => ({
  default: () => <div>Loading...</div>,
}));

vi.mock('@/components/forms/input-field', () => ({
  default: ({ name, label }: any) => (
    <input name={name} data-testid={name} placeholder={label} />
  ),
}));

vi.mock('@/components/forms/select-dropdown', () => ({
  default: ({ name, optionsList }: any) => (
    <select name={name} data-testid={name}>
      {optionsList.map((option: any) => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  ),
}));

vi.mock('@/components/common/app-button', () => ({
  default: ({ onClick, isLoading }: any) => (
    <button onClick={onClick} disabled={isLoading}>
      Submit
    </button>
  ),
}));

const mockStore = configureStore({
  reducer: {
    list: (state = {}) => state,
  },
});

describe('ShippingCompanies Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render the form and submit data for new shipping company', async () => {
    const toggleMock = vi.fn();
    const mockShippingCompanyTypeData = {
      data: [{ type1: 'Type 1' }, { type2: 'Type 2' }],
    };
    const mockAddNewItemMutation = vi.fn().mockResolvedValue({});
    const mockUpdateItemMutation = vi.fn().mockResolvedValue({});

    (useGetListQuery as any).mockReturnValue({ data: null, isFetching: false });
    (useShippingCompanyTypeQuery as any).mockReturnValue({
      data: mockShippingCompanyTypeData,
      isLoading: false,
    });
    (useAddNewItemMutation as any).mockReturnValue([
      mockAddNewItemMutation,
      { isLoading: false },
    ]);
    (useUpdateItemMutation as any).mockReturnValue([
      mockUpdateItemMutation,
      { isLoading: false },
    ]);

    render(
      <Provider store={mockStore}>
        <ShippingCompanies toggle={toggleMock} />
      </Provider>
    );

    expect(screen.getByPlaceholderText('Shipping Company')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Track Page')).toBeInTheDocument();
    expect(screen.getByText('Submit')).toBeInTheDocument();

    fireEvent.change(screen.getByTestId('name'), {
      target: { value: 'New Shipping Co' },
    });
    fireEvent.change(screen.getByTestId('trackPage'), {
      target: { value: 'www.trackpage.com' },
    });
    fireEvent.change(screen.getByTestId('type'), {
      target: { value: 'Type 1' },
    });

    fireEvent.click(screen.getByText('Submit'));

    await waitFor(() => {
      expect(mockAddNewItemMutation);
      expect(toggleMock);
    });
  });

  it('should render loading state when data is fetching', async () => {
    const toggleMock = vi.fn();
    (useGetListQuery as any).mockReturnValue({ data: null, isFetching: true });
    (useShippingCompanyTypeQuery as any).mockReturnValue({
      data: null,
      isLoading: true,
    });

    render(
      <Provider store={mockStore}>
        <ShippingCompanies toggle={toggleMock} />
      </Provider>
    );

    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('should render form with existing shipping company data for editing', async () => {
    const toggleMock = vi.fn();
    const mockData = {
      data: {
        id: 1,
        name: 'Existing Shipping Co',
        trackPage: 'www.existingpage.com',
        type: 'Type 1',
      },
    };
    const mockUpdateItemMutation = vi.fn().mockResolvedValue({});

    vi.spyOn(utils, 'getQueryParam').mockReturnValue('1');
    (useGetListQuery as any).mockReturnValue({
      data: mockData,
      isFetching: false,
    });
    (useShippingCompanyTypeQuery as any).mockReturnValue({
      data: [{ type1: 'Type 1' }],
      isLoading: false,
    });
    (useUpdateItemMutation as any).mockReturnValue([
      mockUpdateItemMutation,
      { isLoading: false },
    ]);

    render(
      <Provider store={mockStore}>
        <ShippingCompanies toggle={toggleMock} />
      </Provider>
    );

    fireEvent.change(screen.getByTestId('name'), {
      target: { value: 'Updated Shipping Co' },
    });
    fireEvent.change(screen.getByTestId('trackPage'), {
      target: { value: 'www.updatedpage.com' },
    });
    fireEvent.change(screen.getByTestId('type'), {
      target: { value: 'Type 1' },
    });

    fireEvent.click(screen.getByText('Submit'));

    await waitFor(() => {
      expect(mockUpdateItemMutation);
      expect(toggleMock);
    });
  });
});
