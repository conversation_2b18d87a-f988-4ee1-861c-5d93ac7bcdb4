import AppButton from '@/components/common/app-button';
import AppSpinner from '@/components/common/app-spinner';
import InputField from '@/components/forms/input-field';
import SelectDropDown from '@/components/forms/select-dropdown';
import { SHIPPING_COMPAINES_API_ROUTES } from '@/constants/api-constants';
import { TEXT_VALIDATION_RULE } from '@/constants/validation-constants';
import { getQueryParam } from '@/lib/utils';
import { useShippingCompanyTypeQuery } from '@/redux/features/customers/choices.api';
import {
  useAddNewItemMutation,
  useGetListQuery,
  useUpdateItemMutation,
} from '@/redux/features/list/category/list.api';
import { ShippingCompaniesType } from '@/types/list.types';
import { useEffect, useMemo } from 'react';
import { SubmitHandler, UseFormReturn, useForm } from 'react-hook-form';

// ShippingCompaniesForm Component
export const ShippingCompanies = ({
  toggle,
}: {
  toggle?: (value?: any) => void;
}) => {
  const id = getQueryParam('id');

  const { data, isFetching } = useGetListQuery(
    {
      url: SHIPPING_COMPAINES_API_ROUTES?.GET(id),
    },
    { skip: !id }
  );
  const {
    data: shippingCompanyTypeData,
    isLoading: isShippingCompanyTypeLoading,
  } = useShippingCompanyTypeQuery();
  const [updateItem, { isLoading }] = useUpdateItemMutation();
  const [addNewItem, { isLoading: newItemLoading }] = useAddNewItemMutation();

  const defaultValues = useMemo(() => {
    const dataValue = data?.data;
    return {
      ...dataValue,
      id: dataValue?.id || 0,
    };
  }, [data?.data]);

  const form: UseFormReturn<ShippingCompaniesType> =
    useForm<ShippingCompaniesType>({
      defaultValues,
      mode: 'onChange',
    });

  const { reset } = form;

  const onSubmit: SubmitHandler<ShippingCompaniesType> = async (
    data: ShippingCompaniesType
  ) => {
    try {
      if (id) {
        await updateItem({
          url: SHIPPING_COMPAINES_API_ROUTES.UPDATE(id),
          data,
        }).unwrap();
      } else {
        await addNewItem({
          url: SHIPPING_COMPAINES_API_ROUTES.CREATE,
          data,
        }).unwrap();
      }
      toggle?.();
    } catch (error) {
      // Error optional
    }
  };

  const shippingCompanyTypeList = shippingCompanyTypeData?.data?.map(
    (item: any) => {
      const key = Object.keys(item)[0];
      const value = item[key];
      return {
        label: key,
        value: value,
      };
    }
  );

  useEffect(() => {
    if (defaultValues) {
      reset(defaultValues);
    }
  }, [defaultValues, reset]);

  return (
    <div>
      <div className="grid grid-cols-2 gap-4">
        <InputField
          form={form}
          name="name"
          label="Shipping Company"
          placeholder="Shipping Company"
          validation={TEXT_VALIDATION_RULE}
        />
        <SelectDropDown
          form={form}
          optionsList={shippingCompanyTypeList ?? []}
          label="Type"
          name="type"
          placeholder="Select Type"
          validation={TEXT_VALIDATION_RULE}
          isLoading={isShippingCompanyTypeLoading}
        />
        <div className="col-span-2">
          <InputField
            form={form}
            name="trackPage"
            label="Track Page"
            placeholder="Track Page"
          />
        </div>
        <AppButton
          label="Submit"
          className="w-full col-span-2 mt-4"
          onClick={form.handleSubmit(onSubmit)}
          isLoading={isLoading || newItemLoading}
        />
      </div>
      <AppSpinner overlay isLoading={isFetching} />
    </div>
  );
};
