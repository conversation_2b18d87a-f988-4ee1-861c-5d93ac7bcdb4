import { AllListsType } from '@/types/list.types';
import { memo } from 'react';
import { BankAccounts } from './bank-accounts';
import { CategoryForm } from './categories';
import { ChecklistItems } from './checklist-items';
import { CustomerTypes } from './customer-types';
import { DeliveryChargesForm } from './delivery-charges';
import { DeliveryType } from './delivery-types';
import { Departments } from './departments';
import { Drivers } from './drivers';
import { ESignFields } from './e-sign-fields';
import { EquipmentTypes } from './equipment-types';
import { EventTypes } from './event-types';
import { PaymentTerms } from './payment-terms';
import { PaymentTypes } from './payment-types';
import { QualityTypes } from './quality-types';
import { ReferralTypes } from './referral-types';
import { SalesTaxCode } from './sales-tax-code';
import DeliveryLocations from './delivery-locations';
import { SetupTakedown } from './setup-takedown';
import { ShippingCompanies } from './shipping-companies';
import { SurgeRates } from './surge-rates';
import { TrucksEuipmentLists } from './truck-equipment-list';
import { Trucks } from './trucks';
import { LocalZipCodes } from './local-zip-codes';
import { Employees } from './employees';
import { PackingListDepartments } from './packing-list-departments';

const AllLists = ({ name, toggle }: AllListsType) => {
  switch (name) {
    case 'bank-accounts':
      return <BankAccounts toggle={toggle} />;
    case 'categories':
      return <CategoryForm toggle={toggle} />;
    case 'checklist-items':
      return <ChecklistItems toggle={toggle} />;
    case 'payment-terms':
      return <PaymentTerms toggle={toggle} />;
    case 'customer-types':
      return <CustomerTypes toggle={toggle} />;
    case 'delivery-types':
      return <DeliveryType toggle={toggle} />;
    case 'departments':
      return <Departments toggle={toggle} />;
    case 'drivers':
      return <Drivers toggle={toggle} />;
    case 'e-sign-fields':
      return <ESignFields toggle={toggle} />;
    case 'employees':
      return <Employees toggle={toggle} />;
    case 'equipment-types':
      return <EquipmentTypes toggle={toggle} />;
    case 'event-types':
      return <EventTypes toggle={toggle} />;
    case 'payment-types':
      return <PaymentTypes toggle={toggle} />;
    case 'quality-types':
      return <QualityTypes toggle={toggle} />;
    case 'referral-types':
      return <ReferralTypes toggle={toggle} />;
    case 'sales-tax-code':
      return <SalesTaxCode toggle={toggle} />;
    case 'delivery-locations':
      return <DeliveryLocations />;
    case 'delivery-charges':
      return <DeliveryChargesForm toggle={toggle} />;
    case 'setup-takedown':
      return <SetupTakedown toggle={toggle} />;
    case 'shipping-companies':
      return <ShippingCompanies toggle={toggle} />;
    case 'surge-rates':
      return <SurgeRates toggle={toggle} />;
    case 'trucks':
      return <Trucks toggle={toggle} />;
    case 'truck-equipment-list':
      return <TrucksEuipmentLists toggle={toggle} />;
    case 'local-zip-codes':
      return <LocalZipCodes toggle={toggle} />;
    case 'packing-list-departments':
      return <PackingListDepartments toggle={toggle} />;
    default:
      return null;
  }
};

export default memo(AllLists);
