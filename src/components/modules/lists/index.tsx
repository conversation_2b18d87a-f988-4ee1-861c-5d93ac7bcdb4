import DataTable from '@/components/common/data-tables/index';
import { listData } from '@/mock-data/list-mock-data';
import { ListTypes } from '@/types/list.types';
import { ColumnDef } from '@tanstack/react-table';
import { useMemo } from 'react';
import { Link } from 'react-router-dom';

const Lists = () => {
  // Memoized table columns
  const columns: ColumnDef<ListTypes>[] = useMemo(
    () => [
      {
        accessorKey: 'listName',
        header: 'List Name',
        cell: (info) => {
          return (
            <Link to={info.row.original.to}>
              <div className="cursor-pointer">
                {(info.getValue() as string) ?? ''}
              </div>
            </Link>
          );
        },
      },
    ],
    []
  );

  return (
    <div className="flex flex-col p-6 gap-6">
      <DataTable
        data={listData}
        columns={columns}
        heading="Lists"
        enablePagination={false}
      />
    </div>
  );
};

export default Lists;
