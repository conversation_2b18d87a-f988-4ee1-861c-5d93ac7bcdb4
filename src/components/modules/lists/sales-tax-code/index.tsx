import AppButton from '@/components/common/app-button';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import AppSpinner from '@/components/common/app-spinner';
import DataTable from '@/components/common/data-tables';
import ActionColumnMenu from '@/components/common/data-tables/ActionColumn';
import CustomDialog from '@/components/common/dialog';
import SwitchField from '@/components/common/switch';
import DatePicker from '@/components/forms/date-picker';
import InputField from '@/components/forms/input-field';
import Labels from '@/components/forms/Label';
import NumberInputField from '@/components/forms/number-input-field';
import SelectWidget from '@/components/forms/select';
import { Label } from '@/components/ui/label';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { SALES_TAX_CODE_API_ROUTES } from '@/constants/api-constants';
import { TEXT_VALIDATION_RULE } from '@/constants/validation-constants';
import useOptionList from '@/hooks/useOptionList';
import {
  convertToFloat,
  generateLabelValuePairs,
  getQueryParam,
} from '@/lib/utils';
import { useAddNewItemMutation } from '@/redux/features/common-api/common.api';
import { useGetListQuery } from '@/redux/features/list/category/list.api';
import { useGetStateListQuery } from '@/redux/features/state/state.api';
import { SortingStateType } from '@/types/common.types';
import { StateType } from '@/types/customer.types';
import { SalesTaxCodeFormType, SalesTaxRatesTypes } from '@/types/list.types';
import { ColumnDef } from '@tanstack/react-table';
import { HelpCircleIcon } from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import {
  SubmitHandler,
  useFieldArray,
  useForm,
  UseFormReturn,
} from 'react-hook-form';

export const SalesTaxCode = ({ toggle }: { toggle?: () => void }) => {
  const id = getQueryParam('id');
  const [isAdditionalRates, setIsAdditionalRates] = useState<boolean>(false);
  const [openModal, setOpenModal] = useState<boolean>(false);
  const [additionalRateId, setAdditionalRateId] = useState<number | null>(null);
  const [sorting, setSorting] = useState<SortingStateType[]>([
    { id: 'salestaxcode', desc: true },
  ]);
  const { data: stateData, isFetching: stateIsFetching } =
    useGetStateListQuery();

  // get sales tax code details
  const { data, isFetching } = useGetListQuery(
    {
      url: SALES_TAX_CODE_API_ROUTES?.GET(id),
    },
    { skip: !id }
  );

  // add  / update sale tax code
  const [addUpdateSaleTaxCode, { isLoading }] = useAddNewItemMutation();

  const defaultValues = useMemo(() => {
    const dataValue = data?.data;
    return {
      ...dataValue,
      salestaxcode_id: dataValue?.salestaxcode_id ?? 0,
      salestaxrate: dataValue?.salestaxrate
        ? Number(dataValue?.salestaxrate)
        : null,
      isactive: id ? dataValue?.isactive : true,
      selectable: id ? dataValue?.selectable : true,
      updateqb: true,
      salesTaxRates: dataValue?.salesTaxRates || [],
    };
  }, [data?.data, id]);

  const form: UseFormReturn<SalesTaxCodeFormType> =
    useForm<SalesTaxCodeFormType>({
      defaultValues,
    });
  const { handleSubmit, reset, watch } = form;

  const { append, remove } = useFieldArray({
    control: form.control,
    name: 'salesTaxRates',
  });

  useEffect(() => {
    if (defaultValues) {
      reset(defaultValues);
    }
  }, [defaultValues, reset]);

  const toggleAdditionalRates = useCallback(() => {
    setIsAdditionalRates((prevState) => !prevState);
  }, []);

  // handle submit
  const onSubmit: SubmitHandler<SalesTaxCodeFormType> = async (
    formData: SalesTaxCodeFormType
  ) => {
    const { salesTaxRates, effectivedate, ...restData } = formData;
    const payload = {
      ...restData,
      effectivedate: effectivedate ?? null,
      salesTaxRates: salesTaxRates?.map((item) => ({
        addnl_salestaxcode_id: item?.addnl_salestaxcode_id,
        salestaxrate: item?.salestaxrate,
      })),
    };
    try {
      await addUpdateSaleTaxCode({
        url: SALES_TAX_CODE_API_ROUTES.ADD_UPDATE,
        data: payload,
      }).unwrap();
      toggle?.();
    } catch (error) {}
  };

  // switch saletax fileds
  const SalesTaxCodeCheckList = [
    { label: 'Tax Delivery', name: 'taxdel' },
    { label: 'Tax Labor', name: 'taxlabor' },
    { label: 'Tax Damage Waiver', name: 'taxdamagewaiver' },
    { label: 'Tax Fuel Surcharge', name: 'taxfuelsurcharge' },
    { label: 'Tax Production Fee', name: 'taxproductionfee' },
    { label: 'Tax Expedited Fee', name: 'taxexpeditedfee' },
    { label: 'Tax Convenience Fee', name: 'taxconvfee' },
    { label: 'Selectable', name: 'selectable' },
    {
      label: 'Transaction',
      name: 'transaction',
      description:
        'A transaction tax will apply the base rate to the taxable amount and also include non-taxable items. It will apply against delivered rentals, no CPU or sales orders/quotes.',
    },
    {
      label: 'Tax Convenience Fees For Taxable Sales Only',
      name: 'convfeetaxsalesonly',
      description:
        'Taxing the convenience fees for taxable sales only will only tax the portion of the convenience fee that is applicable to the total taxable sales of the order.',
    },
  ];

  // toggle delete
  const toggleDelete = useCallback((index: number | null = null) => {
    setOpenModal((prevState) => !prevState);
    setAdditionalRateId(index);
  }, []);

  // state List
  const stateList = generateLabelValuePairs({
    data: stateData as StateType[],
    labelKey: 'code',
    valueKey: 'code',
  });

  // additional rate
  const salesTaxRatesList = form.watch('salesTaxRates');

  // get all sale tax list
  const { options: salesTaxData, optionLoading } = useOptionList({
    url: SALES_TAX_CODE_API_ROUTES.ALL,
    sortBy: 'salestaxcode',
    isOption: false,
  });
  // generate value pair
  const allSalesTaxCode = generateLabelValuePairs({
    data: salesTaxData,
    labelKey: 'salestaxcode',
    valueKey: 'salestaxcode_id',
  });

  // sale tax code
  const taxCodeList = useMemo(() => {
    //get available sale tax code id's
    const salesTaxCodeId = salesTaxRatesList?.map(
      (item: { salestaxcode_id: number }) => item?.salestaxcode_id
    );
    const filterList = allSalesTaxCode?.filter(
      (item: { label: string; value: string }) =>
        ![...salesTaxCodeId, Number(id)]?.includes(Number(item?.value))
    );
    return filterList;
  }, [salesTaxRatesList, allSalesTaxCode, id]);

  const baseRate = watch('salestaxrate');
  // total sale tax rate
  const totalRates = useMemo(() => {
    const additionalRateTotal = salesTaxRatesList?.reduce(
      (acc: any, item: any) => acc + item?.salestaxrate,
      0
    );
    return Number(baseRate || 0) + (additionalRateTotal || 0);
  }, [baseRate, salesTaxRatesList]);

  const taxrateid = form.watch('taxrateid');
  // add new additional rate
  const handleAddNewAdditionalRate = () => {
    const { salestaxcode_id, salestaxcode, salestaxrate } = salesTaxData?.find(
      (item: SalesTaxRatesTypes) => item?.salestaxcode_id === taxrateid
    );

    append({
      addnl_salestaxcode_id: salestaxcode_id,
      salestaxcode_id,
      salestaxrate,
      salestaxcode,
    });
    form.setValue('taxrateid', '');
    toggleAdditionalRates();
  };

  // delete additional sale tax
  const handleDeleteAdditionalRate = useCallback(async () => {
    additionalRateId !== null && remove(additionalRateId);
    toggleDelete();
  }, [additionalRateId, remove, toggleDelete]);

  // Memoized table columns
  const columns: ColumnDef<SalesTaxRatesTypes>[] = useMemo(
    () => [
      {
        accessorKey: 'salestaxcode',
        header: 'Sales Tax Code',
        size: 240,
        enableSorting: true,
        invertSorting: true,
      },
      {
        accessorKey: 'salestaxrate',
        header: 'Rate',
        size: 200,
        enableSorting: true,
        invertSorting: true,
        cell: ({ row }) => (
          <div className="flex items-center justify-between gap-3">
            <div>{convertToFloat({ value: row?.original?.salestaxrate })}</div>
            <ActionColumnMenu
              onDelete={() => toggleDelete(row?.index)}
              contentClassName="z-[99] w-fit"
            />
          </div>
        ),
      },
    ],
    [toggleDelete]
  );

  return (
    <div>
      <div className="grid grid-cols-2 gap-4">
        <InputField
          form={form}
          name="salestaxcode"
          label="Sales Tax Code"
          placeholder="Sales Tax Code"
          validation={TEXT_VALIDATION_RULE}
        />

        <NumberInputField
          form={form}
          name="salestaxrate"
          label="Base Rate"
          placeholder="Base Rate"
          maxLength={8}
          fixedDecimalScale
          decimalScale={3}
          suffix="%"
        />
        <InputField
          form={form}
          name="salestaxdesc"
          label="Description"
          placeholder="Description"
        />
        <SelectWidget
          form={form}
          name="salestaxstate"
          label="State"
          optionsList={stateList}
          placeholder="Select State"
          isLoading={stateIsFetching}
          menuPosition="absolute"
        />
        <DatePicker
          form={form}
          name="effectivedate"
          label="Effective Date"
          placeholder="Effective Date"
          enableInput
        />
        <div className="items-center mt-8 flex">
          <SwitchField
            name="isactive"
            form={form}
            label="Active"
            className="w-fit"
          />
        </div>
        <div className="col-span-2 grid grid-cols-2 gap-4 p-3 border rounded-md">
          {SalesTaxCodeCheckList?.map((item, index: number) => {
            return (
              <div className="flex items-center gap-3 w-full justify-between">
                <Labels label={item?.label} />
                <div className="flex items-center gap-2">
                  {item?.description && (
                    <Popover>
                      <PopoverTrigger asChild>
                        <HelpCircleIcon
                          className="text-white w-6 h-6"
                          fill="#5d26d2"
                        />
                      </PopoverTrigger>
                      <PopoverContent
                        className="w-80 text-sm"
                        side="top"
                        hideWhenDetached={false}
                      >
                        {item?.description}
                      </PopoverContent>
                    </Popover>
                  )}
                  <SwitchField
                    key={`${item?.label}-${index}`}
                    form={form}
                    name={item?.name as any}
                  />
                </div>
              </div>
            );
          })}
        </div>
      </div>
      <div className="flex justify-between items-center col-span-2 mb-2 pt-4">
        <Label className="text-base font-bold">Additional Rates</Label>
        <AppButton
          className="text-left text-text-neutral-Default h-8"
          variant="neutral"
          label="+ Add Rate"
          onClick={toggleAdditionalRates}
        />
      </div>
      <DataTable
        columns={columns}
        data={salesTaxRatesList || []}
        totalItems={salesTaxRatesList?.length}
        enablePagination={false}
        tableClassName="max-h-40"
        sorting={sorting}
        setSorting={setSorting}
        manualSorting={false}
      />

      <div className="flex flex-row justify-end items-center col-span-2 gap-3 mt-3">
        <Label className="text-base font-normal">Total Rates</Label>
        <Label className="text-base font-normal">
          {convertToFloat({
            value: totalRates || 0,
            decimalPlaces: 3,
            postfix: '%',
          })}
        </Label>
      </div>

      <AppButton
        label="Submit"
        className="w-full col-span-2 mt-4"
        onClick={handleSubmit(onSubmit)}
        isLoading={isLoading}
      />

      <CustomDialog
        onOpenChange={toggleAdditionalRates}
        description=""
        open={isAdditionalRates}
        title={'Select Tax Code'}
        className="w-[30%]"
      >
        <div className="pl-6 pr-6">
          <SelectWidget
            form={form}
            name="taxrateid"
            optionsList={taxCodeList}
            placeholder="Tax Code"
            isLoading={optionLoading}
            menuPosition="absolute"
          />
          <AppButton
            label="Add Rate"
            className="w-full col-span-2 mt-4"
            disabled={!taxrateid}
            onClick={handleAddNewAdditionalRate}
          />
        </div>
      </CustomDialog>
      <AppConfirmationModal
        description={<div>Are you sure you want to delete this record?</div>}
        open={openModal}
        onOpenChange={() => toggleDelete()}
        handleCancel={() => toggleDelete()}
        handleSubmit={handleDeleteAdditionalRate}
      />
      <AppSpinner overlay isLoading={isFetching} />
    </div>
  );
};
