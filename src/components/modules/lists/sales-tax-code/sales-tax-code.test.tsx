import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { SalesTaxCode } from './index';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import {
  useGetListQuery,
  useAddNewItemMutation,
  useUpdateItemMutation,
  useDeleteItemMutation,
} from '@/redux/features/list/category/list.api';
import { useGetStateListQuery } from '@/redux/features/state/state.api';
import * as utils from '@/lib/utils';
import { commonApi } from '@/redux/features/common-api/common.api';

// Mock the Redux hooks
vi.mock('@/redux/features/list/category/list.api', () => ({
  useGetListQuery: vi.fn(),
  useAddNewItemMutation: vi.fn(),
  useUpdateItemMutation: vi.fn(),
  useDeleteItemMutation: vi.fn(),
}));

vi.mock('@/redux/features/state/state.api', () => ({
  useGetStateListQuery: vi.fn(),
}));

// Mock the utils module with all required exports
vi.mock('@/lib/utils', async () => {
  const actual = await import('@/lib/utils');
  return {
    ...actual,
    getQueryParam: vi.fn(() => null),
    convertToFloat: vi.fn(({ value }) => value),
    generateLabelValuePairs: vi.fn(() => []),
    cn: vi.fn(),
    getPaginationObject: vi.fn(() => ({
      pagination: { pageIndex: 0, pageSize: 10 },
      sorting: [],
    })),
  };
});

// Mock components
vi.mock('@/components/common/app-button', () => ({
  default: ({ label, onClick }: { label: string; onClick: () => void }) => (
    <button onClick={onClick}>{label}</button>
  ),
}));

vi.mock('@/components/common/app-confirmation-modal', () => ({
  default: ({
    open,
    handleSubmit,
  }: {
    open: boolean;
    handleSubmit: () => void;
  }) =>
    open ? (
      <div data-testid="confirmation-modal">
        <button onClick={handleSubmit}>Confirm</button>
      </div>
    ) : null,
}));

vi.mock('@/components/common/data-tables', () => ({
  default: ({ data }: { data: any[] }) => (
    <div data-testid="data-table">
      {data?.map((item, index) => <div key={index}>{item.salestaxcode}</div>)}
    </div>
  ),
}));

describe('SalesTaxCode Component', () => {
  const mockStore = configureStore({
    reducer: {
      [commonApi.reducerPath]: commonApi.reducer,
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware().concat(commonApi.middleware),
  });

  const mockToggle = vi.fn();

  beforeEach(() => {
    // Reset all mocks before each test
    vi.clearAllMocks();

    // Default mock implementations
    (useGetListQuery as any).mockImplementation(() => ({
      data: null,
      isFetching: false,
      refetch: vi.fn(),
    }));

    (useGetStateListQuery as any).mockImplementation(() => ({
      data: [],
      isFetching: false,
    }));

    (useAddNewItemMutation as any).mockImplementation(() => [
      vi.fn(),
      { isLoading: false },
    ]);

    (useUpdateItemMutation as any).mockImplementation(() => [
      vi.fn(),
      { isLoading: false },
    ]);

    (useDeleteItemMutation as any).mockImplementation(() => [
      vi.fn(),
      { isLoading: false },
    ]);

    (useGetListQuery as any).mockImplementation(({ url }: { url: string }) => {
      if (url.includes('additional-rate')) {
        return {
          data: { data: [] },
          isFetching: false,
        };
      }
      return {
        data: { data: null },
        isFetching: false,
      };
    });
  });

  it('renders the form with default fields', () => {
    render(
      <Provider store={mockStore}>
        <SalesTaxCode toggle={mockToggle} />
      </Provider>
    );
    expect(screen.getByText('Sales Tax Code')).toBeInTheDocument();
    expect(screen.getByText('Base Rate')).toBeInTheDocument();
    expect(screen.getByText('Description')).toBeInTheDocument();
    expect(screen.getByText('State')).toBeInTheDocument();
    expect(screen.getByText('Submit')).toBeInTheDocument();
  });

  it('displays loading state when fetching data', () => {
    (useGetListQuery as any).mockImplementation(() => ({
      data: null,
      isFetching: true,
    }));
    render(
      <Provider store={mockStore}>
        <SalesTaxCode toggle={mockToggle} />
      </Provider>
    );

    expect(screen.getByText('State')).toBeInTheDocument();
  });

  it('pre-fills form when id is present and data is loaded', async () => {
    const mockMainData = {
      salestaxcode: 'TEST123',
      salestaxrate: 5.5,
      salestaxdesc: 'Test description',
      isactive: true,
    };
    vi.mocked(utils.getQueryParam).mockImplementation(() => '123');
    (useGetListQuery as any).mockImplementation(({ url }: { url: string }) => {
      if (url.includes('additional-rate')) {
        return {
          data: mockMainData,
          isFetching: false,
        };
      }
      return {
        data: undefined,
        isFetching: false,
      };
    });
    const renderedDetails = render(
      <Provider store={mockStore}>
        <SalesTaxCode toggle={mockToggle} />
      </Provider>
    );
    expect(renderedDetails);
  });

  it('calls updateItem when form is submitted with id', async () => {
    const mockUpdateItem = vi.fn();
    (useUpdateItemMutation as any).mockImplementation(() => [
      mockUpdateItem,
      { isLoading: false },
    ]);
    vi.mocked(utils.getQueryParam).mockImplementation(() => '123');
    render(
      <Provider store={mockStore}>
        <SalesTaxCode toggle={mockToggle} />
      </Provider>
    );
    fireEvent.click(screen.getByText('Submit'));
    await waitFor(() => {
      expect(mockUpdateItem);
    });
  });

  it('calls addNewItem when form is submitted without id', async () => {
    const mockAddNewItem = vi.fn();
    (useAddNewItemMutation as any).mockImplementation(() => [
      mockAddNewItem,
      { isLoading: false },
    ]);
    vi.mocked(utils.getQueryParam).mockImplementation(() => null);
    render(
      <Provider store={mockStore}>
        <SalesTaxCode toggle={mockToggle} />
      </Provider>
    );
    fireEvent.click(screen.getByText('Submit'));
    await waitFor(() => {
      expect(mockAddNewItem);
    });
  });

  it('opens additional rates dialog when "Add Rate" is clicked', async () => {
    vi.mocked(utils.getQueryParam).mockImplementation(() => '123');
    render(
      <Provider store={mockStore}>
        <SalesTaxCode toggle={mockToggle} />
      </Provider>
    );
    fireEvent.click(screen.getByText('+ Add Rate'));
    await waitFor(() => {
      expect(screen.getByText('Select Tax Code')).toBeInTheDocument();
    });
  });

  it('shows confirmation modal when deleting additional rate', async () => {
    const mockAdditionalRateData = [
      { salestaxrates_id: 1, salestaxcode: 'RATE1', salestaxrate: 2.5 },
    ];
    (useGetListQuery as any).mockImplementation(({ url }: { url: string }) => {
      if (url.includes('additional-rate')) {
        return { data: { data: mockAdditionalRateData }, isFetching: false };
      }
      return { data: null, isFetching: false };
    });
    vi.mocked(utils.getQueryParam).mockImplementation(() => '123');
    render(
      <Provider store={mockStore}>
        <SalesTaxCode toggle={mockToggle} />
      </Provider>
    );
    await waitFor(() => {
      expect(screen.getByTestId('data-table')).toBeInTheDocument();
    });
  });

  it('displays all checkboxes from SalesTaxCodeCheckList', () => {
    render(
      <Provider store={mockStore}>
        <SalesTaxCode toggle={mockToggle} />
      </Provider>
    );
    expect(screen.getByText('Tax Delivery')).toBeInTheDocument();
    expect(screen.getByText('Tax Labor')).toBeInTheDocument();
    expect(screen.getByText('Tax Damage Waiver')).toBeInTheDocument();
    expect(screen.getByText('Tax Fuel Surcharge')).toBeInTheDocument();
    expect(screen.getByText('Tax Production Fee')).toBeInTheDocument();
    expect(screen.getByText('Tax Expedited Fee')).toBeInTheDocument();
    expect(screen.getByText('Tax Convenience Fee')).toBeInTheDocument();
    expect(screen.getByText('Selectable')).toBeInTheDocument();
    expect(screen.getByText('Transaction')).toBeInTheDocument();
    expect(
      screen.getByText('Tax Convenience Fees For Taxable Sales Only')
    ).toBeInTheDocument();
  });
});
