import AppButton from '@/components/common/app-button';
import AppSpinner from '@/components/common/app-spinner';
import DataTable from '@/components/common/data-tables';
import InputField from '@/components/forms/input-field';
import { PACKING_LIST_DEPARTMENTS_API_ROUTES } from '@/constants/api-constants';
import { TEXT_VALIDATION_RULE } from '@/constants/validation-constants';
import { getQueryParam } from '@/lib/utils';
import {
  useGetListQuery,
  useUpdateItemMutation,
} from '@/redux/features/list/category/list.api';
import { PackingListDepartmentsFormType } from '@/types/list.types';
import { RowSelectionState } from '@tanstack/react-table';
import { useEffect, useMemo, useState } from 'react';
import { SubmitHandler, useForm, UseFormReturn } from 'react-hook-form';

// PackingListDepartmentsForm Component
export const PackingListDepartments = ({ toggle }: { toggle?: () => void }) => {
  const id = getQueryParam('id');
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const [updateItem, { isLoading }] = useUpdateItemMutation();

  const { data, isFetching } = useGetListQuery(
    {
      url: PACKING_LIST_DEPARTMENTS_API_ROUTES?.GET(id),
    },
    { skip: !id }
  );

  const defaultValues = useMemo(() => {
    const dataValue = data?.data;
    return {
      ...dataValue,
      id: dataValue?.id || 0,
      name: dataValue?.name ?? '',
    };
  }, [data]);

  const form: UseFormReturn<PackingListDepartmentsFormType> =
    useForm<PackingListDepartmentsFormType>({
      defaultValues,
      mode: 'onChange',
    });
  const { handleSubmit, reset } = form;

  useEffect(() => {
    if (defaultValues) {
      reset(defaultValues);
    }
  }, [defaultValues, reset]);

  const onSubmit: SubmitHandler<PackingListDepartmentsFormType> = async () => {
    const payload = data?.data?.departments?.map((dept: any) => ({
      id: id || 0,
      deptId: dept.deptId || 0,
      deptDescription: dept.deptDescription || '',
      isSelected: !!rowSelection[dept.deptId],
    }));

    try {
      if (id) {
        await updateItem({
          url: PACKING_LIST_DEPARTMENTS_API_ROUTES.UPDATE(id),
          data: payload,
        }).unwrap();
      }
      toggle?.();
    } catch (error) {
      // Handle error (optional)
    }
  };

  useEffect(() => {
    if (data && data?.data?.departments) {
      const initialRowSelection: RowSelectionState = {};
      data?.data?.departments?.forEach((item: any) => {
        if (item.isSelected) {
          initialRowSelection[item.deptId] = true;
        }
      });
      setRowSelection(initialRowSelection);
    }
  }, [data]);

  return (
    <div>
      <div className="grid grid-cols-1 gap-4">
        <InputField
          form={form}
          name="name"
          label="Form Type"
          placeholder="Enter Form Type"
          validation={TEXT_VALIDATION_RULE}
          disabled
        />

        <DataTable
          columns={[
            {
              accessorKey: 'deptDescription',
              header: 'Departments',
              size: 150,
            },
          ]}
          data={data?.data?.departments || []}
          enablePagination={false}
          tableClassName="max-h-80"
          enableRowSelection
          rowSelection={rowSelection}
          onRowSelectionChange={setRowSelection}
          bindingKey="deptId"
          enableMultiRowSelection
        />
      </div>

      <AppButton
        label="Submit"
        className="w-full col-span-2 mt-4"
        onClick={handleSubmit(onSubmit)}
        isLoading={isLoading}
      />
      <AppSpinner overlay isLoading={isFetching} />
    </div>
  );
};
