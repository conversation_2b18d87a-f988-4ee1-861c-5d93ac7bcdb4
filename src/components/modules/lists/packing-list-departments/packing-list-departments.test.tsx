import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, vi, beforeEach, expect } from 'vitest';
import { PackingListDepartments } from './index';
import {
  useGetListQuery,
  useUpdateItemMutation,
} from '@/redux/features/list/category/list.api';
import { PACKING_LIST_DEPARTMENTS_API_ROUTES } from '@/constants/api-constants';
import { getQueryParam } from '@/lib/utils';

// Corrected mocking of modules
vi.mock('@/redux/features/list/category/list.api', async () => {
  const actual = await import('@/redux/features/list/category/list.api');
  return {
    ...actual,
    useGetListQuery: vi.fn(),
    useUpdateItemMutation: vi.fn(() => [vi.fn(), { isLoading: false }]),
  };
});

vi.mock('@/lib/utils', async () => {
  const actual = await import('@/lib/utils');
  return {
    ...actual,
    getQueryParam: vi.fn(),
    cn: vi.fn(),
  };
});

describe('PackingListDepartments Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders correctly with fetched data', async () => {
    (getQueryParam as any).mockReturnValue('123');
    (useGetListQuery as any).mockReturnValue({
      data: {
        data: {
          id: 123,
          name: 'Test Packing List',
          departments: [
            { deptId: 1, deptDescription: 'Dept 1', isSelected: true },
            { deptId: 2, deptDescription: 'Dept 2', isSelected: false },
          ],
        },
      },
      isFetching: false,
    });

    render(<PackingListDepartments />);

    expect(await screen.findByPlaceholderText('Enter Form Type')).toHaveValue(
      'Test Packing List'
    );
    expect(await screen.findByText('Dept 1')).toBeInTheDocument();
    expect(await screen.findByText('Dept 2')).toBeInTheDocument();
  });

  it('calls update mutation on submit', async () => {
    const mockUpdate = vi.fn().mockResolvedValue({});
    (getQueryParam as any).mockReturnValue('123');
    (useGetListQuery as any).mockReturnValue({
      data: {
        data: {
          id: 123,
          name: 'Test Packing List',
          departments: [
            { deptId: 1, deptDescription: 'Dept 1', isSelected: true },
          ],
        },
      },
      isFetching: false,
    });
    (useUpdateItemMutation as any).mockReturnValue([
      mockUpdate,
      { isLoading: false },
    ]);

    render(<PackingListDepartments />);

    fireEvent.click(screen.getByText('Submit'));

    await waitFor(() => {
      expect(mockUpdate).toHaveBeenCalledWith({
        url: PACKING_LIST_DEPARTMENTS_API_ROUTES.UPDATE('123'),
        data: [
          { id: '123', deptId: 1, deptDescription: 'Dept 1', isSelected: true },
        ],
      });
    });
  });
});
