import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { BrowserRouter as Router } from 'react-router-dom';
import Lists from './index';
import { listData } from '@/mock-data/list-mock-data';
vi.mock('@/components/common/data-tables/index', () => ({
  default: ({ data, columns, heading }: any) => (
    <div>
      <h2>{heading}</h2>
      {data.map((item: any, index: number) => (
        <div key={index}>
          {columns.map((column: any) => (
            <div key={column.accessorKey}>{item[column.accessorKey]}</div>
          ))}
        </div>
      ))}
    </div>
  ),
}));

describe('Lists Component', () => {
  it('renders the heading correctly', () => {
    render(
      <Router>
        <Lists />
      </Router>
    );
    expect(screen.getByText('Lists')).toBeInTheDocument();
  });

  it('renders the correct list data in DataTable', () => {
    render(
      <Router>
        <Lists />
      </Router>
    );
    listData.forEach((item) => {
      expect(screen.getByText(item.listName)).toBeInTheDocument();
    });
  });

  it('contains links with correct paths', () => {
    render(
      <Router>
        <Lists />
      </Router>
    );
    listData.forEach((item) => {
      const linkElement = screen.getByText(item.listName).closest('a');
      expect(linkElement);
    });
  });
});
