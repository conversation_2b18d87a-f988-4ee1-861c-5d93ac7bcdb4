import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import AllLists from './allLists';

// Mock the modules with named exports
vi.mock('./bank-accounts', () => ({
  BankAccounts: () => <div>Bank Accounts</div>,
}));

vi.mock('./categories', () => ({
  CategoryForm: () => <div>Category Form</div>,
}));

vi.mock('./checklist-items', () => ({
  ChecklistItems: () => <div>Checklist Items</div>,
}));

vi.mock('./payment-terms', () => ({
  PaymentTerms: () => <div>Payment Terms</div>,
}));

vi.mock('./customer-types', () => ({
  CustomerTypes: () => <div>Customer Types</div>,
}));

vi.mock('./delivery-types', () => ({
  DeliveryType: () => <div>Delivery Type</div>,
}));

describe('AllLists Component', () => {
  it('renders the correct component based on the name prop', () => {
    const toggleMock = vi.fn();

    render(<AllLists name="bank-accounts" toggle={toggleMock} />);
    expect(screen.getByText('Bank Accounts')).toBeInTheDocument();

    render(<AllLists name="categories" toggle={toggleMock} />);
    expect(screen.getByText('Category Form')).toBeInTheDocument();

    render(<AllLists name="checklist-items" toggle={toggleMock} />);
    expect(screen.getByText('Checklist Items')).toBeInTheDocument();

    render(<AllLists name="payment-terms" toggle={toggleMock} />);
    expect(screen.getByText('Payment Terms')).toBeInTheDocument();

    render(<AllLists name="customer-types" toggle={toggleMock} />);
    expect(screen.getByText('Customer Types')).toBeInTheDocument();

    render(<AllLists name="delivery-types" toggle={toggleMock} />);
    expect(screen.getByText('Delivery Type')).toBeInTheDocument();
  });
});
