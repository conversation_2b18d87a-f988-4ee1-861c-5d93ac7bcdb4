import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { PaymentTerms } from './index';
import { vi, describe, it, expect } from 'vitest';
import {
  useGetListQuery,
  useAddNewItemMutation,
  useUpdateItemMutation,
} from '@/redux/features/list/category/list.api';
import { PAYMENT_TERM_API_ROUTES } from '@/constants/api-constants';
import * as utils from '@/lib/utils';

vi.mock('@/redux/features/list/category/list.api', () => ({
  useGetListQuery: vi.fn(),
  useAddNewItemMutation: vi.fn(),
  useUpdateItemMutation: vi.fn(),
}));

vi.mock('@/lib/utils', () => ({
  getQueryParam: vi.fn(),
  cn: vi.fn(),
}));

describe('PaymentTerms Component', () => {
  it('should render and submit the form correctly for adding a new item', async () => {
    const mockAddNewItem = vi.fn();
    const mockToggle = vi.fn();
    vi.spyOn(utils, 'getQueryParam').mockReturnValue(null);

    (useGetListQuery as any).mockReturnValue({ data: null, isFetching: false });
    (useAddNewItemMutation as any).mockReturnValue([
      mockAddNewItem,
      { isLoading: false },
    ]);
    (useUpdateItemMutation as any).mockReturnValue([
      vi.fn(),
      { isLoading: false },
    ]);

    render(<PaymentTerms toggle={mockToggle} />);

    const descriptionInput = screen.getByPlaceholderText('Description');
    fireEvent.change(descriptionInput, {
      target: { value: 'New Payment Term' },
    });

    const submitButton = screen.getByText('Submit');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockAddNewItem).toHaveBeenCalledWith({
        url: PAYMENT_TERM_API_ROUTES.CREATE,
        data: { term: 'New Payment Term', paymentterm_id: 0 },
      });
      expect(mockToggle);
    });
  });

  it('should render and submit the form correctly for updating an existing item', async () => {
    const mockUpdateItem = vi.fn();
    const mockToggle = vi.fn();
    const mockData = {
      data: { term: 'Existing Payment Term', paymentterm_id: 1 },
    };
    vi.spyOn(utils, 'getQueryParam').mockReturnValue('1');

    (useGetListQuery as any).mockReturnValue({
      data: mockData,
      isFetching: false,
    });
    (useUpdateItemMutation as any).mockReturnValue([
      mockUpdateItem,
      { isLoading: false },
    ]);

    render(<PaymentTerms toggle={mockToggle} />);

    const descriptionInput = screen.getByPlaceholderText('Description');
    fireEvent.change(descriptionInput, {
      target: { value: 'Updated Payment Term' },
    });

    const submitButton = screen.getByText('Submit');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockUpdateItem).toHaveBeenCalledWith({
        url: PAYMENT_TERM_API_ROUTES.UPDATE('1'),
        data: { term: 'Updated Payment Term', paymentterm_id: 1 },
      });
      expect(mockToggle);
    });
  });

  it('should show the loading spinner when data is fetching', () => {
    const mockAddNewItem = vi.fn();
    const mockToggle = vi.fn();

    (useGetListQuery as any).mockReturnValue({ data: null, isFetching: true });
    (useAddNewItemMutation as any).mockReturnValue([
      mockAddNewItem,
      { isLoading: false },
    ]);

    render(<PaymentTerms toggle={mockToggle} />);

    const spinner = screen.getByTestId('spinner');
    expect(spinner).toBeInTheDocument();
  });
});
