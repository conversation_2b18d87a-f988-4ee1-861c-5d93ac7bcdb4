import AppButton from '@/components/common/app-button';
import AppSpinner from '@/components/common/app-spinner';
import InputField from '@/components/forms/input-field';
import { PAYMENT_TERM_API_ROUTES } from '@/constants/api-constants';
import { TEXT_VALIDATION_RULE } from '@/constants/validation-constants';
import { getQueryParam } from '@/lib/utils';
import {
  useAddNewItemMutation,
  useGetListQuery,
  useUpdateItemMutation,
} from '@/redux/features/list/category/list.api';
import { PaymentTermsFormType } from '@/types/list.types';
import { useEffect, useMemo } from 'react';
import { SubmitHandler, useForm, UseFormReturn } from 'react-hook-form';

// CategoryForm Component
export const PaymentTerms = ({ toggle }: { toggle?: () => void }) => {
  const id = getQueryParam('id');

  const { data, isFetching } = useGetListQuery(
    {
      url: PAYMENT_TERM_API_ROUTES?.GET(id),
    },
    { skip: !id }
  );

  const [updateItem, { isLoading }] = useUpdateItemMutation();
  const [addNewItem, { isLoading: newItemLoading }] = useAddNewItemMutation();

  const defaultValues = useMemo(() => {
    const dataValue = data?.data;
    return { ...dataValue, paymentterm_id: dataValue?.paymentterm_id || 0 };
  }, [data]);

  const form: UseFormReturn<PaymentTermsFormType> =
    useForm<PaymentTermsFormType>({
      defaultValues,
      mode: 'onChange',
    });
  const { handleSubmit, reset } = form;

  useEffect(() => {
    if (defaultValues) {
      reset(defaultValues);
    }
  }, [defaultValues, reset]);

  const onSubmit: SubmitHandler<PaymentTermsFormType> = async (
    data: PaymentTermsFormType
  ) => {
    try {
      if (id) {
        await updateItem({
          url: PAYMENT_TERM_API_ROUTES.UPDATE(id),
          data,
        }).unwrap();
      } else {
        await addNewItem({
          url: PAYMENT_TERM_API_ROUTES.CREATE,
          data,
        }).unwrap();
      }
      toggle?.();
    } catch (error) {
      // Handle error (optional)
    }
  };

  return (
    <div>
      <div className="grid grid-cols-1 gap-4">
        <InputField
          form={form}
          name="term"
          label="Description"
          placeholder="Description"
          validation={TEXT_VALIDATION_RULE}
        />
      </div>

      <AppButton
        label="Submit"
        className="w-full col-span-2 mt-4"
        onClick={handleSubmit(onSubmit)}
        isLoading={isLoading || newItemLoading}
      />
      <AppSpinner overlay isLoading={isFetching} />
    </div>
  );
};
