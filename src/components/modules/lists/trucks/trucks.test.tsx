import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen } from '@testing-library/react';
import { Trucks } from './index';
import {
  listApi,
  useAddNewItemMutation,
  useGetListQuery,
  useUpdateItemMutation,
} from '@/redux/features/list/category/list.api';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { storeApi } from '@/redux/features/store/store.api';
import { setupListeners } from '@reduxjs/toolkit/query';
// import { useGetStoreLocationsQuery } from '@/redux/features/store/store.api';

// Mock the API hooks
vi.mock('@/redux/features/list/category/list.api', async () => {
  const actual = await vi.importActual<
    typeof import('@/redux/features/list/category/list.api')
  >('@/redux/features/list/category/list.api');

  return {
    ...actual,
    useGetListQuery: vi.fn(),
    useUpdateItemMutation: vi.fn(),
    useAddNewItemMutation: vi.fn(),
  };
});

vi.mock('@/redux/features/customers/customer.api', () => ({
  useGetStoreLocationsQuery: vi.fn(),
}));

vi.mock('@/lib/utils', () => ({
  getQueryParam: vi.fn(),
  generateLabelValuePairs: vi.fn(),
  cn: vi.fn(),
}));

// Mock data
const mockTruckData = {
  data: {
    id: 1,
    truckName: 'Test Truck',
    location: 'All Locations',
    defaultRouteDesc: 'Test Route',
    allowedWeight: 1000,
  },
};

// const mockLocations = {
//   data: [{ location: 'Location 1' }, { location: 'Location 2' }],
// };

// Setup function to configure mocks
const setupMocks = (isEdit = false) => {
  (useGetListQuery as any).mockReturnValue({
    data: isEdit ? mockTruckData : null,
    isFetching: false,
  });

  (useUpdateItemMutation as any).mockReturnValue([
    vi.fn().mockResolvedValue({}),
    { isLoading: false },
  ]);

  (useAddNewItemMutation as any).mockReturnValue([
    vi.fn().mockResolvedValue({}),
    { isLoading: false },
  ]);

  // (useGetStoreLocationsQuery as any).mockReturnValue({
  //   data: mockLocations,
  //   isLoading: false,
  // });
};

const createTestStore = () => {
  const store = configureStore({
    reducer: {
      [listApi.reducerPath]: listApi.reducer,
      [storeApi.reducerPath]: storeApi.reducer,
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware()
        .concat(listApi.middleware)
        .concat(storeApi.middleware),
  });

  setupListeners(store.dispatch);
  return store;
};

const renderWithProvider = (ui: React.ReactElement) => {
  const store = createTestStore();
  return render(<Provider store={store}>{ui}</Provider>);
};

// Test suite
describe('Trucks Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the form with empty values in create mode', () => {
    setupMocks(false);
    const mockToggle = vi.fn();
    renderWithProvider(<Trucks toggle={mockToggle} />);

    expect(screen.getByPlaceholderText('Truck Name')).toBeInTheDocument();
    expect(
      screen.getByPlaceholderText('Default Route Description')
    ).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Allowed Weight')).toBeInTheDocument();
    expect(screen.getByText('Submit')).toBeInTheDocument();
  });

  it('loads and displays existing truck data in edit mode', async () => {
    setupMocks(true);
    const mockToggle = vi.fn();
    renderWithProvider(<Trucks toggle={mockToggle} />);

    expect(screen.getByPlaceholderText('Truck Name')).toBeInTheDocument();
    expect(
      screen.getByPlaceholderText('Default Route Description')
    ).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Allowed Weight')).toBeInTheDocument();
  });
});
