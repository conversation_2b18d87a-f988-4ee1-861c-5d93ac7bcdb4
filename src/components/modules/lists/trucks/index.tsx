import AppButton from '@/components/common/app-button';
import AppSpinner from '@/components/common/app-spinner';
import InputField from '@/components/forms/input-field';
import NumberInputField from '@/components/forms/number-input-field';
import SelectDropDown from '@/components/forms/select-dropdown';
import { TRUCKS_API_ROUTES } from '@/constants/api-constants';
import { TEXT_VALIDATION_RULE } from '@/constants/validation-constants';
import { generateLabelValuePairs, getQueryParam } from '@/lib/utils';
import { useGetStoreLocationsQuery } from '@/redux/features/store/store.api';
import {
  useAddNewItemMutation,
  useGetListQuery,
  useUpdateItemMutation,
} from '@/redux/features/list/category/list.api';
import { TrucksType } from '@/types/list.types';
import { useEffect, useMemo } from 'react';
import { <PERSON>mit<PERSON><PERSON><PERSON>, UseFormReturn, useForm } from 'react-hook-form';

// TrucksForm Component
export const Trucks = ({ toggle }: { toggle?: (value?: any) => void }) => {
  const id = getQueryParam('id');

  const { data, isFetching } = useGetListQuery(
    {
      url: TRUCKS_API_ROUTES?.GET(id),
    },
    { skip: !id }
  );

  const [updateItem, { isLoading }] = useUpdateItemMutation();
  const [addNewItem, { isLoading: newItemLoading }] = useAddNewItemMutation();
  const { data: locationData, isLoading: locationLoaidng } =
    useGetStoreLocationsQuery();

  const defaultValues = useMemo(() => {
    const dataValue = data?.data ?? {};
    return {
      ...dataValue,
      id: dataValue?.id || 0,
      location: dataValue?.location || 'All Locations',
    };
  }, [data]);

  const form: UseFormReturn<TrucksType> = useForm<TrucksType>({
    defaultValues,
    mode: 'onChange',
  });

  const onSubmit: SubmitHandler<TrucksType> = async (data: TrucksType) => {
    try {
      const payload = {
        ...data,
        allowedWeight: data?.allowedWeight || null,
        locationNo: 0,
      };
      if (id) {
        await updateItem({
          url: TRUCKS_API_ROUTES.UPDATE(id),
          data: payload,
        }).unwrap();
      } else {
        await addNewItem({
          url: TRUCKS_API_ROUTES.CREATE,
          data: payload,
        }).unwrap();
      }
      toggle?.();
    } catch (error) {
      // Error optional
    }
  };

  useEffect(() => {
    form.reset(defaultValues);
  }, [defaultValues, form]);

  const storeLocaitonList = generateLabelValuePairs({
    data: locationData?.data,
    labelKey: 'location',
    valueKey: 'location',
  });

  const locationList = storeLocaitonList?.length
    ? [
        ...[{ label: 'All Locations', value: 'All Locations' }],
        ...storeLocaitonList,
      ]
    : [];

  return (
    <div>
      <div className="grid grid-cols-2 gap-4">
        <InputField
          form={form}
          name="truckName"
          label="Truck Name"
          placeholder="Truck Name"
          maxLength={32}
          validation={TEXT_VALIDATION_RULE}
        />
        <SelectDropDown
          form={form}
          optionsList={locationList}
          name="location"
          label="Location"
          placeholder="Select Location"
          isLoading={locationLoaidng}
        />
        <div className="col-span-2">
          <InputField
            form={form}
            name="defaultRouteDesc"
            maxLength={32}
            label="Default Route Description"
            placeholder="Default Route Description"
          />
        </div>
        <NumberInputField
          form={form}
          name="allowedWeight"
          label="Allowed Weight (lbs)"
          placeholder="Allowed Weight"
          maxLength={8}
          fixedDecimalScale
        />

        <AppButton
          label="Submit"
          className="w-full col-span-2 mt-4"
          onClick={form.handleSubmit(onSubmit)}
          isLoading={isLoading || newItemLoading}
        />
      </div>
      <AppSpinner overlay isLoading={isFetching} />
    </div>
  );
};
