import AppButton from '@/components/common/app-button';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import AppSpinner from '@/components/common/app-spinner';
import SwitchField from '@/components/common/switch';
import InputField from '@/components/forms/input-field';
import SelectDropDown from '@/components/forms/select-dropdown';

import ActionColumnMenu from '@/components/common/data-tables/ActionColumn';
import NumberInputField from '@/components/forms/number-input-field';
import { Label } from '@/components/ui/label';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  CATEGORY_API_ROUTES,
  DEPARTMENT_API_ROUTES,
} from '@/constants/api-constants';
import {
  REQUIRED_TEXT,
  TEXT_VALIDATION_RULE,
} from '@/constants/validation-constants';
import useOptionList from '@/hooks/useOptionList';
import { getQueryParam } from '@/lib/utils';
import {
  useAddNewItemMutation,
  useDeleteItemMutation,
  useGetListQuery,
} from '@/redux/features/list/category/list.api';
import { CategoryFormType, RentDto } from '@/types/list.types';
import { useCallback, useEffect, useMemo, useState } from 'react';
import {
  SubmitHandler,
  useFieldArray,
  useForm,
  UseFormReturn,
} from 'react-hook-form';

// RentTable Component
const RentTable = ({
  fields,
  form,
  onDelete,
}: {
  fields: RentDto[];
  form: UseFormReturn<CategoryFormType>;
  onDelete: (id: number, isRemove?: boolean) => void;
}) => (
  <div className="col-span-2 border border-grayScale-20 lg:max-h-56 overflow-auto overflow_initial_table">
    <Table className="rounded-sm">
      <TableHeader
        className="bg-grayScale-10"
        style={{
          position: 'sticky',
          top: 0,
          background: '#f4f4f4',
          zIndex: 1,
        }}
      >
        <TableRow className="text-base font-medium">
          <TableHead className="text-grayScale-90 border-r-[1px] border-grayScale-20">
            Rent Days From
            <span className="text-red-500"> *</span>
          </TableHead>
          <TableHead className="text-grayScale-90 border-r-[1px] border-grayScale-20">
            Rent Days To <span className="text-red-500"> *</span>
          </TableHead>
          <TableHead className="text-grayScale-90 border-grayScale-20">
            Rate Adjustment
            <span className="text-red-500"> *</span>
          </TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {fields?.map((field, index: number) => {
          return (
            <TableRow key={index}>
              <TableCell className="border-r-[1px] border-grayScale-20">
                <NumberInputField
                  form={form}
                  name={`rateAdjs.${index}.rentDaysFrom`}
                  placeholder="Rent Days From"
                  validation={TEXT_VALIDATION_RULE}
                  maxLength={4}
                />
              </TableCell>
              <TableCell className="border-r-[1px] border-grayScale-20">
                <NumberInputField
                  form={form}
                  maxLength={4}
                  name={`rateAdjs.${index}.rentDaysTo`}
                  placeholder="Rent Days To"
                  validation={TEXT_VALIDATION_RULE}
                />
              </TableCell>
              <TableCell className="flex flex-row items-center gap-x-1">
                <NumberInputField
                  form={form}
                  name={`rateAdjs.${index}.rateAdj`}
                  placeholder="___.__"
                  maxLength={6}
                  validation={TEXT_VALIDATION_RULE}
                  fixedDecimalScale
                />
                <ActionColumnMenu
                  onDelete={() =>
                    onDelete(
                      Number(field?.rateAdjId) || index,
                      Boolean(!field?.rateAdj)
                    )
                  }
                  contentClassName="z-[99] w-fit"
                />
              </TableCell>
            </TableRow>
          );
        })}
      </TableBody>
    </Table>
  </div>
);

// CategoryForm Component
export const CategoryForm = ({
  toggle,
}: {
  toggle?: (value?: any) => void;
}) => {
  const categoryId = getQueryParam('id');

  const { data, isFetching, refetch } = useGetListQuery(
    {
      url: CATEGORY_API_ROUTES?.GET(categoryId),
    },
    { skip: !categoryId }
  );

  const [openModal, setOpenModal] = useState<boolean>(false);
  const [isRemove, setIsRemove] = useState<{ id: number; remove: boolean }>({
    id: 0,
    remove: false,
  });
  const [addEditItem, { isLoading: newItemLoading }] = useAddNewItemMutation();

  // delete rent table row
  const [deleteItem, { isLoading: isDeleteLoading }] = useDeleteItemMutation();

  const defaultValues = useMemo(() => {
    const dataValue = data?.data;
    return {
      ...dataValue,
      catNo: dataValue?.catNo,
      category_id: dataValue?.category_id || 0,
      isactive: categoryId ? dataValue?.isactive : true,
      rateAdjs: dataValue?.rateAdjs?.map((item: RentDto) => ({
        ...item,
        rateAdjId: item?.id,
      })),
    };
  }, [categoryId, data?.data]);

  const form: UseFormReturn<CategoryFormType> = useForm<CategoryFormType>({
    defaultValues,
    mode: 'onChange',
  });
  const { control, handleSubmit, reset } = form;
  const { fields, append, remove } = useFieldArray<CategoryFormType>({
    control,
    name: 'rateAdjs',
  });

  useEffect(() => {
    if (defaultValues) {
      reset(defaultValues);
    }
  }, [defaultValues, reset]);

  const onSubmit: SubmitHandler<CategoryFormType> = async (
    data: CategoryFormType
  ) => {
    const payload = {
      ...data,
      rateAdjs: data?.rateAdjs?.map(({ rateAdjId, ...item }) => item) || null,
      catNo: parseInt(data?.catNo),
    };
    try {
      await addEditItem({
        url: CATEGORY_API_ROUTES.CREATE,
        data: payload,
      }).unwrap();
      toggle?.(true);
    } catch (error) {
      // Handle error (optional)
    }
  };

  const { options: departmentOptions, optionLoading } = useOptionList({
    url: DEPARTMENT_API_ROUTES?.ALL,
    labelKey: 'deptDesc',
    valueKey: 'id',
    sortBy: 'deptDesc',
  });

  const toggleDelete = useCallback((id?: number, isRemove?: boolean) => {
    setOpenModal((prevState) => !prevState);
    setIsRemove({ id: id || 0, remove: isRemove ? isRemove : false });
  }, []);

  const handleDeleteRentTableRow = useCallback(async () => {
    if (isRemove?.remove) {
      remove(isRemove?.id);
    } else {
      await deleteItem({
        url: CATEGORY_API_ROUTES?.DELETE_RENT ?? '',
        itemId: isRemove?.id || '',
      }).unwrap();
      refetch();
    }
    setOpenModal((prevState) => !prevState);
    setIsRemove({ id: 0, remove: false });
  }, [deleteItem, isRemove?.id, isRemove?.remove, refetch, remove]);

  return (
    <div className="grid grid-cols-2 gap-4">
      <NumberInputField
        form={form}
        name="catNo"
        maxLength={5}
        label="Category #"
        placeholder="Category #"
        validation={{ required: REQUIRED_TEXT }}
      />

      <InputField
        form={form}
        name="catDesc"
        label="Description"
        placeholder="Description"
        validation={TEXT_VALIDATION_RULE}
      />
      <SelectDropDown
        form={form}
        optionsList={departmentOptions}
        label="Department"
        name="departmentId"
        placeholder="Department"
        isLoading={optionLoading}
        validation={TEXT_VALIDATION_RULE}
      />

      <SelectDropDown
        form={form}
        optionsList={[]}
        label="Income Account"
        name="incomeAcct"
        placeholder="Income Account"
      />
      <SwitchField
        name="isActive"
        form={form}
        label="Active"
        className="w-fit"
      />

      <div className="flex flex-row justify-between items-center col-span-2 mt-2">
        <Label className="text-base font-bold">Rent Table</Label>
        <AppButton
          className="text-left text-text-neutral-Default h-8"
          onClick={() => {
            append({
              id: null,
              rentDaysFrom: '',
              rentDaysTo: '',
              rateAdj: '',
            });
          }}
          variant="neutral"
          label="+ Add New"
        />
      </div>
      <RentTable fields={fields} form={form} onDelete={toggleDelete} />
      <AppButton
        label="Submit"
        className="w-full col-span-2 mt-4"
        onClick={handleSubmit(onSubmit)}
        isLoading={newItemLoading}
      />
      <AppConfirmationModal
        description={<div>Are you sure you want to delete this record?</div>}
        open={openModal}
        onOpenChange={() => toggleDelete()}
        handleCancel={() => toggleDelete()}
        handleSubmit={handleDeleteRentTableRow}
        isLoading={isDeleteLoading}
      />
      <AppSpinner overlay isLoading={isFetching} />
    </div>
  );
};
