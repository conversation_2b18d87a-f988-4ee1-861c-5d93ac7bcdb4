import { render, screen } from '@testing-library/react';
import { vi, describe, beforeEach, it, expect } from 'vitest';
import { CategoryForm } from './index';
import {
  useGetListQuery,
  useAddNewItemMutation,
  useDeleteItemMutation,
  listApi,
} from '@/redux/features/list/category/list.api';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';

vi.mock('@/redux/features/list/category/list.api', async () => {
  const actual = await import('@/redux/features/list/category/list.api');
  return {
    ...actual,
    useGetListQuery: vi.fn(),
    useAddNewItemMutation: vi.fn(),
    useDeleteItemMutation: vi.fn(),
    listApi: {
      ...actual.listApi,
      reducer: vi.fn().mockReturnValue({ data: [] }),
    },
  };
});

vi.mock('react-hook-form', () => {
  const actual = import('react-hook-form');
  return {
    ...actual,
    useForm: vi.fn().mockReturnValue({
      formState: {
        errors: {},
      },
      control: {},
      handleSubmit: vi.fn(),
      reset: vi.fn(),
      watch: vi.fn().mockReturnValue('mockedValue'),
      register: vi.fn(),
    }),
    useFieldArray: vi.fn().mockReturnValue({
      fields: [],
      append: vi.fn(),
      remove: vi.fn(),
    }),
    Controller: vi.fn().mockImplementation(({ render }) => {
      const mockField = {
        value: 'mockedValue',
        onChange: vi.fn(),
      };
      return render({ field: mockField });
    }),
  };
});

vi.mock('@/hooks/useOptionList', () => ({
  default: vi.fn(() => ({ options: [], optionLoading: false })),
}));

vi.mock('@/components/common/app-button', () => ({
  default: vi
    .fn()
    .mockImplementation(({ onClick }) => (
      <button onClick={onClick}>Submit</button>
    )),
}));

vi.mock('@/components/common/app-confirmation-modal', () => ({
  default: vi
    .fn()
    .mockImplementation(({ handleCancel, handleSubmit, description, open }) => (
      <div>
        {open && <div>{description}</div>}
        <button onClick={handleCancel}>Cancel</button>
        <button onClick={handleSubmit}>Confirm</button>
      </div>
    )),
}));

const store = configureStore({
  reducer: {
    listApi: listApi.reducer,
  },
});

describe('CategoryForm Component', () => {
  const mockToggle = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the form and submits correctly', async () => {
    (useGetListQuery as any).mockReturnValue({
      data: {
        data: {
          catNo: 1,
          catDesc: 'Test Category',
          isactive: true,
          rateAdjs: [],
        },
      },
      isFetching: false,
      refetch: vi.fn(),
    });
    (useAddNewItemMutation as any).mockReturnValue([
      vi.fn(),
      { isLoading: false },
    ]);
    (useDeleteItemMutation as any).mockReturnValue([
      vi.fn(),
      { isLoading: false },
    ]);

    render(
      <Provider store={store}>
        <CategoryForm toggle={mockToggle} />
      </Provider>
    );

    expect(screen.getByPlaceholderText('Category #')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Description')).toBeInTheDocument();
    expect(screen.getByText('Rent Table')).toBeInTheDocument();
  });

  it('opens confirmation modal and deletes the row', async () => {
    const mockDeleteItem = vi.fn();
    (useDeleteItemMutation as any).mockReturnValue([
      mockDeleteItem,
      { isLoading: false },
    ]);

    const isDelete = render(
      <Provider store={store}>
        <CategoryForm toggle={mockToggle} />
      </Provider>
    );

    expect(isDelete);
  });

  it('displays loading spinner when fetching data', async () => {
    (useGetListQuery as any).mockReturnValue({
      data: null,
      isFetching: true,
      refetch: vi.fn(),
    });

    render(
      <Provider store={store}>
        <CategoryForm toggle={mockToggle} />
      </Provider>
    );

    expect(screen.getByTestId('spinner')).toBeInTheDocument();
  });
});
