import AppButton from '@/components/common/app-button';
import AppSpinner from '@/components/common/app-spinner';
import SwitchField from '@/components/common/switch';
import InputField from '@/components/forms/input-field';
import NumberInputField from '@/components/forms/number-input-field';
import { CUSTOMER_TYPE_API_ROUTES } from '@/constants/api-constants';
import { TEXT_VALIDATION_RULE } from '@/constants/validation-constants';
import { getQueryParam } from '@/lib/utils';
import {
  useAddNewItemMutation,
  useGetListQuery,
  useUpdateItemMutation,
} from '@/redux/features/list/category/list.api';
import { CustomerTypesDto } from '@/types/list.types';
import { useEffect, useMemo } from 'react';
import { SubmitHandler, useForm, UseFormReturn } from 'react-hook-form';

// CategoryForm Component
export const CustomerTypes = ({ toggle }: { toggle?: () => void }) => {
  const id = getQueryParam('id');

  const { data, isFetching } = useGetListQuery(
    {
      url: CUSTOMER_TYPE_API_ROUTES?.GET(id),
    },
    { skip: !id }
  );

  const [updateItem, { isLoading }] = useUpdateItemMutation();
  const [addNewItem, { isLoading: newItemLoading }] = useAddNewItemMutation();

  const defaultValues = useMemo(() => {
    const dataValue = data?.data;
    return {
      ...dataValue,
      custtype_id: dataValue?.custtype_id || 0,
      isactive: id ? dataValue?.isactive : true,
    };
  }, [data?.data, id]);

  const form: UseFormReturn<CustomerTypesDto> = useForm<CustomerTypesDto>({
    defaultValues,
    mode: 'onChange',
  });
  const { handleSubmit, reset } = form;

  useEffect(() => {
    if (defaultValues) {
      reset(defaultValues);
    }
  }, [defaultValues, reset]);

  const onSubmit: SubmitHandler<CustomerTypesDto> = async (
    data: CustomerTypesDto
  ) => {
    const payload = { ...data, delcharge: Number(data?.delcharge) || null };
    try {
      if (id) {
        await updateItem({
          url: CUSTOMER_TYPE_API_ROUTES.UPDATE(id),
          data: payload,
        }).unwrap();
      } else {
        await addNewItem({
          url: CUSTOMER_TYPE_API_ROUTES.CREATE,
          data: payload,
        }).unwrap();
      }
      toggle?.();
    } catch (error) {
      // Handle error (optional)
    }
  };

  return (
    <div className="grid grid-cols-2 gap-4">
      <InputField
        form={form}
        name="code"
        label="Code"
        placeholder="Code"
        validation={TEXT_VALIDATION_RULE}
        maxLength={2}
      />
      <InputField
        form={form}
        name="description"
        label="Customer Type"
        placeholder="Customer Type"
      />

      <NumberInputField
        form={form}
        name="delcharge"
        label="Delivery Charge"
        placeholder="Delivery Charge"
        maxLength={11}
        prefix="$"
        fixedDecimalScale
        thousandSeparator=","
      />

      <SwitchField
        name="isactive"
        form={form}
        label="Active"
        className="w-fit mt-10"
      />
      <AppButton
        label="Submit"
        className="w-full col-span-2 mt-4"
        onClick={handleSubmit(onSubmit)}
        isLoading={isLoading || newItemLoading}
      />
      <AppSpinner overlay isLoading={isFetching} />
    </div>
  );
};
