import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, beforeEach, it, expect } from 'vitest';
import { CustomerTypes } from './index';
import {
  useAddNewItemMutation,
  useGetListQuery,
  useUpdateItemMutation,
} from '@/redux/features/list/category/list.api';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';

vi.mock('@/redux/features/list/category/list.api', async () => {
  const actual = await import('@/redux/features/list/category/list.api');
  return {
    ...actual,
    useGetListQuery: vi.fn(),
    useAddNewItemMutation: vi.fn(),
    useUpdateItemMutation: vi.fn(),
  };
});

vi.mock('@/components/common/app-button', () => ({
  default: vi
    .fn()
    .mockImplementation(({ onClick }) => (
      <button onClick={onClick}>Submit</button>
    )),
}));

vi.mock('@/components/common/app-spinner', () => ({
  default: vi.fn().mockImplementation(() => <div data-testid="spinner" />),
}));

const store = configureStore({
  reducer: {},
});

describe('CustomerTypes Component', () => {
  const mockToggle = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('submits the form and calls the appropriate API method', async () => {
    const mockAddNewItem = vi.fn();
    const mockUpdateItem = vi.fn();

    (useAddNewItemMutation as any).mockReturnValue([
      mockAddNewItem,
      { isLoading: false },
    ]);
    (useUpdateItemMutation as any).mockReturnValue([
      mockUpdateItem,
      { isLoading: false },
    ]);

    const mockData = {
      data: {
        custtype_id: 1,
        code: 'CT01',
        description: 'Test Customer Type',
        delcharge: 15,
        isactive: true,
      },
    };

    (useGetListQuery as any).mockReturnValue({
      data: mockData,
      isFetching: false,
    });

    render(
      <Provider store={store}>
        <CustomerTypes toggle={mockToggle} />
      </Provider>
    );

    fireEvent.change(screen.getByPlaceholderText('Code'), {
      target: { value: 'CT02' },
    });
    fireEvent.change(screen.getByPlaceholderText('Customer Type'), {
      target: { value: 'Updated Customer Type' },
    });
    fireEvent.change(screen.getByPlaceholderText('Delivery Charge'), {
      target: { value: '20' },
    });

    fireEvent.click(screen.getByText('Submit'));

    await waitFor(() => {
      expect(mockUpdateItem);
      expect(mockAddNewItem);
    });
    expect(mockToggle);
  });

  it('shows loading spinner when fetching data', async () => {
    (useGetListQuery as any).mockReturnValue({
      data: null,
      isFetching: true,
    });

    render(
      <Provider store={store}>
        <CustomerTypes toggle={mockToggle} />
      </Provider>
    );

    expect(screen.getByTestId('spinner')).toBeInTheDocument();
  });

  it('disables the submit button when the form is submitting', async () => {
    const mockAddNewItem = vi.fn();
    const mockUpdateItem = vi.fn();

    (useAddNewItemMutation as any).mockReturnValue([
      mockAddNewItem,
      { isLoading: true },
    ]);
    (useUpdateItemMutation as any).mockReturnValue([
      mockUpdateItem,
      { isLoading: true },
    ]);

    const mockData = {
      data: {
        custtype_id: 1,
        code: 'CT01',
        description: 'Test Customer Type',
        delcharge: 15,
        isactive: true,
      },
    };

    (useGetListQuery as any).mockReturnValue({
      data: mockData,
      isFetching: false,
    });

    render(
      <Provider store={store}>
        <CustomerTypes toggle={mockToggle} />
      </Provider>
    );

    fireEvent.click(screen.getByText('Submit'));

    expect(screen.getByText('Submit'));
  });
});
