import AppButton from '@/components/common/app-button';
import AppSpinner from '@/components/common/app-spinner';
import InputField from '@/components/forms/input-field';
import NumberInputField from '@/components/forms/number-input-field';
import { SURGE_RATES_API_ROUTES } from '@/constants/api-constants';
import { TEXT_VALIDATION_RULE } from '@/constants/validation-constants';
import { getQueryParam } from '@/lib/utils';
import {
  useAddNewItemMutation,
  useGetListQuery,
  useUpdateItemMutation,
} from '@/redux/features/list/category/list.api';
import { SurgeRatesType } from '@/types/list.types';
import { useEffect, useMemo } from 'react';
import { SubmitHandler, UseFormReturn, useForm } from 'react-hook-form';

// SurgeRatesForm Component
export const SurgeRates = ({ toggle }: { toggle?: (value?: any) => void }) => {
  const id = getQueryParam('id');

  const { data, isFetching } = useGetListQuery(
    {
      url: SURGE_RATES_API_ROUTES?.GET(id),
    },
    { skip: !id }
  );

  const [updateItem, { isLoading }] = useUpdateItemMutation();
  const [addNewItem, { isLoading: newItemLoading }] = useAddNewItemMutation();

  const defaultValues = useMemo(() => {
    const dataValue = data?.data ?? {};
    return {
      ...dataValue,
      rate: dataValue?.rate ? `${dataValue?.rate.toFixed(2)}%` : '',
      id: dataValue?.id || 0,
    };
  }, [data?.data]);

  const form: UseFormReturn<SurgeRatesType> = useForm<SurgeRatesType>({
    defaultValues,
    mode: 'onChange',
  });

  const onSubmit: SubmitHandler<SurgeRatesType> = async (
    data: SurgeRatesType
  ) => {
    const payload = {
      ...data,
      rate:
        Number(data.rate?.toString()?.replace(/%/g, '')?.replace(/_/g, '0')) ||
        null,
    };
    try {
      if (id) {
        await updateItem({
          url: SURGE_RATES_API_ROUTES.UPDATE(id),
          data: payload,
        }).unwrap();
      } else {
        await addNewItem({
          url: SURGE_RATES_API_ROUTES.CREATE,
          data: payload,
        }).unwrap();
      }
      toggle?.();
    } catch (error) {
      // Error optional
    }
  };

  useEffect(() => {
    form.reset(defaultValues);
  }, [defaultValues, form]);

  return (
    <div>
      <div className="grid grid-cols-2 gap-4">
        <InputField
          form={form}
          name="code"
          label="Code"
          placeholder="Code"
          maxLength={1}
          validation={TEXT_VALIDATION_RULE}
        />
        <InputField
          form={form}
          name="description"
          label="Description"
          placeholder="Description"
          validation={TEXT_VALIDATION_RULE}
        />
        <NumberInputField
          form={form}
          name="rate"
          label="Rate"
          placeholder="____.__%"
          maxLength={7}
          suffix="%"
          fixedDecimalScale
        />

        <AppButton
          label="Submit"
          className="w-full col-span-2 mt-4"
          onClick={form.handleSubmit(onSubmit)}
          isLoading={isLoading || newItemLoading}
        />
      </div>
      <AppSpinner overlay isLoading={isFetching} />
    </div>
  );
};
