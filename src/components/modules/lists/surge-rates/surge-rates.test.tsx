import { render, screen, fireEvent } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { SurgeRates } from './index';
import {
  useAddNewItemMutation,
  useGetListQuery,
  useUpdateItemMutation,
} from '@/redux/features/list/category/list.api';

// Mock the required hooks and components
vi.mock('@/redux/features/list/category/list.api', () => ({
  useAddNewItemMutation: vi.fn(),
  useUpdateItemMutation: vi.fn(),
  useGetListQuery: vi.fn(),
}));

vi.mock('@/components/forms/input-field', () => ({
  default: ({ form, name, label, placeholder, validation }: any) => (
    <input
      {...form.register(name, validation)}
      name={name}
      placeholder={placeholder}
      aria-label={label}
    />
  ),
}));

vi.mock('@/components/forms/number-input-field', () => ({
  default: ({ form, name, label, placeholder, maxLength }: any) => (
    <input
      {...form.register(name)}
      name={name}
      placeholder={placeholder}
      maxLength={maxLength}
      aria-label={label}
    />
  ),
}));

vi.mock('@/components/common/app-button', () => ({
  default: ({ label, onClick, isLoading }: any) => (
    <button onClick={onClick} disabled={isLoading}>
      {label}
    </button>
  ),
}));

vi.mock('@/components/common/app-spinner', () => ({
  default: () => <div>Loading...</div>,
}));

describe('SurgeRates Component', () => {
  const mockToggle = vi.fn();

  beforeEach(() => {
    (useGetListQuery as any).mockReturnValue({
      data: { data: { code: 'A1', description: 'Test', rate: 5.5, id: 1 } },
      isFetching: false,
    });

    (useAddNewItemMutation as any).mockReturnValue([
      vi.fn(),
      { isLoading: false },
    ]);
    (useUpdateItemMutation as any).mockReturnValue([
      vi.fn(),
      { isLoading: false },
    ]);
  });

  it('renders the SurgeRates form with pre-filled data', async () => {
    render(<SurgeRates toggle={mockToggle} />);
    expect(screen.getByPlaceholderText('Code')).toHaveValue('A1');
    expect(screen.getByPlaceholderText('Description')).toHaveValue('Test');
    expect(screen.getByPlaceholderText('____.__%')).toHaveValue('5.50%');
  });

  it('submits the form successfully with new data', async () => {
    const mockSubmit = vi.fn();

    render(<SurgeRates toggle={mockSubmit} />);

    const codeInput = screen.getByPlaceholderText('Code');
    const descriptionInput = screen.getByPlaceholderText('Description');
    const rateInput = screen.getByPlaceholderText('____.__%');
    const submitButton = screen.getByText('Submit');

    fireEvent.change(codeInput, { target: { value: 'B2' } });
    fireEvent.change(descriptionInput, {
      target: { value: 'Updated Description' },
    });
    fireEvent.change(rateInput, { target: { value: '10.25%' } });

    fireEvent.click(submitButton);

    expect(mockSubmit);
  });

  it('displays loading spinner when fetching data', async () => {
    (useGetListQuery as any).mockReturnValueOnce({
      data: null,
      isFetching: true,
    });

    render(<SurgeRates toggle={mockToggle} />);

    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('calls the toggle function on form submission', async () => {
    render(<SurgeRates toggle={mockToggle} />);

    const submitButton = screen.getByText('Submit');
    fireEvent.click(submitButton);

    expect(mockToggle);
  });
});
