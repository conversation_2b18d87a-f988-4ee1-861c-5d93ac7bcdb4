import { render, screen, fireEvent } from '@testing-library/react';
import { vi, describe, beforeEach, it, expect } from 'vitest';
import { DeliveryType } from './index';
import {
  useGetListQuery,
  useAddNewItemMutation,
  useUpdateItemMutation,
} from '@/redux/features/list/category/list.api';

vi.mock('@/redux/features/list/category/list.api', () => ({
  useGetListQuery: vi.fn(),
  useAddNewItemMutation: vi.fn(),
  useUpdateItemMutation: vi.fn(),
}));

describe('DeliveryType Component', () => {
  beforeEach(() => {
    (useGetListQuery as any).mockReturnValue({
      data: {
        data: {
          deliverytype_id: 1,
          name: 'Express Delivery',
          description: 'Fast delivery service',
          shiptransitdays: '3',
          isactive: true,
        },
      },
      isFetching: false,
    });
    (useAddNewItemMutation as any).mockReturnValue([
      vi.fn(),
      { isLoading: false },
    ]);
    (useUpdateItemMutation as any).mockReturnValue([
      vi.fn(),
      { isLoading: false },
    ]);
  });

  it('should render the DeliveryType form', () => {
    render(<DeliveryType />);

    expect(screen.getByLabelText('Delivery Types')).toBeInTheDocument();
    expect(screen.getByLabelText('Description')).toBeInTheDocument();
    expect(
      screen.getByLabelText('Shipping Days in Transit')
    ).toBeInTheDocument();
    expect(screen.getByText('Active')).toBeInTheDocument();
  });

  it('should handle form submission', async () => {
    const mockUpdateItem = vi.fn();
    const mockAddNewItem = vi.fn();

    (useUpdateItemMutation as any).mockReturnValue([
      mockUpdateItem,
      { isLoading: false },
    ]);
    (useAddNewItemMutation as any).mockReturnValue([
      mockAddNewItem,
      { isLoading: false },
    ]);

    render(<DeliveryType />);

    fireEvent.change(screen.getByLabelText('Delivery Types'), {
      target: { value: 'Standard Delivery' },
    });
    fireEvent.change(screen.getByLabelText('Description'), {
      target: { value: 'Standard delivery service' },
    });
    fireEvent.change(screen.getByLabelText('Shipping Days in Transit'), {
      target: { value: '5' },
    });
    fireEvent.click(screen.getByText('Active'));
    fireEvent.click(screen.getByText('Submit'));
    expect(mockUpdateItem);
  });

  it('should show loading spinner while data is being fetched', () => {
    (useGetListQuery as any).mockReturnValue({
      data: [],
      isFetching: true,
    });
    const isRenderLoader = render(<DeliveryType />);
    expect(isRenderLoader);
  });
});
