import AppButton from '@/components/common/app-button';
import AppSpinner from '@/components/common/app-spinner';
import SwitchField from '@/components/common/switch';
import InputField from '@/components/forms/input-field';
import { DELIVERY_TYPE_API_ROUTES } from '@/constants/api-constants';
import { TEXT_VALIDATION_RULE } from '@/constants/validation-constants';
import { getQueryParam } from '@/lib/utils';
import {
  useAddNewItemMutation,
  useGetListQuery,
  useUpdateItemMutation,
} from '@/redux/features/list/category/list.api';
import { DeliveryFormType } from '@/types/list.types';
import { useEffect, useMemo } from 'react';
import { SubmitHandler, useForm, UseFormReturn } from 'react-hook-form';

// CategoryForm Component
export const DeliveryType = ({ toggle }: { toggle?: () => void }) => {
  const id = getQueryParam('id');

  const { data, isFetching } = useGetListQuery(
    {
      url: DELIVERY_TYPE_API_ROUTES?.GET(id),
    },
    { skip: !id }
  );

  const [updateItem, { isLoading }] = useUpdateItemMutation();
  const [addNewItem, { isLoading: newItemLoading }] = useAddNewItemMutation();

  const defaultValues = useMemo(() => {
    const dataValue = data?.data;
    return {
      ...dataValue,
      deliverytype_id: dataValue?.deliverytype_id || 0,
      shiptransitdays: dataValue?.shiptransitdays,
      isactive: id ? dataValue?.isactive : true,
    };
  }, [data?.data, id]);

  const form: UseFormReturn<DeliveryFormType> = useForm<DeliveryFormType>({
    defaultValues,
    mode: 'onChange',
  });
  const { handleSubmit, reset } = form;

  useEffect(() => {
    if (defaultValues) {
      reset(defaultValues);
    }
  }, [defaultValues, reset]);

  const onSubmit: SubmitHandler<DeliveryFormType> = async (
    data: DeliveryFormType
  ) => {
    try {
      const body = {
        ...data,
        shiptransitdays: parseInt(data.shiptransitdays),
        presun: data?.presun || 0,
        postsun: data?.postsun || 0,
        premon: data?.premon || 0,
        postmon: data?.postmon || 0,
        pretue: data?.pretue || 0,
        posttue: data?.posttue || 0,
        prewed: data?.prewed || 0,
        postwed: data?.postwed || 0,
        prethur: data?.prethur || 0,
        postthur: data?.postthur || 0,
        prefri: data?.prefri || 0,
        postfri: data?.postfri || 0,
        presat: data?.presat || 0,
        postsat: data?.postsat || 0,
      };
      if (id) {
        await updateItem({
          url: DELIVERY_TYPE_API_ROUTES.UPDATE(id),
          data: body,
        }).unwrap();
      } else {
        await addNewItem({
          url: DELIVERY_TYPE_API_ROUTES.CREATE,
          data: body,
        }).unwrap();
      }
      toggle?.();
    } catch (error) {
      // Handle error (optional)
    }
  };

  const eventDays = useMemo(() => {
    return [
      {
        label: 'Sunday',
        preName: 'presun',
        postName: 'postsun',
        preLabel: 'Delivery',
        postLabel: 'Pickup',
      },
      {
        label: 'Monday',
        preName: 'premon',
        postName: 'postmon',
        preLabel: 'Delivery',
        postLabel: 'Pickup',
      },
      {
        label: 'Tuesday',
        preName: 'pretue',
        postName: 'posttue',
        preLabel: 'Delivery',
        postLabel: 'Pickup',
      },
      {
        label: 'Wednesday',
        preName: 'prewed',
        postName: 'postwed',
        preLabel: 'Delivery',
        postLabel: 'Pickup',
      },
      {
        label: 'Thursday',
        preName: 'prethur',
        postName: 'postthur',
        preLabel: 'Delivery',
        postLabel: 'Pickup',
      },
      {
        label: 'Friday',
        preName: 'prefri',
        postName: 'postfri',
        preLabel: 'Delivery',
        postLabel: 'Pickup',
      },
      {
        label: 'Saturday',
        preName: 'presat',
        postName: 'postsat',
        preLabel: 'Delivery',
        postLabel: 'Pickup',
      },
    ];
  }, []);

  return (
    <div>
      <div className="grid grid-cols-2 gap-4">
        <InputField
          form={form}
          name="name"
          label="Delivery Types"
          placeholder="Delivery Types"
          validation={TEXT_VALIDATION_RULE}
        />
        <InputField
          form={form}
          name="description"
          label="Description"
          placeholder="Description"
        />
        <InputField
          type="number"
          form={form}
          maxLength={3}
          name="shiptransitdays"
          label="Shipping Days in Transit"
          placeholder="Shipping Days in Transit"
        />
        <SwitchField
          name="isactive"
          form={form}
          label="Active"
          className="w-fit mt-10"
        />
      </div>
      <div className="grid grid-cols-3 gap-3 text-center pt-5 pb-2">
        <div></div>
        <div>Pre-Event Days</div>
        <div>Post-Event Days</div>
        <div></div>
        <div>(Deliver)</div>
        <div>(Pickup)</div>
      </div>
      <div>
        {eventDays?.map((item, index) => (
          <div
            key={`${item?.label}-${index}`}
            className="grid grid-cols-3 gap-5 items-center pb-2"
          >
            <div>{item?.label}</div>
            <InputField
              type="number"
              maxLength={3}
              form={form}
              name={item?.preName as any}
              placeholder={item?.preLabel}
            />
            <InputField
              type="number"
              form={form}
              name={item?.postName as any}
              placeholder={item?.postLabel}
              maxLength={3}
            />
          </div>
        ))}
      </div>
      <AppButton
        label="Submit"
        className="w-full col-span-2 mt-4"
        onClick={handleSubmit(onSubmit)}
        isLoading={isLoading || newItemLoading}
      />
      <AppSpinner overlay isLoading={isFetching} />
    </div>
  );
};
