import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, vi, expect } from 'vitest';
import { DeliveryChargesForm } from '.';
import { useForm } from 'react-hook-form';

// Mock necessary imports
vi.mock('@/redux/features/list/category/list.api', async () => {
  const actual = await import('@/redux/features/list/category/list.api');
  return {
    ...actual,
    useAddNewItemMutation: vi.fn(() => [vi.fn(), { isLoading: false }]),
    useUpdateItemMutation: vi.fn(() => [vi.fn(), { isLoading: false }]),
    useDeleteItemMutation: vi.fn(() => [vi.fn(), { isLoading: false }]),
    useGetListQuery: vi.fn(() => ({
      data: { data: { id: 1, zipCodes: [] } },
      isFetching: false,
      refetch: vi.fn(),
    })),
    useGetListItemsMutation: vi.fn(() => [
      vi.fn(),
      {
        data: { data: [] },
        isLoading: false,
      },
    ]),
  };
});

vi.mock('@/lib/utils', async () => {
  const actual = await import('@/lib/utils');
  return {
    ...actual,
    generateLabelValuePairs: vi.fn(),
    getPaginationObject: vi.fn(),
    getQueryParam: vi.fn(),
  };
});

vi.mock('@/redux/features/state/state.api', async () => {
  const actual = await import('@/redux/features/state/state.api');
  return {
    ...actual,
    useGetStateListQuery: vi.fn(() => ({
      data: [{ code: 'CA' }, { code: 'NY' }],
      isFetching: false,
    })),
  };
});

vi.mock('react-hook-form', () => {
  const original = vi.importActual('react-hook-form');
  return {
    ...original,
    useForm: vi.fn(() => ({
      handleSubmit: vi.fn((cb) => cb),
      reset: vi.fn(),
      control: {},
    })),
    useFieldArray: vi.fn(() => ({
      fields: [],
      append: vi.fn(),
      remove: vi.fn(),
    })),
  };
});

vi.mock('@/components/forms/input-field', () => ({
  default: vi.fn(() => <input data-testid="input-field" />),
}));
vi.mock('@/components/forms/number-input-field', () => ({
  default: vi.fn(() => <input data-testid="number-input-field" />),
}));
vi.mock('@/components/forms/select-dropdown', () => ({
  default: vi.fn(() => <select data-testid="select-dropdown" />),
}));
vi.mock('@/components/forms/ShadcnSelect', () => ({
  default: vi.fn(() => <select data-testid="shadcn-select" />),
}));
vi.mock('@/components/common/app-button', () => ({
  default: vi.fn(
    ({ onClick, label }: { onClick: () => void; label: string }) => (
      <button onClick={onClick} data-testid="app-button">
        {label}
      </button>
    )
  ),
}));
vi.mock('@/components/common/app-confirmation-modal', () => ({
  default: vi.fn(() => <div data-testid="confirmation-modal" />),
}));
vi.mock('@/components/common/app-spinner', () => ({
  default: vi.fn(() => <div data-testid="spinner" />),
}));

describe('DeliveryChargesForm Component', () => {
  it('renders form components correctly', () => {
    render(<DeliveryChargesForm toggle={vi.fn()} />);

    expect(screen.getByText('+ Add New')).toBeInTheDocument();
    expect(screen.getByText('Zip Code From')).toBeInTheDocument();
    expect(screen.getByText('Zip Code Thru')).toBeInTheDocument();
    expect(screen.getByText('Charge')).toBeInTheDocument();
  });

  it('appends a new row when Add New is clicked', () => {
    const { getByText } = render(<DeliveryChargesForm toggle={vi.fn()} />);
    const addButton = getByText('+ Add New');

    fireEvent.click(addButton);

    expect(useForm().control);
  });

  it('submits the form data correctly', async () => {
    const mockToggle = vi.fn();
    const { getByText } = render(<DeliveryChargesForm toggle={mockToggle} />);
    const submitButton = getByText('Submit');

    fireEvent.click(submitButton);

    expect(mockToggle);
  });

  it('shows the confirmation modal when deleting', async () => {
    const { getByTestId } = render(<DeliveryChargesForm toggle={vi.fn()} />);
    const modal = getByTestId('confirmation-modal');

    expect(modal).toBeInTheDocument();
  });

  it('displays loading spinner during data fetching', async () => {
    render(<DeliveryChargesForm toggle={vi.fn()} />);

    expect(screen.getByTestId('spinner')).toBeInTheDocument();
  });
});
