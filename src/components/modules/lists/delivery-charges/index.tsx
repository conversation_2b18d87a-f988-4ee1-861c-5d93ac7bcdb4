import AppButton from '@/components/common/app-button';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import AppSpinner from '@/components/common/app-spinner';
import ActionColumnMenu from '@/components/common/data-tables/ActionColumn';
import Input<PERSON>ield from '@/components/forms/input-field';
import NumberInputField from '@/components/forms/number-input-field';
import SelectDropDown from '@/components/forms/select-dropdown';
import ShadcnSelect from '@/components/forms/ShadcnSelect';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  CATEGORY_API_ROUTES,
  DELIVERY_CHARGES_API_ROUTES,
  SALES_TAX_CODE_API_ROUTES,
} from '@/constants/api-constants';
import { TEXT_VALIDATION_RULE } from '@/constants/validation-constants';
import {
  generateLabelValuePairs,
  getPaginationObject,
  getQueryParam,
} from '@/lib/utils';
import {
  useAddNewItemMutation,
  useDeleteItemMutation,
  useGetListItemsMutation,
  useGetListQuery,
  useUpdateItemMutation,
} from '@/redux/features/list/category/list.api';
import { useGetStateListQuery } from '@/redux/features/state/state.api';
import {
  DeliveryChargesType,
  DeliveryChargesZipCodesType,
} from '@/types/list.types';
import { useCallback, useEffect, useMemo, useState } from 'react';
import {
  SubmitHandler,
  useFieldArray,
  useForm,
  UseFormReturn,
} from 'react-hook-form';

// RentTable Component
const ZipCodesTable = ({
  fields,
  form,
  remove,
}: {
  fields: DeliveryChargesZipCodesType[];
  form: UseFormReturn<DeliveryChargesType>;
  remove: any;
}) => (
  <div className="col-span-2 border border-grayScale-20 lg:max-h-56 overflow-auto overflow_initial_table">
    <Table className="rounded-sm">
      <TableHeader
        className="bg-grayScale-10"
        style={{
          position: 'sticky',
          top: 0,
          background: '#f4f4f4',
          zIndex: 1,
        }}
      >
        <TableRow className="text-base font-medium">
          <TableHead className="text-grayScale-90 border-r border-grayScale-20">
            Zip Code From
          </TableHead>
          <TableHead className="text-grayScale-90 border-r border-grayScale-20">
            Zip Code Thru
          </TableHead>
          <TableHead className="text-grayScale-90">Charge</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {fields?.map((_field, index: number) => {
          return (
            <TableRow key={index}>
              <TableCell className="border-r border-grayScale-20">
                <NumberInputField
                  form={form}
                  maxLength={10}
                  name={`zipCodes.${index}.zipCodeFrom`}
                  placeholder="Zip Code From"
                />
              </TableCell>
              <TableCell className="border-r border-grayScale-20">
                <NumberInputField
                  form={form}
                  name={`zipCodes.${index}.zipCodeThru`}
                  placeholder="Zip Code Thru"
                  maxLength={10}
                />
              </TableCell>
              <TableCell className="flex flex-row items-center gap-x-1">
                <NumberInputField
                  form={form}
                  name={`zipCodes.${index}.charge`}
                  placeholder="$______.__"
                  prefix="$"
                  maxLength={11}
                  fixedDecimalScale
                  thousandSeparator=","
                />
                <ActionColumnMenu
                  onDelete={() => remove(index)}
                  contentClassName="z-[99] w-fit"
                />
              </TableCell>
            </TableRow>
          );
        })}
      </TableBody>
    </Table>
  </div>
);

// CategoryForm Component
export const DeliveryChargesForm = ({
  toggle,
}: {
  toggle?: (value?: any) => void;
}) => {
  const deliveryChargeId = getQueryParam('id');

  const { data, isFetching, refetch } = useGetListQuery(
    {
      url: DELIVERY_CHARGES_API_ROUTES.GET(deliveryChargeId),
    },
    { skip: !deliveryChargeId }
  );

  const [openModal, setOpenModal] = useState<boolean>(false);
  const [isRemove, setIsRemove] = useState<{ id: number; remove: boolean }>({
    id: 0,
    remove: false,
  });
  const [updateItem, { isLoading }] = useUpdateItemMutation();
  const [addEditItem, { isLoading: newItemLoading }] = useAddNewItemMutation();

  // delete rent table row
  const [deleteItem, { isLoading: isDeleteLoading }] = useDeleteItemMutation();

  const defaultValues = useMemo(() => {
    const dataValue = data?.data;
    return {
      ...dataValue,
      id: dataValue?.id || 0,
      zipCodes: dataValue?.zipCodes,
    };
  }, [data?.data]);

  const form: UseFormReturn<DeliveryChargesType> = useForm<DeliveryChargesType>(
    {
      defaultValues,
      mode: 'onChange',
    }
  );
  const { control, handleSubmit, reset } = form;
  const { fields, append, remove } = useFieldArray<DeliveryChargesType>({
    control,
    name: 'zipCodes',
  });

  useEffect(() => {
    if (defaultValues) {
      reset(defaultValues);
    }
  }, [defaultValues, reset]);

  const onSubmit: SubmitHandler<DeliveryChargesType> = async (
    data: DeliveryChargesType
  ) => {
    const payload = {
      ...data,
      delCharge: Number(data?.delCharge),
      zipCodes:
        data?.zipCodes?.map((items) => ({
          ...items,
          zipCodeFrom: items?.zipCodeFrom || null,
          zipCodeThru: items?.zipCodeThru || null,
          charge: Number(items?.charge) || null,
          town: data?.town,
          state: data?.state,
        })) ?? [],
    };
    try {
      if (deliveryChargeId) {
        await updateItem({
          url: DELIVERY_CHARGES_API_ROUTES.UPDATE(deliveryChargeId),
          data: payload,
        }).unwrap();
      } else {
        await addEditItem({
          url: DELIVERY_CHARGES_API_ROUTES.CREATE,
          data: payload,
        }).unwrap();
      }

      toggle?.(true);
    } catch (error) {
      // Handle error (optional)
    }
  };

  const toggleDelete = useCallback((id?: number, isRemove?: boolean) => {
    setOpenModal((prevState) => !prevState);
    setIsRemove({ id: id || 0, remove: isRemove ? isRemove : false });
  }, []);

  const handleDeleteRentTableRow = useCallback(async () => {
    if (isRemove?.remove) {
      remove(isRemove?.id);
    } else {
      await deleteItem({
        url: CATEGORY_API_ROUTES?.DELETE_RENT ?? '',
        itemId: isRemove?.id || '',
      }).unwrap();
      refetch();
    }
    setOpenModal((prevState) => !prevState);
    setIsRemove({ id: 0, remove: false });
  }, [deleteItem, isRemove?.id, isRemove?.remove, refetch, remove]);

  // sale tax code list
  const [getSalesTaxCode, { data: salesTaxCodeData = { data: [] } }] =
    useGetListItemsMutation();
  const departmentList = generateLabelValuePairs({
    data: salesTaxCodeData?.data,
    labelKey: 'salestaxcode',
    valueKey: 'salestaxcode_id',
  });

  // state List
  const { data: stateData, isFetching: stateIsFetching } =
    useGetStateListQuery();
  const stateList = generateLabelValuePairs({
    data: stateData,
    labelKey: 'code',
    valueKey: 'code',
  });

  useEffect(() => {
    getSalesTaxCode({
      url: SALES_TAX_CODE_API_ROUTES?.ALL,
      data: getPaginationObject({
        pagination: { pageIndex: -1, pageSize: -1 },
        sorting: [{ id: '', desc: true }],
      }),
    });
  }, [getSalesTaxCode]);

  return (
    <div className="grid grid-cols-2 gap-4">
      <InputField
        form={form}
        name="town"
        label="Town"
        placeholder="Town"
        validation={TEXT_VALIDATION_RULE}
      />

      <ShadcnSelect
        form={form}
        optionsList={stateList}
        label="State"
        name="state"
        placeholder="Select State"
        isLoading={stateIsFetching}
        required="Required"
      />
      <NumberInputField
        form={form}
        name="delCharge"
        label="Delivery Charges"
        placeholder="Delivery Charges"
        maxLength={9}
        prefix="$"
        fixedDecimalScale
        thousandSeparator=","
      />
      <SelectDropDown
        form={form}
        optionsList={departmentList}
        label="Default Sales Tax Code"
        name="salesTaxCodeId"
        placeholder="Default Sales Tax Code"
      />
      <div className="flex flex-row justify-end items-center col-span-2 mt-2">
        <AppButton
          className="text-left text-text-neutral-Default h-8"
          onClick={() => {
            append({
              id: 0,
              zipCodeFrom: '',
              zipCodeThru: '',
              charge: 0,
            });
          }}
          variant="neutral"
          label="+ Add New"
        />
      </div>
      <ZipCodesTable fields={fields} form={form} remove={remove} />
      <AppButton
        label="Submit"
        className="w-full col-span-2 mt-4"
        onClick={handleSubmit(onSubmit)}
        isLoading={newItemLoading || isLoading}
      />
      <AppConfirmationModal
        description={<div>Are you sure you want to delete this record?</div>}
        open={openModal}
        onOpenChange={() => toggleDelete()}
        handleCancel={() => toggleDelete()}
        handleSubmit={handleDeleteRentTableRow}
        isLoading={isDeleteLoading}
      />
      <AppSpinner overlay isLoading={isFetching} />
    </div>
  );
};
