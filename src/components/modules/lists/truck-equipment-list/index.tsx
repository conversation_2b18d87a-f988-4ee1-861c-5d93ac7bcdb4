import AppButton from '@/components/common/app-button';
import AppSpinner from '@/components/common/app-spinner';
import InputField from '@/components/forms/input-field';
import NumberInputField from '@/components/forms/number-input-field';
import SelectDropDown from '@/components/forms/select-dropdown';
import {
  EQUIPMENT_TYPES_API_ROUTES,
  TRUCKS_EQUIPMENTS_API_ROUTES,
} from '@/constants/api-constants';
import { TEXT_VALIDATION_RULE } from '@/constants/validation-constants';
import {
  generateLabelValuePairs,
  getPaginationObject,
  getQueryParam,
} from '@/lib/utils';
import {
  useAddNewItemMutation,
  useGetListItemsMutation,
  useGetListQuery,
  useUpdateItemMutation,
} from '@/redux/features/list/category/list.api';
import { TrucksEquipmentsType } from '@/types/list.types';
import { useEffect, useMemo } from 'react';
import { SubmitHandler, UseFormReturn, useForm } from 'react-hook-form';

// TrucksEuipmentListsForm Component
export const TrucksEuipmentLists = ({
  toggle,
}: {
  toggle?: (value?: any) => void;
}) => {
  const id = getQueryParam('id');

  const { data, isFetching } = useGetListQuery(
    {
      url: TRUCKS_EQUIPMENTS_API_ROUTES?.GET(id),
    },
    { skip: !id }
  );

  const [updateItem, { isLoading }] = useUpdateItemMutation();
  const [addNewItem, { isLoading: newItemLoading }] = useAddNewItemMutation();
  const [
    getEquipmentType,
    { data: equipmentTypeData, isLoading: equipmentTypeLoading },
  ] = useGetListItemsMutation();

  const defaultValues = useMemo(() => {
    const dataValue = data?.data ?? {};
    return {
      ...dataValue,
      id: dataValue?.id || 0,
    };
  }, [data?.data]);

  const form: UseFormReturn<TrucksEquipmentsType> =
    useForm<TrucksEquipmentsType>({
      defaultValues,
      mode: 'onChange',
    });
  const { handleSubmit } = form;

  useEffect(() => {
    form.reset(defaultValues);
  }, [defaultValues, form]);

  useEffect(() => {
    getEquipmentType({
      url: EQUIPMENT_TYPES_API_ROUTES?.ALL,
      data: getPaginationObject({
        pagination: { pageIndex: -1, pageSize: -1 },
        sorting: [{ id: '', desc: true }],
      }),
    });
  }, [getEquipmentType]);

  const equipmentTypeList = generateLabelValuePairs({
    data: equipmentTypeData?.data,
    labelKey: 'equipmentTypeDesc',
    valueKey: 'id',
  });

  const onSubmit: SubmitHandler<TrucksEquipmentsType> = async (
    data: TrucksEquipmentsType
  ) => {
    const payload = { ...data, weight: data?.weight || null };
    try {
      if (id) {
        await updateItem({
          url: TRUCKS_EQUIPMENTS_API_ROUTES.UPDATE(id),
          data: payload,
        }).unwrap();
      } else {
        await addNewItem({
          url: TRUCKS_EQUIPMENTS_API_ROUTES.CREATE,
          data: payload,
        }).unwrap();
      }
      toggle?.();
    } catch (error) {
      // Error optional
    }
  };

  return (
    <div>
      <div className="grid grid-cols-2 gap-4">
        <SelectDropDown
          form={form}
          optionsList={equipmentTypeList}
          name="equipmentTypeId"
          label="Equipment Type"
          placeholder="Equipment Type"
          validation={TEXT_VALIDATION_RULE}
          isLoading={equipmentTypeLoading}
        />

        <NumberInputField
          form={form}
          name="weight"
          label="Weight"
          placeholder="Weight"
          maxLength={8}
          fixedDecimalScale
        />
        <div className="col-span-2">
          <InputField
            form={form}
            name="description"
            label="Description"
            placeholder="Description"
            validation={TEXT_VALIDATION_RULE}
            maxLength={64}
          />
        </div>
        <AppButton
          label="Submit"
          className="w-full col-span-2 mt-4"
          onClick={handleSubmit(onSubmit)}
          isLoading={isLoading || newItemLoading}
        />
      </div>
      <AppSpinner overlay isLoading={isFetching} />
    </div>
  );
};
