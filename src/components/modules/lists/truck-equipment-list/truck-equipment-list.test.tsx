import { render, screen, fireEvent } from '@testing-library/react';
import { vi, describe, beforeEach, it, expect } from 'vitest';
import { TrucksEuipmentLists } from './index';
import {
  useAddNewItemMutation,
  useGetListItemsMutation,
  useGetListQuery,
  useUpdateItemMutation,
} from '@/redux/features/list/category/list.api';
import * as utils from '@/lib/utils';
import { generateLabelValuePairs, getQueryParam } from '@/lib/utils';

vi.mock('@/redux/features/list/category/list.api', () => ({
  useAddNewItemMutation: vi.fn(),
  useGetListItemsMutation: vi.fn(),
  useGetListQuery: vi.fn(),
  useUpdateItemMutation: vi.fn(),
}));

vi.mock('@/lib/utils', () => ({
  generateLabelValuePairs: vi.fn(),
  getPaginationObject: vi.fn(),
  getQueryParam: vi.fn(),
  cn: vi.fn(),
}));

vi.mock('@/components/common/app-button', () => ({
  default: ({ label, onClick, isLoading }: any) => (
    <button onClick={onClick} disabled={isLoading}>
      {label}
    </button>
  ),
}));

vi.mock('@/components/common/app-spinner', () => ({
  default: () => <div>Loading...</div>,
}));

vi.mock('@/components/forms/input-field', () => ({
  default: ({ form, name, placeholder }: any) => (
    <input {...form.register(name)} name={name} placeholder={placeholder} />
  ),
}));

vi.mock('@/components/forms/select-dropdown', () => ({
  default: ({ form, optionsList, name, placeholder }: any) => (
    <select {...form.register(name)} name={name} placeholder={placeholder}>
      {optionsList.map((option: any) => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  ),
}));

describe('TrucksEuipmentLists Component', () => {
  const mockToggle = vi.fn();

  beforeEach(() => {
    vi.resetAllMocks();
    (useGetListQuery as any).mockReturnValue({
      data: {
        data: {
          id: 1,
          weight: '500',
          description: 'Test Description',
          equipmentTypeId: 2,
        },
      },
      isFetching: false,
    });

    (useAddNewItemMutation as any).mockReturnValue([
      vi.fn(),
      { isLoading: false },
    ]);
    (useUpdateItemMutation as any).mockReturnValue([
      vi.fn(),
      { isLoading: false },
    ]);
    (useGetListItemsMutation as any).mockReturnValue([
      vi.fn(),
      {
        data: { data: [{ id: 2, equipmentTypeDesc: 'Type A' }] },
        isLoading: false,
      },
    ]);

    vi.spyOn(utils, 'getQueryParam').mockReturnValue('1');
    (generateLabelValuePairs as any).mockReturnValue([
      { label: 'Type A', value: 2 },
    ]);
  });

  it('renders the form with pre-filled data', async () => {
    render(<TrucksEuipmentLists toggle={mockToggle} />);

    expect(screen.getByPlaceholderText('Weight')).toHaveValue('500.00');
    expect(screen.getByPlaceholderText('Description')).toHaveValue(
      'Test Description'
    );
    expect(screen.getByPlaceholderText('Equipment Type')).toBeInTheDocument();
  });

  it('submits the form successfully for updating an item', async () => {
    const mockUpdateItem = vi.fn();
    (useUpdateItemMutation as any).mockReturnValue([
      mockUpdateItem,
      { isLoading: false },
    ]);
    vi.spyOn(utils, 'getQueryParam').mockReturnValue('1');

    render(<TrucksEuipmentLists toggle={mockToggle} />);

    const weightInput = screen.getByPlaceholderText('Weight');
    const descriptionInput = screen.getByPlaceholderText('Description');
    const submitButton = screen.getByText('Submit');

    fireEvent.change(weightInput, { target: { value: '600.00' } });
    fireEvent.change(descriptionInput, {
      target: { value: 'Updated Description' },
    });

    fireEvent.click(submitButton);
    expect(mockToggle);
  });

  it('shows loading spinner during data fetching', () => {
    (useGetListQuery as any).mockReturnValueOnce({
      data: null,
      isFetching: true,
    });

    render(<TrucksEuipmentLists toggle={mockToggle} />);

    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('submits the form successfully for adding a new item', async () => {
    const mockAddNewItem = vi.fn();
    (useAddNewItemMutation as any).mockReturnValue([
      mockAddNewItem,
      { isLoading: false },
    ]);
    (getQueryParam as any).mockReturnValue(null);

    render(<TrucksEuipmentLists toggle={mockToggle} />);

    const weightInput = screen.getByPlaceholderText('Weight');
    const descriptionInput = screen.getByPlaceholderText('Description');
    const submitButton = screen.getByText('Submit');

    fireEvent.change(weightInput, { target: { value: '700' } });
    fireEvent.change(descriptionInput, {
      target: { value: 'New Item Description' },
    });

    fireEvent.click(submitButton);

    expect(mockAddNewItem);
    expect(mockToggle);
  });
});
