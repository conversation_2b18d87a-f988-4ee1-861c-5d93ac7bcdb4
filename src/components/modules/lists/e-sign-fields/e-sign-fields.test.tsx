import { render, screen, fireEvent } from '@testing-library/react';
import { vi, describe, beforeEach, it, expect } from 'vitest';
import { ESignFields } from './index';
import {
  useGetListQuery,
  useUpdateItemMutation,
  useAddNewItemMutation,
} from '@/redux/features/list/category/list.api';
import { getQueryParam } from '@/lib/utils';

vi.mock('@/redux/features/list/category/list.api');
vi.mock('@/lib/utils', () => ({
  getQueryParam: vi.fn(),
  cn: vi.fn(),
}));

describe('ESignFields Component', () => {
  const toggleMock = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the form and submits data when creating a new item', async () => {
    (getQueryParam as any).mockReturnValue(null);
    const addNewItemMock = vi.fn().mockResolvedValue({});
    (useAddNewItemMutation as any).mockReturnValue([
      addNewItemMock,
      { isLoading: false },
    ]);
    const updateItemMock = vi.fn().mockResolvedValue({});
    (useUpdateItemMutation as any).mockReturnValue([
      updateItemMock,
      { isLoading: false },
    ]);
    (useGetListQuery as any).mockReturnValue({
      data: { data: {} },
      isFetching: false,
    });

    render(<ESignFields toggle={toggleMock} />);

    expect(screen.getByPlaceholderText('Enter Form Type')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Enter Field Name')).toBeInTheDocument();

    fireEvent.change(screen.getByPlaceholderText('Enter Form Type'), {
      target: { value: 'Sample Form' },
    });
    fireEvent.change(screen.getByPlaceholderText('Enter Field Name'), {
      target: { value: 'Signature' },
    });
    fireEvent.click(screen.getByText('Submit'));

    expect(addNewItemMock);
    expect(toggleMock);
  });

  it('renders the form and updates data when editing an existing item', async () => {
    (getQueryParam as any).mockReturnValue('123');
    (useGetListQuery as any).mockReturnValue({
      data: {
        data: {
          id: 123,
          formType: 'Existing Form',
          fieldName: 'Existing Field',
        },
      },
      isFetching: false,
    });

    const updateItemMock = vi.fn().mockResolvedValue({});
    (useUpdateItemMutation as any).mockReturnValue([
      updateItemMock,
      { isLoading: false },
    ]);

    render(<ESignFields toggle={toggleMock} />);

    fireEvent.change(screen.getByPlaceholderText('Enter Form Type'), {
      target: { value: 'Updated Form' },
    });
    fireEvent.change(screen.getByPlaceholderText('Enter Field Name'), {
      target: { value: 'Updated Field' },
    });

    fireEvent.click(screen.getByText('Submit'));
    expect(updateItemMock);
    expect(toggleMock);
  });

  it('shows a spinner when data is being fetched', () => {
    (useGetListQuery as any).mockReturnValue({
      data: null,
      isFetching: true,
    });

    render(<ESignFields toggle={toggleMock} />);
    expect(screen.getByTestId('spinner')).toBeInTheDocument();
  });

  it('shows loading state on submit', async () => {
    (getQueryParam as any).mockReturnValue(null);

    const addNewItemMock = vi.fn().mockResolvedValue({});
    (useAddNewItemMutation as any).mockReturnValue([
      addNewItemMock,
      { isLoading: true },
    ]);

    render(<ESignFields toggle={toggleMock} />);
    fireEvent.change(screen.getByPlaceholderText('Enter Form Type'), {
      target: { value: 'New Form' },
    });
    fireEvent.change(screen.getByPlaceholderText('Enter Field Name'), {
      target: { value: 'New Field' },
    });
    fireEvent.click(screen.getByText('Submit'));
    expect(screen.getByText('Submit'));
  });
});
