import AppButton from '@/components/common/app-button';
import AppSpinner from '@/components/common/app-spinner';
import InputField from '@/components/forms/input-field';
import { CHECK_LIST_ITEMS_API_ROUTES } from '@/constants/api-constants';
import { TEXT_VALIDATION_RULE } from '@/constants/validation-constants';
import { getQueryParam } from '@/lib/utils';
import {
  useAddNewItemMutation,
  useGetListQuery,
  useUpdateItemMutation,
} from '@/redux/features/list/category/list.api';
import { CheckListItemsFormType } from '@/types/list.types';
import { useEffect, useMemo } from 'react';
import { SubmitHandler, useForm, UseFormReturn } from 'react-hook-form';

// ChecklistItemsForm Component
export const ChecklistItems = ({ toggle }: { toggle?: () => void }) => {
  const id = getQueryParam('id');

  const { data, isFetching } = useGetListQuery(
    {
      url: CHECK_LIST_ITEMS_API_ROUTES?.GET(id),
    },
    { skip: !id }
  );

  const { data: seqDetails } = useGetListQuery(
    {
      url: CHECK_LIST_ITEMS_API_ROUTES?.GET_CHECKLIST_SEQUENCE_NO,
    },
    { skip: Boolean(id) }
  );

  const [updateItem, { isLoading }] = useUpdateItemMutation();
  const [addNewItem, { isLoading: newItemLoading }] = useAddNewItemMutation();

  const defaultValues = useMemo(() => {
    const dataValue = data?.data;
    const sequenceNumber = seqDetails?.data || '';
    return {
      ...dataValue,
      id: dataValue?.id || 0,
      seqNo: dataValue?.id ? dataValue?.seqNo : sequenceNumber, // Append seqDetails value
    };
  }, [data, seqDetails]);

  const form: UseFormReturn<CheckListItemsFormType> =
    useForm<CheckListItemsFormType>({
      defaultValues,
      mode: 'onChange',
    });
  const { handleSubmit, reset } = form;

  useEffect(() => {
    if (defaultValues) {
      reset(defaultValues);
    }
  }, [defaultValues, reset]);

  const onSubmit: SubmitHandler<CheckListItemsFormType> = async (
    data: CheckListItemsFormType
  ) => {
    try {
      if (id) {
        await updateItem({
          url: CHECK_LIST_ITEMS_API_ROUTES.UPDATE(id),
          data,
        }).unwrap();
      } else {
        await addNewItem({
          url: CHECK_LIST_ITEMS_API_ROUTES.CREATE,
          data,
        }).unwrap();
      }
      toggle?.();
    } catch (error) {
      // Handle error (optional)
    }
  };

  return (
    <div>
      <div className="grid grid-cols-1 gap-4">
        <InputField
          name="seqNo"
          form={form}
          type="number"
          maxLength={6}
          placeholder="Enter Sequence Number"
          label="Sequence #"
          validation={TEXT_VALIDATION_RULE}
        />

        <InputField
          form={form}
          name="description"
          label="Description"
          placeholder="Enter Description"
          validation={TEXT_VALIDATION_RULE}
        />
      </div>

      <AppButton
        label="Submit"
        className="w-full col-span-2 mt-4"
        onClick={handleSubmit(onSubmit)}
        isLoading={isLoading || newItemLoading}
      />
      <AppSpinner overlay isLoading={isFetching} />
    </div>
  );
};
