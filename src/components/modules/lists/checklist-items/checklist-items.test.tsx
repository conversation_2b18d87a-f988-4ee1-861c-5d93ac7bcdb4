import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, beforeEach, it, expect } from 'vitest';
import { ChecklistItems } from './index';
import {
  useGetListQuery,
  useAddNewItemMutation,
  useUpdateItemMutation,
} from '@/redux/features/list/category/list.api';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';

vi.mock('@/redux/features/list/category/list.api', async () => {
  const actual = await import('@/redux/features/list/category/list.api');
  return {
    ...actual,
    useGetListQuery: vi.fn(),
    useAddNewItemMutation: vi.fn(),
    useUpdateItemMutation: vi.fn(),
  };
});

vi.mock('@/components/common/app-button', () => ({
  default: vi
    .fn()
    .mockImplementation(({ onClick }) => (
      <button onClick={onClick}>Submit</button>
    )),
}));

vi.mock('@/components/common/app-spinner', () => ({
  default: vi.fn().mockImplementation(() => <div data-testid="spinner" />),
}));

const store = configureStore({
  reducer: {},
});

describe('ChecklistItems Component', () => {
  const mockToggle = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('calls onSubmit with correct data on submit', async () => {
    const mockAddNewItem = vi.fn();
    const mockUpdateItem = vi.fn();

    (useAddNewItemMutation as any).mockReturnValue([
      mockAddNewItem,
      { isLoading: false },
    ]);
    (useUpdateItemMutation as any).mockReturnValue([
      mockUpdateItem,
      { isLoading: false },
    ]);
    (useGetListQuery as any).mockReturnValue({
      data: { data: { seqNo: '1001', description: 'Test description' } },
      isFetching: false,
    });

    render(
      <Provider store={store}>
        <ChecklistItems toggle={mockToggle} />
      </Provider>
    );

    fireEvent.change(screen.getByPlaceholderText('Enter Sequence Number'), {
      target: { value: '1234' },
    });
    fireEvent.change(screen.getByPlaceholderText('Enter Description'), {
      target: { value: 'Updated description' },
    });

    fireEvent.click(screen.getByText('Submit'));

    await waitFor(() => {
      expect(mockUpdateItem);
      expect(mockAddNewItem);
    });
    expect(mockToggle);
  });

  it('shows loading spinner when fetching data', async () => {
    (useGetListQuery as any).mockReturnValue({
      data: null,
      isFetching: true,
    });

    render(
      <Provider store={store}>
        <ChecklistItems toggle={mockToggle} />
      </Provider>
    );

    expect(screen.getByTestId('spinner')).toBeInTheDocument();
  });

  it('handles the loading state during form submission', async () => {
    const mockAddNewItem = vi.fn();
    const mockUpdateItem = vi.fn();

    (useAddNewItemMutation as any).mockReturnValue([
      mockAddNewItem,
      { isLoading: false },
    ]);
    (useUpdateItemMutation as any).mockReturnValue([
      mockUpdateItem,
      { isLoading: true },
    ]);

    render(
      <Provider store={store}>
        <ChecklistItems toggle={mockToggle} />
      </Provider>
    );

    const isSubmitted = fireEvent.click(screen.getByText('Submit'));

    expect(isSubmitted);
  });
});
