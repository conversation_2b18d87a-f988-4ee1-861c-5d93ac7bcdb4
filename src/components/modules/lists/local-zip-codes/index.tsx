import AppButton from '@/components/common/app-button';
import AppSpinner from '@/components/common/app-spinner';
import InputField from '@/components/forms/input-field';
import { LOCAL_ZIP_CODES_API_ROUTES } from '@/constants/api-constants';
import { TEXT_VALIDATION_RULE } from '@/constants/validation-constants';
import { getQueryParam } from '@/lib/utils';
import {
  useAddNewItemMutation,
  useGetListQuery,
  useUpdateItemMutation,
} from '@/redux/features/list/category/list.api';
import { LocalZipCodesType } from '@/types/list.types';
import { useEffect, useMemo } from 'react';
import { SubmitHandler, UseFormReturn, useForm } from 'react-hook-form';

// SurgeRatesForm Component
export const LocalZipCodes = ({
  toggle,
}: {
  toggle?: (value?: any) => void;
}) => {
  const id = getQueryParam('id');

  const { data, isFetching } = useGetListQuery(
    {
      url: LOCAL_ZIP_CODES_API_ROUTES?.GET(id),
    },
    { skip: !id }
  );

  const [updateItem, { isLoading }] = useUpdateItemMutation();
  const [addNewItem, { isLoading: newItemLoading }] = useAddNewItemMutation();

  const defaultValues = useMemo(() => {
    const dataValue = data?.data ?? {};
    return {
      ...dataValue,
      id: dataValue?.id || 0,
    };
  }, [data?.data]);

  const form: UseFormReturn<LocalZipCodesType> = useForm<LocalZipCodesType>({
    defaultValues,
    mode: 'onChange',
  });

  const onSubmit: SubmitHandler<LocalZipCodesType> = async (
    data: LocalZipCodesType
  ) => {
    const payload = { ...data, zipThru: data?.zipThru || data?.zipFrom };
    try {
      if (id) {
        await updateItem({
          url: LOCAL_ZIP_CODES_API_ROUTES.UPDATE(id),
          data: payload,
        }).unwrap();
      } else {
        await addNewItem({
          url: LOCAL_ZIP_CODES_API_ROUTES.CREATE,
          data: payload,
        }).unwrap();
      }
      toggle?.();
    } catch (error) {
      // Error optional
    }
  };

  useEffect(() => {
    form.reset(defaultValues);
  }, [defaultValues, form]);

  return (
    <div>
      <div className="grid grid-cols-1 gap-4">
        <InputField
          form={form}
          name="zipFrom"
          label="Zip Code From"
          placeholder="Zip Code From"
          maxLength={8}
          validation={TEXT_VALIDATION_RULE}
        />
        <InputField
          form={form}
          name="zipThru"
          label="Zip Code Thru"
          placeholder="Zip Code Thru"
          maxLength={8}
        />

        <AppButton
          label="Submit"
          className="w-full mt-4"
          onClick={form.handleSubmit(onSubmit)}
          isLoading={isLoading || newItemLoading}
        />
      </div>
      <AppSpinner overlay isLoading={isFetching} />
    </div>
  );
};
