import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { LocalZipCodes } from './index';
import {
  useAddNewItemMutation,
  useGetListQuery,
  useUpdateItemMutation,
} from '@/redux/features/list/category/list.api';
import { LOCAL_ZIP_CODES_API_ROUTES } from '@/constants/api-constants';
import * as utils from '@/lib/utils';

// Mock redux hooks
vi.mock('@/redux/features/list/category/list.api', () => ({
  useAddNewItemMutation: vi.fn(),
  useGetListQuery: vi.fn(),
  useUpdateItemMutation: vi.fn(),
}));

vi.mock('@/lib/utils', () => ({
  getQueryParam: vi.fn(),
  cn: vi.fn(),
}));

describe('LocalZipCodes Component', () => {
  const toggleMock = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the form with input fields and submit button', () => {
    (useGetListQuery as any).mockReturnValue({
      data: null,
      isFetching: false,
    });
    (useAddNewItemMutation as any).mockReturnValue([
      vi.fn(),
      { isLoading: false },
    ]);
    (useUpdateItemMutation as any).mockReturnValue([
      vi.fn(),
      { isLoading: false },
    ]);

    render(<LocalZipCodes toggle={toggleMock} />);

    expect(screen.getByLabelText('Zip Code From')).toBeInTheDocument();
    expect(screen.getByLabelText('Zip Code Thru')).toBeInTheDocument();
    expect(screen.getByText('Submit')).toBeInTheDocument();
  });

  it('handles form submission for adding a new item', async () => {
    const addNewItemMock = vi.fn().mockResolvedValue({});
    (useGetListQuery as any).mockReturnValue({
      data: null,
      isFetching: false,
    });
    (useAddNewItemMutation as any).mockReturnValue([
      addNewItemMock,
      { isLoading: false },
    ]);
    (useUpdateItemMutation as any).mockReturnValue([
      vi.fn(),
      { isLoading: false },
    ]);

    render(<LocalZipCodes toggle={toggleMock} />);

    fireEvent.change(screen.getByLabelText('Zip Code From'), {
      target: { value: '12345' },
    });
    fireEvent.change(screen.getByLabelText('Zip Code Thru'), {
      target: { value: '67890' },
    });

    fireEvent.click(screen.getByText('Submit'));

    await waitFor(() => {
      expect(addNewItemMock).toHaveBeenCalledWith({
        url: LOCAL_ZIP_CODES_API_ROUTES.CREATE,
        data: { zipFrom: '12345', zipThru: '67890', id: 0 },
      });
      expect(toggleMock);
    });
  });

  it('handles form submission for updating an existing item', async () => {
    vi.spyOn(utils, 'getQueryParam').mockReturnValue('1');
    const updateItemMock = vi.fn().mockResolvedValue({});
    (useGetListQuery as any).mockReturnValue({
      data: { data: { zipFrom: '54321', zipThru: '98765', id: 1 } },
      isFetching: false,
    });
    (useAddNewItemMutation as any).mockReturnValue([
      vi.fn(),
      { isLoading: false },
    ]);
    (useUpdateItemMutation as any).mockReturnValue([
      updateItemMock,
      { isLoading: false },
    ]);

    render(<LocalZipCodes toggle={toggleMock} />);

    fireEvent.change(screen.getByLabelText('Zip Code From'), {
      target: { value: '54321' },
    });
    fireEvent.change(screen.getByLabelText('Zip Code Thru'), {
      target: { value: '98765' },
    });

    fireEvent.click(screen.getByText('Submit'));

    await waitFor(() => {
      expect(updateItemMock).toHaveBeenCalledWith({
        url: LOCAL_ZIP_CODES_API_ROUTES.UPDATE('1'),
        data: { zipFrom: '54321', zipThru: '98765', id: 1 },
      });
      expect(toggleMock);
    });
  });

  it('displays a spinner while fetching data', () => {
    (useGetListQuery as any).mockReturnValue({
      data: null,
      isFetching: true,
    });

    render(<LocalZipCodes toggle={toggleMock} />);

    expect(screen.getByTestId('spinner')).toBeInTheDocument();
  });
});
