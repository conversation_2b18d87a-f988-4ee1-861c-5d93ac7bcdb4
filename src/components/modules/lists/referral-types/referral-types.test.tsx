import { render, screen, fireEvent } from '@testing-library/react';
import { vi, describe, beforeEach, it, expect } from 'vitest';
import { ReferralTypes } from './index';
import {
  useAddNewItemMutation,
  useUpdateItemMutation,
  useGetListQuery,
} from '@/redux/features/list/category/list.api';

// Mock the necessary hooks
vi.mock('@/redux/features/list/category/list.api', () => ({
  useAddNewItemMutation: vi.fn(),
  useUpdateItemMutation: vi.fn(),
  useGetListQuery: vi.fn(),
}));

describe('ReferralTypes Component', () => {
  const mockToggle = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render the form with correct inputs and submit button', () => {
    (useGetListQuery as any).mockReturnValue({
      data: { data: { reftype_id: 1, code: 'AB', description: 'Referral A' } },
      isFetching: false,
    });
    (useAddNewItemMutation as any).mockReturnValue([
      () => Promise.resolve(),
      { isLoading: false },
    ]);
    (useUpdateItemMutation as any).mockReturnValue([
      () => Promise.resolve(),
      { isLoading: false },
    ]);

    render(<ReferralTypes toggle={mockToggle} />);

    expect(screen.getByLabelText('Code')).toBeInTheDocument();
    expect(screen.getByLabelText('Referral Type')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Submit' })).toBeInTheDocument();
  });

  it('should call the toggle function when the form is submitted for a new referral type', async () => {
    (useGetListQuery as any).mockReturnValue({
      data: { data: {} },
      isFetching: false,
    });
    const mockAddNewItem = vi.fn().mockResolvedValue({});
    (useAddNewItemMutation as any).mockReturnValue([
      mockAddNewItem,
      { isLoading: false },
    ]);

    render(<ReferralTypes toggle={mockToggle} />);

    const submitButton = screen.getByRole('button', { name: 'Submit' });
    fireEvent.click(submitButton);

    expect(mockToggle);
  });

  it('should call the toggle function when the form is submitted for an existing referral type', async () => {
    (useGetListQuery as any).mockReturnValue({
      data: { data: { reftype_id: 1, code: 'AB', description: 'Referral A' } },
      isFetching: false,
    });
    const mockUpdateItem = vi.fn().mockResolvedValue({});
    (useUpdateItemMutation as any).mockReturnValue([
      mockUpdateItem,
      { isLoading: false },
    ]);

    render(<ReferralTypes toggle={mockToggle} />);

    const submitButton = screen.getByRole('button', { name: 'Submit' });
    fireEvent.click(submitButton);

    expect(mockUpdateItem);
  });

  it('should show loading spinner while fetching data', () => {
    (useGetListQuery as any).mockReturnValue({
      data: null,
      isFetching: true,
    });

    render(<ReferralTypes toggle={mockToggle} />);

    expect(screen.getByTestId('spinner')).toBeInTheDocument();
  });

  it('should disable the submit button while loading', async () => {
    (useGetListQuery as any).mockReturnValue({
      data: { data: {} },
      isFetching: false,
    });
    (useAddNewItemMutation as any).mockReturnValue([
      () => Promise.resolve(),
      { isLoading: true },
    ]);

    render(<ReferralTypes toggle={mockToggle} />);

    const submitButton = screen.getByText('Submit');
    expect(submitButton);
  });
});
