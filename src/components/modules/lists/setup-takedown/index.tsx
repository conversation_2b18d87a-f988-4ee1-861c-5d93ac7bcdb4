import AppButton from '@/components/common/app-button';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import AppSpinner from '@/components/common/app-spinner';
import SwitchField from '@/components/common/switch';
import InputField from '@/components/forms/input-field';
import RadioField from '@/components/forms/radio-field';
import { SETUP_TAKEDOWN_API_ROUTES } from '@/constants/api-constants';
import { TEXT_VALIDATION_RULE } from '@/constants/validation-constants';
import { getQueryParam } from '@/lib/utils';
import { useGetItemsMutation } from '@/redux/features/common-api/common.api';
import {
  useAddNewItemMutation,
  useGetListQuery,
  useUpdateItemMutation,
} from '@/redux/features/list/category/list.api';
import { SetupTakedownType, setupTakedown } from '@/types/list.types';
import { useEffect, useMemo, useState } from 'react';
import { SubmitHandler, UseFormReturn, useForm } from 'react-hook-form';

// SetupTakedownForm Component
export const SetupTakedown = ({
  toggle,
}: {
  toggle?: (value?: any) => void;
}) => {
  const id = getQueryParam('id');
  const { data, isFetching } = useGetListQuery(
    {
      url: SETUP_TAKEDOWN_API_ROUTES?.GET(id),
    },
    { skip: !id }
  );

  const [updateItem, { isLoading }] = useUpdateItemMutation();
  const [addNewItem, { isLoading: newItemLoading }] = useAddNewItemMutation();
  const [getdefaultsetup] = useGetItemsMutation();
  const [getdefaulttakedown] = useGetItemsMutation();
  const [openModal, setOpenModal] = useState(false);

  const defaultValues = useMemo(() => {
    const dataValue = data?.data ?? {};
    return {
      ...dataValue,
      id: dataValue?.id || 0,
      type: dataValue?.type?.toString() || '1',
    };
  }, [data?.data]);

  const form: UseFormReturn<SetupTakedownType> = useForm<SetupTakedownType>({
    defaultValues,
    mode: 'onChange',
  });
  const { handleSubmit, watch, setValue } = form;
  const type = watch('type');

  const onSubmit: SubmitHandler<SetupTakedownType> = async (
    data: SetupTakedownType
  ) => {
    try {
      if (id) {
        await updateItem({
          url: SETUP_TAKEDOWN_API_ROUTES.UPDATE(id),
          data: data,
        }).unwrap();
      } else {
        await addNewItem({
          url: SETUP_TAKEDOWN_API_ROUTES.CREATE,
          data: data,
        }).unwrap();
      }
      toggle?.();
    } catch (error) {
      // Error optional
    }
  };

  const handleDefault = async (value: boolean) => {
    if (!value) return;
    const url =
      type === '1'
        ? SETUP_TAKEDOWN_API_ROUTES.DEFAULT_SETUP
        : SETUP_TAKEDOWN_API_ROUTES.DEFAULT_TAKEDOWN;
    const apiFunction = type === '1' ? getdefaultsetup : getdefaulttakedown;
    const response = await apiFunction({
      url: url,
    });
    if (response?.data) {
      if (form.watch('id') !== response?.data?.data?.id) {
        toggleModal();
      }
    }
  };

  const toggleModal = () => {
    setOpenModal((prev) => !prev);
  };

  const handleModalSubmit = () => {
    setValue('isDefault', true);
    toggleModal();
  };

  const handleModalCancel = () => {
    setValue('isDefault', false);
    toggleModal();
  };

  useEffect(() => {
    form.reset(defaultValues);
  }, [defaultValues, form]);

  return (
    <div>
      <div className="grid grid-cols-1 gap-6">
        <InputField
          form={form}
          name="code"
          label="Code"
          placeholder="Code"
          maxLength={1}
          disabled={Boolean(id)}
          validation={TEXT_VALIDATION_RULE}
        />
        <InputField
          form={form}
          name="description"
          label="Description"
          placeholder="Description"
          maxLength={32}
        />
      </div>
      <div className="flex items-center gap-6 mt-5 flex-wrap">
        <RadioField
          form={form}
          name="type"
          options={setupTakedown}
          optionsPerRow={2}
          required
        />
        <SwitchField
          form={form}
          name="isDefault"
          label="Default"
          onChange={handleDefault}
        />
      </div>
      <AppButton
        label="Submit"
        className="w-full mt-6"
        onClick={handleSubmit(onSubmit)}
        isLoading={isLoading || newItemLoading}
      />
      <AppSpinner overlay isLoading={isFetching} />
      <AppConfirmationModal
        className="max-w-[30%]"
        description={
          <div>
            There is another setup code that is set as the default. Do you want
            this code to be the default instead?
          </div>
        }
        open={openModal}
        handleSubmit={() => handleModalSubmit()}
        handleCancel={() => handleModalCancel()}
      />
    </div>
  );
};
