import { render, screen, fireEvent } from '@testing-library/react';
import { vi, expect, describe, beforeEach, it } from 'vitest';
import { SetupTakedown } from './index';
import {
  useGetListQuery,
  useAddNewItemMutation,
  useUpdateItemMutation,
} from '@/redux/features/list/category/list.api';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { commonApi } from '@/redux/features/common-api/common.api';

vi.mock('@/redux/features/list/category/list.api', () => ({
  useGetListQuery: vi.fn(),
  useAddNewItemMutation: vi.fn(),
  useUpdateItemMutation: vi.fn(),
  useGetItemsMutation: vi.fn(),
}));

vi.mock('@/components/common/app-spinner', () => ({
  default: () => <div>Loading...</div>,
}));

vi.mock('@/components/common/app-button', () => ({
  default: ({ onClick, isLoading }: any) => (
    <button onClick={onClick} disabled={isLoading}>
      Submit
    </button>
  ),
}));

vi.mock('@/components/forms/input-field', () => ({
  default: ({ name, label }: any) => (
    <input name={name} data-testid={name} placeholder={label} />
  ),
}));

vi.mock('@/components/common/switch', () => ({
  default: ({ onChange }: any) => (
    <input type="checkbox" onChange={(e) => onChange(e.target.checked)} />
  ),
}));

vi.mock('@/components/common/app-confirmation-modal', () => ({
  default: ({ open, handleSubmit, handleCancel }: any) =>
    open ? (
      <div>
        <button onClick={handleSubmit}>Confirm</button>
        <button onClick={handleCancel}>Cancel</button>
      </div>
    ) : null,
}));

const mockStore = configureStore({
  reducer: {
    list: (state = {}) => state,
    [commonApi.reducerPath]: commonApi.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(commonApi.middleware),
});

describe('SetupTakedown Component', () => {
  const toggleMock = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render the SetupTakedown form and submit the data', async () => {
    const mockData = {
      data: {
        id: 1,
        code: 'AB',
        description: 'Test Description',
        type: '1',
      },
    };

    (useGetListQuery as any).mockReturnValue({
      data: mockData,
      isFetching: false,
    });

    (useUpdateItemMutation as any).mockReturnValue([
      vi.fn(),
      { isLoading: false, isSuccess: true },
    ]);
    (useAddNewItemMutation as any).mockReturnValue([
      vi.fn(),
      { isLoading: false, isSuccess: true },
    ]);

    render(
      <Provider store={mockStore}>
        <SetupTakedown toggle={toggleMock} />
      </Provider>
    );

    expect(screen.getByPlaceholderText('Code')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Description')).toBeInTheDocument();

    fireEvent.change(screen.getByTestId('code'), { target: { value: 'XYZ' } });
    fireEvent.change(screen.getByTestId('description'), {
      target: { value: 'Updated Description' },
    });

    fireEvent.click(screen.getByText('Submit'));

    expect(useUpdateItemMutation);
    expect(toggleMock);
  });

  it('should open and handle the confirmation modal', async () => {
    const mockData = {
      data: {
        id: 1,
        code: 'AB',
        description: 'Test Description',
        type: '1',
      },
    };

    (useGetListQuery as any).mockReturnValue({
      data: mockData,
      isFetching: false,
    });
    (useUpdateItemMutation as any).mockReturnValue([
      vi.fn(),
      { isLoading: false, isSuccess: true },
    ]);
    (useAddNewItemMutation as any).mockReturnValue([
      vi.fn(),
      { isLoading: false, isSuccess: true },
    ]);

    render(
      <Provider store={mockStore}>
        <SetupTakedown toggle={toggleMock} />
      </Provider>
    );

    const isClickedOnCheckbox = fireEvent.click(screen.getByRole('checkbox'));
    expect(isClickedOnCheckbox);
  });

  it('should handle form reset when default values change', async () => {
    const mockData = {
      data: {
        id: 1,
        code: 'AB',
        description: 'Test Description',
        type: '1',
      },
    };

    (useGetListQuery as any).mockReturnValue({
      data: mockData,
      isFetching: false,
    });

    const newMockData = {
      data: {
        id: 2,
        code: 'CD',
        description: 'New Description',
        type: '2',
      },
    };

    (useGetListQuery as any).mockReturnValueOnce({
      data: newMockData,
      isFetching: false,
    });

    render(
      <Provider store={mockStore}>
        <SetupTakedown toggle={toggleMock} />
      </Provider>
    );

    expect(screen.getByPlaceholderText('Code'));
    expect(screen.getByPlaceholderText('Description'));
  });
});
