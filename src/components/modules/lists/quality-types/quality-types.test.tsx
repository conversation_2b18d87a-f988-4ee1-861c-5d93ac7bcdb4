import { render, screen, fireEvent } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { QualityTypes } from './index';
import {
  useAddNewItemMutation,
  useUpdateItemMutation,
  useGetListQuery,
} from '@/redux/features/list/category/list.api';

vi.mock('@/redux/features/list/category/list.api', () => ({
  useAddNewItemMutation: vi.fn(),
  useUpdateItemMutation: vi.fn(),
  useGetListQuery: vi.fn(),
}));

describe('QualityTypes Component', () => {
  const mockToggle = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render the form with correct inputs and submit button', () => {
    (useGetListQuery as any).mockReturnValue({
      data: { data: { id: 1, qualityType: 'A', qualityDesc: 'Description' } },
      isFetching: false,
    });
    (useAddNewItemMutation as any).mockReturnValue([
      () => Promise.resolve(),
      { isLoading: false },
    ]);
    (useUpdateItemMutation as any).mockReturnValue([
      () => Promise.resolve(),
      { isLoading: false },
    ]);

    render(<QualityTypes toggle={mockToggle} />);

    expect(screen.getByLabelText('Code')).toBeInTheDocument();
    expect(screen.getByLabelText('Quality Type')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Submit' })).toBeInTheDocument();
  });

  it('should call the toggle function when the form is submitted for a new item', async () => {
    (useGetListQuery as any).mockReturnValue({
      data: { data: {} },
      isFetching: false,
    });
    const mockAddNewItem = vi.fn().mockResolvedValue({});
    (useAddNewItemMutation as any).mockReturnValue([
      mockAddNewItem,
      { isLoading: false },
    ]);

    render(<QualityTypes toggle={mockToggle} />);

    const submitButton = screen.getByRole('button', { name: 'Submit' });
    fireEvent.click(submitButton);

    expect(mockToggle);
  });

  it('should call the toggle function when the form is submitted for an existing item', async () => {
    (useGetListQuery as any).mockReturnValue({
      data: { data: { id: 1, qualityType: 'A', qualityDesc: 'Description' } },
      isFetching: false,
    });
    const mockUpdateItem = vi.fn().mockResolvedValue({});
    (useUpdateItemMutation as any).mockReturnValue([
      mockUpdateItem,
      { isLoading: false },
    ]);

    render(<QualityTypes toggle={mockToggle} />);

    const submitButton = screen.getByRole('button', { name: 'Submit' });
    fireEvent.click(submitButton);

    expect(mockUpdateItem);
  });

  it('should show loading spinner while fetching data', () => {
    (useGetListQuery as any).mockReturnValue({
      data: null,
      isFetching: true,
    });

    render(<QualityTypes toggle={mockToggle} />);

    expect(screen.getByTestId('spinner')).toBeInTheDocument();
  });

  it('should disable the submit button while loading', async () => {
    (useGetListQuery as any).mockReturnValue({
      data: { data: {} },
      isFetching: false,
    });
    (useAddNewItemMutation as any).mockReturnValue([
      () => Promise.resolve(),
      { isLoading: true },
    ]);

    render(<QualityTypes toggle={mockToggle} />);

    const submitButton = screen.getByText('Submit');
    expect(submitButton);
  });
});
