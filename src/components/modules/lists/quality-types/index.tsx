import AppButton from '@/components/common/app-button';
import AppSpinner from '@/components/common/app-spinner';
import InputField from '@/components/forms/input-field';
import { QUALITY_TYPES_API_ROUTES } from '@/constants/api-constants';
import { TEXT_VALIDATION_RULE } from '@/constants/validation-constants';
import { getQueryParam } from '@/lib/utils';
import {
  useAddNewItemMutation,
  useGetListQuery,
  useUpdateItemMutation,
} from '@/redux/features/list/category/list.api';
import { QualityType } from '@/types/list.types';
import { useEffect, useMemo } from 'react';
import { SubmitHandler, UseFormReturn, useForm } from 'react-hook-form';

// TrucksForm Component
export const QualityTypes = ({
  toggle,
}: {
  toggle?: (value?: any) => void;
}) => {
  const id = getQueryParam('id');

  const { data, isFetching } = useGetListQuery(
    {
      url: QUALITY_TYPES_API_ROUTES?.GET(id),
    },
    { skip: !id }
  );

  const [updateItem, { isLoading }] = useUpdateItemMutation();
  const [addNewItem, { isLoading: newItemLoading }] = useAddNewItemMutation();

  const defaultValues = useMemo(() => {
    const dataValue = data?.data ?? {};
    return {
      ...dataValue,
      id: dataValue?.id || 0,
    };
  }, [data?.data]);

  const form: UseFormReturn<QualityType> = useForm<QualityType>({
    defaultValues,
    mode: 'onChange',
  });
  const { handleSubmit } = form;

  const onSubmit: SubmitHandler<QualityType> = async (data: QualityType) => {
    try {
      if (id) {
        await updateItem({
          url: QUALITY_TYPES_API_ROUTES.UPDATE(id),
          data: data,
        }).unwrap();
      } else {
        await addNewItem({
          url: QUALITY_TYPES_API_ROUTES.CREATE,
          data: data,
        }).unwrap();
      }
      toggle?.();
    } catch (error) {
      // Error optional
    }
  };

  useEffect(() => {
    form.reset(defaultValues);
  }, [defaultValues, form]);

  return (
    <div>
      <div className="grid grid-cols-2 gap-4">
        <InputField
          form={form}
          name="qualityType"
          label="Code"
          placeholder="Enter Code"
          maxLength={1}
          validation={TEXT_VALIDATION_RULE}
        />
        <InputField
          form={form}
          name="qualityDesc"
          label="Quality Type"
          placeholder="Enter Quality Type"
        />
        <AppButton
          label="Submit"
          className="w-full col-span-2 mt-4"
          onClick={handleSubmit(onSubmit)}
          isLoading={isLoading || newItemLoading}
        />
      </div>
      <AppSpinner overlay isLoading={isFetching} />
    </div>
  );
};
