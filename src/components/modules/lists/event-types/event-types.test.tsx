import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect } from 'vitest';
import { EventTypes } from './index';
import * as apiHooks from '@/redux/features/list/category/list.api';
import * as utils from '@/lib/utils';
import { EVENT_TYPES_API_ROUTES } from '@/constants/api-constants';

vi.mock('@/redux/features/list/category/list.api', () => ({
  useGetListQuery: vi.fn(),
  useUpdateItemMutation: vi.fn(),
  useAddNewItemMutation: vi.fn(),
}));

describe('EventTypes Component', () => {
  it('renders the form and submits data correctly for update', async () => {
    vi.spyOn(utils, 'getQueryParam').mockReturnValue('1');
    const mockToggle = vi.fn();
    const mockUpdateItem = vi.fn().mockResolvedValue({});
    const mockAddNewItem = vi.fn().mockResolvedValue({});
    const mockGetListQuery = {
      data: { data: { eventType: 'A', eventDesc: 'Annual', event_type_id: 1 } },
      isFetching: false,
    };

    (apiHooks.useGetListQuery as any).mockReturnValue(mockGetListQuery);
    (apiHooks.useUpdateItemMutation as any).mockReturnValue([
      mockUpdateItem,
      { isLoading: false },
    ]);
    (apiHooks.useAddNewItemMutation as any).mockReturnValue([
      mockAddNewItem,
      { isLoading: false },
    ]);

    render(<EventTypes toggle={mockToggle} />);
    const codeInput = screen.getByLabelText('Code');
    const descInput = screen.getByLabelText('Event Type');
    const submitButton = screen.getByText('Submit');

    expect(codeInput).toHaveValue('A');
    expect(descInput).toHaveValue('Annual');
    fireEvent.change(codeInput, { target: { value: 'B' } });
    fireEvent.change(descInput, { target: { value: 'Biannual' } });

    expect(codeInput).toHaveValue('B');
    expect(descInput).toHaveValue('Biannual');
    fireEvent.click(submitButton);
    await waitFor(() => {
      expect(mockUpdateItem).toHaveBeenCalledWith({
        url: EVENT_TYPES_API_ROUTES.UPDATE('1'),
        data: { eventType: 'B', eventDesc: 'Biannual', event_type_id: 1 },
      });
      expect(mockToggle);
    });
  });

  it('submits data correctly for adding a new item', async () => {
    vi.spyOn(utils, 'getQueryParam').mockReturnValue(null);
    const mockToggle = vi.fn();
    const mockAddNewItem = vi.fn().mockResolvedValue({});
    const mockGetListQuery = {
      data: null,
      isFetching: false,
    };

    (apiHooks.useGetListQuery as any).mockReturnValue(mockGetListQuery);
    (apiHooks.useUpdateItemMutation as any).mockReturnValue([
      vi.fn(),
      { isLoading: false },
    ]);
    (apiHooks.useAddNewItemMutation as any).mockReturnValue([
      mockAddNewItem,
      { isLoading: false },
    ]);

    render(<EventTypes toggle={mockToggle} />);

    const codeInput = screen.getByLabelText('Code');
    const descInput = screen.getByLabelText('Event Type');
    const submitButton = screen.getByText('Submit');

    fireEvent.change(codeInput, { target: { value: 'C' } });
    fireEvent.change(descInput, { target: { value: 'Conference' } });

    expect(codeInput).toHaveValue('C');
    expect(descInput).toHaveValue('Conference');

    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockAddNewItem).toHaveBeenCalledWith({
        url: EVENT_TYPES_API_ROUTES.CREATE,
        data: { eventType: 'C', eventDesc: 'Conference', event_type_id: 0 },
      });
      expect(mockToggle);
    });
  });
});
