import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import AppDataTable from '@/components/common/app-data-table';
import AppTableContextProvider from '@/components/common/app-data-table/AppTableContext';
import ActionColumnMenu from '@/components/common/data-tables/ActionColumn';
import CustomDialog from '@/components/common/dialog';
import { PageHeader } from '@/components/common/PageHeader';
import useCommonSetupListingConstants from '@/constants/list-constants';
import { useGetParamsDetails } from '@/hooks/useGetParamsDetails';
import { cn, getQueryParam, updateQueryParam } from '@/lib/utils';
import { useDeleteItemMutation } from '@/redux/features/list/category/list.api';
import { ColumnDef } from '@tanstack/react-table';
import { PlusIcon } from 'lucide-react';
import { useCallback, useMemo, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import AllLists from '../allLists';

const ListingTable = () => {
  const itemId = getQueryParam('id');
  const { listingSetupConstants } = useCommonSetupListingConstants();
  const { params: listname } = useParams<{ params?: string }>();
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState<boolean>(false);
  const [refresh, setRefresh] = useState<boolean>(false);

  // Separate open states for both dialogs
  const [isCustomDialogOpen, setIsCustomDialogOpen] = useState<boolean>(false);

  // Extract configuration from the hook
  const {
    findApi,
    deleteApi,
    pageLabel,
    columns,
    addLabel,
    isAddButton,
    bindingKey,
    className,
    formLabel,
    isDeleteButton = true,
    defaultSort,
  } = useGetParamsDetails(listname ?? '', listingSetupConstants);
  const navigate = useNavigate();

  const [deleteItem, { isLoading: isDeleteLoading }] = useDeleteItemMutation();
  const [filterOpen, setFilterOpen] = useState(false);

  // Toggle Custom Dialog
  const toggleCustomDialog = useCallback(() => {
    if (listname === 'delivery-locations') {
      navigate('add-delivery-location');
    } else {
      setIsCustomDialogOpen((prevState) => !prevState);
      if (itemId) {
        updateQueryParam(null);
      }
    }
  }, [itemId, listname, navigate]);

  const toggleDelete = useCallback((id?: number) => {
    setIsDeleteDialogOpen((prevState) => !prevState);
    updateQueryParam(id || null);
  }, []);

  const handleEdit = useCallback(
    (id?: number) => {
      if (listname === 'delivery-locations') {
        navigate(`edit-delivery-location?id=${id}`);
      } else {
        updateQueryParam(id || null);
        setIsCustomDialogOpen((prevState) => !prevState);
      }
    },
    [listname, navigate]
  );

  // Memoized ActionColumn definition for reuse in the DataTable
  const ActionColumn: ColumnDef<any> = useMemo(
    () => ({
      id: 'action',
      size: 110,
      header: 'Actions',
      cell: ({ row }) => {
        const value = row.original[bindingKey as string];
        return (
          <ActionColumnMenu
            onEdit={() => handleEdit(value)}
            onDelete={isDeleteButton ? () => toggleDelete(value) : undefined}
            contentClassName="w-fit"
          />
        );
      },
    }),
    [bindingKey, handleEdit, isDeleteButton, toggleDelete]
  );

  const handleDeleteCustomer = async () => {
    try {
      if (itemId === null) return;
      await deleteItem({
        url: deleteApi ?? '',
        itemId: itemId || '',
      }).unwrap();
      toggleDelete();
      setRefresh(true);
      setTimeout(() => {
        setRefresh(false);
      }, 500);
    } catch (error) {
      // Handle error (optional)
    }
  };

  const handleToggle = useCallback(() => {
    handleEdit();
    setRefresh(true);
    setTimeout(() => {
      setRefresh(false);
    }, 500);
  }, [handleEdit]);

  return (
    <AppTableContextProvider key={listname} defaultSort={defaultSort}>
      <div className="flex flex-col px-6 gap-x-6">
        <PageHeader
          title={pageLabel}
          cancelPath="/lists"
          handleSubmit={isAddButton ? toggleCustomDialog : undefined}
          icon={PlusIcon}
          label={addLabel}
          enableCancel={false}
          titleClassName="text-text-Default"
        />
        <AppDataTable
          columns={[...columns, ActionColumn]}
          url={findApi}
          enableSearch={false}
          enablePagination
          isFilterOpen={filterOpen}
          setIsFilterOpen={setFilterOpen}
          filterClassName="w-72"
          refreshList={refresh}
          tableClassName="max-h-[580px] overflow-auto"
        />

        {/* Custom Dialog */}
        <CustomDialog
          onOpenChange={() => handleEdit()}
          description=""
          open={isCustomDialogOpen}
          className={cn('max-h-[96%] overflow-y-auto min-w-72', className)}
          title={formLabel || pageLabel}
        >
          <div className="px-6">
            <AllLists name={listname ?? ''} toggle={handleToggle} />
          </div>
        </CustomDialog>
        {/* Delete Confirmation Dialog */}
        <AppConfirmationModal
          description={<div>Are you sure you want to delete this record?</div>}
          open={isDeleteDialogOpen}
          onOpenChange={() => toggleDelete()}
          handleCancel={() => toggleDelete()}
          handleSubmit={handleDeleteCustomer}
          isLoading={isDeleteLoading}
        />
      </div>
    </AppTableContextProvider>
  );
};

export default ListingTable;
