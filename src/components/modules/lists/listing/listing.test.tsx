import { render } from '@testing-library/react';
import { vi, describe, it, expect } from 'vitest';
import { BrowserRouter as Router } from 'react-router-dom';
import ListingTable from './index';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { commonApi } from '@/redux/features/common-api/common.api';

// Mocking hooks and components used in ListingTable
vi.mock('@/hooks/useGetParamsDetails', () => ({
  useGetParamsDetails: vi.fn().mockReturnValue({
    findApi: '/mock-api-endpoint',
    deleteApi: '/mock-delete-endpoint',
    pageLabel: 'Mock Listing',
    columns: [{ header: 'Name', accessor: 'name' }],
    addLabel: 'Add New',
    isAddButton: true,
    bindingKey: 'id',
    className: 'mock-class',
    formLabel: 'Form Label',
    isDeleteButton: true,
    defaultSort: [{ id: 'name', desc: false }],
  }),
}));

vi.mock('@/redux/features/list/category/list.api', () => ({
  useDeleteItemMutation: vi
    .fn()
    .mockReturnValue([vi.fn(), { isLoading: false }]),
}));

const store = configureStore({
  reducer: {
    [commonApi.reducerPath]: commonApi.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(commonApi.middleware),
});

describe('ListingTable Component', () => {
  it('rendered listing table', async () => {
    const isRendered = render(
      <Provider store={store}>
        <Router>
          <ListingTable />
        </Router>
      </Provider>
    );
    expect(isRendered).toBeTruthy();
  });
});
