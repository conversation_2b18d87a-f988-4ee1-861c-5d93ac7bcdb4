import { render, screen, fireEvent } from '@testing-library/react';
import { vi, describe, it, expect } from 'vitest';
import { EquipmentTypes } from './index';
import {
  useAddNewItemMutation,
  useGetListQuery,
  useUpdateItemMutation,
} from '@/redux/features/list/category/list.api';
import { getQueryParam } from '@/lib/utils';

vi.mock('@/redux/features/list/category/list.api', () => ({
  useAddNewItemMutation: vi
    .fn()
    .mockReturnValue([vi.fn().mockResolvedValue({}), { isLoading: false }]),
  useUpdateItemMutation: vi
    .fn()
    .mockReturnValue([vi.fn().mockResolvedValue({}), { isLoading: false }]),
  useGetListQuery: vi.fn().mockReturnValue({
    data: {
      data: {
        equipmentType: 'A',
        equipmentTypeDesc: 'Airplane',
        equipment_type_id: 1,
      },
    },
    isFetching: false,
  }),
}));

vi.mock('@/lib/utils', () => ({
  getQueryParam: vi.fn((key: string) => (key === 'id' ? '1' : undefined)),
  cn: vi.fn(),
}));

describe('EquipmentTypes Component', () => {
  it('renders the form and displays default values when data is loaded', async () => {
    render(<EquipmentTypes />);
    const codeInput = screen.getByLabelText('Code');
    const descInput = screen.getByLabelText('Equipment Type');
    expect(codeInput).toBeInTheDocument();
    expect(descInput).toBeInTheDocument();
    expect(codeInput).toHaveValue('A');
    expect(descInput).toHaveValue('Airplane');
  });

  it('allows user input and form submission for updating data', async () => {
    const mockToggle = vi.fn();
    const mockUpdateItem = vi.fn().mockResolvedValue({});
    (useUpdateItemMutation as any).mockReturnValue([
      mockUpdateItem,
      { isLoading: false },
    ]);

    render(<EquipmentTypes toggle={mockToggle} />);

    const codeInput = screen.getByLabelText('Code');
    const descInput = screen.getByLabelText('Equipment Type');
    const submitButton = screen.getByText('Submit');
    fireEvent.change(codeInput, { target: { value: 'B' } });
    fireEvent.change(descInput, { target: { value: 'Boat' } });
    expect(codeInput).toHaveValue('B');
    expect(descInput).toHaveValue('Boat');
    fireEvent.click(submitButton);
    expect(mockUpdateItem);
    expect(mockToggle);
  });

  it('submits new data correctly when no ID is present', async () => {
    (getQueryParam as any).mockReturnValue(undefined);
    const mockToggle = vi.fn();
    const mockAddNewItem = vi.fn().mockResolvedValue({});
    (useAddNewItemMutation as any).mockReturnValue([
      mockAddNewItem,
      { isLoading: false },
    ]);

    render(<EquipmentTypes toggle={mockToggle} />);

    // Simulate user input
    const codeInput = screen.getByLabelText('Code');
    const descInput = screen.getByLabelText('Equipment Type');
    const submitButton = screen.getByText('Submit');
    fireEvent.change(codeInput, { target: { value: 'C' } });
    fireEvent.change(descInput, { target: { value: 'Car' } });
    expect(codeInput).toHaveValue('C');
    expect(descInput).toHaveValue('Car');
    fireEvent.click(submitButton);
    expect(mockAddNewItem);
    expect(mockToggle);
  });

  it('displays loading spinner when fetching data', () => {
    (useGetListQuery as any).mockReturnValue({
      data: undefined,
      isFetching: true,
    });
    render(<EquipmentTypes />);
    const spinner = screen.getByTestId('spinner');
    expect(spinner).toBeInTheDocument();
  });

  it('disables the submit button while loading', () => {
    (useUpdateItemMutation as any).mockReturnValue([
      vi.fn(),
      { isLoading: true },
    ]);
    render(<EquipmentTypes />);
    const submitButton = screen.getByText('Submit');
    expect(submitButton);
  });
});
