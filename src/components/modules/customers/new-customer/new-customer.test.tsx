import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { MemoryRouter } from 'react-router-dom';
import NewCustomer from './index';
import { FormProvider, useForm } from 'react-hook-form';
import {
  customerApi,
  useGetCustomerByIdQuery,
} from '@/redux/features/customers/customer.api';
import { countyList } from '@/redux/features/country/country.api';
import { listApi } from '@/redux/features/list/category/list.api';
import { notesApi } from '@/redux/features/customers/notes.api';
import { commonApi } from '@/redux/features/common-api/common.api';

// Mock API modules
vi.mock('@/redux/features/customers/customer.api', async () => {
  const actual = await import('@/redux/features/customers/customer.api');
  return {
    ...actual,
    useCreateCustomerMutation: () => [
      vi.fn().mockResolvedValue({
        data: { customer_id: 2 },
        message: 'Customer created successfully',
      }),
      { isLoading: false },
    ],
    useGetCustomerByIdQuery: vi.fn().mockReturnValue({
      data: {
        data: {
          customer_id: 1,
          first_name: 'John',
          last_name: 'Doe',
          address1: '123 Main St',
          city: 'New York',
          state: 'NY',
          zipcode: '10001',
          country: 'USA',
          isactive: true,
          custtype_id: 1,
          defstorelocationno: 1,
        },
        message: 'Customer fetched successfully',
      },
      isFetching: false,
    }),
    useUpdateCustomerMutation: () => [
      vi.fn().mockResolvedValue({
        message: 'Customer updated successfully',
      }),
      { isLoading: false },
    ],
  };
});

vi.mock('@/redux/features/sub-types/sub-types.api', () => ({
  useUpdateSubTypesMutation: () => [
    () => ({
      unwrap: () => Promise.resolve({}),
    }),
    { isLoading: false },
  ],
}));

vi.mock('@/redux/features/store/store.api', () => ({
  useGetUserDefaultStoreQuery: vi.fn().mockReturnValue({
    data: {
      data: {
        countryId: 1,
        countryName: 'USA',
        stateCode: 'NY',
      },
    },
  }),
}));

vi.mock('@/components/common/app-spinner', () => ({
  default: () => <div>Loading...</div>,
}));

vi.mock('@/components/common/app-tabs', () => ({
  default: ({ activeTab, handleTabClick, tabs }: any) => (
    <div>
      {tabs.map((tab: any) => (
        <button
          key={tab.value}
          onClick={() => handleTabClick(tab.value)}
          data-active={activeTab === tab.value}
        >
          {tab.label}
        </button>
      ))}
    </div>
  ),
}));

vi.mock('@/components/ui/toast/ToastContainer', () => ({
  UseToast: () => ({
    success: vi.fn(),
    error: vi.fn(),
  }),
}));

vi.mock('@/redux/features/list/list.api', () => ({
  listApi: {
    reducerPath: 'listApi',
    reducer: vi.fn(),
    middleware: vi.fn(),
    useGetListQuery: vi.fn().mockReturnValue({
      data: [],
      isLoading: false,
    }),
  },
}));

// Mock notesApi
vi.mock('@/redux/features/notes/notes.api', () => {
  const notesApi = {
    reducerPath: 'notesApi',
    reducer: (state = {}) => state,
    middleware: () => (next: any) => (action: any) => next(action),
    useGetNotesByCustomerIdQuery: vi.fn().mockReturnValue({
      data: [],
      isLoading: false,
    }),
  };
  return { notesApi };
});

vi.mock('@/redux/features/common-api/common.api', async () => {
  const actual = await import('@/redux/features/common-api/common.api');
  return {
    ...actual,
    useGetListItemsMutation: vi.fn().mockReturnValue([
      vi.fn(),
      {
        data: [],
        isLoading: false,
        isError: false,
      },
    ]),
  };
});

const mockStore = configureStore({
  reducer: {
    loginProfile: () => ({
      profile: {
        defaultLocationId: '1',
      },
    }),
    subTypes: () => ({
      subTypesData: [],
      selectedSubTypes: [],
    }),
    notes: () => ({
      open: false,
    }),
    [customerApi.reducerPath]: customerApi.reducer,
    [countyList.reducerPath]: countyList.reducer,
    [listApi.reducerPath]: listApi.reducer,
    [notesApi.reducerPath]: notesApi.reducer,
    [commonApi.reducerPath]: commonApi.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware()
      .concat(customerApi.middleware)
      .concat(countyList.middleware)
      .concat(listApi.middleware)
      .concat(notesApi.middleware)
      .concat(commonApi.middleware),
});

// Wrapper component
const Wrapper = ({ children }: { children: React.ReactNode }) => {
  const methods = useForm();
  return <FormProvider {...methods}>{children}</FormProvider>;
};

// Mock useParams and useNavigate
const mockUseParams = vi.fn();
const mockUseNavigate = vi.fn();

vi.mock('react-router-dom', async (importOriginal) => {
  const actual = await importOriginal<typeof import('react-router-dom')>();
  return {
    ...actual,
    useParams: () => mockUseParams(),
    useNavigate: () => mockUseNavigate,
  };
});

describe('NewCustomer', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockUseParams.mockReturnValue({});
    mockUseNavigate.mockImplementation(() => vi.fn());
  });

  it('renders loading spinner when fetching data', () => {
    // Mock loading state
    (useGetCustomerByIdQuery as any).mockReturnValueOnce({
      data: undefined,
      isFetching: true,
    });

    render(
      <Provider store={mockStore}>
        <MemoryRouter>
          <NewCustomer />
        </MemoryRouter>
      </Provider>
    );

    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('renders "New Customer" header when no customerId is provided', async () => {
    render(
      <Provider store={mockStore}>
        <MemoryRouter>
          <Wrapper>
            <NewCustomer />
          </Wrapper>
        </MemoryRouter>
      </Provider>
    );

    await waitFor(() => {
      expect(screen.getByText('New Customer')).toBeInTheDocument();
    });
  });

  it('renders "Edit Customer" header when customerId is provided', async () => {
    mockUseParams.mockReturnValue({ customerId: '1' });

    render(
      <Provider store={mockStore}>
        <MemoryRouter initialEntries={['/customers/edit-customer/1']}>
          <Wrapper>
            <NewCustomer />
          </Wrapper>
        </MemoryRouter>
      </Provider>
    );

    await waitFor(() => {
      expect(screen.getByText('Edit Customer')).toBeInTheDocument();
    });
  });

  it('pre-fills form with customer data when customerId is provided', async () => {
    mockUseParams.mockReturnValue({ customerId: '1' });

    render(
      <Provider store={mockStore}>
        <MemoryRouter initialEntries={['/customers/edit-customer/1']}>
          <Wrapper>
            <NewCustomer />
          </Wrapper>
        </MemoryRouter>
      </Provider>
    );

    await waitFor(() => {
      expect(screen.getByDisplayValue('John')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Doe')).toBeInTheDocument();
      expect(screen.getByDisplayValue('123 Main St')).toBeInTheDocument();
    });
  });

  it('uses default store location values when creating new customer', async () => {
    const isRendered = render(
      <Provider store={mockStore}>
        <MemoryRouter>
          <Wrapper>
            <NewCustomer />
          </Wrapper>
        </MemoryRouter>
      </Provider>
    );
    expect(isRendered);
  });

  it('submits form data for new customer', async () => {
    const mockNavigate = vi.fn();
    mockUseParams.mockReturnValue({});
    mockUseNavigate.mockImplementation(() => mockNavigate);
    render(
      <Provider store={mockStore}>
        <MemoryRouter>
          <Wrapper>
            <NewCustomer />
          </Wrapper>
        </MemoryRouter>
      </Provider>
    );
    await waitFor(() => {
      fireEvent.change(screen.getByLabelText(/first name/i), {
        target: { value: 'Jane' },
      });
      fireEvent.change(screen.getByLabelText(/last name/i), {
        target: { value: 'Smith' },
      });
      fireEvent.click(screen.getByText('Save'));
    });

    await waitFor(() => {
      expect(mockNavigate);
    });
  });

  it('submits form data for existing customer', async () => {
    mockUseParams.mockReturnValue({ customerId: '1' });

    render(
      <Provider store={mockStore}>
        <MemoryRouter initialEntries={['/customers/edit-customer/1']}>
          <Wrapper>
            <NewCustomer />
          </Wrapper>
        </MemoryRouter>
      </Provider>
    );

    await waitFor(() => {
      fireEvent.change(screen.getByLabelText(/first name/i), {
        target: { value: 'Updated Name' },
      });
      fireEvent.click(screen.getByText('Save'));
    });
  });

  it('handles tab switching for customer other info', async () => {
    mockUseParams.mockReturnValue({ customerId: '1' });
    render(
      <Provider store={mockStore}>
        <MemoryRouter initialEntries={['/customers/edit-customer/1']}>
          <Wrapper>
            <NewCustomer />
          </Wrapper>
        </MemoryRouter>
      </Provider>
    );
    await waitFor(() => {
      const contactInfoTab = screen.getByText('Contact Info');
      fireEvent.click(contactInfoTab);
      expect(contactInfoTab);
    });
  });

  it('handles API errors', async () => {
    (useGetCustomerByIdQuery as any).mockReturnValueOnce({
      data: undefined,
      error: new Error('API Error'),
      isFetching: false,
    });

    const { container } = render(
      <Provider store={mockStore}>
        <MemoryRouter initialEntries={['/customers/edit-customer/1']}>
          <Wrapper>
            <NewCustomer />
          </Wrapper>
        </MemoryRouter>
      </Provider>
    );

    expect(container).toBeInTheDocument();
  });
});
