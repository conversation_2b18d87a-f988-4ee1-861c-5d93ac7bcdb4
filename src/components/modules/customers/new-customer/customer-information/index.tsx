import InputField from '@/components/forms/input-field';
import SelectWidget from '@/components/forms/select';
import ZipCodeInput from '@/components/forms/zipcode-input';
import { Separator } from '@/components/ui/separator';
import {
  CUSTOMER_TYPE_API_ROUTES,
  STORE_OPTIONS_API_ROUTES,
} from '@/constants/api-constants';
import { TEXT_VALIDATION_RULE } from '@/constants/validation-constants';
import useOptionList from '@/hooks/useOptionList';
import { generateLabelValuePairs, searchPayload } from '@/lib/utils';
import {
  useGetCountryListQuery,
  useGetStateByCountryQuery,
} from '@/redux/features/country/country.api';
import { useGetListItemsMutation } from '@/redux/features/list/category/list.api';
import { CountryType, FormData } from '@/types/customer.types';
import React, { useCallback, useEffect, useState } from 'react';
import { useFormContext } from 'react-hook-form';

const statusList = [
  { label: 'Active', value: 'A' },
  { label: 'Inactive', value: 'I' },
];

const CustomerInformation = () => {
  const form = useFormContext<FormData>();
  const countryId = form.watch('country_id');

  // Destructure and provide default empty arrays
  const { data: statesData = [], isFetching: stateIsLoading } =
    useGetStateByCountryQuery(
      {
        countryId,
      },
      {
        skip: !countryId,
      }
    );

  const [getList, { data: customerTypeData, isLoading }] =
    useGetListItemsMutation();
  const [stateList, setStateList] = useState<
    Array<{ label: string; value: string }>
  >([]);

  const { data: countryData = [] } = useGetCountryListQuery();

  // Memoize generated lists to prevent unnecessary re-renders
  const customerTypeList = React.useMemo(
    () =>
      generateLabelValuePairs({
        data: customerTypeData?.data,
        labelKey: 'description',
        valueKey: 'custtype_id',
      }),
    [customerTypeData]
  );

  const countryList = React.useMemo(
    () =>
      generateLabelValuePairs({
        data: countryData as CountryType[],
        labelKey: 'name',
        valueKey: 'name',
      }),
    [countryData]
  );

  const { options: storeLocationList, optionLoading } = useOptionList({
    url: STORE_OPTIONS_API_ROUTES.ALL,
    labelKey: 'locationName',
    valueKey: 'id',
    sortBy: 'locationName',
  });

  // Use useCallback for consistent function reference
  const fetchListItems = useCallback(() => {
    getList({
      url: CUSTOMER_TYPE_API_ROUTES.ALL,
      data: searchPayload({
        pageNumber: -1,
        pageSize: -1,
        sortBy: '',
        sortAscending: true,
        filters: [],
      }),
    });
  }, [getList]);

  // Fetch list items on component mount
  useEffect(() => {
    fetchListItems();
  }, [fetchListItems]);

  const handleCountryChange = (value: string) => {
    form.setValue('country', value);
    const newCountry = countryData.find((element) => element.name === value);
    form.setValue('country_id', newCountry?.country_id ?? 1);
    form.setValue('state', '');
    form.setValue('zipcode', '');
    form.clearErrors('zipcode');
  };

  // Ensure states data is used to generate state list
  useEffect(() => {
    if (statesData?.length) {
      const newStateList = generateLabelValuePairs({
        data: statesData,
        labelKey: 'code',
        valueKey: 'code',
      });
      setStateList(newStateList);
    }
  }, [statesData]); // Only re-run when statesData changes

  const country = form.watch('country');
  const isUSA = ['USA']?.includes(country);
  return (
    <div className="col-span-6 border p-8 rounded-lg bg-white">
      <h3 className="text-xl font-normal text-text-Default mb-2">
        Customer Info
      </h3>
      <Separator
        orientation="horizontal"
        className="h-[1px] bg-border-Default mb-4"
      />

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-4">
        {/* First Name */}

        <InputField
          form={form}
          name="first_name"
          label="First Name"
          placeholder="Enter First Name"
          validation={{ required: 'Required' }}
        />

        {/* Last Name */}
        <InputField
          form={form}
          name="last_name"
          label="Last Name"
          placeholder="Enter Last Name"
        />

        {/* Customer Type */}

        <SelectWidget
          form={form}
          name="customerType"
          label="Customer Type"
          placeholder="Select Customer Type"
          enableSearch={true}
          isClearable={false}
          optionsList={customerTypeList}
          validation={TEXT_VALIDATION_RULE}
          isLoading={isLoading}
        />

        {/* Status */}
        <SelectWidget
          form={form}
          name="isactive"
          label="Status"
          isClearable={false}
          placeholder="Select Status"
          optionsList={statusList}
        />

        {/* Store Location */}

        <SelectWidget
          form={form}
          name="defstorelocationno"
          label="Store Location"
          placeholder="Select Store Location"
          isClearable={false}
          validation={TEXT_VALIDATION_RULE}
          optionsList={storeLocationList}
          isLoading={optionLoading}
        />

        {/* Address Line 1 */}

        <InputField
          form={form}
          name="address1"
          label="Address Line 1"
          placeholder="Enter Address Line 1"
        />

        {/* Address Line 2 */}

        <InputField
          form={form}
          name="address2"
          label="Address Line 2"
          placeholder="Enter Address Line 2"
        />

        {/* City */}

        <InputField
          form={form}
          name="city"
          label="City"
          placeholder="Enter City"
        />

        {/* State */}
        <SelectWidget
          name="state"
          enableSearch={true}
          form={form}
          placeholder="Select State"
          label="State"
          isClearable={false}
          optionsList={stateList}
          isLoading={stateIsLoading}
          validation={TEXT_VALIDATION_RULE}
        />

        {/* Zip Code */}
        <ZipCodeInput
          name="zipcode"
          isUSA={isUSA}
          form={form}
          label="Zip Code"
        />

        {/* Country */}
        <SelectWidget
          enableSearch={true}
          form={form}
          name="country"
          label="Country"
          isClearable={false}
          placeholder="Select Country"
          optionsList={countryList}
          onSelectChange={handleCountryChange}
        />
      </div>
    </div>
  );
};

export default CustomerInformation;
