import { render, screen } from '@testing-library/react';
import { vi, describe, expect, it, beforeEach } from 'vitest';
import CustomerInformation from '.';
import {
  useGetCountryListQuery,
  useGetStateByCountryQuery,
} from '@/redux/features/country/country.api';
import { useGetListItemsMutation } from '@/redux/features/list/category/list.api';

describe('CustomerInformation Component', () => {
  beforeEach(() => {
    // Mock API hooks directly
    vi.mock('@/redux/features/country/country.api', () => ({
      useGetCountryListQuery: vi.fn(),
      useGetStateByCountryQuery: vi.fn(),
    }));

    vi.mock('@/redux/features/list/category/list.api', () => ({
      useGetListItemsMutation: vi.fn(),
    }));

    vi.mock('@/hooks/useOptionList', () => ({
      __esModule: true,
      default: vi.fn(() => ({
        options: [],
        optionLoading: false,
      })),
    }));

    vi.mock('@/lib/utils', () => ({
      generateLabelValuePairs: vi.fn(() => []),
      cn: vi.fn(),
      getErrorMessage: vi.fn(),
      searchPayload: vi.fn(),
    }));

    // Mock react-hook-form, including Controller
    vi.mock('react-hook-form', async () => {
      const actual = await import('react-hook-form');
      return {
        ...actual,
        Controller: ({ name, field }: any) => (
          <input
            id={name}
            data-testid={`controller-${name}`}
            {...field}
            // You can set a default value for testing purposes if needed
          />
        ),
        useFormContext: vi.fn(() => ({
          control: {},
          setValue: vi.fn(),
          register: vi.fn(),
          formState: { isValid: true, errors: {} },
          watch: vi.fn(() => ''),
        })),
      };
    });

    (useGetStateByCountryQuery as any).mockReturnValue({
      data: [],
      isFetching: false,
      refetch: vi.fn(),
    });

    (useGetCountryListQuery as any).mockReturnValue({
      data: { data: [], isLoading: false, isError: false },
    });

    (useGetListItemsMutation as any).mockReturnValue([
      vi.fn(),
      { data: [], isLoading: false },
    ]);
  });

  it('renders the component and checks for required fields', async () => {
    render(<CustomerInformation />);

    expect(screen.getByTestId('controller-first_name')).toBeInTheDocument();
    expect(screen.getByTestId('controller-last_name')).toBeInTheDocument();
  });
});
