import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import NewContact from './NewContact';
import { Provider } from 'react-redux';
import { store } from '@/redux/store';
import { MemoryRouter, useParams } from 'react-router-dom';

// Mock react-router-dom
vi.mock('react-router-dom', async () => {
  const actual = await import('react-router-dom');
  return {
    ...actual,
    useParams: vi.fn(),
  };
});

// Mock choices API
vi.mock('@/redux/features/customers/choices.api', () => ({
  useIncludeInBatchQuery: () => ({ data: { data: [] } }),
  usePhoneTypeQuery: () => ({ data: { data: [] } }),
  choicesApi: {
    reducerPath: 'choicesApi',
    reducer: (state = { data: [] }) => state,
    middleware: () => (next: any) => (action: any) => next(action),
  },
}));

// Mock contacts API
vi.mock('@/redux/features/customers/contacts.api', () => ({
  useCreateContactMutation: () => [vi.fn(), { isLoading: false }],
  useGetContactByIdMutation: () => [vi.fn(), { data: null, isLoading: false }],
  useUpdateContactMutation: () => [vi.fn(), { isLoading: false }],
  contactsApi: {
    reducerPath: 'contactsApi',
    reducer: (state = { data: [] }) => state,
    middleware: () => (next: any) => (action: any) => next(action),
  },
}));

// Mock toast container
vi.mock('@/components/ui/toast/ToastContainer', () => ({
  UseToast: () => ({
    success: vi.fn(),
    error: vi.fn(),
  }),
}));

// Mock form components
vi.mock('@/components/forms/input-field', () => ({
  default: ({ label, name }: any) => (
    <div>
      <label htmlFor={name}>{label}</label>
      <input id={name} name={name} data-testid={name} />
    </div>
  ),
}));

vi.mock('@/components/forms/select-dropdown', () => ({
  default: ({ name }: any) => (
    <select name={name} data-testid={name}>
      <option value="">Select</option>
    </select>
  ),
}));

vi.mock('@/components/forms/phone-input-mask', () => ({
  default: ({ name }: any) => (
    <input id={name} name={name} data-testid={name} />
  ),
}));

vi.mock('@/assets/icons/TrashIcon', () => ({
  default: () => <div data-testid="trash-icon">TrashIcon</div>,
}));

describe('NewContact Component', () => {
  const mockSetOpen = vi.fn();
  const mockOnContactCreatedOrUpdated = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    (useParams as any).mockReturnValue({ customerId: '123' });
  });

  it('renders the NewContact component', () => {
    render(
      <Provider store={store}>
        <MemoryRouter>
          <NewContact
            open={{ state: true, mode: 'create' }}
            setOpen={mockSetOpen}
            onContactCreatedOrUpdated={mockOnContactCreatedOrUpdated}
          />
        </MemoryRouter>
      </Provider>
    );

    expect(screen.getByText('First Name')).toBeInTheDocument();
  });
});
