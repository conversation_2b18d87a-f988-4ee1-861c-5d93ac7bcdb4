import { render, screen } from '@testing-library/react';
import { describe, vi, beforeEach, test, expect } from 'vitest';
import AdditionalContacts from './index';
import { BrowserRouter } from 'react-router-dom';
import AppTableContextProvider from '@/components/common/app-data-table/AppTableContext';

// Mocking API hooks
vi.mock('@/redux/features/customers/contacts.api', () => ({
  useDeleteContactMutation: vi.fn(() => [vi.fn(), { isLoading: false }]),
}));

vi.mock('@/redux/features/common-api/common.api', () => ({
  useGetAllMutation: vi.fn(() => [
    vi.fn(),
    { data: { pagination: { totalCount: 0 } }, isLoading: false },
  ]),
}));

vi.mock('@/redux/features/customers/choices.api', () => ({
  useIncludeInBatchQuery: () => ({ data: { data: [] } }),
  usePhoneTypeQuery: () => ({ data: { data: [] } }),
}));

describe('AdditionalContacts Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('renders component with heading', () => {
    render(
      <BrowserRouter>
        <AppTableContextProvider>
          <AdditionalContacts />
        </AppTableContextProvider>
      </BrowserRouter>
    );

    expect(screen.getByText('Contact Details')).toBeInTheDocument();
  });
});
