import TrashIcon from '@/assets/icons/TrashIcon';
import AppButton from '@/components/common/app-button';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import AppSpinner from '@/components/common/app-spinner';
import S<PERSON><PERSON>ield from '@/components/common/switch';
import Input<PERSON>ield from '@/components/forms/input-field';
import PhoneInputWidget from '@/components/forms/phone-input-mask';
import SelectDropDown from '@/components/forms/select-dropdown';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { UseToast } from '@/components/ui/toast/ToastContainer';
import { EMAIL_VALIDATION_RULEOptional } from '@/constants/auth-constants';
import {
  generateLabelValuePairs,
  getQueryParam,
  updateQueryParam,
} from '@/lib/utils';
import {
  useIncludeInBatchQuery,
  usePhoneTypeQuery,
} from '@/redux/features/customers/choices.api';
import {
  useCreateContactMutation,
  useGetContactByIdMutation,
  useUpdateContactMutation,
} from '@/redux/features/customers/contacts.api';
import { AdditionalContactDto, PhoneNumbersDto } from '@/types/customer.types';
import { useEffect, useMemo, useState } from 'react';
import { useFieldArray, useForm } from 'react-hook-form';
import { useParams } from 'react-router-dom';
import { OpenDialogPropsType } from '.';

const initialState = {
  state: false,
  mode: '',
  id: undefined,
};

// Define types for component props
interface NewContactProps {
  open: OpenDialogPropsType;
  setOpen: React.Dispatch<React.SetStateAction<OpenDialogPropsType>>;
  onContactCreatedOrUpdated: () => void;
}

// PhoneTable Component with types
const PhoneTable = ({
  fields,
  form,
  remove,
}: {
  fields: PhoneNumbersDto[];
  form: any;
  append: (value: any) => void;
  remove: (index: number) => void;
}) => {
  const { data } = usePhoneTypeQuery();
  const phoneTypeList = generateLabelValuePairs({
    data: data?.data,
    labelKey: 'description',
    valueKey: 'phonetype_id',
  });

  const [openDeleteDialog, setOpenDeleteDialog] = useState<boolean>(false);
  const [deleteId, setDeleteId] = useState<number>(0);

  const toggleOpenDeleteDialog = (id?: number) => {
    setOpenDeleteDialog((prev) => !prev);
    setDeleteId(id || 0);
  };
  // Handle deletion of phone number
  const handleDelete = async () => {
    // If no phone_id, remove it directly from the form
    remove(deleteId);
    setOpenDeleteDialog(false);
  };

  return (
    <div className="col-span-2 border-[1px] border-grayScale-20">
      <Table className="rounded-sm" tableClassName="max-h-72 overflow-auto">
        <TableHeader className="sticky top-0 z-10 bg-grayScale-10">
          <TableRow className="text-base font-medium">
            <TableHead className="text-grayScale-90 border-r-[1px] border-grayScale-20">
              Phone Number
            </TableHead>
            <TableHead className="text-grayScale-90 border-grayScale-20">
              Type
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {fields.length === 0 ? (
            <TableRow>
              <TableCell colSpan={2} className="text-center text-grayScale-70">
                No Records Found
              </TableCell>
            </TableRow>
          ) : (
            fields.map((_field, index) => (
              <TableRow key={index}>
                <TableCell className="border-r-[1px] border-grayScale-20">
                  <PhoneInputWidget
                    form={form}
                    name={`phones[${index}].phoneno`}
                    // errorMessage={
                    //   form?.formState?.errors?.phones?.[index]?.phoneno
                    //     ?.message ?? ''
                    // }
                  />
                </TableCell>
                <TableCell className="border-grayScale-20 flex flex-row gap-4 items-center">
                  <SelectDropDown
                    className="w-40"
                    form={form}
                    name={`phones[${index}].phonetype_id`}
                    placeholder="Type"
                    optionsList={phoneTypeList}
                    allowClear={true}
                  />
                  <div
                    onClick={() => toggleOpenDeleteDialog(index)}
                    className="cursor-pointer"
                  >
                    <TrashIcon />
                  </div>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
      <AppConfirmationModal
        description={<div>Are you sure you want to delete this phone?</div>}
        open={openDeleteDialog}
        onOpenChange={toggleOpenDeleteDialog}
        handleCancel={toggleOpenDeleteDialog}
        handleSubmit={handleDelete}
      />
    </div>
  );
};

const NewContact = ({
  open,
  setOpen,
  onContactCreatedOrUpdated,
}: NewContactProps) => {
  // Get contact ID from query params and customer ID from URL
  const { customerId } = useParams<{ customerId?: string }>();

  // Retrieve the customer ID from the URL query parameters
  const customerIdFromQuery = getQueryParam('customerId');

  const [contactData, setContactData] = useState<AdditionalContactDto | null>(
    null
  );

  // Fetch batch and phone type data
  const { data: getIncludeInBatchList } = useIncludeInBatchQuery();
  const [getContactById, { data, isLoading }] = useGetContactByIdMutation();
  const [createContact, { isLoading: createContactLoading }] =
    useCreateContactMutation();
  const [updateContact, { isLoading: updateContactLoading }] =
    useUpdateContactMutation();

  const defaultValues = useMemo(() => {
    return {
      first_name: contactData?.first_name ?? '',
      last_name: contactData?.last_name ?? '',
      title: contactData?.title ?? '',
      batch: contactData?.batch ?? '',
      isprimary: contactData?.isprimary ?? false,
      phones: contactData?.phones?.length
        ? contactData?.phones?.map((phone) => {
            return {
              phoneno: phone?.phoneno ?? '',
              phonetype_id: phone.phonetype_id === 0 ? '' : phone.phonetype_id,
            };
          })
        : [{ phoneno: '', phonetype_id: '' }],
    };
  }, [
    contactData?.batch,
    contactData?.first_name,
    contactData?.isprimary,
    contactData?.last_name,
    contactData?.phones,
    contactData?.title,
  ]);

  // Initialize form with react-hook-form
  const form = useForm<AdditionalContactDto>({
    defaultValues: defaultValues,
    mode: 'onChange',
  });
  const { control } = form;
  const { fields, append, remove } = useFieldArray({
    control,
    name: 'phones',
  });

  const toast = UseToast();

  // Fetch contact data if editing an existing contact
  useEffect(() => {
    if (open.id) {
      getContactById(Number(open.id));
    }
  }, [getContactById, open.id]);

  // Update form values when contact data is fetched
  useEffect(() => {
    if (data && data?.data && open.id) {
      setContactData(data?.data);
    }
  }, [data, open.id]);

  useEffect(() => {
    if (contactData && open.id) {
      form.reset(defaultValues);
    }
  }, [contactData, defaultValues, form, open.id]);

  // Submit form data to create or update contact
  const onSubmit = async (data: AdditionalContactDto) => {
    const phoneData = data.phones
      ?.filter(
        (element) => element.phoneno.replace(/^\+1/, '') || element.phonetype_id
      )
      ?.map((element) => {
        return {
          phone_id: element.phone_id ?? 0,
          phoneno: element.phoneno,
          phonetype_id: Number(element.phonetype_id) ?? null,
        };
      });
    const payload = {
      contact_id: open.id ? open.id : 0,
      first_name: data.first_name,
      last_name: data.last_name,
      email: data?.email,
      title: data.title,
      isprimary: data.isprimary,
      phones: phoneData,
      batch: data.batch,
      linkid: Number(customerId || customerIdFromQuery),
      isactive: true,
    };

    try {
      let response;
      if (open.id) {
        response = await updateContact({
          contactId: open.id,
          contactData: payload,
        });
        updateQueryParam(null, 'contactId');
      } else {
        response = await createContact(payload);
      }

      if (response?.data?.message) {
        toast.success(response.data.message);
        form.reset(defaultValues);
        setOpen(initialState); // Close the modal after success
        onContactCreatedOrUpdated();
      }
    } catch (error) {
      toast.error('Error occurred while saving the contact.');
    }
  };

  // Transform batch options for dropdown
  const includeInBatchList = getIncludeInBatchList?.data?.map((item: any) => {
    const key = Object.keys(item)[0];
    const value = item[key];
    return { label: key, value };
  });

  return (
    <>
      <div className="flex flex-col h-[70vh] justify-between overflow-auto">
        <div className="grid grid-cols-2 gap-4 pl-6 pr-6 pb-3 pt-3 ">
          <InputField
            form={form}
            maxLength={32}
            name="first_name"
            label="First Name"
            placeholder="Enter First Name"
            validation={{
              required: 'Required',
            }}
          />
          <InputField
            maxLength={32}
            form={form}
            name="last_name"
            label="Last Name"
            placeholder="Enter Last Name"
          />
          <InputField
            maxLength={32}
            form={form}
            name="title"
            label="Title"
            placeholder="Enter Title"
          />
          <InputField
            form={form}
            name="email"
            label="Email"
            maxLength={64}
            placeholder="Enter Email"
            validation={EMAIL_VALIDATION_RULEOptional}
          />
          <SelectDropDown
            form={form}
            optionsList={includeInBatchList ?? []}
            label="Include in Batch"
            name="batch"
            placeholder="Select Include in Batch"
          />

          <div className="col-span-2">
            {' '}
            <SwitchField
              label="Set as Primary Contact"
              form={form}
              name="isprimary"
            />
          </div>
          <div className="col-span-2  text-left text-text-neutral-Default flex justify-end">
            <button
              onClick={() => {
                append({
                  phoneno: '',
                  phonetype_id: '',
                });
              }}
            >
              {' '}
              + Add New Phone
            </button>
          </div>
          <div className="col-span-2  flex flex-col  pb-3 gap-4">
            <PhoneTable
              fields={fields}
              form={form}
              append={append}
              remove={remove}
            />
          </div>
        </div>

        <div className="absolute bottom-0 w-full flex flex-row gap-4 pl-6 pr-6 pb-2  m-auto z-20">
          <AppButton
            label="Submit"
            className="w-full"
            isLoading={createContactLoading || updateContactLoading}
            onClick={form.handleSubmit(onSubmit)}
          />
          <AppButton
            label="Cancel"
            className="w-full"
            variant="neutral"
            onClick={() => {
              setOpen(initialState);
            }}
          />
        </div>
      </div>
      {open.id && <AppSpinner overlay isLoading={isLoading} />}
    </>
  );
};

export default NewContact;
