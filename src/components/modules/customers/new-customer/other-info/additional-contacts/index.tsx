import EditIcon from '@/assets/icons/EditIcon';
import UserPlusIcon from '@/assets/icons/UserPlusIcon';
import AppButton from '@/components/common/app-button';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import AppDataTable from '@/components/common/app-data-table';
import AppTableContextProvider from '@/components/common/app-data-table/AppTableContext';
import ActionColumnMenu from '@/components/common/data-tables/ActionColumn';
import CustomDialog from '@/components/common/dialog';
import { CONTACTS_API_ROUTES } from '@/constants/api-constants';
import { cn, getQueryParam } from '@/lib/utils';
import { useDeleteContactMutation } from '@/redux/features/customers/contacts.api';
import { AdditionalContactDto } from '@/types/customer.types';
import { ColumnDef } from '@tanstack/react-table';
import { useCallback, useMemo, useState } from 'react';
import { useParams } from 'react-router-dom';
import NewContact from './NewContact';

export interface OpenDialogPropsType {
  state: boolean;
  mode: string;
  id?: number;
}

const initialState = {
  state: false,
  mode: '',
  id: undefined,
};
interface AdditionalContactsProps {
  heading?: string;
  tableClassName?: string;
}

const AdditionalContacts = ({
  heading = 'Contact Details',
  tableClassName,
}: AdditionalContactsProps) => {
  const { customerId } = useParams<{ customerId?: string }>();
  const customerIdFromQuery = getQueryParam('customerId');

  const [open, setOpen] = useState<OpenDialogPropsType>(initialState);
  const [openDeleteDialog, setOpenDeleteDialog] = useState<boolean>(false);
  const [customerToDelete, setCustomerToDelete] = useState<number>(0);
  const [refresh, setRefresh] = useState<boolean>(false);

  // API hooks for handling contacts
  const [deleteContact, { isLoading: isDeleteLoading }] =
    useDeleteContactMutation();

  const toggleRefresh = useCallback(() => {
    setRefresh(true);
    setTimeout(() => {
      setRefresh(false);
    }, 200);
  }, []);

  // Custom toolbar button for opening New Contact form
  const CustomToolbar = (
    <AppButton
      icon={UserPlusIcon}
      label="New Contact"
      onClick={() => setOpen({ ...open, mode: 'create', state: true })}
    />
  );

  // Open/close delete confirmation modal
  const onOpenChange = useCallback(() => {
    setOpenDeleteDialog((prevState) => !prevState);
  }, []);

  const handleDeleteCustomer = useCallback(async () => {
    try {
      await deleteContact(customerToDelete).unwrap();
      onOpenChange(); // Close delete dialog
      setCustomerToDelete(0); // Reset customer to delete
      toggleRefresh(); // refreshContacts(); // Refresh contacts after delete
    } catch (error) {
      // Handle error (optional)
    }
  }, [customerToDelete, deleteContact, onOpenChange, toggleRefresh]);

  // Set customer ID to delete and open delete modal
  const handleDeleteClick = useCallback(
    (id: number) => {
      setCustomerToDelete(id);
      onOpenChange();
    },
    [onOpenChange]
  );

  // Memoized column definitions for the contact table
  const columns: ColumnDef<AdditionalContactDto>[] = useMemo(
    () => [
      {
        accessorKey: 'first_name',
        header: 'Name',
        size: 100,
        cell: (info) => (
          <div>
            {info?.row?.original?.first_name} {info?.row?.original?.last_name}
          </div>
        ),
      },
      {
        accessorKey: 'email',
        header: 'Email',
        size: 100,
      },
      {
        accessorKey: 'title',
        header: 'Title',
        size: 100,
      },
      {
        accessorKey: 'batchname',
        header: 'Include in Batch',
        size: 100,
      },
      {
        id: 'action',
        size: 110,
        header: 'Actions',
        cell: ({ row }) => (
          <ActionColumnMenu
            customEdit={
              <div
                onClick={() => {
                  setOpen({
                    ...open,
                    mode: 'edit',
                    state: true,
                    id: row?.original?.contact_id,
                  });
                }}
              >
                <EditIcon />
              </div>
            }
            onDelete={() =>
              handleDeleteClick(row?.original?.contact_id as number)
            }
            contentClassName="w-fit z-[99]"
          />
        ),
      },
    ],
    [handleDeleteClick, open]
  );

  const toggleDialog = useCallback(() => {
    setOpen(initialState);
  }, []);

  return (
    <AppTableContextProvider>
      <>
        <div className="grid grid-cols-1 gap-6">
          <AppDataTable
            columns={columns}
            url={CONTACTS_API_ROUTES.ALL(
              (customerId || customerIdFromQuery) ?? ''
            )}
            enablePagination
            customToolBar={CustomToolbar}
            heading={
              heading && (
                <h1 className="text-2xl font-semibold text-[#181A1D]">
                  {heading}
                </h1>
              )
            }
            tableClassName={cn(tableClassName)}
            noDataPlaceholder="No Contacts Added Yet"
            refreshList={refresh}
          />
        </div>
        <CustomDialog
          title={open.mode === 'edit' ? 'Edit Contact' : 'New Contact'}
          open={open.state}
          onOpenChange={toggleDialog}
          className="min-w-[40%] md:min-w-[30%]"
        >
          <NewContact
            open={open}
            setOpen={setOpen}
            onContactCreatedOrUpdated={toggleRefresh}
          />
        </CustomDialog>

        <AppConfirmationModal
          description={<div>Are you sure you want to delete this contact?</div>}
          open={openDeleteDialog}
          onOpenChange={onOpenChange}
          handleCancel={onOpenChange}
          disabled={isDeleteLoading}
          handleSubmit={handleDeleteCustomer}
          isLoading={isDeleteLoading}
        />
      </>
    </AppTableContextProvider>
  );
};

export default AdditionalContacts;
