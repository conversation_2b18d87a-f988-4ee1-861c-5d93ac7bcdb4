import { render, screen } from '@testing-library/react';
import { FormProvider, useForm } from 'react-hook-form';
import AccountDetails from '.';
import { describe, it, expect, vi } from 'vitest';

vi.mock('@/redux/features/referral-type/referral-type.api', () => ({
  useGetReferralTypeQuery: vi.fn(() => ({
    data: { data: [] },
    isFetching: false,
  })),
}));

describe('AccountDetails Component', () => {
  const Wrapper = ({ children }: any) => {
    const form = useForm({
      defaultValues: {
        discountpercent: '',
        reftype_id: '',
        referralcommpct: '',
        blanketpo: '',
        exemptno: '',
        licenseno: '',
        linkedcustno: '',
        acctinfo: '',
        defspecialinstr: '',
        autoemails: false,
        damwaiv: '',
        damwaivpct: '',
        fuelchgpct: '',
        altemailaddress: '',
        invoicecomm: '',
        statementcomm: '',
      },
    });

    return <FormProvider {...form}>{children}</FormProvider>;
  };

  it('should render AccountDetails component correctly', () => {
    render(
      <Wrapper>
        <AccountDetails />
      </Wrapper>
    );

    // Check for the presence of form elements
    expect(screen.getByText('Base Discount %')).toBeInTheDocument();
    expect(screen.getByText('Referral Type')).toBeInTheDocument();
  });

  it('should display loading state for referral type dropdown', () => {
    vi.mock('@/redux/features/referral-type/referral-type.api', () => ({
      useGetReferralTypeQuery: vi.fn(() => ({
        data: null,
        isFetching: true,
      })),
    }));

    render(
      <Wrapper>
        <AccountDetails />
      </Wrapper>
    );

    expect(screen.getByText('Select Referral Type')).toBeInTheDocument();
  });
});
