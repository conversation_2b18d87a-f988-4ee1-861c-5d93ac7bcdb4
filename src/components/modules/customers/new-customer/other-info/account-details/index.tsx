import SwitchField from '@/components/common/switch';
import Input<PERSON>ield from '@/components/forms/input-field';
import Labels from '@/components/forms/Label';
import NumberInputField from '@/components/forms/number-input-field';
import RadioField from '@/components/forms/radio-field';
import SelectWidget from '@/components/forms/select';
import Text<PERSON>reaField from '@/components/forms/text-area';
import { Separator } from '@/components/ui/separator';
import { EMAIL_VALIDATION_RULEOptional } from '@/constants/auth-constants';
import { invoiceDeliveryMethods } from '@/constants/customer-constants';
import { generateLabelValuePairs } from '@/lib/utils';
import { useGetReferralTypeQuery } from '@/redux/features/referral-type/referral-type.api';
import { FormData } from '@/types/customer.types';
import { useFormContext } from 'react-hook-form';

const AccountDetails = () => {
  const { data: referralTypeData, isFetching } = useGetReferralTypeQuery();
  const form = useFormContext<FormData>();

  const referralTypeList = generateLabelValuePairs({
    data: referralTypeData?.data,
    labelKey: 'description',
    valueKey: 'reftype_id',
  });

  return (
    <div className="bg-background-default-hover p-8 flex flex-col gap-4">
      <div className="flex flex-col gap-3">
        <h3 className="text-xl text-text-Default">Account Details</h3>
        <Separator
          orientation="horizontal"
          className=" h-[1px] bg-border-Default"
        />
      </div>
      <div className="flex flex-row justify-between gap-12 w-full">
        <div className="grid grid-cols-2 gap-4 w-full">
          <div className="col-span-2">
            <NumberInputField
              name="discountpercent"
              form={form}
              placeholder="__.__%"
              label="Base Discount %"
              maxLength={6}
              suffix="%"
              fixedDecimalScale
            />
          </div>
          <SelectWidget
            form={form}
            name="reftype_id"
            label="Referral Type"
            placeholder="Select Referral Type"
            optionsList={referralTypeList}
            isClearable={true}
            isLoading={isFetching}
          />
          <NumberInputField
            name="referralcommpct"
            form={form}
            placeholder="__.__%"
            label="Referral Comission %"
            maxLength={6}
            suffix="%"
            fixedDecimalScale
          />

          <NumberInputField
            name="blanketpo"
            form={form}
            placeholder="Enter Blanket PO #"
            label="Blanket PO #"
            maxLength={20}
          />

          <InputField
            form={form}
            name="exemptno"
            // type="number"
            maxLength={20}
            label="Exempt #"
            placeholder="Enter Exempt #"
          />
          <InputField
            form={form}
            name="licenseno"
            // type="number"
            maxLength={25}
            label="License #"
            placeholder="Enter License #"
          />
          <InputField
            form={form}
            name="linkedcustno"
            type="number"
            maxLength={15}
            label="Linked Customer #"
            placeholder="Enter Linked Customer #"
          />
          <TextAreaField
            form={form}
            name="acctinfo"
            label="Account Info"
            placeholder="Enter Account Info"
          />
          <TextAreaField
            maxLength={128}
            form={form}
            name="defspecialinstr"
            label="Default Special Instructions"
            placeholder="Enter Default Special Instructions"
          />
        </div>

        <Separator
          orientation="vertical"
          className=" w-[1px]  bg-border-Default"
        />

        <div className="grid grid-cols-2 gap-4 w-full">
          <div className="col-span-2">
            <SwitchField
              className="h-[74px]"
              form={form}
              name="autoemails"
              label="Auto Emails"
              description="Include customer on automatic e-mail reminders"
            />
          </div>
          <SelectWidget
            form={form}
            name="damwaiv"
            label="Damage Waiver"
            placeholder="Select Damage Waiver"
            isClearable={true}
            optionsList={[
              {
                label: 'Yes',
                value: 'true',
              },
              {
                label: 'No',
                value: 'false',
              },
            ]}
          />
          <NumberInputField
            name="damwaivpct"
            form={form}
            placeholder="__.__%"
            label="Damage Waiver %"
            maxLength={6}
            suffix="%"
            fixedDecimalScale
          />

          <div className="col-span-2">
            <NumberInputField
              name="fuelchgpct"
              form={form}
              placeholder="__.__%"
              label="Fuel Surcharge %"
              maxLength={6}
              suffix="%"
              fixedDecimalScale
            />
          </div>
          <div className="p-4 flex flex-col gap-8 col-span-2 border-[1px] border-border-Default rounded-lg">
            <InputField
              form={form}
              name="altemailaddress"
              label="Alternate Email for Invoices / Statements"
              placeholder="Enter Alternate Email"
              validation={EMAIL_VALIDATION_RULEOptional}
            />

            <div className="flex items-center justify-between">
              <Labels label="Invoices" />
              <RadioField
                form={form}
                name="invoicecomm"
                options={invoiceDeliveryMethods}
                optionsPerRow={3}
              />
            </div>
            <div className="flex items-center justify-between">
              <Labels label="Statements" />
              <RadioField
                form={form}
                name="statementcomm"
                options={invoiceDeliveryMethods}
                optionsPerRow={3}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AccountDetails;
