import DataTable from '@/components/common/data-tables';
import { useGetSubTypesMutation } from '@/redux/features/sub-types/sub-types.api';
import {
  setSelectedSubTypes,
  subTypesList,
} from '@/redux/features/sub-types/subTypesSlice';
import { CustomerSubTypeDto } from '@/types/customer.types';
import { ColumnDef, RowSelectionState } from '@tanstack/react-table';
import { useEffect, useMemo, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useParams } from 'react-router-dom';

const SubTypes = () => {
  const { customerId } = useParams<{ customerId?: string }>();
  const dispatch = useDispatch();
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});

  const [getList, { data, isLoading }] = useGetSubTypesMutation();
  // const [selectedRows, setSelectedRows] = useState<CustomerSubTypeDto[]>([]);

  // Memoized table columns
  const columns = useMemo<ColumnDef<CustomerSubTypeDto>[]>(
    () => [
      {
        accessorKey: 'description',
        header: 'Customer Type',
        size: 600,
      },
    ],
    []
  );

  // Fetch subtypes on component mount or when customerId changes
  useEffect(() => {
    if (customerId) {
      getList(Number(customerId));
    }
  }, [customerId, getList]);

  useEffect(() => {
    if (data && data?.data) {
      const initialRowSelection: RowSelectionState = {};
      data.data.forEach((item, index) => {
        if (item.isSelected) {
          initialRowSelection[index] = true;
        }
      });

      setRowSelection(initialRowSelection);
      dispatch(subTypesList(data?.data));
      const selectedSubTypeList = data?.data.filter(
        (element) => element.isSelected
      );

      dispatch(setSelectedSubTypes(selectedSubTypeList));
    }
  }, [data, dispatch]);

  useEffect(() => {
    const selectedSubTypes =
      data?.data.filter((_row, index) => rowSelection[index]) || [];
    dispatch(setSelectedSubTypes(selectedSubTypes as CustomerSubTypeDto[]));
  }, [data?.data, dispatch, rowSelection]);

  return (
    <DataTable
      tableClassName="max-h-92 overflow-auto"
      data={data?.data ?? []}
      columns={columns}
      enableSearch={false}
      heading={
        <h1 className="text-2xl font-semibold text-[#181A1D]">Sub-Types</h1>
      }
      enableColumnVisibility={false}
      enableRowSelection
      enablePagination={false}
      loaderRows={6}
      isLoading={isLoading}
      rowSelection={rowSelection}
      onRowSelectionChange={setRowSelection}
      enableMultiRowSelection
    />
  );
};

export default SubTypes;
