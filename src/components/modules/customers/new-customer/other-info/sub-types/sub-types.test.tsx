import { render, screen, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { MemoryRouter, Route, Routes } from 'react-router-dom';
import subTypesReducer from '@/redux/features/sub-types/subTypesSlice';
import { useGetSubTypesMutation } from '@/redux/features/sub-types/sub-types.api';
import SubTypes from '.';
import { vi, describe, beforeEach, test, expect } from 'vitest';

// Mock API mutation hook
vi.mock('@/redux/features/sub-types/sub-types.api', () => ({
  useGetSubTypesMutation: vi.fn(),
}));

describe('SubTypes Component', () => {
  const mockGetSubTypes = vi.fn();
  const mockDispatch = vi.fn();

  beforeEach(() => {
    (useGetSubTypesMutation as any).mockReturnValue([
      mockGetSubTypes,
      {
        data: {
          data: [{ id: 1, description: 'Test SubType', isSelected: true }],
        },
        isLoading: false,
      },
    ]);
    mockDispatch.mockClear();
    mockGetSubTypes.mockClear();
  });

  const renderWithProviders = (
    component: React.ReactNode,
    customerId: string
  ) => {
    const store = configureStore({
      reducer: { subTypes: subTypesReducer },
    });

    return render(
      <Provider store={store}>
        <MemoryRouter initialEntries={[`/sub-types/${customerId}`]}>
          <Routes>
            <Route path="/sub-types/:customerId" element={component} />
          </Routes>
        </MemoryRouter>
      </Provider>
    );
  };

  test('renders SubTypes component and displays data', async () => {
    renderWithProviders(<SubTypes />, '123');

    expect(screen.getByText('Customer Type')).toBeInTheDocument();
    expect(screen.getByText('Test SubType')).toBeInTheDocument();
  });

  test('calls API with customerId on mount', async () => {
    renderWithProviders(<SubTypes />, '123');

    await waitFor(() => {
      expect(mockGetSubTypes).toHaveBeenCalledWith(123);
    });
  });
});
