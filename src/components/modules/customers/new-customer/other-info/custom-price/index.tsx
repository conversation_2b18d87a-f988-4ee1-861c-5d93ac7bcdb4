import AppButton from '@/components/common/app-button';
import DataTable from '@/components/common/data-tables';
import CopyIcon from '@/assets/icons/CopyIcon';
import { customPriceData } from '@/mock-data/customer-mock-data';
import { CustomPriceTypes } from '@/types/customer.types';
import { ColumnDef } from '@tanstack/react-table';
import { useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import AmountCell from './AmountCell';
import CopyPriceToCustomer from './CopyPriceToCustomer';
import PercentageCell from './PercentageCell';
import PricingSection from './PricingSection';

interface PaginationType {
  pageSize: number;
  pageIndex: number;
}

const CustomPrice = () => {
  const [isEditingRow, setIsEditingRow] = useState<number | null>(null);
  const [open, setOpen] = useState(false);
  const form = useForm({
    defaultValues: { percentage: 'percentage', mode: 'onChange' },
  });
  const [search, setSearch] = useState<string>('');
  const [pagination, setPagination] = useState<PaginationType>({
    pageSize: 10,
    pageIndex: 0,
  });

  // Column definitions memoized
  const columns: ColumnDef<CustomPriceTypes>[] = useMemo(
    () => [
      { accessorKey: 'itemId', header: 'Item ID' },
      { accessorKey: 'category', header: 'Category' },
      { accessorKey: 'description', header: 'Description' },
      { accessorKey: 'quantity', header: 'Quantity', size: 80 },
      { accessorKey: 'cost', header: 'Cost', size: 90 },
      { accessorKey: 'unitPrice', header: 'Unit Price', size: 110 },
      {
        accessorKey: '',
        header: 'Percentage %',
        size: 150,

        cell: (props) => (
          <PercentageCell
            {...props}
            isEditing={isEditingRow === props.row.index}
          />
        ),
      },
      {
        accessorKey: '',
        header: 'Amount $',
        size: 220,
        cell: (props) => (
          <AmountCell
            {...props}
            isEditing={isEditingRow === props.row.index}
            setIsEditing={setIsEditingRow}
          />
        ),
      },
    ],
    [isEditingRow]
  );

  // Custom toolbar component
  const CustomToolbar = (
    <AppButton
      icon={CopyIcon}
      label="Copy Pricing to Customer"
      onClick={() => setOpen(true)}
    />
  );

  return (
    <div className="flex flex-col gap-8">
      {/* DataTable Section */}
      <DataTable
        search={search}
        customToolBar={CustomToolbar}
        setSearch={setSearch}
        data={customPriceData}
        columns={columns}
        enableSearch
        heading={
          <h1 className="text-2xl font-semibold text-[#181A1D]">
            Custom Pricing
          </h1>
        }
        enableColumnVisibility={false}
        totalItems={20}
        pagination={pagination}
        setPagination={setPagination}
        enableRowSelection
      />

      {/* Pricing Application Section */}
      <PricingSection form={form} />

      {/* Copy Pricing Modal */}
      <CopyPriceToCustomer open={open} onOpenChange={setOpen} />
    </div>
  );
};

export default CustomPrice;
