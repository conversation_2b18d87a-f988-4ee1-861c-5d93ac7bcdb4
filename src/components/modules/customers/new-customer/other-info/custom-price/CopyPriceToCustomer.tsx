import AppButton from '@/components/common/app-button';
import CheckVerified from '@/assets/icons/CheckVerified';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { RootState } from '@/redux/store';
import { CustomPriceTypes } from '@/types/customer.types';
import { ColumnDef } from '@tanstack/react-table';
import React, { useCallback, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import CustomPriceTable from './CustomPriceTable';

interface CopyPriceToCustomerPropsType {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const CopyPriceToCustomer: React.FC<CopyPriceToCustomerPropsType> = ({
  open,
  onOpenChange,
}) => {
  const [search, setSearch] = useState<string>('');
  const [pagination, setPagination] = useState({ pageSize: 10, pageIndex: 0 });
  const [isFilterOpen, setIsFilterOpen] = useState<boolean>(false);

  const filter = useSelector((state: RootState) => state.customer.filters);

  // Memoized table columns
  const columns: ColumnDef<CustomPriceTypes>[] = useMemo(
    () => [
      { accessorKey: 'itemId', header: 'Item ID' },
      { accessorKey: 'category', header: 'Category' },
      { accessorKey: 'description', header: 'Description' },
      { accessorKey: 'quantity', header: 'Quantity' },
      { accessorKey: 'cost', header: 'Cost' },
    ],
    []
  );

  // Handlers
  const handleClearFilter = useCallback(() => {}, []);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="bg-white p-0 flex flex-col gap-0 min-w-[320px] max-w-[1140px]">
        <DialogHeader className="pl-6 pr-6 pb-3 pt-3 bg-background-default-hover">
          <DialogTitle className="text-text-Default font-normal text-xl">
            Copy Price to Customer
          </DialogTitle>
        </DialogHeader>
        <div className="flex flex-col p-6">
          {/* DataTable Component */}
          <CustomPriceTable
            search={search}
            setSearch={setSearch}
            pagination={pagination}
            setPagination={setPagination}
            columns={columns}
            filter={filter}
            isFilterOpen={isFilterOpen}
            setIsFilterOpen={setIsFilterOpen}
            handleClearFilter={handleClearFilter}
          />
        </div>
        <DialogFooter className="flex flex-row justify-between pb-6 pl-6 pr-6">
          <AppButton
            label="Copy Pricing"
            icon={CheckVerified}
            className="w-full"
          />
          <Button
            onClick={() => onOpenChange(false)}
            className="w-full bg-white border-border-Default border-2 text-black"
          >
            Cancel
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default CopyPriceToCustomer;
