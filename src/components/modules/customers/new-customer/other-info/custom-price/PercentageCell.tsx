import { Input } from '@/components/ui/input';

interface PercentageCellProps {
  isEditing: boolean;
}

const PercentageCell = ({ isEditing }: PercentageCellProps) => {
  return (
    <div className="flex items-center justify-between">
      {/* Set a consistent height */}
      {isEditing ? (
        <div className="flex items-center justify-between gap-4 w-full">
          <Input
            className="border-none bg-transparent mb-0 mt-0 h-[30px] w-full"
            placeholder="%"
          />
        </div>
      ) : (
        <div className="flex items-center justify-between w-full">
          <span>---</span>
        </div>
      )}
    </div>
  );
};

export default PercentageCell;
