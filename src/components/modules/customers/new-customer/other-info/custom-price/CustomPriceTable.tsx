import DataTable from '@/components/common/data-tables';
import { customPriceData } from '@/mock-data/customer-mock-data';
import { CustomPriceTypes } from '@/types/customer.types';
import { ColumnDef } from '@tanstack/react-table';

interface CustomerPriceTableProps {
  search: string;
  setSearch: React.Dispatch<React.SetStateAction<string>>;
  pagination: { pageSize: number; pageIndex: number };
  setPagination: React.Dispatch<
    React.SetStateAction<{ pageSize: number; pageIndex: number }>
  >;
  columns: ColumnDef<CustomPriceTypes>[];
  filter: any;
  isFilterOpen: boolean;
  setIsFilterOpen: React.Dispatch<React.SetStateAction<boolean>>;
  handleClearFilter: () => void;
}

const CustomPriceTable: React.FC<CustomerPriceTableProps> = ({
  search,
  setSearch,
  pagination,
  setPagination,
  columns,
  filter,
  isFilterOpen,
  setIsFilterOpen,
  handleClearFilter,
}) => (
  <DataTable
    search={search}
    setSearch={setSearch}
    data={customPriceData}
    pagination={pagination}
    setPagination={setPagination}
    columns={columns}
    enableSearch
    heading={
      <h6 className="text-text-neutral-tertiary text-base font-normal">
        Select a customer to copy the pricing from the current user.
      </h6>
    }
    enableFilter
    enableColumnVisibility={false}
    totalItems={20}
    filter={filter}
    handleClearFilter={handleClearFilter}
    filterClassName="w-[473px]"
    // filterContent={<Filter setFilterOpen={setIsFilterOpen} />}
    isFilterOpen={isFilterOpen}
    setIsFilterOpen={setIsFilterOpen}
    enableRowSelection
  />
);

export default CustomPriceTable;
