import { render, screen } from '@testing-library/react';
import PercentageCell from './PercentageCell';
import { describe, it, expect } from 'vitest';

describe('PercentageCell Component', () => {
  it('renders input field when isEditing is true', () => {
    render(<PercentageCell isEditing={true} />);

    const inputElement = screen.getByPlaceholderText('%');
    expect(inputElement).toBeInTheDocument();

    expect(inputElement).toHaveClass(
      'border-none bg-transparent mb-0 mt-0 h-[30px] w-full'
    );
  });

  it('renders default view when isEditing is false', () => {
    render(<PercentageCell isEditing={false} />);

    const defaultText = screen.getByText('---');
    expect(defaultText).toBeInTheDocument();

    const inputElement = screen.queryByPlaceholderText('%');
    expect(inputElement).not.toBeInTheDocument();
  });
});
