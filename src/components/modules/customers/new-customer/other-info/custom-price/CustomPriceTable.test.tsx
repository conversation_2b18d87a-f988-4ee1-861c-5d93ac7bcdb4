import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import CustomPriceTable from './CustomPriceTable';
import { customPriceData } from '@/mock-data/customer-mock-data';
import { ColumnDef } from '@tanstack/react-table';
import { CustomPriceTypes } from '@/types/customer.types';

describe('CustomPriceTable Component', () => {
  const mockColumns: ColumnDef<CustomPriceTypes>[] = [
    { accessorKey: 'itemId', header: 'Item ID' },
    { accessorKey: 'category', header: 'Category' },
    { accessorKey: 'description', header: 'Description' },
    { accessorKey: 'quantity', header: 'Quantity' },
    { accessorKey: 'cost', header: 'Cost' },
  ];

  const setSearch = vi.fn();
  const setPagination = vi.fn();
  const setIsFilterOpen = vi.fn();
  const handleClearFilter = vi.fn();

  const renderComponent = () =>
    render(
      <CustomPriceTable
        search=""
        setSearch={setSearch}
        pagination={{ pageSize: 10, pageIndex: 0 }}
        setPagination={setPagination}
        columns={mockColumns}
        filter={[]}
        isFilterOpen={false}
        setIsFilterOpen={setIsFilterOpen}
        handleClearFilter={handleClearFilter}
      />
    );

  it('renders the component with the correct heading', () => {
    renderComponent();

    expect(screen.getByText('Item ID')).toBeInTheDocument();
  });

  it('renders the table with correct columns', () => {
    renderComponent();

    mockColumns.forEach((column) => {
      expect(screen.getByText(column.header as string)).toBeInTheDocument();
    });
  });

  it('renders the correct number of rows based on data', () => {
    renderComponent();

    expect(screen.getAllByRole('row').length).toBe(customPriceData.length + 1); // +1 for header row
  });
});
