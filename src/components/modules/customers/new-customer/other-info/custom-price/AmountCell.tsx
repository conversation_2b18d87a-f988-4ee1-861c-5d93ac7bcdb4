import CloseIcon from '@/assets/icons/CloseIcon';
import EditCellIcon from '@/assets/icons/EditCellIcon';
import EditIcon from '@/assets/icons/EditIcon';
import SubmitIcon from '@/assets/icons/SubmitIcon';
import { Input } from '@/components/ui/input';
import { Row } from '@tanstack/react-table';

interface AmountCellProps {
  row: Row<any>;
  isEditing: boolean;
  setIsEditing: React.Dispatch<React.SetStateAction<number | null>>;
}
const AmountCell = ({ row, isEditing, setIsEditing }: AmountCellProps) => {
  const handleEditClick = () => {
    setIsEditing(row.index);
  };

  const handleSaveClick = () => {
    // Add save logic here
    setIsEditing(null);
  };

  const shouldShowEditIcon = row.getIsSelected();

  return (
    <div className="flex items-center justify-between h-8">
      {/* Set a consistent height */}
      {isEditing ? (
        <div className="flex items-center justify-between gap-4 w-full">
          <Input
            className="border-none bg-transparent mb-0 mt-0 h-[30px] w-full" // Keep input height consistent
            placeholder="$"
          />
          <SubmitIcon onClick={handleSaveClick} />
          <CloseIcon />
        </div>
      ) : (
        <div className="flex items-center justify-between w-full">
          <span>---</span>
          {shouldShowEditIcon && (
            <div className="flex flex-row gap-3">
              <div onClick={handleEditClick}>
                <EditIcon />
              </div>
              <EditCellIcon />
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default AmountCell;
