import { render, screen } from '@testing-library/react';
import AmountCell from './AmountCell';
import { describe, it, expect, vi } from 'vitest';
import { Row } from '@tanstack/react-table';

const mockRow = {
  index: 1,
  getIsSelected: () => true,
} as unknown as Row<any>;

describe('AmountCell Component', () => {
  it('render AmountCell component', () => {
    const setIsEditing = vi.fn();
    const isRendered = render(
      <AmountCell row={mockRow} isEditing={false} setIsEditing={setIsEditing} />
    );
    expect(isRendered);
  });

  it('renders correctly in non-editing mode', () => {
    const setIsEditing = vi.fn();
    render(
      <AmountCell row={mockRow} isEditing={false} setIsEditing={setIsEditing} />
    );
    expect(screen.getByText('---')).toBeInTheDocument();
  });
});
