import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import CustomPrice from './index';
import '@testing-library/jest-dom';

// Mock necessary components
vi.mock('@/components/common/data-tables', () => ({
  default: vi.fn(() => <div>DataTable Component</div>),
}));

vi.mock('@/components/common/app-button', () => ({
  default: vi.fn(({ onClick }) => (
    <button onClick={onClick}>Copy Pricing to Customer</button>
  )),
}));

vi.mock('@/assets/icons/CopyIcon', () => ({
  default: 'CopyIcon',
}));

vi.mock('@/components/common/app-button', () => ({
  default: vi.fn(() => <div>AppButton</div>),
}));

vi.mock('./CopyPriceToCustomer', () => ({
  default: vi.fn(({ open }) =>
    open ? <div>Modal Opened</div> : <div>Modal Closed</div>
  ),
}));

describe('CustomPrice Component', () => {
  it('renders the Custom Pricing page with necessary elements', () => {
    render(<CustomPrice />);

    expect(screen.getByText('DataTable Component')).toBeInTheDocument();

    expect(screen.getByPlaceholderText('Value')).toBeInTheDocument();
  });

  it('opens the modal when "Copy Pricing to Customer" is clicked', () => {
    render(<CustomPrice />);

    expect(screen.getByText('Modal Closed')).toBeInTheDocument();
  });

  it('closes the modal when the state changes', () => {
    render(<CustomPrice />);

    const copyButton = screen.getByText('Reset All Pricing');
    fireEvent.click(copyButton);

    expect(screen.getByText('Modal Closed')).toBeInTheDocument();
  });
});
