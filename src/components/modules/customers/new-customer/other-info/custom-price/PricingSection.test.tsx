import { render, screen } from '@testing-library/react';
import PricingSection from './PricingSection';
import { describe, it, expect, vi } from 'vitest';

vi.mock('react-hook-form', () => ({
  useForm: vi.fn().mockReturnValue({
    control: {},
    register: vi.fn(),
    handleSubmit: vi.fn(),
    setValue: vi.fn(),
    getValues: vi.fn(),
    formState: {
      errors: {},
    },
    fields: [],
    append: vi.fn(),
    remove: vi.fn(),
    update: vi.fn(),
  }),
  Controller: vi.fn(({ render }) =>
    render({
      field: {
        value: 'percentage',
        onChange: vi.fn(),
      },
    })
  ),
}));

describe('PricingSection Component', () => {
  const mockForm = {
    control: {},
    register: vi.fn(),
    handleSubmit: vi.fn(),
    setValue: vi.fn(),
    getValues: vi.fn(),
    formState: {
      errors: {},
    },
    fields: [],
    append: vi.fn(),
    remove: vi.fn(),
    update: vi.fn(),
    reset: vi.fn(),
  };

  it('renders all elements correctly', () => {
    render(<PricingSection form={mockForm} />);

    expect(
      screen.getByText(
        'Note: Filter by category or select separate items from table above.'
      )
    ).toBeInTheDocument();
  });

  it('renders correct initial values for the dropdown', () => {
    render(<PricingSection form={mockForm} />);

    const selectDropdown = screen.getByRole('combobox');
    expect(selectDropdown);
  });
});
