import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import CopyPriceToCustomer from './CopyPriceToCustomer';
import { Provider } from 'react-redux';
import { store } from '@/redux/store';

describe('CopyPriceToCustomer Component', () => {
  const onOpenChange = vi.fn();

  const renderComponent = (open: boolean) =>
    render(
      <Provider store={store}>
        <CopyPriceToCustomer open={open} onOpenChange={onOpenChange} />
      </Provider>
    );

  it('renders the component when open is true', () => {
    renderComponent(true);

    expect(screen.getByText('Copy Price to Customer')).toBeInTheDocument();

    expect(screen.getByRole('table')).toBeInTheDocument();

    expect(screen.getByText('Copy Pricing')).toBeInTheDocument();

    expect(screen.getByText('Cancel')).toBeInTheDocument();
  });

  it('does not render the component when open is false', () => {
    renderComponent(false);

    expect(
      screen.queryByText('Copy Price to Customer')
    ).not.toBeInTheDocument();
  });

  it('calls onOpenChange with false when the Cancel button is clicked', () => {
    renderComponent(true);

    const cancelButton = screen.getByText('Cancel');
    fireEvent.click(cancelButton);

    expect(onOpenChange).toHaveBeenCalledWith(false);
  });

  it('renders the CustomPriceTable with correct props', () => {
    renderComponent(true);

    const table = screen.getByRole('table');
    expect(table).toBeInTheDocument();

    expect(screen.getByText('Item ID')).toBeInTheDocument();
    expect(screen.getByText('Category')).toBeInTheDocument();
    expect(screen.getByText('Description')).toBeInTheDocument();
  });
});
