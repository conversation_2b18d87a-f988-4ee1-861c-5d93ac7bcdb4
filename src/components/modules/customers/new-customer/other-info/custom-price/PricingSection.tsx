import AppButton from '@/components/common/app-button';
import SelectDropDown from '@/components/forms/select-dropdown';
import CheckVerified from '@/assets/icons/CheckVerified';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

const PricingSection = ({ form }: { form: any }) => (
  <div className="rounded-lg p-6 gap-6 flex flex-col bg-background-default-hover">
    <div className="flex flex-col gap-3">
      <h3 className="text-2xl font-semibold">Apply Pricing</h3>
      <span className="text-text-neutral-tertiary">
        Note: Filter by category or select separate items from table above.
        <span className="text-danger font-bold text-base">
          "If none selected in above table a flat rate will be applied".
        </span>
      </span>
    </div>

    <div className="flex flex-row gap-3">
      <Input placeholder="Value" />
      <SelectDropDown
        name="percentage"
        className="w-40"
        form={form}
        defaultValue="percentage"
        optionsList={[{ label: 'Percentage', value: 'percentage' }]}
      />
      <AppButton label="Apply Pricing" icon={CheckVerified} />
      <Button className="bg-transparent border-border-Default border-2 text-black rounded-lg">
        Reset All Pricing
      </Button>
    </div>
  </div>
);

export default PricingSection;
