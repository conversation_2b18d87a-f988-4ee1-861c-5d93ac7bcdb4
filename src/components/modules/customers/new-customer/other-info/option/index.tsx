import SwitchField from '@/components/common/switch';
import Input<PERSON>ield from '@/components/forms/input-field';
import NumberInputField from '@/components/forms/number-input-field';
import SelectWidget from '@/components/forms/select';
import { Separator } from '@/components/ui/separator';
import { generateLabelValuePairs } from '@/lib/utils';
import {
  useCreditStatusQuery,
  useFreeShippingQuery,
  usePaymentTermsQuery,
  usePaymentTypeQuery,
  useRentStatusQuery,
  useSalesPersonQuery,
} from '@/redux/features/customers/choices.api';
import { useGetDeliveryTypeQuery } from '@/redux/features/delivery-type/delivery-type.api';
import { useGetSalesTaxCodeQuery } from '@/redux/features/sales-tax-code/sales-tax-code.api';
import { FormData } from '@/types/customer.types';
import { useFormContext } from 'react-hook-form';

const Option = () => {
  const form = useFormContext<FormData>();
  const { data, isLoading } = usePaymentTermsQuery();
  const { data: paymentTypeData, isLoading: paymentTypeLoading } =
    usePaymentTypeQuery();
  const { data: salesTaxCodeData, isLoading: salesTaxCodLoading } =
    useGetSalesTaxCodeQuery();
  const { data: deliveryTypeData, isLoading: deliveryTyLoading } =
    useGetDeliveryTypeQuery();
  const { data: salespersonData, isLoading: salespersonLoading } =
    useSalesPersonQuery();
  const { data: rentstatusData, isLoading: rentstatusLoading } =
    useRentStatusQuery();
  const { data: freeshippingData, isLoading: freeshippingLoading } =
    useFreeShippingQuery();
  const { data: creditStatusData, isLoading: creditStatusLoading } =
    useCreditStatusQuery();

  const paymentTermList = data?.data?.map((item: any) => {
    const key = Object.keys(item)[0];
    const value = item[key];
    return {
      label: key,
      value: value,
    };
  });

  const paymentTypeList = paymentTypeData?.data?.map((item: any) => {
    const key = Object.keys(item)[0];
    const value = item[key];
    return {
      label: key,
      value: value,
    };
  });

  const salesTaxCodeList = generateLabelValuePairs({
    data: salesTaxCodeData?.data,
    labelKey: 'salestaxcode',
    valueKey: 'salestaxcode_id',
  });

  const deliveryTypeList = generateLabelValuePairs({
    data: deliveryTypeData?.data,
    labelKey: 'name',
    valueKey: 'deliverytype_id',
  });

  const salesPersonList = generateLabelValuePairs({
    data: salespersonData?.data,
    labelKey: 'name',
    valueKey: 'id',
  });

  const rentStatusList = rentstatusData?.data?.map((item: any) => {
    const key = Object.keys(item)[0];
    const value = item[key];
    return {
      label: key,
      value: value?.toString(),
    };
  });

  const freeShippingList = freeshippingData?.data?.map((item: any) => {
    const key = Object.keys(item)[0];
    const value = item[key];
    return {
      label: key,
      value: value,
    };
  });

  const creditStatusList = creditStatusData?.data?.map((item: any) => {
    const key = Object.keys(item)[0];
    const value = item[key];
    return {
      label: key,
      value: value,
    };
  });

  return (
    <div className="flex gap-6">
      <div className="bg-background-default-hover p-8 flex flex-col gap-8 rounded-lg w-full">
        <div className="flex flex-col gap-3">
          <h3 className="text-xl text-text-Default">Delivery Details</h3>
          <Separator
            orientation="horizontal"
            className=" h-[1px] bg-border-Default"
          />
        </div>
        <div className="grid grid-cols-2 gap-4">
          <div className="col-span-2">
            <SelectWidget
              form={form}
              optionsList={salesPersonList}
              name="salesPersonId"
              label="Salesperson"
              placeholder="Select Sales Person"
              isLoading={salespersonLoading}
              isClearable={true}
            />
          </div>
          <div>
            <SelectWidget
              form={form}
              name="deliverytype_id"
              label="Default Delivery Type"
              optionsList={deliveryTypeList}
              isLoading={deliveryTyLoading}
              placeholder="Select Default Delivery Type"
              isClearable={true}
            />
          </div>

          <div>
            <NumberInputField
              form={form}
              name="defdelchg"
              label="Default Delivery Charge"
              placeholder="$____.__"
              maxLength={10}
              prefix="$"
              fixedDecimalScale
            />
          </div>

          <div className="col-span-2">
            <InputField
              form={form}
              type="number"
              maxLength={15}
              name="upsacctnum"
              label="UPS Account #"
              placeholder="Enter UPS Account"
            />
          </div>
          <div className="col-span-2">
            <SelectWidget
              form={form}
              optionsList={freeShippingList ?? []}
              name="free_shipping"
              label="Free Shipping"
              isLoading={freeshippingLoading}
              placeholder="Select Free Shipping"
              isClearable={true}
            />
          </div>
        </div>

        <div className="flex gap-4 p-2 rounded-lg border-[1px] border-border-Default">
          <div className="flex flex-col gap-8 w-full">
            <SwitchField label="Corporate" form={form} name="corporate" />
            <SwitchField
              label="Price on Invoice"
              form={form}
              name="pricingoninvoice"
            />
            <SwitchField label="Drop Ship" form={form} name="dropship" />
          </div>
          <Separator
            orientation="vertical"
            className=" w-[1px] bg-border-Default"
          />

          <div className="flex flex-col gap-8 w-full">
            <SwitchField
              label="Charge Convinience Fee"
              form={form}
              name="defconvfeeflag"
            />
            <SwitchField
              label="Display Alert on order"
              form={form}
              name="displayalert"
            />
            <SwitchField
              label="Online Payments"
              form={form}
              name="onlinepmts"
            />
          </div>
        </div>
      </div>
      <div className="bg-background-default-hover p-8 flex flex-col gap-8 rounded-lg w-full">
        <div className="flex flex-col gap-3">
          <h3 className="text-xl text-text-Default">Payment Details</h3>
          <Separator
            orientation="horizontal"
            className=" h-[1px] bg-border-Default"
          />
        </div>
        <div className="grid grid-cols-2 gap-4">
          <div className="col-span-2">
            <SelectWidget
              form={form}
              optionsList={salesTaxCodeList}
              name="salestaxcode_id"
              label="Default Sales Tax Code"
              isLoading={salesTaxCodLoading}
              placeholder="Select Default Sales Tax Code"
              isClearable={false}
            />
          </div>
          <div className="col-span-2">
            <SelectWidget
              form={form}
              optionsList={paymentTypeList ?? []}
              name="paymenttype_id"
              label="Default Payment Type"
              isLoading={paymentTypeLoading}
              placeholder="Select Default Payment Type"
              isClearable={true}
            />
          </div>
          <SelectWidget
            form={form}
            optionsList={creditStatusList ?? []}
            name="creditstatus"
            label="Credit Status"
            isLoading={creditStatusLoading}
            placeholder="Select Credit Status"
            isClearable={true}
          />
          <NumberInputField
            form={form}
            decimalScale={0}
            name="creditlimit"
            label="Credit Limit"
            placeholder="Enter Credit Limit"
            maxLength={6}
          />

          <div className="col-span-2">
            <InputField
              maxLength={128}
              form={form}
              name="creditmessage"
              label="Credit Message"
              placeholder="Enter Credit Message"
            />
          </div>
          <div className="col-span-2">
            <SelectWidget
              form={form}
              optionsList={rentStatusList ?? []}
              name="rentstatus"
              isClearable={true}
              label="Rent Status"
              isLoading={rentstatusLoading}
              placeholder="Select Rent Status"
            />
          </div>
          <div className="col-span-2">
            <SelectWidget
              form={form}
              optionsList={paymentTermList ?? []}
              isClearable={true}
              name="paymentterm_id"
              label="Payment Terms"
              isLoading={isLoading}
              placeholder="Select Payment Terms"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Option;
