import { render, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { useFormContext } from 'react-hook-form';
import Option from '.';
import {
  usePaymentTermsQuery,
  usePaymentTypeQuery,
  useSalesPersonQuery,
  useRentStatusQuery,
  useFreeShippingQuery,
  useCreditStatusQuery,
} from '@/redux/features/customers/choices.api';
import { useGetSalesTaxCodeQuery } from '@/redux/features/sales-tax-code/sales-tax-code.api';
import { useGetDeliveryTypeQuery } from '@/redux/features/delivery-type/delivery-type.api';

vi.mock('@/redux/features/customers/choices.api', () => ({
  usePaymentTermsQuery: vi.fn(),
  usePaymentTypeQuery: vi.fn(),
  useSalesTaxCodeQuery: vi.fn(),
  useSalesPersonQuery: vi.fn(),
  useRentStatusQuery: vi.fn(),
  useFreeShippingQuery: vi.fn(),
  useCreditStatusQuery: vi.fn(),
}));

vi.mock('@/redux/features/delivery-type/delivery-type.api', () => ({
  useGetDeliveryTypeQuery: vi.fn(),
}));

vi.mock('@/redux/features/sales-tax-code/sales-tax-code.api', () => ({
  useGetSalesTaxCodeQuery: vi.fn(),
}));

vi.mock('react-hook-form', () => ({
  useFormContext: vi.fn(() => ({
    control: {},
    register: vi.fn(),
    handleSubmit: vi.fn(),
    setValue: vi.fn(),
    watch: vi.fn(() => ''),
    formState: {
      errors: {},
    },
  })),
  Controller: vi.fn(({ render }) =>
    render({
      field: {
        onChange: vi.fn(),
        value: '',
      },
    })
  ),
}));

describe('Option Component', () => {
  beforeEach(() => {
    (useFormContext as any).mockReturnValue({
      control: {},
      register: vi.fn(),
      handleSubmit: vi.fn(),
      setValue: vi.fn(),
      watch: vi.fn(() => ''),
      formState: {
        errors: {},
      },
    });

    (usePaymentTermsQuery as any).mockReturnValue({
      data: { data: [{ term: 'Net 30' }] },
      isLoading: false,
    });
    (usePaymentTypeQuery as any).mockReturnValue({
      data: { data: [{ type: 'Credit Card' }] },
      isLoading: false,
    });
    (useGetSalesTaxCodeQuery as any).mockReturnValue({
      data: { data: [{ salestaxcode: 'STC123' }] },
      isLoading: false,
    });
    (useGetDeliveryTypeQuery as any).mockReturnValue({
      data: { data: [{ name: 'Standard Shipping' }] },
      isLoading: false,
    });
    (useSalesPersonQuery as any).mockReturnValue({
      data: { data: [{ name: 'John Doe' }] },
      isLoading: false,
    });
    (useRentStatusQuery as any).mockReturnValue({
      data: { data: [{ status: 'Active' }] },
      isLoading: false,
    });
    (useFreeShippingQuery as any).mockReturnValue({
      data: { data: [{ free_shipping: 'Yes' }] },
      isLoading: false,
    });
    (useCreditStatusQuery as any).mockReturnValue({
      data: { data: [{ creditstatus: 'Good' }] },
      isLoading: false,
    });
  });

  it('should render the delivery details and payment details sections', async () => {
    render(<Option />);

    expect(screen.getByText('Delivery Details')).toBeInTheDocument();
    expect(screen.getByText('Payment Details')).toBeInTheDocument();
  });

  it('should render form fields for select and input components', async () => {
    render(<Option />);

    expect(screen.getByText('Salesperson')).toBeInTheDocument();

    expect(screen.getByText('UPS Account #')).toBeInTheDocument();

    expect(screen.getByText('Default Delivery Type')).toBeInTheDocument();

    expect(screen.getByText('Corporate')).toBeInTheDocument();
  });

  it('should call form setValue when select option is chosen', async () => {
    render(<Option />);

    const salespersonSelect = screen.getByText('Salesperson');
    expect(salespersonSelect.onclick);
  });
});
