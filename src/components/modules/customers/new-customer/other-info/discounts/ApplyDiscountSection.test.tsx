import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import ApplyDiscountSection from './ApplyDiscountSection';
import '@testing-library/jest-dom';

vi.mock('@/components/common/app-button', () => ({
  default: vi.fn(({ label, onClick, disabled, isLoading }) => (
    <button onClick={onClick} disabled={disabled}>
      {isLoading ? 'Loading...' : label}
    </button>
  )),
}));

vi.mock('@/assets/icons/CheckVerified', () => ({
  default: 'CheckVerified',
}));

describe('ApplyDiscountSection Component', () => {
  it('renders Apply Discount section with initial props', () => {
    render(
      <ApplyDiscountSection
        bulkDiscount="10.00%"
        setBulkDiscount={vi.fn()}
        onApplyDiscount={vi.fn()}
        isLoading={false}
        label="Apply"
      />
    );

    expect(screen.getByText('Apply Discount')).toBeInTheDocument();
    expect(screen.getByText('Apply')).toBeInTheDocument();

    const inputField = screen.getByPlaceholderText('__.__%');
    expect(inputField).toBeInTheDocument();
    expect(inputField).toHaveValue('10.00%');
  });

  it('calls onApplyDiscount when the button is clicked', async () => {
    const onApplyDiscount = vi.fn();
    const setBulkDiscount = vi.fn();

    render(
      <ApplyDiscountSection
        bulkDiscount="10.00%"
        setBulkDiscount={setBulkDiscount}
        onApplyDiscount={onApplyDiscount}
        isLoading={false}
        label="Apply"
      />
    );

    const applyButton = screen.getByText('Apply');
    fireEvent.click(applyButton);

    expect(onApplyDiscount).toHaveBeenCalled();
  });

  it('disables the apply button when input is empty or has default value "__.__%"', () => {
    const setBulkDiscount = vi.fn();
    const onApplyDiscount = vi.fn();

    render(
      <ApplyDiscountSection
        bulkDiscount=""
        setBulkDiscount={setBulkDiscount}
        onApplyDiscount={onApplyDiscount}
        isLoading={false}
        label="Apply"
      />
    );

    const applyButton = screen.getByText('Apply');
    expect(applyButton).toBeDisabled();

    render(
      <ApplyDiscountSection
        bulkDiscount="__.__%"
        setBulkDiscount={setBulkDiscount}
        onApplyDiscount={onApplyDiscount}
        isLoading={false}
        label="Apply"
      />
    );

    expect(applyButton).toBeDisabled();
  });

  it('enables the apply button when a valid discount is provided', () => {
    const setBulkDiscount = vi.fn();
    const onApplyDiscount = vi.fn();

    render(
      <ApplyDiscountSection
        bulkDiscount="10.00%"
        setBulkDiscount={setBulkDiscount}
        onApplyDiscount={onApplyDiscount}
        isLoading={false}
        label="Apply"
      />
    );

    const applyButton = screen.getByText('Apply');
    expect(applyButton).toBeEnabled();
  });
});
