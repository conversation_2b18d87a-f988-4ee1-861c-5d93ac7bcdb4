import DataTable from '@/components/common/data-tables';
import { searchPayload } from '@/lib/utils';
import {
  useGetAllDiscountMutation,
  useUpdateBulkDiscountMutation,
  useUpdateDiscountMutation,
} from '@/redux/features/customers/discount.api';
import { DiscountsTypes } from '@/types/customer.types';
import { ColumnDef, RowSelectionState } from '@tanstack/react-table';
import { useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { useParams } from 'react-router-dom';
import ApplyDiscountSection from './ApplyDiscountSection';
import DiscountCell from './DiscountCell';

import CheckVerified from '@/assets/icons/CheckVerified';
import AppButton from '@/components/common/app-button';
import AppTableContextProvider, {
  AppTableContext,
} from '@/components/common/app-data-table/AppTableContext';
import CustomDialog from '@/components/common/dialog';
import { UseToast } from '@/components/ui/toast/ToastContainer';
import { useGetDepartmentsQuery } from '@/redux/features/customers/customer.api';
import { NumericFormat } from 'react-number-format';

/**
 * Discounts Component
 * Handles displaying and updating discount data for specific customer categories and departments.
 */
const Discounts = () => {
  // Extract customer ID from URL params
  const { customerId } = useParams<{ customerId?: string }>();
  const { data: departmentData, isFetching } = useGetDepartmentsQuery();
  const [updatedDiscounts, setUpdatedDiscounts] = useState<{
    [key: string]: string;
  }>({});
  // State hooks for managing component state
  const [selectedRows, setSelectedRows] = useState<RowSelectionState>({});
  const [selectedRowsData, setSelectedRowsData] = useState<DiscountsTypes[]>(
    []
  );
  const [accumulatedDiscounts, setAccumulatedDiscounts] = useState<
    DiscountsTypes[]
  >([]);
  const [discountData, setDiscountData] = useState<DiscountsTypes[]>([]);
  const [bulkDiscount, setBulkDiscount] = useState('');
  const [search, setSearch] = useState('');
  const { pagination, setPagination } = useContext(AppTableContext);
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [isCustomDialogOpen, setIsCustomDialogOpen] = useState<boolean>(false);
  const isDiscount = Object.values(updatedDiscounts)?.filter((value) => value);
  // API hooks for fetching and updating discount data
  const [getDiscount, { isLoading, data }] = useGetAllDiscountMutation();
  const [updateBulkDiscount, { isLoading: isBulkDiscountLoading }] =
    useUpdateBulkDiscountMutation();
  const [updateDiscount, { isLoading: isUpdating }] =
    useUpdateDiscountMutation();

  // Memoized table columns for performance
  const columns: ColumnDef<DiscountsTypes>[] = useMemo(
    () => [
      { accessorKey: 'catno', header: 'Category #' },
      { accessorKey: 'deptdesc', header: 'Department' },
      { accessorKey: 'catdesc', header: 'Category' },
      {
        accessorKey: 'catdiscount',
        header: 'Discount',
        cell: ({ row }: any) => (
          <DiscountCell
            row={row}
            setDiscountData={setDiscountData}
            discountData={discountData}
          />
        ),
        maxSize: 100,
        size: 100,
      },
    ],
    [discountData]
  );

  // Function to handle changes in the discount for each department
  const handleDepartmentDiscountChange = useCallback(
    (departmentId: string, newDiscount: string) => {
      setUpdatedDiscounts((prevState) => ({
        ...prevState,
        [departmentId]: newDiscount, // Store the updated discount for this department
      }));
    },
    []
  );

  const DepartmentDiscountCell = ({
    row,
    onDiscountChange,
  }: {
    row: any;
    onDiscountChange: (departmentId: string, discount: string) => void;
  }) => {
    const [discount, setDiscount] = useState({
      departmentId: row.original?.id,
      custdiscount: '',
    });

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const newDiscount = e.target.value;
      setDiscount({
        ...discount,
        custdiscount: newDiscount,
      });

      // Call the prop function to update parent state
      onDiscountChange(row.original?.id, newDiscount);
    };

    return (
      <NumericFormat
        value={discount.custdiscount}
        placeholder="__.__%"
        decimalScale={2}
        fixedDecimalScale
        isAllowed={({ formattedValue }) => formattedValue.length <= 6}
        suffix={'%'}
        className="flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground  focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
        onChange={handleInputChange}
      />
    );
  };

  const discountByDepartmentColumns = useMemo(
    () => [
      { accessorKey: 'dept', header: 'Department' },
      { accessorKey: 'deptDesc', header: 'Description' },
      {
        accessorKey: 'catdiscount',
        header: 'Discount',
        cell: ({ row }: any) => (
          <DepartmentDiscountCell
            row={row}
            onDiscountChange={handleDepartmentDiscountChange}
          />
        ),
        maxSize: 100,
        size: 100,
      },
    ],
    [handleDepartmentDiscountChange]
  );

  // Fetch discount data based on filter, search, and pagination
  useEffect(() => {
    // Define an async function inside the useEffect hook
    const fetchDiscountData = async () => {
      try {
        // Construct the payload for the API request
        const payload = searchPayload({
          pageNumber: pagination.pageIndex + 1,
          pageSize: pagination.pageSize,
          sortBy: '',
          sortAscending: true,
          filters: [{ field: '', value: '', operator: '' }],
        });

        // Fetch discount data for the specific customer
        const response = await getDiscount({
          id: customerId ?? '',
          body: payload,
        }).unwrap();

        // Set the discount data in state
        setDiscountData(response?.data);
      } catch (error) {
        // Handle any errors
      }
    };

    // Call the async function inside useEffect
    fetchDiscountData();

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [search, pagination]); // Ensure this effect runs when 'search' or 'pagination' change

  const refetchDiscounts = async () => {
    const payload = searchPayload({
      pageNumber: pagination.pageIndex + 1,
      pageSize: pagination.pageSize,
      sortBy: '',
      sortAscending: true,
      filters: [{ field: '', value: '', operator: '' }],
    });

    // Refetch the discount data
    const response = await getDiscount({
      id: customerId ?? '',
      body: payload,
    }).unwrap();
    setDiscountData(response?.data);
  };

  /**
   * Handles applying the bulk discount to the selected rows or all categories.
   */
  const handleApplyDiscount = async () => {
    if (!bulkDiscount) return;

    try {
      // If specific rows are selected, update discounts for those
      if (selectedRowsData.length > 0) {
        const discountData = selectedRowsData.map((row) => ({
          customer_id: Number(customerId),
          custdiscount_id: row.custdiscount_id,
          category_id: row.category_id,
          catdiscount: Number(bulkDiscount),
        }));

        const response = await updateDiscount(discountData).unwrap();
        refetchDiscounts();
        setBulkDiscount('');
        showSuccessMessage(response?.message);
      } else {
        // If no specific rows selected, apply discount in bulk
        const response = await updateBulkDiscount([
          {
            customer_id: Number(customerId),
            department_id: 0,
            catdiscount: Number(bulkDiscount),
          },
        ]).unwrap();
        setBulkDiscount('');
        refetchDiscounts();

        showSuccessMessage(response?.message);
      }

      // Fetch updated discount data after applying the discount
    } catch (error) {
      // Handle error (could be improved with specific error messages)
    }
  };

  /**
   * Shows a success toast message.
   */
  const showSuccessMessage = (message: string) => {
    UseToast().success(message);
  };

  // Toggle Custom Dialog
  const toggleCustomDialog = useCallback(() => {
    setIsCustomDialogOpen((prevState) => !prevState);
  }, []);

  const handleApplyDiscountByDept = async () => {
    // Collect updated discount data from the rows
    const discountPayload = departmentData?.data
      ?.map((row: any) => {
        // Ensure updatedDiscounts[row.department_id] is defined before using 'replace'
        let updatedValue = updatedDiscounts[row?.id];

        if (!updatedValue) {
          return null; // Skip if no discount is provided for this department
        }

        updatedValue = updatedValue.replace(/%/g, '').replace(/_/g, '0');

        // If the value is in the format '__.__%', set it to empty
        if (updatedValue === '__.__') {
          updatedValue = ''; // Clear the field
        }

        // If the updatedValue is empty or invalid, skip this department
        if (!updatedValue || updatedValue === '') {
          return null;
        }

        return {
          department_id: row?.id,
          customer_id: Number(customerId),
          catdiscount: Number(updatedValue || 0),
        };
      })
      .filter((item: any) => item !== null); // Filter out invalid (null) values

    const response = await updateBulkDiscount(discountPayload);
    showSuccessMessage(response?.data?.message ?? '');
    toggleCustomDialog();
    refetchDiscounts();
  };

  const CustomToolbar = (
    <AppButton
      label="Discount By Department"
      onClick={() => toggleCustomDialog()}
      icon={CheckVerified}
    />
  );

  // Update accumulated discounts when new data is fetched
  useEffect(() => {
    if (data?.data) {
      setAccumulatedDiscounts((prev) => {
        // Merge new page data with existing data
        const merged = [...prev];
        data?.data.forEach((row) => {
          if (!merged.some((item) => item.category_id === row.category_id)) {
            merged.push(row);
          }
        });
        return merged;
      });
    }
  }, [data?.data]);

  // Update selected rows data when selected rows change
  useEffect(() => {
    const selectedData = accumulatedDiscounts.filter(
      (row) => row.category_id !== undefined && selectedRows[row.category_id]
    );
    setSelectedRowsData(selectedData);
  }, [selectedRows, accumulatedDiscounts]);

  return (
    <AppTableContextProvider>
      <div className="flex flex-col gap-8">
        {/* Data Table */}
        <DataTable
          search={search}
          setSearch={setSearch}
          data={discountData ?? []}
          columns={columns}
          enableSearch={false}
          heading={
            <h1 className="text-2xl font-semibold text-[#181A1D]">
              Department & Categories
            </h1>
          }
          isLoading={isLoading}
          totalItems={data?.pagination?.totalCount}
          pagination={pagination}
          setPagination={setPagination}
          enableColumnVisibility={false}
          enableRowSelection
          customToolBar={CustomToolbar}
          enableFilter={false}
          rowSelection={selectedRows}
          onRowSelectionChange={setSelectedRows}
          setIsFilterOpen={setIsFilterOpen}
          enableMultiRowSelection
          bindingKey="category_id"
        />

        {/* Apply Discount Section */}
        <ApplyDiscountSection
          bulkDiscount={bulkDiscount}
          setBulkDiscount={setBulkDiscount}
          onApplyDiscount={handleApplyDiscount}
          disabled={isUpdating || data?.data?.length === 0}
          isLoading={isBulkDiscountLoading || isUpdating}
          label={
            selectedRowsData.length > 0 ? 'Apply Discount' : 'Apply Flat Rate'
          }
        />

        <CustomDialog
          onOpenChange={() => {
            setUpdatedDiscounts({});
            toggleCustomDialog();
          }}
          description=""
          open={isCustomDialogOpen}
          className="lg:min-w-[60%]"
          title={'Discount By Department'}
        >
          <div className="pl-6 pr-6 p-2">
            <DataTable
              tableClassName="max-h-[400px] overflow-auto"
              data={departmentData?.data ?? []}
              columns={discountByDepartmentColumns}
              enableSearch={false}
              isLoading={isFetching}
              enableColumnVisibility={false}
              enableRowSelection={false}
              isFilterOpen={isFilterOpen}
              enableFilter={false}
              enablePagination={false}
              loaderRows={5}
              totalItems={departmentData?.pagination?.totalCount}
              setIsFilterOpen={setIsFilterOpen}
            />
            <div className="mt-4  flex flex-row justify-end gap-4">
              <div className="w-[50%] flex flex-row justify-end gap-4">
                <AppButton
                  label="Apply Discount"
                  className="w-full"
                  onClick={handleApplyDiscountByDept}
                  icon={CheckVerified}
                  isLoading={isBulkDiscountLoading}
                  disabled={!isDiscount.length}
                />
                <AppButton
                  className="w-full"
                  label="Cancel"
                  variant="neutral"
                  onClick={() => {
                    toggleCustomDialog();
                    setUpdatedDiscounts({});
                  }}
                />
              </div>
            </div>
          </div>
        </CustomDialog>
      </div>
    </AppTableContextProvider>
  );
};

export default Discounts;
