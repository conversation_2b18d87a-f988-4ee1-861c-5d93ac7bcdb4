import { render, fireEvent, screen } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import Discounts from './index';
import { UseToast } from '@/components/ui/toast/ToastContainer';
import { MemoryRouter } from 'react-router-dom';
import { Provider } from 'react-redux';
import { store } from '@/redux/store';
import {
  useGetAllDiscountMutation,
  useUpdateBulkDiscountMutation,
  useUpdateDiscountMutation,
} from '@/redux/features/customers/discount.api';

// Mocking necessary hooks and context
vi.mock('@/redux/features/customers/discount.api', () => {
  const actual = vi.importActual('@/redux/features/customers/discount.api');
  return {
    ...actual,
    useGetAllDiscountMutation: vi.fn(),
    useUpdateBulkDiscountMutation: vi.fn(),
    useUpdateDiscountMutation: vi.fn(),
    discountApi: {
      reducerPath: 'discountApi',
      reducer: (state = { data: [] }) => state,
      middleware: () => (next: any) => (action: any) => next(action),
    },
  };
});

vi.mock('@/components/ui/toast/ToastContainer', () => ({
  UseToast: vi.fn(),
}));

const mockToast = vi.fn();

describe('Discounts Component', () => {
  const mockData = {
    data: [
      {
        category_id: 1,
        custdiscount_id: '1',
        catno: '001',
        deptdesc: 'Dept A',
        catdesc: 'Category 1',
        catdiscount: 10,
      },
      {
        category_id: 2,
        custdiscount_id: '2',
        catno: '002',
        deptdesc: 'Dept B',
        catdesc: 'Category 2',
        catdiscount: 15,
      },
    ],
    pagination: { totalCount: 2 },
  };

  const renderComponent = () => {
    return render(
      <MemoryRouter>
        <Provider store={store}>
          <Discounts />
        </Provider>
      </MemoryRouter>
    );
  };

  beforeEach(() => {
    vi.clearAllMocks();
    (UseToast as any).mockReturnValue(mockToast);
    // Mock API hooks return data
    (useGetAllDiscountMutation as any).mockReturnValue([
      vi.fn().mockResolvedValue(mockData),
      { isLoading: false },
    ]);
    (useUpdateBulkDiscountMutation as any).mockReturnValue([
      vi.fn().mockResolvedValue({ message: 'Bulk Discount Applied' }),
      { isLoading: false },
    ]);
    (useUpdateDiscountMutation as any).mockReturnValue([
      vi.fn().mockResolvedValue({ message: 'Discount Applied' }),
      { isLoading: false },
    ]);
  });

  it('should render Discounts component correctly', () => {
    renderComponent();
    expect(screen.getByText('Department & Categories')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('__.__%')).toBeInTheDocument();
  });

  it('should apply bulk discount correctly', async () => {
    renderComponent();

    const bulkDiscountInput = await screen.findByPlaceholderText('__.__%');
    fireEvent.change(bulkDiscountInput, { target: { value: '20.00%' } });

    const applyDiscountButton = await screen.findByText('Apply Flat Rate');
    fireEvent.click(applyDiscountButton);
  });
});
