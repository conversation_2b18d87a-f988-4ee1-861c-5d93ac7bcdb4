import CloseIcon from '@/assets/icons/CloseIcon';
import EditIcon from '@/assets/icons/EditIcon';
import SubmitIcon from '@/assets/icons/SubmitIcon';
import AppSpinner from '@/components/common/app-spinner';
import { Button } from '@/components/ui/button';
import { UseToast } from '@/components/ui/toast/ToastContainer';
import { convertToFloat } from '@/lib/utils';
import { useUpdateDiscountMutation } from '@/redux/features/customers/discount.api';
import { useState } from 'react';
import { NumericFormat } from 'react-number-format';
import { useParams } from 'react-router-dom';

interface DiscountCellProps {
  row: any;
  setDiscountData: any;
  discountData: any;
}

const DiscountCell = ({
  row,
  setDiscountData,
  discountData,
}: DiscountCellProps) => {
  const { customerId } = useParams<{ customerId?: string }>();

  const [isEditing, setIsEditing] = useState(false);
  const [discount, setDiscount] = useState<string>(
    row.original.catdiscount ? `${row.original.catdiscount}` : ''
  );
  const [, setIsFocused] = useState(false);
  const [updateDiscount, { isLoading }] = useUpdateDiscountMutation();

  // Sanitizing the discount input to remove special characters
  const sanitizeDiscount = (input: string) => {
    return input.replace(/%/g, '').replace(/_/g, '0');
  };

  // Toggle edit mode
  const handleEditClick = () => setIsEditing(true);

  // Save the discount and reset edit mode
  const handleSaveClick = async () => {
    const sanitizedDiscount = sanitizeDiscount(discount);

    const response = await updateDiscount([
      {
        custdiscount_id: row.original?.custdiscount_id ?? 0,
        customer_id: Number(customerId),
        category_id: row?.original?.category_id,
        catdiscount: Number(sanitizedDiscount),
      },
    ]);

    const selectedCategoryIds = new Set(
      response?.data?.data?.map((selected: any) => selected.category_id)
    );

    const updatedDiscountData = discountData.map((element: any) => {
      const isSelected = selectedCategoryIds.has(element.category_id);

      return {
        ...element,
        custdiscount_id: isSelected
          ? (response?.data?.data?.find(
              (selected: any) => selected.category_id === element.category_id
            )?.custdiscount_id ?? null)
          : element.custdiscount_id,
        catdiscount: isSelected
          ? (response?.data?.data?.find(
              (selected: any) => selected.category_id === element.category_id
            )?.catdiscount ?? null)
          : element.catdiscount,
      };
    });

    setDiscount(sanitizedDiscount);
    setDiscountData(updatedDiscountData);
    UseToast().success(response?.data?.message ?? '');
    setIsEditing(false);
  };

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setDiscount(e.target.value);
  };

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = () => {
    setIsFocused(false);
  };

  // Cancel editing and reset the discount value
  const handleCancelClick = () => {
    setDiscount(`${row.original.catdiscount}`);
    setIsEditing(false);
  };

  // Check if the row is selected
  const shouldShowEditIcon = row.getIsSelected();
  return (
    <div className="flex items-center justify-between h-8">
      {/* Conditionally render based on the editing state */}
      {isEditing ? (
        <div className="flex items-center justify-between gap-4 w-full">
          <NumericFormat
            value={discount}
            placeholder="__.__%"
            decimalScale={2}
            fixedDecimalScale
            isAllowed={({ formattedValue }) => formattedValue.length <= 6}
            suffix={'%'}
            className="flex h-8 w-[100%] rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground  focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
            onFocus={handleFocus}
            onBlur={handleBlur}
            onChange={handleInputChange}
          />

          <Button
            variant="ghost"
            className="p-0"
            onClick={handleSaveClick}
            disabled={!discount || isLoading}
          >
            {isLoading ? <AppSpinner className="w-4 h-4" /> : <SubmitIcon />}
          </Button>
          <Button variant="ghost" className="p-0" onClick={handleCancelClick}>
            <CloseIcon />
          </Button>
        </div>
      ) : (
        <div className="flex items-center justify-between w-full">
          {/* Display the discount or a placeholder */}
          <span>
            {Number(discount) > 0
              ? convertToFloat({ value: discount, postfix: '%' })
              : '---'}
          </span>
          {/* Show edit icon only when row is selected */}
          {shouldShowEditIcon && (
            <div className="flex gap-3">
              <div onClick={handleEditClick}>
                <EditIcon />
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default DiscountCell;
