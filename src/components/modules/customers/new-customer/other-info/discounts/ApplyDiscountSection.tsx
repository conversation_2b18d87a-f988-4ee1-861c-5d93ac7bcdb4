import CheckVerified from '@/assets/icons/CheckVerified';
import AppButton from '@/components/common/app-button';
import { NumericFormat } from 'react-number-format';

interface ApplyDiscountSectionProps {
  bulkDiscount: string;
  setBulkDiscount: React.Dispatch<React.SetStateAction<string>>;
  onApplyDiscount: () => void;
  disabled?: boolean;
  isLoading: boolean;
  label: string;
}

const ApplyDiscountSection: React.FC<ApplyDiscountSectionProps> = ({
  bulkDiscount,
  setBulkDiscount,
  onApplyDiscount,
  disabled,
  isLoading,
  label,
}) => {
  const onBlur = () => {
    // Replace underscores with 0 and update the field value
    let updatedValue = bulkDiscount?.replace(/%/g, '').replace(/_/g, '0');

    if (bulkDiscount === '__.__%') {
      updatedValue = ''; // Clear the field
    }
    setBulkDiscount(updatedValue ?? '');
  };

  return (
    <div className="rounded-lg p-6 gap-6 flex flex-col bg-background-default-hover">
      <div className="flex flex-col gap-3">
        <h3 className="text-2xl font-semibold">Apply Discount</h3>
        <span className="text-text-neutral-tertiary">
          Note: Filter by departments or select separate items from table above.
          <span className="text-danger font-bold text-base">
            "If none selected in above table a flat rate will be applied".
          </span>
        </span>
      </div>
      <div className="flex flex-row gap-3">
        <NumericFormat
          value={bulkDiscount}
          placeholder="__.__%"
          decimalScale={2}
          fixedDecimalScale
          isAllowed={({ formattedValue }) => formattedValue.length <= 6}
          suffix={'%'}
          className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground  focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
          onBlur={() => onBlur()}
          maxLength={100}
          onChange={(e) => setBulkDiscount(e.target.value)}
        />
        <AppButton
          label={label}
          onClick={onApplyDiscount}
          icon={CheckVerified}
          isLoading={isLoading}
          disabled={
            !bulkDiscount.length || disabled || bulkDiscount === '__.__%'
          }
        />
      </div>
    </div>
  );
};

export default ApplyDiscountSection;
