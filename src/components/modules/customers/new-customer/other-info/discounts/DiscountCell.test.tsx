import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import DiscountCell from './DiscountCell';
import { vi, describe, beforeEach, it, expect } from 'vitest';
import { useParams } from 'react-router-dom';
import { useUpdateDiscountMutation } from '@/redux/features/customers/discount.api';

// Mock dependencies
vi.mock('react-router-dom', () => ({
  useParams: vi.fn(),
}));

vi.mock('@/redux/features/customers/discount.api', () => ({
  useUpdateDiscountMutation: vi.fn(),
}));

vi.mock('@/components/ui/toast/ToastContainer', () => ({
  UseToast: () => ({
    success: vi.fn(),
  }),
}));

vi.mock('@/components/common/app-spinner', () => ({
  default: () => <div data-testid="spinner" />,
}));

vi.mock('@/assets/icons/EditIcon', () => ({
  default: () => <div data-testid="edit-icon" />,
}));

vi.mock('@/assets/icons/SubmitIcon', () => ({
  default: () => <div data-testid="submit-icon" />,
}));

vi.mock('@/assets/icons/CloseIcon', () => ({
  default: () => <div data-testid="close-icon" />,
}));

describe('DiscountCell', () => {
  const mockSetDiscountData = vi.fn();
  // const mockUseUpdateDiscountMutation = vi.fn();
  const mockUpdateDiscount = vi.fn();

  beforeEach(() => {
    vi.resetAllMocks();
    (useParams as any).mockReturnValue({ customerId: '123' });
    (useUpdateDiscountMutation as any).mockReturnValue([
      mockUpdateDiscount,
      { isLoading: false },
    ]);
  });

  const defaultProps = {
    row: {
      original: {
        custdiscount_id: 1,
        category_id: 10,
        catdiscount: '5',
      },
      getIsSelected: () => true,
    },
    setDiscountData: mockSetDiscountData,
    discountData: [
      {
        category_id: 10,
        custdiscount_id: 1,
        catdiscount: '5',
      },
    ],
  };

  it('renders the component with the correct discount value', () => {
    render(<DiscountCell {...defaultProps} />);
    expect(screen.getByText('5.00%')).toBeInTheDocument();
    expect(screen.getByTestId('edit-icon')).toBeInTheDocument();
  });

  it('enters edit mode when the edit icon is clicked', () => {
    render(<DiscountCell {...defaultProps} />);
    fireEvent.click(screen.getByTestId('edit-icon'));

    expect(screen.getByPlaceholderText('__.__%')).toBeInTheDocument();
    expect(screen.getByTestId('submit-icon')).toBeInTheDocument();
    expect(screen.getByTestId('close-icon')).toBeInTheDocument();
  });

  it('saves the discount value when save is clicked', async () => {
    mockUpdateDiscount.mockResolvedValueOnce({
      data: {
        message: 'Discount updated successfully',
        data: [
          {
            custdiscount_id: 1,
            category_id: 10,
            catdiscount: 8,
          },
        ],
      },
    });

    render(<DiscountCell {...defaultProps} />);
    fireEvent.click(screen.getByTestId('edit-icon'));

    const input = screen.getByPlaceholderText('__.__%');
    fireEvent.change(input, { target: { value: '8' } });

    fireEvent.click(screen.getByTestId('submit-icon'));

    await waitFor(() => {
      expect(mockUpdateDiscount).toHaveBeenCalledWith([
        {
          custdiscount_id: 1,
          customer_id: 123,
          category_id: 10,
          catdiscount: 8,
        },
      ]);
    });

    expect(mockSetDiscountData).toHaveBeenCalled();
    expect(screen.queryByTestId('spinner')).not.toBeInTheDocument();
  });

  it('cancels editing and reverts to the original discount value', () => {
    render(<DiscountCell {...defaultProps} />);
    fireEvent.click(screen.getByTestId('edit-icon'));

    const input = screen.getByPlaceholderText('__.__%');
    fireEvent.change(input, { target: { value: '8' } });

    fireEvent.click(screen.getByTestId('close-icon'));

    expect(screen.getByText('5.00%')).toBeInTheDocument();
    expect(screen.queryByPlaceholderText('__.__%')).not.toBeInTheDocument();
  });

  it('shows a spinner while the discount is being updated', async () => {
    (useUpdateDiscountMutation as any).mockReturnValueOnce([
      mockUpdateDiscount,
      { isLoading: true },
    ]);

    render(<DiscountCell {...defaultProps} />);
    fireEvent.click(screen.getByTestId('edit-icon'));

    const loadSpinner = fireEvent.click(screen.getByTestId('submit-icon'));
    expect(loadSpinner).toBe(true);
  });
});
