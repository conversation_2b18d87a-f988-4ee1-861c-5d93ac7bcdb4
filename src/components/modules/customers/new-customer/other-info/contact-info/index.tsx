import InputField from '@/components/forms/input-field';
import PhoneInputWidget from '@/components/forms/phone-input-mask';
import { Separator } from '@/components/ui/separator';
import { EMAIL_VALIDATION_RULEOptional } from '@/constants/auth-constants';
import {
  FAX_VALIDATION_RULE,
  WEB_SITE_VALIDATION_RULE,
} from '@/constants/validation-constants';
import { FormData } from '@/types/customer.types';
import { useFormContext } from 'react-hook-form';

const ContactInfo = () => {
  const form = useFormContext<FormData>();

  return (
    <div className="bg-background-default-hover p-8 flex flex-col gap-6 rounded-lg">
      <div className="flex flex-col gap-3">
        <h3 className="text-xl text-text-Default">Contact Info</h3>
        <Separator
          orientation="horizontal"
          className="h-[1px] bg-border-Default"
        />
      </div>

      <div className="grid grid-cols-2 gap-4">
        <InputField
          form={form}
          name="contact"
          label="Contact"
          placeholder="Enter Contact"
        />
        <InputField
          form={form}
          name="emailaddress"
          label="Email"
          placeholder="Enter Email"
          validation={EMAIL_VALIDATION_RULEOptional}
        />
        <PhoneInputWidget form={form} name="tel1" label="Phone" />
        <PhoneInputWidget form={form} name="tel2" label="Phone 2" />
        <InputField
          form={form}
          name="website"
          label="Website"
          placeholder="Enter Website"
          validation={WEB_SITE_VALIDATION_RULE}
        />
        <PhoneInputWidget
          form={form}
          name="telfax"
          label="Fax"
          isFax={true}
          validation={FAX_VALIDATION_RULE}
        />
      </div>
    </div>
  );
};

export default ContactInfo;
