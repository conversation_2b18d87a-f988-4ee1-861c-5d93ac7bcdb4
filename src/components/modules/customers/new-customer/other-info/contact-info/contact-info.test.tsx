import { render, screen } from '@testing-library/react';
import { FormProvider, useForm } from 'react-hook-form';
import ContactInfo from '.';
import { describe, it, expect } from 'vitest';

describe('ContactInfo Component', () => {
  const Wrapper = ({ children }: { children: React.ReactNode }) => {
    const methods = useForm({
      defaultValues: {
        emailaddress: '',
        tel1: '',
        website: '',
        telfax: '',
      },
    });

    return <FormProvider {...methods}>{children}</FormProvider>;
  };

  it('should render ContactInfo component with all fields and labels', () => {
    render(
      <Wrapper>
        <ContactInfo />
      </Wrapper>
    );

    // Check for heading
    expect(screen.getByText('Contact Info')).toBeInTheDocument();

    // Check for all labels
    expect(screen.getByText('Email')).toBeInTheDocument();
    expect(screen.getByText('Phone')).toBeInTheDocument();
  });

  it('should validate email field with correct rule', () => {
    render(
      <Wrapper>
        <ContactInfo />
      </Wrapper>
    );

    const emailField = screen.getByLabelText('Email');
    expect(emailField).toHaveAttribute('type', 'text');
  });

  it('should render phone and fax fields with phone input widget', () => {
    render(
      <Wrapper>
        <ContactInfo />
      </Wrapper>
    );

    const phoneField = screen.getByText('Phone');
    const faxField = screen.getByText('Fax');

    expect(phoneField).toBeInTheDocument();
    expect(faxField).toBeInTheDocument();
  });
});
