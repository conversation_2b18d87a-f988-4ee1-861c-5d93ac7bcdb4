import UploadIcon from '@/assets/icons/UploadIcon';
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { cn } from '@/lib/utils';
import React, { useCallback, useEffect, useState } from 'react';
import { CircleProgress } from '@/components/ui/circular-progress';
import AppButton from '@/components/common/app-button';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
import { setDialog } from '@/redux/features/customers/noteSlice';
import { useUploadFilesMutation } from '@/redux/features/customers/files';
import { useParams } from 'react-router-dom';
import { UseToast } from '@/components/ui/toast/ToastContainer';
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5 MB in bytes
const ACCEPTED_FILE_TYPES = [
  'image/jpeg', // standard image format
  'image/png', // PNG format
  'image/gif', // GIF format
  'image/heic', // HEIC (iPhone High Efficiency Image Coding) format
  'image/heif', // HEIF (High Efficiency Image Format, used by iPhone)
  'application/pdf', // pdf type
  'application/msword', // doc type
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // docx type
  'text/plain', // txt type
  'application/vnd.ms-excel', // xls type
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // xlsx type
  'application/vnd.ms-excel.sheet.macroenabled.12', // xlsm type (Excel macro-enabled)
  'application/vnd.ms-word.document.macroenabled.12', // docm type (Word macro-enabled)
  'application/xml', // xml type
];

const FileUploadDialog: React.FC = () => {
  const { customerId } = useParams<{ customerId?: string }>();
  const notes = useSelector((state: RootState) => state.notes.open);
  const dispatch = useDispatch();
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const [files, setFiles] = useState<File[]>([]);
  const [progressMap, setProgressMap] = useState<{ [key: string]: number }>({});
  const [uploadFile, { isLoading }] = useUploadFilesMutation();
  const toast = UseToast();

  const handleDrag = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(e.type === 'dragenter' || e.type === 'dragover');
  }, []);

  const handleDrop = useCallback(
    (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault();
      e.stopPropagation();
      setIsDragging(false);

      const droppedFiles = Array.from(e.dataTransfer.files);

      // Ensure only one file is dropped
      if (droppedFiles.length > 1) {
        toast.error(
          'Multiple images are not allowed. Please drop only one file.'
        );
        return;
      }
      // Only handle the first file
      const selectedFile = droppedFiles[0];

      if (!selectedFile) {
        toast.error('No file dropped. Please drop a valid file.');
        return;
      }

      // Validation
      if (selectedFile.size > MAX_FILE_SIZE) {
        toast.error(
          `File ${selectedFile.name} exceeds the 5 MB limit. Please select a smaller file.`
        );
        return;
      }

      if (!ACCEPTED_FILE_TYPES.includes(selectedFile.type)) {
        toast.error(
          `File ${selectedFile.name} has an unsupported file type. Please select a valid file.`
        );
        return;
      }

      // If validation passes, update the state with the selected file
      setFiles([selectedFile]);
    },
    [toast]
  );

  const removeFile = (fileToRemove: File) => {
    setFiles((prevFiles) => prevFiles.filter((file) => file !== fileToRemove));
  };

  const handleFileInput = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0]; // Use optional chaining to safely access the first file

    // Early return if no file is selected
    if (!selectedFile) {
      toast.error('No file selected. Please choose a file.');
      return;
    }

    // Validation
    if (selectedFile.size > MAX_FILE_SIZE) {
      toast.error(
        `File ${selectedFile.name} exceeds the 5 MB limit. Please select a smaller file.`
      );
      return;
    }

    if (!ACCEPTED_FILE_TYPES.includes(selectedFile.type)) {
      toast.error(
        `File ${selectedFile.name} has an unsupported file type. Please select a valid file.`
      );
      return;
    }

    // If validation passes, update the state with the selected file
    setFiles([selectedFile]);
  };

  const uploadFiles = async () => {
    if (files.length === 0) {
      toast.error('No files selected for upload.');
      return;
    }

    const formData = new FormData();
    files.forEach((file) => formData.append('file', file));

    try {
      const response: any = await uploadFile({ formData, customerId });

      if (response?.data?.statusCode === 200) {
        toast.success(response?.data.message);
        dispatch(setDialog({ name: 'linked-files', state: false, id: null }));
        setFiles([]);
      } else {
        toast.error(
          response.data.message ||
            'An error occurred while uploading the files.'
        );
      }
    } catch (error) {
      // toast.error('Error uploading files. Please try again.');
    }
  };

  useEffect(() => {
    return () => {
      setFiles([]);
      setProgressMap({});
    };
  }, []);

  return (
    <Dialog
      open={notes.name === 'linked-files' && notes.state}
      onOpenChange={(state) => {
        dispatch(
          setDialog({
            name: 'quick-notes',
            state: state,
            id: null,
          })
        );
        setFiles([]);
        setProgressMap({});
      }}
    >
      <DialogContent
        className="bg-white p-0 max-w-[570px] border-0 "
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
      >
        <DialogHeader className="pl-6 pr-6 pb-3 pt-3 bg-background-default-hover rounded-t-md">
          <DialogTitle className="text-text-Default font-normal text-xl">
            Linked Files
          </DialogTitle>
        </DialogHeader>

        <div className="flex flex-col gap-6 p-6 w-full">
          {files.length < 1 && (
            <div
              onDragEnter={handleDrag}
              onDragOver={handleDrag}
              onDragLeave={handleDrag}
              onDrop={handleDrop}
              onClick={() => document.getElementById('file-upload')?.click()}
              className={cn(
                isDragging
                  ? 'border-purple-500 bg-purple-50'
                  : 'border-gray-300',
                files.length > 0 ? 'pb-4' : 'pb-8',
                'border-2 border-dashed flex flex-col justify-center items-center cursor-pointer rounded-lg text-center p-16 bg-background-default-hover'
              )}
            >
              <>
                <UploadIcon />
                <span className="mt-2 text-text-tertiary font-normal text-base">
                  Drag & Drop or upload to add a document as a Linked File to
                  this customer
                </span>
              </>

              <input
                type="file"
                accept="image/*,application/pdf,.doc,.docx,.txt,.xls,.xlsx,.docm,.xlsm,.xml"
                className="hidden"
                id="file-upload"
                onChange={handleFileInput}
                multiple={false}
              />
            </div>
          )}
          {files.length > 0 && (
            <div className="mt-4">
              {files.map((file) => (
                <div
                  key={file.name}
                  className="flex flex-col items-center justify-center gap-4 p-6 rounded border border-dashed border-blue-300 bg-[#F5F5F5] w-full"
                >
                  <CircleProgress value={progressMap[file.name] || 0} />
                  <span className="text-base text-text-Default font-semibold">
                    {file.name}
                  </span>
                  <span className="text-sm text-gray-500">
                    {`${(file.size / 1024).toFixed(2)} KB`}
                  </span>

                  <AppButton
                    label="Remove File"
                    className="w-full"
                    variant="danger"
                    onClick={() => removeFile(file)}
                    disabled={isLoading}
                  />
                </div>
              ))}
            </div>
          )}
          <AppButton
            label="Upload Document"
            className="w-full"
            isLoading={isLoading}
            disabled={!files.length}
            onClick={uploadFiles}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default FileUploadDialog;
