import { render, screen } from '@testing-library/react';
import { vi, describe, beforeEach, it, expect } from 'vitest';
import FileUploadDialog from '.';
import {
  useUploadFilesMutation,
  useGetFilesByCustomerIdQuery,
  useDeletefileMutation,
} from '@/redux/features/customers/files';

describe('FileUploadDialog', () => {
  beforeEach(() => {
    vi.mock('@/redux/features/customers/files', () => ({
      useUploadFilesMutation: vi.fn(),
      useGetFilesByCustomerIdQuery: vi.fn(),
      useDeletefileMutation: vi.fn(),
    }));

    vi.mock('@/components/ui/toast/ToastContainer', () => ({
      UseToast: vi.fn().mockReturnValue({
        error: vi.fn(),
        success: vi.fn(),
      }),
    }));

    (useUploadFilesMutation as any).mockReturnValue([
      vi.fn(),
      { isLoading: false },
    ]);

    (useGetFilesByCustomerIdQuery as any).mockReturnValue([
      vi.fn(),
      { data: {}, isLoading: false },
    ]);

    (useDeletefileMutation as any).mockReturnValue([
      vi.fn(),
      { data: {}, isLoading: false },
    ]);
  });

  it('should display the upload dialog', () => {
    render(<FileUploadDialog />);

    expect(screen.getByText('Icon')).toBeInTheDocument();
    expect(screen.getByText('File Name')).toBeInTheDocument();
    expect(screen.getByText('Date Created')).toBeInTheDocument();
    expect(screen.getByText('Owner')).toBeInTheDocument();
  });
});
