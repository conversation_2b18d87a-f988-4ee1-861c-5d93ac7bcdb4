import TrailingIcon from '@/assets/icons/TrailingIcon';
import AppButton from '@/components/common/app-button';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import DataTable from '@/components/common/data-tables';
import ActionColumnMenu from '@/components/common/data-tables/ActionColumn';
import { UseToast } from '@/components/ui/toast/ToastContainer';
import { FILES_API } from '@/constants/api-constants';
import { useDownloadFile } from '@/hooks/useDownloadFile';
import { formatDate } from '@/lib/utils';
import {
  useDeletefileMutation,
  useGetFilesByCustomerIdQuery,
} from '@/redux/features/customers/files';
import { CustomerFilesDto } from '@/types/customer.types';
import { ColumnDef } from '@tanstack/react-table';
import { Download } from 'lucide-react';
import { memo, useCallback, useState } from 'react';
import { useParams } from 'react-router-dom';

const LinkedFiles = () => {
  const { customerId } = useParams<{ customerId?: string }>();
  const { data, isFetching } = useGetFilesByCustomerIdQuery(Number(customerId));
  const [openDeleteDialog, setOpenDeleteDialog] = useState<boolean>(false);
  const [fileId, setFileId] = useState<number | null>(null);
  const toast = UseToast();
  const [deleteFile, { isLoading: isDeleteLoading }] = useDeletefileMutation();
  const { downloadFile, isLoading: isDownloading } = useDownloadFile();

  const handleDownload = useCallback(
    (id: number) => {
      const response = downloadFile({
        url: FILES_API.DOWNLOAD(id),
      });
      toast.promise(response, {
        loading: 'Downloading file...',
        success: 'File downloaded successfully.',
        error: 'Failed to download file',
      });
    },
    [downloadFile, toast]
  );

  const columns: ColumnDef<CustomerFilesDto>[] = [
    {
      accessorKey: 'icon',
      header: 'Icon',
      size: 50,
      cell: () => (
        <div className="flex justify-center">
          <TrailingIcon />
        </div>
      ),
    },
    {
      accessorKey: 'fileName',
      header: 'File Name',
      cell: ({ row }) => {
        return (
          <div className="lg:max-w-[16rem] 2xl:max-w-none">
            <p className="max-w-48 truncate">{row.getValue('fileName')}</p>
          </div>
        );
      },
    },
    {
      accessorKey: 'dateCreated',
      header: 'Date Created',
      cell: (info) => formatDate(info?.row?.original?.createdAt),
    },
    {
      accessorKey: 'createdBy',
      header: 'Owner',
      size: 90,
    },
    {
      id: 'action',
      size: 110,
      header: 'Actions',
      cell: ({ row }) => (
        <ActionColumnMenu
          customEdit={
            <>
              <AppButton
                onClick={() => handleDownload(row.original.id)}
                variant="neutral"
                label="Download"
                icon={Download}
                disabled={isDownloading}
              />
            </>
          }
          onDelete={() => handleDeleteRecods(row.original.id)}
        />
      ),
    },
  ];

  const handleDeleteFile = async () => {
    try {
      await deleteFile({ FileId: fileId })
        .unwrap()
        .then((response) => {
          UseToast().success(response?.message ?? '');
        })
        .finally(() => {
          onOpenChange();
          setFileId(null);
        });
    } catch (error) {}
  };
  const onOpenChange = useCallback(() => {
    setOpenDeleteDialog((prevState) => !prevState);
  }, [setOpenDeleteDialog]);

  const handleDeleteRecods = useCallback(
    (id: number) => {
      setFileId(id);
      onOpenChange();
    },
    [onOpenChange]
  );

  return (
    <div className="w-full">
      <DataTable
        columns={columns}
        data={data?.data ?? []}
        enablePagination={false}
        isLoading={isFetching}
      />
      <AppConfirmationModal
        description={<div>Are you sure you want to delete records ?</div>}
        open={openDeleteDialog}
        onOpenChange={onOpenChange}
        handleCancel={onOpenChange}
        disabled={isDeleteLoading}
        isLoading={isDeleteLoading}
        handleSubmit={handleDeleteFile}
      />
    </div>
  );
};

export default memo(LinkedFiles);
