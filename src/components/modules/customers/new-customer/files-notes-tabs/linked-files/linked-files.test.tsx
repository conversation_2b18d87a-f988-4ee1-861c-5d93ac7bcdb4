import { render, screen } from '@testing-library/react';
import { vi, describe, beforeEach, it, expect } from 'vitest';
import { MemoryRouter, Route, Routes } from 'react-router-dom';
import LinkedFiles from '.';
import {
  useGetFilesByCustomerIdQuery,
  useDeletefileMutation,
} from '@/redux/features/customers/files';

describe('LinkedFiles', () => {
  const mockFiles = [
    {
      id: '1',
      filename: 'file1.txt',
      createdon: '2022-01-01',
      owner: 'Owner 1',
      actions: <div className="flex justify-center">Cancel</div>,
    },
    {
      id: '2',
      filename: 'file2.txt',
      createdon: '2022-01-02',
      owner: 'Owner 2',
      actions: <div className="flex justify-center">Cancel</div>,
    },
  ];

  const mockDeleteFile = vi.fn();

  beforeEach(() => {
    vi.mock('@/redux/features/customers/files', () => ({
      useGetFilesByCustomerIdQuery: vi.fn(),
      useDeletefileMutation: vi.fn(),
    }));

    (useGetFilesByCustomerIdQuery as any).mockReturnValue({
      data: { data: mockFiles },
      isFetching: false,
    });

    (useDeletefileMutation as any).mockReturnValue([
      mockDeleteFile,
      { isLoading: false },
    ]);
  });

  it('renders the linked files table', () => {
    render(
      <MemoryRouter initialEntries={['/customer/1']}>
        <Routes>
          <Route path="/customer/:customerId" element={<LinkedFiles />} />
        </Routes>
      </MemoryRouter>
    );

    // Ensure the file names are displayed
    expect(screen.getByText('Icon')).toBeInTheDocument();
    expect(screen.getByText('File Name')).toBeInTheDocument();
    expect(screen.getByText('Date Created')).toBeInTheDocument();
    expect(screen.getByText('Owner')).toBeInTheDocument();
  });

  it('opens and closes the delete confirmation modal', async () => {
    render(
      <MemoryRouter initialEntries={['/customer/1']}>
        <Routes>
          <Route path="/customer/:customerId" element={<LinkedFiles />} />
        </Routes>
      </MemoryRouter>
    );
    expect(
      screen.queryByText('Are you sure you want to delete records ?')
    ).not.toBeInTheDocument();
  });
});
