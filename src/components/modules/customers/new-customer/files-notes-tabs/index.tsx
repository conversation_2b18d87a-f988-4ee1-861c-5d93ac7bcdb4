import ActionArea from '@/components/common/action-area';
import { Separator } from '@/components/ui/separator';
import { filesNotesTablist } from '@/constants/customer-constants';
import { cn } from '@/lib/utils';
import { setDialog } from '@/redux/features/customers/noteSlice';
import { InfoIcon, Plus } from 'lucide-react';
import { memo, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useParams } from 'react-router-dom';
import FileUploadDialog from './linked-files/LinkedFilesDialog';
import QuickNotesDialog from './quick-notes/QuickNotesDialog';
import AppButton from '@/components/common/app-button';

const FilesNotesTabs = () => {
  const { customerId } = useParams<{ customerId?: string }>();
  const [activeTab, setActiveTab] = useState('quick-notes');
  // const [open, setOpen] = useState({ name: '', state: false });
  const dispatch = useDispatch();

  const handleAddNew = () => {
    // Open the appropriate dialog based on the active tab
    if (activeTab === 'quick-notes') {
      dispatch(setDialog({ name: 'quick-notes', state: true, id: null }));
    } else if (activeTab === 'linked-files') {
      dispatch(setDialog({ name: 'linked-files', state: true, id: null }));
    }
  };

  const handleTabChange = (value: string) => {
    setActiveTab(value);
  };

  return (
    <div
      className={cn(
        'col-span-12 flex flex-col md:col-span-6 ',
        customerId && 'border p-8 rounded-lg'
      )}
    >
      {customerId ? (
        <>
          <div className="flex justify-between items-center">
            <h3 className="text-xl font-normal text-text-Default mb-2">
              Files & Notes
            </h3>
            {customerId && (
              <AppButton
                label="Add New"
                variant="neutral"
                className="bg-brand-teal-Default hover:bg-brand-teal-hover border-border-brand-teal-Default text-white mb-2"
                iconClassName="text-white"
                icon={Plus}
                onClick={handleAddNew}
              />
            )}
          </div>
          <Separator
            orientation="horizontal"
            className="h-[1px] bg-border-Default mb-3"
          />
          <div>
            <ActionArea
              tabsContentClassName="h-full"
              tabs={filesNotesTablist}
              defaultValue={activeTab}
              onValueChange={handleTabChange}
            />
          </div>
          <FileUploadDialog />
          <QuickNotesDialog />
        </>
      ) : (
        <div className=" border border-[#E2C7AC] h-[150px] bg-[#FFFBEB] rounded-md  flex flex-col gap-6 p-8">
          <div className="text-[#522504] flex flex-row gap-[10px] items-center">
            <InfoIcon className="w-5 h-5" />
            <h3 className="text-brown-600 font-normal text-lg">Info</h3>
          </div>
          <div>
            <p className="text-[#BF6A02]">
              Please fill out the mandatory info on the left to add more
              details.
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default memo(FilesNotesTabs);
