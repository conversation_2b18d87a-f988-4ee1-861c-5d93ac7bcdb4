import EditIcon from '@/assets/icons/EditIcon';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';

import DataTable from '@/components/common/data-tables';
import ActionColumnMenu from '@/components/common/data-tables/ActionColumn';
import { formatDate, getQueryParam, updateQueryParam } from '@/lib/utils';
import {
  useDeleteNoteMutation,
  useGetNotesByCustomerIdQuery,
} from '@/redux/features/customers/notes.api';
import { setDialog } from '@/redux/features/customers/noteSlice';
import { CustomerNotesDto } from '@/types/customer.types';
import { ColumnDef } from '@tanstack/react-table';
import { useCallback, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useParams } from 'react-router-dom';

const QuickNotes = () => {
  const { customerId } = useParams<{ customerId?: string }>();

  // Retrieve the customer ID from the URL query parameters
  const customerIdFromQuery = getQueryParam('customerId');

  const { data, isFetching } = useGetNotesByCustomerIdQuery(
    Number(customerId || customerIdFromQuery),
    { skip: !(customerId || customerIdFromQuery) }
  );
  const dispatch = useDispatch();
  const [deleteNote, { isLoading: isDeleteLoading }] = useDeleteNoteMutation();
  const [openDeleteDialog, setOpenDeleteDialog] = useState<boolean>(false);
  const [notesToDelete, setNotesToDelete] = useState<number | null>(null);

  const onOpenChange = useCallback(() => {
    setOpenDeleteDialog((prevState) => !prevState);
  }, [setOpenDeleteDialog]);

  const handleDeleteClick = useCallback(
    (noteId: number) => {
      setNotesToDelete(noteId);
      onOpenChange();
    },
    [onOpenChange]
  );

  const handleDeleteCustomer = async () => {
    try {
      await deleteNote(notesToDelete as number).unwrap();
      onOpenChange();
      setNotesToDelete(null);
    } catch (error) {}
  };

  const columns: ColumnDef<CustomerNotesDto>[] = [
    {
      accessorKey: 'note',
      header: 'Notes',
      cell: ({ row }) => {
        return <p className="max-w-40 truncate ">{row.getValue('note')}</p>;
      },
    },
    {
      accessorKey: 'dateCreated',
      header: 'Date Created',
      cell: (info) => formatDate(info?.row?.original?.createdOn),
    },
    {
      accessorKey: 'createdBy',
      header: 'Owner',
      size: 100,
    },
    {
      id: 'action',
      size: 110,
      header: 'Actions',
      cell: ({ row }) => (
        <ActionColumnMenu
          customEdit={
            <div
              onClick={() => {
                updateQueryParam(row.original.customernote_id, 'noteId');
                dispatch(
                  setDialog({
                    name: 'quick-notes',
                    state: true,
                    id: row.original.customernote_id,
                  })
                );
              }}
            >
              <EditIcon />
            </div>
          }
          onDelete={() => handleDeleteClick(row.original.customernote_id)}
          contentClassName="w-fit z-[99]"
        />
      ),
    },
  ];

  return (
    <div>
      <DataTable
        tableClassName="max-h-[400px] overflow-auto"
        columns={columns}
        data={data?.data ?? []}
        enablePagination={false}
        isLoading={isFetching}
        loaderRows={6}
      />
      <AppConfirmationModal
        description={<div>Are you sure you want to delete records ?</div>}
        open={openDeleteDialog}
        onOpenChange={onOpenChange}
        handleCancel={onOpenChange}
        disabled={isDeleteLoading}
        handleSubmit={handleDeleteCustomer}
        isLoading={isDeleteLoading}
      />
    </div>
  );
};

export default QuickNotes;
