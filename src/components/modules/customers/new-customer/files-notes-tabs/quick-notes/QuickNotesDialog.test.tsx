import { render, screen, fireEvent } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import QuickNotesDialog from '.';
import { Provider } from 'react-redux';
import { store } from '@/redux/store';
import {
  useGetNoteByIdMutation,
  useUpdateNoteMutation,
  useGetNotesByCustomerIdQuery,
} from '@/redux/features/customers/notes.api';
import { BrowserRouter, useParams } from 'react-router-dom';

const renderWithProviders = () =>
  render(
    <Provider store={store}>
      <BrowserRouter>
        <QuickNotesDialog />
      </BrowserRouter>
    </Provider>
  );

describe('QuickNotesDialog', () => {
  const customerId = '123';
  const mockNotesData = {
    data: [
      {
        customernote_id: 1,
        note: 'Test Note',
        dateCreated: '2025-01-01',
        owner: 'Test Owner',
      },
    ],
  };

  beforeEach(() => {
    vi.mock('react-router-dom', () => ({
      BrowserRouter: ({ children }: { children: React.ReactNode }) => (
        <div>{children}</div>
      ),
      useParams: vi.fn(),
    }));

    (useParams as any).mockReturnValue({ customerId });

    // Mock the notesApi module
    vi.mock('@/redux/features/customers/notes.api', async () => {
      const actual = await import('@/redux/features/customers/notes.api');
      return {
        ...actual,
        useGetNotesByCustomerIdQuery: vi.fn(),
        useCreateNoteMutation: vi.fn(),
        useUpdateNoteMutation: vi.fn(),
        useGetNoteByIdMutation: vi.fn(),
      };
    });

    (useGetNotesByCustomerIdQuery as any).mockReturnValue({
      data: mockNotesData,
      isFetching: false,
    });

    (useUpdateNoteMutation as any).mockReturnValue([
      vi.fn(),
      { isLoading: false },
    ]);

    (useGetNoteByIdMutation as any).mockReturnValue([
      vi.fn(),
      { data: { data: mockNotesData.data }, isLoading: false },
    ]);
  });

  it('should QuickNotesDialog render', () => {
    store.dispatch({
      type: 'notes/setDialog',
      payload: { name: 'quick-notes', state: true, id: null },
    });

    renderWithProviders();
    expect(screen.getByText('Notes')).toBeInTheDocument();
  });

  it('should click on table row', async () => {
    renderWithProviders();
    const isClicked = fireEvent.click(screen.getByRole('button'));
    expect(isClicked).toBe(true);
  });
});
