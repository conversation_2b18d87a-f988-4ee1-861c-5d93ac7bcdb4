import AppButton from '@/components/common/app-button';
import AppSpinner from '@/components/common/app-spinner';
import CustomDialog from '@/components/common/dialog';
import Text<PERSON>reaField from '@/components/forms/text-area';
import { getQueryParam, updateQueryParam } from '@/lib/utils';
import {
  useCreateNoteMutation,
  useGetNoteByIdMutation,
  useUpdateNoteMutation,
} from '@/redux/features/customers/notes.api';
import { setDialog } from '@/redux/features/customers/noteSlice';
import { RootState } from '@/redux/store';
import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import { useParams } from 'react-router-dom';

type NoteForm = {
  note: string;
};

const QuickNotesDialog: React.FC = () => {
  const notes = useSelector((state: RootState) => state.notes.open);
  const dispatch = useDispatch();
  const { customerId } = useParams<{ customerId?: string }>();

  // Retrieve the customer ID from the URL query parameters
  const customerIdFromQuery = getQueryParam('customerId');

  const form = useForm<NoteForm>({
    defaultValues: { note: '' },
    mode: 'onChange',
  });
  const [getNoteById, { data: noteData, isLoading }] = useGetNoteByIdMutation();
  const [updateNote, { isLoading: isUpdating }] = useUpdateNoteMutation();
  const [createNote, { isLoading: isCreating }] = useCreateNoteMutation();
  const noteId = getQueryParam('noteId');

  // Fetch existing note if an ID is provided
  useEffect(() => {
    if (notes.id && notes.name === 'quick-notes') {
      getNoteById(Number(noteId) ?? 0); // Fetch the note by ID
    }
  }, [getNoteById, notes.id, notes.name, noteId]);

  // Reset form when noteData is available
  useEffect(() => {
    if (noteData?.data) {
      form.reset({
        note: noteData?.data?.note || '', // Reset form with note data
      });
    }
  }, [noteData, form]);

  // Handle form submission
  const onSubmit = async (data: NoteForm) => {
    if (notes.id) {
      // If note ID exists, update the note
      try {
        await updateNote({
          noteId: notes.id as number,
          noteData: {
            customernote_id: notes.id as number,
            customer_id: customerId || customerIdFromQuery,
            note: data?.note,
          },
        });
        dispatch(
          setDialog({
            name: '',
            state: false,
            id: null,
          })
        );
        form.reset({ note: '' }); // Clear form after submit
      } catch (error) {}
    } else {
      // If note ID does not exist, create a new note
      try {
        await createNote({
          note: data.note,
          customer_id: Number(customerId || customerIdFromQuery),
        });
        dispatch(
          setDialog({
            name: '',
            state: false,
            id: null,
          })
        );
        form.reset({ note: '' }); // Clear form after submit
      } catch (error) {}
    }
  };

  // Reset form when dialog is closed
  const handleDialogClose = () => {
    dispatch(
      setDialog({
        name: '',
        state: false,
        id: null,
      })
    );
    if (noteId) {
      updateQueryParam(null, 'noteId');
    }
    form.reset({ note: '' }); // Reset form when dialog is closed
  };

  return (
    <CustomDialog
      onOpenChange={(state) => {
        if (!state) {
          handleDialogClose(); // Reset form when dialog is closed
        }
      }}
      title="Quick Note"
      description=""
      open={notes.name === 'quick-notes' && notes.state}
    >
      <div className="relative px-6 h-[300px]">
        <TextAreaField
          rows={10}
          name="note" // Name matches with the form field
          form={form}
          placeholder="Enter quick notes"
          className="border-border-Default min-h-[220px] max-h-[220px]"
        />
        <AppButton
          label="Submit"
          className="w-full mt-6"
          isLoading={isUpdating || isCreating}
          onClick={form.handleSubmit(onSubmit)}
        />
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-white/50 backdrop-blur-[2px] z-10">
            <AppSpinner
              className="h-8 w-8 text-brand-teal-Default"
              isLoading={isLoading}
            />
          </div>
        )}
      </div>
    </CustomDialog>
  );
};

export default QuickNotesDialog;
