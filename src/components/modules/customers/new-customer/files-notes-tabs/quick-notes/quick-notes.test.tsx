import { render, screen, fireEvent } from '@testing-library/react';
import { vi, describe, beforeEach, test, expect } from 'vitest';
import { Provider } from 'react-redux';
import { BrowserRouter as Router } from 'react-router-dom';
import { store } from '@/redux/store';
import QuickNotes from '.';
import {
  useDeleteNoteMutation,
  useGetNotesByCustomerIdQuery,
} from '@/redux/features/customers/notes.api';

// Mock hooks used in QuickNotes
vi.mock('@/redux/features/customers/notes.api', async () => {
  const actual = await import('@/redux/features/customers/notes.api');

  return {
    ...actual,
    useGetNotesByCustomerIdQuery: vi.fn(),
    useDeleteNoteMutation: vi.fn(),
    notesApi: {
      ...actual.notesApi,
      reducerPath: 'notesApi',
    },
  };
});

vi.mock('@/lib/utils', () => ({
  formatDate: vi.fn().mockReturnValue('2025-01-16'),
  updateQueryParam: vi.fn(),
  getQueryParam: vi.fn().mockReturnValue(''), // add a default return value as needed
  cn: vi.fn(),
}));
describe('QuickNotes Component', () => {
  let deleteNoteMock;

  beforeEach(() => {
    deleteNoteMock = vi.fn().mockResolvedValue({});

    (useGetNotesByCustomerIdQuery as any).mockReturnValue({
      data: {
        data: [
          {
            customernote_id: 1,
            note: 'Test Note',
            dateCreated: '2025-01-16',
            owner: 'Owner',
          },
        ],
      },
      isFetching: false,
    });

    (useDeleteNoteMutation as any).mockReturnValue([
      deleteNoteMock,
      { isLoading: false },
    ]);
  });

  test('should render QuickNotes without crashing', () => {
    render(
      <Provider store={store}>
        <Router>
          <QuickNotes />
        </Router>
      </Provider>
    );

    expect(screen.getByText('Notes')).toBeInTheDocument();
    expect(screen.getByText('Date Created')).toBeInTheDocument();
  });

  test('should open the delete dialog when delete action is clicked', () => {
    render(
      <Provider store={store}>
        <Router>
          <QuickNotes />
        </Router>
      </Provider>
    );

    const isClickAble = fireEvent.click(
      screen.getByRole('button', { name: 'Column Visibility Icon' })
    );

    expect(isClickAble).toBe(true);
  });
});
