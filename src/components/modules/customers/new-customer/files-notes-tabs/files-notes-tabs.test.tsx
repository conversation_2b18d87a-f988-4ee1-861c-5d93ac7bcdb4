import { render, screen, cleanup } from '@testing-library/react';
import { Provider } from 'react-redux';
import { MemoryRouter } from 'react-router-dom';
import { store } from '@/redux/store';
import FilesNotesTabs from '.';
import { vi, describe, afterEach, it, expect } from 'vitest';

vi.mock('@/components/common/action-area', () => ({
  __esModule: true,
  default: () => <div>Mock ActionArea</div>,
}));

vi.mock('@/components/ui/separator', () => ({
  __esModule: true,
  Separator: () => <div>Mock Separator</div>,
}));

vi.mock('@/components/linked-files/LinkedFilesDialog', () => ({
  __esModule: true,
  default: () => <div>Mock FileUploadDialog</div>,
}));

vi.mock('@/components/quick-notes/QuickNotesDialog', () => ({
  __esModule: true,
  default: () => <div>Mock QuickNotesDialog</div>,
}));

describe('FilesNotesTabs', () => {
  afterEach(cleanup);

  it('should show info message when customerId is not provided', () => {
    render(
      <Provider store={store}>
        <MemoryRouter initialEntries={['/customer']}>
          <FilesNotesTabs />
        </MemoryRouter>
      </Provider>
    );

    expect(screen.getByText('Info')).toBeInTheDocument();
    expect(
      screen.getByText(
        'Please fill out the mandatory info on the left to add more details.'
      )
    ).toBeInTheDocument();
  });

  it('should open correct dialog based on active tab when clicking + Add New', () => {
    render(
      <Provider store={store}>
        <MemoryRouter initialEntries={['/customer/1']}>
          <FilesNotesTabs />
        </MemoryRouter>
      </Provider>
    );

    expect(screen.getByText('Info')).toBeInTheDocument();
  });
});
