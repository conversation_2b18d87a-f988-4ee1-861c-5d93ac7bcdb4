import { render, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import Header from '.';
import { BrowserRouter as Router } from 'react-router-dom';

describe('Header Component', () => {
  it('should render header with title and button', () => {
    const onSaveMock = vi.fn();

    const { getByText } = render(
      <Router>
        <Header
          onSave={onSaveMock}
          headerTitle="Test Header"
          isLoading={false}
          buttonLable="Save"
        />
      </Router>
    );

    expect(getByText('Test Header')).toBeInTheDocument();
    expect(getByText('Save')).toBeInTheDocument();
  });

  it('should call onSave when the button is clicked', () => {
    const onSaveMock = vi.fn();

    const { getByText } = render(
      <Router>
        <Header
          onSave={onSaveMock}
          headerTitle="Test Header"
          isLoading={false}
          buttonLable="Save"
        />
      </Router>
    );

    const saveButton = getByText('Save');
    fireEvent.click(saveButton);

    expect(onSaveMock).toHaveBeenCalledTimes(1);
  });

  it('should disable the button when isDisabled prop is true', () => {
    const onSaveMock = vi.fn();

    const { getByText } = render(
      <Router>
        <Header
          onSave={onSaveMock}
          headerTitle="Test Header"
          isLoading={false}
          isDisabled={true}
          buttonLable="Save"
        />
      </Router>
    );

    const saveButton = getByText('Save');
    expect(saveButton);
  });
});
