import AppButton from '@/components/common/app-button';
import IconButton from '@/components/common/icon-button';
import CheveronLeft from '@/assets/icons/CheveronLeft';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import { useNavigate } from 'react-router-dom';

const Header = ({
  onSave,
  headerTitle,
  className,
  navigateTo,
  isLoading,
  isDisabled = false,
  showButton = true,
  hideBackBtn = false,
  buttonLable = 'Save',
}: {
  onSave: () => void;
  headerTitle: string;
  className?: string;
  navigateTo?: string;
  isLoading?: boolean;
  isDisabled?: boolean;
  showButton?: boolean;
  hideBackBtn?: boolean;
  buttonLable?: string;
}) => {
  const navigation = useNavigate();
  return (
    <div className={cn('flex flex-col gap-4', className)}>
      {/* {/ Title and Icon Section /} */}
      <div className="flex justify-between items-center">
        <div className="flex gap-x-4 items-center">
          {!hideBackBtn && (
            <IconButton onClick={() => navigation(navigateTo || '/customers')}>
              <CheveronLeft />
            </IconButton>
          )}
          <h1 className="text-2xl font-semibold">{headerTitle}</h1>
        </div>
        {showButton && (
          <AppButton
            label={buttonLable}
            onClick={onSave}
            isLoading={isLoading}
            disabled={isDisabled}
          />
        )}
      </div>
      <Separator
        orientation="horizontal"
        className="h-[1px] bg-border-Default"
      />
    </div>
  );
};

export default Header;
