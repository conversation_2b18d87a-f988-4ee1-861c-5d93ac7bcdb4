import AppSpinner from '@/components/common/app-spinner';
import AppTabs from '@/components/common/app-tabs';
import { UseToast } from '@/components/ui/toast/ToastContainer';
import { customerOtherInfoTabsList } from '@/constants/customer-constants';
import { ROUTES } from '@/constants/routes-constants';
import { convertToFloat, getQueryParam, updateQueryParam } from '@/lib/utils';
import {
  useCreateCustomerMutation,
  useGetCustomerByIdQuery,
  useUpdateCustomerMutation,
} from '@/redux/features/customers/customer.api';
import { useUpdateSubTypesMutation } from '@/redux/features/sub-types/sub-types.api';
import {
  setSelectedSubTypes,
  subTypesList,
} from '@/redux/features/sub-types/subTypesSlice';
import { RootState } from '@/redux/store';
import { FormData, InvoiceDeliveryMethod } from '@/types/customer.types';
import { useEffect, useMemo, useState } from 'react';
import { Form<PERSON>rov<PERSON>, SubmitHandler, useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import CustomerInformation from './customer-information';
import FilesNotesTabs from './files-notes-tabs';
import Header from './header';
import { useGetUserDefaultStoreQuery } from '@/redux/features/store/store.api';

const NewCustomer = () => {
  const tab = getQueryParam('tab');
  const { customerId } = useParams<{ customerId?: string }>();
  const profile = useSelector((state: RootState) => state.loginProfile.profile);
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState(tab || 'contact-info');
  const subTypesData = useSelector(
    (state: RootState) => state.subTypes.subTypesData
  );
  const dispatch = useDispatch();
  const selectedSubTypes = useSelector(
    (state: RootState) => state.subTypes.selectedSubTypes
  );
  const toast = UseToast();
  const { data: storeLocations } = useGetUserDefaultStoreQuery(undefined, {
    refetchOnMountOrArgChange: true,
  });

  const [createCustomer, { isLoading: createCustomerLoader }] =
    useCreateCustomerMutation();

  const [updateCustomer, { isLoading: updateCustomerLoader }] =
    useUpdateCustomerMutation();
  const [updateSubTypes] = useUpdateSubTypesMutation();

  const { data: customerData, isFetching } = useGetCustomerByIdQuery(
    customerId ?? '',
    {
      skip: !customerId,
      refetchOnMountOrArgChange: true,
    }
  );
  const defaultValues = useMemo(() => {
    const dataValue = customerData?.data;
    if (!dataValue) {
      return {
        country: storeLocations?.data?.countryName ?? 'USA',
        customer_id: 0,
        corporate: true,
        pricingoninvoice: true,
        dropship: true,
        defconvfeeflag: true,
        displayalert: true,
        onlinepmts: true,
        autoemails: true,
        isactive: 'A',
        defstorelocationno: profile.defaultLocationId
          ? Number(profile.defaultLocationId)
          : null,
        invoiceDelivery: InvoiceDeliveryMethod.Email,
        statementDelivery: InvoiceDeliveryMethod.Email,
        country_id: storeLocations?.data?.countryId,
        state: storeLocations?.data?.stateCode,
      };
    }

    return {
      ...dataValue,
      country: dataValue?.country ?? 'USA',
      customer_id: dataValue?.customer_id ?? 0,
      corporate: dataValue?.corporate ?? true,
      pricingoninvoice: dataValue?.pricingoninvoice ?? true,
      dropship: dataValue?.dropship ?? true,
      defconvfeeflag: dataValue?.defconvfeeflag ?? true,
      displayalert: dataValue?.displayalert ?? true,
      onlinepmts: dataValue?.onlinepmts ?? true,
      autoemails: dataValue?.autoemails ?? true,
      customerType: dataValue?.custtype_id,
      contact: dataValue?.contact ?? '',
      tel1: dataValue?.tel1 ?? '',
      tel2: dataValue?.tel2 ?? '',
      damwaiv: dataValue?.damwaiv ? 'true' : 'false',
      isactive: dataValue?.isactive ? 'A' : 'I',
      defdelchg: convertToFloat({ value: dataValue?.defdelchg }),
      discountpercent: dataValue?.discountpercent
        ? `${dataValue?.discountpercent.toFixed(2)}%`
        : '',
      referralcommpct: dataValue?.referralcommpct
        ? `${dataValue?.referralcommpct.toFixed(2)}%`
        : '',
      damwaivpct: dataValue?.damwaivpct
        ? `${dataValue?.damwaivpct.toFixed(2)}%`
        : '',
      fuelchgpct: dataValue?.fuelchgpct
        ? `${dataValue?.fuelchgpct.toFixed(2)}%`
        : '',
      invoicecomm: dataValue?.invoicecomm ?? InvoiceDeliveryMethod.Email,
      statementcomm: dataValue?.statementcomm ?? InvoiceDeliveryMethod.Email,
      defstorelocation: dataValue?.defstorelocation,
      defstorelocationno: dataValue?.defstorelocationno,
      telfax: dataValue?.telfax ?? '',
    };
  }, [
    customerData?.data,
    profile.defaultLocationId,
    storeLocations?.data?.countryId,
    storeLocations?.data?.countryName,
    storeLocations?.data?.stateCode,
  ]);
  const form = useForm<FormData>({
    defaultValues,
    mode: 'onChange',
  });

  useEffect(() => {
    if (defaultValues) {
      form?.reset(defaultValues);
    }
  }, [defaultValues, form]);

  const handleSave = async (data: FormData) => {
    try {
      const payload = {
        first_name: data.first_name,
        custtype_id: parseInt(data.customerType || '0'),
        isactive: data?.isactive?.toString() === 'A',
        last_name: data.last_name,
        address1: data.address1,
        address2: data.address2,
        city: data.city,
        state: data.state,
        zipcode: data.zipcode?.replace(' ', ''),
        country: data.country,
        defstorelocationno: Number(data.defstorelocationno),
      };
      await createCustomer(payload)
        .unwrap()
        .then((response) => {
          toast.success(response?.message);
          navigate(
            `/customers/edit-customer/${response?.data?.customer_id}?tab=contact-info`
          );
        });
    } catch (error) {
      // toast.error('Failed to create customer');
    }
  };

  const handleUpdate = async (data: FormData) => {
    try {
      await updateCustomer({
        customerId: data?.customer_id,
        customerData: {
          ...data,
          zipcode: data?.zipcode?.replace(' ', ''),
          defdelchg: data?.defdelchg || null,
          custtype_id: parseInt(data.customerType || '0'),
          isactive: data?.isactive?.toString() === 'A',
          damwaiv: data.damwaiv === 'true',
          defstorelocationno: Number(data.defstorelocationno),
          discountpercent: data.discountpercent
            ? Number(
                data.discountpercent
                  .toString()
                  .replace(/%/g, '')
                  .replace(/_/g, '0')
              )
            : null,
          fuelchgpct: data.fuelchgpct
            ? Number(
                data.fuelchgpct.toString().replace(/%/g, '').replace(/_/g, '0')
              )
            : null,
          referralcommpct: data.referralcommpct
            ? Number(
                data.referralcommpct
                  .toString()
                  .replace(/%/g, '')
                  .replace(/_/g, '0')
              )
            : null,
          damwaivpct: data.damwaivpct
            ? Number(
                data.damwaivpct.toString().replace(/%/g, '').replace(/_/g, '0')
              )
            : null,
        },
      })
        .unwrap()
        .then((response) => {
          toast.success(response?.message);
        });
    } catch (error) {
      // toast.error('Failed to update customer');
    }
  };

  const handleSaveSubTypes = async () => {
    const updatedSubTypes = subTypesData.map((subType) => {
      // Check if the subType id is in the selectedSubTypes
      const isSelected = selectedSubTypes.some(
        (selected) => selected.custTypeId === subType.custTypeId
      );

      // If it's not in selectedSubTypes, set isSelected to false
      return {
        custTypeId: subType.custTypeId,
        isSelected: isSelected ? true : false, // Ensure `isSelected` is set to `false` for unselected items
      };
    });

    await updateSubTypes({
      id: Number(customerId),
      body: updatedSubTypes,
    }).unwrap();
    dispatch(setSelectedSubTypes([]));
    dispatch(subTypesList([]));
  };

  const onSubmit: SubmitHandler<FormData> = async (data) => {
    if (customerId) {
      handleUpdate(data);
    } else {
      handleSave(data);
    }
  };

  const handleTabClick = (value: string) => {
    setActiveTab(value);
    updateQueryParam(value, 'tab');
  };

  if (isFetching) {
    return <AppSpinner />;
  }

  return (
    <FormProvider {...form}>
      <div className="h-[1800px]">
        <Header
          navigateTo={ROUTES.CUSTOMERS}
          onSave={() => {
            form.handleSubmit(onSubmit)();
            if (customerId) {
              handleSaveSubTypes();
            }
          }}
          headerTitle={customerId ? 'Edit Customer' : 'New Customer'}
          className="sticky top-16 p-4 z-[20] bg-white"
        />

        <div className="grid grid-cols-12 gap-4 p-4">
          <CustomerInformation />
          <FilesNotesTabs />

          {customerId && (
            <div className="col-span-12 border p-8 rounded-lg bg-white">
              <AppTabs
                className="w-full"
                activeTab={activeTab}
                handleTabClick={handleTabClick}
                tabs={customerOtherInfoTabsList}
                header={
                  <div className=" min-w-[200px] h-full border-b-2">
                    <h3 className="text-xl text-text-Default">Other Info</h3>
                  </div>
                }
              />
            </div>
          )}
        </div>
      </div>
      <AppSpinner
        overlay
        isLoading={isFetching || createCustomerLoader || updateCustomerLoader}
      />
    </FormProvider>
  );
};

export default NewCustomer;
