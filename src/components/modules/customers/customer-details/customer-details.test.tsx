import { render } from '@testing-library/react';
import { describe, it, vi } from 'vitest';
import Customers from '.';

vi.mock('@/redux/features/customers/customer.api', () => ({
  useDeleteCustomerMutation: vi
    .fn()
    .mockReturnValue([vi.fn(), { isLoading: false }]),
  useGetCustomerColumsQuery: vi
    .fn()
    .mockReturnValue([vi.fn(), { isLoading: false }]),
  useUpdateCustomerColumsMutation: vi
    .fn()
    .mockReturnValue([vi.fn(), { isLoading: false }]),
}));

vi.mock('react-redux', () => ({
  useDispatch: vi.fn(),
  useSelector: vi.fn(),
}));

vi.mock('react-router-dom', () => ({
  useNavigate: vi.fn(),
}));

// Mock AppButton
vi.mock('@/components/common/app-button', () => ({
  default: ({ children, ...props }: any) => (
    <button {...props}>{children}</button>
  ),
}));

// Mock AppConfirmationModal
vi.mock('@/components/common/app-confirmation-modal', () => ({
  default: ({ isOpen, onClose, onConfirm }: any) =>
    isOpen ? (
      <div>
        <p>Are you sure?</p>
        <button onClick={onConfirm}>Confirm</button>
        <button onClick={onClose}>Cancel</button>
      </div>
    ) : null,
}));

// Mock API calls
vi.mock('@/lib/api', () => ({
  deleteCustomer: vi.fn(),
}));

describe('Customers Component', () => {
  it('renders the customers component correctly', () => {
    // Mock AppDataTable
    vi.mock('@/components/common/app-data-table', () => ({
      default: ({
        data,
      }: {
        data: { id: string; name: string; email: string }[];
      }) => (
        <table>
          <tbody>
            {data?.map((row) => (
              <tr key={row.id}>
                <td>{row.name}</td>
                <td>{row.email}</td>
              </tr>
            ))}
          </tbody>
        </table>
      ),
    }));

    render(<Customers />);
  });
});
