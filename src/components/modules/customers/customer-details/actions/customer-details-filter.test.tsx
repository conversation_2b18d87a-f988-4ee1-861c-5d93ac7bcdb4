import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { vi, describe, beforeEach, it, expect } from 'vitest';
import Filter from './Filter';
import customerReducer from '@/redux/features/customers/customerSlice';
import { useGetStoreLocationsQuery } from '@/redux/features/store/store.api';
import { useGetListItemsMutation } from '@/redux/features/list/category/list.api';

// Mock Redux store and hooks
vi.mock('@/redux/features/store/store.api', () => ({
  useGetStoreLocationsQuery: vi.fn(),
}));
vi.mock('@/redux/features/list/category/list.api', () => ({
  useGetListItemsMutation: vi.fn(),
}));

describe('customer details filter component', () => {
  const mockStore = configureStore({
    reducer: {
      customer: customerReducer,
    },
  });

  const renderComponent = () =>
    render(
      <Provider store={mockStore}>
        <Filter setIsFilterOpen={vi.fn()} />
      </Provider>
    );

  beforeEach(() => {
    (useGetStoreLocationsQuery as any).mockReturnValue({
      data: { data: [{ location: 'Store 1' }, { location: 'Store 2' }] },
    });

    (useGetListItemsMutation as any).mockReturnValue([
      vi.fn(),
      { data: { data: [{ description: 'Type A', custtype_id: '1' }] } },
    ]);
  });

  it('should render the Filter component', async () => {
    renderComponent();

    expect(screen.getByText(/Filters/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Customer Name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Customer Type/i)).toBeInTheDocument();
  });

  it('should handle form submission of customer details filter', async () => {
    const mockSetIsFilterOpen = vi.fn();
    render(
      <Provider store={mockStore}>
        <Filter setIsFilterOpen={mockSetIsFilterOpen} />
      </Provider>
    );

    // Fill in the form
    fireEvent.change(screen.getByLabelText(/Customer Name/i), {
      target: { value: 'John Doe' },
    });

    // Submit the form
    fireEvent.click(screen.getByText('Apply'));

    // Wait for any potential state updates
    await waitFor(() => {
      expect(mockSetIsFilterOpen).toHaveBeenCalledTimes(1);
      expect(mockSetIsFilterOpen).toHaveBeenCalledWith(false);
    });
  });

  it('should clear the form', () => {
    const mockSetIsFilterOpen = vi.fn();
    render(
      <Provider store={mockStore}>
        <Filter setIsFilterOpen={mockSetIsFilterOpen} />
      </Provider>
    );

    // Fill in the form
    fireEvent.change(screen.getByLabelText(/Customer Name/i), {
      target: { value: 'John Doe' },
    });

    // Clear the form
    fireEvent.click(screen.getByText(/Clear/i));

    // Assertions for clearing the form
    expect(screen.getByLabelText(/Customer Name/i)).toHaveValue('');
    expect(mockSetIsFilterOpen).toHaveBeenCalledWith(false);
  });
});
