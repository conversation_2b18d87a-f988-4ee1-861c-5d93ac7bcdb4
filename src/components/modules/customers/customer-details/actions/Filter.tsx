import AppButton from '@/components/common/app-button';
import Input<PERSON>ield from '@/components/forms/input-field';
import PhoneInputWidget from '@/components/forms/phone-input-mask';
import SelectDropDown from '@/components/forms/select-dropdown';
import { Separator } from '@/components/ui/separator';
import { CUSTOMER_TYPE_API_ROUTES } from '@/constants/api-constants';
import { generateLabelValuePairs, searchPayload } from '@/lib/utils';
import { useGetStoreLocationsQuery } from '@/redux/features/store/store.api';
import {
  clearAllFilters,
  setFilter,
  updateFormValues,
} from '@/redux/features/customers/customerSlice';
import { useGetListItemsMutation } from '@/redux/features/list/category/list.api';
import { RootState } from '@/redux/store';
import { memo, useCallback, useEffect } from 'react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';

// Define the interface for filter data
interface FilterFormValues {
  name: string;
  customerType: string;
  address1: string;
  city: string;
  state: string;
  phone: string;
  defstorelocation: string;
  isactive: string;
  zipCode: string;
  tel1: string;
}

const defaultValues: FilterFormValues = {
  name: '',
  customerType: '',
  address1: '',
  city: '',
  state: '',
  phone: '',
  defstorelocation: '',
  isactive: '',
  zipCode: '',
  tel1: '+1',
};

const customerStatus = [
  { label: 'Active', value: 'true' },
  { label: 'Inactive', value: 'false' },
];

interface FilterProps {
  setIsFilterOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const Filter: React.FC<FilterProps> = ({ setIsFilterOpen }) => {
  const formValuesFromRedux = useSelector(
    (state: RootState) => state.customer.formValues
  );
  const dispatch = useDispatch();

  const { data: storeLocations } = useGetStoreLocationsQuery();
  const [getList, { data: customerTypeData, isLoading }] =
    useGetListItemsMutation();

  // Initialize form with values from Redux
  const form = useForm<FilterFormValues>({
    defaultValues: formValuesFromRedux,
    mode: 'onChange',
  });
  const customerTypeList = generateLabelValuePairs({
    data: customerTypeData?.data,
    labelKey: 'description',
    valueKey: 'custtype_id',
  }).filter((item) => item.value !== '');

  const fetchListItems = useCallback(() => {
    getList({
      url: CUSTOMER_TYPE_API_ROUTES.ALL,
      data: searchPayload({
        pageNumber: -1,
        pageSize: -1,
        sortBy: '',
        sortAscending: true,
        filters: [],
      }),
    });
  }, [getList]);

  useEffect(() => {
    fetchListItems();
  }, [fetchListItems]);

  // Watch form changes and update Redux state
  useEffect(() => {
    const subscription = form.watch((values) => {
      dispatch(updateFormValues(values));
    });

    return () => subscription.unsubscribe();
  }, [form, dispatch]);

  const storeLocationList = generateLabelValuePairs({
    data: storeLocations?.data,
    labelKey: 'location',
    valueKey: 'location',
  }); // Filter out empty values
  const onSubmit: SubmitHandler<FilterFormValues> = (data) => {
    const selectedCustomerType = customerTypeList.find(
      (item) => item.value === data.customerType
    )?.label;

    const newFilterData: any[] = [
      {
        label: 'Name',
        value: data.name,
        name: 'first_name',
        tagValue: data.name,
        operator: 'Contains',
      },
      {
        label: 'Customer Type',
        value: data.customerType?.toString(),
        name: 'custtype_id',
        tagValue: selectedCustomerType ?? '',
        operator: 'Equals',
      },
      {
        label: 'Active Customers',
        value: data.isactive,
        name: 'isactive',
        tagValue:
          data.isactive === 'true'
            ? 'Yes'
            : data.isactive === 'false'
              ? 'No'
              : '',
        operator: 'Equals',
      },
      {
        label: 'Address',
        value: data.address1,
        name: 'address1',
        tagValue: data.address1,
        operator: 'Contains',
      },
      {
        label: 'City',
        value: data.city,
        name: 'city',
        tagValue: data.city,
        operator: 'Contains',
      },
      {
        label: 'State',
        value: data.state,
        name: 'state',
        tagValue: data.state,
        operator: 'Contains',
      },
      {
        label: 'Zip Code',
        value: data.zipCode,
        name: 'zipCode',
        tagValue: data.zipCode,
        operator: 'Contains',
      },
      {
        label: 'Store Location',
        value: data.defstorelocation,
        name: 'defstorelocation',
        tagValue: data.defstorelocation,
        operator: 'Equals',
      },
      {
        label: 'Phone',
        value: data.tel1?.slice(2),
        name: 'tel1',
        tagValue: data.tel1?.slice(2),
        operator: 'Contains',
      },
      // {
      //   label: 'Address 2',
      //   value: data.address2,
      //   name: 'address2',
      //   tagValue: data.address2,
      //   operator: 'Contains',
      // },
    ].filter((item) => item.value);

    dispatch(setFilter(newFilterData));
    setIsFilterOpen(false);
  };

  const handleClear = () => {
    form.reset(defaultValues); // Reset form to default values
    dispatch(clearAllFilters()); // Clear all filters in Redux
    setIsFilterOpen(false); // Close the filter form
  };

  // Track if form values are different from the default values
  const isFormModified =
    JSON.stringify(form.getValues()) !== JSON.stringify(defaultValues);

  return (
    <>
      <div className="px-3">
        <div className="text-normal py-2  font-semibold">Filters</div>
        <Separator
          orientation="horizontal"
          className="h-[1px] bg-border-Default mb-2"
        />
      </div>
      <div className="pl-3 flex flex-col gap-2 pb-2">
        <InputField
          form={form}
          name="name"
          label="Customer Name"
          placeholder="Eg: John Doe"
        />
        <div className="flex flex-row items-center justify-between gap-4">
          <div className="w-full">
            <SelectDropDown
              form={form}
              name="customerType"
              label="Customer Type"
              isLoading={isLoading}
              optionsList={customerTypeList}
              placeholder="Select Customer Type"
            />
          </div>
          <div className="w-full">
            <SelectDropDown
              form={form}
              name="isactive"
              label="Customer Status"
              optionsList={customerStatus}
              placeholder="Customer Status"
            />
          </div>
        </div>

        <SelectDropDown
          form={form}
          name="defstorelocation"
          label="Store Location"
          placeholder="Select Store Location"
          optionsList={storeLocationList}
        />
        <InputField
          form={form}
          name="address1"
          label="Address"
          placeholder="Eg: 001 North Ave"
        />

        <InputField
          form={form}
          name="city"
          label="City"
          placeholder="Eg: Orlando"
        />
        <div className="flex flex-row items-center justify-between gap-4">
          <div className="w-full">
            <InputField
              form={form}
              name="state"
              label="State"
              placeholder="Eg: FL"
            />
          </div>
          <div className="w-full">
            <InputField
              form={form}
              name="zipCode"
              label="Zip Code"
              placeholder="Eg: 12345"
              type="number"
              maxLength={5}
            />
          </div>
        </div>

        <PhoneInputWidget form={form} name="tel1" label="Phone No." />
      </div>
      <div className="w-full h-full flex justify-between gap-3 px-4 py-1 sticky bottom-0 bg-white z-30">
        <AppButton
          className="w-full"
          onClick={form.handleSubmit(onSubmit)}
          label="Apply"
          disabled={!isFormModified}
        />
        <AppButton
          className="w-full"
          label="Clear"
          variant="neutral"
          onClick={handleClear}
          disabled={!isFormModified} // Disable button if no changes
        />
      </div>
    </>
  );
};

export default memo(Filter);
