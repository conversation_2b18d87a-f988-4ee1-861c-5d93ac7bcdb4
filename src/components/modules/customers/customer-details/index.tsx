import AddIcon from '@/assets/icons/AddIcon';
import CreditCardPlus from '@/assets/icons/CreditCardPlus';
import DotIcon from '@/assets/icons/DotIcon';
import EditIcon from '@/assets/icons/EditIcon';
import EyeIcon from '@/assets/icons/EyeIcon';
import MailIcon from '@/assets/icons/MailIcon';
import PackagePlus from '@/assets/icons/PackagePlus';
import Tool from '@/assets/icons/Tool';
import TrashIcon from '@/assets/icons/TrashIcon';
import UserPlusIcon from '@/assets/icons/UserPlusIcon';
import AppButton from '@/components/common/app-button';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import AppDataTable from '@/components/common/app-data-table';
import AppSpinner from '@/components/common/app-spinner';
import ColumnReOrdering from '@/components/common/column-reording';
import Filter from '@/components/modules/customers/customer-details/actions/Filter';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuPortal,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { CUSTOMER_API_ROUTES } from '@/constants/api-constants';
import { ROUTES } from '@/constants/routes-constants';
import { formatPhoneNumber } from '@/lib/utils';
import {
  useDeleteCustomerMutation,
  useGetCustomerColumsQuery,
  useUpdateCustomerColumsMutation,
} from '@/redux/features/customers/customer.api';
import { clearFilter } from '@/redux/features/customers/customerSlice';
import { RootState } from '@/redux/store';
import { CustomerDetailTypes } from '@/types/customer.types';
import { ColumnDef } from '@tanstack/react-table';
import { useCallback, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useNavigate } from 'react-router-dom';

interface CustomerToDeleteType {
  id: number | null;
  name: string;
}

const Customers = () => {
  const [openDeleteDialog, setOpenDeleteDialog] = useState<boolean>(false);
  const [customerToDelete, setCustomerToDelete] =
    useState<CustomerToDeleteType>({
      id: null,
      name: '',
    });

  const filter = useSelector((state: RootState) => state.customer.filters);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [isFilterOpen, setIsFilterOpen] = useState<boolean>(false);
  const [refreshList, setRefreshList] = useState<boolean>(false);
  const [openColumnOrdering, setOpenColumnOrdering] = useState<boolean>(false);

  // get reorganize columns
  const { data, isLoading } = useGetCustomerColumsQuery();
  // update reorganize columns
  const [updateCustomerColums, { isLoading: updateColumIsLoading }] =
    useUpdateCustomerColumsMutation();

  // delete customer
  const [deleteCustomer, { isLoading: isDeleteLoading }] =
    useDeleteCustomerMutation();

  const componentMap: any = useMemo(
    () => ({
      // StatusBadge: ({ value }: any) => <StatusBadge status={value} />,
      // LabelDollar: ({ value }: any) => convertToFloat({ value, prefix: '$' }),
      Phone: ({ value }: any) => formatPhoneNumber(value),
    }),
    []
  );

  const onOpenChange = useCallback(() => {
    setOpenDeleteDialog((prevState) => !prevState);
  }, [setOpenDeleteDialog]);

  const handleDeleteClick = useCallback(
    (customerId: number, name: string) => {
      setCustomerToDelete({ id: customerId, name: name });
      onOpenChange();
    },
    [onOpenChange]
  );

  const handleDeleteCustomer = async () => {
    await deleteCustomer(customerToDelete.id as number)
      .unwrap()
      .then(() => {
        setRefreshList(true);
        setCustomerToDelete({ id: null, name: '' });
      })
      .finally(() => {
        onOpenChange();
        setTimeout(() => {
          setRefreshList(false);
        }, 500);
      });
  };

  const handleNavigateNewOrder = useCallback(
    (id: number) =>
      navigate(`${ROUTES.ADD_ORDERS}?custId=${id}&tab=information`),
    [navigate]
  );

  const handleSearchOrder = useCallback(
    (name?: string) => navigate(`${ROUTES.ORDERS}?search=${name}`),
    [navigate]
  );

  const tableColumns = useMemo(() => {
    return data?.data;
  }, [data?.data]);

  const handleOrderingColumn = useCallback(
    async (formData: any) => {
      const updatedColumns = tableColumns.map((item: any) => ({
        ...item,
        enabled: formData[item.accessorKey],
      }));

      await updateCustomerColums(updatedColumns).unwrap();
      setOpenColumnOrdering(false);
    },
    [updateCustomerColums, tableColumns]
  );

  const actionColumn: ColumnDef<CustomerDetailTypes> = useMemo(
    () => ({
      id: 'action',
      size: 110,
      header: 'Actions',
      cell: ({ row }) => (
        <div className="flex flex-row gap-x-4 justify-center items-center ">
          <Link
            to={`edit-customer/${row.original.customer_id}?tab=contact-info`}
            ria-label={`Edit ${row.original.first_name} ${row.original.last_name}`}
            className="flex items-center justify-center gap-2 border border-text-tertiary rounded-md px-3 py-1"
          >
            <EditIcon /> Edit details
          </Link>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="h-8 w-8 p-0"
                aria-label="Open actions menu"
              >
                <DotIcon />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              align="end"
              className="w-[300px] p-2 rounded-lg z-10"
            >
              <DropdownMenuItem className="pl-4 pr-4 pt-3 pb-3 text-base text-text-Default">
                <div className="flex flex-row gap-3 items-center">
                  <MailIcon />
                  <span>Send Email</span>
                </div>
              </DropdownMenuItem>

              <DropdownMenuSub>
                <DropdownMenuSubTrigger className="pl-4 pr-4 pt-3 pb-3 text-base text-text-Default">
                  <div className="flex flex-row gap-3 items-center">
                    <AddIcon />
                    <span>New</span>
                  </div>
                </DropdownMenuSubTrigger>
                <DropdownMenuPortal>
                  <DropdownMenuSubContent
                    sideOffset={12}
                    className="w-[300px] h-[154px] p-2 text-base text-text-Default"
                  >
                    <DropdownMenuItem
                      onClick={() =>
                        handleNavigateNewOrder(row.original.customer_id)
                      }
                      className="pl-4 pr-4 pt-3 pb-3 text-base text-text-Default"
                    >
                      <div className="flex flex-row gap-3 items-center">
                        <PackagePlus />
                        <span>New Order</span>
                      </div>
                    </DropdownMenuItem>
                    <DropdownMenuItem className="pl-4 pr-4 pt-3 pb-3 text-base text-text-Default">
                      <div className="flex flex-row gap-3 items-center">
                        <CreditCardPlus />
                        <span>New Payment</span>
                      </div>
                    </DropdownMenuItem>
                    <DropdownMenuItem className="pl-4 pr-4 pt-3 pb-3 text-base text-text-Default">
                      <div className="flex flex-row gap-3 items-center">
                        <Tool /> <span>New Adjustment</span>
                      </div>
                    </DropdownMenuItem>
                  </DropdownMenuSubContent>
                </DropdownMenuPortal>
              </DropdownMenuSub>
              <DropdownMenuItem
                onClick={() => handleSearchOrder(row.original.full_name)}
                className="pl-4 pr-4 pt-3 pb-3 text-base text-text-Default"
              >
                <div className="flex flex-row gap-3 items-center">
                  <EyeIcon />
                  <span>View Orders</span>
                </div>
              </DropdownMenuItem>

              <DropdownMenuItem
                onClick={() =>
                  handleDeleteClick(
                    row.original.customer_id,
                    `${row.original?.first_name ?? ''} ${row?.original?.last_name ?? ''}`
                  )
                }
                className="pl-4 pr-4 pt-3 pb-3 text-base text-text-danger"
                aria-label={`Delete ${row.original.first_name} ${row.original.last_name}`}
              >
                <div className="flex flex-row gap-3 items-center">
                  <TrashIcon />
                  <span>Delete</span>
                </div>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      ),
    }),
    [handleDeleteClick, handleNavigateNewOrder, handleSearchOrder]
  );

  const memoizedColumns = useMemo(() => {
    if (!tableColumns?.length) return [];

    const formattedColumns = tableColumns
      .filter((column: any) => column.enabled) // Ensure only enabled columns are included
      .map((column: any) => ({
        accessorKey: column.accessorKey,
        header: column.header,
        enableSorting: column.enableSorting || false,
        size: column.size || 200, // Default size if not provided
        cell: ({ row }: any) => {
          const Component = componentMap[column.component]; // Get the component based on the key
          return Component ? (
            <Component value={row?.original?.[column.accessorKey]} />
          ) : (
            row?.original?.[column.accessorKey]
          );
        },
      }));

    return [
      {
        accessorKey: 'ordering',
        header: () => (
          <ColumnReOrdering
            isOpen={openColumnOrdering}
            handleOrderingColumn={handleOrderingColumn}
            tableColumns={tableColumns}
            setOpenColumnOrdering={setOpenColumnOrdering}
          />
        ),
        size: 50,
      },
      ...formattedColumns,
      actionColumn,
    ];
  }, [
    tableColumns,
    actionColumn,
    componentMap,
    openColumnOrdering,
    handleOrderingColumn,
  ]);

  const handleClearFilter = useCallback(
    (key: string) => {
      dispatch(clearFilter(key));
      setIsFilterOpen(false);
    },
    [dispatch]
  );

  const handleNewCustomer = useCallback(
    () => navigate(ROUTES.ADD_NEW_CUSTOMER),
    [navigate]
  );

  const CustomToolbar = (
    <AppButton
      icon={UserPlusIcon}
      className="w-[170px]"
      label="New Customer"
      onClick={handleNewCustomer}
      aria-label="Create new customer"
    />
  );

  return (
    <div className="flex flex-col p-4 gap-6">
      <AppDataTable
        url={CUSTOMER_API_ROUTES.ALL}
        columns={memoizedColumns}
        enableSearch={true}
        searchKey="full_name"
        heading="Customer Details"
        enableFilter
        filter={filter}
        handleClearFilter={handleClearFilter}
        filterClassName="w-[473px]"
        filterContent={<Filter setIsFilterOpen={setIsFilterOpen} />}
        setIsFilterOpen={setIsFilterOpen}
        isFilterOpen={isFilterOpen}
        customToolBar={CustomToolbar}
        enablePagination={true}
        refreshList={refreshList}
        tableClassName="max-h-[580px] overflow-auto"
      />
      <AppConfirmationModal
        description={
          <div>
            Are you sure you want to delete{' '}
            <span className="font-semibold text-base text-text-Default">
              {customerToDelete.name}
            </span>
            &nbsp;?
          </div>
        }
        open={openDeleteDialog}
        onOpenChange={onOpenChange}
        handleCancel={onOpenChange}
        disabled={isDeleteLoading}
        isLoading={isDeleteLoading}
        handleSubmit={handleDeleteCustomer}
        aria-labelledby="delete-confirmation-modal"
      />

      <AppSpinner overlay isLoading={isLoading || updateColumIsLoading} />
    </div>
  );
};

export default Customers;
