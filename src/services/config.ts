export const CURRENT_ENVIRONMENT: string =
  process.env.NODE_ENV ?? 'development';

type VariableType = {
  API_BASE: string;
  AUTH_BASE: string;
};

type ConfigType = {
  [key: string]: VariableType;
};

// LOCAL- MAC- IP
// const AUTH_ENV = 'http://*************:5220/api/auth';
// const API_ENV = 'http://*************:5097/api';

// LOCAL - MANDAR IP
// const AUTH_ENV = 'http://*************:9090/api/auth';
// const API_ENV = 'http://*************:8080/api';

// RIO ENV
// const AUTH_ENV = 'http://*************:91/api/Auth';
// const API_ENV = 'http://*************:92/api';

// LOCAL - Akash S IP
// const AUTH_ENV = 'http://**************:5219/api/Auth';
// const API_ENV = 'http://**************:5096/api';

// LOCAL - VISHWAJEET IP
// const AUTH_ENV = 'https://localhost:44329/api/Auth';
// const API_ENV = 'https://localhost:44346/api';

// DEV ENV
const AUTH_ENV = 'https://ptrnew.ambrisk.com/api/Auth';
const API_ENV = 'https://ptrnew.ambrisk.com/api';

// QA ENV
// const AUTH_ENV = 'https://ptrnew.gametest.io/api/Auth';
// const API_ENV = 'https://ptrnew.gametest.io/api';

const BASE_URL = typeof window !== 'undefined' ? window?.location?.origin : '';
const PROD_AUTH_ENV = BASE_URL + '/api/Auth';
const PROD_API_ENV = BASE_URL + '/api';

const CONFIG: ConfigType = {
  test: {
    AUTH_BASE: AUTH_ENV,
    API_BASE: API_ENV,
  },
  development: {
    AUTH_BASE: AUTH_ENV,
    API_BASE: API_ENV,
  },
  production: {
    AUTH_BASE: PROD_AUTH_ENV,
    API_BASE: PROD_API_ENV,
  },
};

export const ENV_VARIABLES = CONFIG[CURRENT_ENVIRONMENT];
