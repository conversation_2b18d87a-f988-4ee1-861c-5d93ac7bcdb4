import {
  BaseQueryFn,
  <PERSON>tch<PERSON>rgs,
  FetchBaseQueryError,
  fetchBaseQuery,
} from '@reduxjs/toolkit/query';
import { ENV_VARIABLES } from './config';
import { UseToast } from '@/components/ui/toast/ToastContainer';
import {
  enhancedLogout,
  setLogoutTimer,
  setToastPending,
} from '@/redux/features/sessions/sessionSlice';
import { RootState } from '@/redux/store';

export const baseQuery = fetchBaseQuery({
  baseUrl: ENV_VARIABLES.API_BASE,
  prepareHeaders: (headers) => {
    const token =
      localStorage.getItem('accessToken') ||
      sessionStorage.getItem('accessToken');
    if (token) {
      headers.set('authorization', `Bearer ${token}`);
    }
    return headers;
  },
});

export const authQuery = fetchBaseQuery({
  baseUrl: ENV_VARIABLES.AUTH_BASE,
});

export const baseQueryWithReauth: BaseQueryFn<
  string | FetchArgs,
  any,
  FetchBaseQueryError
> = async (args, store, extraOptions) => {
  let result: any = await baseQuery(args, store, extraOptions);

  // Check if there's an error with the response
  const responseStatus = result.meta?.response?.status;
  const errorStatus = result.error?.status;
  const dataStatusCode = result.data?.statusCode;
  if (responseStatus === 401 || errorStatus === 401) {
    const state = store.getState() as RootState;

    if (!state.session.isToastPending) {
      showErrorMessage('Session expired. Logging out from the System.');
      store.dispatch(setToastPending(true));

      const timerId = window.setTimeout(() => {
        store.dispatch(enhancedLogout());
      }, 2000);

      store.dispatch(setLogoutTimer(timerId));
    }
    return { data: undefined };
  }

  if (
    (responseStatus !== 200 && errorStatus !== 200) ||
    (responseStatus === 200 && dataStatusCode !== 200)
  ) {
    const errorMessage =
      result.error?.data?.message ||
      result.data?.message ||
      result?.error?.data?.title ||
      'Something went wrong. Please contact administrator.';
    showErrorMessage(errorMessage);
  }

  const responseData = result.data as
    | { success: boolean; data: unknown; message: string }
    | undefined;
  if (!responseData || !responseData.success) {
    return {
      error: {
        status: errorStatus || dataStatusCode || 500,
        data: result.error?.data,
        message: result.error?.data?.message || result.data?.message,
      } as FetchBaseQueryError,
    };
  }
  return {
    data: responseData || undefined,
    meta: result.meta,
  };
};

const showErrorMessage = (message: string | undefined) => {
  if (message) {
    UseToast().error(message);
  }
};
