export const customerListData = [
  {
    id: 'cc001',
    name: 'Classic Catering',
    type: 'Cat<PERSON>',
    address: '3021 39th St',
    city: 'Orlando',
    state: 'FL',
    zipCode: '32839',
    phone: '(*************',
    notes: 'This is a notes',
  },
  {
    id: 'tc001',
    name: '<PERSON> Customer',
    type: 'Caterer',
    state: '<PERSON>',
  },
  {
    id: 'tm001',
    name: '<PERSON>',
    type: 'Cat<PERSON>',
    address: '711 Alba Dr',
    city: 'Orlando',
    state: 'FL',
    zipCode: '32804',
  },
  {
    id: 'bj001',
    name: '<PERSON><PERSON>, <PERSON>',
    type: 'Church',
    address: '3525 S Rosalind Ave',
    city: 'Orlando',
    state: 'FL',
    zipCode: '32806',
  },
  {
    id: 'cits001',
    name: 'Church In The Sun',
    type: 'Church',
    address: '4484 N John Young P<PERSON>wy',
    city: 'Orlando',
    state: 'FL',
    zipCode: '32804',
    phone: '(*************',
  },
  {
    id: 'fbo001',
    name: 'First Baptist Orlando',
    type: 'Church',
    address: '3000 S John <PERSON>wy',
    city: 'Orlando',
    state: 'FL',
    zipCode: '32805',
    phone: '(*************',
  },
  {
    id: 'ak001',
    name: 'Akshay',
    type: 'Individual',
    state: 'FL',
  },
];

// files and notes list
export const filesData = [
  {
    fileName: 'Need to place order. Send or',
    dateCreated: 'September 12, 2024',
    owner: 'Admin',
  },
  {
    fileName: 'Add reports for the order hist',
    dateCreated: 'April 1, 2024',
    owner: 'Admin',
  },
];

export const notesData = [
  {
    noteName: 'This is a small notes this will be used to make notes regarding',
    dateCreated: 'September 12, 2024',
    owner: 'Admin',
  },
  {
    noteName: 'This is a small notes this will be used to make notes regarding',
    dateCreated: 'April 1, 2024',
    owner: 'Admin',
  },
];

export const discountTableData = [
  {
    categoryId: 100,
    department: 'Tenting',
    category: 'Canopies',
    catdiscount: '10.00%',
  },
  {
    categoryId: 100,
    department: 'Tenting',
    category: 'Canopies',
  },
  {
    categoryId: 100,
    department: 'Tenting',
    category: 'Canopies',
  },
  {
    categoryId: 100,
    department: 'Tenting',
    category: 'Canopies',
  },
  {
    categoryId: 100,
    department: 'Tenting',
    category: 'Canopies',
  },
  {
    categoryId: 100,
    department: 'Tenting',
    category: 'Canopies',
  },
  {
    categoryId: 100,
    department: 'Tenting',
    category: 'Canopies',
  },
];

export const SubTypesData = [
  {
    customerType: 'Caterer',
  },
  {
    customerType: 'Church',
  },
  {
    customerType: 'Government',
  },
  {
    customerType: 'Indiviual',
  },
  {
    customerType: 'Government',
  },
  {
    customerType: 'Indiviual',
  },
  {
    customerType: 'Government',
  },
  {
    customerType: 'Indiviual',
  },
  {
    customerType: 'Government',
  },
  {
    customerType: 'Indiviual',
  },
];

export const customPriceData = [
  {
    itemId: 'BE15CG',
    category: 'Bar Equipment',
    description: 'Champagne Glasses',
    quantity: 86,
    cost: '$1.50',
    unitPrice: '$0.50',
  },
  {
    itemId: 'BB36',
    category: 'Baskets',
    description: 'Bread Basket: 9" x 6" x 2 1/2"',
    quantity: 276,
    cost: '$1.23',
    unitPrice: '$0.50',
  },
  {
    itemId: 'B9RD',
    category: 'Bowls',
    description: 'China Bowls',
    quantity: 426,
    cost: '$0.90',
    unitPrice: '$0.50',
  },
  {
    itemId: 'CA01WR',
    category: 'Canopies',
    description: 'Water resistant canopies',
    quantity: 50,
    cost: '$5.00',
    unitPrice: '$2.50',
  },
  {
    itemId: 'CH230V',
    category: 'Chairs',
    description: 'Bar Stools',
    quantity: 34,
    cost: '$0.00',
    unitPrice: '$7.50',
  },
];

// contatct info
export const contatctInfoData = [
  {
    phoneNumber: '(************)',
    type: 'Work Phone',
  },
];
