import { ROUTES } from '@/constants/routes-constants';

export const listData = [
  {
    listName: 'Bank Accounts',
    lastUpdated: 'September 12, 2024',
    updatedBy: 'Admin',
    to: ROUTES.BANK_ACCOUNTS,
  },
  {
    listName: 'Categories',
    lastUpdated: 'April 1, 2024',
    updatedBy: 'Admin',
    to: ROUTES.CATEGORIES,
  },
  {
    listName: 'Checklist Items',
    lastUpdated: 'March 25, 2024',
    updatedBy: 'Admin',
    to: ROUTES.CHECKLIST_ITEMS,
  },
  {
    listName: 'Customer Types',
    lastUpdated: 'February 18, 2024',
    updatedBy: 'Admin',
    to: ROUTES.CUSTOMER_TYPES,
  },
  {
    listName: 'Delivery Charges',
    lastUpdated: 'January 15, 2024',
    updatedBy: 'Admin',
    to: ROUTES.DELIVERY_CHARGES,
  },
  {
    listName: 'Delivery Locations',
    lastUpdated: 'January 15, 2024',
    updatedBy: 'Admin',
    to: ROUTES.DELIVERY_LOCATION,
  },
  {
    listName: 'Delivery Types',
    lastUpdated: 'November 25, 2023',
    updatedBy: 'Admin',
    to: ROUTES.DELIVERY_TYPES,
  },
  {
    listName: 'Departments',
    lastUpdated: 'October 10, 2023',
    updatedBy: 'Admin',
    to: ROUTES.DEPARTMENTS,
  },
  {
    listName: 'Drivers',
    lastUpdated: 'September 30, 2023',
    updatedBy: 'Admin',
    to: ROUTES.DRIVERS,
  },
  {
    listName: 'E-sign Fields',
    lastUpdated: 'August 20, 2023',
    updatedBy: 'Admin',
    to: ROUTES.E_SIGN_FIELDS,
  },
  {
    listName: 'Employees',
    lastUpdated: 'June 5, 2023',
    updatedBy: 'Admin',
    to: ROUTES.EMPLOYEES,
  },
  {
    listName: 'Equipment Types',
    lastUpdated: 'July 14, 2023',
    updatedBy: 'Admin',
    to: ROUTES.EQUIPMENT_TYPES,
  },
  {
    listName: 'Event Types',
    lastUpdated: 'May 10, 2023',
    updatedBy: 'Admin',
    to: ROUTES.EVENT_TYPES,
  },
  {
    listName: 'Local Zip Codes',
    lastUpdated: 'April 23, 2023',
    updatedBy: 'Admin',
    to: ROUTES.LOCAL_ZIP_CODES,
  },
  {
    listName: 'Packing List Departments',
    lastUpdated: 'March 10, 2023',
    updatedBy: 'Admin',
    to: ROUTES.PACKING_LIST_DEPARTMENTS,
  },
  {
    listName: 'Payment Terms',
    lastUpdated: 'February 2, 2023',
    updatedBy: 'Admin',
    to: ROUTES.PAYMENT_TERMS,
  },
  {
    listName: 'Payment Types',
    lastUpdated: 'January 15, 2023',
    updatedBy: 'Admin',
    to: ROUTES.PAYMENT_TYPES,
  },
  {
    listName: 'Quality Types',
    lastUpdated: 'December 1, 2022',
    updatedBy: 'Admin',
    to: ROUTES.QUALITY_TYPES,
  },
  {
    listName: 'Referral Types',
    lastUpdated: 'November 5, 2022',
    updatedBy: 'Admin',
    to: ROUTES.REFERRAL_TYPES,
  },
  {
    listName: 'Sales Tax Codes',
    lastUpdated: 'October 10, 2022',
    updatedBy: 'Admin',
    to: ROUTES.SALES_TAX_CODE,
  },
  {
    listName: 'Setup/Takedown',
    lastUpdated: 'September 25, 2022',
    updatedBy: 'Admin',
    to: ROUTES.SETUP_TAKEDOWN,
  },
  {
    listName: 'Shipping Companies',
    lastUpdated: 'August 20, 2022',
    updatedBy: 'Admin',
    to: ROUTES.SHIPPING_COMPANIES,
  },
  {
    listName: 'Surge Rates',
    lastUpdated: 'July 15, 2022',
    updatedBy: 'Admin',
    to: ROUTES.SURGE_RATES,
  },
  {
    listName: 'Trucks',
    lastUpdated: 'June 10, 2022',
    updatedBy: 'Admin',
    to: ROUTES.TRUCKS,
  },
  {
    listName: 'Truck Equipment List',
    lastUpdated: 'May 30, 2022',
    updatedBy: 'Admin',
    to: ROUTES.TRUCK_EQUIPMENT_LIST,
  },
];

export const rentTableData = [
  {
    rentDaysFrom: '3',
    rentDaysTo: '3',
    rateadjust: '122.22',
  },
];
