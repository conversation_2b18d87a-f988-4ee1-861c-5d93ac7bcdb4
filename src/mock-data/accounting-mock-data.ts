import { ROUTES } from '@/constants/routes-constants';

export const accountingData = [
  {
    accountingName: 'Adjustments',
    lastUpdated: 'January 8, 2025',
    updatedBy: 'Admin',
    to: ROUTES.ADJUSTMENTS,
  },
  {
    accountingName: 'Inquiry',
    lastUpdated: 'January 8, 2025',
    updatedBy: 'Admin',
    to: ROUTES.INQUIRY,
  },
  {
    accountingName: 'Payments',
    lastUpdated: 'January 8, 2025',
    updatedBy: 'Admin',
    to: ROUTES.ACCOUNTING_PAYMENTS,
  },
  {
    accountingName: 'Process Credit Cards',
    lastUpdated: 'January 8, 2025',
    updatedBy: 'Admin',
    to: ROUTES.PROCESS_CREDIT_CARDS,
  },
];

export const rentTableData = [
  {
    rentDaysFrom: '3',
    rentDaysTo: '3',
    rateadjust: '122.22',
  },
];

export const postingMockData = [
  {
    orderNumber: 715,
    dateOfUse: '08/31/2024',
    customer: '<PERSON>',
    total: '$251,685.54',
    checked: true,
  },
  {
    orderNumber: 932,
    dateOfUse: '10/09/2024',
    customer: 'Jack Black',
    total: '$176.10',
    checked: true,
  },
  {
    orderNumber: 961,
    dateOfUse: '01/01/2025',
    customer: 'ABCD',
    total: '$159.50',
    checked: true,
  },
  {
    orderNumber: 785,
    dateOfUse: '02/05/2025',
    customer: 'CASH SALE 3',
    total: '$143.00',
    checked: true,
  },
  {
    orderNumber: 960,
    dateOfUse: '02/07/2025',
    customer: 'Amway Center',
    total: '$0.00',
    checked: true,
  },
  {
    orderNumber: 669,
    dateOfUse: '02/25/2025',
    customer: 'Amway Center',
    total: '$580.00',
    checked: true,
  },
  {
    orderNumber: 1468,
    dateOfUse: '03/06/2025',
    customer: 'Amway Center',
    total: '$1.00',
    checked: true,
  },
  {
    orderNumber: 1618,
    dateOfUse: '03/17/2025',
    customer: 'Amway Center',
    total: '$308.42',
    checked: true,
  },
  {
    orderNumber: 1588,
    dateOfUse: '03/18/2025',
    customer: 'ABCD',
    total: '$10.00',
    checked: true,
  },
  {
    orderNumber: 1626,
    dateOfUse: '03/18/2025',
    customer: 'Amway Center',
    total: '$144.00',
    checked: true,
  },
  {
    orderNumber: 1629,
    dateOfUse: '03/18/2025',
    customer: 'Brian J',
    total: '$156.15',
    checked: true,
  },
  {
    orderNumber: 1657,
    dateOfUse: '03/18/2025',
    customer: 'Brian J',
    total: '$294.50',
    checked: true,
  },
  {
    orderNumber: 1635,
    dateOfUse: '03/19/2025',
    customer: 'Brian J',
    total: '$370,924.40',
    checked: true,
  },
  {
    orderNumber: 1645,
    dateOfUse: '03/19/2025',
    customer: 'Amway Center',
    total: '$258.00',
    checked: true,
  },
  {
    orderNumber: 1654,
    dateOfUse: '03/19/2025',
    customer: 'Brian J',
    total: '$235.80',
    checked: true,
  },
];
