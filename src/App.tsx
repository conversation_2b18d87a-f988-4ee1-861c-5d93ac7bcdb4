import { RouterProvider } from 'react-router-dom';
import { router } from './routes';

function App() {
  // const dispatch = useDispatch();

  // useEffect(() => {
  //   const handleStorageChange = (event: StorageEvent) => {
  //     if (event.key == 'accessToken' && !event.newValue) {
  //       // Token cleared from localStorage
  //       dispatch(logoutUser());
  //     }
  //   };

  //   // Add storage event listener
  //   window.addEventListener('storage', handleStorageChange);

  //   // Clean up the listener on component unmount
  //   return () => {
  //     window.removeEventListener('storage', handleStorageChange);
  //   };
  // }, [dispatch]);

  // console.log(import.meta.env.VITE_API_KEY); // 12345678

  return <RouterProvider router={router} />;
}

export default App;
