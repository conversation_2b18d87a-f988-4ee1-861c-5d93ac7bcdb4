import ForgotPasswordForm from '@/components/modules/auth/forgot-password';
import LoginLogoIcon from '@/assets/icons/LoginLogoIcon';

const ForgotPasswordPage = () => {
  return (
    <div className="min-h-screen bg-gradient-to-b from-[#55B9C0] to-[#5A2AC2]">
      <div className="container mx-auto flex min-h-[100vh] flex-col lg:flex-row justify-center items-center">
        {/* Logo Section */}
        <div className="flex w-full items-center justify-center p-4 lg:w-1.5/3">
          <div className="relative w-full max-w-lg">
            {/* Adjust logo size based on screen sizes */}
            <LoginLogoIcon className="h-auto w-2/3 mx-auto md:w-4/5 lg:w-full" />
          </div>
        </div>

        {/* Login Form Section */}
        <div className="flex w-full items-center justify-center p-4 lg:w-1.5/3">
          <ForgotPasswordForm />
        </div>
      </div>
    </div>
  );
};

export default ForgotPasswordPage;
