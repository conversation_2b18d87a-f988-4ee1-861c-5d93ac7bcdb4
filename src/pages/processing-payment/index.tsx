import { useEffect } from 'react';
import { useParams, useSearchParams } from 'react-router-dom'; // If using React Router
import './PaymentProcessingPage.css';
import { useProcessingPaymentSuccessMutation } from '@/redux/features/orders/order.api';
import { PaymentCallbackType } from '@/types/order.types';

type PaymentResponseType = {
  success: boolean;
  statusCode: number;
  message: string;
  data: string;
};

const PaymentProcessingPage = () => {
  const [searchParams] = useSearchParams();
  const { sendPaymentId } = useParams<{ sendPaymentId?: string }>();

  // process payment API
  const [processPayment] = useProcessingPaymentSuccessMutation();

  useEffect(() => {
    const sendPaymentConfirmation = async (data: PaymentCallbackType) => {
      const response = (await processPayment(
        data
      ).unwrap()) as unknown as PaymentResponseType;
      if (response && response?.data && response?.statusCode === 200) {
        setTimeout(() => {
          window.close();
        }, 1000);
      }
    };

    const params = Object.fromEntries(searchParams.entries());
    // Optional: Transform values if needed (e.g. decodeURIComponent, convert to number)
    const payload: PaymentCallbackType = {
      sentPaymentId: Number(sendPaymentId) || 0,
      processor: params.processor || '',
      status: params.status === 'true',
      resultmsg: params.resultmsg || '',
      transid: params.transid || '',
      authcode: params.authcode || '',
      card: params.card || '',
      expdate: params.expdate || '',
      amount: Number(params.amount) || 0,
      partial: params.partial === 'true',
      type: params.type || '',
      billingname: params.billingname || '',
    };

    // 👉 Call your API function
    sendPaymentConfirmation(payload);
  }, [processPayment, searchParams, sendPaymentId]);

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="max-w-5xl w-full text-center animate-fade-in">
        <div className="mb-6 flex justify-center">
          <svg
            className="animate-spin h-12 w-12 text-blue-600"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
              fill="none"
            ></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
            ></path>
          </svg>
        </div>

        <h2 className="text-2xl font-bold text-gray-800 mb-2">
          Processing Your Payment
        </h2>
        <p className="text-gray-600 mb-6">
          Please wait a moment while we confirm your transaction.
        </p>

        <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
          <div className="bg-gradient-to-r from-[#55B9C0] to-[#5A2AC2] h-full animate-loading  rounded-full"></div>
        </div>

        <p className="text-sm text-gray-400 mt-4">
          Do not refresh or close this window.
        </p>
      </div>
    </div>
  );
};

export default PaymentProcessingPage;
