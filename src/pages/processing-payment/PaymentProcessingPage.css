@keyframes loading {
    0% {
        transform: translateX(-100%);
    }

    100% {
        transform: translateX(100%);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }

    to {
        opacity: 1;
        transform: scale(1);
    }
}

.animate-loading {
    animation: loading 2.5s infinite linear;
    width: 50%;
}

.animate-fade-in {
    animation: fadeIn 0.5s ease-out both;
}