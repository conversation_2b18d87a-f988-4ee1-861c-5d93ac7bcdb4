import AppButton from '@/components/common/app-button';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import AppTableContextProvider from '@/components/common/app-data-table/AppTableContext';
import AppDataTable from '@/components/common/app-data-table/index';
import CustomDialog from '@/components/common/dialog';
import InputField from '@/components/forms/input-field';
import { Button } from '@/components/ui/button';
import { EMAIL_VALIDATION_RULEs } from '@/constants/auth-constants';
import { INVITE_CLIENT_API } from '@/constants/tenant-constants';
import { cn, dayJsFormat } from '@/lib/utils';
import { useAddNewItemMutation } from '@/redux/features/common-api/common.api';
import { InviteClient } from '@/types/list.types';
import { ColumnDef } from '@tanstack/react-table';
import { MailIcon } from 'lucide-react';
import { useCallback, useMemo, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, useForm, UseFormReturn } from 'react-hook-form';
import { toast } from 'sonner';

type ResendDialogTypes = {
  open: boolean;
  data: string | null;
};

const ClientInvite = () => {
  const [addNewItem, { isLoading: resendLoading }] = useAddNewItemMutation();
  const form: UseFormReturn<InviteClient> = useForm<InviteClient>({
    mode: 'onChange',
  });
  const [isInviteClient, setIsInviteClient] = useState<boolean>(false);
  const [refreshList, setRefreshList] = useState<boolean>(false);
  const [openResendDialog, setOpenResendDialog] = useState<ResendDialogTypes>({
    open: false,
    data: null,
  });
  const [inviteClient, { isLoading }] = useAddNewItemMutation();
  const { handleSubmit } = form;

  const onOpenChange = useCallback(() => {
    setOpenResendDialog({ open: false, data: null });
  }, [setOpenResendDialog]);

  const toggleResendDialog = useCallback((email: string) => {
    setOpenResendDialog({ open: true, data: email });
  }, []);

  // Resend email logic with error handling and feedback
  const handleResendEmail = useCallback(async () => {
    try {
      await addNewItem({
        url: INVITE_CLIENT_API.RESEND_INVITE,
        data: { email: openResendDialog.data ?? '' },
      })
        .unwrap()
        .then(() => {
          onOpenChange();
          setRefreshList(true);
        })
        .finally(() => setTimeout(() => setRefreshList(false), 500));
    } catch (error) {
      toast.error('Failed to resend invite');
    }
  }, [addNewItem, onOpenChange, openResendDialog.data]);

  // Toggle Invite Client modal
  const handleInviteClientModal = useCallback(() => {
    form.reset();
    setIsInviteClient((prevState) => !prevState);
  }, [form]);

  const CustomToolbar = (
    <>
      <AppButton
        icon={MailIcon}
        className="w-[170px]"
        label="Invite Client"
        onClick={handleInviteClientModal}
        aria-label="Invite a new client"
      />
    </>
  );

  const actionColumn: ColumnDef<InviteClient> = useMemo(
    () => ({
      id: 'action',
      size: 110,
      header: 'Actions',
      cell: ({ row }) => (
        <div className="flex flex-row gap-x-4 justify-start items-center ">
          <button
            onClick={() => toggleResendDialog(row?.original?.email)}
            className={cn('flex flex-row gap-3 items-center cursor-pointer')}
            title="Resend Invitation"
            aria-label={`Resend invitation to ${row?.original?.email}`}
          >
            <MailIcon className="w-5 h-5" />
          </button>
        </div>
      ),
    }),
    [toggleResendDialog]
  );

  const columns: ColumnDef<InviteClient>[] = useMemo(
    () => [
      {
        accessorKey: 'email',
        accessorFn: (row) => `${row?.email ?? ''}`,
        header: 'Tenant Admin Email',
        size: 150,
        cell: (info) => <div>{info.getValue() as string} </div>,
        enableSorting: true,
      },
      {
        accessorFn: (row) => `${row?.createdOn ?? ''}`,
        header: 'Invitation Sent Date',
        size: 150,
        cell: (info) => (
          <div>
            {dayJsFormat(info.getValue() as string, 'MM/DD/YYYY hh:mm A')}
          </div>
        ),
      },
      {
        accessorKey: 'status',
        accessorFn: (row) => {
          return (
            <Button
              className={cn(
                `h-8 w-20 px-4 py-0 rounded-full ${row?.status === 'Pending' ? 'bg-yellow-400 hover:bg-yellow-400' : ' bg-red-500 hover:bg-red-500'}`
              )}
              aria-label={`Status: ${row?.status}`}
            >
              {row?.status}
            </Button>
          );
        },
        header: 'Status',
        size: 100,
        cell: (info) => <div>{info.getValue() as string} </div>,
      },
      {
        accessorKey: 'expiredBy',
        header: 'Expiration Date Time',
        size: 150,
        cell: (info) => (
          <div>
            {dayJsFormat(info.getValue() as string, 'MM/DD/YYYY hh:mm A')}
          </div>
        ),
        enableSorting: true,
      },
    ],
    []
  );

  const handleInviteClient: SubmitHandler<InviteClient> = async (
    data: InviteClient
  ) => {
    try {
      await inviteClient({
        url: INVITE_CLIENT_API.CLIENTS_INVITE,
        data,
      })
        .unwrap()
        .then(() => {
          form.reset();
          handleInviteClientModal();
          setRefreshList(true);
        })
        .finally(() => setTimeout(() => setRefreshList(false), 500));
    } catch (error) {
      // Handle error (optional)
    }
  };

  return (
    <AppTableContextProvider>
      <div className="flex flex-col p-6 gap-6">
        <AppDataTable
          url={INVITE_CLIENT_API.GET_ALL_INVITES}
          heading="Invites"
          enableSearch={true}
          enableFilter={false}
          enablePagination={true}
          columns={[...columns, actionColumn]}
          filterClassName="w-[473px]"
          filterContent={<></>}
          searchKey={'email'}
          customToolBar={CustomToolbar}
          refreshList={refreshList}
        />
        <CustomDialog
          onOpenChange={handleInviteClientModal}
          description="Invite a client to join"
          open={isInviteClient}
          className="min-w-[30%]"
          title={'Invite Client'}
          aria-labelledby="invite-client-dialog"
          aria-describedby="invite-client-dialog-description"
        >
          <div className="px-4 space-y-4">
            <InputField
              form={form}
              name="email"
              label="Email Address"
              placeholder="Enter Client Email"
              validation={EMAIL_VALIDATION_RULEs}
              aria-describedby="email-helper-text"
            />

            <div className="flex gap-3">
              <AppButton
                label="Invite"
                className="w-full col-span-2 mt-4"
                onClick={handleSubmit(handleInviteClient)}
                isLoading={isLoading}
                aria-label="Send invite"
              />
              <AppButton
                label="Close"
                className="w-full col-span-2 mt-4"
                variant="neutral"
                onClick={handleInviteClientModal}
                disabled={isLoading}
                aria-label="Close invite dialog"
              />
            </div>
          </div>
        </CustomDialog>
        <AppConfirmationModal
          title={'Confirmation'}
          description={<div>Do you want to resend this invitation?</div>}
          open={openResendDialog.open}
          onOpenChange={onOpenChange}
          handleCancel={onOpenChange}
          handleSubmit={handleResendEmail}
          isLoading={resendLoading}
        />
      </div>
    </AppTableContextProvider>
  );
};

export default ClientInvite;
