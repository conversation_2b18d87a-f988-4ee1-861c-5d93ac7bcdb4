import LoginLogoIcon from '@/assets/icons/LoginLogoIcon';
import AppSpinner from '@/components/common/app-spinner';
import SetPasswordForm from '@/components/modules/auth/set-password';
import { SYSTEM_USERS_API_ROUTES } from '@/constants/api-constants';
import { useValdiateTokenMutation } from '@/redux/features/auth/authApi';
import { useCallback, useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';

const SetPasswordPage = () => {
  const location = useLocation();
  let token = location.search.replace('?token=', '');

  const [isValidInvitation, setIsValidInvitation] = useState<boolean | null>(
    null
  );
  const [verifiedEmail, setVerifiedEmail] = useState<string>('');
  // Hook to call the API to validate the token
  const [validateToken, { isLoading: isLoadingValidate }] =
    useValdiateTokenMutation();

  const verifyToken = useCallback(async () => {
    try {
      await validateToken({
        url: SYSTEM_USERS_API_ROUTES.VERIFY_TOKEN,
        data: { token }, // Send encoded token in API request
        showToaster: false,
      })
        .unwrap()
        .then((response: any) => {
          if (response?.data?.emailId) {
            setIsValidInvitation(true);
            setVerifiedEmail(response?.data?.emailId);
          } else {
            setIsValidInvitation(false);
          }
        })
        .catch((error) => {
          if (
            error?.status === 500 &&
            error?.data?.message ===
              'Link is expired , Please contact administrator'
          ) {
            setIsValidInvitation(false);
          } else {
            setIsValidInvitation(false);
          }
        });
    } catch (error) {
      setIsValidInvitation(false);
    }
  }, [token, validateToken]);

  useEffect(() => {
    verifyToken();
  }, [verifyToken]);

  // Show loading spinner while validating token
  if (isLoadingValidate || isValidInvitation === null) {
    return <AppSpinner overlay isLoading={true} />;
  }

  // Show invitation expired message if the token is invalid
  if (isValidInvitation === false) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-[#55B9C0] to-[#5A2AC2] flex justify-center items-center">
        <div className="text-center p-6 bg-white shadow-xl rounded-lg w-[40%]">
          <h2 className="text-3xl font-bold text-[#5A2AC2]">
            Invitation Expired !
          </h2>
          <p className="text-xl text-gray-700 mt-4">
            The link you're trying to use is no longer valid. <br />
            Please contact the administrator for further assistance. <br />
            <br />
            You can reach us at{' '}
            <a
              href="mailto:<EMAIL>"
              className="text-[#5A2AC2] font-semibold"
            >
              <EMAIL>
            </a>
          </p>
          <button
            onClick={() => (window.location.href = '/')}
            className="mt-6 px-8 py-2 bg-[#5A2AC2] text-white rounded-full hover:bg-[#4b1f8b]"
          >
            Go Back Home
          </button>
        </div>
      </div>
    );
  }

  // If the invitation is valid, show the password form
  if (isValidInvitation === true) {
    return (
      <>
        <div className="min-h-screen bg-gradient-to-b from-[#55B9C0] to-[#5A2AC2] w-full flex flex-col lg:flex-row p-4 md:p-8 lg:p-12 xl:p-16 gap-4 items-center">
          {/* Left section: Logo */}
          <div className="hidden xl:flex w-full lg:w-1/2 aspect-square lg:aspect-auto lg:h-full flex-col justify-center items-center p-4">
            <LoginLogoIcon className="w-full max-w-lg h-auto" />
          </div>

          {/* Right section: Set Password Form */}
          <div className="w-full lg:w-1/2 h-full min-h-[500px] bg-white/10 rounded-2xl p-4 sm:p-8 lg:p-12 xl:p-16 flex flex-col justify-center">
            <SetPasswordForm emailId={verifiedEmail} />
          </div>
        </div>
      </>
    );
  }

  return null; // Fallback if no valid state
};

export default SetPasswordPage;
