'use client';
import { SortingStateType } from '@/types/common.types';
import { createContext, useState } from 'react';

type CreateAppTableContextProviderProps = {
  children: React.ReactNode;
  defaultSort?: SortingStateType[];
};

interface CreateItemsContextModel {
  isItemType: boolean;
  isPackageItem: boolean;
  setIsItemType: React.Dispatch<React.SetStateAction<boolean>>;
  setIsPackageItem: React.Dispatch<React.SetStateAction<boolean>>;
}

export const ItemsContext = createContext<CreateItemsContextModel>({
  isItemType: false,
  isPackageItem: false,
  setIsItemType: () => {},
  setIsPackageItem: () => {},
});

const ItemsContextProvider = ({
  children,
}: CreateAppTableContextProviderProps) => {
  const [isItemType, setIsItemType] = useState<boolean>(false);
  const [isPackageItem, setIsPackageItem] = useState<boolean>(false);

  return (
    <ItemsContext.Provider
      value={{
        isItemType,
        setIsItemType,
        isPackageItem,
        setIsPackageItem,
      }}
    >
      {children}
    </ItemsContext.Provider>
  );
};

export default ItemsContextProvider;
