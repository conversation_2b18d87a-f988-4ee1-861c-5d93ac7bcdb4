import ResetPasswordForm from '@/components/modules/auth/reset-password';
import LoginLogoIcon from '@/assets/icons/LoginLogoIcon';

const ResetPasswordPage = () => {
  return (
    <>
      <div className="bg-gradient-to-b from-[#55B9C0] to-[#5A2AC2]  w-full h-screen flex flex-col lg:flex-row md:p-16 gap-4 items-center">
        {/* Left section: Logo */}
        <div className="hidden xl:flex w-full h-full flex-col justify-center">
          <LoginLogoIcon className="m-auto h-[65%] w-[65%]" />
        </div>

        {/* Right section: Login Form */}
        <div className="w-full h-full overflow-auto bg-white/10 rounded-2xl p-8 flex flex-col justify-center">
          <ResetPasswordForm />
        </div>
      </div>
    </>
  );
};

export default ResetPasswordPage;
