// import AppSpinner from '@/components/common/app-spinner';
import AppButton from '@/components/common/app-button';
import InputField from '@/components/forms/input-field';
import LoginLogoIcon from '@/assets/icons/LoginLogoIcon';
import SignupForm from '@/components/modules/auth/signup';
import { INVITE_CLIENT_API } from '@/constants/tenant-constants';
import { cn } from '@/lib/utils';
import { useAddNewItemMutation } from '@/redux/features/common-api/common.api';
import { MailIcon } from 'lucide-react';
import { useCallback, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useLocation } from 'react-router-dom';
import FormHeader from './FormHeader';
import { EMAIL_VALIDATION_RULEs } from '@/constants/auth-constants';

const SignupPage = () => {
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const token = queryParams.get('token');
  const [isValidInvitation, setIsValidInvitation] = useState<boolean | null>(
    null
  );

  const [valdiateToken, { isLoading }] = useAddNewItemMutation();
  const labelClass = 'text-[#fff]';

  const form = useForm<{ email: string }>({
    defaultValues: {
      email: '',
    },
    mode: 'onChange',
  });

  const handleVerifyEmail = useCallback(
    async (formData: { email: string }) => {
      await valdiateToken({
        url: INVITE_CLIENT_API.VALIDTE_INVITE,
        data: { token, email: formData.email },
        showToaster: false,
      })
        .unwrap()
        .then(() => setIsValidInvitation(true))
        .catch((error) => {
          if (
            error.status === 401 &&
            error.message === 'Invalid or expired token'
          ) {
            setIsValidInvitation(false);
          }
        });
    },
    [token, valdiateToken]
  );

  if (isValidInvitation === null) {
    // Show loading spinner or placeholder while checking validity
    return (
      <div className="min-h-screen bg-gradient-to-b from-[#55B9C0] to-[#5A2AC2]">
        <div className="container mx-auto flex min-h-[100vh] flex-col lg:flex-row justify-center items-center">
          {/* Logo Section */}
          <div className="lg:w-1/2 flex w-full items-center justify-center p-4">
            <div className="relative w-full max-w-lg">
              {/* Adjust logo size based on screen sizes */}
              <LoginLogoIcon className="h-auto w-1/3  mx-auto md:w-1/3 lg:w-full" />
            </div>
          </div>
          <div className="lg:w-1/2 w-full h-full overflow-hidden bg-white/10 rounded-2xl lg:px-16 lg:py-20 md:p-12 p-10 flex flex-col justify-center">
            <FormHeader
              title="Email Verification"
              Icon={MailIcon}
              className="w-12 h-[49px] text-white"
            />
            <div className="lg:mb-40">
              <InputField
                form={form}
                name="email"
                label="Email"
                placeholder="Enter Contact Email"
                validation={EMAIL_VALIDATION_RULEs}
                labelClassName={labelClass}
              />
            </div>
            <AppButton
              isLoading={isLoading}
              className={cn(
                'bg-brand-teal-Default text-white hover:bg-brand-teal-Default mt-20 w-full transition-colors sticky bottom-0'
              )}
              spinnerClass={
                'border-white-500  border-t-transparent animate-spin'
              }
              label="Verify"
              onClick={form.handleSubmit(handleVerifyEmail)}
            />
          </div>
        </div>
      </div>
    );
  }

  if (isValidInvitation === false) {
    // Show invitation expired message if the link is invalid
    return (
      <div className="min-h-screen bg-gradient-to-b from-[#55B9C0] to-[#5A2AC2] flex justify-center items-center">
        <div className="text-center p-6 bg-white shadow-xl rounded-lg w-[40%]">
          <h2 className="text-3xl font-bold text-[#5A2AC2]">
            Invitation Expired !
          </h2>
          <p className="text-xl text-gray-700 mt-4">
            The link you're trying to use is no longer valid. <br />
            Please contact the administrator for further assistance. <br />
            <br />
            You can reach us at{' '}
            <a
              href="mailto:<EMAIL>"
              className="text-[#5A2AC2] font-semibold"
            >
              <EMAIL>
            </a>
          </p>
          <button
            onClick={() => (window.location.href = '/')}
            className="mt-6 px-8 py-2 bg-[#5A2AC2] text-white rounded-full hover:bg-[#4b1f8b]"
          >
            Go Back Home
          </button>
        </div>
      </div>
    );
  }

  if (isValidInvitation === true)
    return (
      <>
        <>
          <div className="min-h-screen bg-gradient-to-b from-[#55B9C0] to-[#5A2AC2]">
            <div className="container mx-auto flex min-h-[100vh] flex-col lg:flex-row justify-center items-center">
              {/* Logo Section */}
              <div className="flex w-full items-center justify-center p-4 lg:w-1/3 ">
                <div className="relative w-full max-w-lg">
                  {/* Adjust logo size based on screen sizes */}
                  <LoginLogoIcon className="h-auto w-1/3  mx-auto md:w-1/3 lg:w-full" />
                </div>
              </div>

              {/* Login Form Section */}
              <div className="flex w-full items-center justify-center p-4 lg:w-2/3">
                <SignupForm email={form.getValues('email')} />
              </div>
            </div>
          </div>
        </>
      </>
    );
};

export default SignupPage;
