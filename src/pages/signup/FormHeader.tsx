import { cn } from '@/lib/utils';

interface FormHeaderProps {
  title: string;
  Icon?: React.ElementType;
  className?: string;
}

export const FormHeader = ({ title, Icon, className }: FormHeaderProps) => (
  <div className="flex items-center gap-3 mb-5">
    <span className={cn(className)}>
      {Icon && <Icon className="w-full h-full" />}
    </span>
    <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-white">
      {title}
    </h2>
  </div>
);

export default FormHeader;
