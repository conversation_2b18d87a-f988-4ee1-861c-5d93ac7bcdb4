import AppButton from '@/components/common/app-button';
import AppConfirmationModal from '@/components/common/app-confirmation-modal';
import AppDataTable from '@/components/common/app-data-table/index';
import StatusBadge from '@/components/common/app-status-badge';
import CustomDialog from '@/components/common/dialog';
import InputField from '@/components/forms/input-field';
import DotIcon from '@/assets/icons/DotIcon';
import EyeIcon from '@/assets/icons/EyeIcon';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { EMAIL_VALIDATION_RULEs } from '@/constants/auth-constants';
import { ROUTES } from '@/constants/routes-constants';
import {
  INVITE_CLIENT_API,
  TENANT_API_ROUTES,
} from '@/constants/tenant-constants';
import { useAddNewItemMutation } from '@/redux/features/common-api/common.api';
import { useActivateAccountMutation } from '@/redux/features/tenant/tenant.api';
import { InviteClient } from '@/types/list.types';
import { TenantListType } from '@/types/tenant.types';
import { ColumnDef } from '@tanstack/react-table';
import { MailIcon, MonitorCheck, ShieldX } from 'lucide-react';
import { useCallback, useMemo, useState } from 'react';
import { SubmitHandler, useForm, UseFormReturn } from 'react-hook-form';
import { Link, useNavigate } from 'react-router-dom';

const Accounts = () => {
  const navigate = useNavigate();

  const [activateAccount, { isLoading }] = useActivateAccountMutation();
  const [clientDetails, setClientDetails] = useState<TenantListType>();
  const [isInviteClient, setIsInviteClient] = useState<boolean>(false);
  const [inviteClient, { isLoading: newItemLoading }] = useAddNewItemMutation();
  const [openDeleteDialog, setOpenDeleteDialog] = useState<boolean>(false);
  const [refreshList, setRefreshList] = useState<boolean>(false);
  const form: UseFormReturn<InviteClient> = useForm<InviteClient>({
    mode: 'onChange',
  });
  const { handleSubmit } = form;

  const onOpenChange = useCallback(() => {
    setOpenDeleteDialog((prevState) => !prevState);
  }, [setOpenDeleteDialog]);

  const handleAccountActivationModal = useCallback(
    async (clientData: TenantListType) => {
      setClientDetails(clientData);
      onOpenChange();
    },
    [onOpenChange]
  );

  const handleInviteClient: SubmitHandler<InviteClient> = async (
    data: InviteClient
  ) => {
    try {
      await inviteClient({
        url: INVITE_CLIENT_API.CLIENTS_INVITE,
        data,
      }).unwrap();
      form.reset();
      navigate(ROUTES.CLIENT_INVITE);
      setIsInviteClient(false);
    } catch (error) {
      // Handle error (optional)
    }
  };

  const handleAccountActivation = async () => {
    let payload = `${clientDetails?.id}/${!clientDetails?.isActive ? 'activate' : 'deactivate'}`;
    await activateAccount(payload)
      .unwrap()
      .then(() => {
        setRefreshList(true);
      })
      .catch((error) => error)
      .finally(() => {
        onOpenChange();
        setTimeout(() => {
          setRefreshList(false);
        }, 500);
      });
  };

  const actionColumn: ColumnDef<TenantListType> = useMemo(
    () => ({
      id: 'action',
      size: 110,
      header: 'Actions',
      cell: ({ row }) => (
        <div className="flex flex-row gap-x-4 justify-start items-center ">
          <Link
            to={`edit-account/${row.original.id}`}
            aria-label={`Edit ${row.original.clientName} account`}
          >
            <EyeIcon />
          </Link>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="h-8 w-8 p-0"
                aria-label="More options"
              >
                <DotIcon />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              align="end"
              className="w-[300px] p-2 rounded-xl z-10"
              role="menu"
            >
              <DropdownMenuItem
                onClick={() => {
                  handleAccountActivationModal(row?.original);
                }}
                className="cursor-pointer pl-4 pr-4 pt-3 pb-3 text-base text-text-Default"
                role="menuitem"
                aria-label={`Activate or deactivate ${row.original.clientName}`}
              >
                {row.original.isActive ? (
                  <div className="flex flex-row gap-3 items-center">
                    <ShieldX className="bg-red" />
                    Deactivate Client
                  </div>
                ) : (
                  <div className="flex flex-row gap-3 items-center">
                    <MonitorCheck />
                    Activate Client
                  </div>
                )}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      ),
    }),
    [handleAccountActivationModal]
  );

  const columns: ColumnDef<TenantListType>[] = useMemo(
    () => [
      {
        accessorKey: 'clientName',
        accessorFn: (row) => `${row?.clientName ?? ''}`,
        header: 'Client Name',
        size: 150,
        cell: (info) => <div>{info.getValue() as string} </div>,
        enableSorting: true,
      },
      {
        accessorKey: 'contactEmail',
        accessorFn: (row) => `${row?.contactEmail ?? ''}`,
        header: 'Contact Email',
        size: 150,
        cell: (info) => <div>{info.getValue() as string} </div>,
        enableSorting: true,
      },

      {
        accessorKey: 'address_line1',
        accessorFn: (row) =>
          `${row?.addressLine1 ?? ''} ${row?.addressLine2 ?? ''}`,
        header: 'Address',
        enableSorting: true,
        size: 150,
        cell: (info) => {
          return <div>{(info.getValue() as string) ?? ''}</div>;
        },
      },
      {
        accessorKey: 'city',
        header: 'City',
        size: 130,
        enableSorting: true,
      },
      {
        accessorKey: 'state',
        header: 'State',
        size: 110,
        enableSorting: true,
      },
      {
        accessorKey: 'zipcode',
        header: 'Zip Code',
        size: 130,
        enableSorting: true,
      },

      {
        accessorKey: 'website',
        header: 'Website',
        size: 150,
      },
      {
        accessorFn: (row) => `${row?.isActive ?? ''}`,
        header: 'Status',
        cell: ({ row }) => <StatusBadge status={row?.original?.isActive} />,
        size: 80,
        enableSorting: true,
      },
    ],
    []
  );

  const handleInviteClientModal = useCallback(() => {
    form.reset();
    setIsInviteClient((prevState) => !prevState);
  }, [form]);

  const CustomToolbar = (
    <>
      <AppButton
        icon={MailIcon}
        className="w-[170px]"
        label="Invite Client"
        onClick={handleInviteClientModal}
        aria-label="Invite a new client"
      />
      {/* 
      <AppButton
        icon={UserPlusIcon}
        className="w-[170px]"
        label="New Client"
        onClick={handleNewCustomer}
      /> */}
    </>
  );

  return (
    <div className="flex flex-col p-6 gap-6">
      <AppDataTable
        url={TENANT_API_ROUTES.ALL}
        heading="Clients"
        enableSearch={true}
        enableFilter={false}
        enablePagination={true}
        columns={[...columns, actionColumn]}
        filterClassName="w-[473px]"
        filterContent={<></>}
        searchKey={'client_name'}
        customToolBar={CustomToolbar}
        refreshList={refreshList}
        tableClassName="max-h-[550px] overflow-auto"
      />
      <AppConfirmationModal
        title={`Confirmation`}
        description={
          <div>
            Are you sure you want to
            <span className="font-semibold text-base text-text-Default">
              {' '}
              {clientDetails?.isActive ? ' Deactivate' : ' Activate'}{' '}
              {clientDetails?.clientName}
            </span>
            &nbsp;?
          </div>
        }
        isLoading={isLoading}
        open={openDeleteDialog}
        onOpenChange={onOpenChange}
        handleCancel={onOpenChange}
        handleSubmit={handleAccountActivation}
      />
      <CustomDialog
        onOpenChange={handleInviteClientModal}
        description=""
        open={isInviteClient}
        className="min-w-[30%]"
        title={'Invite Client'}
      >
        <div className="px-4 space-y-4">
          <InputField
            form={form}
            name="email"
            label="Email Address"
            placeholder="Enter Client Email"
            validation={EMAIL_VALIDATION_RULEs}
            aria-label="Client's email address"
          />

          <div className="flex gap-3">
            <AppButton
              label="Invite"
              className="w-full col-span-2 mt-4"
              onClick={handleSubmit(handleInviteClient)}
              isLoading={isLoading || newItemLoading}
              aria-label="Send client invitation"
            />
            <AppButton
              label="Close"
              className="w-full col-span-2 mt-4"
              variant="neutral"
              onClick={handleInviteClientModal}
              disabled={isLoading || newItemLoading}
              aria-label="Close invitation modal"
            />
          </div>
        </div>
      </CustomDialog>
    </div>
  );
};

export default Accounts;
