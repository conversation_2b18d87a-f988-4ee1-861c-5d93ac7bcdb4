import NewAccountInformation from '@/components/modules/accounts/new-accounts';
import AppSpinner from '@/components/common/app-spinner';

import {
  useCreateTenatMutation,
  useGetTenantByIdQuery,
  useUpdateTenantMutation,
} from '@/redux/features/tenant/tenant.api';

import { TenantType } from '@/types/tenant.types';

import { useEffect, useMemo } from 'react';
import { FormProvider, SubmitHandler, useForm } from 'react-hook-form';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'sonner';
import Header from '@/components/modules/customers/new-customer/header';
import { ROUTES } from '@/constants/routes-constants';

const NewAccount = () => {
  const { clientId } = useParams<{ clientId?: string }>();
  const navigate = useNavigate();

  const [createAccount, { isLoading: createCustomerLoader }] =
    useCreateTenatMutation();

  const [updateAcount, { isLoading: updateCustomerLoader }] =
    useUpdateTenantMutation();

  const { data: accountData, isLoading } = useGetTenantByIdQuery(
    clientId ?? '',
    {
      skip: !clientId,
    }
  );

  const defaultValues = useMemo(() => {
    return {
      ...accountData,
      isActive: accountData?.isActive ? 'true' : 'false',
      country: 'USA',
      contactPhone: accountData?.contactPhone ?? '',
    };
  }, [accountData]);

  const form = useForm<TenantType>({
    defaultValues: defaultValues,
    mode: 'onChange',
  });

  const handleSave = async (data: TenantType) => {
    try {
      const respone: any = await createAccount({
        ...data,
        isActive: data.isActive === 'true',
      }).unwrap();

      if (respone && respone?.message) {
        toast.success(respone?.message);
      }
      navigate(ROUTES.ACCOUNTS);
    } catch (error) {}
  };

  const handleUpdate = async (data: TenantType) => {
    try {
      const respone: any = await updateAcount({
        clientData: {
          ...data,
          isActive: data.isActive === 'true',
        },
        id: Number(clientId),
      }).unwrap();

      if (respone && respone?.message) {
        toast.success(respone?.message);
      }
    } catch (error) {}
  };

  const onSubmit: SubmitHandler<TenantType> = (data) => {
    if (clientId) {
      handleUpdate(data);
    } else {
      handleSave(data);
    }
  };

  useEffect(() => {
    if (accountData) {
      form.reset(defaultValues);
    }

    return () => {
      form.reset();
    };
  }, [accountData, defaultValues, form]);

  if (isLoading || createCustomerLoader || updateCustomerLoader) {
    return <AppSpinner />;
  }

  return (
    <FormProvider {...form}>
      <div className="flex flex-col p-4 gap-4">
        <Header
          navigateTo={ROUTES.ACCOUNTS}
          onSave={() => form.handleSubmit(onSubmit)()}
          headerTitle={clientId ? 'Edit Client' : 'New Client'}
          showButton={false}
        />
        {/* <Separator
          orientation="horizontal"
          className="h-[1px] bg-border-Default"
        /> */}
        <div className="grid grid-cols-12 gap-4">
          {/* Account Info Section */}
          <NewAccountInformation />
        </div>
      </div>
    </FormProvider>
  );
};

export default NewAccount;
