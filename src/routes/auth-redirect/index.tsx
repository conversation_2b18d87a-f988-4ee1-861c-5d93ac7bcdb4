import { RootState } from '@/redux/store';
import { ROUTES } from '@/constants/routes-constants';
import { useSelector } from 'react-redux';
import { Navigate, useLocation } from 'react-router-dom';

const AuthRedirect = ({ children }: { children: React.ReactNode }) => {
  const { token } = useSelector((state: RootState) => state.auth);
  const location = useLocation();

  // Check if the user is authenticated
  if (token) {
    return <Navigate to={ROUTES.HOME} state={{ from: location }} replace />;
  }

  return children;
};

export default AuthRedirect;
