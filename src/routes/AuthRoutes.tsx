import { lazy } from 'react';
const Login = lazy(() => import('@/pages/login'));
const SignupPage = lazy(() => import('@/pages/signup'));
const ResetPasswordPage = lazy(() => import('@/pages/reset-password'));
const ForgotPasswordPage = lazy(() => import('@/pages/forgot-password'));
const SetPasswordPage = lazy(() => import('@/pages/set-password'));
import AuthRedirect from '@/routes/auth-redirect';
import SuspenseWrapper from './SuspenseWrapper';
import { ROUTES } from '@/constants/routes-constants';

const ProcessingPayments = lazy(() => import('../pages/processing-payment'));

export const AuthRoutes = [
  {
    path: ROUTES.LOGIN,
    element: (
      <AuthRedirect>
        <SuspenseWrapper>
          <Login />
        </SuspenseWrapper>
      </AuthRedirect>
    ),
  },
  {
    path: ROUTES.SIGNUP,
    element: (
      <AuthRedirect>
        <SuspenseWrapper>
          <SignupPage />
        </SuspenseWrapper>
      </AuthRedirect>
    ),
  },
  {
    path: ROUTES.RESET_PASSWORD,
    element: (
      <AuthRedirect>
        <SuspenseWrapper>
          <ResetPasswordPage />
        </SuspenseWrapper>
      </AuthRedirect>
    ),
  },
  {
    path: ROUTES.FORGOT_PASSWORD,
    element: (
      <AuthRedirect>
        <SuspenseWrapper>
          <ForgotPasswordPage />
        </SuspenseWrapper>
      </AuthRedirect>
    ),
  },
  {
    path: ROUTES.SET_PASSWORD,
    element: (
      <AuthRedirect>
        <SuspenseWrapper>
          <SetPasswordPage />
        </SuspenseWrapper>
      </AuthRedirect>
    ),
  },
  {
    path: ROUTES.PROCESSING_PAYMENTS,
    element: (
      <SuspenseWrapper>
        <ProcessingPayments />
      </SuspenseWrapper>
    ),
  },
];
