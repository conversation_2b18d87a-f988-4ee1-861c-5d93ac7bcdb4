import { Permissions } from '@/constants/permissions';
import { ROUTES } from '@/constants/routes-constants';
import AdminPage from '@/pages/admin';
import UserPage from '@/pages/users/UserPage';
import { lazy } from 'react';
import SuspenseWrapper from './SuspenseWrapper';

const ProcessesPage = lazy(() => import('@/pages/processes'));
const ReportsPage = lazy(() => import('@/pages/reports'));
const WarehousePage = lazy(() => import('@/pages/warehouse'));

// Store Options
const StoreOptionsPage = lazy(() => import('../pages/options/store-options'));
const AddEditStoreOptions = lazy(
  () => import('../pages/options/edit-store-options')
);
const StoreOptions = lazy(() => import('@/components/modules/system/store'));

// Company Options
const CompanyPage = lazy(() => import('@/pages/company/index'));

// Accounting Module
const Accounting = lazy(() => import('@/pages/accounting'));
const AdjustmentsPage = lazy(() => import('@/pages/accounting/AdjustmentPage'));
const Adjustments = lazy(
  () => import('@/components/modules/accounting/adjustments')
);
const AddEditAdjustments = lazy(
  () =>
    import(
      '@/components/modules/accounting/adjustments/add-edit-adjustments/AddEditAdjustments'
    )
);
const InquiryPage = lazy(() => import('@/pages/accounting/InquiryPage'));
const Inquiry = lazy(() => import('@/components/modules/accounting/inquiry'));
const InquiryDetails = lazy(
  () => import('@/components/modules/accounting/inquiry/inquiry-details')
);
const PaymentsPage = lazy(() => import('@/pages/accounting/PaymentsPage'));
const AccountingCustomers = lazy(
  () => import('@/components/modules/accounting/payments')
);

const CustomerPaymentPage = lazy(
  () => import('@/pages/accounting/CustomerPaymentPage')
);

const Payments = lazy(
  () => import('@/components/modules/accounting/payments/PaymentsList')
);
const AddEditPayment = lazy(
  () => import('@/components/modules/accounting/payments/add-view-payments')
);
const ProcessCreditCardsPage = lazy(
  () => import('@/pages/process-credit-cards')
);
const ProcessCreditCards = lazy(
  () => import('@/components/modules/accounting/process-credit-cards')
);
const AddEditCreditCards = lazy(
  () => import('../pages/process-credit-cards/add-edit-credit-cards/')
);

// Customers Module
const NewCustomer = lazy(() => import('@/pages/new-customer'));
const CustomersPage = lazy(() => import('@/pages/customers'));
const Customers = lazy(
  () => import('@/components/modules/customers/customer-details')
);

// Items Module
const ItemsPage = lazy(() => import('@/pages/items'));
const Items = lazy(() => import('@/components/modules/items/item-details'));
const AddEditItems = lazy(() => import('../pages/items/edit-item'));

// Lists Module
const Listpage = lazy(() => import('@/pages/lists'));
const ListingTable = lazy(() => import('@/components/modules/lists/listing'));
const DeliveryLocationTable = lazy(
  () =>
    import(
      '@/components/modules/lists/delivery-locations/DeliveryLocationTable'
    )
);
const AddDeliveryLocation = lazy(
  () =>
    import('@/components/modules/lists/delivery-locations/AddDeliveryLocation')
);
const EditDeliveryLocation = lazy(
  () =>
    import('@/components/modules/lists/delivery-locations/EditDeliveryLocation')
);

const UserOptionsPage = lazy(() => import('@/pages/user-options'));

// Orders Module
const OrdersPage = lazy(() => import('@/pages/orders'));
const Orders = lazy(() => import('@/components/modules/orders/'));
const AddEditOrders = lazy(() => import('../pages/orders/add-edit-orders'));
const OverbookedItemInfo = lazy(
  () => import('@/components/modules/orders/overbooked-item-info')
);
const AvailabilityCalendar = lazy(
  () => import('@/components/modules/orders/availability-calendar')
);
const BusyCalendar = lazy(
  () => import('@/components/modules/orders/busy-calendar')
);
const InventoryManager = lazy(
  () => import('@/components/modules/orders/inventory-manager')
);
const ESignDocuments = lazy(
  () => import('@/components/modules/orders/e-sign-documents')
);
const ItemInquiry = lazy(
  () => import('@/components/modules/orders/item-inquiry')
);
const OnlinePayments = lazy(
  () => import('@/components/modules/orders/online-payments')
);
const CustomPickupReturn = lazy(
  () => import('@/components/modules/warehouse/custom-pickup-return')
);
const OrderDeliveriesPickups = lazy(
  () => import('@/components/modules/warehouse/order-deliveries-pickups')
);
const SubrentalPickupsReturns = lazy(
  () => import('@/components/modules/warehouse/subrental-pickups-returns')
);
const TruckItineraries = lazy(
  () => import('@/components/modules/warehouse/truck-itineraries')
);

// Purchase Orders Module
const PurchaseOrdersPage = lazy(() => import('@/pages/purchase-orders'));
const PurchaseOrder = lazy(
  () => import('@/components/modules/purchase-order/list/index')
);
const AddEditPurchaseOrder = lazy(
  () => import('@/components/modules/purchase-order/tabs/AddEditPurchaseOrder')
);

// Sub Rentals Module
const SubRentalsPage = lazy(() => import('@/pages/sub-rentals'));
const SubRental = lazy(() => import('@/components/modules/sub-rental'));
const AddEditSubRental = lazy(
  () => import('@/components/modules/sub-rental/add-edit-sub-rental')
);

// System Module
const SystemPage = lazy(() => import('@/pages/system'));
const Users = lazy(() => import('@/components/modules/system/users'));
const UserOptions = lazy(
  () => import('@/components/modules/system/user-options')
);
const AddEditUsers = lazy(
  () => import('@/components/modules/system/users/AddEditUsers')
);

// Vendors Module
const VendorsPage = lazy(() => import('@/pages/vendors'));

export const AdminRoutes = [
  {
    path: ROUTES.ADMIN,
    element: (
      <SuspenseWrapper>
        <AdminPage />
      </SuspenseWrapper>
    ),
  },
  {
    path: ROUTES.ADJUSTMENTS,
    element: (
      <SuspenseWrapper>
        <Accounting />
      </SuspenseWrapper>
    ),
    children: [
      {
        path: ROUTES.ADJUSTMENTS,
        element: (
          <SuspenseWrapper>
            <AdjustmentsPage />
          </SuspenseWrapper>
        ),
        children: [
          {
            path: '',
            element: (
              <SuspenseWrapper>
                <Adjustments />
              </SuspenseWrapper>
            ),
          },
          {
            path: ROUTES.ADD_ADJUSTMENTS,
            element: (
              <SuspenseWrapper>
                <AddEditAdjustments />
              </SuspenseWrapper>
            ),
          },
          {
            path: ROUTES.EDIT_ADJUSTMENTS,
            element: (
              <SuspenseWrapper>
                <AddEditAdjustments />
              </SuspenseWrapper>
            ),
          },
        ],
      },
    ],
    permissions: [Permissions.Accounting],
  },
  {
    path: ROUTES.INQUIRY,
    element: (
      <SuspenseWrapper>
        <InquiryPage />
      </SuspenseWrapper>
    ),
    children: [
      {
        path: '',
        element: (
          <SuspenseWrapper>
            <Inquiry />
          </SuspenseWrapper>
        ),
      },
      {
        path: ROUTES.INQUIRY_DETAILS,
        element: (
          <SuspenseWrapper>
            <InquiryDetails />
          </SuspenseWrapper>
        ),
      },
    ],
    permissions: [Permissions.Accounting],
  },

  {
    path: ROUTES.ACCOUNTING_CUSTOMERS,
    element: (
      <SuspenseWrapper>
        <PaymentsPage />
      </SuspenseWrapper>
    ),
    children: [
      {
        path: '',
        element: (
          <SuspenseWrapper>
            <AccountingCustomers />
          </SuspenseWrapper>
        ),
      },
      {
        path: ROUTES.ACCOUNTING_PAYMENTS,
        element: (
          <SuspenseWrapper>
            <CustomerPaymentPage />
          </SuspenseWrapper>
        ),
        children: [
          {
            path: '',
            element: (
              <SuspenseWrapper>
                <Payments />
              </SuspenseWrapper>
            ),
          },
          {
            path: ROUTES.ACCOUNTING_ADD_PAYMENT,
            element: (
              <SuspenseWrapper>
                <AddEditPayment />
              </SuspenseWrapper>
            ),
          },
          {
            path: ROUTES.ACCOUNTING_EDIT_PAYMENT,
            element: (
              <SuspenseWrapper>
                <AddEditPayment />
              </SuspenseWrapper>
            ),
          },
        ],
      },
    ],
  },
  {
    path: ROUTES.PROCESS_CREDIT_CARDS,
    element: (
      <SuspenseWrapper>
        <ProcessCreditCardsPage />
      </SuspenseWrapper>
    ),
    children: [
      {
        path: '',
        element: (
          <SuspenseWrapper>
            <ProcessCreditCards />
          </SuspenseWrapper>
        ),
      },
      {
        path: ROUTES.PROCESS_CREDIT_CARDS,
        element: (
          <SuspenseWrapper>
            <AddEditCreditCards />
          </SuspenseWrapper>
        ),
      },
      {
        path: ROUTES.PROCESS_CREDIT_CARDS,
        element: (
          <SuspenseWrapper>
            <AddEditCreditCards />
          </SuspenseWrapper>
        ),
      },
    ],
  },
  {
    path: ROUTES.CUSTOMERS,
    element: (
      <SuspenseWrapper>
        <CustomersPage />
      </SuspenseWrapper>
    ),
    permissions: [Permissions.Customers],
    children: [
      {
        path: '',
        element: (
          <SuspenseWrapper>
            <Customers />
          </SuspenseWrapper>
        ),
      },
      {
        path: ROUTES.ADD_NEW_CUSTOMER,
        element: (
          <SuspenseWrapper>
            <NewCustomer />
          </SuspenseWrapper>
        ),
      },
      {
        path: ROUTES.EDIT_CUSTOMER,
        element: (
          <SuspenseWrapper>
            <NewCustomer />
          </SuspenseWrapper>
        ),
      },
    ],
  },
  {
    path: ROUTES.ITEMS,
    element: (
      <SuspenseWrapper>
        <ItemsPage />
      </SuspenseWrapper>
    ),
    permissions: [Permissions.Items],
    children: [
      {
        path: '',
        element: (
          <SuspenseWrapper>
            <Items />
          </SuspenseWrapper>
        ),
      },
      {
        path: ROUTES.ADD_NEW_ITEM,
        element: (
          <SuspenseWrapper>
            <AddEditItems />
          </SuspenseWrapper>
        ),
      },
      {
        path: ROUTES.EDIT_ITEM,
        element: (
          <SuspenseWrapper>
            <AddEditItems />
          </SuspenseWrapper>
        ),
      },
    ],
  },
  {
    path: ROUTES.SUBLISTS,
    element: (
      <SuspenseWrapper>
        <ListingTable />
      </SuspenseWrapper>
    ),
  },
  {
    path: ROUTES.LISTS,
    element: (
      <SuspenseWrapper>
        <Listpage />
      </SuspenseWrapper>
    ),
    permissions: [Permissions.Lists],
  },
  {
    path: ROUTES.DELIVERY_LOCATION,
    element: (
      <SuspenseWrapper>
        <DeliveryLocationTable />
      </SuspenseWrapper>
    ),
    permissions: [Permissions.Lists],
  },
  {
    path: ROUTES.LISTS,
    element: (
      <SuspenseWrapper>
        <ListingTable />
      </SuspenseWrapper>
    ),
    permissions: [Permissions.Lists],
  },
  {
    path: ROUTES.ADD_DELIVERY_LOCATION,
    element: (
      <SuspenseWrapper>
        <AddDeliveryLocation />
      </SuspenseWrapper>
    ),
    permissions: [Permissions.Lists],
  },
  {
    path: ROUTES.EDIT_DELIVERY_LOCATION,
    element: (
      <SuspenseWrapper>
        <EditDeliveryLocation />
      </SuspenseWrapper>
    ),
    permissions: [Permissions.Lists],
  },

  {
    path: ROUTES.ORDERS,
    element: (
      <SuspenseWrapper>
        <OrdersPage />
      </SuspenseWrapper>
    ),
    children: [
      {
        path: '',
        element: (
          <SuspenseWrapper>
            <Orders />
          </SuspenseWrapper>
        ),
      },
      {
        path: ROUTES.ADD_ORDERS,
        element: (
          <SuspenseWrapper>
            <AddEditOrders />
          </SuspenseWrapper>
        ),
      },
      {
        path: ROUTES.EDIT_ORDERS,
        element: (
          <SuspenseWrapper>
            <AddEditOrders />
          </SuspenseWrapper>
        ),
      },
      {
        path: ROUTES.OVERBOOKED_ITEM_INFO,
        element: (
          <SuspenseWrapper>
            <OverbookedItemInfo />
          </SuspenseWrapper>
        ),
      },
      {
        path: ROUTES.AVAILABILITY_CALENDAR,
        element: (
          <SuspenseWrapper>
            <AvailabilityCalendar />
          </SuspenseWrapper>
        ),
      },
      {
        path: ROUTES.BUSY_CALENDAR,
        element: (
          <SuspenseWrapper>
            <BusyCalendar />
          </SuspenseWrapper>
        ),
      },

      {
        path: ROUTES.INVENTORY_MANAGER,
        element: (
          <SuspenseWrapper>
            <InventoryManager />
          </SuspenseWrapper>
        ),
      },
      {
        path: ROUTES.E_SIGN_DOCUMENTS,
        element: (
          <SuspenseWrapper>
            <ESignDocuments />
          </SuspenseWrapper>
        ),
      },
      {
        path: ROUTES.ITEM_INQUIRY,
        element: (
          <SuspenseWrapper>
            <ItemInquiry />
          </SuspenseWrapper>
        ),
      },
    ],
    permissions: [Permissions.Orders],
  },
  {
    path: ROUTES.PROCESSES,
    element: (
      <SuspenseWrapper>
        <ProcessesPage />
      </SuspenseWrapper>
    ),
  },
  {
    path: ROUTES.PURCHASE_ORDERS,
    element: (
      <SuspenseWrapper>
        <PurchaseOrdersPage />
      </SuspenseWrapper>
    ),
    children: [
      {
        path: '',
        element: (
          <SuspenseWrapper>
            <PurchaseOrder />
          </SuspenseWrapper>
        ),
      },
      {
        path: ROUTES.ADD_NEW_PURCHASE_ORDER,
        element: (
          <SuspenseWrapper>
            <AddEditPurchaseOrder />
          </SuspenseWrapper>
        ),
      },
      {
        path: ROUTES.EDIT_PURCHASE_ORDER,
        element: (
          <SuspenseWrapper>
            <AddEditPurchaseOrder />
          </SuspenseWrapper>
        ),
      },
    ],
    permissions: [Permissions.PurchaseOrders],
  },
  {
    path: ROUTES.REPORTS,
    element: (
      <SuspenseWrapper>
        <ReportsPage />
      </SuspenseWrapper>
    ),
  },
  {
    path: ROUTES.SUB_RENTALS,
    element: (
      <SuspenseWrapper>
        <SubRentalsPage />
      </SuspenseWrapper>
    ),
    children: [
      {
        path: '',
        element: (
          <SuspenseWrapper>
            <SubRental />
          </SuspenseWrapper>
        ),
      },
      {
        path: ROUTES.ADD_SUB_RENTAL,
        element: (
          <SuspenseWrapper>
            <AddEditSubRental />
          </SuspenseWrapper>
        ),
      },
      {
        path: ROUTES.EDIT_SUB_RENTAL,
        element: (
          <SuspenseWrapper>
            <AddEditSubRental />
          </SuspenseWrapper>
        ),
      },
    ],
  },
  {
    path: ROUTES.SYSTEM_USERS,
    element: (
      <SuspenseWrapper>
        <SystemPage />
      </SuspenseWrapper>
    ),
  },
  {
    path: ROUTES.SYSTEM_USERS,
    element: (
      <SuspenseWrapper>
        <UserPage />
      </SuspenseWrapper>
    ),
    children: [
      {
        path: '',
        element: (
          <SuspenseWrapper>
            <Users />
          </SuspenseWrapper>
        ),
      },
      {
        path: ROUTES.ADD_SYSTEM_USERS,
        element: (
          <SuspenseWrapper>
            <AddEditUsers />
          </SuspenseWrapper>
        ),
      },
      {
        path: ROUTES.EDIT_SYSTEM_USERS,
        element: (
          <SuspenseWrapper>
            <AddEditUsers />
          </SuspenseWrapper>
        ),
      },
    ],
  },
  {
    path: ROUTES.VENDORS,
    element: (
      <SuspenseWrapper>
        <VendorsPage />
      </SuspenseWrapper>
    ),
    permissions: [Permissions.Vendors],
  },
  {
    path: ROUTES.WAREHOUSE,
    element: (
      <SuspenseWrapper>
        <WarehousePage />
      </SuspenseWrapper>
    ),
    permissions: [Permissions.Warehouse],
  },
  {
    path: ROUTES.WAREHOUSE_CUSTOM_PICKUP_RETURN,
    element: (
      <SuspenseWrapper>
        <CustomPickupReturn />
      </SuspenseWrapper>
    ),
    permissions: [Permissions.Warehouse],
  },
  {
    path: ROUTES.WAREHOUSE_ORDER_DELIVERIES_PICKUPS,
    element: (
      <SuspenseWrapper>
        <OrderDeliveriesPickups />
      </SuspenseWrapper>
    ),
    permissions: [Permissions.Warehouse],
  },
  {
    path: ROUTES.WAREHOUSE_SUBRENTAL_PICKUPS_RETURNS,
    element: (
      <SuspenseWrapper>
        <SubrentalPickupsReturns />
      </SuspenseWrapper>
    ),
    permissions: [Permissions.Warehouse],
  },
  {
    path: ROUTES.WAREHOUSE_TRUCK_ITINERARIES,
    element: (
      <SuspenseWrapper>
        <TruckItineraries />
      </SuspenseWrapper>
    ),
    permissions: [Permissions.Warehouse],
  },
  {
    path: ROUTES.USER_OPTIONS,
    element: (
      <SuspenseWrapper>
        <UserOptions />
      </SuspenseWrapper>
    ),
  },
  {
    path: ROUTES.COMPANY,
    element: (
      <SuspenseWrapper>
        <CompanyPage />
      </SuspenseWrapper>
    ),
  },
  {
    path: ROUTES.STORE_OPTIONS,
    element: (
      <SuspenseWrapper>
        <StoreOptionsPage />
      </SuspenseWrapper>
    ),
    children: [
      {
        path: '',
        element: (
          <SuspenseWrapper>
            <StoreOptions />
          </SuspenseWrapper>
        ),
      },
      {
        path: ROUTES.ADD_STORE_OPTIONS,
        element: (
          <SuspenseWrapper>
            <AddEditStoreOptions />
          </SuspenseWrapper>
        ),
      },
      {
        path: ROUTES.EDIT_STORE_OPTIONS,
        element: (
          <SuspenseWrapper>
            <AddEditStoreOptions />
          </SuspenseWrapper>
        ),
      },
    ],
  },
  {
    path: ROUTES.NORMAL_USER_OPTIONS,
    element: (
      <SuspenseWrapper>
        <UserOptionsPage />
      </SuspenseWrapper>
    ),
  },

  {
    path: ROUTES.ONLINE_PAYMENTS,
    element: (
      <SuspenseWrapper>
        <OnlinePayments />
      </SuspenseWrapper>
    ),
  },
];
