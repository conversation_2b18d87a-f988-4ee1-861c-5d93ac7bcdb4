import { lazy } from 'react';
import SuspenseWrapper from './SuspenseWrapper';
import { ROUTES } from '@/constants/routes-constants';
const Accounts = lazy(() => import('@/pages/accounts'));
const AccountsPage = lazy(() => import('@/pages/accounts/AccountsPage'));
const NewAccount = lazy(() => import('@/pages/new-account'));
const ClientInvite = lazy(() => import('@/pages/client-invite'));

export const SuperAdminRoutes = [
  {
    path: ROUTES.ACCOUNTS,
    element: (
      <SuspenseWrapper>
        <AccountsPage />
      </SuspenseWrapper>
    ),
    children: [
      {
        path: '',
        element: (
          <SuspenseWrapper>
            <Accounts />
          </SuspenseWrapper>
        ),
      },
      {
        path: ROUTES.NEW_ACCOUNTS,
        element: (
          <SuspenseWrapper>
            <NewAccount />
          </SuspenseWrapper>
        ),
      },
      {
        path: ROUTES.EDIT_ACCOUNTS,
        element: (
          <SuspenseWrapper>
            <NewAccount />
          </SuspenseWrapper>
        ),
      },
    ],
  },
  {
    path: ROUTES.CLIENT_INVITE,
    element: (
      <SuspenseWrapper>
        <ClientInvite />
      </SuspenseWrapper>
    ),
  },
];
