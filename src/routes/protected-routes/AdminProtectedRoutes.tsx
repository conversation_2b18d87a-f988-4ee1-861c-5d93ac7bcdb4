import { Navigate, useLocation } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
import { ROLES } from '@/constants/common-constants';
import { ROUTES } from '@/constants/routes-constants';
import AccessDenied from '@/components/common/AccessDenied';
import AppLayout from '@/components/common/app-layout';
import { adminOnlyRoutes } from '@/constants/permissions';
import { AdminRoutes } from '../AdminRoutes';

const AdminProtectedRoutes = () => {
  const location = useLocation();
  const { token, isAuthenticated, role, permissions } = useSelector(
    (state: RootState) => state.auth
  );

  const redirectToLogin = !token || !isAuthenticated;

  if (redirectToLogin) {
    return <Navigate to={ROUTES.LOGIN} state={{ from: location }} replace />;
  }

  // Check if the current route is in the admin-only routes
  const isAdminOnlyRoute = adminOnlyRoutes.some((route) =>
    location.pathname.startsWith(route)
  );

  if (role !== ROLES.SUPER_ADMIN && isAdminOnlyRoute && role !== ROLES.ADMIN) {
    return <AccessDenied />;
  }

  const currentRoute = AdminRoutes.find(
    (route) => route.path === location.pathname
  );

  if (currentRoute?.permissions) {
    const hasRequiredPermissions = currentRoute.permissions.every((required) =>
      permissions.some(
        ({ permission }: { permission: string }) => permission === required
      )
    );

    if (!hasRequiredPermissions) {
      return <AccessDenied />;
    }
  }

  return <AppLayout />;
};

export default AdminProtectedRoutes;
