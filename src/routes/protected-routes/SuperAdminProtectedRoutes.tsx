import { Navigate, useLocation } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { RootState } from '@/redux/store';
import { ROLES } from '@/constants/common-constants';
import AccessDenied from '@/components/common/AccessDenied';
import { ROUTES } from '@/constants/routes-constants';
import AppLayout from '@/components/common/app-layout';

const SuperAdminProtectedRoutes = () => {
  const location = useLocation();
  const { token, isAuthenticated, role } = useSelector(
    (state: RootState) => state.auth
  );

  if (!token || !isAuthenticated) {
    // If not authenticated, redirect to login page
    return <Navigate to={ROUTES.LOGIN} state={{ from: location }} replace />;
  }

  if (role !== ROLES.SUPER_ADMIN) {
    // If user is authenticated but doesn't have admin role
    return <AccessDenied />;
  }

  return <AppLayout />;
};

export default SuperAdminProtectedRoutes;
