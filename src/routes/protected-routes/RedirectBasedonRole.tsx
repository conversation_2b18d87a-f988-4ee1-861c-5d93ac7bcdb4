import AppSpinner from '@/components/common/app-spinner';
import { ROLES } from '@/constants/common-constants';
import { ROUTES } from '@/constants/routes-constants';
import { RootState } from '@/redux/store';
import { useSelector } from 'react-redux';
import { Navigate } from 'react-router-dom';

const RedirectBasedOnRole = () => {
  const { role, token, isAuthenticated, permissions, permissionsLoading } =
    useSelector((state: RootState) => state.auth);

  if (permissionsLoading) {
    return (
      <div className="bg-white">
        <AppSpinner isLoading />
      </div>
    );
  }

  if (role === ROLES.SUPER_ADMIN && token && isAuthenticated) {
    return <Navigate to={ROUTES.ACCOUNTS} replace />;
  }

  if (
    (role === ROLES.ADMIN || role === ROLES.NORMAL_USER) &&
    token &&
    isAuthenticated
  ) {
    const hasCustomersPermission = permissions?.some(
      (perm: { permission: string }) => perm.permission === 'Customers'
    );

    if (hasCustomersPermission) {
      return <Navigate to={ROUTES.CUSTOMERS} replace />; // Redirect Admin to /customer
    } else {
      return <Navigate to={ROUTES.ADMIN} replace />;
    }
  }

  // Default to login if no role or not authenticated
  return <Navigate to={ROUTES.LOGIN} replace />;
};

export default RedirectBasedOnRole;
