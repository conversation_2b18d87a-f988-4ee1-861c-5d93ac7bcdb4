import { ROUTES } from '@/constants/routes-constants';
import { AdminRoutes } from '@/routes/AdminRoutes';
import { AuthRoutes } from '@/routes/AuthRoutes';
import { SuperAdminRoutes } from '@/routes/SuperAdminRoutes';
import SuspenseWrapper from '@/routes/SuspenseWrapper';
import { lazy } from 'react';
import { createBrowserRouter } from 'react-router-dom';
import RedirectBasedOnRole from './protected-routes/RedirectBasedonRole';

const AdminProtectedRoutes = lazy(
  () => import('./protected-routes/AdminProtectedRoutes')
);
const SuperAdminProtectedRoutes = lazy(
  () => import('./protected-routes/SuperAdminProtectedRoutes')
);

export const router = createBrowserRouter([
  {
    path: ROUTES.HOME,
    element: (
      <SuspenseWrapper>
        <RedirectBasedOnRole />
      </SuspenseWrapper>
    ),
  },
  {
    element: (
      <SuspenseWrapper>
        <AdminProtectedRoutes />
      </SuspenseWrapper>
    ),
    children: AdminRoutes,
  },
  {
    element: (
      <SuspenseWrapper>
        <AdminProtectedRoutes />
      </SuspenseWrapper>
    ),
    path: ROUTES.HOME,
    children: AdminRoutes,
  },

  {
    element: (
      <SuspenseWrapper>
        <SuperAdminProtectedRoutes />
      </SuspenseWrapper>
    ),
    path: ROUTES.SUPER_ADMIN,
    children: SuperAdminRoutes,
  },

  ...AuthRoutes,
]);
