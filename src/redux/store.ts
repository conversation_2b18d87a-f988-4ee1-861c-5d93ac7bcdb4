import { configureStore } from '@reduxjs/toolkit';
import accountingCustomersReducer from './features/accounting/accountingCustomersSlice';
import { adjustmentsApi } from './features/accounting/adjustments.api';
import adjustmentsReducer from './features/accounting/adjustmentsSlice';
import { accountingInquiryApi } from './features/accounting/Inquiry.api';
import accountingInquiryReducer from './features/accounting/InquirySlice';
import { paymentsApi } from './features/accounting/payments.api';
import { authApi } from './features/auth/authApi';
import authReducer from './features/auth/authSlice';
import { commonApi } from './features/common-api/common.api';
import { companyApi } from './features/company/company.api';
import { countyList } from './features/country/country.api';
import { customerTypeApi } from './features/customer-type/customer-type.api';
import { choicesApi } from './features/customers/choices.api';
import { contactsApi } from './features/customers/contacts.api';
import { customerApi } from './features/customers/customer.api';
import customerReducer from './features/customers/customerSlice';
import { discountApi } from './features/customers/discount.api';
import discountReducer from './features/customers/discountSlice';
import { filesApi } from './features/customers/files';
import { notesApi } from './features/customers/notes.api';
import noteReducer from './features/customers/noteSlice';
import { deliveryTypeApi } from './features/delivery-type/delivery-type.api';
import { esignApi } from './features/e-sign/e-sign.api';
import { enumsApi } from './features/enums-api/enums-api';
import { inventoryManagerApi } from './features/inventory-manager/inventory-manager.api';
import bulkItemReducer from './features/items/bulkItemSlice';
import { itemApi } from './features/items/item.api';
import itemReducer from './features/items/itemSlice';
import { linkedFilesApi } from './features/items/linkedFiles.api';
import { listApi } from './features/list/category/list.api';
import deliveryLocationReducer from './features/list/delivery-location/deliveryLocationSlice';
import { onlinePaymentApi } from './features/online-payment/online-payment.api';
import { additionalInfoApi } from './features/orders/additional-info.api';
import { additionalItemInfoApi } from './features/orders/additional-item-info.api';
import { itemDetailsApi } from './features/orders/item-details.api';
import { orderApi } from './features/orders/order.api';
import ordersReducer from './features/orders/orderSlice';
import { paymentTermApi } from './features/payment-term/payement-term.api';
import { paymentTypeApi } from './features/payment-type/payment-type.api';
import { processesApi } from './features/processes/processes.api';
import { purchaseOrderApi } from './features/purchase-order/purchaseOrder.api';
import purchaseOrderReducer from './features/purchase-order/purchaseOrderSlice';
import { referralTypeApi } from './features/referral-type/referral-type.api';
import { salesTaxCodeApi } from './features/sales-tax-code/sales-tax-code.api';
import sessionReducer from './features/sessions/sessionSlice';
import { stateList } from './features/state/state.api';
import { storeApi } from './features/store/store.api';
import { subRentalApi } from './features/sub-rental/subRental.api';
import subRentalReducer from './features/sub-rental/subRentalSlice';
import { customerSubTypes } from './features/sub-types/sub-types.api';
import customerSubTypesReducer from './features/sub-types/subTypesSlice';
import { systemUserApi } from './features/system/users.api';
import { accountAPI } from './features/tenant/tenant.api';
import { timezoneList } from './features/timezone/timezone.api';
import { imagesApi } from './features/user-options/images.api';
import { loginProfileApi } from './features/user-options/login-profile-api';
import loginProfileReducer from './features/user-options/loginProfileSlice';
import { profileDetailsApi } from './features/user-options/profile-details.api';
import { vendorsApi } from './features/vendors-api/vendors.api';
import vendorReducer from './features/vendors-api/vendorSlice';
import warehouseReducer from './features/warehouse/warehouseSlice';
import { warehouseCustomPickupReturnApi } from './features/warehouse/warehouseCustomPickupReturn.api';
import { warehouseOrderDeliveriesPickupsApi } from './features/warehouse/warehouseOrderDeliveriesPickups.api';
import { warehouseSubrentalPickupsReturnsApi } from './features/warehouse/warehouseSubrentalPickupsReturns.api';
import { truckItinerariesApi } from './features/warehouse/truckItineraries.api';
import ccProcessingReducer from './features/accounting/ccProcessingSlice';

export const store = configureStore({
  reducer: {
    [authApi.reducerPath]: authApi.reducer,
    [customerApi.reducerPath]: customerApi.reducer,
    [itemApi.reducerPath]: itemApi.reducer,
    [customerTypeApi.reducerPath]: customerTypeApi.reducer,
    [paymentTermApi.reducerPath]: paymentTermApi.reducer,
    [paymentTypeApi.reducerPath]: paymentTypeApi.reducer,
    [salesTaxCodeApi.reducerPath]: salesTaxCodeApi.reducer,
    [referralTypeApi.reducerPath]: referralTypeApi.reducer,
    [deliveryTypeApi.reducerPath]: deliveryTypeApi.reducer,
    [listApi.reducerPath]: listApi.reducer,
    [countyList.reducerPath]: countyList.reducer,
    [stateList.reducerPath]: stateList.reducer,
    [timezoneList.reducerPath]: timezoneList.reducer,
    [accountAPI.reducerPath]: accountAPI.reducer,
    [customerSubTypes.reducerPath]: customerSubTypes.reducer,
    [notesApi.reducerPath]: notesApi.reducer,
    [discountApi.reducerPath]: discountApi.reducer,
    [choicesApi.reducerPath]: choicesApi.reducer,
    [commonApi.reducerPath]: commonApi.reducer,
    [contactsApi.reducerPath]: contactsApi.reducer,
    [filesApi.reducerPath]: filesApi.reducer,
    [profileDetailsApi.reducerPath]: profileDetailsApi.reducer,
    [companyApi.reducerPath]: companyApi.reducer,
    [imagesApi.reducerPath]: imagesApi.reducer,
    [enumsApi.reducerPath]: enumsApi.reducer,
    [loginProfileApi.reducerPath]: loginProfileApi.reducer,
    [storeApi.reducerPath]: storeApi.reducer,
    [linkedFilesApi.reducerPath]: linkedFilesApi.reducer,
    [adjustmentsApi.reducerPath]: adjustmentsApi.reducer,
    [vendorsApi.reducerPath]: vendorsApi.reducer,
    [systemUserApi.reducerPath]: systemUserApi.reducer,
    [orderApi.reducerPath]: orderApi.reducer,
    [purchaseOrderApi.reducerPath]: purchaseOrderApi.reducer,
    [itemDetailsApi.reducerPath]: itemDetailsApi.reducer,
    [additionalItemInfoApi.reducerPath]: additionalItemInfoApi.reducer,
    [additionalInfoApi.reducerPath]: additionalInfoApi.reducer,
    [inventoryManagerApi.reducerPath]: inventoryManagerApi.reducer,
    [onlinePaymentApi.reducerPath]: onlinePaymentApi.reducer,
    [subRentalApi.reducerPath]: subRentalApi.reducer,
    [esignApi.reducerPath]: esignApi.reducer,
    [warehouseCustomPickupReturnApi.reducerPath]:
      warehouseCustomPickupReturnApi.reducer,
    [warehouseOrderDeliveriesPickupsApi.reducerPath]:
      warehouseOrderDeliveriesPickupsApi.reducer,
    [processesApi.reducerPath]: processesApi.reducer,
    [accountingInquiryApi.reducerPath]: accountingInquiryApi.reducer,
    [warehouseSubrentalPickupsReturnsApi.reducerPath]:
      warehouseSubrentalPickupsReturnsApi.reducer,
    [truckItinerariesApi.reducerPath]: truckItinerariesApi.reducer,
    [paymentsApi.reducerPath]: paymentsApi.reducer,

    customer: customerReducer,
    item: itemReducer,
    bulkItem: bulkItemReducer,
    auth: authReducer,
    discount: discountReducer,
    notes: noteReducer,
    subTypes: customerSubTypesReducer,
    loginProfile: loginProfileReducer,
    deliveryLocation: deliveryLocationReducer,
    adjustment: adjustmentsReducer,
    orders: ordersReducer,
    vendor: vendorReducer,
    warehouse: warehouseReducer,
    session: sessionReducer,
    subRental: subRentalReducer,
    purchaseOrder: purchaseOrderReducer,
    accountingInquiry: accountingInquiryReducer,
    accountingCustomers: accountingCustomersReducer,
    ccProcessing: ccProcessingReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware()
      .concat(authApi.middleware)
      .concat(customerApi.middleware)
      .concat(itemApi.middleware)
      .concat(customerTypeApi.middleware)
      .concat(paymentTermApi.middleware)
      .concat(paymentTypeApi.middleware)
      .concat(salesTaxCodeApi.middleware)
      .concat(referralTypeApi.middleware)
      .concat(deliveryTypeApi.middleware)
      .concat(listApi.middleware)
      .concat(countyList.middleware)
      .concat(countyList.middleware)
      .concat(stateList.middleware)
      .concat(timezoneList.middleware)
      .concat(accountAPI.middleware)
      .concat(customerSubTypes.middleware)
      .concat(notesApi.middleware)
      .concat(discountApi.middleware)
      .concat(choicesApi.middleware)
      .concat(commonApi.middleware)
      .concat(contactsApi.middleware)
      .concat(filesApi.middleware)
      .concat(profileDetailsApi.middleware)
      .concat(companyApi.middleware)
      .concat(imagesApi.middleware)
      .concat(enumsApi.middleware)
      .concat(loginProfileApi.middleware)
      .concat(storeApi.middleware)
      .concat(linkedFilesApi.middleware)
      .concat(adjustmentsApi.middleware)
      .concat(vendorsApi.middleware)
      .concat(systemUserApi.middleware)
      .concat(orderApi.middleware)
      .concat(purchaseOrderApi.middleware)
      .concat(itemDetailsApi.middleware)
      .concat(additionalItemInfoApi.middleware)
      .concat(inventoryManagerApi.middleware)
      .concat(onlinePaymentApi.middleware)
      .concat(esignApi.middleware)
      .concat(warehouseCustomPickupReturnApi.middleware)
      .concat(warehouseOrderDeliveriesPickupsApi.middleware)
      .concat(processesApi.middleware)
      .concat(additionalInfoApi.middleware)
      .concat(subRentalApi.middleware)
      .concat(esignApi.middleware)
      .concat(accountingInquiryApi.middleware)
      .concat(warehouseSubrentalPickupsReturnsApi.middleware)
      .concat(truckItinerariesApi.middleware)
      .concat(paymentsApi.middleware),
});

// Infer the `RootState` and `AppDispatch` types from the store itself
export type RootState = ReturnType<typeof store.getState>;
// Inferred type: {posts: PostsState, comments: CommentsState, users: UsersState}
export type AppDispatch = typeof store.dispatch;
