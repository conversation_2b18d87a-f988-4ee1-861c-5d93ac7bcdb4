import { UseToast } from '@/components/ui/toast/ToastContainer';
import { ONLINE_PAYMENTS_API_ROUTES } from '@/constants/api-constants';
import { baseQueryWithReauth } from '@/services/api';
import { ApiResponseDto, PaginationFilterPayload } from '@/types/common.types';
import {
  CancelLinkPayloadTypes,
  OnlinePaymentListTypes,
} from '@/types/orders/online-payments.types';
import { createApi } from '@reduxjs/toolkit/query/react';

// Define the API service using RTK Query
export const onlinePaymentApi = createApi({
  reducerPath: 'onlinePaymentApi',
  baseQuery: baseQueryWithReauth,
  tagTypes: ['PaymentList'],
  endpoints: (builder) => ({
    getOnlinePaymentData: builder.query<
      ApiResponseDto<OnlinePaymentListTypes[]>,
      PaginationFilterPayload
    >({
      query: (body) => ({
        url: ONLINE_PAYMENTS_API_ROUTES.GET,
        body,
        method: 'POST',
      }),
      providesTags: ['PaymentList'],
    }),
    cancelLink: builder.mutation<
      ApiResponseDto<null>,
      { body: CancelLinkPayloadTypes }
    >({
      query: ({ body }) => ({
        url: ONLINE_PAYMENTS_API_ROUTES.CANCEL,
        method: 'POST',
        body,
      }),
      transformResponse: (response) => {
        if (response.statusCode === 200) {
          UseToast().success(response.message);
        }
        return response.data;
      },
      invalidatesTags: ['PaymentList'],
    }),
    refreshList: builder.mutation<ApiResponseDto<null>, void>({
      query: () => ({
        url: ONLINE_PAYMENTS_API_ROUTES.REFRESH,
        method: 'GET',
      }),
      transformResponse: (response) => {
        if (response.statusCode === 200) {
          UseToast().success(response.message);
        }
        return response.data;
      },
      invalidatesTags: ['PaymentList'],
    }),
  }),
});

export const {
  useGetOnlinePaymentDataQuery,
  useCancelLinkMutation,
  useRefreshListMutation,
} = onlinePaymentApi;
