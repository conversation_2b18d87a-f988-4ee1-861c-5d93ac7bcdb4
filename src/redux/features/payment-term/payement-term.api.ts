import { PAYMENT_TERM_API_ROUTES } from '@/constants/api-constants';
import { baseQueryWithReauth } from '@/services/api';
import { PaymentTermType } from '@/types/customer.types';
import { createApi } from '@reduxjs/toolkit/query/react';

export const paymentTermApi = createApi({
  reducerPath: 'paymentTermApi',
  baseQuery: baseQueryWithReauth,
  endpoints: (builder) => ({
    getPaymentTerm: builder.query<PaymentTermType[], void>({
      query: () => ({
        url: PAYMENT_TERM_API_ROUTES.ALL,
        method: 'GET',
      }),
    }),
  }),
});

export const { useGetPaymentTermQuery } = paymentTermApi;
