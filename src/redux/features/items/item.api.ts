// Imports
import { UseToast } from '@/components/ui/toast/ToastContainer';
import {
  CONFIG_API,
  INVENTORY_ITEMS_API_ROUTES,
  ITEMS_API_ROUTES,
  QUANTITY_ITEMS_API_ROUTES,
} from '@/constants/api-constants';
import { baseQueryWithReauth } from '@/services/api';
import { ApiResponseDto, PaginationFilterPayload } from '@/types/common.types';
import {
  BulkItemList,
  CopyItemsType,
  ItemBriefType,
  ItemLookupDropdownType,
  ItemLookupTypes,
  KitItemsType,
  PackageItemsFormType,
  PackageItemsType,
  PurchaseDataSequenceItemsTypes,
} from '@/types/item.types';
import { createApi } from '@reduxjs/toolkit/query/react';

import {
  AddQuantityFormDataTypes,
  AdjustQuantityFormDataTypes,
  InventoryFormDataTypes,
  InventoryListItemTypes,
  QuantityDetailsTypes,
  QuantityListItemTypes,
  RemoveQuantityFormDataTypes,
} from '@/types/item.types';

// Create the API service for item-related operations
export const itemApi = createApi({
  reducerPath: 'itemApi',
  baseQuery: baseQueryWithReauth,
  tagTypes: [
    'Item',
    'KitItems',
    'PackageItems',
    'Quantity',
    'Inventory',
    'InventoryDetails',
    'PurchaseDate',
    'ValidateInventory',
  ],

  // Define the endpoints for the API
  endpoints: (builder) => ({
    // Get an item by its ID
    getItemById: builder.query<any, string>({
      query: (itemId) => ({
        url: ITEMS_API_ROUTES.GET(itemId),
        method: 'GET',
      }),
      providesTags: (result) =>
        result ? [{ type: 'Item', id: result.item_id }] : [],
    }),

    // Update an existing item
    updateItem: builder.mutation<
      any,
      { url: string; body: any; method: 'POST' | 'PUT' }
    >({
      query: ({ url, body, method = 'POST' }) => ({
        url,
        method,
        body,
      }),
      transformResponse: (response) => {
        if (response?.message) UseToast().success(response.message);
        return response;
      },
      invalidatesTags: ['Item'],
    }),

    // Delete an item by its ID
    deleteItem: builder.mutation<void, number>({
      query: (itemId) => ({
        url: ITEMS_API_ROUTES.DELETE(itemId),
        method: 'DELETE',
      }),
      transformResponse: (response) => {
        if (response?.message) UseToast().success(response.message);
        return response;
      },
    }),

    // Get available store locations
    getStoreLocations: builder.query<any, void>({
      query: () => ({
        url: CONFIG_API.storeLocations,
        method: 'GET',
      }),
    }),

    // Fetch item options with pagination and search filters
    getOptions: builder.query({
      query: ({ pageNumber, pageSize, searchTerm }) => ({
        url: ITEMS_API_ROUTES.ALL,
        method: 'POST',
        body: {
          pageNumber,
          pageSize,
          sortBy: 'itemId',
          filters: [
            { field: 'filter', value: searchTerm, operator: 'Contains' },
          ],
        },
      }),
    }),

    // Copy an item
    copyItem: builder.mutation<any, { body: CopyItemsType }>({
      query: ({ body }) => ({
        url: ITEMS_API_ROUTES.COPY_ITEM,
        method: 'POST',
        body,
      }),
      transformResponse: (response) => {
        if (response?.message) UseToast().success(response.message);
        return response;
      },
    }),

    // Get kit items by item ID
    getKitItems: builder.query<ApiResponseDto<KitItemsType[]>, string>({
      query: (id) => ITEMS_API_ROUTES.KIT_ITEM(id),
      providesTags: ['KitItems'],
    }),

    // Get package items by item ID
    getPackageItems: builder.query<ApiResponseDto<PackageItemsType[]>, string>({
      query: (id) => ITEMS_API_ROUTES.PACKAGE_ITEM(id),
      providesTags: ['PackageItems'],
    }),

    // Fetch item lookup data with pagination
    getItemLookup: builder.query<
      ApiResponseDto<ItemLookupTypes[]>,
      { url: string; body: PaginationFilterPayload }
    >({
      query: ({ url, body }) => ({
        url,
        method: 'POST',
        body,
      }),
      keepUnusedDataFor: 30,
    }),

    // Generic data query for fetching data with filters, sorting, and pagination
    getData: builder.query({
      query: ({
        url,
        pageNumber,
        pageSize,
        sortBy,
        sortAscending,
        filters,
      }) => ({
        url,
        method: 'POST',
        body: { pageNumber, pageSize, sortBy, sortAscending, filters },
      }),
      serializeQueryArgs: ({ endpointName, queryArgs }) =>
        `${endpointName}-${queryArgs.sortBy}-${queryArgs.sortAscending}-${JSON.stringify(queryArgs.filters)}`,
      merge: (currentCache, newData) => {
        if (newData?.data) currentCache.data.push(...newData.data);
      },
      forceRefetch: ({ currentArg, previousArg }) => currentArg !== previousArg,
    }),

    // Fetch item lookup dropdown data
    getItemLookupDropDown: builder.query<
      ApiResponseDto<ItemLookupDropdownType[]>,
      { url: string; body: PaginationFilterPayload }
    >({
      query: ({ url, body }) => ({
        url,
        method: 'POST',
        body,
      }),
      keepUnusedDataFor: 30,
    }),

    // Bulk edit items
    bulkEditItem: builder.mutation<any, { body: BulkItemList[] }>({
      query: ({ body }) => ({
        url: ITEMS_API_ROUTES.EDIT_BULK_ITEM,
        method: 'PUT',
        body,
      }),
      transformResponse: (response) => {
        if (response?.message) UseToast().success(response.message);
        return response;
      },
    }),

    // Get brief details of an item by its ID
    getItemBrief: builder.mutation<ApiResponseDto<ItemBriefType>, string>({
      query: (itemId) => ITEMS_API_ROUTES.ITEM_BRIEF(itemId),
    }),

    // Update kit items for an item
    updateKitItem: builder.mutation<
      ApiResponseDto<KitItemsType[]>,
      { itemId: string; body: KitItemsType[] }
    >({
      query: ({ itemId, body }) => ({
        url: ITEMS_API_ROUTES.UPDATE_KIT(itemId),
        method: 'POST',
        body,
      }),
      transformResponse: (response) => {
        if (response?.message) UseToast().success(response.message);
        return response;
      },
      invalidatesTags: ['KitItems'],
    }),

    // Update package items for an item
    updatePackageItem: builder.mutation<
      ApiResponseDto<PackageItemsType[]>,
      { itemId: string; body: PackageItemsFormType[] }
    >({
      query: ({ itemId, body }) => ({
        url: ITEMS_API_ROUTES.UPDATE_PACKAGE(itemId),
        method: 'POST',
        body,
      }),
      transformResponse: (response) => {
        if (response?.message) UseToast().success(response.message);
        return response;
      },
      invalidatesTags: ['PackageItems'],
    }),

    // Quantity APIs
    getQuantityList: builder.query<
      ApiResponseDto<QuantityListItemTypes>,
      {
        itemId: string;
        groupSelect: string;
        itemQuantityId?: string;
        groupBy: 'PURCHASE' | 'LOCATION';
      }
    >({
      query: ({ itemId, groupSelect, itemQuantityId, groupBy }) => ({
        url: QUANTITY_ITEMS_API_ROUTES.ALL,
        params: { itemId, groupSelect, itemQuantityId, groupBy },
      }),
      providesTags: ['Quantity'],
    }),

    // Add quantity to an item
    addQuantity: builder.mutation<
      ApiResponseDto<AddQuantityFormDataTypes>,
      { quantityData: AddQuantityFormDataTypes }
    >({
      query: ({ quantityData }) => ({
        url: QUANTITY_ITEMS_API_ROUTES.ADD,
        method: 'POST',
        body: quantityData,
      }),
      transformResponse: (response) => {
        if (response?.message) UseToast().success(response.message);
        return response;
      },
      invalidatesTags: [
        'Quantity',
        'Item',
        'Inventory',
        'PurchaseDate',
        'ValidateInventory',
      ],
    }),

    // Fetch quantity details by ID
    getQuantityItemDetailsById: builder.query<
      ApiResponseDto<QuantityDetailsTypes>,
      number
    >({
      query: (id) => QUANTITY_ITEMS_API_ROUTES.GET(id),
    }),

    // Update quantity for an item
    updateQuantity: builder.mutation<
      ApiResponseDto<QuantityDetailsTypes>,
      { quantityData: QuantityDetailsTypes }
    >({
      query: ({ quantityData }) => ({
        url: QUANTITY_ITEMS_API_ROUTES.UPDATE,
        method: 'POST',
        body: quantityData,
      }),
      transformResponse: (response) => {
        if (response?.message) UseToast().success(response.message);
        return response;
      },
      invalidatesTags: ['Quantity', 'Item', 'Inventory', 'ValidateInventory'],
    }),

    // Adjust quantity for an item
    updateAdjustQuantity: builder.mutation<
      ApiResponseDto<AdjustQuantityFormDataTypes>,
      { quantityData: AdjustQuantityFormDataTypes }
    >({
      query: ({ quantityData }) => ({
        url: QUANTITY_ITEMS_API_ROUTES.ADJUST,
        method: 'POST',
        body: quantityData,
      }),
      transformResponse: (response) => {
        if (response?.message) UseToast().success(response.message);
        return response;
      },
      invalidatesTags: ['Quantity', 'Item', 'Inventory', 'ValidateInventory'],
    }),

    // Remove quantity from an item
    removeQuantity: builder.mutation<
      ApiResponseDto<RemoveQuantityFormDataTypes>,
      { quantityData: RemoveQuantityFormDataTypes }
    >({
      query: ({ quantityData }) => ({
        url: QUANTITY_ITEMS_API_ROUTES.REMOVE,
        method: 'POST',
        body: quantityData,
      }),
      transformResponse: (response) => {
        if (response?.message) UseToast().success(response.message);
        return response;
      },
      invalidatesTags: ['Quantity', 'Item', 'Inventory', 'ValidateInventory'],
    }),

    // Inventory APIs
    getInventoryList: builder.query<
      ApiResponseDto<InventoryListItemTypes[]>,
      string
    >({
      query: (id) => ({
        url: INVENTORY_ITEMS_API_ROUTES.ALL(id),
        method: 'POST',
        body: {
          pageNumber: -1,
          pageSize: -1,
          filters: [],
          sortBy: '',
        },
      }),
      providesTags: ['Inventory'],
    }),

    // Fetch inventory details by ID
    getInventoryDetailsById: builder.query<
      ApiResponseDto<InventoryFormDataTypes>,
      number
    >({
      query: (id) => INVENTORY_ITEMS_API_ROUTES.GET(id),
      providesTags: ['InventoryDetails'],
    }),

    // Update inventory data for an item
    saveInventory: builder.mutation<
      ApiResponseDto<InventoryFormDataTypes>,
      {
        inventoryData: InventoryListItemTypes[];
        itemId: string;
      }
    >({
      query: ({ inventoryData, itemId }) => ({
        url: INVENTORY_ITEMS_API_ROUTES.SAVE(itemId),
        method: 'POST',
        body: inventoryData,
      }),
      transformResponse: (response) => {
        if (response?.message) UseToast().success(response.message);
        return response;
      },
      invalidatesTags: ['Inventory', 'InventoryDetails', 'ValidateInventory'],
    }),

    // Delete inventory data by ID
    deleteInventory: builder.mutation<ApiResponseDto<null>, number>({
      query: (id) => ({
        url: INVENTORY_ITEMS_API_ROUTES.DELETE(id),
        method: 'DELETE',
      }),
      transformResponse: (response) => {
        if (response?.message) UseToast().success(response.message);
        return response;
      },
      invalidatesTags: ['Inventory', 'PurchaseDate', 'ValidateInventory'],
    }),

    // validate inventory quantity
    validateInventory: builder.query<ApiResponseDto<string>, string>({
      query: (id) => ({
        url: INVENTORY_ITEMS_API_ROUTES.VALIDATE_QUANTITY(id),
        method: 'GET',
      }),
      providesTags: ['ValidateInventory'],
    }),

    // get purchase date sequence dropdown list
    getPurchaseDateSequence: builder.query<
      ApiResponseDto<PurchaseDataSequenceItemsTypes[]>,
      string
    >({
      query: (id) => INVENTORY_ITEMS_API_ROUTES.PURCHASEDATE_SEQUENCE(id),
      providesTags: ['PurchaseDate'],
    }),

    // get purchase date sequence dropdown list
    getBulkItems: builder.query<ApiResponseDto<any[]>, any>({
      query: (body) => ({
        url: ITEMS_API_ROUTES.ALL,
        method: 'POST',
        body,
      }),
      keepUnusedDataFor: 30,
    }),
  }),
});

// Export hooks for usage in components
export const {
  useGetItemByIdQuery,
  useUpdateItemMutation,
  useDeleteItemMutation,
  useGetStoreLocationsQuery,
  useGetOptionsQuery,
  useGetKitItemsQuery,
  useBulkEditItemMutation,
  useGetItemBriefMutation,
  useUpdateKitItemMutation,
  useUpdatePackageItemMutation,
  useGetPackageItemsQuery,
  useGetItemLookupQuery,
  useGetItemLookupDropDownQuery,
  useCopyItemMutation,
  useGetDataQuery,
  useLazyGetDataQuery,
  useGetQuantityListQuery,
  useAddQuantityMutation,
  useGetQuantityItemDetailsByIdQuery,
  useUpdateQuantityMutation,
  useRemoveQuantityMutation,
  useUpdateAdjustQuantityMutation,
  useGetInventoryListQuery,
  useGetInventoryDetailsByIdQuery,
  useSaveInventoryMutation,
  useDeleteInventoryMutation,
  useGetPurchaseDateSequenceQuery,
  useGetBulkItemsQuery,
  useValidateInventoryQuery,
} = itemApi;
