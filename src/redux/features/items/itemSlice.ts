import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// Define the interface for filter state
interface ItemState {
  filters: Array<{
    label: string;
    value: string;
    name: string;
    operator: string;
  }>;
  formValues: {
    itemId: string;
    description: string;
    category: string;
    location: string | null;
    unitPrice: string;
    replacementCharge: string;
  };
}

// Initial state with default values matching the Filter component
const initialState: ItemState = {
  filters: [],
  formValues: {
    itemId: '',
    category: '',
    description: '',
    location: '',
    unitPrice: '',
    replacementCharge: '',
  },
};

const itemSlice = createSlice({
  name: 'item',
  initialState,
  reducers: {
    // Set filters based on form data
    setItemFilter(
      state,
      action: PayloadAction<
        Array<{ label: string; value: string; name: string; operator: string }>
      >
    ) {
      state.filters = action.payload;
    },

    // Update form values
    updateItemFormValues(
      state,
      action: PayloadAction<Partial<ItemState['formValues']>>
    ) {
      state.formValues = {
        ...state.formValues,
        ...action.payload,
      };
    },

    // Clear all filters and reset form values
    clearAllItemFilters(state) {
      state.filters = [];
      state.formValues = initialState.formValues;
    },

    // Clear a specific filter
    clearItemFilter(state, action: PayloadAction<string>) {
      state.filters = state.filters.filter(
        (filter) => filter.name !== action.payload
      );
      switch (action.payload) {
        case 'itemId':
          state.formValues.itemId = '';
          break;
        case 'description':
          state.formValues.description = '';
          break;
        case 'category':
          state.formValues.category = '';
          break;
        case 'itemLocation':
          state.formValues.location = '';
          break;
        case 'unitPrice':
          state.formValues.unitPrice = '';
          break;
        case 'replacementCharge':
          state.formValues.replacementCharge = '';
          break;
      }
    },
  },
});

export const {
  setItemFilter,
  clearItemFilter,
  clearAllItemFilters,
  updateItemFormValues,
} = itemSlice.actions;

export default itemSlice.reducer;
