import { ITEM_LINKED_FILE_API_ROUTES } from '@/constants/api-constants';
import { baseQueryWithReauth } from '@/services/api';
import { ApiResponseDto } from '@/types/common.types';
import { LinkedFilesTypes } from '@/types/item.types';
import { createApi } from '@reduxjs/toolkit/query/react';

// Define the API service using RTK Query
export const linkedFilesApi = createApi({
  reducerPath: 'linkedFilesApi',
  baseQuery: baseQueryWithReauth,
  tagTypes: ['LinkedFiles'],
  endpoints: (builder) => ({
    // GET request to fetch the list of linked files
    getLinkedFilesListById: builder.query<
      ApiResponseDto<LinkedFilesTypes[]>,
      string
    >({
      query: (id) => ITEM_LINKED_FILE_API_ROUTES.GET(id),
      providesTags: (_result, _error, id) => [{ type: 'LinkedFiles', id }],
    }),

    deleteLinkedFile: builder.mutation<
      ApiResponseDto<LinkedFilesTypes>,
      number
    >({
      query: (fileId) => {
        return {
          url: ITEM_LINKED_FILE_API_ROUTES.DELETE(fileId),
          method: 'DELETE',
        };
      },
      // Invalidate the specific linked files list associated with the item
      invalidatesTags: (_result, _error, fileId) => {
        return [{ type: 'LinkedFiles', id: fileId }];
      },
    }),
    defaultLinkedFile: builder.mutation<
      ApiResponseDto<LinkedFilesTypes>,
      number
    >({
      query: (fileId) => {
        return {
          url: ITEM_LINKED_FILE_API_ROUTES.DEFAULT(fileId),
          method: 'POST',
          body: { linkedFileId: fileId },
        };
      },
      // Invalidate the specific linked files list associated with the item
      invalidatesTags: (_result, _error, fileId) => {
        return [{ type: 'LinkedFiles', id: fileId }];
      },
    }),

    uploadLinkedFiles: builder.mutation<
      ApiResponseDto<any>,
      { url: string; body: FormData }
    >({
      query: ({ url, body }) => {
        return {
          url: url,
          method: 'POST',
          body: body,
        };
      },
    }),
  }),
});

export const {
  useGetLinkedFilesListByIdQuery,
  useUploadLinkedFilesMutation,
  useDeleteLinkedFileMutation,
  useDefaultLinkedFileMutation,
} = linkedFilesApi;
