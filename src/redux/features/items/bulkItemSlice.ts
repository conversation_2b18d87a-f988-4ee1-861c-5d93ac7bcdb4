import { ItemFiltersType } from '@/types/item.types';
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface ItemState {
  filters: Array<ItemFiltersType>;
  formValues: {
    itemId: string;
    description: string;
    category: string;
    location: string | null;
    unitPrice: string;
    replacementCharge: string;
    isActive: string;
    sequenceNo: string;
    quantity: string;
  };
}

const initialState: ItemState = {
  filters: [],
  formValues: {
    itemId: '',
    description: '',
    category: '',
    location: '',
    unitPrice: '',
    replacementCharge: '',
    isActive: '',
    sequenceNo: '',
    quantity: '',
  },
};

// Utility function to reset form values
const resetFormValues = (): ItemState['formValues'] => ({
  itemId: '',
  description: '',
  category: '',
  location: '',
  unitPrice: '',
  replacementCharge: '',
  isActive: '',
  sequenceNo: '',
  quantity: '',
});

// Mapping filter field names to form values
const resetFormValueMap: Record<string, keyof ItemState['formValues']> = {
  itemId: 'itemId',
  description: 'description',
  category: 'category',
  itemLocation: 'location',
  unitPrice: 'unitPrice',
  replacementCharge: 'replacementCharge',
  isActive: 'isActive',
  sequenceNo: 'sequenceNo',
  quantity: 'quantity',
};

const bulkItemSlice = createSlice({
  name: 'bulkItem',
  initialState,
  reducers: {
    setItemFilter(
      state,
      action: PayloadAction<
        { label: string; value: string; name: string; operator: string }[]
      >
    ) {
      state.filters = action.payload;
    },
    updateItemFormValues(
      state,
      action: PayloadAction<Partial<ItemState['formValues']>>
    ) {
      Object.assign(state.formValues, action.payload);
    },
    clearAllItemFilters(state) {
      state.filters = [];
      state.formValues = resetFormValues();
    },
    clearItemFilter(state, action: PayloadAction<string>) {
      state.filters = state.filters.filter(
        (filter) => filter.name !== action.payload
      );

      // Safely access form field name using resetFormValueMap
      const formField = resetFormValueMap[action.payload];
      if (formField) {
        state.formValues[formField] = ''; // Reset the corresponding field
      }
    },
  },
});

export const {
  setItemFilter,
  clearItemFilter,
  clearAllItemFilters,
  updateItemFormValues,
} = bulkItemSlice.actions;
export default bulkItemSlice.reducer;
