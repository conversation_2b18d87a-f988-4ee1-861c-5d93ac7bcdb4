import { UseToast } from '@/components/ui/toast/ToastContainer';
import { SUB_RENTAL_API_ROUTES } from '@/constants/api-constants';
import { baseQueryWithReauth } from '@/services/api';
import { ApiResponseDto, PaginationFilterPayload } from '@/types/common.types';
import { CustomerVendorLookupTypes } from '@/types/sub-rentals.types';
import { createApi } from '@reduxjs/toolkit/query/react';

// Define the API service using RTK Query
export const subRentalApi = createApi({
  reducerPath: 'subRentalApi',
  baseQuery: baseQueryWithReauth,
  tagTypes: ['subRental', 'subRentalColumns', 'subRentalItems'],
  endpoints: (builder) => ({
    getSubRentalColums: builder.query<ApiResponseDto<any>, void>({
      query: () => SUB_RENTAL_API_ROUTES.GET_COLUMN,
      providesTags: ['subRentalColumns'],
    }),

    updateSubRentalColums: builder.mutation<ApiResponseDto<any>, any>({
      query: (body) => ({
        url: SUB_RENTAL_API_ROUTES.UPDATE_COLUMN,
        method: 'POST',
        body,
      }),
      transformResponse: (response) => {
        if (response?.message) {
          UseToast().success(response.message);
        }
        // Return the response as it is after processing it
        return response;
      },

      invalidatesTags: ['subRentalColumns'],
    }),

    // GET request to fetch the company data
    getSubRental: builder.query<ApiResponseDto<any>, string>({
      query: (id) => SUB_RENTAL_API_ROUTES.GET(id),
      providesTags: ['subRental'],
    }),

    addUpdateSubRentalInfo: builder.mutation<ApiResponseDto<any>, any>({
      query: (data) => ({
        url: SUB_RENTAL_API_ROUTES.ADD_UPDATE,
        method: 'POST',
        body: data,
      }),

      transformResponse: (response) => {
        if (response?.message) {
          UseToast().success(response.message);
        }
        return response;
      },

      invalidatesTags: ['subRental'],
    }),

    deleteSubRental: builder.mutation<
      any,
      {
        id: string | number | null;
        deletedBy: string;
        deletedReason: string;
        deletedOn: string;
      }
    >({
      query: (data) => ({
        url: SUB_RENTAL_API_ROUTES.DELETE,
        method: 'DELETE',
        body: data,
      }),
      transformResponse: (response: any) => {
        if (response?.message) {
          UseToast().success(response.message);
        }
        return response;
      },
      invalidatesTags: ['subRental'],
    }),

    customerVendorSearch: builder.query<
      ApiResponseDto<CustomerVendorLookupTypes[]>,
      { body: PaginationFilterPayload }
    >({
      query: ({ body }) => ({
        url: SUB_RENTAL_API_ROUTES.CUSTOMER_VENDRO_LOOKUP,
        method: 'POST',
        body,
      }),
    }),

    getSubRentalItems: builder.query<ApiResponseDto<any>, string | number>({
      query: (subRentId) => ({
        url: SUB_RENTAL_API_ROUTES.LIST_SUB_RENTAL_ITEMS(subRentId),
      }),
      providesTags: ['subRentalItems'],
    }),

    addUpdateSubRentItems: builder.mutation<
      ApiResponseDto<any>,
      { subRentId: number | string; data: any }
    >({
      query: ({ subRentId, data }) => ({
        url: SUB_RENTAL_API_ROUTES.SAVE_SUB_RENTAL_ITEMS(subRentId),
        method: 'POST',
        body: data,
      }),
      transformResponse: (response) => {
        if (response?.message) {
          UseToast().success(response.message);
        }
        return response;
      },

      invalidatesTags: ['subRentalItems'],
    }),
    deleteSubRentalItem: builder.mutation<
      any,
      {
        subRentId: string | number;
        itemId: string | number;
      }
    >({
      query: ({ subRentId, itemId }) => ({
        url: SUB_RENTAL_API_ROUTES.DELETE_SUB_RENTAL_ITEMS(subRentId, itemId),
        method: 'DELETE',
      }),
      transformResponse: (response: any) => {
        if (response?.message) {
          UseToast().success(response.message);
        }
        return response;
      },
      invalidatesTags: ['subRentalItems'],
    }),
  }),
});

export const {
  useDeleteSubRentalMutation,
  useGetSubRentalQuery,
  useAddUpdateSubRentalInfoMutation,
  useGetSubRentalColumsQuery,
  useUpdateSubRentalColumsMutation,
  useCustomerVendorSearchQuery,
  useGetSubRentalItemsQuery,
  useAddUpdateSubRentItemsMutation,
  useDeleteSubRentalItemMutation,
} = subRentalApi;
