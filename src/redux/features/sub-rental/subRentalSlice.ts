import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// Define the interface for filter state
interface SubRentalState {
  filters: Array<{
    label: string;
    value: string;
    name: string;
    operator: string;
  }>;
  formValues: {
    type: string;
    range: string;
    customer: string;
    'sub-rental-no': string;
    operator: string;
    searchValue: string;
    searchBy: string;
  };
}

// Initial state with default values matching the Filter component
const initialState: SubRentalState = {
  filters: [],
  formValues: {
    customer: '',
    'sub-rental-no': '',
    operator: 'Contains',
    searchBy: '',
    type: '',
    range: '',
    searchValue: '',
  },
};

const subRentalSlice = createSlice({
  name: 'subRental',
  initialState,
  reducers: {
    // Set filters based on form data
    setFilter(
      state,
      action: PayloadAction<
        Array<{ label: string; value: string; name: string; operator: string }>
      >
    ) {
      state.filters = action.payload;
    },

    // Update form values
    updateFormValues(
      state,
      action: PayloadAction<Partial<SubRentalState['formValues']>>
    ) {
      state.formValues = {
        ...state.formValues,
        ...action.payload,
      };
    },

    // Clear all filters and reset form values
    clearAllFilters(state) {
      state.filters = [];
      state.formValues = initialState.formValues;
    },

    // Clear a specific filter
    clearFilter(state, action: PayloadAction<string>) {
      state.filters = state.filters.filter(
        (filter) => filter.name !== action.payload
      );
      switch (action.payload) {
        case 'type':
          state.formValues.type = '';
          break;
        case 'range':
          state.formValues.range = '';
          break;
        case 'sub-rental-no':
        case 'customer':
          state.formValues.searchBy = '';
          state.formValues.operator = 'Contains';
          state.formValues.searchValue = '';
          break;
        // Add more cases as needed
      }
    },
  },
});

export const { setFilter, clearFilter, clearAllFilters, updateFormValues } =
  subRentalSlice.actions;

export default subRentalSlice.reducer;
