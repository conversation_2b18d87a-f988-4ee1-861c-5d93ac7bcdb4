import { CUSTOMERS_API } from '@/constants/api-constants';
import { baseQueryWithReauth } from '@/services/api';
import { CountryType, StateType } from '@/types/customer.types';

import { createApi } from '@reduxjs/toolkit/query/react';

export const countyList = createApi({
  reducerPath: 'countyList',
  baseQuery: baseQueryWithReauth,
  tagTypes: ['CountryType', 'StateType'],
  endpoints: (builder) => ({
    getCountryList: builder.query<CountryType[], void>({
      query: () => ({
        url: CUSTOMERS_API.LOCATION.COUNTRY_LIST,
        method: 'GET',
      }),
      keepUnusedDataFor: Infinity,
      onQueryStarted: async (_args, { queryFulfilled }) => {
        try {
          await queryFulfilled;
        } catch (error) {}
      },
      transformResponse: (response: {
        success: boolean;
        statusCode: number;
        message: string;
        data: CountryType[];
      }) => {
        if (response.statusCode === 200) {
          return response.data; // Return the data directly
        }
        return []; // Fallback to an empty array in case of failure
      },
      providesTags: (result) =>
        result ? [{ type: 'CountryType', id: 'LIST' }] : [],
    }),

    // New endpoint to get states by country
    getStateByCountry: builder.query<StateType[], { countryId: number }>({
      query: ({ countryId }) => ({
        url: `${CUSTOMERS_API.LOCATION.STATE_LIST}/${countryId}`, // Assuming API endpoint structure
        method: 'GET',
      }),
      keepUnusedDataFor: Infinity, // Keep data indefinitely in the cache
      transformResponse: (response: {
        success: boolean;
        statusCode: number;
        message: string;
        data: StateType[];
      }) => {
        if (response.statusCode === 200) {
          return response.data; // Return the data directly
        }
        return []; // Fallback to an empty array in case of failure
      },
      providesTags: (result) =>
        result ? [{ type: 'StateType', id: 'StateId' }] : [],
    }),
  }),
});

export const { useGetCountryListQuery, useGetStateByCountryQuery } = countyList;
