import { SUB_TYPES_API } from '@/constants/api-constants';
import { baseQueryWithReauth } from '@/services/api';
import { ApiResponseDto } from '@/types/common.types';
import { CustomerSubTypeDto } from '@/types/customer.types';

import { createApi } from '@reduxjs/toolkit/query/react';

export const customerSubTypes = createApi({
  reducerPath: 'customerSubTypes',
  baseQuery: baseQueryWithReauth,
  tagTypes: ['SubTypes'],
  endpoints: (builder) => ({
    getSubTypes: builder.mutation<ApiResponseDto<CustomerSubTypeDto[]>, number>(
      {
        query: (id) => ({
          url: SUB_TYPES_API.ALL(id),
          method: 'GET',
        }),
      }
    ),
    updateSubTypes: builder.mutation<
      any,
      { id: number; body: CustomerSubTypeDto[] }
    >({
      query: ({ id, body }) => ({
        url: SUB_TYPES_API.UPDATE(id),
        method: 'PUT',
        body: body,
      }),
    }),
  }),
});

export const { useGetSubTypesMutation, useUpdateSubTypesMutation } =
  customerSubTypes;
