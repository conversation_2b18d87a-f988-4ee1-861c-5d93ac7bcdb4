// src/redux/features/customerSubTypes/customerSubTypesSlice.ts
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { CustomerSubTypeDto } from '@/types/customer.types';

interface CustomerSubTypesState {
  selectedSubTypes: CustomerSubTypeDto[];
  subTypesData: CustomerSubTypeDto[];
}

const initialState: CustomerSubTypesState = {
  selectedSubTypes: [],
  subTypesData: [],
};

const customerSubTypesSlice = createSlice({
  name: 'customerSubTypes',
  initialState,
  reducers: {
    subTypesList: (state, action: PayloadAction<CustomerSubTypeDto[]>) => {
      state.subTypesData = action.payload;
    },
    setSelectedSubTypes: (
      state,
      action: PayloadAction<CustomerSubTypeDto[]>
    ) => {
      state.selectedSubTypes = action.payload;
    },
  },
});

export const { setSelectedSubTypes, subTypesList } =
  customerSubTypesSlice.actions;

export default customerSubTypesSlice.reducer;
