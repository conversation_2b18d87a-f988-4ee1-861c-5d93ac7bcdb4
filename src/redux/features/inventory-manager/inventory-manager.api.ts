import { INVENTORY_MANAGER_API_ROUTES } from '@/constants/api-constants';
import { baseQueryWithReauth } from '@/services/api';
import { ApiResponseDto } from '@/types/common.types';
import { createApi } from '@reduxjs/toolkit/query/react';

// Define the API service using RTK Query
export const inventoryManagerApi = createApi({
  reducerPath: 'inventoryManagerApi',
  baseQuery: baseQueryWithReauth,
  tagTypes: [],
  endpoints: (builder) => ({
    getInventoryManagerData: builder.query<ApiResponseDto<any>, any>({
      query: (body) => ({
        url: INVENTORY_MANAGER_API_ROUTES.GET_DATA,
        body,
        method: 'POST',
      }),
    }),
  }),
});

export const { useGetInventoryManagerDataQuery } = inventoryManagerApi;
