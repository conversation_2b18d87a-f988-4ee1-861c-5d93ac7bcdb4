import {
  DISCOUNT_API_ROUTES,
  ORDER_DELIVERY_CHARGE_API_ROUTES,
  ORDER_DISCOUNT_API_ROUTES,
} from '@/constants/api-constants';
import { baseQueryWithReauth } from '@/services/api';
import { ApiResponseDto } from '@/types/common.types';
import { DiscountsTypes } from '@/types/customer.types';
import { OrderDiscountsTypes } from '@/types/order.types';
import { createApi } from '@reduxjs/toolkit/query/react';

// Define the API slice
export const discountApi = createApi({
  reducerPath: 'discountApi',
  baseQuery: baseQueryWithReauth,
  tagTypes: ['Discount', 'OrderDiscount'],
  endpoints: (builder) => ({
    // Get discounts by customer ID (dynamic URL)
    getAllDiscount: builder.mutation<
      ApiResponseDto<DiscountsTypes[]>,
      { id: string; body: any }
    >({
      query: ({ id, body }) => ({
        url: DISCOUNT_API_ROUTES.ALL(id), // Pass customerId to the route
        method: 'POST',
        body: body,
      }),
    }),
    // Get discounts by for Order (dynamic URL)
    getOrderDiscounts: builder.mutation<
      ApiResponseDto<OrderDiscountsTypes[]>,
      { id: string; body: any }
    >({
      query: ({ id, body }) => ({
        url: ORDER_DISCOUNT_API_ROUTES.ALL(id), // Pass customerId to the route
        method: 'POST',
        body: body,
      }),
    }),
    //Update Customer discount
    updateDiscount: builder.mutation<any, DiscountsTypes[]>({
      query: (body) => ({
        url: DISCOUNT_API_ROUTES.UPDATE,
        method: 'PUT',
        body: body,
      }),
      invalidatesTags: [{ type: 'Discount', id: 'LIST' }],
    }),

    //Update Order discount
    updateOrderDiscount: builder.mutation<any, any>({
      query: (body) => ({
        url: ORDER_DISCOUNT_API_ROUTES.UPDATE(body.orderId),
        method: 'POST',
        body: body.discounts,
      }),
      invalidatesTags: [{ type: 'OrderDiscount', id: 'LIST' }],
    }),

    // apply bulk discount for customer
    updateBulkDiscount: builder.mutation<
      ApiResponseDto<null>,
      DiscountsTypes[]
    >({
      query: (body) => ({
        url: DISCOUNT_API_ROUTES.BULK_DISCOUNT,
        method: 'PUT',
        body: body,
      }),
      invalidatesTags: [{ type: 'Discount', id: 'LIST' }],
    }),

    // apply bulk discount for Order
    updateBulkDiscountOrder: builder.mutation<
      ApiResponseDto<null>,
      OrderDiscountsTypes[]
    >({
      query: (body) => ({
        url: ORDER_DISCOUNT_API_ROUTES.BULK_DISCOUNT,
        method: 'PUT',
        body: body,
      }),
      invalidatesTags: [{ type: 'OrderDiscount', id: 'LIST' }],
    }),

    getDeliveryCharges: builder.query<ApiResponseDto<any>, any>({
      query: (orderId) => ({
        url: ORDER_DELIVERY_CHARGE_API_ROUTES.GET(orderId),
        method: 'GET',
      }),
    }),

    getDeliveryChargesHistory: builder.query<ApiResponseDto<any>, any>({
      query: (orderId) => ({
        url: ORDER_DELIVERY_CHARGE_API_ROUTES.GET_HISTORY(orderId),
        method: 'GET',
      }),
    }),
    updateDeliveryCharges: builder.mutation<any, any>({
      query: (body) => ({
        url: ORDER_DELIVERY_CHARGE_API_ROUTES.UPDATE,
        method: 'POST',
        body: body,
      }),
    }),
  }),
});

// Export hooks for usage in components
export const {
  useGetAllDiscountMutation,
  useUpdateBulkDiscountMutation,
  useUpdateDiscountMutation,
  useGetOrderDiscountsMutation,
  useUpdateBulkDiscountOrderMutation,
  useUpdateOrderDiscountMutation,
  useGetDeliveryChargesQuery,
  useUpdateDeliveryChargesMutation,
  useGetDeliveryChargesHistoryQuery,
} = discountApi;
