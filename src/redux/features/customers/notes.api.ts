import { UseToast } from '@/components/ui/toast/ToastContainer';
import { CUSTOMER_NOTES_API } from '@/constants/api-constants';
import { baseQueryWithReauth } from '@/services/api';
import { ApiResponseDto } from '@/types/common.types';
import { CustomerNotesDto } from '@/types/customer.types';
import { createApi } from '@reduxjs/toolkit/query/react';

export const notesApi = createApi({
  reducerPath: 'notesApi',
  baseQuery: baseQueryWithReauth,
  tagTypes: ['Notes'], // Define the tag types that will be used for cache invalidation
  endpoints: (builder) => ({
    // Get customerNotes List by Customer ID
    getNotesByCustomerId: builder.query<
      ApiResponseDto<CustomerNotesDto[]>,
      number
    >({
      query: (id) => CUSTOMER_NOTES_API.GET(id), // Query endpoint to get notes by customer ID
      providesTags: (result) => (result ? [{ type: 'Notes', id: 'LIST' }] : []), // Tags for cache invalidation
    }),

    // Create a new note
    createNote: builder.mutation<ApiResponseDto<CustomerNotesDto>, any>({
      query: (newNote) => ({
        url: CUSTOMER_NOTES_API.CREATE,
        method: 'POST',
        body: newNote,
      }),
      transformResponse: (response) => {
        if (response?.message) {
          UseToast().success(response.message);
        }
        return response;
      },
      invalidatesTags: [{ type: 'Notes', id: 'LIST' }], // Invalidate LIST cache to trigger refetch
    }),

    // Get CustomerNote by NoteId
    getNoteById: builder.mutation<any, number>({
      query: (noteId) => ({
        url: CUSTOMER_NOTES_API.FIND(noteId),
        method: 'GET',
      }),
    }),

    // Update an existing note
    updateNote: builder.mutation<
      CustomerNotesDto,
      { noteId: number; noteData: any }
    >({
      query: ({ noteId, noteData }) => ({
        url: CUSTOMER_NOTES_API.UPDATE(noteId),
        method: 'PUT',
        body: noteData,
      }),
      transformResponse: (response) => {
        if (response?.message) {
          UseToast().success(response.message);
        }
        return response;
      },
      invalidatesTags: (_result, _error, { noteId }) => [
        { type: 'Notes', id: noteId }, // Invalidate the specific note cache
        { type: 'Notes', id: 'LIST' }, // Invalidate the LIST cache for refetch
      ],
    }),

    // Delete a note
    deleteNote: builder.mutation<void, number>({
      query: (noteId) => ({
        url: CUSTOMER_NOTES_API.DELETE(noteId),
        method: 'DELETE',
      }),
      transformResponse: (response) => {
        if (response?.message) {
          UseToast().success(response.message);
        }
        return response;
      },
      invalidatesTags: (_result, _error, noteId) => [
        { type: 'Notes', id: noteId }, // Invalidate the specific note cache
        { type: 'Notes', id: 'LIST' }, // Invalidate the LIST cache for refetch
      ],
    }),
  }),
});

// Export hooks for usage in components
export const {
  useCreateNoteMutation,
  useGetNoteByIdMutation,
  useUpdateNoteMutation,
  useDeleteNoteMutation,
  useGetNotesByCustomerIdQuery, // Added to access the getCustomerNotesById query
} = notesApi;
