import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface NoteState {
  open: {
    name: string; // Name of the dialog
    state: boolean; // Whether the dialog is open or closed
    id?: number | null; // The ID of the note if applicable
  };
}

const initialState: NoteState = {
  open: {
    name: '',
    state: false,
    id: null,
  },
};

const noteSlice = createSlice({
  name: 'notes',
  initialState,
  reducers: {
    // Action to open or close the dialog
    setDialog(
      state,
      action: PayloadAction<{ name: string; state: boolean; id: number | null }>
    ) {
      state.open = action.payload;
    },
  },
});

export const { setDialog } = noteSlice.actions;

export default noteSlice.reducer;
