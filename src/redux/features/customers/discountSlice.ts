// customerSlice.ts

import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface CustomerState {
  filters: Array<{ label: string; value: string; name: string }>;
}

const initialState: CustomerState = {
  filters: [],
};

const discountSlice = createSlice({
  name: 'discount',
  initialState,
  reducers: {
    setFilter(
      state,
      action: PayloadAction<
        Array<{ label: string; value: string; name: string }>
      >
    ) {
      state.filters = action.payload;
    },
    clearAllFilters(state) {
      state.filters = [];
    },
    clearFilter(state, action: PayloadAction<string>) {
      state.filters = state.filters.filter(
        (filter) => filter.label !== action.payload
      );
    },
  },
});

export const { setFilter, clearFilter, clearAllFilters } =
  discountSlice.actions;

export default discountSlice.reducer;
