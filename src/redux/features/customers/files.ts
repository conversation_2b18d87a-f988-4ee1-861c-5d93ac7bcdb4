import { FILES_API } from '@/constants/api-constants';
import { baseQueryWithReauth } from '@/services/api';
import { ApiResponseDto } from '@/types/common.types';
import { CustomerFilesDto } from '@/types/customer.types';
import { createApi } from '@reduxjs/toolkit/query/react';

export const filesApi = createApi({
  reducerPath: 'filesApi',
  baseQuery: baseQueryWithReauth,
  tagTypes: ['Files'], // Define the tag types that will be used for cache invalidation
  endpoints: (builder) => ({
    // Get Customer Files List by Customer ID
    getFilesByCustomerId: builder.query<
      ApiResponseDto<CustomerFilesDto[]>,
      number
    >({
      query: (id) => FILES_API.ALL(id),
      providesTags: (result) => (result ? [{ type: 'Files', id: 'LIST' }] : []),
    }),

    uploadFiles: builder.mutation<ApiResponseDto<CustomerFilesDto>, any>({
      query: ({ formData, customerId }: any) => ({
        url: FILES_API.UPLOAD(customerId), // The URL with customer ID
        method: 'POST',
        body: formData, // File data (FormData)
      }),
      invalidatesTags: [{ type: 'Files', id: 'LIST' }], // Invalidate cache for Files LIST to trigger refetch
    }),

    // Delete a File
    deletefile: builder.mutation<any, any>({
      query: ({ FileId }: { FileId: string }) => ({
        url: FILES_API.DELETE(FileId),
        method: 'DELETE',
      }),
      invalidatesTags: (_result, _error, FileId) => [
        { type: 'Files', id: FileId }, // Invalidate the specific File cache
        { type: 'Files', id: 'LIST' }, // Invalidate the LIST cache for refetch
      ],
    }),
  }),
});

// Export hooks for usage in components
export const {
  useDeletefileMutation,
  useGetFilesByCustomerIdQuery,
  useUploadFilesMutation,
} = filesApi;
