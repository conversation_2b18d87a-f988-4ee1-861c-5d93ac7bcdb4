import { CONTACTS_API_ROUTES, Phone_API } from '@/constants/api-constants';
import { baseQueryWithReauth } from '@/services/api';
import { ApiResponseDto } from '@/types/common.types';
import { AdditionalContactDto } from '@/types/customer.types';
import { createApi } from '@reduxjs/toolkit/query/react';

// Defining the contactsApi using RTK Query's createApi
export const contactsApi = createApi({
  reducerPath: 'contactsApi',
  baseQuery: baseQueryWithReauth,
  tagTypes: ['Contacts'],
  endpoints: (builder) => ({
    // Endpoint to get all contacts
    getAllContact: builder.mutation<
      ApiResponseDto<AdditionalContactDto[]>,
      { id: string; body: any }
    >({
      query: ({ id, body }) => ({
        url: CONTACTS_API_ROUTES.ALL(id),
        method: 'POST',
        body: body,
      }),
    }),

    // Endpoint to create a new contact
    createContact: builder.mutation<
      ApiResponseDto<AdditionalContactDto>,
      AdditionalContactDto
    >({
      query: (body) => ({
        url: CONTACTS_API_ROUTES.CREATE,
        method: 'POST',
        body: body,
      }),
    }),

    // Endpoint to get contact by ID
    getContactById: builder.mutation<any, number>({
      query: (contactId) => ({
        url: CONTACTS_API_ROUTES.GET(contactId),
        method: 'GET',
      }),
    }),

    // Endpoint to update an existing contact
    updateContact: builder.mutation<
      any,
      { contactId: number; contactData: AdditionalContactDto }
    >({
      query: ({ contactId, contactData }) => ({
        url: CONTACTS_API_ROUTES.UPDATE(contactId),
        method: 'PUT',
        body: contactData,
      }),
    }),

    // Endpoint to delete a contact by ID
    deleteContact: builder.mutation<void, number>({
      query: (contactId) => ({
        url: CONTACTS_API_ROUTES.DELETE(contactId),
        method: 'DELETE',
      }),
    }),

    // Endpoint to delete a phone number by ID
    deletePhoneNumber: builder.mutation<void, number>({
      query: (phoneId) => ({
        url: Phone_API.DELETE(phoneId),
        method: 'DELETE',
      }),
    }),
  }),
});

// Export hooks for each mutation to be used in React components
export const {
  useGetAllContactMutation,
  useCreateContactMutation,
  useGetContactByIdMutation,
  useUpdateContactMutation,
  useDeleteContactMutation,
  useDeletePhoneNumberMutation,
} = contactsApi;
