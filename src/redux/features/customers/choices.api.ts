import { CHOICES_API } from '@/constants/api-constants';
import { baseQueryWithReauth } from '@/services/api';
import { createApi } from '@reduxjs/toolkit/query/react';

export const choicesApi = createApi({
  reducerPath: 'choicesApi',
  baseQuery: baseQueryWithReauth,
  endpoints: (builder) => ({
    SalesPerson: builder.query<any, void>({
      query: () => ({
        url: CHOICES_API.SALES_PERSON,
        method: 'GET',
      }),
    }),
    FreeShipping: builder.query<any, void>({
      query: () => ({
        url: CHOICES_API.FREE_SHIPPING,
        method: 'GET',
      }),
    }),
    PaymentType: builder.query<any, void>({
      query: () => ({
        url: CHOICES_API.PAYMENT_TYPE,
        method: 'GET',
      }),
    }),
    RentStatus: builder.query<any, void>({
      query: () => ({
        url: CHOICES_API.RENT_STATUS,
        method: 'GET',
      }),
    }),
    PaymentTerms: builder.query<any, void>({
      query: () => ({
        url: CHOICES_API.PAYMENT_TERM,
        method: 'GET',
      }),
    }),
    includeInBatch: builder.query<any, void>({
      query: () => ({
        url: CHOICES_API.INCLUDE_IN_BATCH,
        method: 'GET',
      }),
    }),
    phoneType: builder.query<any, void>({
      query: () => ({
        url: CHOICES_API.PHONE_TYPE,
        method: 'GET',
      }),
    }),
    creditStatus: builder.query<any, void>({
      query: () => ({
        url: CHOICES_API.CREDIT_STATUS,
        method: 'GET',
      }),
    }),
    shippingCompanyType: builder.query<any, void>({
      query: () => ({
        url: CHOICES_API.SHIPPING_COMPANY,
        method: 'GET',
      }),
    }),
    communicationtype: builder.query<any, void>({
      query: () => ({
        url: CHOICES_API.SHIPPING_COMPANY,
        method: 'GET',
      }),
    }),
    locationSelection: builder.query<any, void>({
      query: () => ({
        url: CHOICES_API.LOCATION_SELECTION,
        method: 'GET',
      }),
    }),
  }),
});

export const {
  useFreeShippingQuery,
  usePaymentTypeQuery,
  usePaymentTermsQuery,
  useRentStatusQuery,
  useSalesPersonQuery,
  useIncludeInBatchQuery,
  usePhoneTypeQuery,
  useCreditStatusQuery,
  useShippingCompanyTypeQuery,
  useCommunicationtypeQuery,
  useLocationSelectionQuery,
} = choicesApi;
