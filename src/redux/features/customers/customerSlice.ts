import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// Define the interface for filter state
interface CustomerState {
  filters: Array<{
    label: string;
    value: string;
    name: string;
    operator: string;
  }>;
  formValues: {
    name: string;
    customerType: string;
    address1: string;
    city: string;
    state: string;
    phone: string;
    defstorelocation: string;
    isactive: string;
    zipCode: string;
    tel1: string;
  };
}

// Initial state with default values matching the Filter component
const initialState: CustomerState = {
  filters: [],
  formValues: {
    name: '',
    customerType: '',
    address1: '',
    city: '',
    state: '',
    phone: '',
    defstorelocation: '',
    isactive: '',
    zipCode: '',
    tel1: '+1',
  },
};

const customerSlice = createSlice({
  name: 'customer',
  initialState,
  reducers: {
    // Set filters based on form data
    setFilter(
      state,
      action: PayloadAction<
        Array<{ label: string; value: string; name: string; operator: string }>
      >
    ) {
      state.filters = action.payload;
    },

    // Update form values
    updateFormValues(
      state,
      action: PayloadAction<Partial<CustomerState['formValues']>>
    ) {
      state.formValues = {
        ...state.formValues,
        ...action.payload,
      };
    },

    // Clear all filters and reset form values
    clearAllFilters(state) {
      state.filters = [];
      state.formValues = initialState.formValues;
    },

    // Clear a specific filter
    clearFilter(state, action: PayloadAction<string>) {
      state.filters = state.filters.filter(
        (filter) => filter.name !== action.payload
      );
      switch (action.payload) {
        case 'first_name':
          state.formValues.name = '';
          break;
        case 'custtype_id':
          state.formValues.customerType = '';
          break;
        case 'isactive':
          state.formValues.isactive = '';
          break;
        case 'address1':
          state.formValues.address1 = '';
          break;
          break;
        case 'city':
          state.formValues.city = '';
          break;
        case 'state':
          state.formValues.state = '';
          break;
        case 'zipCode':
          state.formValues.zipCode = '';
          break;
        case 'defstorelocation':
          state.formValues.defstorelocation = '';
          break;
        case 'tel1':
          state.formValues.tel1 = '';
          break;
        // Add more cases as needed
      }
    },
  },
});

export const { setFilter, clearFilter, clearAllFilters, updateFormValues } =
  customerSlice.actions;

export default customerSlice.reducer;
