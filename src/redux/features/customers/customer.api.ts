import { UseToast } from '@/components/ui/toast/ToastContainer';
import {
  CUSTOMER_API_ROUTES,
  DEPARTMENT_API_ROUTES,
} from '@/constants/api-constants';
import { searchPayload } from '@/lib/utils';
import { baseQueryWithReauth } from '@/services/api';
import { ApiResponseDto } from '@/types/common.types';
import { CreateCustomerResponseDto, FormData } from '@/types/customer.types';
import { createApi } from '@reduxjs/toolkit/query/react';

export const customerApi = createApi({
  reducerPath: 'customerApi',
  baseQuery: baseQueryWithReauth,
  tagTypes: ['Customer', 'CustomerColumns'],
  endpoints: (builder) => ({
    // get reorganize columns
    getCustomerColums: builder.query<ApiResponseDto<any>, void>({
      query: () => CUSTOMER_API_ROUTES.GET_COLUMN,
      providesTags: ['CustomerColumns'],
    }),
    // update reorganize columns
    updateCustomerColums: builder.mutation<ApiResponseDto<any>, any>({
      query: (body) => ({
        url: CUSTOMER_API_ROUTES.UPDATE_COLUMN,
        method: 'POST',
        body,
      }),
      transformResponse: (response) => {
        if (response?.message) {
          UseToast().success(response.message);
        }
        return response;
      },
      invalidatesTags: ['CustomerColumns'],
    }),

    // Get a list of customers
    getCustomers: builder.mutation<any, any>({
      query: (body) => ({
        url: CUSTOMER_API_ROUTES.ALL,
        method: 'POST',
        body: body,
      }),
    }),

    // Create a new customer
    createCustomer: builder.mutation<CreateCustomerResponseDto, any>({
      query: (newCustomer) => ({
        url: CUSTOMER_API_ROUTES.CREATE,
        method: 'POST',
        body: newCustomer,
      }),
      invalidatesTags: [{ type: 'Customer', id: 'LIST' }],
    }),

    // Get customer by ID
    getCustomerById: builder.query<any, string>({
      query: (customerId) => ({
        url: `${CUSTOMER_API_ROUTES.GET}/${customerId}`,
        method: 'GET',
      }),
      onQueryStarted: async (_args, { queryFulfilled }) => {
        try {
          await queryFulfilled;
        } catch (error) {}
      },
      providesTags: (result) =>
        result ? [{ type: 'Customer', id: result.customer_id }] : [],
    }),

    // Update an existing customer
    updateCustomer: builder.mutation<
      any,
      { customerId: number; customerData: FormData }
    >({
      query: ({ customerId, customerData }) => ({
        url: `${CUSTOMER_API_ROUTES.GET}/${customerId}`,
        method: 'PUT',
        body: customerData,
      }),
      onQueryStarted: async (_args, { queryFulfilled }) => {
        try {
          await queryFulfilled;
        } catch (error) {}
      },
      // Invalidate the cached data or refetch
      invalidatesTags: (_result, _error, { customerId }) => [
        { type: 'Customer', id: customerId },
        { type: 'Customer', id: 'LIST' },
      ],
    }),

    // Delete a customer
    deleteCustomer: builder.mutation<void, number>({
      query: (customerId) => ({
        url: `${CUSTOMER_API_ROUTES.DELETE}/${customerId}`,
        method: 'DELETE',
      }),
      onQueryStarted: async (_args, { queryFulfilled }) => {
        try {
          await queryFulfilled;
        } catch (error) {}
      },
      // Invalidate the cached data or refetch
      invalidatesTags: (_result, _error, customerId) => [
        { type: 'Customer', id: customerId },
        { type: 'Customer', id: 'LIST' },
      ],
    }),

    getDepartments: builder.query<any, void>({
      query: () => ({
        url: DEPARTMENT_API_ROUTES.ALL,
        method: 'POST',
        body: searchPayload({
          pageNumber: -1,
          pageSize: -1,
          sortBy: 'deptDesc',
          sortAscending: true,
          filters: [],
        }),
      }),
    }),
    getCustomerDetails: builder.query<any, string>({
      query: (customerId) => ({
        url: CUSTOMER_API_ROUTES.GET_DETAILS(customerId),
        method: 'GET',
      }),
    }),
  }),
});

// Export hooks for usage in components
export const {
  useGetCustomerColumsQuery,
  useUpdateCustomerColumsMutation,
  useGetCustomersMutation,
  useCreateCustomerMutation,
  useGetCustomerByIdQuery,
  useUpdateCustomerMutation,
  useDeleteCustomerMutation,
  useGetDepartmentsQuery,
  useLazyGetCustomerDetailsQuery,
  useGetCustomerDetailsQuery,
} = customerApi;
