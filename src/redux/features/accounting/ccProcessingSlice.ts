import { formatDate } from '@/lib/utils';
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// Define the interface for filter state
interface CCProcessingState {
  filters: Array<{
    label: string;
    value: string;
    name: string;
    operator: string;
    tagValue: string;
  }>;
  formValues: {
    dateOfUseFrom?: string | Date | null;
    dateOfUseThru?: string | Date | null;
    showAll?: boolean | null;
  };
}

// Initial state with default values matching the Filter component
const initialState: CCProcessingState = {
  filters: [],
  formValues: {
    dateOfUseFrom: formatDate(new Date()),
    dateOfUseThru: formatDate(new Date()),
    showAll: null,
  },
};

const creditCardProcessingSlice = createSlice({
  name: 'creditCardProcessing',
  initialState,
  reducers: {
    // Set filters based on form data
    setCCProcessingFilter(
      state,
      action: PayloadAction<
        Array<{
          label: string;
          value: string;
          name: string;
          operator: string;
          tagValue: string;
        }>
      >
    ) {
      state.filters = action.payload;
    },

    // Update form values
    updateCCProcessingFormValues(
      state,
      action: PayloadAction<Partial<CCProcessingState['formValues']>>
    ) {
      state.formValues = {
        ...state.formValues,
        ...action.payload,
      };
    },

    // Clear all filters and reset form values
    clearAllCCProcessingFilters(state) {
      state.filters = [];
      state.formValues = {
        dateOfUseFrom: null,
        dateOfUseThru: null,
        showAll: false,
      };
    },

    // Clear a specific filter
    clearCCProcessingFilter(state, action: PayloadAction<string>) {
      state.filters = state.filters.filter(
        (filter) => filter.name !== action.payload
      );

      // Reset corresponding form values
      switch (action.payload) {
        case 'dateOfUseFrom':
          state.formValues.dateOfUseFrom = null;
          break;
        case 'dateOfUseThru':
          state.formValues.dateOfUseThru = null;
          break;
        case 'showAll':
          state.formValues.showAll = false;
          break;
      }
    },
  },
});

export const {
  setCCProcessingFilter,
  clearAllCCProcessingFilters,
  clearCCProcessingFilter,
  updateCCProcessingFormValues,
} = creditCardProcessingSlice.actions;

export default creditCardProcessingSlice.reducer;
