import { UseToast } from '@/components/ui/toast/ToastContainer';
import { INQUIRY_API_ROUTES } from '@/constants/api-constants/accounting-api-constants';
import { baseQueryWithReauth } from '@/services/api';
import { ApiResponseDto } from '@/types/common.types';
import { createApi } from '@reduxjs/toolkit/query/react';

export const accountingInquiryApi = createApi({
  reducerPath: 'accountingInquiryApi',
  baseQuery: baseQueryWithReauth,
  tagTypes: ['InquirColumn'],
  endpoints: (builder) => ({
    // get reorganize columns
    getInquiryColums: builder.query<ApiResponseDto<any>, void>({
      query: () => INQUIRY_API_ROUTES.GET_COLUMN,
      providesTags: ['InquirColumn'],
    }),

    // update reorganize columns
    updateInquiryColums: builder.mutation<ApiResponseDto<any>, any>({
      query: (body) => ({
        url: INQUIRY_API_ROUTES.UPDATE_COLUMN,
        method: 'POST',
        body,
      }),
      transformResponse: (response) => {
        if (response?.message) {
          UseToast().success(response.message);
        }
        return response;
      },
      invalidatesTags: ['InquirColumn'],
    }),

    // inquiry  list
    getInquiryDetails: builder.mutation<
      ApiResponseDto<any>,
      {
        customerID: string | number;
        displayEnum: string | number;
        balanceTransactionEnum: string | number;
        balanceTransactionEnumVal: string;
      }
    >({
      query: (body) => ({
        url: INQUIRY_API_ROUTES.ALL,
        method: 'POST',
        body,
      }),
    }),
    // get Payment info
    getPaymentsInfo: builder.query<
      ApiResponseDto<any>,
      { customerId: string; date: string }
    >({
      query: ({ customerId, date }) =>
        INQUIRY_API_ROUTES.PAYMENT_INFO(customerId, date),
    }),
    // get Invoice info
    getInvoiceInfo: builder.query<
      ApiResponseDto<any>,
      { customerId: string; orderId: string }
    >({
      query: ({ customerId, orderId }) =>
        INQUIRY_API_ROUTES.INVOICE_INFO(customerId, orderId),
    }),
  }),
});

// Export hooks for usage in components
export const {
  useGetInquiryColumsQuery,
  useUpdateInquiryColumsMutation,
  useGetInquiryDetailsMutation,
  useGetPaymentsInfoQuery,
  useGetInvoiceInfoQuery,
} = accountingInquiryApi;
