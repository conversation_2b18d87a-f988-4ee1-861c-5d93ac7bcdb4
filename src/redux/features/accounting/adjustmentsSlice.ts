import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// Define the interface for filter state
interface AdjustmentState {
  filters: Array<{
    label: string;
    value: string;
    name: string;
    operator: string;
  }>;
  formValues: {
    type: string;
    searchBy: string;
    operator: string;
    value: string;
  };
}

// Initial state with default values matching the Filter component
const initialState: AdjustmentState = {
  filters: [],
  formValues: {
    type: '',
    searchBy: '',
    operator: '',
    value: '',
  },
};

const adjustmentSlice = createSlice({
  name: 'adjustment',
  initialState,
  reducers: {
    // Set filters based on form data
    setAdjustmentFilter(
      state,
      action: PayloadAction<
        Array<{ label: string; value: string; name: string; operator: string }>
      >
    ) {
      state.filters = action.payload;
    },

    // Update form values
    updateAdjustmentFormValues(
      state,
      action: PayloadAction<Partial<AdjustmentState['formValues']>>
    ) {
      state.formValues = {
        ...state.formValues,
        ...action.payload,
      };
    },

    // Clear all filters and reset form values
    clearAllAdjustmentFilters(state) {
      state.filters = [];
      state.formValues = initialState.formValues;
    },

    // Clear a specific filter
    clearAdjustmentFilter(state, action: PayloadAction<string>) {
      state.filters = state.filters.filter(
        (filter) => filter.name !== action.payload
      );
      switch (action.payload) {
        case 'status':
          state.formValues.type = '';
          break;
        case 'operator':
          state.formValues.operator = '';
          break;
        case 'searchBy':
          state.formValues.searchBy = '';
          break;
        case 'value':
          state.formValues.value = '';
          break;
        default:
          state.formValues.searchBy = '';
          state.formValues.operator = '';
          state.formValues.value = '';
      }
    },
  },
});

export const {
  setAdjustmentFilter,
  clearAllAdjustmentFilters,
  clearAdjustmentFilter,
  updateAdjustmentFormValues,
} = adjustmentSlice.actions;

export default adjustmentSlice.reducer;
