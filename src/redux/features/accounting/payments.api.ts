import { UseToast } from '@/components/ui/toast/ToastContainer';
import { ORDERS_API_ROUTES } from '@/constants/api-constants';
import { ACCOUNTING_PAYMENTS_API_ROUTES } from '@/constants/api-constants/accounting-api-constants';
import { baseQueryWithReauth } from '@/services/api';
import {
  CalculateConvFeeTaxAllPayloadTypes,
  CalculateConvFeeTaxAllTypes,
  CreditCardDetailsTypes,
} from '@/types/accounting.types';
import { ApiResponseDto, PaginationFilterPayload } from '@/types/common.types';
import { ExtendedPaymentInfoDTO } from '@/types/order.types';
import { createApi } from '@reduxjs/toolkit/query/react';

export const paymentsApi = createApi({
  reducerPath: 'paymentsApi',
  baseQuery: baseQueryWithReauth,
  tagTypes: ['Payments', 'PaymentColumns', 'accountingCustomerColums'],
  endpoints: (builder) => ({
    // get reorganize columns
    getAccountingCustomerColums: builder.query<ApiResponseDto<any>, void>({
      query: () => ACCOUNTING_PAYMENTS_API_ROUTES.GET_CUST_COLUMN,
      providesTags: ['accountingCustomerColums'],
    }),

    // update reorganize columns
    updateAccountingCustomerColums: builder.mutation<ApiResponseDto<any>, any>({
      query: (body) => ({
        url: ACCOUNTING_PAYMENTS_API_ROUTES.UPDATE_CUST_COLUMN,
        method: 'POST',
        body,
      }),
      transformResponse: (response) => {
        if (response?.message) {
          UseToast().success(response.message);
        }
        return response;
      },
      invalidatesTags: ['accountingCustomerColums'],
    }),

    // get reorganize columns
    getPaymentsColums: builder.query<ApiResponseDto<any>, void>({
      query: () => ACCOUNTING_PAYMENTS_API_ROUTES.GET_PAYMENTS_COLUMN,
      providesTags: ['PaymentColumns'],
    }),

    // update reorganize columns
    updatePaymentColums: builder.mutation<ApiResponseDto<any>, any>({
      query: (body) => ({
        url: ACCOUNTING_PAYMENTS_API_ROUTES.UPDATE_PAYMENTS_COLUMN,
        method: 'POST',
        body,
      }),
      transformResponse: (response) => {
        if (response?.message) {
          UseToast().success(response.message);
        }
        return response;
      },
      invalidatesTags: ['PaymentColumns'],
    }),

    getPaymentsList: builder.query<
      ApiResponseDto<ExtendedPaymentInfoDTO[]>,
      { body: PaginationFilterPayload }
    >({
      query: ({ body }) => ({
        url: ACCOUNTING_PAYMENTS_API_ROUTES.PAYMENTS_LIST,
        method: 'POST',
        body,
      }),
      providesTags: (result) =>
        result?.data
          ? [
              ...result.data.map((payment) => ({
                type: 'Payments' as const,
                id: payment.id,
              })),
              { type: 'Payments', id: 'LIST' },
            ]
          : [{ type: 'Payments', id: 'LIST' }],
    }),

    deletePayment: builder.mutation<
      void,
      { paymentId: number; voidTransaction: boolean }
    >({
      query: ({ paymentId, voidTransaction }) => ({
        url: ORDERS_API_ROUTES.DELETE_PAYMENT(paymentId, voidTransaction),
        method: 'DELETE',
      }),
      transformResponse: (response) => {
        if (response?.message) UseToast().success(response.message);
        return response;
      },
      invalidatesTags: (_result, _error) => [{ type: 'Payments', id: 'LIST' }],
    }),

    // payment details
    getPaymentDetails: builder.query<
      ApiResponseDto<any>,
      { paymentId: string; customerId: string }
    >({
      query: ({ paymentId, customerId }) => ({
        url: ACCOUNTING_PAYMENTS_API_ROUTES.PAYMENT_DETAILS,
        params: {
          ...(paymentId && { paymentId }),
          ...(!paymentId && customerId && { customerId }),
        },
      }),
    }),
    getNewPaymentLink: builder.mutation<any, any>({
      query: (body) => ({
        url: ACCOUNTING_PAYMENTS_API_ROUTES.GET_PAYMENT_LINK,
        method: 'POST',
        body,
      }),
      transformResponse: (response) => {
        if (response?.message) UseToast().success(response.message);
        return response;
      },
    }),
    addPayment: builder.mutation<any, any>({
      query: (body) => ({
        url: ACCOUNTING_PAYMENTS_API_ROUTES.ADD_PAYMENT,
        method: 'POST',
        body,
      }),
      transformResponse: (response) => {
        if (response?.message) UseToast().success(response.message);
        return response;
      },
    }),

    calculateConvFeeTaxAll: builder.mutation<
      ApiResponseDto<CalculateConvFeeTaxAllTypes[]>,
      { type: string; body: CalculateConvFeeTaxAllPayloadTypes[] }
    >({
      query: ({ type, body }) => ({
        url: ACCOUNTING_PAYMENTS_API_ROUTES.CAL_CONVFEE_TAX(type),
        method: 'POST',
        body,
      }),
    }),

    creditCardDetails: builder.mutation<
      ApiResponseDto<CreditCardDetailsTypes>,
      string
    >({
      query: (cardNumber) => ({
        url: ACCOUNTING_PAYMENTS_API_ROUTES.CREDIT_CARD_DETAILS(cardNumber),
        method: 'GET',
      }),
    }),

    getCardType: builder.mutation<ApiResponseDto<string | null>, string>({
      query: (paymentMethodId) => ({
        url: ACCOUNTING_PAYMENTS_API_ROUTES.CARD_TYPE,
        method: 'GET',
        params: { paymentMethodId },
      }),
    }),
  }),
});

export const {
  useGetAccountingCustomerColumsQuery,
  useUpdateAccountingCustomerColumsMutation,
  useGetPaymentsColumsQuery,
  useUpdatePaymentColumsMutation,
  useDeletePaymentMutation,
  useGetPaymentDetailsQuery,
  useGetPaymentsListQuery,
  useGetNewPaymentLinkMutation,
  useAddPaymentMutation,
  useCalculateConvFeeTaxAllMutation,
  useCreditCardDetailsMutation,
  useGetCardTypeMutation,
} = paymentsApi;
