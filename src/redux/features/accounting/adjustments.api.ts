import { UseToast } from '@/components/ui/toast/ToastContainer';
import { ADJUSTMENTS_API_ROUTES } from '@/constants/api-constants/accounting-api-constants';
import { baseQueryWithReauth } from '@/services/api';
import {
  AdjustmentAmountForm,
  AdjustmentsTypes,
  LoadAdjustmentDistributionTypes,
  SaveDistributionPayloadTypes,
} from '@/types/accounting.types';
import { ApiResponseDto } from '@/types/common.types';
import { createApi } from '@reduxjs/toolkit/query/react';

export const adjustmentsApi = createApi({
  reducerPath: 'adjustmentsApi',
  baseQuery: baseQueryWithReauth,
  tagTypes: ['Adjustments', 'CalculatedAdjustment', 'AdjustmentDistribution'],
  endpoints: (builder) => ({
    // GET BY ID
    getAdjustment: builder.query<ApiResponseDto<AdjustmentsTypes>, string>({
      query: (id) => ({
        url: ADJUSTMENTS_API_ROUTES.GET,
        params: { id },
      }),
      providesTags: ['Adjustments'],
    }),

    // SAVE
    saveAdjustment: builder.mutation<
      ApiResponseDto<AdjustmentsTypes>,
      AdjustmentsTypes
    >({
      query: (body) => ({
        url: ADJUSTMENTS_API_ROUTES.SAVE,
        method: 'POST',
        body,
      }),
      transformResponse: (response) => {
        if (response?.message) UseToast().success(response.message);
        return response;
      },
      invalidatesTags: ['Adjustments', 'CalculatedAdjustment'],
    }),

    // DELETE
    deleteAdjustment: builder.mutation<void, number>({
      query: (id) => ({
        url: ADJUSTMENTS_API_ROUTES.DELETE,
        method: 'DELETE',
        params: { id },
      }),
      transformResponse: (response) => {
        if (response?.message) UseToast().success(response.message);
        return response;
      },
      invalidatesTags: ['Adjustments'],
    }),

    // GET ADJUSTMENT
    getCalculatedAdjustment: builder.query<
      ApiResponseDto<AdjustmentAmountForm[]>,
      string
    >({
      query: (customerId) => ({
        url: ADJUSTMENTS_API_ROUTES.GET_CALCULATED_ADJUSTMENT,
        params: { customerId },
      }),
      providesTags: ['CalculatedAdjustment'],
    }),

    // SAVE DISTRIBUTION
    saveDistribution: builder.mutation<
      ApiResponseDto<null>,
      SaveDistributionPayloadTypes
    >({
      query: (body) => ({
        url: ADJUSTMENTS_API_ROUTES.SAVE_DISTRIBUTION,
        method: 'POST',
        body,
      }),
      transformResponse: (response) => {
        if (response?.message) UseToast().success(response.message);
        return response;
      },
      invalidatesTags: ['AdjustmentDistribution'],
    }),

    // Load Distribution
    loadAdjustmentDistribution: builder.query<
      ApiResponseDto<LoadAdjustmentDistributionTypes[]>,
      string
    >({
      query: (customerId) => ({
        url: ADJUSTMENTS_API_ROUTES.GET_DISTRIBUTION,
        params: { customerId },
      }),
      providesTags: ['AdjustmentDistribution'],
    }),
  }),
});

// Export hooks for usage in components
export const {
  useGetAdjustmentQuery,
  useSaveAdjustmentMutation,
  useDeleteAdjustmentMutation,
  useGetCalculatedAdjustmentQuery,
  useSaveDistributionMutation,
  useLoadAdjustmentDistributionQuery,
} = adjustmentsApi;
