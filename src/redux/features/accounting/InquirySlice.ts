import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface SubRentalState {
  filters: Array<{
    label: string;
    value: string;
    name: string;
    operator: string;
  }>;
  formValues: {
    isactive: string;
    searchBy: string;
    operator: string;
    searchValue: string;
  };
}

// Initial state with default values matching the Filter component
const initialState: SubRentalState = {
  filters: [],
  formValues: {
    isactive: 'all',
    searchBy: 'full_name',
    operator: 'Contains',
    searchValue: '',
  },
};

const accountingInquirySlice = createSlice({
  name: 'accountingInquiry',
  initialState,
  reducers: {
    // Set filters based on form data
    setFilter(
      state,
      action: PayloadAction<
        Array<{ label: string; value: string; name: string; operator: string }>
      >
    ) {
      state.filters = action.payload;
    },

    // Update form values
    updateFormValues(
      state,
      action: PayloadAction<Partial<SubRentalState['formValues']>>
    ) {
      state.formValues = {
        ...state.formValues,
        ...action.payload,
      };
    },

    // Clear all filters and reset form values
    clearAllFilters(state) {
      state.filters = [];
      state.formValues = initialState.formValues;
    },

    // Clear a specific filter
    clearFilter(state, action: PayloadAction<string>) {
      state.filters = state.filters.filter(
        (filter) => filter.name !== action.payload
      );
      const caseValue =
        action.payload === 'isactive' ? action.payload : 'searchBy';
      switch (caseValue) {
        case 'isactive':
          state.formValues.isactive = 'all';
          break;
        case 'searchBy':
          state.formValues.searchBy = '';
          state.formValues.operator = 'Contains';
          state.formValues.searchValue = '';
          break;
        // Add more cases as needed
      }
    },
  },
});

export const { setFilter, clearFilter, clearAllFilters, updateFormValues } =
  accountingInquirySlice.actions;

export default accountingInquirySlice.reducer;
