import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface SubRentalState {
  filters: Array<{
    label: string;
    value: string;
    name: string;
    operator: string;
  }>;
  formValues: {
    isactive: string;
    searchBy: string;
    operator: string;
    searchValue: string;
  };
}

// Initial state with default values matching the Filter component
const initialState: SubRentalState = {
  filters: [],
  formValues: {
    isactive: 'all',
    searchBy: '',
    operator: 'Contains',
    searchValue: '',
  },
};

const accountingInquirySlice = createSlice({
  name: 'accountingCustomers',
  initialState,
  reducers: {
    // Set filters based on form data
    setFilter(
      state,
      action: PayloadAction<
        Array<{ label: string; value: string; name: string; operator: string }>
      >
    ) {
      state.filters = action.payload;
    },

    // Update form values
    updateFormValues(
      state,
      action: PayloadAction<Partial<SubRentalState['formValues']>>
    ) {
      state.formValues = {
        ...state.formValues,
        ...action.payload,
      };
    },

    // Clear all filters and reset form values
    clearAllFilters(state) {
      state.filters = [];
      state.formValues = initialState.formValues;
    },

    // Clear a specific filter
    clearFilter(state, action: PayloadAction<string>) {
      const { payload } = action;

      state.filters = state.filters.filter(
        (filter) => filter.name !== action.payload
      );
      const resetMap: Record<string, () => void> = {
        isactive: () => {
          state.formValues.isactive = 'all';
        },

        searchBy: () => {
          state.formValues.searchBy = '';
          state.formValues.operator = 'Contains';
          state.formValues.searchValue = '';
        },
      };

      const key = ['isactive'].includes(payload) ? payload : 'searchBy';
      resetMap[key]?.();
    },
  },
});

export const { setFilter, clearFilter, clearAllFilters, updateFormValues } =
  accountingInquirySlice.actions;

export default accountingInquirySlice.reducer;
