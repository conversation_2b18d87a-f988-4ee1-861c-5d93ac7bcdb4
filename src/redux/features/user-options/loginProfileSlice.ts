import { ProfileGeneralInformation } from '@/types/user-options.types';
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface LoginProfileState {
  profile: ProfileGeneralInformation;
  isLoading: boolean;
  error: string | null;
}

const initialState: LoginProfileState = {
  profile: {} as ProfileGeneralInformation,
  isLoading: false,
  error: null,
};

const loginProfileSlice = createSlice({
  name: 'loginProfile',
  initialState,
  reducers: {
    setProfile: (state, action: PayloadAction<ProfileGeneralInformation>) => {
      state.profile = action.payload;
    },
    removeProfile: (state) => {
      state.profile = {} as ProfileGeneralInformation;
    },
  },
});

export const { setProfile, removeProfile } = loginProfileSlice.actions;

export default loginProfileSlice.reducer;
