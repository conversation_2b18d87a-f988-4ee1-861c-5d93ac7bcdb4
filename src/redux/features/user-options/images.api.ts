import { IMAGES_API } from '@/constants/user-options-constants';
import { baseQueryWithReauth } from '@/services/api';
import { ApiResponseDto } from '@/types/common.types';
import { createApi } from '@reduxjs/toolkit/query/react';

export const imagesApi = createApi({
  reducerPath: 'imagesApi',
  baseQuery: baseQueryWithReauth,
  tagTypes: ['Images'], // Define the tag types that will be used for cache invalidation
  endpoints: (builder) => ({
    // Get Customer Images List by Customer ID
    getProfileImage: builder.query<ApiResponseDto<any>, any>({
      query: (userId) => IMAGES_API.GET(userId),
      providesTags: (result) =>
        result ? [{ type: 'Images', id: 'LIST' }] : [],
    }),

    uploadImage: builder.mutation<ApiResponseDto<string>, any>({
      query: ({ formData, userId }: any) => ({
        url: IMAGES_API.UPLOAD(userId), // The URL with customer ID
        method: 'POST',
        body: formData, // File data (FormData)
      }),
      invalidatesTags: [{ type: 'Images', id: 'LIST' }], // Invalidate cache for Images LIST to trigger refetch
    }),

    // Delete a File
    deleteImage: builder.mutation<void, any>({
      query: ({ userId }: { userId: string }) => ({
        url: IMAGES_API.DELETE(userId),
        method: 'DELETE',
      }),
      invalidatesTags: (_result, _error, userId) => [
        { type: 'Images', id: userId }, // Invalidate the specific File cache
        { type: 'Images', id: 'LIST' }, // Invalidate the LIST cache for refetch
      ],
    }),
  }),
});

// Export hooks for usage in components
export const {
  useDeleteImageMutation,
  useGetProfileImageQuery,
  useUploadImageMutation,
} = imagesApi;
