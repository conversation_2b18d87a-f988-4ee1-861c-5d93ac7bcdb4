import { LOGIN_PROFILE_API } from '@/constants/user-options-constants';
import { baseQueryWithReauth } from '@/services/api';
import { ApiResponseDto } from '@/types/common.types';
import { ProfileGeneralInformation } from '@/types/user-options.types';
import { createApi } from '@reduxjs/toolkit/query/react';
import { setProfile } from './loginProfileSlice';
import { SYSTEM_USERS_API_ROUTES } from '@/constants/api-constants';
import { setPermissions, setPermissionsLoading } from '../auth/authSlice';

// Define the API service using RTK Query
export const loginProfileApi = createApi({
  reducerPath: 'loginProfileApi',
  baseQuery: baseQueryWithReauth,
  endpoints: (builder) => ({
    // GET request to fetch the profile details
    getProfileDetails: builder.mutation<
      ApiResponseDto<ProfileGeneralInformation>,
      { id: number }
    >({
      query: ({ id }) => ({
        url: LOGIN_PROFILE_API.GET(id),
        method: 'GET',
      }),
      async onQueryStarted(_args, { dispatch, queryFulfilled }) {
        try {
          const { data } = await queryFulfilled;
          if (data.statusCode === 200) {
            dispatch(setProfile(data.data));
          }
        } catch (error) {}
      },
    }),

    getAssigndPermissions: builder.mutation<ApiResponseDto<any>, void>({
      query: () => ({
        url: SYSTEM_USERS_API_ROUTES.PERMISSIONS,
        method: 'GET',
      }),
      async onQueryStarted(_args, { dispatch, queryFulfilled }) {
        try {
          const { data } = await queryFulfilled;
          if (data.statusCode === 200) {
            dispatch(setPermissions(data.data));
            dispatch(setPermissionsLoading(false));
          }
        } catch (error) {}
      },
    }),
  }),
});

export const {
  useGetProfileDetailsMutation,
  useGetAssigndPermissionsMutation,
} = loginProfileApi;
