import { UseToast } from '@/components/ui/toast/ToastContainer';
import { baseQueryWithReauth } from '@/services/api';
import { createApi } from '@reduxjs/toolkit/query/react';

export const profileDetailsApi = createApi({
  reducerPath: 'profileDetailsApi',
  baseQuery: baseQueryWithReauth,
  endpoints: (builder) => ({
    getList: builder.query<any, { url: string }>({
      query: ({ url }) => ({
        url: url,
        method: 'GET',
      }),
    }),

    getListProfileDetails: builder.mutation<any, { url: string; data: any }>({
      query: ({ url, data }) => ({
        url: url,
        method: 'POST',
        body: data,
      }),
    }),

    // Update an Profile Details (with dynamic URL), Generic API Response Type
    updateProfileDetails: builder.mutation<void, { url: string; data?: any }>({
      query: ({ url, data }) => ({
        url: url,
        method: 'PUT',
        body: data,
      }),
      transformResponse: (response: {
        success: boolean;
        statusCode: number;
        message: string;
        data: any;
      }) => {
        if (response.statusCode === 200) {
          // toast.success(response.message);
          UseToast().success(response.message);
          return response.data; // Return the data directly
        }
        return <any>{}; // Fallback to an empty array in case of failure
      },
    }),
    // Update an Profile Details (with dynamic URL), Generic API Request Type
    addNewProfileDetails: builder.mutation<void, { url: string; data?: any }>({
      query: ({ url, data }) => ({
        url: url,
        method: 'POST',
        body: data,
      }),
      transformResponse: (response: {
        success: boolean;
        statusCode: number;
        message: string;
        data: any;
      }) => {
        if (response.statusCode === 200) {
          UseToast().success(response.message);
          return response.data; // Return the data directly
        }
        return <any>{}; // Fallback to an empty array in case of failure
      },
    }),
    // Delete an Profile Details (with dynamic URL)
    deleteProfileDetails: builder.mutation<
      void,
      { url: string; itemId?: string | number }
    >({
      query: ({ url, itemId }) => ({
        url: itemId ? `${url}/${itemId}` : url,
        method: 'DELETE',
      }),
      transformResponse: (response: {
        success: boolean;
        statusCode: number;
        message: string;
        data: any;
      }) => {
        if (response.statusCode === 200) {
          // toast.success(response.message);
          UseToast().success(response.message);
          return response.data; // Return the data directly
        }
        return <any>{}; // Fallback to an empty array in case of failure
      },
    }),
  }),
  refetchOnMountOrArgChange: true,
});
export const {
  useGetListQuery,
  useGetListProfileDetailsMutation,
  useAddNewProfileDetailsMutation,
  useUpdateProfileDetailsMutation,
  useDeleteProfileDetailsMutation,
} = profileDetailsApi;
