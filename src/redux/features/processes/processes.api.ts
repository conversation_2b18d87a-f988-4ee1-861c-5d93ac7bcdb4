import { UseToast } from '@/components/ui/toast/ToastContainer';
import {
  PROCESS_POSTING_API_ROUTES,
  PROCESSES_API_ROUTES,
  PROCESSES_SERVICE_CHARGES_API_ROUTES,
} from '@/constants/api-constants/processes-api-constants';
import { baseQueryWithReauth } from '@/services/api';
import { ApiResponseDto } from '@/types/common.types';
import { createApi } from '@reduxjs/toolkit/query/react';

// Define the API service using RTK Query
export const processesApi = createApi({
  reducerPath: 'processesApi',
  baseQuery: baseQueryWithReauth,
  tagTypes: ['processes', 'processesColumns', 'unDeleteOrders'],
  endpoints: (builder) => ({
    getProcessesColums: builder.query<ApiResponseDto<any>, void>({
      query: () => PROCESSES_API_ROUTES.GET_COLUMN,
      providesTags: ['processesColumns'],
    }),

    updateProcessesColums: builder.mutation<ApiResponseDto<any>, any>({
      query: (body) => ({
        url: PROCESSES_API_ROUTES.UPDATE_COLUMN,
        method: 'POST',
        body,
      }),
      transformResponse: (response) => {
        if (response?.message) {
          UseToast().success(response.message);
        }
        return response;
      },

      invalidatesTags: ['processesColumns'],
    }),

    // // GET request to processes list
    getProcesses: builder.query<ApiResponseDto<any>, void>({
      query: () => PROCESSES_API_ROUTES.ALL,
    }),

    // posting
    // get all posting list
    getPostingOrders: builder.query<ApiResponseDto<any>, any>({
      query: (body) => ({
        url: PROCESS_POSTING_API_ROUTES.ALL,
        method: 'POST',
        body,
      }),
    }),

    getAllDeleteOrders: builder.query<ApiResponseDto<any>, void>({
      query: () => PROCESSES_API_ROUTES.DELETE_ALL,
      providesTags: ['unDeleteOrders'],
    }),
    // un delete order
    unDeleteOrder: builder.mutation<void, string>({
      query: (orderId) => ({
        url: PROCESSES_API_ROUTES.UN_DELETE(orderId),
        method: 'GET',
      }),
      transformResponse: (response) => {
        if (response?.message) {
          UseToast().success(response.message);
        }
        return response;
      },
    }),

    // service charges
    // get all service charges
    getAllServiceCharges: builder.mutation<ApiResponseDto<any>, any>({
      query: (body) => ({
        url: PROCESSES_SERVICE_CHARGES_API_ROUTES.ALL,
        method: 'POST',
        body,
      }),
    }),
    // insert services charges
    insertServiceCharges: builder.mutation<ApiResponseDto<any>, any>({
      query: (body) => ({
        url: PROCESSES_SERVICE_CHARGES_API_ROUTES.INSERT_SERVICE_CHARGES,
        method: 'POST',
        body,
      }),
      transformResponse: (response) => {
        if (response?.message) {
          UseToast().success(response.message);
        }
        return response;
      },
    }),

    // post order
    postOrders: builder.mutation<ApiResponseDto<any>, number[]>({
      query: (orderIds) => ({
        url: PROCESS_POSTING_API_ROUTES.POST,
        method: 'POST',
        body: { orderIds },
      }),
      transformResponse: (response) => {
        if (response?.message) {
          UseToast().success(response.message);
        }
        return response;
      },
    }),
    // unpost order
    unPostOrder: builder.mutation<
      ApiResponseDto<any>,
      { orderId: string; incZeroBalOrd?: boolean }
    >({
      query: (body) => ({
        url: PROCESS_POSTING_API_ROUTES.UNPOST,
        method: 'POST',
        body,
      }),
      transformResponse: (response) => {
        if (response?.message && !response?.data?.requiresConfirmation) {
          UseToast().success(response.message);
        }
        return response;
      },
    }),
  }),
});

export const {
  useGetProcessesQuery,
  useGetProcessesColumsQuery,
  useUpdateProcessesColumsMutation,
  // posting
  useGetPostingOrdersQuery,
  usePostOrdersMutation,
  // unpost order
  useUnPostOrderMutation,
  // undelete
  useUnDeleteOrderMutation,
  useGetAllDeleteOrdersQuery,
  // service charges
  useGetAllServiceChargesMutation,
  useInsertServiceChargesMutation,
} = processesApi;
