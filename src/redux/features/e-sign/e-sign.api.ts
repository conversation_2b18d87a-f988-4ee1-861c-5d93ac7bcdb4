import { ORDERS_API_ROUTES } from '@/constants/api-constants';
import { baseQueryWithReauth } from '@/services/api';
import { createApi } from '@reduxjs/toolkit/query/react';

export const esignApi = createApi({
  reducerPath: 'esignApi',
  baseQuery: baseQueryWithReauth,
  tagTypes: ['customerContacts'],
  endpoints: (builder) => ({
    getIsOrderInvoiced: builder.query<any, string>({
      query: (orderId) => ({
        url: ORDERS_API_ROUTES.E_SIGN_IS_ORDER_INVOICED(orderId),
        method: 'GET',
      }),
    }),

    getCustomerContacts: builder.query<any, string>({
      query: (orderId) => ({
        url: ORDERS_API_ROUTES.E_SIGN_CUSTOMER_CONTACTS(orderId),
        method: 'GET',
      }),
      providesTags: ['customerContacts'],
    }),

    addAdditionalCustomer: builder.mutation<
      any,
      { orderId: string; body: any }
    >({
      query: ({ orderId, body }) => ({
        url: ORDERS_API_ROUTES.E_SIGN_ADD_ADDITIONAL_CUSTOMER(orderId),
        method: 'POST',
        body,
      }),
      invalidatesTags: ['customerContacts'],
    }),

    sendEsign: builder.mutation<any, { orderId: string; body: any }>({
      query: ({ orderId, body }) => ({
        url: ORDERS_API_ROUTES.ESIGN_SEND(orderId),
        method: 'POST',
        body,
      }),
    }),
  }),
});

export const {
  useGetIsOrderInvoicedQuery,
  useGetCustomerContactsQuery,
  useAddAdditionalCustomerMutation,
  useSendEsignMutation,
} = esignApi;
