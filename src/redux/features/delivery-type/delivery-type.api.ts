import { DELIVERY_TYPE_API_ROUTES } from '@/constants/api-constants';
import { searchPayload } from '@/lib/utils';
import { baseQueryWithReauth } from '@/services/api';
import { createApi } from '@reduxjs/toolkit/query/react';

export const deliveryTypeApi = createApi({
  reducerPath: 'deliveryTypeApi',
  baseQuery: baseQueryWithReauth,
  endpoints: (builder) => ({
    getDeliveryType: builder.query<any, void>({
      query: () => ({
        url: DELIVERY_TYPE_API_ROUTES.ALL,
        method: 'POST',
        body: searchPayload({
          pageNumber: -1,
          pageSize: -1,
          sortBy: '',
          sortAscending: true,
          filters: [],
        }),
      }),
    }),
  }),
});

export const { useGetDeliveryTypeQuery } = deliveryTypeApi;
