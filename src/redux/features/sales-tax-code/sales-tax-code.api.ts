import { SALES_TAX_CODE_API_ROUTES } from '@/constants/api-constants';
import { searchPayload } from '@/lib/utils';
import { baseQueryWithReauth } from '@/services/api';
import { createApi } from '@reduxjs/toolkit/query/react';

export const salesTaxCodeApi = createApi({
  reducerPath: 'salesTaxCodeApi',
  baseQuery: baseQueryWithReauth,
  endpoints: (builder) => ({
    getSalesTaxCode: builder.query<any, void>({
      query: () => ({
        url: SALES_TAX_CODE_API_ROUTES.ALL,
        method: 'POST',
        body: searchPayload({
          pageNumber: -1,
          pageSize: -1,
          sortBy: '',
          sortAscending: true,
          filters: [],
        }),
      }),
    }),
  }),
});

export const { useGetSalesTaxCodeQuery } = salesTaxCodeApi;
