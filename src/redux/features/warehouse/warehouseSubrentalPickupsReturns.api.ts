import { UseToast } from '@/components/ui/toast/ToastContainer';
import { WAREHOUSE_SUBRENTAL_PICKUPS_RETURNS_API_ROUTES } from '@/constants/api-constants';
import { baseQueryWithReauth } from '@/services/api';
import { ApiResponseDto } from '@/types/common.types';
import { createApi } from '@reduxjs/toolkit/query/react';

// Define the API service using RTK Query
export const warehouseSubrentalPickupsReturnsApi = createApi({
  reducerPath: 'warehouseSubrentalPickupsReturnsApi',
  baseQuery: baseQueryWithReauth,
  tagTypes: ['warehouse', 'warehouseColumns'],
  endpoints: (builder) => ({
    getWarehouseColums: builder.query<ApiResponseDto<any>, void>({
      query: () => WAREHOUSE_SUBRENTAL_PICKUPS_RETURNS_API_ROUTES.GET_COLUMN,
      providesTags: ['warehouseColumns'],
    }),
    updateWarehouseColums: builder.mutation<ApiResponseDto<any>, any>({
      query: (body) => ({
        url: WAREHOUSE_SUBRENTAL_PICKUPS_RETURNS_API_ROUTES.UPDATE_COLUMN,
        method: 'POST',
        body,
      }),
      transformResponse: (response) => {
        if (response?.message) {
          UseToast().success(response.message);
        }
        // Return the response as it is after processing it
        return response;
      },

      invalidatesTags: ['warehouseColumns'],
    }),
    deleteWarehouse: builder.mutation({
      query: (id: number) => ({
        url: WAREHOUSE_SUBRENTAL_PICKUPS_RETURNS_API_ROUTES.DELETE(id),
        method: 'DELETE',
      }),
      transformResponse: (response) => {
        if (response?.message) {
          UseToast().success(response.message);
        }
        return response;
      },
      invalidatesTags: ['warehouse'],
    }),
    downloadFile: builder.mutation<ApiResponseDto<any>, any>({
      query: (payload) => ({
        url: WAREHOUSE_SUBRENTAL_PICKUPS_RETURNS_API_ROUTES.DOWNLOAD_FILE,
        method: 'POST',
        body: payload,
      }),
    }),
    getListForPrint: builder.query<ApiResponseDto<any>, any>({
      query: () => ({
        url: WAREHOUSE_SUBRENTAL_PICKUPS_RETURNS_API_ROUTES.GET_LIST_PRINT,
        method: 'GET',
      }),
    }),
  }),
});

export const {
  useDeleteWarehouseMutation,
  useGetWarehouseColumsQuery,
  useUpdateWarehouseColumsMutation,
  useDownloadFileMutation,
  useGetListForPrintQuery,
} = warehouseSubrentalPickupsReturnsApi;
