import { UseToast } from '@/components/ui/toast/ToastContainer';
import { WAREHOUSE_TRUCK_ITINERARIES_API_ROUTES } from '@/constants/api-constants';
import { baseQueryWithReauth } from '@/services/api';
import { ApiResponseDto } from '@/types/common.types';
import { createApi } from '@reduxjs/toolkit/query/react';

// Define the API service using RTK Query
export const truckItinerariesApi = createApi({
  reducerPath: 'truckItinerariesApi',
  baseQuery: baseQueryWithReauth,
  tagTypes: [
    'warehouse',
    'warehouseColumns',
    'warehouseTruckItinerary',
    'warehouseTruckItineraryOrderJob',
    'warehouseTruckItinerarySubRental',
  ],
  endpoints: (builder) => ({
    getWarehouseColums: builder.query<ApiResponseDto<any>, void>({
      query: () => WAREHOUSE_TRUCK_ITINERARIES_API_ROUTES.GET_COLUMN,
      providesTags: ['warehouseColumns'],
    }),
    updateWarehouseColums: builder.mutation<ApiResponseDto<any>, any>({
      query: (body) => ({
        url: WAREHOUSE_TRUCK_ITINERARIES_API_ROUTES.UPDATE_COLUMN,
        method: 'POST',
        body,
      }),
      transformResponse: (response) => {
        if (response?.message) {
          UseToast().success(response.message);
        }
        // Return the response as it is after processing it
        return response;
      },

      invalidatesTags: ['warehouseColumns'],
    }),
    deleteWarehouse: builder.mutation<ApiResponseDto<any>, any>({
      query: (id: number) => ({
        url: WAREHOUSE_TRUCK_ITINERARIES_API_ROUTES.DELETE(id),
        method: 'DELETE',
      }),
      transformResponse: (response) => {
        if (response?.message) {
          UseToast().success(response.message);
        }
        return response;
      },
      invalidatesTags: ['warehouse'],
    }),
    getTruckOrderList: builder.query<ApiResponseDto<any>, any>({
      query: (id: string) => ({
        url: WAREHOUSE_TRUCK_ITINERARIES_API_ROUTES.LIST_TRUCK_ITINERARY_ORDERS(
          id
        ),
        method: 'GET',
      }),
      providesTags: ['warehouseTruckItinerary'],
    }),
    saveTruckOrderList: builder.mutation<ApiResponseDto<any>, any>({
      query: ({ truckOrderList, id }: any) => ({
        url: WAREHOUSE_TRUCK_ITINERARIES_API_ROUTES.SAVE_TRUCK_ITINERARY_ORDERS(
          id
        ),
        method: 'POST',
        body: truckOrderList,
      }),
      invalidatesTags: ['warehouseTruckItinerary'],
    }),
    getWarehouseColumsForOrderJob: builder.query<ApiResponseDto<any>, void>({
      query: () => WAREHOUSE_TRUCK_ITINERARIES_API_ROUTES.GET_COLUMN_ORDER_JOB,
      providesTags: ['warehouseTruckItineraryOrderJob'],
    }),
    updateWarehouseColumsForOrderJob: builder.mutation<
      ApiResponseDto<any>,
      any
    >({
      query: (body) => ({
        url: WAREHOUSE_TRUCK_ITINERARIES_API_ROUTES.UPDATE_COLUMN_ORDER_JOB,
        method: 'POST',
        body,
      }),
      transformResponse: (response) => {
        if (response?.message) {
          UseToast().success(response.message);
        }
        // Return the response as it is after processing it
        return response;
      },

      invalidatesTags: ['warehouseTruckItineraryOrderJob'],
    }),
    getWarehouseColumsForSubRental: builder.query<ApiResponseDto<any>, void>({
      query: () => WAREHOUSE_TRUCK_ITINERARIES_API_ROUTES.GET_COLUMN_SUB_RENTAL,
      providesTags: ['warehouseTruckItinerarySubRental'],
    }),
    updateWarehouseColumsForSubRental: builder.mutation<
      ApiResponseDto<any>,
      any
    >({
      query: (body) => ({
        url: WAREHOUSE_TRUCK_ITINERARIES_API_ROUTES.UPDATE_COLUMN_SUB_RENTAL,
        method: 'POST',
        body,
      }),
      transformResponse: (response) => {
        if (response?.message) {
          UseToast().success(response.message);
        }
        // Return the response as it is after processing it
        return response;
      },

      invalidatesTags: ['warehouseTruckItinerarySubRental'],
    }),
    downloadFile: builder.mutation<ApiResponseDto<any>, any>({
      query: (payload) => ({
        url: WAREHOUSE_TRUCK_ITINERARIES_API_ROUTES.DOWNLOAD_FILE,
        method: 'POST',
        body: payload,
      }),
    }),
    getListForPrint: builder.query<ApiResponseDto<any>, any>({
      query: () => ({
        url: WAREHOUSE_TRUCK_ITINERARIES_API_ROUTES.GET_LIST_PRINT,
        method: 'GET',
      }),
    }),
  }),
});

export const {
  useDeleteWarehouseMutation,
  useGetWarehouseColumsQuery,
  useUpdateWarehouseColumsMutation,
  useGetTruckOrderListQuery,
  useSaveTruckOrderListMutation,
  useLazyGetWarehouseColumsForOrderJobQuery,
  useUpdateWarehouseColumsForOrderJobMutation,
  useLazyGetWarehouseColumsForSubRentalQuery,
  useUpdateWarehouseColumsForSubRentalMutation,
  useDownloadFileMutation,
  useGetListForPrintQuery,
} = truckItinerariesApi;
