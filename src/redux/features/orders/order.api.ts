import { UseToast } from '@/components/ui/toast/ToastContainer';
import {
  CONFIG_API,
  CUSTOMER_TYPE_API_ROUTES,
  LEAD_TYPE_ENUMS,
  ORDERS_API_ROUTES,
} from '@/constants/api-constants';
import { baseQueryWithReauth } from '@/services/api';
import { ApiResponseDto, PaginationFilterPayload } from '@/types/common.types';
import {
  AddPaymentType,
  CashSaleTypes,
  ExtendedPaymentInfoDTO,
  LinkedFilesType,
  OrderNotesType,
  PaymentCallbackType,
  RefundPaymentType,
  StatusTypes,
} from '@/types/order.types';
import {
  ESignDocumentsPayloadTypes,
  ESignDocumentsTypes,
} from '@/types/orders/e-sign-documents.types';
import { createApi } from '@reduxjs/toolkit/query/react';

export const orderApi = createApi({
  reducerPath: 'orderApi',
  baseQuery: baseQueryWithReauth,
  tagTypes: [
    'Order',
    'Payments',
    'Status',
    'AdditionalInfo',
    'ShippingInfoItem',
    'ShippingInfoBox',
    'CustomerCard',
  ],
  endpoints: (builder) => ({
    // Get Order by ID
    getOrderById: builder.query<any, string>({
      query: (orderId) => ({
        url: ORDERS_API_ROUTES.GET(orderId),
        method: 'GET',
      }),
      keepUnusedDataFor: 0,
      onQueryStarted: async (_args, { queryFulfilled }) => {
        try {
          await queryFulfilled;
        } catch (error) {}
      },
      providesTags: (result) =>
        result ? [{ type: 'Order', id: result.order_id }] : [],
    }),

    // Update an existing Order
    updateOrder: builder.mutation<
      any,
      { url: string; body: any; method: 'POST' | 'PUT' }
    >({
      query: ({ url, body, method = 'POST' }) => ({
        url,
        method,
        body,
      }),
      transformResponse: (response) => {
        if (response?.message) {
          UseToast().success(response.message);
        }
        return response;
      },
      onQueryStarted: async (_args, { queryFulfilled }) => {
        try {
          await queryFulfilled;
        } catch (error) {}
      },
      // invalidatesTags: ['Order'],
    }),

    // Create new order via order enquiry flow
    saveOrderEnquiry: builder.mutation<any, any>({
      query: (body) => ({
        url: ORDERS_API_ROUTES.ORDER_ENQUIRY_SAVE,
        method: 'POST',
        body,
      }),
      transformResponse: (response) => {
        if (response?.message) {
          UseToast().success(response.message);
        }
        return response;
      },
    }),

    // Delete a order
    deleteOrder: builder.mutation<
      void,
      { id: string; reasonOfDeletion: string; deletedBy: string }
    >({
      query: ({ id, reasonOfDeletion, deletedBy }) => ({
        url: ORDERS_API_ROUTES.DELETE(id),
        method: 'DELETE',
        body: { id, reasonOfDeletion, deletedBy },
      }),

      onQueryStarted: async (_args, { queryFulfilled }) => {
        try {
          await queryFulfilled;
        } catch (error) {}
      },
      transformResponse: (response) => {
        if (response?.message) {
          UseToast().success(response.message);
        }
        return response;
      },
    }),

    // lock order
    lockedOrder: builder.query<any, string>({
      query: (orderId) => ({
        url: ORDERS_API_ROUTES.LOCK_ORDER(orderId),
        method: 'GET',
      }),
    }),

    // unlock order
    unlockedOrder: builder.query<any, string>({
      query: (orderId) => ({
        url: ORDERS_API_ROUTES.UNLOCK_ORDER(orderId),
        method: 'GET',
      }),
    }),

    // get Store Location
    getStoreLocations: builder.query<any, void>({
      query: () => ({
        url: CONFIG_API.storeLocations,
        method: 'GET',
      }),
    }),

    // get Customer Type
    getCustomerType: builder.query<any, void>({
      query: () => ({
        url: CUSTOMER_TYPE_API_ROUTES.ALL,
        method: 'GET',
      }),
    }),

    // get Ordered By List
    getOrderByList: builder.query<any, void>({
      query: () => ({
        url: ORDERS_API_ROUTES.ALL,
        method: 'GET',
      }),
    }),

    dateCalculation: builder.mutation<any, { body: any }>({
      query: ({ body }) => ({
        url: ORDERS_API_ROUTES.DATE_CALCULATION,
        method: 'POST',
        body,
      }),
    }),

    getLinkedFilesListById: builder.query<
      ApiResponseDto<LinkedFilesType[]>,
      string
    >({
      query: (orderId) => ORDERS_API_ROUTES.LINKED_ALL(orderId),
      providesTags: (_result, _error, id) => [{ type: 'Order', id }],
    }),

    deleteLinkedFile: builder.mutation<ApiResponseDto<LinkedFilesType>, number>(
      {
        query: (fileId) => {
          return {
            url: ORDERS_API_ROUTES.DELETE_LINKEDFILE(fileId),
            method: 'DELETE',
          };
        },

        invalidatesTags: (_result, _error, fileId) => {
          return [{ type: 'Order', id: fileId }];
        },
      }
    ),

    defaultLinkedFile: builder.mutation<
      ApiResponseDto<LinkedFilesType>,
      number
    >({
      query: (fileId) => {
        return {
          url: ORDERS_API_ROUTES.DEFAULT(fileId),
          method: 'POST',
          body: { linkedFileId: fileId },
        };
      },

      invalidatesTags: (_result, _error, fileId) => {
        return [{ type: 'Order', id: fileId }];
      },
    }),

    // Create a new note
    createNote: builder.mutation<OrderNotesType, { orderId: any; body: any }>({
      query: ({ orderId, body }) => ({
        url: ORDERS_API_ROUTES.CREATE_UPDATE_NOTE(orderId),
        method: 'POST',
        body: body,
      }),
      transformResponse: (response) => {
        if (response?.message) {
          UseToast().success(response.message);
        }
        return response;
      },
      invalidatesTags: [{ type: 'Order', id: 'LIST' }], // Invalidate LIST cache to trigger refetch
    }),

    getOrderNoteById: builder.query<
      any,
      { orderId: string; orderNoteId: string }
    >({
      query: ({ orderId, orderNoteId }) => ({
        url: ORDERS_API_ROUTES.GET_NOTE(orderId, orderNoteId),
        method: 'GET',
      }),
    }),

    // Update an existing note
    updateNote: builder.mutation<OrderNotesType, { orderId: any; body: any }>({
      query: ({ orderId, body }) => ({
        url: ORDERS_API_ROUTES.CREATE_UPDATE_NOTE(orderId),
        method: 'PUT',
        body: body,
      }),
      invalidatesTags: (_result, _error, { orderId }) => [
        { type: 'Order', id: orderId }, // Invalidate the specific note cache
        { type: 'Order', id: 'LIST' }, // Invalidate the LIST cache for refetch
      ],
    }),

    deleteNote: builder.mutation<any, { orderId: number; orderNoteId: number }>(
      {
        query: ({ orderId, orderNoteId }) => ({
          url: ORDERS_API_ROUTES.DELETE_NOTE(orderId, orderNoteId),
          method: 'DELETE',
        }),
        transformResponse: (response) => {
          if (response?.message) {
            UseToast().success(response.message);
          }
          return response;
        },
        invalidatesTags: (_result, _error, { orderNoteId }) => [
          { type: 'Order', id: orderNoteId }, // Invalidate the specific note cache
          { type: 'Order', id: 'LIST' }, // Invalidate the LIST cache for refetch
        ],
      }
    ),

    GetNotesByOrderId: builder.query<ApiResponseDto<OrderNotesType[]>, number>({
      query: (id) => ORDERS_API_ROUTES.ALL_NOTE(id),
      providesTags: (result) => (result ? [{ type: 'Order', id: 'LIST' }] : []),
    }),

    cloneOrder: builder.mutation({
      query: ({ id, isCopy }) => ({
        url: ORDERS_API_ROUTES.CLONE(id, isCopy),
        method: 'POST',
        body: {},
      }),
    }),
    // get the all the check list
    getCheckList: builder.mutation<any, { orderId: string; body: any }>({
      query: ({ orderId, body }) => ({
        url: ORDERS_API_ROUTES.CHECKLIST(orderId),
        method: 'POST',
        body,
      }),
    }),
    // update the all the check list
    updateCheckList: builder.mutation<any, { body: any }>({
      query: ({ body }) => ({
        url: ORDERS_API_ROUTES.UPDATE_CHECKLIST,
        method: 'POST',
        body,
      }),
    }),

    // // missing equipment
    // getMissingEquipment: builder.mutation<any, { body: any }>({
    //   query: ({ body }) => ({
    //     url: ORDERS_API_ROUTES.MISSING_EQUIPMENT,
    //     method: 'POST',
    //     body,
    //   }),
    // }),

    createMissingEquipmentItems: builder.mutation<any, { body: any }>({
      query: ({ body }) => ({
        url: ORDERS_API_ROUTES.CREATE_MISSING_EQUIPMENT_ITEMS,
        method: 'POST',
        body,
      }),
      transformResponse: (response) => {
        if (response?.message) {
          UseToast().success(response.message);
        }
        return response;
      },
    }),

    // return order quantity
    returnOrder: builder.mutation<any, any>({
      query: (body) => ({
        url: ORDERS_API_ROUTES.RETURN_ORDER,
        method: 'POST',
        body,
      }),
      transformResponse: (response) => {
        if (response?.message) {
          UseToast().success(response.message);
        }
        return response;
      },
    }),

    // get order direction info
    getOrderDirection: builder.query<any, string>({
      query: (orderId) => ({
        url: ORDERS_API_ROUTES.GET_ORDER_DIRECTION(orderId),
        method: 'GET',
      }),
    }),

    // add update order direction info
    addUpdateOrderDirection: builder.mutation<any, any>({
      query: (body) => ({
        url: ORDERS_API_ROUTES.ADD_UPDATE_ORDER_DIRECTION(body?.orderId),
        method: 'POST',
        body,
      }),
      transformResponse: (response) => {
        if (response?.message) {
          UseToast().success(response.message);
        }
        return response;
      },
    }),

    // Order History
    getOrderHistory: builder.query<any, string>({
      query: (id) => ({
        url: ORDERS_API_ROUTES.ORDER_HISTORY(id),
        method: 'GET',
      }),
    }),

    // Cash Sale
    getCashSale: builder.mutation<ApiResponseDto<CashSaleTypes[]>, string>({
      query: (id) => ({
        url: ORDERS_API_ROUTES.CASH_SALE(id),
        method: 'GET',
      }),
    }),

    deleteKit: builder.mutation<any, { orderId: string; kitItemId: number }>({
      query: ({ orderId, kitItemId }) => ({
        url: ORDERS_API_ROUTES.DELETE_KIT(orderId, kitItemId),
        method: 'DELETE',
      }),
      transformResponse: (response) => {
        if (response?.message) {
          UseToast().success(response.message);
        }
        return response;
      },
      invalidatesTags: (_result, _error, { kitItemId }) => [
        { type: 'Order', id: kitItemId }, // Invalidate the specific note cache
        { type: 'Order', id: 'LIST' }, // Invalidate the LIST cache for refetch
      ],
    }),

    // get overbooked item info
    getOverbookedItemInfo: builder.query<any, any>({
      query: (body) => ({
        url: ORDERS_API_ROUTES.OVERBOOKED_ITEM_INFO,
        method: 'POST',
        body,
      }),
      keepUnusedDataFor: 120,
    }),

    // get overbooked item info
    getSerialInfoByItem: builder.query<any, string>({
      query: (itemId) => ({
        url: ORDERS_API_ROUTES.SERIAL_NOS(itemId),
        method: 'GET',
      }),
      keepUnusedDataFor: 120,
    }),
    // get invetory by location
    getInventoryBylocation: builder.query<any, any>({
      query: (body) => ({
        url: ORDERS_API_ROUTES.INVENTORY_BY_LOCATION,
        method: 'POST',
        body,
      }),
      keepUnusedDataFor: Infinity,
    }),

    // get TotalPayment for Order
    getTotalPayment: builder.query<any, string>({
      query: (orderId) => ({
        url: ORDERS_API_ROUTES.GET_TOTAL_PAYMENTS(orderId),
        method: 'GET',
      }),
      providesTags: ['Payments'],
    }),

    // UPDATE DELEVERY CHARGE
    updateDeleveryCharge: builder.mutation<ApiResponseDto<null>, string>({
      query: (orderId) => ({
        url: ORDERS_API_ROUTES.UPDATE_DELEVERY_CHARGE,
        method: 'GET',
        params: { orderId },
      }),
      transformResponse: (response) => {
        if (response?.message) {
          UseToast().success(response.message);
        }
        return response;
      },
    }),

    taxExempt: builder.mutation<any, { orderId: string; toExempt: boolean }>({
      query: ({ orderId, toExempt }) => ({
        url: ORDERS_API_ROUTES.TAX_EXEMPT(orderId, toExempt),
        method: 'GET',
      }),
    }),

    // Save item details for a given order
    saveTotalPayments: builder.mutation<ApiResponseDto<any>, { body: any }>({
      query: ({ body }) => ({
        url: ORDERS_API_ROUTES.SAVE_TOTAL_PAYMENTS,
        method: 'POST',
        body,
      }),
      transformResponse: (response) => {
        // Display success toast if the API response contains a message
        if (response?.message) UseToast().success(response.message);
        return response;
      },
      invalidatesTags: ['Payments'], // Invalidate cache for item details to reflect the update
    }),

    // get TotalPayment for Order
    recalCulate: builder.mutation<any, any>({
      query: ({ orderId, chargeType }) => ({
        url: ORDERS_API_ROUTES.RECALCULATE_PAYMENTS(orderId, chargeType),
        method: 'GET',
      }),
    }),

    getPaymentLink: builder.mutation<any, any>({
      query: (body) => ({
        url: ORDERS_API_ROUTES.GET_PAYMENT_LINK,
        method: 'POST',
        body,
      }),
      invalidatesTags: ['Payments'],
    }),

    processingPaymentSuccess: builder.mutation<PaymentCallbackType, any>({
      query: (body) => ({
        url: ORDERS_API_ROUTES.PAYMENT_SUCCESS,
        method: 'POST',
        body,
      }),
      transformResponse: (response) => {
        // Display success toast if the API response contains a message
        if (response?.message) UseToast().success(response.message);
        return response;
      },
    }),

    getCustomerCardInfo: builder.query<any, any>({
      query: ({ customerId }) => ({
        url: ORDERS_API_ROUTES.GET_CUSTOMER_CARD_INFO(customerId),
        method: 'GET',
      }),
      providesTags: ['CustomerCard'],
    }),

    removeCustomerCard: builder.mutation<any, any>({
      query: (cardId) => ({
        url: ORDERS_API_ROUTES.DELETE_CUSTOMER_CARD_INFO(cardId),
        method: 'DELETE',
      }),
      transformResponse: (response) => {
        // Display success toast if the API response contains a message
        if (response?.message) UseToast().success(response.message);
        return response;
      },
      invalidatesTags: ['CustomerCard'],
    }),

    processPaymentReuseCCACH: builder.mutation<any, any>({
      query: (body) => ({
        url: ORDERS_API_ROUTES.PAYMENT_REUSE_CC_ACH,
        method: 'POST',
        body,
      }),
      transformResponse: (response) => {
        // Display success toast if the API response contains a message
        if (response?.message) UseToast().success(response.message);
        return response;
      },
    }),

    getPaymentDetails: builder.query<
      ApiResponseDto<ExtendedPaymentInfoDTO[]>,
      { body: any }
    >({
      query: ({ body }) => ({
        url: ORDERS_API_ROUTES.GET_PAYMENT_DETAILS,
        method: 'POST',
        body,
      }),
      providesTags: ['Payments'],
    }),

    addPayment: builder.mutation<AddPaymentType, any>({
      query: (body) => ({
        url: ORDERS_API_ROUTES.ADD_PAYMENT,
        method: 'POST',
        body,
      }),
      transformResponse: (response) => {
        // Display success toast if the API response contains a message
        if (response?.message) UseToast().success(response.message);
        return response;
      },
      invalidatesTags: ['Payments'],
    }),

    removePayment: builder.mutation<
      any,
      { paymentId: number; voidTransaction: boolean }
    >({
      query: ({ paymentId, voidTransaction }) => ({
        url: ORDERS_API_ROUTES.DELETE_PAYMENT(paymentId, voidTransaction),
        method: 'DELETE',
      }),
      transformResponse: (response) => {
        if (response?.message) {
          UseToast().success(response.message);
        }
        return response;
      },
      invalidatesTags: ['Payments'],
    }),

    calculateConvFeeTax: builder.mutation<any, any>({
      query: ({ orderId, paymentTypeId, amount }) => ({
        url: ORDERS_API_ROUTES.CALCULATE_CONV_FEE_TAX(
          orderId,
          paymentTypeId,
          amount
        ),
        method: 'GET',
      }),
    }),

    getRefundAmount: builder.mutation<RefundPaymentType, any>({
      query: (body) => ({
        url: ORDERS_API_ROUTES.REFUND_PAYMENT,
        method: 'POST',
        body,
      }),
      transformResponse: (response) => {
        // Display success toast if the API response contains a message
        if (response?.message) UseToast().success(response.message);
        return response;
      },
      invalidatesTags: ['Payments'],
    }),

    getTaxCodeList: builder.mutation<any, any>({
      query: (body) => ({
        url: ORDERS_API_ROUTES.CHANGE_TAX_CODE,
        method: 'POST',
        body,
      }),
    }),

    // Get status order details
    getStatusDetails: builder.query<ApiResponseDto<StatusTypes>, string>({
      query: (orderId) => ({
        url: ORDERS_API_ROUTES.GET_STATUS_ORDER(orderId),
      }),
      providesTags: ['Status'],
    }),

    // Save status order details
    saveOrderStatus: builder.mutation<
      ApiResponseDto<StatusTypes>,
      { body: any }
    >({
      query: ({ body }) => ({
        url: ORDERS_API_ROUTES.SAVE_STATUS_ORDER,
        method: 'POST',
        body,
      }),
      transformResponse: (response) => {
        // Display success toast if the API response contains a message
        if (response?.message) UseToast().success(response.message);
        return response;
      },
      invalidatesTags: ['Status'],
    }),

    // update order status
    updateOrderStatus: builder.mutation<ApiResponseDto<null>, string>({
      query: (orderId) => ({
        url: ORDERS_API_ROUTES.UPDATE_ORDER_STATUS(orderId),
        method: 'GET',
      }),
      transformResponse: (response) => {
        // Display success toast if the API response contains a message
        if (response?.message) UseToast().success(response.message);
        return response;
      },
      invalidatesTags: ['Status'],
    }),

    // Get Order Sales & Referrals Sales Person API
    getSalesPersonSalesReferrals: builder.mutation<ApiResponseDto<any>, any>({
      query: (userId) => ({
        url: ORDERS_API_ROUTES.GET_SALES_PERSON_SALES_REFERRALS(userId),
        method: 'GET',
      }),
    }),

    getSalesCustomerSearch: builder.mutation<ApiResponseDto<any>, any>({
      query: (userId) => ({
        url: ORDERS_API_ROUTES.GET_SALES_REFERRALS_CUSTOMER_SERACH(userId),
        method: 'GET',
      }),
    }),

    // Availability Calendar
    getAvailabilitycalDates: builder.query<any, any>({
      query: (body) => ({
        url: ORDERS_API_ROUTES.GET_AVAILABILITY_DATES,
        method: 'POST',
        body,
      }),
    }),

    getAvailabilitycalOrders: builder.mutation<ApiResponseDto<any>, any>({
      query: (body) => ({
        url: ORDERS_API_ROUTES.GET_AVAILABILITY_ORDERS,
        method: 'POST',
        body,
      }),
    }),

    // Busy Calendar
    getBusyCalDates: builder.query<any, any>({
      query: (body) => ({
        url: ORDERS_API_ROUTES.GET_BUSY_DATES,
        method: 'POST',
        body,
      }),
    }),
    getBusyCalOrders: builder.mutation<ApiResponseDto<any>, any>({
      query: (body) => ({
        url: ORDERS_API_ROUTES.GET_BUSY_ORDERS,
        method: 'POST',
        body,
      }),
    }),
    setBusyCalStatus: builder.mutation<
      ApiResponseDto<any>,
      { date: string; busy: boolean }
    >({
      query: (body) => ({
        url: ORDERS_API_ROUTES.BUSY_CAL_SET_STATUS,
        method: 'POST',
        body,
      }),
      transformResponse: (response) => {
        // Display success toast if the API response contains a message
        if (response?.message) UseToast().success(response.message);
        return response;
      },
    }),

    // Get Busycal Reviewlevel
    getBusycalReviewlevel: builder.query<ApiResponseDto<any>, void>({
      query: () => ({
        url: ORDERS_API_ROUTES.BUSY_CAL_REVIEW_LEVEL,
        method: 'GET',
      }),
    }),

    // Get busy calendar status based on the selected date
    getBusyStatusByDate: builder.query<ApiResponseDto<any>, string>({
      query: (date) => ({
        url: ORDERS_API_ROUTES.GET_BUSY_CALENDAR_STATUS_BY_DATE(date),
        method: 'GET',
      }),
    }),
    // Save Busycal Reviewlevel
    updateBusycalReviewlevel: builder.mutation<ApiResponseDto<any>, any>({
      query: (body) => ({
        url: ORDERS_API_ROUTES.BUSY_CAL_REVIEW_LEVEL,
        method: 'POST',
        body,
      }),
      transformResponse: (response) => {
        if (response?.message) UseToast().success(response.message);
        return response;
      },
    }),
    //E-sign Documents
    getESignDocuments: builder.query<
      ApiResponseDto<ESignDocumentsTypes[]>,
      PaginationFilterPayload
    >({
      query: (body) => ({
        url: ORDERS_API_ROUTES.E_SIGN_DOCUMENTS,
        method: 'POST',
        body,
      }),
      keepUnusedDataFor: 0,
    }),

    sendRemainder: builder.mutation<
      ApiResponseDto<null>,
      ESignDocumentsPayloadTypes
    >({
      query: (body) => ({
        url: ORDERS_API_ROUTES.E_SIGN_REMAINDER,
        method: 'POST',
        body,
      }),
      transformResponse: (response) => {
        if (response?.message) UseToast().success(response.message);
        return response;
      },
    }),

    cancelESignLink: builder.mutation<
      ApiResponseDto<null>,
      ESignDocumentsPayloadTypes
    >({
      query: (body) => ({
        url: ORDERS_API_ROUTES.E_SIGN_CANCEL_LINK,
        method: 'POST',
        body,
      }),
      transformResponse: (response) => {
        if (response?.message) UseToast().success(response.message);
        return response;
      },
    }),
    // Fresh the e Sign data
    refreshESign: builder.query<ApiResponseDto<any>, void>({
      query: () => ({
        url: ORDERS_API_ROUTES.REFRESH_E_SIGN,
        method: 'GET',
      }),
      transformResponse: (response) => {
        if (response?.message) UseToast().success(response.message);
        return response;
      },
      keepUnusedDataFor: 0,
    }),

    // Additional Info (Assign Orders) GET API
    getAssignOption: builder.query<ApiResponseDto<any>, any>({
      query: (userId) =>
        ORDERS_API_ROUTES?.GET_ASSIGN_OPTIONS_ADDITIONAL_INFO(userId),
    }),

    // Additional Info (Assign Orders) SAVE API
    updateAssignOption: builder.mutation<
      { success: boolean },
      { isChecked: boolean }
    >({
      query: (data) => ({
        url: ORDERS_API_ROUTES?.SAVE_ASSIGN_OPTIONS_ADDITIONAL_INFO,
        method: 'POST',
        body: data,
      }),
    }),
    checkInactiveItems: builder.mutation<ApiResponseDto<any>, number>({
      query: (orderId) => ({
        url: ORDERS_API_ROUTES.CHECK_INACTIVE_ITEMS(orderId),
        method: 'GET',
      }),
    }),

    // ENUM API LEAD TYPE
    getEnumForLeadType: builder.query<ApiResponseDto<any>, any>({
      query: () => LEAD_TYPE_ENUMS?.GET,
    }),
  }),
});

// Export hooks for usage in components
export const {
  // useGetOrderMutation,
  useGetOrderByIdQuery,
  useUpdateOrderMutation,
  useSaveOrderEnquiryMutation,
  useDeleteOrderMutation,
  useGetStoreLocationsQuery,
  useGetCustomerTypeQuery,
  useGetOrderByListQuery,
  useDateCalculationMutation,
  useGetLinkedFilesListByIdQuery,
  useDeleteLinkedFileMutation,
  useDefaultLinkedFileMutation,
  useCreateNoteMutation,
  useUpdateNoteMutation,
  useDeleteNoteMutation,
  useGetNotesByOrderIdQuery,
  useGetOrderNoteByIdQuery,
  useCloneOrderMutation,
  useGetCheckListMutation,
  useUpdateCheckListMutation,
  // missing equipment
  // useGetMissingEquipmentMutation,
  // useGetMissingEquipmentItemsQuery,
  useCreateMissingEquipmentItemsMutation,
  // return order quantity
  useReturnOrderMutation,
  // order direction info
  useGetOrderDirectionQuery,
  useAddUpdateOrderDirectionMutation,
  // order history info
  useGetOrderHistoryQuery,
  useGetCashSaleMutation,
  // overbooked item info
  useGetOverbookedItemInfoQuery,
  useGetSerialInfoByItemQuery,
  useGetInventoryBylocationQuery,
  // Kit Components
  useDeleteKitMutation,
  // Total Payments
  useGetTotalPaymentQuery,
  useTaxExemptMutation,
  useSaveTotalPaymentsMutation,
  useGetPaymentLinkMutation,
  useProcessingPaymentSuccessMutation,
  useGetCustomerCardInfoQuery,
  useRemoveCustomerCardMutation,
  useProcessPaymentReuseCCACHMutation,
  useGetPaymentDetailsQuery,
  useAddPaymentMutation,
  useRemovePaymentMutation,
  useCalculateConvFeeTaxMutation,
  useGetRefundAmountMutation,
  // Order status
  useGetStatusDetailsQuery,
  useSaveOrderStatusMutation,
  useUpdateOrderStatusMutation,
  // Get & Save Order Addtional Info
  // useGetAdditionalInfoQuery,
  // useSaveOrderAdditionalInfoMutation,
  // Get & Save Order Addtional Info (Assign Job)
  // useLazyGetAdditionalInfoAssignJobQuery,
  // useSaveAdditionalInfoAssignJobMutation,
  // Get & Save Additional contact info in order (Assign Orders)
  // useLazyGetAdditionalInfoAssignOrdersQuery,
  // useSaveAdditionalInfoAssignOrdersMutation,
  // Get additional contact info in order (Breakdown)
  // useGetAdditionalInfoBreakdownQuery,
  // useSaveAdditionalInfoBreakdownMutation,
  // Get & Save Order Addtional Info (Shipping Info)
  // useLazyGetAdditionalInfoShippingInfoItemQuery,
  // useLazyGetAdditionalInfoShippingInfoBoxQuery,
  // useSplitRowInItemMutation,
  // useAddReturnBoxMutation,
  // useAddShippingBoxMutation,
  // useSaveAdditionalInfoShippingInfoBoxMutation,
  // useSaveAdditionalInfoShippingInfoItemMutation,
  // useLazyGetTrackShippmentQuery,
  useRecalCulateMutation,
  // Get & Save Order Sales & Referrals
  useGetSalesPersonSalesReferralsMutation,
  useGetSalesCustomerSearchMutation,
  // Availability Calendar
  useGetAvailabilitycalDatesQuery,
  useGetAvailabilitycalOrdersMutation,
  useGetTaxCodeListMutation,
  // Busy Calendar
  useGetBusyCalDatesQuery,
  useGetBusyCalOrdersMutation,
  useLazyGetBusyCalDatesQuery,
  useSetBusyCalStatusMutation,
  useGetBusycalReviewlevelQuery,
  useUpdateBusycalReviewlevelMutation,
  useLazyGetBusyStatusByDateQuery,

  // Additional Info (Assign Options)
  useGetAssignOptionQuery,
  useUpdateAssignOptionMutation,
  //  E-sign Documents
  useGetESignDocumentsQuery,
  useSendRemainderMutation,
  useCancelESignLinkMutation,
  useCheckInactiveItemsMutation,
  useLazyRefreshESignQuery,

  // ENUM API LEAD TYPE
  useGetEnumForLeadTypeQuery,

  // update delivery charge
  useUpdateDeleveryChargeMutation,
  // lock order
  useLazyLockedOrderQuery,
  // unlock order
  useLazyUnlockedOrderQuery,
} = orderApi;
