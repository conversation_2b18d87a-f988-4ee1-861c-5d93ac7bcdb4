import { UseToast } from '@/components/ui/toast/ToastContainer';
import { ORDERS_API_ROUTES } from '@/constants/api-constants';
import { baseQueryWithReauth } from '@/services/api';
import { ApiResponseDto } from '@/types/common.types';
import {
  AdditionalInfoTypes,
  CrewDetailsListsTypes,
  RequestAdditionalInfoTypes,
} from '@/types/order.types';
import { createApi } from '@reduxjs/toolkit/query/react';

export const additionalInfoApi = createApi({
  reducerPath: 'additionalInfoApi',
  baseQuery: baseQueryWithReauth,
  tagTypes: [
    'AdditionalInfo',
    'SalesReferrals',
    'ShippingInfoItem',
    'ShippingInfoBox',
    'CrewDetails',
  ],
  endpoints: (builder) => ({
    // Get additional info in order details
    getAdditionalInfo: builder.query<
      ApiResponseDto<AdditionalInfoTypes>,
      { orderId: string }
    >({
      query: ({ orderId }) => ({
        url: ORDERS_API_ROUTES.GET_ADDITIONAL_INFO(orderId),
      }),
      providesTags: ['AdditionalInfo'],
    }),

    // Save additional info order details
    saveOrderAdditionalInfo: builder.mutation<
      ApiResponseDto<AdditionalInfoTypes>,
      RequestAdditionalInfoTypes
    >({
      query: ({ body, orderId }) => ({
        url: ORDERS_API_ROUTES.SAVE_ADDITIONAL_INFO_ORDER(orderId),
        method: 'POST',
        body,
      }),
      transformResponse: (response) => {
        // Display success toast if the API response contains a message
        if (response?.message) UseToast().success(response.message);
        return response;
      },
      invalidatesTags: ['AdditionalInfo'],
    }),

    saveOrderAdditionalInfoForCrew: builder.mutation<
      ApiResponseDto<AdditionalInfoTypes>,
      RequestAdditionalInfoTypes
    >({
      query: ({ body, orderId }) => ({
        url: ORDERS_API_ROUTES.SAVE_ADDITIONAL_INFO_ORDER(orderId),
        method: 'POST',
        body,
      }),

      invalidatesTags: ['AdditionalInfo', 'CrewDetails'],
    }),

    // Get additional contact info in order (Assign Job)
    getAdditionalInfoAssignJob: builder.query<ApiResponseDto<any>, any>({
      query: ({ customerid }) => ({
        url: ORDERS_API_ROUTES.GET_ADDITIONAL_INFO_ASSIGN_JOB(customerid),
      }),
    }),

    // Save additional contact info in order (Assign Job)
    saveAdditionalInfoAssignJob: builder.mutation<ApiResponseDto<any>, any>({
      query: ({ orderid, jobno, assignjobno }) => ({
        url: ORDERS_API_ROUTES.SAVE_ADDITIONAL_INFO_ASSIGN_JOB(
          orderid,
          jobno,
          assignjobno
        ),
        method: 'GET',
      }),
      transformResponse: (response) => {
        if (response.statusCode === 200) {
          UseToast().success(response.message);
        }
        return response.data;
      },
      invalidatesTags: ['AdditionalInfo'],
    }),

    // Get additional contact info in order (Assign Orders)
    getAdditionalInfoAssignOrders: builder.query<ApiResponseDto<any>, any>({
      query: ({ customerid }) => ({
        url: ORDERS_API_ROUTES.GET_ADDITIONAL_INFO_ASSIGN_ORDERS(customerid),
      }),
    }),

    // Save additional contact info in order (Assign Orders)
    saveAdditionalInfoAssignOrders: builder.mutation<ApiResponseDto<any>, any>({
      query: ({ body, orderid, jobno }) => ({
        url: ORDERS_API_ROUTES.SAVE_ADDITIONAL_INFO_ASSIGN_ORDERS(
          orderid,
          jobno
        ),
        method: 'POST',
        body,
      }),
      transformResponse: (response) => {
        // Display success toast if the API response contains a message
        if (response?.message) UseToast().success(response.message);
        return response;
      },
      invalidatesTags: ['AdditionalInfo'],
    }),

    // Get additional contact info in order (Breakdown)
    getAdditionalInfoBreakdown: builder.query<ApiResponseDto<any>, any>({
      query: ({ orderId }) => ({
        url: ORDERS_API_ROUTES.GET_ADDITIONAL_INFO_BREAKDOWN(orderId),
      }),
    }),

    // Save additional contact info in order (Breakdown)
    saveAdditionalInfoBreakdown: builder.mutation<ApiResponseDto<any>, any>({
      query: ({ body }) => ({
        url: ORDERS_API_ROUTES.SAVE_ADDITIONAL_INFO_BREAKDOWN,
        method: 'POST',
        body,
      }),
      transformResponse: (response) => {
        // Display success toast if the API response contains a message
        if (response?.message) UseToast().success(response.message);
        return response;
      },
      invalidatesTags: ['AdditionalInfo'],
    }),

    // Get Order Additional Info ITEM Table (Shipping Info)
    getAdditionalInfoShippingInfoItem: builder.query<
      ApiResponseDto<any>,
      string
    >({
      query: (orderId) => ({
        url: ORDERS_API_ROUTES.GET_ADDITIONAL_INFO_SHIPPING_INFO_ITEM(orderId),
        method: 'GET',
      }),
      providesTags: ['ShippingInfoItem'],
    }),

    // Get Order Additional Info BOX Table (Shipping Info)
    getAdditionalInfoShippingInfoBox: builder.query<
      ApiResponseDto<any>,
      string
    >({
      query: (orderId) => ({
        url: ORDERS_API_ROUTES.GET_ADDITIONAL_INFO_SHIPPING_INFO_BOX(orderId),
        method: 'GET',
      }),
      providesTags: ['ShippingInfoBox'],
    }),

    // Add Shipping box in the BOX Table (Shipping Info)
    addShippingBox: builder.mutation<any, any>({
      query: ({ orderId }) => ({
        url: ORDERS_API_ROUTES.ADD_SHIPPING_BOX(orderId),
        method: 'POST',
        body: { orderId },
      }),
      transformResponse: (response) => {
        if (response.statusCode === 200) {
          UseToast().success(response.message);
        }
        return response.data;
      },
      invalidatesTags: ['ShippingInfoBox'],
    }),

    // Add Return box in the BOX Table (Shipping Info)
    addReturnBox: builder.mutation<any, any>({
      query: ({ orderId }) => ({
        url: ORDERS_API_ROUTES.ADD_RETURN_BOX(orderId),
        method: 'POST',
        body: { orderId },
      }),
      transformResponse: (response) => {
        if (response.statusCode === 200) {
          UseToast().success(response.message);
        }
        return response.data;
      },
      invalidatesTags: ['ShippingInfoBox'],
    }),

    // Split row in the Item Table (Shipping Info)
    splitRowInItem: builder.mutation<ApiResponseDto<any>, any>({
      query: ({ orderId, itemId }) => ({
        url: ORDERS_API_ROUTES.SPLIT_ITEM_ROW(orderId, itemId),
        method: 'GET',
      }),
      invalidatesTags: ['ShippingInfoItem'],
    }),

    // Save Order Additional Info Item Table (Shipping Info)
    saveAdditionalInfoShippingInfoItem: builder.mutation<
      ApiResponseDto<any>,
      any
    >({
      query: ({ orderId, body }) => ({
        url: ORDERS_API_ROUTES.SAVE_ADDITIONAL_INFO_SHIPPING_INFO_ITEM(orderId),
        method: body?.id ? 'POST' : 'PUT',
        body,
      }),
      transformResponse: (response) => {
        if (response.statusCode === 200) {
          UseToast().success(response.message);
        }
        return response.data;
      },
      invalidatesTags: ['ShippingInfoItem', 'ShippingInfoBox'],
    }),

    // Save Order Additional Info Box Table (Shipping Info)
    saveAdditionalInfoShippingInfoBox: builder.mutation<
      ApiResponseDto<any>,
      any
    >({
      query: ({ orderId, body }) => ({
        url: body?.id
          ? ORDERS_API_ROUTES.UPDATE_ADDITIONAL_INFO_SHIPPING_INFO_BOX(orderId)
          : ORDERS_API_ROUTES.SAVE_ADDITIONAL_INFO_SHIPPING_INFO_BOX(orderId),
        method: body?.id ? 'POST' : 'PUT',
        body,
      }),
      transformResponse: (response) => {
        if (response.statusCode === 200) {
          UseToast().success(response.message);
        }
        return response.data;
      },
      invalidatesTags: ['ShippingInfoBox'],
    }),

    // Track Shippment in the BOX Table (Shipping Info)
    getTrackShippment: builder.query<ApiResponseDto<any>, any>({
      query: ({ orderId, pkId }) => ({
        url: ORDERS_API_ROUTES.TRACK_SHIPPMENT(orderId, pkId),
        method: 'GET',
      }),
    }),

    // Missing Equipment (Shipping Info)
    missingEqu: builder.mutation<any, string>({
      query: (orderId) => ({
        url: ORDERS_API_ROUTES.CREATE_MISSING_EQUIPMENT(orderId),
        method: 'GET',
      }),
      transformResponse: (response) => {
        if (response.statusCode === 200) {
          UseToast().success(response.message);
        }
        return response.data;
      },
      invalidatesTags: ['ShippingInfoItem', 'ShippingInfoBox'],
    }),

    // ENUM for Shipping Info Status
    getStatusForShippingInfo: builder.query<ApiResponseDto<any>, any>({
      query: () => ({
        url: ORDERS_API_ROUTES.GET_STATUS_SHIPPING_INFO,
        method: 'GET',
      }),
    }),

    getInstallationCrewDetails: builder.query<
      ApiResponseDto<CrewDetailsListsTypes[]>,
      { departmentId: string; leadType: string; orderId: string }
    >({
      query: ({ departmentId, leadType, orderId }) =>
        ORDERS_API_ROUTES.GET_CREW_DETAILS({ departmentId, leadType, orderId }),
      providesTags: ['CrewDetails'],
    }),
    updateCrewDetails: builder.mutation<
      ApiResponseDto<null>,
      { orderId: string; body: CrewDetailsListsTypes[] }
    >({
      query: ({ orderId, body }) => ({
        url: ORDERS_API_ROUTES.SAVE_CREW_DETAILS(orderId),
        method: 'POST',
        body,
      }),
      transformResponse: (response) => {
        if (response.statusCode === 200) {
          UseToast().success(response.message);
        }
        return response.data;
      },
      invalidatesTags: ['CrewDetails'],
    }),
  }),
});

export const {
  useLazyGetAdditionalInfoShippingInfoItemQuery,
  useGetAdditionalInfoShippingInfoItemQuery,
  useLazyGetAdditionalInfoShippingInfoBoxQuery,
  useGetAdditionalInfoShippingInfoBoxQuery,
  useLazyGetAdditionalInfoAssignJobQuery,
  useGetAdditionalInfoQuery,
  useSplitRowInItemMutation,
  useAddReturnBoxMutation,
  useAddShippingBoxMutation,
  useSaveAdditionalInfoShippingInfoBoxMutation,
  useSaveAdditionalInfoShippingInfoItemMutation,
  useSaveAdditionalInfoAssignJobMutation,
  useLazyGetTrackShippmentQuery,
  useMissingEquMutation,
  useLazyGetAdditionalInfoAssignOrdersQuery,
  useSaveAdditionalInfoAssignOrdersMutation,
  useSaveOrderAdditionalInfoMutation,
  useGetStatusForShippingInfoQuery,
  useSaveOrderAdditionalInfoForCrewMutation,

  useGetInstallationCrewDetailsQuery,
  useUpdateCrewDetailsMutation,
} = additionalInfoApi;
