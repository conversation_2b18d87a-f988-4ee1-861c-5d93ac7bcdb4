import { UseToast } from '@/components/ui/toast/ToastContainer';
import { ORDER_ITEM_INFO_API } from '@/constants/api-constants';
import { baseQueryWithReauth } from '@/services/api';
import { ApiResponseDto } from '@/types/common.types';
import { LinkedFilesTypes } from '@/types/item.types';
import {
  AdditionalEmailInfoPayloadTypes,
  AdditionalItemInfoTypes,
  EmailListTypes,
} from '@/types/orders/order-item-details.types';
import { createApi } from '@reduxjs/toolkit/query/react';

export const additionalItemInfoApi = createApi({
  reducerPath: 'additionalItemInfoApi',
  baseQuery: baseQueryWithReauth,
  tagTypes: ['AdditionalItemInfo', 'LinkedFiles'],
  endpoints: (builder) => ({
    additionalItemInfo: builder.query<
      ApiResponseDto<AdditionalItemInfoTypes>,
      { orderId: string; orderItemId: string }
    >({
      query: ({ orderId, orderItemId }) =>
        ORDER_ITEM_INFO_API.GET({ orderId, orderItemId }),
      providesTags: ['AdditionalItemInfo'],
    }),

    getLinkedFilesList: builder.query<
      ApiResponseDto<LinkedFilesTypes[]>,
      string
    >({
      query: (orderItemId) => ORDER_ITEM_INFO_API.LIST(orderItemId),
      providesTags: ['LinkedFiles'],
    }),

    deleteLinkedFile: builder.mutation<
      ApiResponseDto<LinkedFilesTypes>,
      number
    >({
      query: (fileId) => {
        return {
          url: ORDER_ITEM_INFO_API.DELETE(fileId),
          method: 'DELETE',
        };
      },
      invalidatesTags: (_result, _error, fileId) => {
        return [{ type: 'LinkedFiles', id: fileId }];
      },
    }),
    defaultLinkedFile: builder.mutation<
      ApiResponseDto<LinkedFilesTypes>,
      number
    >({
      query: (fileId) => {
        return {
          url: ORDER_ITEM_INFO_API.DEFAULT(fileId),
          method: 'POST',
          body: {},
        };
      },
      invalidatesTags: (_result, _error, fileId) => {
        return [{ type: 'LinkedFiles', id: fileId }];
      },
    }),

    getEmailsList: builder.query<ApiResponseDto<EmailListTypes[]>, string>({
      query: (customerId) => ORDER_ITEM_INFO_API.GET_EMAIL_LIST(customerId),
    }),

    sendEmail: builder.mutation<
      ApiResponseDto<LinkedFilesTypes>,
      {
        body: AdditionalEmailInfoPayloadTypes;
        orderId: string;
        orderItemId: string;
      }
    >({
      query: ({ body, orderId, orderItemId }) => {
        return {
          url: ORDER_ITEM_INFO_API.SEND({ orderId, orderItemId }),
          method: 'POST',
          body: body,
        };
      },
      transformResponse: (response) => {
        // Display success toast if the API response contains a message
        if (response?.message) UseToast().success(response.message);
        return response;
      },
    }),
  }),
});

export const {
  useAdditionalItemInfoQuery,
  useGetLinkedFilesListQuery,
  useDefaultLinkedFileMutation,
  useDeleteLinkedFileMutation,
  useSendEmailMutation,
  useGetEmailsListQuery,
} = additionalItemInfoApi;
