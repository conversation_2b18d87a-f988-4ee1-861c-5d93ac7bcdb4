import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// Define the interface for filter state
interface OrderState {
  filters: Array<{
    label: string;
    value: string;
    name: string;
    operator: string;
  }>;
  formValues: {
    customer: string;
    eventDescription: string;
    total: string | null;
    shipLocation: string | null;
    address: string;
    location: string | null;
    address2: string;
    town: string;
    state: string;
    zipCode: string | null;
    orderedBy: string | null;
    orderType: string | null;
    range: string;
    purchaseOrderNo: string;
    salesperson: string;
    enteredBy: string | null;
    deliveryOption: string | null;
    customerType: string;
    webOrderNo: string;
    dateOfUseFrom: string;
    dateOfUseThru: string;
    dateType: string;
    operator: string;
    searchBy: string;
  };
}

// Initial state with default values matching the Filter component
const initialState: OrderState = {
  filters: [],
  formValues: {
    customer: '',
    eventDescription: '',
    total: '',
    shipLocation: '',
    location: '',
    address: '',
    address2: '',
    town: '',
    state: '',
    zipCode: '',
    orderedBy: '',
    orderType: '',
    range: '',
    purchaseOrderNo: '',
    salesperson: '',
    enteredBy: '',
    deliveryOption: '',
    customerType: '',
    webOrderNo: '',
    dateOfUseFrom: '',
    dateOfUseThru: '',
    operator: 'Contains',
    dateType: 'dateOfUseFrom',
    searchBy: '',
  },
};

const orderSlice = createSlice({
  name: 'order',
  initialState,
  reducers: {
    // Set filters based on form data
    setFilter(
      state,
      action: PayloadAction<
        Array<{ label: string; value: string; name: string; operator: string }>
      >
    ) {
      state.filters = action.payload;
    },

    // Update form values
    updateFormValues(
      state,
      action: PayloadAction<Partial<OrderState['formValues']>>
    ) {
      state.formValues = {
        ...state.formValues,
        ...action.payload,
      };
    },

    // Clear all filters and reset form values
    clearAllFilters(state) {
      state.filters = [];
      state.formValues = initialState.formValues;
    },

    // Clear a specific filter
    clearFilter(state, action: PayloadAction<string>) {
      state.filters = state.filters.filter(
        (filter) => filter.name !== action.payload
      );
      switch (action.payload) {
        case 'customer':
          state.formValues.searchBy = '';
          break;
        case 'eventDescription':
          state.formValues.searchBy = '';
          break;
        case 'total':
          state.formValues.searchBy = '';
          break;
        case 'location':
          state.formValues.location = '';
          break;
        case 'shipLocation':
          state.formValues.searchBy = '';
          break;
        case 'address':
          state.formValues.searchBy = '';
          break;
        case 'address2':
          state.formValues.searchBy = '';
          break;
        case 'town':
          state.formValues.searchBy = '';
          break;
        case 'state':
          state.formValues.searchBy = '';
          break;
        case 'zipCode':
          state.formValues.searchBy = '';
          break;
        case 'orderedBy':
          state.formValues.searchBy = '';
          break;
        case 'purchaseOrderNo':
          state.formValues.searchBy = '';
          break;
        case 'salesperson':
          state.formValues.searchBy = '';
          break;
        case 'enteredBy':
          state.formValues.searchBy = '';
          break;
        case 'deliveryOption':
          state.formValues.searchBy = '';
          break;
        case 'customerType':
          state.formValues.searchBy = '';
          break;
        case 'webOrderNo':
          state.formValues.searchBy = '';
          break;
        case 'orderType':
          state.formValues.orderType = '';
          break;
        case 'range':
          state.formValues.range = '';
          break;

        // Add more cases as needed
      }
    },
  },
});

export const { setFilter, clearFilter, clearAllFilters, updateFormValues } =
  orderSlice.actions;

export default orderSlice.reducer;
