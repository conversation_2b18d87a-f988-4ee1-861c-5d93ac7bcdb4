import { UseToast } from '@/components/ui/toast/ToastContainer';
import {
  CHANGE_SERIALIZED_ORDER_ITEMS_API_ROUTES,
  ORDER_ITEM_DETAILS_API_ROUTES,
  ORDER_SUB_RENT_ITEM_API_ROUTES,
  ORDERS_API_ROUTES,
  PRINT_EMAIL_API_ROUTES,
} from '@/constants/api-constants';
import { baseQueryWithReauth } from '@/services/api';
import { ApiResponseDto, PaginationFilterPayload } from '@/types/common.types';
import { ItemLookupDropdownType } from '@/types/item.types';
import { OrderItemLookupTypes } from '@/types/order.types';
import {
  ChangeSerializedItemPayloadTypes,
  ChangeSerializedItemTypes,
  ChangeSortingTypes,
  CustomerVendorLookupTypes,
  KitItemListTypes,
  OrderSubRentsTypes,
  SubRentDetailTypes,
  SubRentItemListTypes,
  SubRentPayload,
} from '@/types/orders/order-item-details.types';
import { PrintEmailListTypes } from '@/types/orders/print-email-order.types';
import { createApi } from '@reduxjs/toolkit/query/react';

/**
 * This API slice handles item details CRUD operations,
 * including fetching, updating, deleting, and saving order items.
 */
export const itemDetailsApi = createApi({
  reducerPath: 'itemDetailsApi',
  baseQuery: baseQueryWithReauth,
  tagTypes: ['ItemDetails', 'SubRent', 'KitItem', 'SalesReferrals'],
  endpoints: (builder) => ({
    // Fetch list of items for a given order
    getItemList: builder.query<ApiResponseDto<any>, { orderId: string }>({
      query: ({ orderId }) => ({
        url: ORDER_ITEM_DETAILS_API_ROUTES.LIST_ORDER_ITEMS,
        params: { orderId },
      }),
      providesTags: ['ItemDetails'],
    }),

    // Fetch item lookup dropdown data based on pagination and filter
    getItemLookup: builder.query<
      ApiResponseDto<ItemLookupDropdownType[]>,
      { url: string; body: PaginationFilterPayload }
    >({
      query: ({ url, body }) => ({
        url,
        method: 'POST',
        body,
      }),
    }),

    // Fetch detailed list of items for lookup based on pagination and filter
    getItemLookupList: builder.mutation<
      ApiResponseDto<OrderItemLookupTypes[]>,
      { url: string; body: PaginationFilterPayload }
    >({
      query: ({ url, body }) => ({
        url,
        method: 'POST',
        body,
      }),
    }),

    // Save item details for a given order
    saveItemDetails: builder.mutation<
      ApiResponseDto<any>,
      { orderId: string; body: any }
    >({
      query: ({ orderId, body }) => ({
        url: ORDER_ITEM_DETAILS_API_ROUTES.SAVE(orderId),
        method: 'POST',
        body,
      }),
      transformResponse: (response) => {
        // Display success toast if the API response contains a message
        if (response?.message) UseToast().success(response.message);
        return response;
      },
      invalidatesTags: ['ItemDetails', 'KitItem', 'SalesReferrals'], // Invalidate cache for item details to reflect the update
    }),

    // Fetch details of a specific item within an order
    getItemDetails: builder.mutation<
      ApiResponseDto<any>,
      { orderId: string; itemId: string }
    >({
      query: ({ orderId, itemId }) => ({
        url: ORDER_ITEM_DETAILS_API_ROUTES.ITEM_BRIEF,
        params: { orderId, itemId },
      }),
    }),

    // Delete an order item by ID
    deleteOrderItem: builder.mutation<void, { orderItemId: number }>({
      query: ({ orderItemId }) => ({
        url: ORDER_ITEM_DETAILS_API_ROUTES.DELETE,
        method: 'DELETE',
        params: { orderItemId },
      }),
      transformResponse: (response) => {
        if (response.statusCode === 200) {
          UseToast().success(response.message); // Show success toast on successful deletion
        }
        return response.data; // Return the data directly (we may not need the whole response object)
      },
      invalidatesTags: ['ItemDetails', 'KitItem', 'SalesReferrals'], // Invalidate cache to ensure fresh data
    }),

    updateSurgeCharge: builder.mutation<
      void,
      { orderId: string; surgeRateId: string | null }
    >({
      query: ({ orderId, surgeRateId }) => ({
        url: ORDER_ITEM_DETAILS_API_ROUTES.UPDATE_SURGE,
        method: 'POST',
        params: { orderId, surgeRateId },
      }),
      transformResponse: (response) => {
        if (response.statusCode === 200) {
          UseToast().success(response.message);
        }
        return response.data;
      },
      invalidatesTags: ['ItemDetails'],
    }),

    /** SUBRENT API */
    subRentItem: builder.query<
      ApiResponseDto<SubRentItemListTypes>,
      {
        orderId: string;
        itemId: string;
        body: { sortBy: string; sortAscending: boolean };
      }
    >({
      query: ({ orderId, itemId, body }) => ({
        url: ORDER_SUB_RENT_ITEM_API_ROUTES.GET({ orderId, itemId }),
        method: 'POST',
        body,
      }),

      providesTags: ['SubRent'],
    }),
    customerVendorSearch: builder.query<
      ApiResponseDto<CustomerVendorLookupTypes[]>,
      { body: PaginationFilterPayload }
    >({
      query: ({ body }) => ({
        url: ORDER_SUB_RENT_ITEM_API_ROUTES.LOOKUP,
        method: 'POST',
        body,
      }),
    }),
    subRentDetail: builder.query<
      ApiResponseDto<SubRentDetailTypes>,
      { orderId: string }
    >({
      query: ({ orderId }) => ({
        url: ORDER_SUB_RENT_ITEM_API_ROUTES.SUBRENT_DETAIL({
          orderId,
          contactTypeValue: 0,
          custVenId: 0,
        }),
      }),
    }),
    addSubRent: builder.mutation<
      ApiResponseDto<null>,
      { body: SubRentPayload; orderItemId: number }
    >({
      query: ({ body, orderItemId }) => ({
        url: ORDER_SUB_RENT_ITEM_API_ROUTES.SAVE({
          orderId: body.orderId,
          orderItemId,
        }),
        method: 'POST',
        body,
      }),
      transformResponse: (response) => {
        // Display success toast if the API response contains a message
        if (response?.message) UseToast().success(response.message);
        return response;
      },
      invalidatesTags: ['SubRent', 'KitItem'],
    }),

    deleteSubRentItem: builder.mutation<
      void,
      { orderItemId: number; orderId: string }
    >({
      query: ({ orderItemId, orderId }) => ({
        url: ORDER_SUB_RENT_ITEM_API_ROUTES.DELETE({
          orderId,
          orderItemId,
        }),
        method: 'DELETE',
      }),
      transformResponse: (response) => {
        if (response.statusCode === 200) {
          UseToast().success(response.message); // Show success toast on successful deletion
        }
        return response.data; // Return the data directly (we may not need the whole response object)
      },
      invalidatesTags: ['SubRent'], // Invalidate cache to ensure fresh data
    }),

    subRentByOrderId: builder.mutation<
      ApiResponseDto<OrderSubRentsTypes[]>,
      number
    >({
      query: (orderId) => ({
        url: ORDER_SUB_RENT_ITEM_API_ROUTES.SUBRENT_BY_ORDERID(orderId),
        method: 'GET',
      }),
    }),
    changeSorting: builder.mutation<
      ApiResponseDto<null>,
      { body: ChangeSortingTypes }
    >({
      query: ({ body }) => ({
        url: ORDER_ITEM_DETAILS_API_ROUTES.CHANGE_SORTING,
        method: 'POST',
        body,
      }),
      transformResponse: (response) => {
        // Display success toast if the API response contains a message
        if (response?.message) UseToast().success(response.message);
        return response;
      },
      invalidatesTags: ['ItemDetails'],
    }),

    getMissingItemsList: builder.query<ApiResponseDto<any>, string>({
      query: (orderId) => ({
        url: ORDER_ITEM_DETAILS_API_ROUTES.MISSING_ITEMS(orderId),
      }),
      providesTags: ['ItemDetails'],
    }),
    getChangeItemList: builder.query<
      ApiResponseDto<ChangeSerializedItemTypes[]>,
      { body: ChangeSerializedItemPayloadTypes }
    >({
      query: ({ body }) => ({
        url: CHANGE_SERIALIZED_ORDER_ITEMS_API_ROUTES.GET,
        method: 'POST',
        body,
      }),
    }),
    getKitList: builder.query<ApiResponseDto<KitItemListTypes[]>, string>({
      query: (id) => ORDERS_API_ROUTES.KIT_LIST(id),
      providesTags: ['KitItem'],
    }),
    changeSerializeItem: builder.mutation<
      ApiResponseDto<ChangeSerializedItemTypes[]>,
      { orderItemId: number; inventoryId: string }
    >({
      query: ({ orderItemId, inventoryId }) => ({
        url: CHANGE_SERIALIZED_ORDER_ITEMS_API_ROUTES.POST({
          inventoryId,
          orderItemId,
        }),
        method: 'POST',
        body: {},
      }),
      transformResponse: (response) => {
        // Display success toast if the API response contains a message
        if (response?.message) UseToast().success(response.message);
        return response;
      },
      invalidatesTags: ['ItemDetails', 'KitItem'],
    }),
    // Print Email and Order
    getPrintFormList: builder.query<
      ApiResponseDto<PrintEmailListTypes[]>,
      string
    >({
      query: (name) => PRINT_EMAIL_API_ROUTES.LIST(name),
    }),

    // Get Order Sales & Referrals
    getSalesReferrals: builder.query<ApiResponseDto<any>, any>({
      query: ({ orderId }) => ({
        url: ORDERS_API_ROUTES.GET_SALES_REFERRALS(orderId),
        method: 'GET',
      }),
      providesTags: ['SalesReferrals'],
    }),

    // Save Order Sales & Referrals
    saveSalesReferrals: builder.mutation<ApiResponseDto<any>, any>({
      query: ({ body }) => ({
        url: ORDERS_API_ROUTES.SAVE_SALES_REFERRALS,
        method: 'POST',
        body,
      }),
      transformResponse: (response) => {
        // Display success toast if the API response contains a message
        if (response?.message) UseToast().success(response.message);
        return response;
      },
      invalidatesTags: ['SalesReferrals'],
    }),
  }),
});

// Export hooks for each API endpoint
export const {
  useGetItemListQuery,
  useGetItemLookupQuery,
  useSaveItemDetailsMutation,
  useGetItemLookupListMutation,
  useGetItemDetailsMutation,
  useDeleteOrderItemMutation,
  useUpdateSurgeChargeMutation,
  useChangeSortingMutation,
  useGetMissingItemsListQuery,
  useGetChangeItemListQuery,
  useChangeSerializeItemMutation,
  useGetKitListQuery,

  // SubRent
  useCustomerVendorSearchQuery,
  useSubRentItemQuery,
  useSubRentDetailQuery,
  useAddSubRentMutation,
  useDeleteSubRentItemMutation,
  useSubRentByOrderIdMutation,

  // Print and Email Order
  useGetPrintFormListQuery,

  useGetSalesReferralsQuery,
  useSaveSalesReferralsMutation,
} = itemDetailsApi;
