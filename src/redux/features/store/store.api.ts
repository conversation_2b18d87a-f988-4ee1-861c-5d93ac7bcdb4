import { UseToast } from '@/components/ui/toast/ToastContainer';
import {
  CONFIG_API,
  STORE_OPTIONS_API_ROUTES,
} from '@/constants/api-constants';
import { baseQueryWithReauth } from '@/services/api';
import { ApiResponseDto } from '@/types/common.types';
import { createApi } from '@reduxjs/toolkit/query/react';

// Define the API service using RTK Query
export const storeApi = createApi({
  reducerPath: 'storeApi',
  baseQuery: baseQueryWithReauth,
  tagTypes: ['store', 'UserDefaultStore', 'UserOptions'],
  endpoints: (builder) => ({
    // GET request to fetch the company data
    getStore: builder.query<ApiResponseDto<any>, string>({
      query: (id) => STORE_OPTIONS_API_ROUTES.GET(id),
      providesTags: ['store'],
    }),

    postStore: builder.mutation<
      ApiResponseDto<any>,
      { data: any; url: string }
    >({
      query: ({ data, url }) => ({
        url: url,
        method: 'POST',
        body: data,
      }),
      transformResponse: (response) => {
        if (response?.message) {
          UseToast().success(response.message);
        }
        // Return the response as it is after processing it
        return response;
      },

      invalidatesTags: ['store', 'UserOptions'],
    }),
    cloneStore: builder.mutation({
      query: (id: number) => ({
        url: STORE_OPTIONS_API_ROUTES.CLONE(id),
        method: 'POST',
        body: {},
      }),
    }),
    deleteStore: builder.mutation({
      query: (id: number) => ({
        url: `${STORE_OPTIONS_API_ROUTES.DELETE(id)}`,
        method: 'DELETE',
      }),
    }),
    getUserDefaultStore: builder.query<any, void>({
      query: () => STORE_OPTIONS_API_ROUTES.USER_DEFAULT_STORE,
      keepUnusedDataFor: 120, // Cache persists for 2 minutes (120 seconds)
      providesTags: ['UserDefaultStore'], // Enables cache invalidation if needed
    }),

    // get Store Location
    getStoreLocations: builder.query<any, void>({
      query: () => ({
        url: CONFIG_API.storeLocations,
        method: 'GET',
      }),
      providesTags: ['UserOptions'],
    }),
  }),
});

export const {
  useGetStoreQuery,
  useLazyGetStoreQuery,
  usePostStoreMutation,
  useCloneStoreMutation,
  useDeleteStoreMutation,
  useGetUserDefaultStoreQuery,
  useGetStoreLocationsQuery,
} = storeApi;
