import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { logoutUser } from '../auth/authSlice';

interface SessionState {
  isToastPending: boolean;
  logoutTimerId: number | null;
}

const initialState: SessionState = {
  isToastPending: false,
  logoutTimerId: null,
};

const sessionSlice = createSlice({
  name: 'session',
  initialState,
  reducers: {
    setToastPending: (state, action: PayloadAction<boolean>) => {
      state.isToastPending = action.payload;
    },
    setLogoutTimer: (state, action: PayloadAction<number>) => {
      state.logoutTimerId = action.payload;
    },
    clearLogoutTimer: (state) => {
      if (state.logoutTimerId) {
        clearTimeout(state.logoutTimerId);
        state.logoutTimerId = null;
      }
    },
  },
});

export const enhancedLogout = () => (dispatch: any) => {
  localStorage.clear();
  sessionStorage.clear();
  dispatch(clearLogoutTimer());
  dispatch(setToastPending(false));
  dispatch(logoutUser());
  setTimeout(() => {
    window.location.href = '/login';
  }, 0);
};

export const { setToastPending, setLogoutTimer, clearLogoutTimer } =
  sessionSlice.actions;
export default sessionSlice.reducer;
