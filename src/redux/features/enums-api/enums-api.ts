import { STORE_LOCATIONS_ENUMS } from '@/constants/api-constants';
import { baseQueryWithReauth } from '@/services/api';
import { createApi } from '@reduxjs/toolkit/query/react';

export const enumsApi = createApi({
  reducerPath: 'enumsApi',
  baseQuery: baseQueryWithReauth,
  endpoints: (builder) => ({
    getEnumsList: builder.query<any, { name: string; isAsc?: boolean }>({
      query: ({ name, isAsc }) => ({
        url: STORE_LOCATIONS_ENUMS.GET(name, isAsc),
        method: 'GET',
      }),
      keepUnusedDataFor: Infinity, // Ensures cache persists until refresh
    }),
  }),
});

export const { useGetEnumsListQuery } = enumsApi;
