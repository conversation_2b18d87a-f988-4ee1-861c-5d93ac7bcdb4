import { AUTH_API_ROUTES } from '@/constants/api-constants';
import { baseQueryWithReauth } from '@/services/api';
import { TenantListType, TenantType } from '@/types/tenant.types';
import { createApi } from '@reduxjs/toolkit/query/react';
import { TENANT_API_ROUTES } from './../../../constants/tenant-constants';
import { CommonResponseType } from '@/types/auth.types';
import { UseToast } from '@/components/ui/toast/ToastContainer';

export const accountAPI = createApi({
  reducerPath: 'accountAPI',
  baseQuery: baseQueryWithReauth,
  tagTypes: ['Accounts'],
  endpoints: (builder) => ({
    // Get a list of customers
    getTenants: builder.mutation<CommonResponseType<TenantListType[]>, void>({
      query: (body) => ({
        url: TENANT_API_ROUTES.ALL,
        method: 'POST',
        body,
      }),
      transformResponse: (response) => {
        if (response.statusCode === 200) {
          return response; // Return the data directly
        }
      },
    }),

    // Create a new customer
    createTenat: builder.mutation<TenantType, any>({
      query: (newCustomer) => ({
        url: TENANT_API_ROUTES.CREATE,
        method: 'POST',
        body: newCustomer,
      }),
      transformResponse: (response: {
        success: boolean;
        statusCode: number;
        message: string;
        data: TenantType;
      }) => {
        if (response.statusCode === 200) {
          UseToast().success(response.message);
          return response.data; // Return the data directly
        }
        return <TenantType>{}; // Fallback to an empty array in case of failure
      },
      invalidatesTags: [{ type: 'Accounts', id: 'LIST' }],
    }),

    // Get customer by ID
    getTenantById: builder.query<TenantType, string>({
      query: (id) => ({
        url: `${TENANT_API_ROUTES.GET}/${id}`,
        method: 'GET',
      }),
      transformResponse: (response: {
        success: boolean;
        statusCode: number;
        message: string;
        data: TenantType;
      }) => {
        if (response.statusCode === 200) {
          return response.data; // Return the data directly
        }
        return <TenantType>{}; // Fallback to an empty array in case of failure
      },
      providesTags: (result) =>
        result ? [{ type: 'Accounts', id: result.id }] : [],
    }),

    // Update an existing customer
    updateTenant: builder.mutation<TenantType, { id: number; clientData: any }>(
      {
        query: ({ id, clientData }) => ({
          url: `${TENANT_API_ROUTES.GET}/${id}`,
          method: 'PUT',
          body: clientData,
        }),

        transformResponse: (response: {
          success: boolean;
          statusCode: number;
          message: string;
          data: TenantType;
        }) => {
          if (response.statusCode === 200) {
            UseToast().success(response.message);
            return response.data; // Return the data directly
          }
          return <TenantType>{}; // Fallback to an empty array in case of failure
        },
        // Invalidate the cached data or refetch
        invalidatesTags: (_result, _error, { id }) => [
          { type: 'Accounts', id: id },
          { type: 'Accounts', id: 'LIST' },
        ],
      }
    ),

    // Delete a customer
    deleteTenant: builder.mutation<void, number>({
      query: (id) => ({
        url: `${TENANT_API_ROUTES.DELETE}/${id}`,
        method: 'DELETE',
      }),
      onQueryStarted: async (_args, { queryFulfilled }) => {
        try {
          await queryFulfilled;
        } catch (error) {}
      },
      // Invalidate the cached data or refetch
      invalidatesTags: (_result, _error, id) => [
        { type: 'Accounts', id: id },
        { type: 'Accounts', id: 'LIST' },
      ],
    }),
    resendEmail: builder.mutation({
      query: (email) => ({
        url: TENANT_API_ROUTES.RESEND_EMAIL + email,
        method: 'POST',
        body: {},
      }),
    }),
    activateAccount: builder.mutation({
      query: (payload) => ({
        url: TENANT_API_ROUTES.ACTIVE_ACCOUNT + payload,
        method: 'PUT',
        body: {},
      }),
      transformResponse: (response: {
        success: boolean;
        statusCode: number;
        message: string;
        data: TenantType;
      }) => {
        if (response.statusCode === 200) {
          UseToast().success(response.message);
          return response.data; // Return the data directly
        }
        return <TenantType>{}; // Fallback to an empty array in case of failure
      },
      // Invalidate the cache for the tenant by their ID
      invalidatesTags: (_result, _error, payload) => [
        { type: 'Accounts', id: payload.split('/')[0] },
        { type: 'Accounts', id: 'LIST' }, // Optionally invalidate the whole list
      ],
    }),
    logout: builder.mutation({
      query: () => ({
        url: AUTH_API_ROUTES.LOGOUT,
        method: 'POST',
      }),
      onQueryStarted: async (_args, { queryFulfilled }) => {
        try {
          await queryFulfilled;
        } catch (error) {}
      },
    }),
  }),
});

// Export hooks for usage in components
export const {
  useCreateTenatMutation,
  useUpdateTenantMutation,
  useDeleteTenantMutation,
  useGetTenantByIdQuery,
  useGetTenantsMutation,
  useLogoutMutation,
  useActivateAccountMutation,
} = accountAPI;
