import { PAYMENT_TYPE_API_ROUTES } from '@/constants/api-constants';
import { baseQueryWithReauth } from '@/services/api';
import { PaymentType } from '@/types/customer.types';
import { createApi } from '@reduxjs/toolkit/query/react';

export const paymentTypeApi = createApi({
  reducerPath: 'paymentTypeApi',
  baseQuery: baseQueryWithReauth,
  endpoints: (builder) => ({
    getPaymentType: builder.query<PaymentType[], void>({
      query: () => ({
        url: PAYMENT_TYPE_API_ROUTES.ALL,
        method: 'GET',
      }),
    }),
  }),
});

export const { useGetPaymentTypeQuery } = paymentTypeApi;
