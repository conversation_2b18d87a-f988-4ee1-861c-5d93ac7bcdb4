import { createApi } from '@reduxjs/toolkit/query/react';
import { baseQueryWithReauth } from '@/services/api';
import { UseToast } from '@/components/ui/toast/ToastContainer';
import { PURCHASE_API_ROUTES } from '@/constants/api-constants';
import { ApiResponseDto } from '@/types/common.types';
import {
  POInformationTypes,
  POItemDetailsTypes,
} from '@/types/purchase-order.types';

export const purchaseOrderApi = createApi({
  reducerPath: 'purchaseOrderApi',
  baseQuery: baseQueryWithReauth,
  tagTypes: ['PurchaseOrder', 'POItemDetails'],
  endpoints: (builder) => ({
    // Get PO by ID
    getPurchaseOrderById: builder.query<
      ApiResponseDto<POInformationTypes>,
      string
    >({
      query: (id) => PURCHASE_API_ROUTES.GET(id),
      providesTags: ['PurchaseOrder'],
    }),

    // Update an existing Item
    savePurchaseOrder: builder.mutation<any, { body: any }>({
      query: ({ body }) => ({
        url: PURCHASE_API_ROUTES.SAVE,
        method: 'POST',
        body,
      }),
      transformResponse: (response) => {
        if (response?.message) {
          UseToast().success(response.message);
        }
        return response;
      },
      invalidatesTags: ['PurchaseOrder'],
    }),

    // Delete a item
    deletePurchaseOrder: builder.mutation<void, { data: any }>({
      query: ({ data }) => ({
        url: PURCHASE_API_ROUTES.DELETE,
        method: 'DELETE',
        body: data,
      }),
      transformResponse: (response) => {
        if (response?.message) {
          UseToast().success(response.message);
        }
        return response;
      },
      invalidatesTags: ['PurchaseOrder'],
    }),

    getPOItemList: builder.query<ApiResponseDto<POItemDetailsTypes[]>, string>({
      query: (id) => PURCHASE_API_ROUTES.GET_ITEM_DETAILS(id),
      providesTags: ['POItemDetails'],
    }),

    savePOItemDetails: builder.mutation<
      ApiResponseDto<POItemDetailsTypes>,
      { id: string; body: any }
    >({
      query: ({ id, body }) => ({
        url: PURCHASE_API_ROUTES.SAVE_ITEM_DETAILS(id),
        method: 'POST',
        body,
      }),
      transformResponse: (response) => {
        if (response?.message) {
          UseToast().success(response.message);
        }
        return response;
      },
      invalidatesTags: ['POItemDetails', 'PurchaseOrder'],
    }),

    saveRecievedItemDetails: builder.mutation<
      ApiResponseDto<POItemDetailsTypes>,
      { id: string; body: any }
    >({
      query: ({ id, body }) => ({
        url: PURCHASE_API_ROUTES.SAVE_RECIEVED_ITEM_DETAILS(id),
        method: 'POST',
        body,
      }),
      transformResponse: (response) => {
        if (response?.message) {
          UseToast().success(response.message);
        }
        return response;
      },
      invalidatesTags: ['POItemDetails', 'PurchaseOrder'],
    }),

    receiveAll: builder.mutation<ApiResponseDto<null>, string>({
      query: (id) => ({
        url: PURCHASE_API_ROUTES.RECIEVED_ALL(id),
        method: 'POST',
      }),
      transformResponse: (response) => {
        if (response?.message) {
          UseToast().success(response.message);
        }
        return response;
      },
      invalidatesTags: ['POItemDetails', 'PurchaseOrder'],
    }),

    deletePOItems: builder.mutation<void, { poId: string; poItemId: number }>({
      query: ({ poId, poItemId }) => ({
        url: PURCHASE_API_ROUTES.DELETE_ITEM({ poId, poItemId }),
        method: 'DELETE',
      }),
      transformResponse: (response) => {
        if (response?.message) {
          UseToast().success(response.message);
        }
        return response;
      },
      invalidatesTags: ['POItemDetails', 'PurchaseOrder'],
    }),

    copyPO: builder.mutation<
      ApiResponseDto<POItemDetailsTypes>,
      { id: string; copyItems: boolean }
    >({
      query: ({ id, copyItems }) => ({
        url: PURCHASE_API_ROUTES.COPY,
        params: { id, copyItems },
        method: 'GET',
      }),
      transformResponse: (response) => {
        if (response?.message) {
          UseToast().success(response.message);
        }
        return response;
      },
    }),
  }),
});

// Export hooks for usage in components
export const {
  useDeletePurchaseOrderMutation,
  useGetPurchaseOrderByIdQuery,
  useSavePurchaseOrderMutation,
  useGetPOItemListQuery,
  useSavePOItemDetailsMutation,
  useSaveRecievedItemDetailsMutation,
  useDeletePOItemsMutation,
  useCopyPOMutation,
  useReceiveAllMutation,
} = purchaseOrderApi;
