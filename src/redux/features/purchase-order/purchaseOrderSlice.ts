import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// Define the interface for filter state
interface ItemState {
  filters: Array<{
    label: string;
    value: string;
    name: string;
    operator: string;
  }>;
  formValues: {
    range: string;
    type: string;
    itemDesc: string;
    operator: string;
    searchBy: string;
    value: string;
  };
}

// Initial state with default values matching the Filter component
const initialState: ItemState = {
  filters: [],
  formValues: {
    range: '',
    type: '',
    operator: '',
    searchBy: '',
    value: '',
    itemDesc: '',
  },
};

const itemSlice = createSlice({
  name: 'item',
  initialState,
  reducers: {
    // Set filters based on form data
    setPurchaseOrderFilter(
      state,
      action: PayloadAction<
        Array<{ label: string; value: string; name: string; operator: string }>
      >
    ) {
      state.filters = action.payload;
    },

    // Update form values
    updatePurchaseOrderFormValues(
      state,
      action: PayloadAction<Partial<ItemState['formValues']>>
    ) {
      state.formValues = {
        ...state.formValues,
        ...action.payload,
      };
    },

    // Clear all filters and reset form values
    clearAllPurchaseOrderFilters(state) {
      state.filters = [];
      state.formValues = initialState.formValues;
    },

    // Clear a specific filter
    clearPurchaseOrderFilter(state, action: PayloadAction<string>) {
      state.filters = state.filters.filter(
        (filter) => filter.name !== action.payload
      );
      switch (action.payload) {
        case 'range':
          state.formValues.range = '';
          break;
        case 'type':
          state.formValues.type = '';
          break;
        case 'itemDesc':
          state.formValues.itemDesc = '';
          break;
        case 'operator':
          state.formValues.operator = '';
          break;
        case 'searchBy':
          state.formValues.searchBy = '';
          break;
        case 'value':
          state.formValues.value = '';
          break;
        default:
          state.formValues.searchBy = '';
          state.formValues.operator = '';
          state.formValues.value = '';
      }
    },
  },
});

export const {
  setPurchaseOrderFilter,
  updatePurchaseOrderFormValues,
  clearAllPurchaseOrderFilters,
  clearPurchaseOrderFilter,
} = itemSlice.actions;

export default itemSlice.reducer;
