import { baseQuery<PERSON>ith<PERSON>eauth } from '@/services/api';
import { createApi } from '@reduxjs/toolkit/query/react';
import { UseToast } from '@/components/ui/toast/ToastContainer';

export const commonApi = createApi({
  reducerPath: 'commonApi',
  baseQuery: baseQueryWithReauth,
  tagTypes: ['UserDefaultStore'],
  endpoints: (builder) => ({
    // Common API call for GET And POST default is post
    getAll: builder.mutation<
      any,
      { url: string; type?: 'GET' | 'POST'; body: any }
    >({
      query: ({ url, type = 'POST', body }) => ({
        url: url,
        method: type,
        body,
      }),
    }),
    getAllQuery: builder.query<
      any,
      { url: string; type?: 'GET' | 'POST'; body?: any }
    >({
      query: ({ url, type = 'POST', body }) => {
        // If the request type is GET, we don't need to send the body
        const config: any = {
          url,
          method: type,
          headers: {
            'Content-Type': 'application/json', // Set the content type to application/json for POST requests
          },
        };
        if (type === 'POST' && body) {
          config.body = JSON.stringify(body); // Ensure the body is serialized for POST
        }

        return config; // Returning the configuration for the request
      },
      providesTags: ['UserDefaultStore'],
    }),

    getListItems: builder.mutation<any, { url: string; data: any }>({
      query: ({ url, data }) => ({
        url: url,
        method: 'POST',
        body: data,
      }),
      // invalidatesTags: ['UserDefaultStore'], // Invalidates the cached query
    }),

    // Update an item (with dynamic URL), Generic API Response Type
    updateItem: builder.mutation<
      void,
      { url: string; data: any; showToaster?: boolean }
    >({
      query: ({ url, data }) => ({
        url: url,
        method: 'PUT',
        body: data,
      }),
      transformResponse: (response: {
        success: boolean;
        statusCode: number;
        message: string;
        data: any;
      }) => {
        if (response.statusCode === 200) {
          UseToast().success(response.message);
          return response.data; // Return the data directly
        }
        return <any>{}; // Fallback to an empty array in case of failure
      },
      invalidatesTags: ['UserDefaultStore'],
    }),
    // Update an item (with dynamic URL), Generic API Request Type
    addNewItem: builder.mutation<
      void,
      { url: string; data: any; showToaster?: boolean }
    >({
      query: ({ url, data }) => ({
        url: url,
        method: 'POST',
        body: data,
      }),
      transformResponse: (
        response: {
          success: boolean;
          statusCode: number;
          message: string;
          data: any;
        },
        _meta,
        { showToaster = true }
      ) => {
        if (response.statusCode === 200 && showToaster) {
          UseToast().success(response.message);
          return response.data; // Return the data directly
        }
        return <any>{}; // Fallback to an empty array in case of failure
      },
    }),
    // Delete an item (with dynamic URL)
    deleteItem: builder.mutation<
      void,
      { url: string; itemId: number | number; showToaster?: boolean }
    >({
      query: ({ url, itemId }) => ({
        url: `${url}/${itemId}`,
        method: 'DELETE',
      }),
      transformResponse: (response: {
        success: boolean;
        statusCode: number;
        message: string;
        data: any;
      }) => {
        if (response.statusCode === 200) {
          UseToast().success(response.message);
          return response.data; // Return the data directly
        }
        return <any>{}; // Fallback to an empty array in case of failure
      },
    }),
    getItems: builder.mutation<any, { url: string }>({
      query: ({ url }) => ({
        url: url,
        method: 'GET',
      }),
    }),
  }),
  refetchOnMountOrArgChange: true,
});
export const {
  useGetAllMutation,
  useGetAllQueryQuery,
  useGetListItemsMutation,
  useAddNewItemMutation,
  useUpdateItemMutation,
  useDeleteItemMutation,
  useGetItemsMutation,
} = commonApi;
