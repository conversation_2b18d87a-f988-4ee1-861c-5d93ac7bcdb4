import { STORAGE_KEYS } from '@/constants/storageKeys';
import { User } from '@/types/auth.types';
import { RolesEnum } from '@/types/common.types';
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface AuthState {
  tokenType?: string | null;
  token: string | null;
  refreshToken?: string | null;
  usedToken?: string | null;
  expiresIn?: number;
  isAuthenticated?: boolean;
  role: string | RolesEnum | null;
  rememberMe?: string | null;
  userIsActive?: string | null;
  profileImage?: string | null;
  userName?: string | null;
  permissions?: any;
  permissionsLoading: boolean;
}

const getStorageValue = (key: string): string | null =>
  localStorage.getItem(key) || sessionStorage.getItem(key);

export const setStorageValue = (
  key: string,
  value: string,
  rememberMe: boolean
) => {
  if (rememberMe) {
    localStorage.setItem(key, value);
  } else {
    sessionStorage.setItem(key, value);
  }
};

const removeStorageValue = (key: string) => {
  localStorage.removeItem(key);
  sessionStorage.removeItem(key);
};

const getInitialState = (): AuthState => ({
  tokenType: getStorageValue(STORAGE_KEYS.TOKEN_TYPE),
  token: getStorageValue(STORAGE_KEYS.ACCESS_TOKEN),
  refreshToken: getStorageValue(STORAGE_KEYS.REFRESH_TOKEN),
  usedToken: getStorageValue(STORAGE_KEYS.USED_TOKEN),
  expiresIn: Number(getStorageValue(STORAGE_KEYS.EXPIRES_IN)) || 0,
  isAuthenticated: !!getStorageValue(STORAGE_KEYS.IS_AUTHENTICATED),
  role: getStorageValue(STORAGE_KEYS.ROLE),
  rememberMe: getStorageValue(STORAGE_KEYS.REMEMBER_ME),
  profileImage: getStorageValue(STORAGE_KEYS.PROFILE_IMAGE),
  userName: getStorageValue(STORAGE_KEYS.USER_NAME),
  permissions: JSON.parse(getStorageValue(STORAGE_KEYS.PERMISSIONS) as string),
  permissionsLoading: false,
});
const initialState: AuthState = getInitialState();

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    authTokenChange: (
      state,
      action: PayloadAction<{
        tokenType?: string;
        token: string;
        refreshToken?: string;
        accessToken?: string;
        expiresIn?: number;
        role: string;
        rememberMe?: string;
        userId: string;
        profileImage?: string;
        userName?: string;
        user?: User;
      }>
    ) => {
      const {
        tokenType,
        token,
        refreshToken,
        accessToken,
        expiresIn,
        role,
        rememberMe,
        userId,
        profileImage,
        userName,
        user,
      } = action.payload;
      const remember = rememberMe === 'true';

      setStorageValue('accessToken', token, remember);
      setStorageValue('role', role, remember);
      setStorageValue('userId', userId, remember);
      setStorageValue('firstName', user?.firstName || '', remember);
      setStorageValue('lastName', user?.lastName || '', remember);
      setStorageValue(
        'defaultLocationId',
        user?.defaultLocationId?.toString() || '',
        remember
      );
      setStorageValue(
        'idleLogoutMinutes',
        user?.idleLogoutMinutes?.toString() || '120',
        remember
      );
      setStorageValue('profileImage', user?.profileImage || '', remember);
      setStorageValue(
        'timeZoneOffset',
        user?.timeZoneOffset || '+00:00',
        remember
      );
      setStorageValue(
        'idleOrderMinutes',
        user?.idleOrderMinutes?.toString() || '10',
        remember
      );

      state.tokenType = tokenType;
      state.token = token;
      state.refreshToken = refreshToken;
      state.usedToken = accessToken;
      state.expiresIn = expiresIn;
      state.role = role;
      state.rememberMe = rememberMe;
      state.isAuthenticated = true;
      state.profileImage = profileImage;
      state.userName = userName;
      // state.permissionsLoading = true;
    },
    logoutUser: (state) => {
      [
        'accessToken',
        'role',
        'userId',
        'firstName',
        'lastName',
        'defaultLocationId',
        'idleLogoutMinutes',
        'profileImage',
        'permissions',
        'timeZoneOffset',
        'idleOrderMinutes',
      ].forEach(removeStorageValue);

      state.tokenType = null;
      state.token = null;
      state.refreshToken = null;
      state.usedToken = null;
      state.expiresIn = 0;
      state.isAuthenticated = false;
      state.role = null;
      state.rememberMe = null;
      state.userIsActive = null;
      state.profileImage = '';
      state.userName = '';
      // state.permissions = [];
      // state.permissionsLoading = true;
    },
    adjustUsedToken: (state, action: PayloadAction<string>) => {
      state.usedToken = action.payload;
    },

    setPermissions: (state, action: PayloadAction<string>) => {
      state.permissions = action.payload;
      setStorageValue(
        STORAGE_KEYS.PERMISSIONS,
        JSON.stringify(action.payload),
        true
      );
    },
    setPermissionsLoading: (state, action: PayloadAction<boolean>) => {
      state.permissionsLoading = action.payload;
    },
  },
});

export const {
  authTokenChange,
  logoutUser,
  adjustUsedToken,
  setPermissions,
  setPermissionsLoading,
} = authSlice.actions;
export default authSlice.reducer;
