import { UseToast } from '@/components/ui/toast/ToastContainer';
import { AUTH_API_ROUTES } from '@/constants/api-constants';
import { authQuery } from '@/services/api';
import {
  CommonResponseType,
  ForgotPasswordRequest,
  ForgotPasswordResponse,
  LoginRequest,
  LoginResponse,
} from '@/types/auth.types';
import { ErrorResponse } from '@/types/common.types';
import { createApi } from '@reduxjs/toolkit/query/react';
import { authTokenChange } from './authSlice';
import { setProfile } from '../user-options/loginProfileSlice';

export const authApi = createApi({
  reducerPath: 'authApi',
  baseQuery: authQuery,
  endpoints: (builder) => ({
    login: builder.mutation<CommonResponseType<LoginResponse>, LoginRequest>({
      query: (credentials) => ({
        url: AUTH_API_ROUTES.LOGIN,
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          rememberMe: credentials.rememberMe.toString(),
        },
        body: credentials,
      }),
      async onQueryStarted(_args, { dispatch, queryFulfilled }) {
        try {
          const { data, meta } = await queryFulfilled;

          let rememberme = '';
          if (meta?.request) {
            const responseHeaders = Object.fromEntries(meta.request.headers);
            rememberme = responseHeaders.rememberme;
          }

          if (data.statusCode === 200) {
            const { token, role, user } = data.data;
            // Dispatch the action to store tokens
            dispatch(
              authTokenChange({
                token,
                role,
                rememberMe: rememberme,
                userId: user.id?.toString(),
                user,
              })
            );
            if (user) {
              dispatch(
                setProfile({ ...user, profileImage: user.profileImage })
              );
            }
          } else {
            UseToast().error(data.message);
          }
        } catch (error) {
          // Type assertion for error handling
          const err = error as { error: ErrorResponse };
          // Show error toast with the appropriate message
          UseToast().error(err.error?.data?.message as string);
        }
      },
    }),
    setpassword: builder.mutation<any, any>({
      query: (credentials) => ({
        url: AUTH_API_ROUTES.SETPASSWORD,
        method: 'POST',
        body: credentials,
      }),
      async onQueryStarted(_args, { queryFulfilled }) {
        try {
          const { data } = await queryFulfilled;
          if (data) {
          }
        } catch (error) {
          // Type assertion for error handling
          const err = error as { error: ErrorResponse };

          // Show error toast with the appropriate message
          UseToast().error(err.error?.data?.message as string);
        }
      },
    }),
    resetpassword: builder.mutation<
      ForgotPasswordResponse,
      ForgotPasswordRequest
    >({
      query: (credentials) => ({
        url: AUTH_API_ROUTES.FORGOTPASSWORD,
        method: 'POST',
        body: credentials,
      }),
      async onQueryStarted(_args, { queryFulfilled }) {
        try {
          const { data } = await queryFulfilled;
          if (data) {
          }
        } catch (error) {
          // Type assertion for error handling
          const err = error as { error: ErrorResponse };

          // Show error toast with the appropriate message
          UseToast().error(err.error?.data?.message as string);
        }
      },
    }),

    valdiateToken: builder.mutation<
      void,
      { url: string; data: any; showToaster?: boolean }
    >({
      query: ({ url, data }) => ({
        url: url,
        method: 'POST',
        body: data,
      }),
      transformResponse: (response: {
        success: boolean;
        statusCode: number;
        message: string;
        data: { emailId: string; token: string };
      }) => {
        if (response.statusCode === 200) {
          return response; // Return the data directly
        } else {
          UseToast().error(response.message);
        }
        return <any>{}; // Fallback to an empty array in case of failure
      },
    }),
  }),
});

export const {
  useLoginMutation,
  useSetpasswordMutation,
  useResetpasswordMutation,
  useValdiateTokenMutation,
} = authApi;
