import { CUSTOMER_TYPE_API_ROUTES } from '@/constants/api-constants';
import { baseQueryWithReauth } from '@/services/api';
import { CustomerTypeDto } from '@/types/customer.types';
import { createApi } from '@reduxjs/toolkit/query/react';

export const customerTypeApi = createApi({
  reducerPath: 'customerTypeApi',
  baseQuery: baseQueryWithReauth,
  tagTypes: ['CustomerType'],
  endpoints: (builder) => ({
    getCustomerTypeList: builder.query<CustomerTypeDto, void>({
      query: () => ({
        url: CUSTOMER_TYPE_API_ROUTES.ALL,
        method: 'GET',
      }),
      providesTags: (result) =>
        result ? [{ type: 'CustomerType', id: 'LIST' }] : [],
    }),
  }),
});

export const { useGetCustomerTypeListQuery } = customerTypeApi;
