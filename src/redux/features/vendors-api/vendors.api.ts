import { UseToast } from '@/components/ui/toast/ToastContainer';
import { VENDORS_API_ROUTES } from '@/constants/api-constants';
import { baseQueryWithReauth } from '@/services/api';
import { ApiResponseDto } from '@/types/common.types';
import { createApi } from '@reduxjs/toolkit/query/react';

// Define the API service using RTK Query
export const vendorsApi = createApi({
  reducerPath: 'vendorsApi',
  baseQuery: baseQueryWithReauth,
  tagTypes: ['vendors', 'vendorColumns'],
  endpoints: (builder) => ({
    getVendorColums: builder.query<ApiResponseDto<any>, void>({
      query: () => VENDORS_API_ROUTES.GET_COLUMN,
      providesTags: ['vendorColumns'],
    }),

    updateVendorColums: builder.mutation<ApiResponseDto<any>, any>({
      query: (body) => ({
        url: VENDORS_API_ROUTES.UPDATE_COLUMN,
        method: 'POST',
        body,
      }),
      transformResponse: (response) => {
        if (response?.message) {
          UseToast().success(response.message);
        }
        // Return the response as it is after processing it
        return response;
      },

      invalidatesTags: ['vendorColumns'],
    }),

    // GET request to fetch the company data
    getVendors: builder.query<ApiResponseDto<any>, string>({
      query: (id) => VENDORS_API_ROUTES.GET(id),
      providesTags: ['vendors'],
    }),

    addUpdateVendorInfo: builder.mutation<
      ApiResponseDto<any>,
      { url: string; method?: 'POST' | 'PUT'; data: any }
    >({
      query: ({ url, data, method = 'POST' }) => ({
        url,
        method,
        body: data,
      }),
      transformResponse: (response) => {
        if (response?.message) {
          UseToast().success(response.message);
        }
        // Return the response as it is after processing it
        return response;
      },

      invalidatesTags: ['vendors'],
    }),

    deleteVendors: builder.mutation({
      query: (id: number) => ({
        url: VENDORS_API_ROUTES.DELETE(id),
        method: 'DELETE',
      }),
      transformResponse: (response) => {
        if (response?.message) {
          UseToast().success(response.message);
        }
        return response;
      },
      invalidatesTags: ['vendors'],
    }),

    // add update additional contact
    addUpdateAdditionalContact: builder.mutation<any, any>({
      query: (body) => ({
        url: VENDORS_API_ROUTES.ADD_UPDATE_ADDITIONAL_CONTACT(body?.contact_id),
        method: 'POST',
        body,
      }),
      transformResponse: (response) => {
        if (response?.message) {
          UseToast().success(response.message);
        }
        return response;
      },
      invalidatesTags: ['vendors'],
    }),
    // Get Additional Contact
    getAdditionalContact: builder.query<any, string | number>({
      query: (id) => ({
        url: VENDORS_API_ROUTES.GET_ADDITIONAL_CONTACT(id),
        method: 'GET',
      }),
      providesTags: ['vendors'],
    }),

    // Delete Item in addtional contact row
    deleteAdditionalContact: builder.mutation<any, string | number>({
      query: (id) => ({
        url: VENDORS_API_ROUTES.DELETE_ADDITIONAL_CONTACT(id),
        method: 'DELETE',
      }),
      transformResponse: (response) => {
        if (response?.message) {
          UseToast().success(response.message);
        }
        return response;
      },
    }),
    // delete additonal contact specific phone number
    deleteAdditionalContactPhoneNumber: builder.mutation<
      any,
      { contactId: number; phoneId: number }
    >({
      query: ({ contactId, phoneId }) => ({
        url: VENDORS_API_ROUTES.DELETE_ADDITIONAL_CONTACT_PHONE_NUMBER(
          contactId,
          phoneId
        ),
        method: 'DELETE',
      }),
      transformResponse: (response) => {
        if (response?.message) {
          UseToast().success(response.message);
        }
        return response;
      },
    }),
  }),
});

export const {
  useDeleteVendorsMutation,
  useGetVendorsQuery,
  useLazyGetVendorsQuery,
  useAddUpdateVendorInfoMutation,
  useAddUpdateAdditionalContactMutation,
  useGetAdditionalContactQuery,
  useDeleteAdditionalContactMutation,
  useGetVendorColumsQuery,
  useUpdateVendorColumsMutation,
  useDeleteAdditionalContactPhoneNumberMutation,
} = vendorsApi;
