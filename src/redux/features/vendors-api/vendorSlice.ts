import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// Define the interface for filter state
interface VendorState {
  filters: Array<{
    label: string;
    value: string;
    name: string;
    operator: string;
  }>;
  formValues: {
    vendor: string;
    address: string;
    city: string;
    state: string;
    phone: string;
    zipCode: string | null;
    operator: string;
    searchBy: string;
    status: string;
  };
}

// Initial state with default values matching the Filter component
const initialState: VendorState = {
  filters: [],
  formValues: {
    vendor: '',
    address: '',
    city: '',
    state: '',
    phone: '',
    zipCode: '',
    operator: 'Contains',
    searchBy: '',
    status: '',
  },
};

const vendorSlice = createSlice({
  name: 'vendor',
  initialState,
  reducers: {
    // Set filters based on form data
    setFilter(
      state,
      action: PayloadAction<
        Array<{ label: string; value: string; name: string; operator: string }>
      >
    ) {
      state.filters = action.payload;
    },

    // Update form values
    updateFormValues(
      state,
      action: PayloadAction<Partial<VendorState['formValues']>>
    ) {
      state.formValues = {
        ...state.formValues,
        ...action.payload,
      };
    },

    // Clear all filters and reset form values
    clearAllFilters(state) {
      state.filters = [];
      state.formValues = initialState.formValues;
    },

    // Clear a specific filter
    clearFilter(state, action: PayloadAction<string>) {
      state.filters = state.filters.filter(
        (filter) => filter.name !== action.payload
      );
      switch (action.payload) {
        case 'vendorName':
          state.formValues.searchBy = '';
          break;
        case 'address1':
          state.formValues.searchBy = '';
          break;
        case 'city':
          state.formValues.searchBy = '';
          break;
        case 'state':
          state.formValues.searchBy = '';
          break;
        case 'tel1':
          state.formValues.searchBy = '';
          break;
        case 'zipCode':
          state.formValues.searchBy = '';
          break;
        case 'emailaddress':
          state.formValues.searchBy = '';
          break;
        case 'isactive':
          state.formValues.status = '';
          break;
        // Add more cases as needed
      }
    },
  },
});

export const { setFilter, clearFilter, clearAllFilters, updateFormValues } =
  vendorSlice.actions;

export default vendorSlice.reducer;
