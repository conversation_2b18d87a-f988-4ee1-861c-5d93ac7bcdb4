import {
  CUSTOMERS_API,
  DELIVERY_CHARGES_API_ROUTES,
} from '@/constants/api-constants';
import { baseQueryWithReauth } from '@/services/api';
import { StateType } from '@/types/customer.types';

import { createApi } from '@reduxjs/toolkit/query/react';

export const stateList = createApi({
  reducerPath: 'stateList',
  baseQuery: baseQueryWithReauth,
  tagTypes: ['StateType'],
  endpoints: (builder) => ({
    getStateList: builder.query<StateType[], void>({
      query: () => ({
        url: CUSTOMERS_API.LOCATION.STATE_LIST_ALL,
        method: 'GET',
      }),
      onQueryStarted: async (_args, { queryFulfilled }) => {
        try {
          await queryFulfilled;
        } catch (error) {}
      },
      transformResponse: (response: {
        success: boolean;
        statusCode: number;
        message: string;
        data: StateType[];
      }) => {
        if (response.statusCode === 200) {
          return response.data; // Return the data directly
        }
        return []; // Fallback to an empty array in case of failure
      },
      providesTags: (result) =>
        result ? [{ type: 'StateType', id: 'LIST' }] : [],
    }),
    getZipCodes: builder.mutation<any, any>({
      query: (body) => ({
        url: DELIVERY_CHARGES_API_ROUTES.DELIVERY_LOCATION_ZIP_CODES,
        method: 'POST',
        body: body,
      }),
    }),
  }),
});

export const { useGetStateListQuery, useGetZipCodesMutation } = stateList;
