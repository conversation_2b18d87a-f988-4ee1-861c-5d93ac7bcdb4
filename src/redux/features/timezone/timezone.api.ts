import { TIMEZONE_API } from '@/constants/api-constants';
import { baseQueryWithReauth } from '@/services/api';
import { TimeZoneType } from '@/types/customer.types';

import { createApi } from '@reduxjs/toolkit/query/react';

export const timezoneList = createApi({
  reducerPath: 'timezoneList',
  baseQuery: baseQueryWithReauth,
  tagTypes: ['TimeZoneType'],
  endpoints: (builder) => ({
    getTimeZoneList: builder.query<TimeZoneType[], void>({
      query: () => ({
        url: TIMEZONE_API.ALL,
        method: 'GET',
      }),
      transformResponse: (response: {
        success: boolean;
        statusCode: number;
        message: string;
        data: TimeZoneType[];
      }) => {
        if (response.statusCode === 200) {
          return response.data; // Return the data directly
        }
        return []; // Fallback to an empty array in case of failure
      },
      providesTags: (result) =>
        result ? [{ type: 'TimeZoneType', id: 'LIST' }] : [],
    }),
  }),
});

export const { useGetTimeZoneListQuery } = timezoneList;
