import { COMPANY_API_ROUTES } from '@/constants/api-constants';
import { baseQueryWithReauth } from '@/services/api';
import { ApiResponseDto } from '@/types/common.types';
import { CompanyFormData, CompanyData } from '@/types/company.types';
import { createApi } from '@reduxjs/toolkit/query/react';

// Define the API service using RTK Query
export const companyApi = createApi({
  reducerPath: 'companyApi',
  baseQuery: baseQueryWithReauth,
  tagTypes: ['Company'],
  endpoints: (builder) => ({
    // GET request to fetch the company data
    getCompany: builder.query<ApiResponseDto<CompanyData>, void>({
      query: () => COMPANY_API_ROUTES.GET,
      providesTags: ['Company'],
    }),

    postCompany: builder.mutation<
      ApiResponseDto<CompanyFormData>,
      { companyData: CompanyFormData; url: string }
    >({
      query: ({ companyData, url }) => ({
        url: url,
        method: 'POST',
        body: companyData,
      }),
      invalidatesTags: ['Company'],
    }),
  }),
});

export const { useGetCompanyQuery, usePostCompanyMutation } = companyApi;
