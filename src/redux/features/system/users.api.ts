import { UseToast } from '@/components/ui/toast/ToastContainer';
import { SYSTEM_USERS_API_ROUTES } from '@/constants/api-constants';
import { baseQueryWithReauth } from '@/services/api';
import { ApiResponseDto } from '@/types/common.types';
import { createApi } from '@reduxjs/toolkit/query/react';

// Define the API service using RTK Query
export const systemUserApi = createApi({
  reducerPath: 'systemUserApi',
  baseQuery: baseQueryWithReauth,
  tagTypes: ['system-users', 'users-permissions'],
  endpoints: (builder) => ({
    // GET request to fetch the company data
    getSystemUsers: builder.query<ApiResponseDto<any>, string>({
      query: (id) => SYSTEM_USERS_API_ROUTES.GET(id),
      providesTags: ['system-users'],
    }),

    getPermissions: builder.query<ApiResponseDto<any>, string>({
      query: (id) => SYSTEM_USERS_API_ROUTES.GET_PERMISSIONS(id),
      providesTags: ['users-permissions'],
    }),

    updateUserStatus: builder.mutation<ApiResponseDto<any>, { url: string }>({
      query: ({ url }) => ({
        url: url,
        method: 'GET',
      }),
      transformResponse: (response) => {
        if (response?.message) {
          UseToast().success(response.message);
        }
        // Return the response as it is after processing it
        return response;
      },
      invalidatesTags: ['system-users'],
    }),

    postSystemUser: builder.mutation<
      ApiResponseDto<any>,
      { data: any; url: string }
    >({
      query: ({ data, url }) => ({
        url: url,
        method: 'POST',
        body: data,
      }),
      transformResponse: (response) => {
        if (response?.message) {
          UseToast().success(response.message);
        }
        // Return the response as it is after processing it
        return response;
      },

      invalidatesTags: ['system-users'],
    }),

    postProfileOptions: builder.mutation<
      ApiResponseDto<any>,
      { data: any; url: string }
    >({
      query: ({ data, url }) => ({
        url: url,
        method: 'POST',
        body: data,
      }),
      transformResponse: (response) => {
        if (response?.message) {
          UseToast().success(response.message);
        }
        // Return the response as it is after processing it
        return response;
      },

      invalidatesTags: ['users-permissions'],
    }),

    deleteSystemUser: builder.mutation<
      ApiResponseDto<any>,
      { data: any; url: string }
    >({
      query: ({ data, url }) => ({
        url: url,
        method: 'POST',
        body: data,
      }),
      transformResponse: (response) => {
        if (response?.message) {
          UseToast().success(response.message);
        }

        return response;
      },
      invalidatesTags: ['system-users'],
    }),
  }),
});

export const {
  useGetSystemUsersQuery,
  usePostSystemUserMutation,
  useDeleteSystemUserMutation,
  useGetPermissionsQuery,
  usePostProfileOptionsMutation,
  useUpdateUserStatusMutation,
} = systemUserApi;
