import { REFERRAL_TYPE_API_ROUTES } from '@/constants/api-constants';
import { searchPayload } from '@/lib/utils';
import { baseQueryWithReauth } from '@/services/api';
import { createApi } from '@reduxjs/toolkit/query/react';

export const referralTypeApi = createApi({
  reducerPath: 'referralTypeApi',
  baseQuery: baseQueryWithReauth,
  endpoints: (builder) => ({
    getReferralType: builder.query<any, void>({
      query: () => ({
        url: REFERRAL_TYPE_API_ROUTES.ALL,
        method: 'POST',
        body: searchPayload({
          pageNumber: -1,
          pageSize: -1,
          sortBy: '',
          sortAscending: true,
          filters: [],
        }),
      }),
    }),
  }),
});

export const { useGetReferralTypeQuery } = referralTypeApi;
