import { UseToast } from '@/components/ui/toast/ToastContainer';
import { baseQueryWithReauth } from '@/services/api';
import { createApi } from '@reduxjs/toolkit/query/react';

export const listApi = createApi({
  reducerPath: 'listApi',
  baseQuery: baseQueryWithReauth,
  endpoints: (builder) => ({
    // Get the list of items from the given URL, Generic API Response Type
    getList: builder.query<any, { url: string }>({
      query: ({ url }) => ({
        url: url,
        method: 'GET',
      }),
    }),

    getListItems: builder.mutation<any, { url: string; data: any }>({
      query: ({ url, data }) => ({
        url: url,
        method: 'POST',
        body: data,
      }),
    }),

    // Update an item (with dynamic URL), Generic API Response Type
    updateItem: builder.mutation<void, { url: string; data?: any }>({
      query: ({ url, data }) => ({
        url: url,
        method: 'PUT',
        body: data,
      }),
      transformResponse: (response: {
        success: boolean;
        statusCode: number;
        message: string;
        data: any;
      }) => {
        if (response.statusCode === 200) {
          // toast.success(response.message);
          UseToast().success(response.message);
          return response.data; // Return the data directly
        }
        return <any>{}; // Fallback to an empty array in case of failure
      },
    }),
    // Update an item (with dynamic URL), Generic API Request Type
    addNewItem: builder.mutation<void, { url: string; data?: any }>({
      query: ({ url, data }) => ({
        url: url,
        method: 'POST',
        body: data,
      }),
      transformResponse: (response: {
        success: boolean;
        statusCode: number;
        message: string;
        data: any;
      }) => {
        if (response.statusCode === 200) {
          UseToast().success(response.message);
          return response.data; // Return the data directly
        }
        return <any>{}; // Fallback to an empty array in case of failure
      },
    }),
    // Delete an item (with dynamic URL)
    deleteItem: builder.mutation<
      void,
      { url: string; itemId?: string | number }
    >({
      query: ({ url, itemId }) => ({
        url: itemId ? `${url}/${itemId}` : url,
        method: 'DELETE',
      }),
      transformResponse: (response: {
        success: boolean;
        statusCode: number;
        message: string;
        data: any;
      }) => {
        if (response.statusCode === 200) {
          // toast.success(response.message);
          UseToast().success(response.message);
          return response.data; // Return the data directly
        }
        return <any>{}; // Fallback to an empty array in case of failure
      },
    }),
  }),
  refetchOnMountOrArgChange: true,
});
export const {
  useGetListQuery,
  useLazyGetListQuery,
  useGetListItemsMutation,
  useAddNewItemMutation,
  useUpdateItemMutation,
  useDeleteItemMutation,
} = listApi;
