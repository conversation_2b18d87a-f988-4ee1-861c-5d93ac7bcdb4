import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// Define the interface for filter state
interface DeliveryLocationState {
  filters: Array<{
    label: string;
    value: string;
    name: string;
    operator: string;
  }>;
  formValues: {
    location: string;
    town: string;
    phone: string;
  };
}

// Initial state with default values matching the Filter component
const initialState: DeliveryLocationState = {
  filters: [],
  formValues: {
    location: '',
    town: '',
    phone: '+1',
  },
};

const deliveryLocationrSlice = createSlice({
  name: 'deliveryLocation',
  initialState,
  reducers: {
    // Set filters based on form data
    setDeliveryLocationFilters(
      state,
      action: PayloadAction<
        Array<{ label: string; value: string; name: string; operator: string }>
      >
    ) {
      state.filters = action.payload;
    },

    // Update form values
    updateDeliveryLocationFormValues(
      state,
      action: PayloadAction<Partial<DeliveryLocationState['formValues']>>
    ) {
      state.formValues = {
        ...state.formValues,
        ...action.payload,
      };
    },

    // Clear all filters and reset form values
    clearAllDeliveryLocationFilters(state) {
      state.filters = [];
      state.formValues = initialState.formValues;
    },

    // Clear a specific filter
    clearDeliveryLocationFilter(state, action: PayloadAction<string>) {
      state.filters = state.filters.filter(
        (filter) => filter.name !== action.payload
      );
      switch (action.payload) {
        case 'location':
          state.formValues.location = '';
          break;
        case 'town':
          state.formValues.town = '';
          break;
        case 'phone':
          state.formValues.phone = '';
          break;
      }
    },
  },
});

export const {
  setDeliveryLocationFilters,
  clearDeliveryLocationFilter,
  clearAllDeliveryLocationFilters,
  updateDeliveryLocationFormValues,
} = deliveryLocationrSlice.actions;

export default deliveryLocationrSlice.reducer;
