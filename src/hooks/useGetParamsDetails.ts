import { CommonListingSetupTypes } from '@/constants/list-constants';

export const useGetParamsDetails = (
  pathParam: string,
  listing: CommonListingSetupTypes[]
) => {
  const defaultSlugData: CommonListingSetupTypes = {
    pageLabel: '',
    navigatingRoute: '',
    saveApi: '',
    findApi: '',
    deleteApi: '',
    columns: [],
    isDeleteButton: true,
    isAddButton: true,
    bindingKey: '',
  };

  try {
    const slugData = listing?.find(
      (list) => list?.navigatingRoute === pathParam
    );
    if (pathParam && slugData) {
      return slugData;
    } else {
      return defaultSlugData;
    }
  } catch (error) {
    return defaultSlugData;
  }
};
