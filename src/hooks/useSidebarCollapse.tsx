import { useCallback, useState } from 'react';

export const useSidebarCollapse = (
  setIsCollapsed?: React.Dispatch<React.SetStateAction<boolean>>
) => {
  const [isPinned, setIsPinned] = useState(true);

  const handleMouseEnter = useCallback(() => {
    if (!isPinned && setIsCollapsed) {
      setIsCollapsed(false);
    }
  }, [isPinned, setIsCollapsed]);

  const handleMouseLeave = useCallback(() => {
    if (!isPinned && setIsCollapsed) {
      setIsCollapsed(true);
    }
  }, [isPinned, setIsCollapsed]);

  const handlePinClick = useCallback(() => {
    setIsPinned((prev) => !prev);
    if (setIsCollapsed) {
      setIsCollapsed(false);
    }
  }, [setIsCollapsed]);

  return {
    isPinned,
    handleMouseEnter,
    handleMouseLeave,
    handlePinClick,
  };
};
