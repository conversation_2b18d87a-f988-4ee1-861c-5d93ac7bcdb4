import { getPaginationObject } from '@/lib/utils';
import { useGetListItemsMutation } from '@/redux/features/common-api/common.api';
import { PaginationFilterType } from '@/types/common.types';
import { useEffect } from 'react';

interface UseOptionListProps {
  url: string;
  labelKey?: string;
  valueKey?: string;
  descKey?: string;
  sortBy?: string;
  isOption?: boolean;
  extraKey?: string;
  skip?: boolean;
  filters?: PaginationFilterType[];
}

interface OptionData {
  [key: string]: any;
}

export interface OptionListType {
  label: string;
  value: string;
  descValue?: string;
  extraKey?: any;
}

const useOptionList = ({
  url,
  labelKey = 'label',
  valueKey = 'value',
  descKey,
  sortBy = '',
  isOption = true,
  extraKey,
  skip = false,
  filters = [],
}: UseOptionListProps) => {
  const [getItems, { data: optionsData, isLoading, isError }] =
    useGetListItemsMutation();

  useEffect(() => {
    if (skip || !url) return;
    const fetchOptions = async () => {
      await getItems({
        url,
        data: getPaginationObject({
          pagination: { pageIndex: -1, pageSize: -1 },
          sorting: [{ id: sortBy, desc: true }],
          filters: filters ? filters : [],
        }),
      });
    };

    fetchOptions();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [url, getItems, sortBy, skip]);

  const options = optionsData?.data?.map((item: OptionData) => {
    const option: OptionListType = {
      label: item[labelKey] as string,
      value: item[valueKey] as string,
    };

    // Conditionally add descValue if descKey is provided
    if (descKey) {
      option.descValue = item[descKey] as string; // Assign directly to `option.descValue`
    }

    if (extraKey) {
      option.extraKey = item[extraKey];
    }

    return option;
  });

  const optionList = isOption ? options : optionsData?.data;
  return { options: optionList, optionLoading: isLoading, isError };
};

export default useOptionList;
