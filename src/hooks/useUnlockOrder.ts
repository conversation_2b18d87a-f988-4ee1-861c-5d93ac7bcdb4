import { getQueryParam } from '@/lib/utils';
import { useLazyUnlockedOrderQuery } from '@/redux/features/orders/order.api';
import { useCallback, useEffect, useRef } from 'react';
import { useLocation } from 'react-router-dom';

/**
 * Hook to trigger `unlockOrder` on unmount,
 * but only if the path matches AND `isDeleted === false` at unmount time.
 */
export const useUnlockOrder = (isDeleted: boolean) => {
  const orderId = getQueryParam('id') as string;
  const pathStart = 'orders/edit-orders';
  const { pathname } = useLocation();
  const isTargetPath = pathname.startsWith(`/${pathStart}`);

  const [unlockedOrder] = useLazyUnlockedOrderQuery();
  // Track latest isDeleted in a ref
  const isDeletedRef = useRef(isDeleted);
  useEffect(() => {
    isDeletedRef.current = isDeleted;
  }, [isDeleted]);

  const unlockOrder = useCallback(() => {
    if (orderId) {
      unlockedOrder(orderId);
    }
  }, [orderId, unlockedOrder]);

  useEffect(() => {
    return () => {
      // On unmount — only call if valid path & not deleted
      if (isTargetPath && !isDeletedRef.current) {
        unlockOrder();
      }
    };
  }, [isTargetPath, unlockOrder]);

  return unlockOrder; // manual use if needed
};
