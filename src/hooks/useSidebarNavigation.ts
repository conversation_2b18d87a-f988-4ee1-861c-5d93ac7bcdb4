import { useLocation } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { ROLES } from '@/constants/common-constants';
import { RootState } from '@/redux/store';
import { MenuItem } from '@/types/sidebar.types';

export const useSidebarNavigation = (to: string, children?: MenuItem[]) => {
  const location = useLocation();
  const { role } = useSelector((state: RootState) => state.auth);

  const path = location.pathname.split('/');
  const currentPath =
    role === ROLES.SUPER_ADMIN ? `/${path[1]}/${path[2]}` : `/${path[1]}`;

  const isActive =
    currentPath === to ||
    children?.some((child) => location.pathname.includes(child.to));

  return {
    isActive,
    currentPath,
    role,
  };
};
