import { useState, useEffect } from 'react';

export const useHasPermission = (permission: string): any => {
  const [hasPermission, setHasPermission] = useState(false);

  useEffect(() => {
    const permissions = localStorage.getItem('permissions');
    if (permissions) {
      const parsedPermissions = JSON.parse(permissions);
      if (Array.isArray(parsedPermissions)) {
        const isPresent = parsedPermissions.find(
          (per) => per.permission === permission
        );
        if (isPresent) setHasPermission(true);
      }
    }
  }, [permission]);

  return { hasPermission };
};
