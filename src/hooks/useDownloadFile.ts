import { useState } from 'react';
import { getStorageValue } from '@/lib/utils';
import { ENV_VARIABLES } from '@/services/config';
import { UseToast } from '@/components/ui/toast/ToastContainer';

// Types and interfaces
interface DocumentData {
  mimeType: string | null;
  fileName: string | null;
  fileBlobUrl: string;
}

interface DownloadError {
  statusCode?: number;
  message: string;
}

interface DownloadOptions {
  url: string;
  body?: unknown;
  method?: 'GET' | 'POST';
  showToast?: boolean;
}

interface DownloadResponse {
  blob: Blob;
  fileName: string | null;
}

/**
 * Custom hook for handling file downloads with authentication
 * @returns Object containing download state and function
 */
export const useDownloadFile = () => {
  const toast = UseToast();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<DownloadError | null>(null);
  const [data, setData] = useState<DocumentData | null>(null);

  /**
   * Extracts filename from Content-Disposition header
   */
  const extractFileName = (
    contentDisposition: string | null
  ): string | null => {
    if (!contentDisposition) return null;

    const fileNameMatch = contentDisposition
      .split(';')
      .find((part) => part.trim().startsWith('filename='));

    return fileNameMatch
      ? fileNameMatch.split('=')[1]?.replace(/"/g, '').trim() || null
      : null;
  };

  /**
   * Processes the API response and extracts relevant data
   */
  const processResponse = async (
    response: Response
  ): Promise<DownloadResponse> => {
    const fileName = extractFileName(
      response.headers.get('Content-Disposition')
    );
    const blob = await response.blob();

    return { blob, fileName };
  };

  /**
   * Initiates file download using browser's download mechanism
   */
  const triggerDownload = (blob: Blob, fileName: string | null): void => {
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName ?? 'download';
    link.click();
    URL.revokeObjectURL(url);
  };
  /**
   * Main download function
   */
  const downloadFile = async ({
    url,
    body,
    method = 'GET',
    showToast = true,
  }: DownloadOptions) => {
    setIsLoading(true);
    setError(null);

    try {
      const token = getStorageValue('accessToken');
      const response = await fetch(`${ENV_VARIABLES.API_BASE}/${url}`, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: method === 'POST' && body ? JSON.stringify(body) : undefined,
        method,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw {
          message: errorData.message,
          statusCode: response.status,
        };
      }

      const { blob, fileName } = await processResponse(response);
      setData({
        fileBlobUrl: URL.createObjectURL(blob),
        fileName,
        mimeType: response.headers.get('content-type'),
      });
      triggerDownload(blob, fileName);

      return response;
    } catch (error: unknown) {
      const err = error as DownloadError;
      setError({
        statusCode: err.statusCode,
        message: err.message,
      });

      if (showToast) {
        toast.error(err.message);
      }
    } finally {
      setIsLoading(false);
    }
  };

  return {
    data,
    isLoading,
    error,
    downloadFile,
  };
};
