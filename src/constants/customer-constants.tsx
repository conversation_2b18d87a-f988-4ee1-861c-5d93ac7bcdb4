import BankNoteIcon from '@/assets/icons/BankNoteIcon';
import FileAttachmentIcon from '@/assets/icons/FileAttachmentIcon';
import ImageUserIcon from '@/assets/icons/ImageUserIcon';
import PathIcon from '@/assets/icons/PathIcon';
import SaleIcon from '@/assets/icons/SaleIcon';
import SettingsIcon from '@/assets/icons/SettingsIcon';
import SignIcon from '@/assets/icons/SignIcon';
import TrailingIcon from '@/assets/icons/TrailingIcon';
import UserEditIcon from '@/assets/icons/UserEditIcon';
import LinkedFiles from '@/components/modules/customers/new-customer/files-notes-tabs/linked-files';
import QuickNotes from '@/components/modules/customers/new-customer/files-notes-tabs/quick-notes';
import AccountDetails from '@/components/modules/customers/new-customer/other-info/account-details';
import AdditionalContacts from '@/components/modules/customers/new-customer/other-info/additional-contacts';
import ContactInfo from '@/components/modules/customers/new-customer/other-info/contact-info';
import Discounts from '@/components/modules/customers/new-customer/other-info/discounts';
import Option from '@/components/modules/customers/new-customer/other-info/option';
import SubTypes from '@/components/modules/customers/new-customer/other-info/sub-types';
import { InvoiceDeliveryMethod } from '@/types/customer.types';

// Files and Notes Tabs Constants
const filesNotesTablist = [
  {
    value: 'quick-notes',
    label: (
      <div className="flex items-center gap-x-2">
        <TrailingIcon />
        <span>Quick Notes</span>
      </div>
    ),
    content: <QuickNotes />,
  },
  {
    value: 'linked-files',
    label: (
      <div className="flex items-center gap-x-2">
        <FileAttachmentIcon />
        <span>Linked Files</span>
      </div>
    ),
    content: <LinkedFiles />,
  },
];

const customerOtherInfoTabsList = [
  {
    value: 'contact-info',
    label: (
      <div className="flex items-center gap-x-3">
        <SignIcon />
        <span>Contact Info</span>
      </div>
    ),
    content: <ContactInfo />,
  },
  {
    value: 'option',
    label: (
      <div className="flex items-center gap-x-3">
        <SettingsIcon />
        <span>Options</span>
      </div>
    ),
    content: <Option />,
  },
  {
    value: 'account-details',
    label: (
      <div className="flex items-center gap-x-3">
        <UserEditIcon />
        <span>Account Details</span>
      </div>
    ),
    content: <AccountDetails />,
  },
  {
    value: 'discounts',
    label: (
      <div className="flex items-center gap-x-3">
        <SaleIcon />
        <span>Discounts</span>
      </div>
    ),
    content: <Discounts />,
  },
  {
    value: 'additional-contacts',
    label: (
      <div className="flex items-center gap-x-3">
        <ImageUserIcon />
        <span>Additional Contacts</span>
      </div>
    ),
    content: <AdditionalContacts />,
  },
  {
    value: 'sub-types',
    label: (
      <div className="flex items-center gap-x-3">
        <PathIcon />
        <span>Sub Types</span>
      </div>
    ),
    content: <SubTypes />,
  },
  {
    value: 'custom-price',
    label: (
      <div className="flex items-center gap-x-3">
        <BankNoteIcon />
        <span>Custom Pricing</span>
      </div>
    ),
    content: <></>,
    // content: <CustomPrice />,
  },
];

// List of available delivery methods for invoices/statements
const invoiceDeliveryMethods = [
  { label: 'Email', value: InvoiceDeliveryMethod.Email },
  { label: 'Fax', value: InvoiceDeliveryMethod.Fax },
  { label: 'Print', value: InvoiceDeliveryMethod.Print },
];

export { customerOtherInfoTabsList, filesNotesTablist, invoiceDeliveryMethods };
