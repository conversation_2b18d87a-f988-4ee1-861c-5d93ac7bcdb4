import { ResourceId } from '@/types/common.types';

/** Processes API Routes **/
export const PROCESSES_API_ROUTES = {
  ALL: 'Process/all',
  POSTING: 'processes/posting',
  GET_COLUMN: 'user/tableMapping?tableName=PROCESS',
  UPDATE_COLUMN: 'user/tableMapping/save/PROCESS',
  GET_POSTING_DETAILS: 'processes/postingDetails',

  // Undelete Order
  UN_DELETE: (orderId: ResourceId) => `order/${orderId}/undelete`,
  DELETE_ALL: 'order/deleted',
};

export const PROCESS_POSTING_API_ROUTES = {
  ALL: 'posting/all',
  POST: 'posting',
  UNPOST: 'posting/revert',
};

export const PROCESSES_SERVICE_CHARGES_API_ROUTES = {
  ALL: 'servicecharges/all',
  INSERT_SERVICE_CHARGES: 'servicecharges/insert',
};
