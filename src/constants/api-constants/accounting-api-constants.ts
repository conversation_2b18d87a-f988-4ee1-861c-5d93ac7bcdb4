import { ResourceId } from '@/types/common.types';

// Adjustments
export const ADJUSTMENTS_API_ROUTES = {
  ALL: 'adjustment/all',
  GET_COLUMN: 'user/tableMapping?tableName=ADJUSTMENT',
  UPDATE_COLUMN: 'user/tableMapping/save/ADJUSTMENT',
  GET: `adjustment/get`,
  SAVE: '/adjustment/save',
  DELETE: 'adjustment/delete',
  GET_CALCULATED_ADJUSTMENT: 'adjustment/calculate/list',
  SAVE_DISTRIBUTION: 'adjustment/save/distribution',
  GET_DISTRIBUTION: 'adjustment/get/distribution',
};

// Adjustments
export const INQUIRY_API_ROUTES = {
  GET_COLUMN: 'user/tableMapping?tableName=INQUIRY',
  UPDATE_COLUMN: 'user/tableMapping/save/INQUIRY',
  ALL: 'inquiry/all',
  PAYMENT_INFO: (
    customerId: ResourceId,
    date: ResourceId
  ) => `inquiry/payments/customer/${customerId}/date/${date}
`,
  INVOICE_INFO: (customerId: ResourceId, orderId: ResourceId) =>
    `/inquiry/invoice/customer/${customerId}/order/${orderId}`,
};

// Payments
export const ACCOUNTING_PAYMENTS_API_ROUTES = {
  GET_CUST_COLUMN: 'user/tableMapping?tableName=CUST_PAYMENTS',
  UPDATE_CUST_COLUMN: 'user/tableMapping/save/CUST_PAYMENTS',
  GET_PAYMENTS_COLUMN: 'user/tableMapping?tableName=PAYMENT_DETAILS',
  UPDATE_PAYMENTS_COLUMN: 'user/tableMapping/save/PAYMENT_DETAILS',
  PAYMENTS_LIST: 'accounting/payments/customer',
  DELETE: 'accounting/payment/delete',
  PAYMENT_DETAILS: 'accounting/payment/details',
  GET_PAYMENT_LINK: 'account/payment/new',
  ADD_PAYMENT: 'account/payment/addPayment',
  CARD_TYPE: 'payment/getPaymentType',
  CAL_CONVFEE_TAX: (type: string) => `order/calculateConvFeeTaxAll/${type}`,
  CREDIT_CARD_DETAILS: (cardNumber: string) =>
    `payment/checkCardDetails/${cardNumber}`,
};
