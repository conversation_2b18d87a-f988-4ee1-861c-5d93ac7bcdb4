import { ResourceId } from '@/types/common.types';

export const AUTH_API_ROUTES = {
  LOGIN: '/login',
  LOGOUT: 'Auth/logout',
  REFRESH: '/refresh',
  SIGNUP: '/register',
  SETPASSWORD: '/resetPassword',
  FORGOTPASSWORD: '/forgotPassword',
};

export const CUSTOMER_API_ROUTES = {
  ALL: '/Customer/All',
  CREATE: '/Customer',
  GET: '/Customer',
  UPDATE: '/Customer',
  DELETE: '/Customer',
  // get customer details
  GET_DETAILS: (id: ResourceId) => `/customer/get/details/${id}`,
  GET_COLUMN: 'user/tableMapping?tableName=CUSTOMER',
  UPDATE_COLUMN: 'user/tableMapping/save/CUSTOMER',
};

export const PAYMENT_TERM_API_ROUTES = {
  ALL: 'PaymentTerm/All',
  GET: (id: ResourceId) => `PaymentTerm/${id}`,
  UPDATE: (id: ResourceId) => `PaymentTerm/${id}`,
  CREATE: 'PaymentTerm',
  DELETE: 'PaymentTerm',
};

// list module
// Category
export const CATEGORY_API_ROUTES = {
  ALL: 'Category/All',
  GET: (id: ResourceId) => `Category/GetCategoryById/${id}`,
  UPDATE: 'Category/SaveCategory',
  CREATE: 'Category/SaveCategory',
  DELETE: 'Category',
  DELETE_RENT: '/Category/DeleteRateAdj',
};
//Customer Type
export const CUSTOMER_TYPE_API_ROUTES = {
  ALL: 'CustomerType/All',
  GET: (id: ResourceId) => `CustomerType/${id}`,
  UPDATE: (id: ResourceId) => `CustomerType/${id}`,
  CREATE: 'CustomerType',
  DELETE: 'CustomerType',
};

//Delivery Type
export const DELIVERY_TYPE_API_ROUTES = {
  ALL: 'DeliveryType/All',
  GET: (id: ResourceId) => `DeliveryType/${id}`,
  UPDATE: (id: ResourceId) => `DeliveryType/${id}`,
  CREATE: 'DeliveryType',
  DELETE: 'DeliveryType',
};

// Delivery charges
export const DELIVERY_CHARGES_API_ROUTES = {
  ALL: 'deliveryrate/all',
  GET: (id: ResourceId) => `deliveryrate/${id}`,
  UPDATE: (id: ResourceId) => `deliveryrate/${id}`,
  CREATE: 'deliveryrate',
  DELETE: 'deliveryrate',
  DELIVERY_LOCATION_ZIP_CODES: 'deliveryrate/zipcodes',
};

// Employee
export const EMPLOYEE_API_ROUTES = {
  ALL: 'employee/all',
  GET: (id: ResourceId) => `employee/${id}`,
  UPDATE: (id: ResourceId) => `employee/${id}`,
  CREATE: 'employee',
  DELETE: 'employee',
};

//Department
export const DEPARTMENT_API_ROUTES = {
  ALL: 'Department/All',
  GET: (id: ResourceId) => `Department/${id}`,
  UPDATE: (id: ResourceId) => `Department/${id}`,
  CREATE: 'Department',
  DELETE: 'Department',
};

//PaymentType
export const PAYMENT_TYPE_API_ROUTES = {
  ALL: 'paymenttype/all',
  GET: (id: ResourceId) => `paymenttype/${id}`,
  UPDATE: (id: ResourceId) => `paymenttype/${id}`,
  CREATE: 'paymenttype',
  DELETE: 'paymenttype',
};
//ReferralType
export const REFERRAL_TYPE_API_ROUTES = {
  ALL: 'ReferralType/All',
  GET: (id: ResourceId) => `ReferralType/${id}`,
  UPDATE: (id: ResourceId) => `ReferralType/${id}`,
  CREATE: 'ReferralType',
  DELETE: 'ReferralType',
};
//SalesTaxCode

export const SALES_TAX_CODE_API_ROUTES = {
  ALL: 'SalesTaxCode/All',
  GET: (id: ResourceId) => `SalesTaxCode/${id}`,
  ADD_UPDATE: 'salestaxcode/salestaxcode/save',
  DELETE: 'SalesTaxCode',
};

export const CUSTOMERS_API = {
  LOCATION: {
    COUNTRY_LIST: '/Country/All',
    STATE_LIST: '/State/ByCountry',
    STATE_LIST_ALL: '/State/All',
  },
};

export const TIMEZONE_API = {
  ALL: '/TimeZone/All',
};

export const SUB_TYPES_API = {
  ALL: (id: ResourceId) => `CustomerSubType/Customer/${id}`,
  UPDATE: (id: ResourceId) => `CustomerSubType/UpdateCustomerSubType/${id}`,
};

export const CONFIG_API = {
  storeLocations: 'Config/StoreLocations',
};

export const CUSTOMER_NOTES_API = {
  ALL: 'CustomerNote/All',
  GET: (id: ResourceId) => `Customer/Notes/${id}`,
  CREATE: 'CustomerNote',
  UPDATE: (id: ResourceId) => `CustomerNote/${id}`,
  DELETE: (id: ResourceId) => `CustomerNote/${id}`,
  FIND: (id: ResourceId) => `CustomerNote/${id}`,
};

export const FILES_API = {
  ALL: (customerid: ResourceId) => `customer/${customerid}/linkedfile/all`,
  UPLOAD: (customerid: ResourceId) =>
    `customer/${customerid}/linkedfile/upload`,
  DELETE: (id: ResourceId) => `customer/linkedfile/delete/${id}`,
  DOWNLOAD: (id: ResourceId) => `customer/linkedfile/download/${id}`,
};

// Discount
export const DISCOUNT_API_ROUTES = {
  ALL: (id: ResourceId) => `CustomerDiscount/GetDiscountByCustomerId/${id}`,
  GET: (id: ResourceId) => `CustomerDiscount/${id}`,
  UPDATE: `CustomerDiscount/update`,
  CREATE: 'CustomerDiscount',
  DELETE: (id: ResourceId) => `CustomerDiscount/${id}`,
  BULK_DISCOUNT: 'CustomerDiscount/UpdateAllCustDisByCategoryOrDepatment',
};

// Discount Order
export const ORDER_DISCOUNT_API_ROUTES = {
  ALL: (orderId: ResourceId) => `order/discount/all?orderId=${orderId}`,
  GET: (id: ResourceId) => `order/discount/${id}`,
  UPDATE: (orderId: ResourceId) => `order/discount/save?orderId=${orderId}`,
  CREATE: 'order/discount',
  DELETE: (id: ResourceId) => `order/discount/${id}`,
  BULK_DISCOUNT: 'order/discount/updateAll',
};

// Delivery Charge
export const ORDER_DELIVERY_CHARGE_API_ROUTES = {
  GET: (orderId: ResourceId) => `order/${orderId}/delivery-charges`,
  UPDATE: `order/delivery-charges`,
  GET_HISTORY: (orderId: ResourceId) =>
    `order/${orderId}/history/delivery-charges`,
};

export const CHOICES_API = {
  SALES_PERSON: 'User/All',
  DELIVERY_TYPE: 'DeliveryType/All',
  FREE_SHIPPING: 'Choice/FreeShipping',
  SALES_TAX_CODE: 'SalesTaxCode/All',
  PAYMENT_TYPE: 'Choice/DefaultPaymentTypes',
  RENT_STATUS: 'Choice/RentStatus',
  PAYMENT_TERM: 'Choice/PaymentTerms',
  REFERRAL_TYPE: 'ReferralType/All',
  INCLUDE_IN_BATCH: 'Choice/IncludeInBatch',
  PHONE_TYPE: 'PhoneType',
  CREDIT_STATUS: 'choice/creditstatus',
  SHIPPING_COMPANY: 'choice/shippingcompanytype',
  COMMUNICATION_TYPE: 'choice/communicationtype',
  LOCATION_SELECTION: 'choice/locationselection',
};

export const CONTACTS_API_ROUTES = {
  ALL: (id: ResourceId) => `Customer/Contacts/${id}`,
  GET: (id: ResourceId) => `AdditionalContact/${id}`,
  UPDATE: (id: ResourceId) => `AdditionalContact/update/${id}`,
  CREATE: 'AdditionalContact/create',
  DELETE: (id: ResourceId) => `AdditionalContact/${id}`,
};

export const Phone_API = {
  DELETE: (id: ResourceId) => `Phone/${id}`,
};

export const INVITE_CLIENT_API = {
  CREATE: 'client/inviteClient',
  CLIENTS_INVITE: 'client/invites',
};

//Delivery Type
export const DELIVERY_LOCATION_API_ROUTES = {
  ALL: 'deliverylocation/all',
  GET: (id: ResourceId) => `deliverylocation/${id}`,
  UPDATE: (id: ResourceId) => `deliverylocation/${id}`,
  CREATE: 'deliverylocation',
  DELETE: 'deliverylocation',
};
// Bank Accounts
export const BANK_ACCOUNTS_API_ROUTES = {
  ALL: 'bankaccount/all',
  GET: (id: ResourceId) => `bankaccount/${id}`,
  UPDATE: (id: ResourceId) => `bankaccount/${id}`,
  CREATE: 'bankaccount',
  DELETE: 'bankaccount',
};

// Checklist Items
export const CHECK_LIST_ITEMS_API_ROUTES = {
  ALL: 'checklistmain/All',
  GET: (id: ResourceId) => `checklistmain/${id}`,
  UPDATE: (id: ResourceId) => `checklistmain/${id}`,
  CREATE: 'checklistmain',
  DELETE: 'checklistmain',
  GET_CHECKLIST_SEQUENCE_NO: 'checklistmain/get/sequenceno',
};

// Drivers
export const DRIVERS_API_ROUTES = {
  ALL: 'driver/all',
  GET: (id: ResourceId) => `driver/${id}`,
  UPDATE: (id: ResourceId) => `driver/${id}`,
  CREATE: 'driver',
  DELETE: 'driver',
};

// E-sign Fields
export const E_SIGN_FILEDS_API_ROUTES = {
  ALL: 'esignfield/all',
  GET: (id: ResourceId) => `esignfield/${id}`,
  UPDATE: (id: ResourceId) => `esignfield/${id}`,
  CREATE: 'esignfield',
  DELETE: 'esignfield',
};

// Equipment Types
export const EQUIPMENT_TYPES_API_ROUTES = {
  ALL: 'equipmenttype/all',
  GET: (id: ResourceId) => `equipmenttype/${id}`,
  UPDATE: (id: ResourceId) => `equipmenttype/${id}`,
  CREATE: 'equipmenttype',
  DELETE: 'equipmenttype',
};

// Event Types
export const EVENT_TYPES_API_ROUTES = {
  ALL: 'eventtype/all',
  GET: (id: ResourceId) => `eventtype/${id}`,
  UPDATE: (id: ResourceId) => `eventtype/${id}`,
  CREATE: 'eventtype',
  DELETE: 'eventtype',
};
export const SETUP_TAKEDOWN_API_ROUTES = {
  ALL: 'setuptakedown/all',
  GET: (id: ResourceId) => `setuptakedown/${id}`,
  UPDATE: (id: ResourceId) => `setuptakedown/${id}`,
  CREATE: 'setuptakedown',
  DELETE: 'setuptakedown',
  DEFAULT_SETUP: 'setuptakedown/defaultsetup',
  DEFAULT_TAKEDOWN: 'setuptakedown/defaulttakedown',
};

//SHIPPING_COMPAINES
export const SHIPPING_COMPAINES_API_ROUTES = {
  ALL: 'shipcompany/All',
  GET: (id: ResourceId) => `shipcompany/${id}`,
  UPDATE: (id: ResourceId) => `shipcompany/${id}`,
  CREATE: 'shipcompany',
  DELETE: 'shipcompany',
};

//SURGE_RATES
export const SURGE_RATES_API_ROUTES = {
  ALL: 'surgerate/all',
  GET: (id: ResourceId) => `surgerate/${id}`,
  UPDATE: (id: ResourceId) => `surgerate/${id}`,
  CREATE: 'surgerate',
  DELETE: 'surgerate',
};

//TRUCKS
export const TRUCKS_API_ROUTES = {
  ALL: 'truck/all',
  GET: (id: ResourceId) => `truck/${id}`,
  UPDATE: (id: ResourceId) => `truck/${id}`,
  CREATE: 'truck',
  DELETE: 'truck',
};

//TRUCKS_EQUIPMENTS
export const TRUCKS_EQUIPMENTS_API_ROUTES = {
  ALL: 'truckequipment/all',
  GET: (id: ResourceId) => `truckequipment/${id}`,
  UPDATE: (id: ResourceId) => `truckequipment/${id}`,
  CREATE: 'truckequipment',
  DELETE: 'truckequipment',
};

//QUALITY_TYPES
export const QUALITY_TYPES_API_ROUTES = {
  ALL: 'quality/all',
  GET: (id: ResourceId) => `quality/${id}`,
  UPDATE: (id: ResourceId) => `quality/${id}`,
  CREATE: 'quality',
  DELETE: 'quality',
};

// /api/shipzipcode/all

// Local Zip codes
export const LOCAL_ZIP_CODES_API_ROUTES = {
  ALL: 'shipzipcode/all',
  GET: (id: ResourceId) => `shipzipcode/${id}`,
  UPDATE: (id: ResourceId) => `shipzipcode/${id}`,
  CREATE: 'shipzipcode',
  DELETE: 'shipzipcode',
};

// Packing List Departments
export const PACKING_LIST_DEPARTMENTS_API_ROUTES = {
  ALL: 'packinglistdepartment/all',
  GET: (id: ResourceId) => `packinglistdepartment/${id}`,
  UPDATE: (id: ResourceId) => `packinglistdepartment/${id}`,
};

/** Profile Details **/

// Security
export const SECURITY_API_ROUTES = {
  CREATE: 'user/changepassword',
};

/** enumName API Route **/
export const STORE_LOCATIONS_ENUMS = {
  GET: (enumName: ResourceId, isAsc?: boolean) =>
    `enum/${enumName}${isAsc ? '?IsAsc=true' : ''}`,
};

/** Stores Options API Routes **/
export const STORE_OPTIONS_API_ROUTES = {
  ALL: 'store/location/all',
  DELETE: (id: ResourceId) => `store/delete/${id}`,
  GET: (id: ResourceId) => `store/location/find/${id}`,
  CREATE: 'store/location/save',
  CLONE: (id: ResourceId) => `/store/clone/${id}`,

  // user default store
  USER_DEFAULT_STORE: `store/user/default`,

  // Store option tabs
  COPYING_ORDER: 'store/save/copy-order-setting',
  DATE_CALCULATIONS: 'store/save/date-calculation',
  DELIVERY_CHARGES: 'store/save/delivery-charges',
  EMAIL_SETTINGS: 'store/email-settings/save',
  FORMS: 'store/save/form-data',
  MANAGER_APPROVAL: 'store/save/manager-approval',
  MULTI_LOCATION: 'store/save/multi-location',
  ORDER_ITEM_SETTINGS: 'store/save/order-item-settings',
  PASSWORDS: 'store/save/passwords',
  PAYMENTS: 'store/save/payment-settings',
  PRINT_SETTING: 'store/save/print-settings',
  RFID_SETTINGS: '/store/save/rfid-settings',
  TOTALS: '/store/save/totals',
  WAREHOUSE: 'store/save/warehouse-settings',
  MISC_SETTINGS: 'store/save/misc-settings',
  MISC_ORDER_SETTINGS: 'store/save/misc-order-setting',
  MISC_CUSTOMER_SETTINGS: 'store/save/misc-cust-setting',
};

export const COMPANY_API_ROUTES = {
  GET: 'company',
  CREDIT_CARD_PROCESSING: `/company/creditcard`,
  WEB_RATE_SERVICES: `/company/webrate`,
  E_SIGN: `/company/esign`,
  COMPANY_DETAILS: `/company/config`,
};

export const ITEMS_API_ROUTES = {
  ALL: '/item/all',
  CREATE: 'item/create',
  GET: (id: ResourceId) => `/item/${id}`,
  UPDATE: '/item/information/update',
  DELETE: (id: ResourceId) => `/item/${id}`,
  GET_COLUMN: 'user/tableMapping?tableName=ITEMS',
  UPDATE_COLUMN: 'user/tableMapping/save/ITEMS',
  // Option tabs
  UPDATE_OPTIONS: '/item/option/update',
  // Edit Items items
  EDIT_BULK_ITEM: '/item/bulk/update',
  COPY_ITEM: '/item/copy',
  KIT_ITEM: (id: ResourceId) => `/item/${id}/kits`,
  PACKAGE_ITEM: (id: ResourceId) => `/item/${id}/packages`,
  ITEM_BRIEF: (id: string) => `/item/${id}/brief`,
  UPDATE_KIT: (id: string) => `/item/${id}/kits/addupdate`,
  UPDATE_PACKAGE: (id: string) => `/item/${id}/packages/addupdate`,
  KIT_ITEM_LOOKUP: (id: string) => `/item/${id}/kits/itemlookup`,
  PACKAGE_ITEM_LOOKUP: (id: string) => `/item/${id}/packages/itemlookup`,
  KIT_ITEM_LOOKUP_DROPDOWN: (id: string) => `/item/${id}/kits/itemidlookup`,
  PACKAGE_ITEM_LOOKUP_DROPDOWN: (id: string) =>
    `/item/${id}/packages/itemidlookup`,
  DOWNLOAD: `item/export`,
};

export const INVENTORY_ITEMS_API_ROUTES = {
  ALL: (id: ResourceId) => `item/${id}/inventory/all`,
  GET: (id: ResourceId) => `item/inventory/${id}`,
  DELETE: (id: ResourceId) => `/item/inventory/${id}`,
  SAVE: (itemId: string) => `/item/${itemId}/inventory/save`,
  PURCHASEDATE_SEQUENCE: (id: ResourceId) =>
    `/item/${id}/inventory/listPurchaseDateSequence`,
  VALIDATE_QUANTITY: (itemId: ResourceId) =>
    `item/${itemId}/inventory/validatequantity`,
};

export const QUANTITY_ITEMS_API_ROUTES = {
  ALL: `item/quantity/all`,
  ADD: `item/addquantity`,
  UPDATE: `item/updateQuantity`,
  UPDATE_QUANTITY: (id: ResourceId) => `item/${id}/quantity/update`,
  GET: (id: ResourceId) => `item/${id}/quantity/get`,
  ADJUST: `item/adjustQuantity`,
  REMOVE: `/item/removequantity`,
  VIEW: (id: ResourceId) => `/item/adjust/${id}`,
};

export const ITEM_LINKED_FILE_API_ROUTES = {
  GET: (id: ResourceId) => `item/${id}/linkedfile/all`,
  UPLOAD: (id: ResourceId) => `item/${id}/linkedfile/upload`,
  REUPLOAD: (id: ResourceId) => `item/linkedfile/reupload/${id}`,
  DELETE: (id: ResourceId) => `item/linkedfile/delete/${id}`,
  DOWNLOAD: (id: ResourceId) => `item/linkedfile/download/${id}`,
  DEFAULT: (id: ResourceId) => `item/linkedfile/setdefault/${id}`,
};

/** Vendors API Routes **/
export const VENDORS_API_ROUTES = {
  ALL: 'vendor/all',
  GET: (id: ResourceId) => `vendor/${id}`,
  ADD: 'vendor/add',
  UPDATE: (id: ResourceId) => `vendor/${id}/update`,
  DELETE: (id: ResourceId) => `vendor/${id}/delete`,

  // List all additional contacts
  ADDITIONAL_CONTACT_ALL: 'additionalcontact/all',
  GET_ADDITIONAL_CONTACT: (id: ResourceId) => `additionalcontact/${id}`,
  ADD_UPDATE_ADDITIONAL_CONTACT: (id: ResourceId) =>
    `additionalcontact/${id}/save`,
  DELETE_ADDITIONAL_CONTACT: (id: ResourceId) => `additionalcontact/${id}`,
  DELETE_ADDITIONAL_CONTACT_PHONE_NUMBER: (
    contactId: ResourceId,
    phoneId: ResourceId
  ) => `/AdditionalContact/${contactId}/phone/${phoneId}/delete
`,

  EXPORT_CSV: 'vendor/export',
  GET_COLUMN: 'user/tableMapping?tableName=VENDOR',
  UPDATE_COLUMN: 'user/tableMapping/save/VENDOR',
};

/** System Users API Routes **/
export const SYSTEM_USERS_API_ROUTES = {
  ALL: 'user/all',
  DELETE: 'system/user',
  GET: (id: ResourceId) => `user/${id}`,
  GET_PERMISSIONS: (id: ResourceId) =>
    `user/permission/all?roleName=USER&userId=${id}`,
  INFORMATION_SAVE: 'user/save',
  INFORMATION_UPDATE: `user/save`,
  USER_OPTIONS_SAVE: 'user/save',
  USER_OPTIONS_UPDATE: 'user/save/contact',
  PROFILE_OPTIONS_SAVE: 'user/save',
  PROFILE_OPTIONS_UPDATE: 'user/permission/save',
  VERIFY_TOKEN: 'verify',
  PERMISSIONS: 'user/assigned/permissions',
  USER_ACTIVATION: (id: ResourceId, isActive: boolean) =>
    `user/update/status?id=${id}&isActive=${isActive}`,
  RESEND_INVITE: (userId: ResourceId) => `/user/${userId}/invite/resendemail`,
};

// Orders
export const ORDERS_API_ROUTES = {
  ALL: '/order/all',
  GET_COLUMN: 'user/tableMapping?tableName=ORDERS',
  UPDATE_COLUMN: 'user/tableMapping/save/ORDERS',
  CREATE: '/order/save',
  ORDER_ENQUIRY_SAVE: 'order/enquiry/save',
  GET: (id: ResourceId) => `/order/get?id=${id}`,
  UPDATE: '/order/save',
  DELETE: (id: ResourceId) => `order/delete?id=${id}`,
  CLONE: (id: ResourceId, isCopy: boolean) =>
    `/order/${id}/copy?toCopyItems=${isCopy}`,
  LOCK_ORDER: (id: ResourceId) => `order/locked?orderid=${id}`,
  UNLOCK_ORDER: (id: ResourceId) => `order/unlocked?orderid=${id}`,

  // Option tabs
  UPDATE_OPTIONS: '/order/option/update',
  DATE_CALCULATION: '/delivery/calculate',

  // Export CSV
  EXPORT_CSV: 'order/export',

  ORDER_NOTES: 'order/order-notes',
  LINKED_FILES: 'order/linked-files',
  CHECKLIST: (id: ResourceId) => `checklistmain/order/checklist?orderId=${id}`,
  UPDATE_CHECKLIST: 'checklistmain/update',

  LINKED_ALL: (id: ResourceId) => `order/${id}/linkedfile/all`,
  ALL_NOTE: (id: ResourceId) => `order/${id}/note/all`,
  CREATE_UPDATE_NOTE: (id: ResourceId) => `order/${id}/note/addupdate`,
  GET_NOTE: (orderId: ResourceId, orderNoteId: ResourceId) =>
    `order/${orderId}/note/${orderNoteId}/fetch`,
  DELETE_NOTE: (orderId: ResourceId, orderNoteId: ResourceId) =>
    `order/${orderId}/note/${orderNoteId}/delete`,
  FIND: (id: ResourceId) => `order/${id}`,
  UPLOAD: (id: ResourceId) => `order/${id}/linkedfile/upload`,
  REUPLOAD: (id: ResourceId) => `order/linkedfile/reupload/${id}`,
  DOWNLOAD: (id: ResourceId) => `order/linkedfile/download/${id}`,
  DELETE_LINKEDFILE: (id: ResourceId) => `order/linkedfile/delete/${id}`,
  DEFAULT: (id: ResourceId) => `order/linkedfile/setdefault/${id}`,

  // missing equipment
  MISSING_EQUIPMENT: 'order/fetch/missing',
  CREATE_MISSING_EQUIPMENT: (orderId: ResourceId) =>
    `order/${orderId}/shipitem/me`,
  CREATE_MISSING_EQUIPMENT_ITEMS: '/order/missing',

  // return order
  RETURN_ORDER: 'order/return',

  // order history
  ORDER_HISTORY: (orderId: ResourceId) => `order/${orderId}/history/all`,

  // order orderDirections
  ORDER_DIRECTIONS: 'order/directions-info',
  CASH_SALE: (orderId: ResourceId) => `order/${orderId}/customer/cashsale`,
  GET_ORDER_DIRECTION: (orderId: ResourceId) =>
    `order/${orderId}/direction/fetch`,
  ADD_UPDATE_ORDER_DIRECTION: (orderId: ResourceId) =>
    `order/${orderId}/direction/addupdate`,

  // Kit Components
  KIT_LIST: (id: ResourceId) =>
    `order/item/listKitComponents?orderItemId=${id}`,
  DELETE_KIT: (orderId: ResourceId, kitItemId: ResourceId) =>
    `order/${orderId}/kit/${kitItemId}/delete`,

  // Overbooked Item Info
  OVERBOOKED_ITEM_INFO: 'item/overbookedinfo',
  SERIAL_NOS: (itemId: ResourceId) => `item/${itemId}/serialnos`,
  INVENTORY_BY_LOCATION: 'item/inventorybylocation',

  // Total and Payments
  GET_TOTAL_PAYMENTS: (orderId: ResourceId) => `order/total?orderId=${orderId}`,
  UPDATE_DELEVERY_CHARGE: 'order/total/update/delivery',
  TAX_EXEMPT: (orderId: ResourceId, toExempt: boolean) =>
    `order/exempt/tax?orderId=${orderId}&toExempt=${toExempt}`,
  SAVE_TOTAL_PAYMENTS: 'order/save/total',
  RECALCULATE_PAYMENTS: (orderId: ResourceId, chargeType: string) =>
    `order/total/recalculate?orderId=${orderId}&chargeType=${chargeType}`,
  CHANGE_TAX_CODE: 'order/total/change/taxcode',
  GET_PAYMENT_LINK: 'payment/makePayment',
  PAYMENT_SUCCESS: 'payment/paymentCallBack',
  GET_CUSTOMER_CARD_INFO: (customerId: ResourceId) =>
    `customer/customerCard?customerId=${customerId}`,
  DELETE_CUSTOMER_CARD_INFO: (cardId: ResourceId) =>
    `customer/removeCard?cardId=${cardId}`,
  PAYMENT_REUSE_CC_ACH: 'payment/reuse/ccach',
  GET_PAYMENT_DETAILS: `order/paymentDetails`,
  ADD_PAYMENT: 'order/addPayment',
  DELETE_PAYMENT: (paymentId: number, voidTransaction: boolean) =>
    `order/removePayment?paymentId=${paymentId}&voidTransaction=${voidTransaction}`,
  CALCULATE_CONV_FEE_TAX: (
    orderId: ResourceId,
    paymentTypeId: ResourceId,
    amount: ResourceId
  ) =>
    `order/calculateConvFeeTax?orderId=${orderId}&paymentTypeId=${paymentTypeId}&amount=${amount}`,
  REFUND_PAYMENT: 'payment/refund',

  // Order Status Details
  GET_STATUS_ORDER: (orderId: ResourceId) => `order/${orderId}/orderstatus`,
  SAVE_STATUS_ORDER: `order/orderstatus/save`,
  UPDATE_ORDER_STATUS: (id: ResourceId) => `order/${id}/orderstatus/update`,

  // Order Additional Info
  GET_ADDITIONAL_INFO: (orderid: ResourceId) =>
    `order/${orderid}/additional-info`,
  SAVE_ADDITIONAL_INFO_ORDER: (orderid: ResourceId) =>
    `order/${orderid}/additional-info/save`,

  // Order Additional Info (Assign Job)
  GET_ADDITIONAL_INFO_ASSIGN_JOB: (customerid: ResourceId) =>
    `order/jobs/customer/${customerid}`,

  // Order Additional Info (Assign Job)
  SAVE_ADDITIONAL_INFO_ASSIGN_JOB: (
    orderid: ResourceId,
    jobno: number,
    assignjobno: number
  ) => `order/${orderid}/job/${jobno}/assignjob/${assignjobno}`,

  // Order Additional Info (Assign Orders)
  GET_ADDITIONAL_INFO_ASSIGN_ORDERS: (customerid: ResourceId) =>
    `order/customer/${customerid}`,

  // Order Additional Info (Assign Orders)
  SAVE_ADDITIONAL_INFO_ASSIGN_ORDERS: (orderid: ResourceId, jobno: number) =>
    `order/${orderid}/job/${jobno}/assignorder`,

  // Order Additional Info (Breakdown)
  GET_ADDITIONAL_INFO_BREAKDOWN: (orderId: ResourceId) =>
    `order/additional-info/breakdown?orderId=${orderId}`,

  // Order Additional Info (Breakdown)
  SAVE_ADDITIONAL_INFO_BREAKDOWN: `order/additional-info/breakdown`,

  // Order Additional Info (Shipping Info)
  GET_ADDITIONAL_INFO_SHIPPING_INFO_ITEM: (orderId: ResourceId) =>
    `order/${orderId}/shipitems`,

  GET_ADDITIONAL_INFO_SHIPPING_INFO_BOX: (orderId: ResourceId) =>
    `order/${orderId}/shipboxes`,

  // Order Additional Info (Shipping Info)
  SAVE_ADDITIONAL_INFO_SHIPPING_INFO_ITEM: (orderId: ResourceId) =>
    `order/${orderId}/shipitems`,

  // Order Additional Info (Shipping Info)
  SAVE_ADDITIONAL_INFO_SHIPPING_INFO_BOX: (orderId: ResourceId) =>
    `order/${orderId}/shipboxes`,

  UPDATE_ADDITIONAL_INFO_SHIPPING_INFO_BOX: (orderId: ResourceId) =>
    `order/${orderId}/shipbox`,

  // Add row the BOX Table (Shipping Box) (Shipping Info)
  ADD_SHIPPING_BOX: (orderId: ResourceId) => `order/${orderId}/shipbox`,

  // Add row the BOX Table (Shipping Box) (Shipping Info)
  ADD_RETURN_BOX: (orderId: ResourceId) => `order/${orderId}/returnbox`,

  // Split row in the Item Table
  SPLIT_ITEM_ROW: (orderId: ResourceId, itemid: ResourceId) =>
    `order/${orderId}/shipitem/${itemid}/split`,

  // Track Shippment in the BOX Table
  TRACK_SHIPPMENT: (orderId: ResourceId, pkId: ResourceId) =>
    `order/${orderId}/shipbox/${pkId}/trackshippment`,

  // ENUM for Shipping Info Status
  GET_STATUS_SHIPPING_INFO: `enum/ShipStatus`,

  // Order Sales & Referrals Tab
  GET_SALES_PERSON_SALES_REFERRALS: (userId: ResourceId) =>
    `/user/${userId}/commission`,
  GET_SALES_REFERRALS_CUSTOMER_SERACH: (userId: ResourceId) =>
    `/customer/${userId}/referral`,
  GET_SALES_REFERRALS: (orderId: ResourceId) =>
    `/order/${orderId}/salesreferral/detail`,
  SAVE_SALES_REFERRALS: `/order/salesreferral/saveupdate`,

  // Availability Calendar
  GET_AVAILABILITY_DATES: 'availabilitycal/dates',
  GET_AVAILABILITY_ORDERS: 'availabilitycal/orders',

  // Busy Calendar
  GET_BUSY_DATES: 'busycal/dates',
  GET_BUSY_ORDERS: 'busycal/orders',
  BUSY_CAL_SET_STATUS: 'busycal/setstatus',
  BUSY_CAL_REVIEW_LEVEL: 'busycal/reviewlevel',
  GET_BUSY_CALENDAR_STATUS_BY_DATE: (date: ResourceId) =>
    `busycal/getstatus/${date}`,

  // Order Additional Info (Assign Options)
  GET_ASSIGN_OPTIONS_ADDITIONAL_INFO: (orderId: ResourceId) =>
    `order/additional-info/assign-option?orderId=${orderId}`,

  // Order Additional Info (Assign Options)
  SAVE_ASSIGN_OPTIONS_ADDITIONAL_INFO: `order/additional-info/assign-option`,

  // GET Crew Details
  GET_CREW_DETAILS: ({
    orderId,
    leadType,
    departmentId,
  }: {
    orderId: string;
    leadType: string;
    departmentId: string;
  }) => `order/${orderId}/crew/lead/${leadType}/department/${departmentId}`,
  // Save Crew Details
  SAVE_CREW_DETAILS: (orderid: ResourceId) => `order/${orderid}/crew/update`,

  CHECK_INACTIVE_ITEMS: (orderId: number) => `item/order/${orderId}/inactive`,
  //E-sign Documents
  E_SIGN_DOCUMENTS: 'esign/all',
  E_SIGN_REMAINDER: 'esign/send/reminder',
  E_SIGN_CANCEL_LINK: 'esign/cancel/link',
  E_SIGN_DOWNLOAD_CERTIFICATE: 'esign/download/certificate',
  E_SIGN_IS_ORDER_INVOICED: (orderId: ResourceId) =>
    `order/${orderId}/esign/isorderinvoiced`,
  E_SIGN_CUSTOMER_CONTACTS: (orderId: ResourceId) =>
    `order/${orderId}/esign/customercontacts`,
  E_SIGN_ADD_ADDITIONAL_CUSTOMER: (orderId: ResourceId) =>
    `order/${orderId}/esign/customer/additional/new`,
  ESIGN_SEND: (orderId: ResourceId) => `order/${orderId}/esign/send`,
  REFRESH_E_SIGN: 'esign/refresh/data',
};

// Order Item Details
export const ORDER_ITEM_DETAILS_API_ROUTES = {
  ITEM_LOOKUP: (orderId: ResourceId) => `item/order/${orderId}/lookup`,
  LIST_ORDER_ITEMS: `order/listorderitems`,
  SEARCH_ITEM: 'item/lookup',
  SAVE: (orderId: ResourceId) => `order/${orderId}/additems`,
  ITEM_BRIEF: 'order/item/brief',
  DELETE: `order/item/delete`,
  UPDATE_SURGE: `order/item/updateSurgeCharge`,
  CHANGE_SORTING: 'order/item/savePrintPreference',
  MISSING_ITEMS: (orderId: ResourceId) =>
    `order/${orderId}/item/listWithKitComponents`,
};

type OrderInfoType = {
  orderId?: ResourceId;
  orderItemId: ResourceId;
  id?: ResourceId;
};

// Order Item Details
export const ORDER_ITEM_INFO_API = {
  GET: ({ orderId, orderItemId }: OrderInfoType) =>
    `order/${orderId}/additionaliteminfo/${orderItemId}`,

  LIST: (orderItemId: ResourceId) =>
    `order/orderitem/${orderItemId}/linkedfile/all`,

  UPLOAD: (orderItemId: ResourceId) =>
    `order/orderitem/${orderItemId}/linkedfile/upload`,

  REUPLOAD: ({ orderItemId, id }: OrderInfoType) =>
    `order/orderitem/${orderItemId}/linkedfile/${id}/reupload`,

  DELETE: (id: ResourceId) => `order/orderitem/linkedfile/delete/${id}`,

  DOWNLOAD: (id: ResourceId) => `order/orderitem/linkedfile/download/${id}`,

  DEFAULT: (id: ResourceId) => `order/orderitem/linkedfile/setdefault/${id}`,

  SEND: ({ orderId, orderItemId }: OrderInfoType) =>
    `email/${orderId}/orderitem/${orderItemId}/sendemail`,

  GET_EMAIL_LIST: (id: ResourceId) => `AdditionalContact/customer/${id}`,
};

// Change Serialized Items
export const CHANGE_SERIALIZED_ORDER_ITEMS_API_ROUTES = {
  GET: 'order/item/changeSerializeItemFilter',
  POST: ({
    orderItemId,
    inventoryId,
  }: {
    orderItemId: ResourceId;
    inventoryId: ResourceId;
  }) =>
    `order/item/changeSerializeItem/order/${orderItemId}/inventory/${inventoryId}`,
};

// SubRent Item API Routes
export const ORDER_SUB_RENT_ITEM_API_ROUTES = {
  GET: ({ orderId, itemId }: { orderId: ResourceId; itemId: ResourceId }) =>
    `order/${orderId}/orderitem/${itemId}/subrent`,
  SUBRENT_DETAIL: ({
    orderId,
    custVenId,
    contactTypeValue,
  }: {
    orderId: ResourceId;
    custVenId: ResourceId;
    contactTypeValue: ResourceId;
  }) =>
    `order/${orderId}/subrent/custvend/${custVenId}/type/${contactTypeValue}/detail`,
  LOOKUP: `order/subrent/custvenddetails`,
  SAVE: ({
    orderId,
    orderItemId,
  }: {
    orderId: ResourceId;
    orderItemId: ResourceId;
  }) => `order/${orderId}/orderitem/${orderItemId}/subrent/save`,

  DELETE: ({
    orderId,
    orderItemId,
  }: {
    orderId: ResourceId;
    orderItemId: ResourceId;
  }) => `order/${orderId}/orderitem/${orderItemId}/subrent/delete`,
  SUBRENT_BY_ORDERID: (orderId: ResourceId) => `order/${orderId}/subrent/all`,
};

/** Sun rental API Routes **/
export const SUB_RENTAL_API_ROUTES = {
  ALL: 'subrent/all',
  GET: (id: ResourceId) => `subrent/${id}`,
  ADD_UPDATE: 'subrent/save',
  DELETE: 'subrent/delete',
  CUSTOMER_VENDRO_LOOKUP: `order/subrent/custvenddetails`,
  ITEM_LOOKUP: (subRentId: ResourceId) => `subrent/${subRentId}/lookup`,

  GET_COLUMN: 'user/tableMapping?tableName=SUB_RENTAL',
  UPDATE_COLUMN: 'user/tableMapping/save/SUB_RENTAL',
  LIST_SUB_RENTAL_ITEMS: (subRentId: ResourceId) =>
    `subrent/${subRentId}/subrentItems`,
  SAVE_SUB_RENTAL_ITEMS: (subRentId: ResourceId) =>
    `subrent/${subRentId}/subrentitems/save`,
  DELETE_SUB_RENTAL_ITEMS: (subRentId: ResourceId, itemId: ResourceId) =>
    `subrent/${subRentId}/subrentitem/${itemId}/delete
`,
};

// Print and Email Order API Routes
export const PRINT_EMAIL_API_ROUTES = {
  LIST: (name: string) => `/printform/${name}/all`,
  SELECTED_OPTIONS: 'printform/autoselectionoptions',
};

// inventory manager API Route
export const INVENTORY_MANAGER_API_ROUTES = {
  GET_DATA: 'inventorymanager/getdata',
  EXPORT_CSV: 'inventorymanager/export',
};

// Online Payment API Route
export const ONLINE_PAYMENTS_API_ROUTES = {
  GET: '/paymenthistory/get',
  CANCEL: 'paymenthistory/cancel/link',
  REFRESH: 'paymenthistory/refresh',
};

// Purchase Orders
export const PURCHASE_API_ROUTES = {
  ALL: 'purchaseorder/list',
  SAVE: 'purchaseorder/save',
  GET: (id: ResourceId) => `purchaseorder/get/${id}`,
  DELETE: 'purchaseorder/delete',
  COPY: `purchaseorder/copy`,
  GET_COLUMN: 'user/tableMapping?tableName=PURCHASE_ORDER',
  UPDATE_COLUMN: 'user/tableMapping/save/PURCHASE_ORDER',
  DOWNLOAD: `purchaseorder/export`,
  GET_ITEM_DETAILS: (id: ResourceId) => `purchaseorder/${id}/item/list`,
  SAVE_ITEM_DETAILS: (id: ResourceId) => `/purchaseorder/${id}/addItem`,
  DELETE_ITEM: ({ poId, poItemId }: { poId: string; poItemId: number }) =>
    `purchaseorder/${poId}/item/${poItemId}`,
  SAVE_RECIEVED_ITEM_DETAILS: (id: ResourceId) =>
    `purchaseorder/${id}/item/receive`,
  RECIEVED_ALL: (id: ResourceId) => `purchaseorder/${id}/item/receiveAll`,
  GENERIC_ITEM_LOOKUP: `item/genericLookup`,

  // update details
  INFORMATION_UPDATE: (id: ResourceId) =>
    `purchaseorder/update/information/${id}`,
  RECEIVED_UPDATE: (id: ResourceId) =>
    `purchaseorder/update/received-item/${id}`,

  // get details
  ITEM_DETAILS_GET: (id: ResourceId) => `purchaseorder/item-details/${id}`,
  RECEIVED_GET: (id: ResourceId) => `purchaseorder/received-item/${id}`,
};

// ENUM LEAD TYPE
export const LEAD_TYPE_ENUMS = {
  GET: `enum/Lead`,
};

/** Warehouse API Routes **/
export const WAREHOUSE_CUSTOMER_PICKUPS_RETURNS_API_ROUTES = {
  ALL: 'warehouse/all',
  GET: (id: ResourceId) => `warehouse/${id}`,
  ADD: 'warehouse/add',
  UPDATE: (id: ResourceId) => `warehouse/${id}/update`,
  DELETE: (id: ResourceId) => `warehouse/${id}/delete`,

  EXPORT_CSV: 'warehouse/export',
  GET_COLUMN: 'user/tableMapping?tableName=WAREHOUSE',
  UPDATE_COLUMN: 'user/tableMapping/save/WAREHOUSE',

  DOWNLOAD_FILE: 'warehouse/download',
  GET_LIST_PRINT: 'warehouse/print',
};

export const WAREHOUSE_ORDER_DELIVERIES_PICKUP_API_ROUTES = {
  ALL: 'warehouse/all',
  GET: (id: ResourceId) => `warehouse/${id}`,
  ADD: 'warehouse/add',
  UPDATE: (id: ResourceId) => `warehouse/${id}/update`,
  DELETE: (id: ResourceId) => `warehouse/${id}/delete`,

  EXPORT_CSV: 'warehouse/export',
  GET_COLUMN: 'user/tableMapping?tableName=WAREHOUSE',
  UPDATE_COLUMN: 'user/tableMapping/save/WAREHOUSE',

  DOWNLOAD_FILE: 'warehouse/download',
  GET_LIST_PRINT: 'warehouse/print',
};

export const WAREHOUSE_SUBRENTAL_PICKUPS_RETURNS_API_ROUTES = {
  ALL: 'warehouse/all',
  GET: (id: ResourceId) => `warehouse/${id}`,
  ADD: 'warehouse/add',
  UPDATE: (id: ResourceId) => `warehouse/${id}/update`,
  DELETE: (id: ResourceId) => `warehouse/${id}/delete`,

  EXPORT_CSV: 'warehouse/export',
  GET_COLUMN: 'user/tableMapping?tableName=WAREHOUSE',
  UPDATE_COLUMN: 'user/tableMapping/save/WAREHOUSE',

  DOWNLOAD_FILE: 'warehouse/download',
  GET_LIST_PRINT: 'warehouse/print',
};

export const WAREHOUSE_TRUCK_ITINERARIES_API_ROUTES = {
  ALL: 'warehouse/all',
  GET: (id: ResourceId) => `warehouse/${id}`,
  ADD: 'warehouse/add',
  UPDATE: (id: ResourceId) => `warehouse/${id}/update`,
  DELETE: (id: ResourceId) => `warehouse/${id}/delete`,

  EXPORT_CSV: 'warehouse/export',
  GET_COLUMN: 'user/tableMapping?tableName=WAREHOUSE',
  UPDATE_COLUMN: 'user/tableMapping/save/WAREHOUSE',

  LIST_TRUCK_ITINERARY_ORDERS: (id: ResourceId) => `warehouse/order/list/${id}`,
  SAVE_TRUCK_ITINERARY_ORDERS: (id: ResourceId) =>
    `warehouse/order/list/save/${id}`,

  LIST_TRUCK_ITINERARY_JOB_ORDER: (id: ResourceId) =>
    `warehouse/order-job/list/${id}`,
  GET_COLUMN_ORDER_JOB: 'user/tableMapping?tableName=WAREHOUSE_ORDER_JOB',
  UPDATE_COLUMN_ORDER_JOB: 'user/tableMapping/save/WAREHOUSE_ORDER_JOB',

  LIST_TRUCK_ITINERARY_SUB_RENTAL: (id: ResourceId) =>
    `warehouse/sub-rental/list/${id}`,
  GET_COLUMN_SUB_RENTAL: 'user/tableMapping?tableName=WAREHOUSE_SUB_RENTAL',
  UPDATE_COLUMN_SUB_RENTAL: 'user/tableMapping/save/WAREHOUSE_SUB_RENTAL',

  DOWNLOAD_FILE: 'warehouse/download',
  GET_LIST_PRINT: 'warehouse/print',
};

/** Processes API Routes **/
export const PROCESSES_API_ROUTES = {
  ALL: 'processes/all',
  POSTING: 'processes/posting',
  GET_COLUMN: 'user/tableMapping?tableName=VENDOR',
  UPDATE_COLUMN: 'user/tableMapping/save/VENDOR',
};
