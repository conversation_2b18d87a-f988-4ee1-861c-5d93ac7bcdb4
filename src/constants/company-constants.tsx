import CompanyDetails from '@/components/modules/system/company/CompanyDetails';
import CreditCardProcessing from '@/components/modules/system/company/CreditCardProcessing';
import Esign from '@/components/modules/system/company/Esign';
import WebRateServices from '@/components/modules/system/company/WebRateServices';
import {
  CCProcessingType,
  CompanyTabsEnum,
  EsignType,
  FedExOption,
  GravityCheckboxOption,
} from '@/types/company.types';

export const companyTabList = [
  {
    value: CompanyTabsEnum.COMPANY_DETAILS,
    label: 'Information',
    content: <CompanyDetails />,
  },
  {
    value: CompanyTabsEnum.CREDIT_CARD_PROCESSING,
    label: 'Credit Card Processing',
    content: <CreditCardProcessing />,
  },

  {
    value: CompanyTabsEnum.WEB_RATE_SERVICES,
    label: 'Web Rate Services',
    content: <WebRateServices />,
  },
  {
    value: CompanyTabsEnum.E_SIGN,
    label: 'E-Sign',
    content: <Esign />,
  },
];

// Define Gravity Checkbox options with comments for clarity
export const GRAVITY_CHECKBOX_OPTIONS = [
  {
    name: GravityCheckboxOption.USE_DEVICES,
    label: 'Use Devices',
  },
  {
    name: GravityCheckboxOption.LEVEL_2_DATA,
    label: 'Level 2 Data',
    value: 'enable-ssl',
  },
];

// Define the radio button options as a constant
export const ESIGN_TYPE_OPTIONS = [
  { label: 'None', value: EsignType.NONE },
  { label: 'PTSign', value: EsignType.PTSIGN },
];

// Define FedEx options as a constant
export const FEDEX_OPTIONS = [
  { label: 'Yes', value: FedExOption.YES },
  { label: 'No', value: FedExOption.NO },
];

// Define the radio button options as a constant
export const CC_PROCESSING_TYPE_OPTIONS = [
  { label: 'None', value: CCProcessingType.NONE },
  { label: 'Gravity Payments', value: CCProcessingType.GRAViTY_PAYMENTS },
];
