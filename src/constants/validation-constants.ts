import { ZipCodePatterns } from '@/types/common.types';

// Const required declaration
const REQUIRED_TEXT = 'Required';

// Regex that allows only letters and spaces
const ALPHA_TEXT_ONLY_PATTERN = /^[A-Za-z\s]+$/;

const TEXT_VALIDATION_RULE = {
  required: REQUIRED_TEXT,
};

const TEXT_ONLY_VALIDATION_RULE = {
  required: REQUIRED_TEXT,
  pattern: {
    value: ALPHA_TEXT_ONLY_PATTERN,
    message: 'Invalid input. Enter text value only.',
  },
};
// without required
const EMAIL_VALIDATION_RULE = {
  pattern: {
    value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
    message: 'Please Enter a valid Email Address',
  },
};

// with required
const emailValidation = (required?: boolean) => {
  // Create the validation rule object
  const validationRule: any = {
    pattern: {
      value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
      message: 'Please Enter a valid Email Address',
    },
    validate: (value: string | undefined | null) => {
      const cleanedValue = value?.trim().replace(/^\+1/, '') || '';

      if (!cleanedValue) return true;

      return true;
    },
  };

  // Conditionally add the 'required' property if 'required' is true
  if (required) {
    validationRule.required = REQUIRED_TEXT;
  }

  return validationRule;
};

// // without required
// const PHONE_VALIDATION_RULE = {
//   validate: (value: string | undefined | null) => {
//     const cleanedValue = value?.trim().replace(/^\+1/, '') || '';

//     if (cleanedValue.length < 1) return true;

//     const phonePattern = /^\(?([0-9]{3})\)?[-.●]?([0-9]{3})[-.●]?([0-9]{4})$/;

//     return phonePattern.test(cleanedValue)
//       ? true
//       : 'Please enter a valid Phone number';
//   },
// };

const PHONE_VALIDATION_RULE = {
  validate: (value: string | undefined | null) => {
    if (!value) return true;

    const cleanedValue = value.trim().replace(/^\+1/, '').replace(/_/g, '');

    // Split by 'x' to separate base and extension
    const [phonePart] = cleanedValue.split('x');

    // Remove non-digit characters
    const digitsOnly = phonePart.replace(/\D/g, '');

    // Only validate phone part, must be exactly 10 digits
    return digitsOnly.length === 0 || digitsOnly.length === 10
      ? true
      : 'Please enter a valid Phone number';
  },
};

// without required
// const FAX_VALIDATION_RULE = {
//   validate: (value: string | undefined | null) => {
//     const cleanedValue = value?.trim().replace(/^\+1/, '') || '';

//     if (cleanedValue.length < 1) return true;

//     const phonePattern = /^\(?([0-9]{3})\)?[-.●]?([0-9]{3})[-.●]?([0-9]{4})$/;

//     return phonePattern.test(cleanedValue)
//       ? true
//       : 'Please enter a valid Fax number';
//   },
// };

// without required
const FAX_VALIDATION_RULE = {
  validate: (value: string | undefined | null) => {
    if (!value) return true;

    const cleanedValue = value.trim().replace(/^\+1/, '').replace(/_/g, '');

    // Split by 'x' to separate base and extension
    const [phonePart] = cleanedValue.split('x');

    // Remove non-digit characters
    const digitsOnly = phonePart.replace(/\D/g, '');

    // Only validate phone part, must be exactly 10 digits
    return digitsOnly.length === 0 || digitsOnly.length === 10
      ? true
      : 'Please enter a valid Fax number';
  },
};

const zipCodeValidationRule = (isUSA?: boolean, required?: boolean) => {
  // Create the validation rule object
  const validationRule: any = {
    validate: (value: string | undefined | null) => {
      const trimmedValue = value?.trim() || '';

      if (!trimmedValue) return true;

      const usaZipCodePattern = /^\d{5}(-\d{4})?$/; // USA ZIP (5 or 9 digits)
      const canadaZipCodePattern = /^[A-Za-z0-9]{3}\s[A-Za-z0-9]{3}$/;

      const isValid = isUSA
        ? usaZipCodePattern.test(trimmedValue) // USA validation
        : canadaZipCodePattern.test(trimmedValue); // Canada validation

      return isValid || 'Please enter a valid ZIP Code'; // Return validation message
    },
  };

  // Conditionally add the 'required' property if 'required' is true
  if (required) {
    validationRule.required = REQUIRED_TEXT;
  }

  return validationRule;
};

const getZipCodeMask = (isUSA?: boolean): string => {
  return isUSA ? '99999' : '*** ***';
};
export {
  EMAIL_VALIDATION_RULE,
  FAX_VALIDATION_RULE,
  getZipCodeMask,
  PHONE_VALIDATION_RULE,
  REQUIRED_TEXT,
  TEXT_ONLY_VALIDATION_RULE,
  TEXT_VALIDATION_RULE,
  zipCodeValidationRule,
  emailValidation,
};

export const WEB_SITE_VALIDATION_RULE = {
  pattern: {
    value:
      /^(https?:\/\/)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(\/[a-zA-Z0-9#?&=._-]*)?$/,
    message: 'Please enter a valid URL',
  },
};

export const ZIP_CODE_PATTERNS: ZipCodePatterns = {
  US: {
    basic: /^\d{5}$/,
    extended: /^\d{5}-\d{4}$/,
    any: /^\d{5}(-\d{4})?$/,
  },
  CA: /^[A-Z0-9]{3} ?[A-Z0-9]{3}$/i,
};

export const ALPHA_NUMERIC_VALIDATION_RULE = {
  required: REQUIRED_TEXT,
  pattern: {
    value: /^[A-Za-z0-9]+$/,
    message: 'Please Enter Only Alphanumeric Characters',
  },
};

// Password validation rule
export const PASSWORD_VALIDATION_RULE = {
  value:
    /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[!@#$%^&*()_+{}\[\]:;"'<>,.?/-]).*$/,
  message:
    'Must contain at least one uppercase letter, lowercase letter, number and special character',
};

export const QUANTITY_VALIDATION_RULE = {
  required: REQUIRED_TEXT,
  validate: {
    greaterThanZero: (value: any) =>
      parseFloat(value) > 0 || 'Quantity must be greater than 0',
  },
};

export const SERIAL_VALIDATION_RULE = {
  required: REQUIRED_TEXT,
  validate: {
    validSerialQuantity: (value: any) =>
      parseFloat(value) === 1 ||
      'Quantity must be 1 when a serial number is present',
  },
};

export const FIRST_SIX_DIGITS_VALIDATION = {
  required: 'Please enter the first 6 digits of your credit card',
  pattern: {
    value: /^[0-9]{6}$/,
    message: 'Please enter exactly 6 digits',
  },
};
