import AppTabsVertical from '@/components/common/app-tabs-vertical';
import { PURCHASE_API_ROUTES } from '@/constants/api-constants';
import { generateAdditionalItemInfoTabs } from '@/constants/order-item-details-constants';
import { cn } from '@/lib/utils';

import ChangeSerializedItem from '@/components/modules/orders/item-details/change-item/ChangeSerializedItem';
import AvailabiltyCalendarInfo from '@/components/modules/orders/item-details/item-list/availability-calendar/availabilty-calendar-info';
import ItemLookup from '@/components/modules/orders/item-details/kit-item/ItemLookup';
import KitItems from '@/components/modules/orders/item-details/kit-item/kit-items';
import InventoryByLocation from '@/components/modules/orders/item-details/overbooked-item-info/InventoryByLocation';
import OverBookedItemLookup from '@/components/modules/orders/item-details/overbooked-item-info/ItemLookup';
import OverBookedItems from '@/components/modules/orders/item-details/overbooked-item-info/OverbookedItems';
import SerialInfo from '@/components/modules/orders/item-details/overbooked-item-info/SerialInfo';
import CustomerLook from '@/components/modules/orders/item-details/sub-rental/CustomerLook';
import ItemNewSubRent from '@/components/modules/orders/item-details/sub-rental/ItemNewSubRent';
import SubRenting from '@/components/modules/orders/item-details/sub-rental/SubRenting';
import { SubRentingTabEnum } from '@/types/order.types';
import {
  CustomerVendorLookupTypes,
  GenerateKitItemsTreeProps,
} from '@/types/orders/order-item-details.types';

export const generateKitItemsTree = ({
  kitForm,
  additionalItemInfoForm,
  availabilityForm,
  overBookedForm,
  customerData,
  setCustomerData,
  serialData,
  serialList,
  serialLoading,
  fields,
  append,
  remove,
  onOpenChange,
  setActiveTab,
  handleAddItemLookupData,
  activeInfoTab,
  handleTabChange,
  rowSelection,
  setRowSelection,
  kitItemId,
  setKitItemId,
  isLoading,
  selectedItem,
  orderItemId,
  subRentalRowSelection,
  setSubRentalRowSelection,
  handleUpdateSubRent,
  isUpdating,
  sorting,
  setSorting,
  handleNewSubRental,
  setSelectedSubRents,
  orderSubRents,
  isSubRentLoading,
}: GenerateKitItemsTreeProps) => {
  return {
    label: 'Kit Components',
    value: 'kit-item',
    content: (
      <KitItems
        form={kitForm}
        onOpenChange={onOpenChange}
        setActiveTab={setActiveTab}
        append={append}
        remove={remove}
        fields={fields}
        rowSelection={rowSelection}
        setRowSelection={setRowSelection}
        setKitItemId={setKitItemId}
        isLoading={isLoading}
        orderItemId={orderItemId}
      />
    ),
    children: [
      {
        label: 'Item Lookup',
        value: 'kit-item-lookup',
        content: (
          <ItemLookup
            onClick={handleAddItemLookupData}
            url={PURCHASE_API_ROUTES.GENERIC_ITEM_LOOKUP}
            heading="Select one or more Item to add to the Kit Item list"
            setActiveTab={setActiveTab}
            btnLabel="Add to Kit Components List"
            extraFilters={[{ field: 'isKitAllowded', value: 'false' }]}
          />
        ),
      },
      {
        label: 'Sub-Renting',
        value: SubRentingTabEnum.SUB_RENTING,
        content: (
          <SubRenting
            orderItemId={Number(Object.keys(rowSelection)[0])}
            isLoading={isSubRentLoading}
            orderSubRents={orderSubRents}
            sorting={sorting}
            setSorting={setSorting}
            toggleNewSubRental={handleNewSubRental}
            handleUpdate={handleUpdateSubRent}
            isUpdating={isUpdating}
            setSelectedRows={setSelectedSubRents}
            rowSelection={subRentalRowSelection}
            setRowSelection={setSubRentalRowSelection}
          />
        ),
        children: [
          {
            label: 'New Sub Rental',
            value: SubRentingTabEnum.NEW_SUB_RENT,
            content: (
              <ItemNewSubRent
                customerData={customerData}
                setActiveTab={setActiveTab}
                orderItemId={Number(Object.keys(rowSelection)[0])}
              />
            ),
            children: [
              {
                label: 'Customers/Vendors Search',
                value: SubRentingTabEnum.CUSTOMER_LOOKUP,
                content: (
                  <CustomerLook
                    handleOk={(value: CustomerVendorLookupTypes) => {
                      setActiveTab(SubRentingTabEnum.NEW_SUB_RENT);
                      setCustomerData(value);
                    }}
                    setActiveTab={setActiveTab}
                  />
                ),
              },
            ],
          },
        ],
      },
      {
        label: 'Change Serialized Item',
        value: 'change-item',
        content: (
          <ChangeSerializedItem
            onOpenChange={() => setActiveTab('kit-item')}
            selectedItem={selectedItem}
            setItemRowSelection={setRowSelection}
          />
        ),
      },
      {
        label: 'Overbooked Item Info',
        value: 'overbooked-item',
        content: (
          <OverBookedItems
            serialList={serialList}
            serialLoading={serialLoading}
            setActiveTab={setActiveTab}
          />
        ),
        children: [
          {
            label: 'Item Lookup',
            value: 'item-lookup',
            content: (
              <OverBookedItemLookup
                handleChange={() => setActiveTab('overbooked-item')}
                form={overBookedForm}
              />
            ),
          },
          {
            label: 'Serial #',
            value: 'serial-info',
            content: (
              <SerialInfo
                data={serialData}
                handleChange={() => setActiveTab('overbooked-item')}
              />
            ),
          },
          {
            label: 'Inventory By Location',
            value: 'inventory-by-location',
            content: (
              <InventoryByLocation
                handleChange={() => setActiveTab('overbooked-item')}
              />
            ),
          },
        ],
      },
      {
        label: 'Availability Calendar',
        value: 'availability-calendar',
        content: (
          <AvailabiltyCalendarInfo
            handlechange={() => setActiveTab('availability-item-lookup')}
          />
        ),
        children: [
          {
            label: 'Item Lookup',
            value: 'availability-item-lookup',
            content: (
              <OverBookedItemLookup
                handleChange={() => setActiveTab('availability-calendar')}
                form={availabilityForm}
              />
            ),
          },
        ],
      },
      {
        label: 'Additional Item Info',
        value: 'additional-item-info',
        content: (
          <AppTabsVertical
            tabs={generateAdditionalItemInfoTabs({
              form: additionalItemInfoForm,
              orderItemId: kitItemId
                ? kitItemId
                : Object.keys(rowSelection).at(0),
              onOpenChange,
            })}
            activeTab={activeInfoTab}
            onTabChange={handleTabChange}
            className="px-4 pb-3"
            contentClassName={cn(
              'h-[500px] 2xl:h-[600px] overflow-y-auto p-0 border-none'
            )}
          />
        ),
      },
    ],
  };
};
