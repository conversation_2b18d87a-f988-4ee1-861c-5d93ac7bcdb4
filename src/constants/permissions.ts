import { ROUTES } from './routes-constants';

export enum Permissions {
  Accounting = 'Accounting',
  Customers = 'Customers',
  Items = 'Items',
  Lists = 'Lists',
  Orders = 'Orders',
  Processes = 'Processes',
  PurchaseOrders = 'Purchase Orders',
  Reports = 'Reports',
  SubRentals = 'Sub-Rentals',
  Vendors = 'Vendors',
  Warehouse = 'Warehouse',
  AccessAllOptions = 'Access All options',
  ProcessCreditCards = 'Process Credit Cards',
  CreateDeletePayments = 'Create / Delete Payments',
  OrderEntryReadOnly = 'Order Entry Read only',
  ManageSystem = 'Manage Systems',
}

/** Tenant Admin Only Routes */
export const adminOnlyRoutes = [ROUTES.SYSTEM];
