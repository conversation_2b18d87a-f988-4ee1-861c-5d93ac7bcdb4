import Email from '@/components/modules/orders/item-details/additional-item-info/Email';
import Information from '@/components/modules/orders/item-details/additional-item-info/Information';
import LinkedFilesTab from '@/components/modules/orders/item-details/additional-item-info/LinkedFiles';
import { AdditionalEmailInfoTypes } from '@/types/orders/order-item-details.types';
import { UseFormReturn } from 'react-hook-form';

// Enum for tab values to enforce consistency and avoid hardcoded strings
export enum TabValue {
  INFORMATION = 'information',
  LINKED_FILES = 'linked-files',
  EMAIL = 'email',
}

/**
 * Generates a list of tab items for displaying additional item information
 * @param {object} props - The props for the component
 * @param {UseFormReturn<AdditionalEmailInfoTypes>} props.form - The form methods from react-hook-form
 */
export const generateAdditionalItemInfoTabs = ({
  form,
  orderItemId,
  onOpenChange,
}: {
  form: UseFormReturn<AdditionalEmailInfoTypes>;
  orderItemId?: number | string | null;
  onOpenChange: () => void;
}) => {
  return [
    {
      value: TabValue.INFORMATION,
      label: 'Information',
      content: <Information orderItemId={orderItemId} />,
    },
    {
      value: TabValue.LINKED_FILES,
      label: 'Linked Files',
      content: <LinkedFilesTab orderItemId={orderItemId} />,
    },
    {
      value: TabValue.EMAIL,
      label: 'Email',
      content: (
        <Email
          form={form}
          orderItemId={orderItemId}
          onOpenChange={onOpenChange}
        />
      ),
    },
  ];
};
