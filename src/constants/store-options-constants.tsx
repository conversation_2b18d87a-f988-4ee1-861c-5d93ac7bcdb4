import SuspenseWrapper from '@/routes/SuspenseWrapper';
import { lazy } from 'react';

// Lazy load all components
const CopyingOrders = lazy(
  () => import('@/components/modules/system/store/CopyingOrders')
);
const DateCalculation = lazy(
  () => import('@/components/modules/system/store/DateCalculation')
);
const DeliveryCharges = lazy(
  () => import('@/components/modules/system/store/DeliveryCharges')
);
const EmailSettings = lazy(
  () => import('@/components/modules/system/store/EmailSettings')
);
const Forms = lazy(() => import('@/components/modules/system/store/Forms'));
const ManagerApproval = lazy(
  () => import('@/components/modules/system/store/ManagerApproval')
);
const MiscCustomerSetting = lazy(
  () => import('@/components/modules/system/store/MiscCustomerSetting')
);
const MiscOrderSetting = lazy(
  () => import('@/components/modules/system/store/MiscOrderSetting')
);
const MiscSettings = lazy(
  () => import('@/components/modules/system/store/MiscSettings')
);
const MultiLocation = lazy(
  () => import('@/components/modules/system/store/MultiLocation')
);
const OrderItemSettings = lazy(
  () => import('@/components/modules/system/store/OrderItemSettings')
);
const Passwords = lazy(
  () => import('@/components/modules/system/store/Passwords')
);
const Payments = lazy(
  () => import('@/components/modules/system/store/Payments')
);
const PrintSettings = lazy(
  () => import('@/components/modules/system/store/PrintSettings')
);
const RFIDSettings = lazy(
  () => import('@/components/modules/system/store/RFIDSettings')
);
const Totals = lazy(() => import('@/components/modules/system/store/Totals'));
const Warehouse = lazy(
  () => import('@/components/modules/system/store/Warehouse')
);
const InformationSystem = lazy(
  () => import('@/components/modules/system/store/InformationSystem')
);

// Updated storeTabList with lazy loading for all components
export const storeTabList = [
  {
    value: 'information',
    label: 'Information',
    content: (
      <SuspenseWrapper>
        <InformationSystem />
      </SuspenseWrapper>
    ),
  },
  {
    value: 'copying-orders',
    label: 'Copying Orders',
    content: (
      <SuspenseWrapper>
        <CopyingOrders />
      </SuspenseWrapper>
    ),
  },
  {
    value: 'date-calculation',
    label: 'Date Calculation',
    content: (
      <SuspenseWrapper>
        <DateCalculation />
      </SuspenseWrapper>
    ),
  },
  {
    value: 'delivery-charges',
    label: 'Delivery Charges',
    content: (
      <SuspenseWrapper>
        <DeliveryCharges />
      </SuspenseWrapper>
    ),
  },
  {
    value: 'email-settings',
    label: 'E-mail Settings',
    content: <EmailSettings />,
  },
  {
    value: 'forms',
    label: 'Forms',
    content: (
      <SuspenseWrapper>
        <Forms />
      </SuspenseWrapper>
    ),
  },

  {
    value: 'manager-approval',
    label: 'Manager Approval',
    content: (
      <SuspenseWrapper>
        <ManagerApproval />
      </SuspenseWrapper>
    ),
  },
  {
    value: 'misc-customer-settings',
    label: 'Misc Customer Settings',
    content: <MiscCustomerSetting />,
  },
  {
    value: 'misc-order-settings',
    label: 'Misc Order Settings',
    content: <MiscOrderSetting />,
  },
  {
    value: 'misc-settings',
    label: 'Misc Settings',
    content: <MiscSettings />,
  },
  {
    value: 'multi-location',
    label: 'Multi-Location',
    content: <MultiLocation />,
  },
  {
    value: 'order-item-settings',
    label: 'Order Item Settings',
    content: (
      <SuspenseWrapper>
        <OrderItemSettings />
      </SuspenseWrapper>
    ),
  },
  {
    value: 'passwords',
    label: 'Passwords',
    content: (
      <SuspenseWrapper>
        <Passwords />
      </SuspenseWrapper>
    ),
  },
  {
    value: 'payments',
    label: 'Payments',
    content: (
      <SuspenseWrapper>
        <Payments />
      </SuspenseWrapper>
    ),
  },
  {
    value: 'print-settings',
    label: 'Print Settings',
    content: (
      <SuspenseWrapper>
        <PrintSettings />
      </SuspenseWrapper>
    ),
  },
  {
    value: 'rfid',
    label: 'RFID',
    content: (
      <SuspenseWrapper>
        <RFIDSettings />
      </SuspenseWrapper>
    ),
  },
  {
    value: 'totals',
    label: 'Totals',
    content: (
      <SuspenseWrapper>
        <Totals />
      </SuspenseWrapper>
    ),
  },
  {
    value: 'warehouse',
    label: 'Warehouse',
    content: (
      <SuspenseWrapper>
        <Warehouse />
      </SuspenseWrapper>
    ),
  },
];
