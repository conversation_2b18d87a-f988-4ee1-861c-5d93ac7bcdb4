import CircleDollarSignIcon from '@/assets/icons/CircleDollarSignIcon';
import ListTreeIcon from '@/assets/icons/ListTreeIcon';
import OrdersIcon from '@/assets/icons/OrdersIcon';
// import PurchaseOrderIcon from '@/assets/icons/PurchaseOrderIcon';
import ProcessesIcon from '@/assets/icons/ProcessesIcon';
import PurchaseOrderIcon from '@/assets/icons/PurchaseOrderIcon';
// import ReportsIcon from '@/assets/icons/ReportsIcon';
import SubRentalsIcon from '@/assets/icons/SubRentalsIcon';
import SystemIcon from '@/assets/icons/SystemIcon';
import UsersRoundIcon from '@/assets/icons/UsersRoundIcon';
// import VendorsIcon from '@/assets/icons/VendorsIcon';
import WareHouseIcon from '@/assets/icons/WareHouseIcon';
import ClientsIcon from '@/assets/icons/Clients';
import HomeIcon from '@/assets/icons/HomeIcon';
import LayoutGridIcon from '@/assets/icons/LayoutGridIcon';
import SystemUsersIcon from '@/assets/icons/SystemUsersIcon';
import VendorsIcon from '@/assets/icons/VendorsIcon';
import { convertToFloat, formatPhoneNumber } from '@/lib/utils';
import { RolesEnum } from '@/types/common.types';
import { Permissions } from './permissions';
import { ROUTES } from './routes-constants';
import { Banknote, BookOpenText, CreditCard, FileBadge } from 'lucide-react';

export const superAdminMenu = [
  // { icon: HomeIcon, label: 'Dashborad', to: ROUTES.HOME },
  {
    icon: ClientsIcon,
    label: 'Clients',
    to: ROUTES.ACCOUNTS,
    permission: '',
  },
  {
    icon: UsersRoundIcon,
    label: 'Invites',
    to: ROUTES.CLIENT_INVITE,
    permission: '',
  },
];

export const userMenu = [
  { icon: HomeIcon, label: 'Home', to: ROUTES.HOME, permission: '' },
];

export const tenantMenu = [
  {
    icon: HomeIcon,
    label: 'Home',
    to: ROUTES.ADMIN,
    permission: '',
  },
  {
    icon: CircleDollarSignIcon,
    label: 'Accounting',
    to: ROUTES.ADJUSTMENTS,
    permission: Permissions.Accounting,
    children: [
      {
        label: 'Adjustments',
        to: ROUTES.ADJUSTMENTS,
        permission: Permissions.Accounting,
        icon: FileBadge,
        iconClassName: 'w-5 h-5 mr-0',
      },
      {
        label: 'Inquiry',
        to: ROUTES.INQUIRY,
        permission: Permissions.Accounting,
        icon: BookOpenText,
        iconClassName: 'w-5 h-5 mr-0',
      },
      {
        label: 'Payments',
        to: ROUTES.ACCOUNTING_CUSTOMERS,
        permission: Permissions.CreateDeletePayments,
        icon: Banknote,
        iconClassName: 'w-5 h-5 mr-0',
      },
      {
        label: 'Process Credit Cards',
        to: ROUTES.PROCESS_CREDIT_CARDS,
        permission: Permissions.ProcessCreditCards,
        icon: CreditCard,
        iconClassName: 'w-5 h-5 mr-0',
      },
    ],
  },
  {
    icon: UsersRoundIcon,
    label: 'Customers',
    to: ROUTES.CUSTOMERS,
    permission: Permissions.Customers,
  },
  {
    icon: LayoutGridIcon,
    label: 'Items',
    to: ROUTES.ITEMS,
    permission: Permissions.Items,
  },
  {
    icon: ListTreeIcon,
    label: 'Lists',
    to: ROUTES.LISTS,
    permission: Permissions.Lists,
    children: [
      {
        label: 'Bank Accounts',
        to: ROUTES.BANK_ACCOUNTS,
        permission: Permissions.Lists,
      },
      {
        label: 'Categories',
        to: ROUTES.CATEGORIES,
        permission: Permissions.Lists,
      },
      {
        label: 'Checklist Items',
        to: ROUTES.CHECKLIST_ITEMS,
        permission: Permissions.Lists,
      },
      {
        label: 'Customer Types',
        to: ROUTES.CUSTOMER_TYPES,
        permission: Permissions.Lists,
      },
      {
        label: 'Delivery Charges',
        to: ROUTES.DELIVERY_CHARGES,
        permission: Permissions.Lists,
      },
      {
        label: 'Delivery Locations',
        to: ROUTES.DELIVERY_LOCATION,
        permission: Permissions.Lists,
      },
      {
        label: 'Delivery Types',
        to: ROUTES.DELIVERY_TYPES,
        permission: Permissions.Lists,
      },
      {
        label: 'Departments',
        to: ROUTES.DEPARTMENTS,
        permission: Permissions.Lists,
      },
      { label: 'Drivers', to: ROUTES.DRIVERS, permission: Permissions.Lists },
      {
        label: 'E-sign Fields',
        to: ROUTES.E_SIGN_FIELDS,
        permission: Permissions.Lists,
      },
      {
        label: 'Employees',
        to: ROUTES.EMPLOYEES,
        permission: Permissions.Lists,
      },
      {
        label: 'Equipment Types',
        to: ROUTES.EQUIPMENT_TYPES,
        permission: Permissions.Lists,
      },
      {
        label: 'Event Types',
        to: ROUTES.EVENT_TYPES,
        permission: Permissions.Lists,
      },
      {
        label: 'Local Zip Codes',
        to: ROUTES.LOCAL_ZIP_CODES,
        permission: Permissions.Lists,
      },
      {
        label: 'Packing List Departments',
        to: ROUTES.PACKING_LIST_DEPARTMENTS,
        permission: Permissions.Lists,
      },
      {
        label: 'Payment Terms',
        to: ROUTES.PAYMENT_TERMS,
        permission: Permissions.Lists,
      },
      {
        label: 'Payment Types',
        to: ROUTES.PAYMENT_TYPES,
        permission: Permissions.Lists,
      },
      {
        label: 'Quality Types',
        to: ROUTES.QUALITY_TYPES,
        permission: Permissions.Lists,
      },
      {
        label: 'Referral Types',
        to: ROUTES.REFERRAL_TYPES,
        permission: Permissions.Lists,
      },
      {
        label: 'Sales Tax Codes',
        to: ROUTES.SALES_TAX_CODE,
        permission: Permissions.Lists,
      },
      {
        label: 'Setup/Takedown',
        to: ROUTES.SETUP_TAKEDOWN,
        permission: Permissions.Lists,
      },
      {
        label: 'Shipping Companies',
        to: ROUTES.SHIPPING_COMPANIES,
        permission: Permissions.Lists,
      },
      {
        label: 'Surge Rates',
        to: ROUTES.SURGE_RATES,
        permission: Permissions.Lists,
      },
      { label: 'Trucks', to: ROUTES.TRUCKS, permission: Permissions.Lists },
      {
        label: 'Truck Equipment List',
        to: ROUTES.TRUCK_EQUIPMENT_LIST,
        permission: Permissions.Lists,
      },
    ],
  },
  {
    icon: OrdersIcon,
    label: 'Orders',
    to: ROUTES.ORDERS,
    permission: Permissions.Orders,
  },
  {
    icon: ProcessesIcon,
    label: 'Processes',
    to: ROUTES.PROCESSES,
    permission: Permissions.Processes,
  },
  {
    icon: PurchaseOrderIcon,
    label: 'Purchase Orders',
    to: ROUTES.PURCHASE_ORDERS,
    permission: Permissions.PurchaseOrders,
  },
  // {
  //   icon: ReportsIcon,
  //   label: 'Reports',
  //   to: ROUTES.REPORTS,
  //   permission: Permissions.Reports,
  // },
  {
    icon: SubRentalsIcon,
    label: 'Sub Rentals',
    to: ROUTES.SUB_RENTALS,
    permission: Permissions.SubRentals,
  },
  {
    icon: SystemIcon,
    label: 'System',
    to: ROUTES.SYSTEM_USERS,
    permission: Permissions.ManageSystem,
    children: [
      {
        icon: SystemUsersIcon,
        label: 'Users',
        to: ROUTES.SYSTEM_USERS,
        permission: Permissions.ManageSystem,
      },
    ],
  },
  {
    icon: VendorsIcon,
    label: 'Vendors',
    to: ROUTES.VENDORS,
    permission: Permissions.Vendors,
  },
  {
    icon: WareHouseIcon,
    label: 'Warehouse',
    to: ROUTES.WAREHOUSE,
    permission: Permissions.Warehouse,
    children: [
      {
        label: 'Customer Pickups & Returns',
        to: ROUTES.WAREHOUSE_CUSTOM_PICKUP_RETURN,
        permission: Permissions.Warehouse,
      },
      // {
      //   label: 'Order Deliveries & Pickups',
      //   to: ROUTES.WAREHOUSE_ORDER_DELIVERIES_PICKUPS,
      //   permission: Permissions.Warehouse,
      // },
      // {
      //   label: 'Subrental Pickups & Returns',
      //   to: ROUTES.WAREHOUSE_SUBRENTAL_PICKUPS_RETURNS,
      //   permission: Permissions.Warehouse,
      // },
      // {
      //   label: 'Truck Itineraries',
      //   to: ROUTES.WAREHOUSE_TRUCK_ITINERARIES,
      //   permission: Permissions.Warehouse,
      // },
    ],
  },
];

export const statusList = [
  {
    label: 'Active',
    value: 'true',
  },
  {
    label: 'Inactive',
    value: 'false',
  },
];

export const statusListWithAll = [
  {
    label: 'All',
    value: 'all',
  },
  ...statusList,
];

export const ROLES = {
  SUPER_ADMIN: RolesEnum.SUPER_ADMIN,
  ADMIN: RolesEnum.ADMIN,
  NORMAL_USER: RolesEnum.NORMAL_USER,
};

export const defaultDataTablePageSize = 10;
export const RowPerPage = [10, 20, 30, 50, 100, 500];
export const DEFAULT_MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
export const DEFAULT_ACCEPTED_TYPES = ['*/*'];

// List of available Bulk Edit Items - Single cost /Update all costs
export const BulkEditItemsCost = [
  {
    label: 'Single Cost Adjustment Record',
    value: 'single',
  },
  { label: 'Update All Costs', value: 'all' },
];

// Customer Search lookup constants types
export const CustomerSearchType = [
  { label: 'Customer', value: 'name' },
  { label: 'Phone', value: 'tel1' },
];
// Status Colours
export const StatusColors: Record<string, string> = {
  Ordered: 'bg-blue-200 text-blue-800',
  Deleted: 'bg-red-200 text-red-800',
  paid: 'bg-yellow-200 text-yellow-800',
  Received: 'bg-green-200 text-green-800',
};

// Vendor Search lookup constants types
export const VendorSearchType = [
  { label: 'Vendor', value: 'name' },
  { label: 'Phone', value: 'tel1' },
];

//  Enum representing internal operator types used in filters
export enum OperatorTypeValue {
  CONTAINS = 'Contains',
  EQUALS = 'Equals',
  STARTS_WITH = 'Startswith',
}
// Dropdown options for operator types used in the UI.
export const OperatorTypeOptions = [
  { value: OperatorTypeValue.CONTAINS, label: 'Contains' },
  { value: OperatorTypeValue.EQUALS, label: 'Equals' },
  { value: OperatorTypeValue.STARTS_WITH, label: 'Starts With' },
];

// Enum representing comparison operator types used in filtering.

export enum ComparisonOperator {
  EQUALS = 'equals',
  GREATER_THAN = 'greaterthan',
  LESS_THAN = 'lessthan',
  STARTS_WITH = 'startswith',
}

// Dropdown options for comparison operators used in the UI.
// The label is formatted for display, while the value uses the enum for consistency.
export const ComparisonOperatorOptions = [
  { value: ComparisonOperator.EQUALS, label: 'Equals' },
  { value: ComparisonOperator.GREATER_THAN, label: 'Greater Than' },
  { value: ComparisonOperator.LESS_THAN, label: 'Less Than' },
];

// componentMap for the base on the key it sould map the componetn
export const componentMap: any = {
  PhoneWidget: ({ value }: any) => formatPhoneNumber(value),
  LabelDollar: ({ value }: any) => convertToFloat({ value, prefix: '$' }),
};
