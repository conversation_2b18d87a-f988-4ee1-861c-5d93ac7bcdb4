import InformationSystem from '@/components/modules/items/item-details/InformationSystem';
import Inventory from '@/components/modules/items/item-details/inventory/Inventory';
import LinkedFilesTab from '@/components/modules/items/item-details/linked-files/LinkedFiles';
import Options from '@/components/modules/items/item-details/Options';
import Quantity from '@/components/modules/items/item-details/quantity/Quantity';
import { InventoryListTypes } from '@/types/item.types';
import {
  FileArchive,
  FileIcon,
  FileImage,
  FileSpreadsheet,
  FileText,
  FileVideo,
} from 'lucide-react';
import { UseFormReturn } from 'react-hook-form';

type TabItem = {
  value: string;
  label: string;
  content: React.ReactNode;
};

interface ItemTabListType {
  inventoryForm: UseFormReturn<InventoryListTypes>;
  onDefaultValuesLoaded?: (defaults: InventoryListTypes) => void;
}

/**
 * Configuration for the tab navigation in item details
 * Each tab contains a value (for routing/identification),
 * label (for display), and content component
 */
export const itemTabList = ({
  inventoryForm,
  onDefaultValuesLoaded,
}: ItemTabListType): TabItem[] => [
  {
    value: 'information',
    label: 'Information',
    content: <InformationSystem />,
  },
  {
    value: 'linked-files',
    label: 'Linked Files',
    content: <LinkedFilesTab />,
  },
  {
    value: 'options',
    label: 'Options',
    content: <Options />,
  },
  {
    value: 'inventory',
    label: 'Inventory',
    content: (
      <Inventory
        form={inventoryForm}
        onDefaultValuesLoaded={onDefaultValuesLoaded}
      />
    ),
  },
  {
    value: 'quantity',
    label: 'Quantity',
    content: <Quantity />,
  },
];

/**
 * List of options for status
 */
export const statusList = [
  { label: 'Active Item', value: 'true' },
  { label: 'Inactive Item', value: 'false' },
];

/**
 * Maximum file size allowed for uploads (5MB)
 */
export const MAX_FILE_SIZE = 5 * 1024 * 1024;

/**
 * List of accepted MIME types for file uploads
 * Includes various image formats, documents, and spreadsheets
 */
export const ACCEPTED_FILE_TYPES = [
  // Image formats
  'image/jpeg',
  'image/png',
  'image/gif',
  'image/heic',
  'image/heif',

  // Document formats
  'application/pdf',
  'application/msword', // .doc
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
  'application/vnd.oasis.opendocument.text',
  'text/plain',

  // Spreadsheet formats
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'text/csv',
  'application/vnd.oasis.opendocument.spreadsheet',

  // Generic office formats
  'application/vnd.ms-office',
  'application/vnd.ms-excel.sheet.macroenabled.12', // .xlsm

  // Compressed files
  'application/zip', // .zip
  'application/x-rar-compressed', // .rar
  'application/x-7z-compressed', // .7z
];

/**
 * Configuration for different file types and their associated icons
 * Each type contains supported extensions and the corresponding icon component
 */
const FILE_TYPES = {
  document: {
    extensions: ['pdf', 'doc', 'docx', 'txt'],
    icon: <FileText className="w-6 h-6" />,
  },
  image: {
    extensions: ['jpg', 'jpeg', 'png', 'svg'],
    icon: <FileImage className="w-6 h-6" />,
  },
  spreadsheet: {
    extensions: ['xlsx', 'xls', 'csv'],
    icon: <FileSpreadsheet className="w-6 h-6" />,
  },
  zip: {
    extensions: ['zip', '7z', 'rar'],
    icon: <FileArchive className="w-6 h-6" />,
  },
  video: {
    extensions: ['mp4', 'avi', 'mov'],
    icon: <FileVideo className="w-6 h-6" />,
  },
};

/**
 * Returns the appropriate icon component based on the file extension
 * @param filename - The full filename including extension
 * @returns React component of the corresponding file type icon
 */
export const getFileIcon = (filename: string): React.ReactNode => {
  // Extract file extension and convert to lowercase
  const extension = filename.split('.').pop()?.toLowerCase() || '';

  // Find matching file type configuration
  const fileType = Object.values(FILE_TYPES).find((type) =>
    type.extensions.includes(extension)
  );

  // Return default file icon if no matching type is found
  if (!fileType) {
    return <FileIcon className="w-6 h-6" />;
  }

  return fileType.icon;
};
