// import ItemList from '@/components/modules/orders/item-details/item-list';
import {
  AdditionalInfoTypes,
  ItemListTypes,
  OrderInformationTypes,
  QuickDiscounts,
  SalesReferralsType,
  STATUS_INFO_TAB,
  StatusTypes,
  StoreSettings,
  TotalPaymentsTypes,
} from '@/types/order.types';
import {
  Box,
  FileArchive,
  FileIcon,
  FileImage,
  FileSpreadsheet,
  FileText,
  FileVideo,
  PhoneCall,
} from 'lucide-react';
// import InformationSystem from '@/components/modules/orders/InformationSystem';
// import RfidTags from '@/components/modules/orders/RfidTags';
import { lazy } from 'react';
import { UseFormReturn } from 'react-hook-form';
import AdditionalInfo from '@/components/modules/orders/additional-info/AdditionalInfo';

const WorkInProgress = lazy(
  () => import('@/components/common/app-work-progress')
);
const ItemList = lazy(
  () => import('@/components/modules/orders/item-details/item-list')
);
const SalesReferrals = lazy(
  () => import('@/components/modules/orders/sales-referrals/SalesReferrals')
);
const DeliveryStatus = lazy(
  () => import('@/components/modules/orders/status/DeliveryStatus')
);
const PickupStatus = lazy(
  () => import('@/components/modules/orders/status/PickupStatus')
);
const OrderStatus = lazy(
  () => import('@/components/modules/orders/status/Status')
);
const TotalPayments = lazy(
  () => import('@/components/modules/orders/TotalPayments')
);
const OrderInformation = lazy(
  () => import('@/components/modules/orders/information')
);

type TabOrder = {
  value: string;
  label: string;
  content: React.ReactNode;
};

interface OrderTabListType {
  itemDetailsForm: UseFormReturn<ItemListTypes>;
  totalPaymentsForm: UseFormReturn<TotalPaymentsTypes>;
  onDefaultValuesLoaded?: (defaults: ItemListTypes) => void;
  orderStatusForm: UseFormReturn<StatusTypes>;
  orderAdditionalInfoForm: UseFormReturn<AdditionalInfoTypes>;
  orderSalesReferralsForm: UseFormReturn<SalesReferralsType>;
  userDefaultStoreInfo: StoreSettings;
  shippingManager: boolean;
  orderInformationForm: UseFormReturn<OrderInformationTypes>;
}

/**
 * Configuration for the tab navigation in Order details
 * Each tab contains a value (for routing/identification),
 * label (for display), and content component
 */
export const orderTabList = ({
  itemDetailsForm,
  onDefaultValuesLoaded,
  totalPaymentsForm,
  orderStatusForm,
  orderAdditionalInfoForm,
  orderSalesReferralsForm,
  userDefaultStoreInfo,
  shippingManager,
  orderInformationForm,
}: OrderTabListType): TabOrder[] => [
  {
    value: 'information',
    label: 'Information',
    content: <OrderInformation form={orderInformationForm} />,
  },
  {
    value: 'item-details',
    label: 'Item Details',
    content: (
      <ItemList
        form={itemDetailsForm}
        onDefaultValuesLoaded={onDefaultValuesLoaded}
      />
    ),
  },
  {
    value: 'total-payments',
    label: 'Total / Payments',
    content: <TotalPayments form={totalPaymentsForm} />,
  },
  {
    value: 'status',
    label: 'Status',
    content: <OrderStatus form={orderStatusForm} />,
  },
  {
    value: 'additional-info',
    label: 'Additional Info',
    content: (
      // <WorkInProgress />
      <AdditionalInfo
        form={orderAdditionalInfoForm}
        shippingManager={shippingManager}
      />
    ),
  },
  {
    value: 'sales-referrals',
    label: 'Sales Referrals',
    content: (
      <SalesReferrals
        form={orderSalesReferralsForm}
        userDefaultStoreInfo={userDefaultStoreInfo}
      />
    ),
  },
  {
    value: 'rfid-tags',
    label: 'RFID Tags',
    content: <WorkInProgress />,
  },
];

// Orders Type Tabs Constants
// export const ordersTypeTablist = [
//   {
//     value: ORDERT_TYPE_TAB.DELIVER,
//     label: (
//       <div className="flex items-center gap-x-2">
//         <Box className="h-4" />
//         <span>Deliver</span>
//       </div>
//     ),
//     content: <Deliver />,
//   },
//   {
//     value: ORDERT_TYPE_TAB.SHIP,
//     label: (
//       <div className="flex items-center gap-x-2">
//         <Truck className="h-4" />
//         <span>Ship</span>
//       </div>
//     ),
//     content: <Ship />,
//   },
//   {
//     value: ORDERT_TYPE_TAB.WILL_CALL,
//     label: (
//       <div className="flex items-center gap-x-2">
//         <PhoneCall className="h-4" />
//         <span>Will Call</span>
//       </div>
//     ),
//     content: <WillCall />,
//   },
// ];

// Status Info Tabs Constants
export const statusInfoTabList = (form: UseFormReturn<StatusTypes>) => [
  {
    value: STATUS_INFO_TAB.DELIVER,
    label: (
      <div className="flex items-center gap-x-2">
        <Box className="h-4" />
        <span>Delivery Status Info</span>
      </div>
    ),
    content: <DeliveryStatus form={form} />,
  },
  {
    value: STATUS_INFO_TAB.PICKUP,
    label: (
      <div className="flex items-center gap-x-2">
        <PhoneCall className="h-4" />
        <span>Pickup Status Info</span>
      </div>
    ),
    content: <PickupStatus form={form} />,
  },
];

/**
 * List of options for status
 */
export const statusList = [
  { label: 'Active', value: 'true' },
  { label: 'Inactive', value: 'false' },
];

/**
 * Maximum file size allowed for uploads (5MB)
 */
export const MAX_FILE_SIZE = 5 * 1024 * 1024;

/**
 * List of accepted MIME types for file uploads
 * Includes various image formats, documents, and spreadsheets
 */
export const ACCEPTED_FILE_TYPES = [
  // Image formats
  'image/jpeg',
  'image/png',
  'image/gif',
  'image/heic',
  'image/heif',

  // Document formats
  'application/pdf',
  'application/msword', // .doc
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
  'application/vnd.oasis.opendocument.text',
  'text/plain',

  // Spreadsheet formats
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'text/csv',
  'application/vnd.oasis.opendocument.spreadsheet',

  // Generic office formats
  'application/vnd.ms-office',
  'application/vnd.ms-excel.sheet.macroenabled.12', // .xlsm

  // Compressed files
  'application/zip', // .zip
  'application/x-rar-compressed', // .rar
  'application/x-7z-compressed', // .7z
];

/**
 * Configuration for different file types and their associated icons
 * Each type contains supported extensions and the corresponding icon component
 */
const FILE_TYPES = {
  document: {
    extensions: ['pdf', 'doc', 'docx', 'txt'],
    icon: <FileText className="w-6 h-6" />,
  },
  image: {
    extensions: ['jpg', 'jpeg', 'png', 'svg'],
    icon: <FileImage className="w-6 h-6" />,
  },
  spreadsheet: {
    extensions: ['xlsx', 'xls', 'csv'],
    icon: <FileSpreadsheet className="w-6 h-6" />,
  },
  zip: {
    extensions: ['zip', '7z', 'rar'],
    icon: <FileArchive className="w-6 h-6" />,
  },
  video: {
    extensions: ['mp4', 'avi', 'mov'],
    icon: <FileVideo className="w-6 h-6" />,
  },
};

/**
 * Returns the appropriate icon component based on the file extension
 * @param filename - The full filename including extension
 * @returns React component of the corresponding file type icon
 */
export const getFileIcon = (filename: string): React.ReactNode => {
  // Extract file extension and convert to lowercase
  const extension = filename.split('.').pop()?.toLowerCase() || '';

  // Find matching file type configuration
  const fileType = Object.values(FILE_TYPES).find((type) =>
    type.extensions.includes(extension)
  );

  // Return default file icon if no matching type is found
  if (!fileType) {
    return <FileIcon className="w-6 h-6" />;
  }

  return fileType.icon;
};

export const typeColors: Record<string, string> = {
  'Rental Order': 'bg-[#CBEDD5]',
  'Sales Order': 'bg-[#BFE7FA]',
  'Rental Quote': 'bg-[#F5FBF7]',
  'Sales Quote': 'bg-[#F2FAFE]',
};
// Edit Quick Discount
export const QuickDiscountsMethods = [
  { label: '0%', value: QuickDiscounts.Zero },
  { label: '5%', value: QuickDiscounts.Five },
  { label: '10%', value: QuickDiscounts.Ten },
  { label: '15%', value: QuickDiscounts.Fifteen },
  { label: '20%', value: QuickDiscounts.Twenty },
  { label: '25%', value: QuickDiscounts.TwentyFive },
  { label: '50%', value: QuickDiscounts.Fifty },
  { label: '100%', value: QuickDiscounts.Hundred },
];

// List of options for selecting discount percentages or states
export const discountOptions = [
  {
    label: 'Off',
    value: 'off',
  },
  {
    label: 'A-10%',
    value: 'A-10',
  },
  {
    label: 'B-20%',
    value: 'B-20',
  },
  {
    label: 'C-30%',
    value: 'C-30',
  },
];

// Status order inventory quality
export const inventoryQualityList = [
  {
    label: 'Excellent',
    value: 'excellent',
  },
  {
    label: 'Good',
    value: 'good',
  },
  {
    label: 'Fair',
    value: 'fair',
  },
];

type AvailableInventoryItemsTypes = {
  label: string;
  value: string;
  group: 'ITEM' | 'CATEGORY';
  key:
    | 'availInvForLocation'
    | 'availInvForAllLocation'
    | 'allInvForTheLocation'
    | 'allInvForAllLocation';
};

export const availableInventoryItems: AvailableInventoryItemsTypes[] = [
  {
    label: 'Available inventory from this location',
    value: '1',
    group: 'ITEM',
    key: 'availInvForLocation',
  },
  {
    label: 'Available inventory from this location',
    value: '2',
    group: 'CATEGORY',
    key: 'availInvForLocation',
  },
  {
    label: 'Available inventory from all locations',
    value: '3',
    group: 'ITEM',
    key: 'availInvForAllLocation',
  },
  {
    label: 'Available inventory from all locations',
    value: '4',
    group: 'CATEGORY',
    key: 'availInvForAllLocation',
  },

  {
    label: 'All inventory from this location',
    value: '5',
    group: 'ITEM',
    key: 'allInvForTheLocation',
  },
  {
    label: 'All inventory from this location',
    value: '6',
    group: 'CATEGORY',
    key: 'allInvForTheLocation',
  },
  {
    label: 'All inventory from all locations',
    value: '7',
    group: 'ITEM',
    key: 'allInvForAllLocation',
  },
  {
    label: 'All inventory from all locations',
    value: '8',
    group: 'CATEGORY',
    key: 'allInvForAllLocation',
  },
];
// Yes no
export const selectYesNo = [
  { label: 'Yes', value: 'true' },
  { label: 'No', value: 'false' },
];

// Departments in Crew Details
export const departmentOptions = [
  { label: 'Tenting', value: '1' },
  { label: 'Furnishings', value: '2' },
  { label: 'Carpenter', value: '3' },
  { label: 'Sewing', value: '4' },
];

// Shipping Company
export enum SHIP_INFO_TYPE {
  RETURN = 'RETURN',
  SHIPPING = 'SHIPPING',
}

// Record Online Payments
export const recordOnlinePayments = [
  {
    label: 'All',
    value: 'all',
  },
  {
    label: 'Past 7 Days',
    value: 'past7Days',
  },
  {
    label: 'Past 30 Days',
    value: 'past30Days',
  },
  {
    label: 'Past 60 Days',
    value: 'past60Days',
  },
  {
    label: 'Past 90 Days',
    value: 'past90Days',
  },
];

// Status Online Payments
export const statusOnlinePayments = [
  {
    label: 'Order #',
    value: 'orderId',
  },
  {
    label: 'Customer',
    value: 'customer',
  },
  {
    label: 'Salesperson',
    value: 'salesperson',
  },
  {
    label: 'Amount',
    value: 'amount',
  },
  {
    label: 'Status',
    value: 'status',
  },
];

// Operators constant for order # and amount
export const matchOrderAmount = [
  {
    label: 'Equals',
    value: 'equals',
  },
  {
    label: 'Greater Than',
    value: 'GreaterThan',
  },
  {
    label: 'Less Than',
    value: 'LessThan',
  },
];

// Operators constant for customers, sales person, and status
export const matchCustomerSalesStatus = [
  {
    label: 'Equals',
    value: 'equals',
  },
  {
    label: 'Contains',
    value: 'contains',
  },
  {
    label: 'Starts With',
    value: 'startswith',
  },
];

// Payment Action Online Payments
export const paymentActionOnlinePayments = [
  {
    label: 'All',
    value: 'all',
  },
  {
    label: 'Waiting For Payment',
    value: 'waitingForPayment',
  },
  {
    label: 'Paid',
    value: 'paid',
  },
  {
    label: 'Paid via Pay Now',
    value: 'paidviaPayNow',
  },
  {
    label: 'Cancelled',
    value: 'cancelled',
  },
];

export enum PAYMENT_BRED_CRUM_DETAILS {
  PROCESS_WITH_CREDIT_CARD = 'process-with-credit-card',
  PROCESS_WITH_REFUND = 'process-with-refund',
  EXTENDED_PAYMENT_INFO = 'extended-payment-info',
  SAVED_PAYMENTS = 'saved-payments',
  NEW_PAYMENT = 'new-payment',
}

export enum ADDTIONAL_INFO {
  ASSIGN_JOB = 'assign-job',
  ASSIGN_ORDERS = 'assign-orders',
  BREAKDOWN = 'breakdown',
  SHIPPING_INFO = 'shipping-info',
  ADDITIONAL_OPRIONS = 'additional-options',
}
