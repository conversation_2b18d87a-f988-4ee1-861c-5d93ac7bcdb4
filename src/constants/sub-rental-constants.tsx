import Info from '@/components/modules/sub-rental/information/Info';
import RentedForm from '@/components/modules/sub-rental/information/RentedForm';
import ItemDetails from '@/components/modules/sub-rental/item-details/ItemDetails';
import { SubRentalItemDetailsTypes } from '@/types/sub-rentals.types';
import { UseFormReturn } from 'react-hook-form';

export const SubRentalTabList = ({
  itemDetailsForm,
  onDefaultValuesLoaded,
}: {
  itemDetailsForm: UseFormReturn<SubRentalItemDetailsTypes>;
  onDefaultValuesLoaded: (defaults: SubRentalItemDetailsTypes) => void;
}) => [
  {
    value: 'information',
    label: 'Information',
    content: (
      <div className="grid grid-cols-2 gap-6">
        <Info />
        <RentedForm />
      </div>
    ),
  },
  {
    value: 'item-details',
    label: 'Item Details',
    content: (
      <ItemDetails
        form={itemDetailsForm}
        onDefaultValuesLoaded={onDefaultValuesLoaded}
      />
    ),
  },
];

export const filtersInCustomerVendorLookup = [
  { label: 'Customers', value: '1' },
  { label: 'Vendors', value: '2' },
  { label: 'Both', value: '-1' },
];

export interface OpenDialogType {
  state: boolean;
  action: string;
}
