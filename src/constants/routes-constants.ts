export const ROUTES = {
  HOME: '/',
  ACCOUNTING: '/accounting',
  CUSTOMERS: '/customers',
  ITEMS: '/items',
  LISTS: '/lists',
  SUBLISTS: '/lists/:params',
  ORDERS: '/orders',
  PROCESSES: '/processes',
  PURCHASE_ORDERS: '/purchase-orders',
  REPORTS: '/reports',
  SUB_RENTALS: '/sub-rentals',
  SYSTEM: '/system',
  FORGOTPASSWORD: '/forgotPassword',
  TASKS: '/tasks',
  VENDORS: '/vendors',
  WAREHOUSE: '/warehouse',
  ADD_NEW_CUSTOMER: 'add-new-customer',
  EDIT_CUSTOMER: 'edit-customer/:customerId',
  ADD_NEW_ITEM: 'add-item',
  EDIT_ITEM: 'edit-item',
  LOGIN: '/login',
  SIGNUP: '/registration',
  FORGOT_PASSWORD: '/forgot-password',
  SET_PASSWORD: '/set-password',
  RESET_PASSWORD: '/reset-password',
  BANK_ACCOUNTS: '/lists/bank-accounts',
  CATEGORIES: '/lists/categories',
  CHECKLIST_ITEMS: '/lists/checklist-items',
  CUSTOMER_TYPES: '/lists/customer-types',
  DELIVERY_CHARGES: '/lists/delivery-charges',
  DELIVERY_LOCATION: '/lists/delivery-locations',
  ADD_DELIVERY_LOCATION: 'lists/delivery-locations/add-delivery-location',
  EDIT_DELIVERY_LOCATION: 'lists/delivery-locations/edit-delivery-location',
  DELIVERY_TYPES: '/lists/delivery-types',
  DEPARTMENTS: '/lists/departments',
  DRIVERS: '/lists/drivers',
  E_SIGN_FIELDS: '/lists/e-sign-fields',
  EQUIPMENT_TYPES: '/lists/equipment-types',
  EMPLOYEES: '/lists/employees',
  EVENT_TYPES: '/lists/event-types',
  LOCAL_ZIP_CODES: '/lists/local-zip-codes',
  PACKING_LIST_DEPARTMENTS: '/lists/packing-list-departments',
  PAYMENT_TERMS: '/lists/payment-terms',
  PAYMENT_TYPES: '/lists/payment-types',
  QUALITY_TYPES: '/lists/quality-types',
  REFERRAL_TYPES: '/lists/referral-types',
  SALES_TAX_CODE: '/lists/sales-tax-code',
  SETUP_TAKEDOWN: '/lists/setup-takedown',
  SHIPPING_COMPANIES: '/lists/shipping-companies',
  SURGE_RATES: '/lists/surge-rates',
  TRUCKS: '/lists/trucks',
  TRUCK_EQUIPMENT_LIST: '/lists/truck-equipment-list',
  // LIST: 'list/:listname',
  ACCOUNTS: '/super-admin/accounts',
  NEW_ACCOUNTS: '/super-admin/accounts/new-account',
  EDIT_ACCOUNTS: '/super-admin/accounts/edit-account/:clientId',
  CLIENT_INVITE: '/super-admin/client-invite',
  PROFILE_DETAILS: '/profile-details',
  STORE_OPTIONS: '/store',
  ADD_STORE_OPTIONS: 'add-store',
  EDIT_STORE_OPTIONS: 'edit-store',
  USER_OPTIONS: '/user-options',
  NORMAL_USER_OPTIONS: '/user/user-options',
  COMPANY: '/company',
  USER: '/user',
  SUPER_ADMIN: '/super-admin',
  ADMIN: '/home',
  ADJUSTMENTS: '/accounting/adjustments',
  ADD_ADJUSTMENTS: '/accounting/adjustments/add-adjustments',
  EDIT_ADJUSTMENTS: '/accounting/adjustments/edit-adjustments',
  INQUIRY: '/accounting/inquiry',
  // ADD_NEW_INQUIRY: 'add-inquiry',
  // EDIT_INQUIRY: 'edit-inquiry',
  INQUIRY_DETAILS: '/accounting/inquiry/details',
  ACCOUNTING_CUSTOMERS: '/accounting/customers',
  ACCOUNTING_PAYMENTS: '/accounting/customers/payments',
  ACCOUNTING_ADD_PAYMENT: 'add-payment',
  ACCOUNTING_EDIT_PAYMENT: 'edit-payment',
  PROCESS_CREDIT_CARDS: '/accounting/process-credit-cards',
  SYSTEM_USERS: '/system/users',
  ADD_SYSTEM_USERS: '/system/users/add-user',
  EDIT_SYSTEM_USERS: '/system/users/edit-user',
  ADD_ORDERS: '/orders/add-orders',
  EDIT_ORDERS: '/orders/edit-orders',
  INVENTORY_MANAGER: '/orders/inventory-manager',
  ACCESS_DENIED: 'access-denied',
  ADD_NEW_PURCHASE_ORDER: 'add-purchase-order',
  EDIT_PURCHASE_ORDER: 'edit-purchase-order',
  AVAILABILITY_CALENDAR: '/orders/availability-calendar',
  OVERBOOKED_ITEM_INFO: '/orders/overbooked-iten-info',
  ADD_SUB_RENTAL: '/sub-rentals/add-sub-rentals',
  EDIT_SUB_RENTAL: '/sub-rentals/edit-sub-rentals',
  BUSY_CALENDAR: '/orders/busy-calendar',
  E_SIGN_DOCUMENTS: '/orders/e-sign-documents',
  ITEM_INQUIRY: '/orders/item-inquiry',
  ONLINE_PAYMENTS: '/orders/online-payments',
  WAREHOUSE_CUSTOM_PICKUP_RETURN: '/warehouse/custom-pickup-return',
  WAREHOUSE_ORDER_DELIVERIES_PICKUPS: '/warehouse/order-deliveries-pickups',
  WAREHOUSE_SUBRENTAL_PICKUPS_RETURNS: '/warehouse/subrental-pickups-returns',
  WAREHOUSE_TRUCK_ITINERARIES: '/warehouse/truck-itineraries',

  // INVENTORY_MANAGER: '/orders/inventory-manager',
  PROCESSING_PAYMENTS: '/processing-payments/:sendPaymentId',
};
