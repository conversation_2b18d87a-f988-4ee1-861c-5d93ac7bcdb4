import Information from '@/components/modules/purchase-order/tabs/information/Information';
import ItemDetails from '@/components/modules/purchase-order/tabs/item-details/ItemDetails';
import ReceivedItems from '@/components/modules/purchase-order/tabs/received-items/ReceivedItems';
import SuspenseWrapper from '@/routes/SuspenseWrapper';
import {
  POItemDetailsList,
  ReceivedItemsList,
} from '@/types/purchase-order.types';
import { UseFormReturn } from 'react-hook-form';

interface PurchaseOrderTabListType {
  itemDetailsForm: UseFormReturn<POItemDetailsList>;
  receivedForm: UseFormReturn<ReceivedItemsList>;
  onDefaultValuesLoaded?: (defaults: POItemDetailsList) => void;
  onReceivedDefaultValuesLoaded?: (defaults: ReceivedItemsList) => void;
}

/**
 * Configuration for the tab navigation in item details
 * Each tab contains a value (for routing/identification),
 * label (for display), and content component
 */
export const purchaseOrderTabList = ({
  itemDetailsForm,
  receivedForm,
  onDefaultValuesLoaded,
  onReceivedDefaultValuesLoaded,
}: PurchaseOrderTabListType) => {
  return [
    {
      value: 'information',
      label: 'Information',
      content: (
        <SuspenseWrapper>
          <Information />
        </SuspenseWrapper>
      ),
    },
    {
      value: 'item-details',
      label: 'Item Details',
      content: (
        <SuspenseWrapper>
          <ItemDetails
            form={itemDetailsForm}
            onDefaultValuesLoaded={onDefaultValuesLoaded}
          />
        </SuspenseWrapper>
      ),
    },
    {
      value: 'received-items',
      label: 'Received Items',
      content: (
        <SuspenseWrapper>
          <ReceivedItems
            form={receivedForm}
            onReceivedDefaultValuesLoaded={onReceivedDefaultValuesLoaded}
          />
        </SuspenseWrapper>
      ),
    },
  ];
};

/**
 * List of options for status
 */
export const statusList = [
  { label: 'Active Item', value: 'true' },
  { label: 'Inactive Item', value: 'false' },
];

export const FilterList = [
  {
    label: 'All Types',
    value: 'ALL',
  },
  {
    label: 'Purchase Order',
    value: 'PURCHASE_ORDER',
  },
  {
    label: 'Request For Quotation',
    value: 'REQUEST_FOR_QUOTATION',
  },
  {
    label: 'Deleted',
    value: 'DELETED',
  },
];

export const RangeList = [
  {
    label: 'Current/Future',
    value: 'CURRENT_FUTURE',
  },
  {
    label: 'Past 30 Days',
    value: 'PAST_30_DAYS',
  },
  {
    label: 'Past 60 Days',
    value: 'PAST_60_DAYS',
  },
  {
    label: 'Past 90 Days',
    value: 'PAST_90_DAYS',
  },
  {
    label: 'Year-to-date',
    value: 'YEAR_TO_DATE',
  },
  {
    label: 'All',
    value: 'All',
  },
];

export const SearchByList = [
  {
    label: 'PO #',
    value: 'PONO',
  },
  {
    label: 'JOB #',
    value: 'JOBNO',
  },
  {
    label: 'Vendor',
    value: 'VENDOR',
  },
  {
    label: 'Total',
    value: 'TOTAL',
  },
];

export const OperatorListFor1 = [
  {
    label: 'Equals',
    value: 'EQUALS',
  },
  {
    label: 'Contains',
    value: 'CONTAINS',
  },
  {
    label: 'Start With',
    value: 'STARTSWITH',
  },
];

export const OperatorListFor2 = [
  {
    label: 'Equals',
    value: 'EQUALS',
  },
  {
    label: 'Greater Than',
    value: 'GREATERTHAN',
  },
  {
    label: 'Less Than',
    value: 'LESSTHAN',
  },
];

export const POStatusColors: Record<string, string> = {
  ORDERED: 'bg-blue-100 text-blue-800',
  DELETED: 'bg-red-100 text-red-800',
  PAID: 'bg-yellow-100 text-yellow-800',
  RECEIVED: '	bg-green-100 text-green-800',
};

export enum TAB_NAMES {
  INFORMATION = 'information',
  ITEM_DETAILS = 'item-details',
  RECEIVED_ITEMS = 'received-items',
}

export const purchaseOrderTypeList = [
  { label: 'Purchase Order', value: '1' },
  { label: 'Request For Quotation', value: '2' },
];

export const purchaseOrderStatusList = [
  { label: 'Ordered', value: '1' },
  { label: 'Received', value: '2' },
  { label: 'Paid', value: '3' },
  { label: 'Deleted', value: '4' },
];
