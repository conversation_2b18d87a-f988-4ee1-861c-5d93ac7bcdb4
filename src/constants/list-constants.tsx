import AppButton from '@/components/common/app-button';
import StatusBadge from '@/components/common/app-status-badge';
import CustomerLookup from '@/components/common/lookups/customer-lookup';
import CheckboxField from '@/components/forms/checkbox';
import InputField from '@/components/forms/input-field';
import NumberIn<PERSON>Field from '@/components/forms/number-input-field';
import PhoneInputWidget from '@/components/forms/phone-input-mask';
import SelectWidget, { ListType } from '@/components/forms/select';
import Text<PERSON>reaField from '@/components/forms/text-area';
import ZipCodeInput from '@/components/forms/zipcode-input';
import { convertToFloat, formatDate, formatPhoneNumber } from '@/lib/utils';
import { SortingStateType } from '@/types/common.types';
import { CustomerDetailTypes } from '@/types/customer.types';
import {
  DeliveryLocationFormType,
  SETUP_TAKEDOWN_STRING,
} from '@/types/list.types';
import {
  FieldValues,
  Path,
  RegisterOptions,
  UseFormReturn,
} from 'react-hook-form';
import {
  BANK_ACCOUNTS_API_ROUTES,
  CATEGORY_API_ROUTES,
  CHECK_LIST_ITEMS_API_ROUTES,
  CUSTOMER_TYPE_API_ROUTES,
  DELIVERY_CHARGES_API_ROUTES,
  DELIVERY_TYPE_API_ROUTES,
  DEPARTMENT_API_ROUTES,
  DRIVERS_API_ROUTES,
  E_SIGN_FILEDS_API_ROUTES,
  EMPLOYEE_API_ROUTES,
  EQUIPMENT_TYPES_API_ROUTES,
  EVENT_TYPES_API_ROUTES,
  LOCAL_ZIP_CODES_API_ROUTES,
  PACKING_LIST_DEPARTMENTS_API_ROUTES,
  PAYMENT_TERM_API_ROUTES,
  PAYMENT_TYPE_API_ROUTES,
  QUALITY_TYPES_API_ROUTES,
  REFERRAL_TYPE_API_ROUTES,
  SALES_TAX_CODE_API_ROUTES,
  SETUP_TAKEDOWN_API_ROUTES,
  SHIPPING_COMPAINES_API_ROUTES,
  SURGE_RATES_API_ROUTES,
  TRUCKS_API_ROUTES,
  TRUCKS_EQUIPMENTS_API_ROUTES,
} from './api-constants';
import { EMAIL_VALIDATION_RULEOptional } from './auth-constants';
import { TEXT_VALIDATION_RULE } from './validation-constants';

export interface CommonListingSetupTypes {
  pageLabel: string;
  navigatingRoute: string;
  sortDirection?: string;
  manageRoute?: string;
  addRoute?: string;
  editRoute?: string;
  searchFilter?: string;
  sortBy?: string;
  findApi: string;
  deleteApi?: string;
  saveApi?: string;
  get?: string;
  columns: any;
  criteriaName?: string;
  enableRowActions?: boolean;
  isAddButton?: boolean;
  form?: JSX.Element;
  addLabel?: string;
  bindingKey?: string;
  className?: string;
  formLabel?: string;
  isDeleteButton?: boolean;
  defaultSort?: SortingStateType[];
}

const useCommonSetupListingConstants = () => {
  const listingSetupConstants: CommonListingSetupTypes[] = [
    {
      pageLabel: 'Bank Accounts',
      navigatingRoute: 'bank-accounts',
      findApi: BANK_ACCOUNTS_API_ROUTES.ALL,
      deleteApi: BANK_ACCOUNTS_API_ROUTES.DELETE,
      addLabel: 'New Bank Account',
      formLabel: 'Bank Account',
      bindingKey: 'id',
      isAddButton: true,
      className: 'max-w-[30%]',
      defaultSort: [{ id: 'account', desc: true }],
      columns: [
        {
          accessorKey: 'account',
          header: 'Bank Account',
          size: 130,
          enableSorting: true,
        },
      ],
    },
    {
      pageLabel: 'Categories',
      formLabel: 'Category',
      navigatingRoute: 'categories',
      findApi: CATEGORY_API_ROUTES.ALL,
      deleteApi: CATEGORY_API_ROUTES.DELETE,
      addLabel: 'New Category',
      bindingKey: 'id',
      isAddButton: true,
      className: 'max-w-[43%]',
      defaultSort: [{ id: 'catNo', desc: true }],

      columns: [
        {
          accessorKey: 'catNo',
          header: 'Category #',
          size: 130,
          enableSorting: true,
        },
        {
          accessorKey: 'catDesc',
          header: 'Description',
          enableSorting: true,
        },
        {
          accessorKey: 'departmentName',
          header: 'Department',
          enableSorting: true,
        },
        {
          accessorKey: 'isActive',
          enableSorting: true,
          header: 'Status',
          cell: ({ row }: any) => (
            <StatusBadge status={row.original.isActive} />
          ),
        },
      ],
    },
    {
      pageLabel: 'Checklist Items',
      formLabel: 'Checklist Item',
      navigatingRoute: 'checklist-items',
      findApi: CHECK_LIST_ITEMS_API_ROUTES.ALL,
      deleteApi: CHECK_LIST_ITEMS_API_ROUTES.DELETE,
      addLabel: 'New Checklist Item',
      bindingKey: 'id',
      isAddButton: true,
      className: 'max-w-[30%]',
      defaultSort: [{ id: 'seqNo', desc: true }],
      columns: [
        {
          accessorKey: 'seqNo',
          header: 'Seq #',
          size: 130,
          enableSorting: true,
        },
        {
          accessorKey: 'description',
          header: 'Description',
          enableSorting: true,
        },
      ],
    },
    {
      pageLabel: 'Customer Types',
      formLabel: 'Customer Type',
      navigatingRoute: 'customer-types',
      findApi: CUSTOMER_TYPE_API_ROUTES.ALL,
      deleteApi: CUSTOMER_TYPE_API_ROUTES.DELETE,
      addLabel: 'New Customer Type',
      bindingKey: 'custtype_id',
      isAddButton: true,
      className: 'max-w-[30%]',
      defaultSort: [{ id: 'code', desc: true }],

      columns: [
        {
          accessorKey: 'code',
          header: 'Code',
          size: 130,
          enableSorting: true,
        },
        {
          accessorKey: 'description',
          header: 'Customer Type',
          enableSorting: true,
        },
        {
          accessorKey: 'delcharge',
          header: 'Delivery Charge',
          cell: ({ row }: any) => <span> ${row.original.delcharge}</span>,
          enableSorting: true,
        },
        {
          accessorKey: 'isactive',
          enableSorting: true,
          header: 'Status',
          cell: ({ row }: any) => (
            <StatusBadge status={row.original.isactive} />
          ),
        },
      ],
    },
    {
      pageLabel: 'Delivery Charges',
      formLabel: 'Delivery Charge',
      navigatingRoute: 'delivery-charges',
      findApi: DELIVERY_CHARGES_API_ROUTES.ALL,
      deleteApi: DELIVERY_CHARGES_API_ROUTES.DELETE,
      addLabel: 'New Delivery Charge',
      bindingKey: 'id',
      isAddButton: true,
      className: 'max-w-[40%]',
      defaultSort: [{ id: 'town', desc: true }],
      columns: [
        {
          accessorKey: 'town',
          header: 'Town',
          size: 130,
          enableSorting: true,
        },
        {
          accessorKey: 'state',
          header: 'State',
          enableSorting: true,
        },
        {
          accessorKey: 'delCharge',
          header: 'Delivery Charge',
          enableSorting: true,
          cell: ({ row }: any) =>
            convertToFloat({
              value: row?.original?.delCharge,
              prefix: '$',
            }),
        },
        {
          accessorKey: 'salesTaxCode',
          header: 'Default Sales Tax Code',
          enableSorting: true,
        },
      ],
    },
    {
      pageLabel: 'Employees',
      formLabel: 'Employee',
      navigatingRoute: 'employees',
      findApi: EMPLOYEE_API_ROUTES.ALL,
      deleteApi: EMPLOYEE_API_ROUTES.DELETE,
      addLabel: 'New Employee',
      bindingKey: 'id',
      isAddButton: true,
      className: 'max-w-[40%]',
      defaultSort: [{ id: 'name', desc: true }],
      columns: [
        {
          accessorKey: 'name',
          header: 'Employee',
          size: 130,
          enableSorting: true,
        },
        // {
        //   accessorKey: 'employeeNo',
        //   header: 'Employee No',
        //   enableSorting: true,
        // },
        // {
        //   accessorKey: 'rate',
        //   header: 'Rate',
        //   enableSorting: true,
        // },
        // {
        //   accessorKey: 'halfRate',
        //   header: 'Half Rate',
        //   enableSorting: true,
        // },
        // {
        //   accessorKey: 'doubleRate',
        //   header: 'Double Rate',
        //   enableSorting: true,
        // },
      ],
    },
    {
      pageLabel: 'Delivery Types',
      formLabel: 'Delivery Type',
      navigatingRoute: 'delivery-types',
      findApi: DELIVERY_TYPE_API_ROUTES.ALL,
      deleteApi: DELIVERY_TYPE_API_ROUTES.DELETE,
      addLabel: 'New Delivery Type',
      bindingKey: 'deliverytype_id',
      isAddButton: true,
      className: 'max-w-[40%]',
      defaultSort: [{ id: 'name', desc: true }],
      columns: [
        {
          accessorKey: 'name',
          header: 'Delivery Type',
          size: 130,
          enableSorting: true,
        },
        {
          accessorKey: 'description',
          header: 'Description',
          enableSorting: true,
        },
        {
          accessorKey: 'shiptransitdays',
          header: 'Shipping Days in Transit',
          enableSorting: true,
        },
        {
          accessorKey: 'isactive',
          enableSorting: true,
          header: 'Status',
          cell: ({ row }: any) => (
            <StatusBadge status={row?.original?.isactive} />
          ),
        },
      ],
    },
    {
      pageLabel: 'Sales Tax Codes',
      formLabel: 'Sales Tax Code',
      navigatingRoute: 'sales-tax-code',
      findApi: SALES_TAX_CODE_API_ROUTES.ALL,
      deleteApi: SALES_TAX_CODE_API_ROUTES.DELETE,
      addLabel: 'New Sales Tax Code',
      bindingKey: 'salestaxcode_id',
      isAddButton: true,
      className: 'max-w-[50%] 2xl:w-[40%]',
      defaultSort: [{ id: 'salestaxcode', desc: true }],
      columns: [
        {
          accessorKey: 'salestaxcode',
          header: 'Sales Tax Code',
          size: 130,
          enableSorting: true,
        },
        {
          accessorKey: 'salestaxdesc',
          header: 'Description',
          enableSorting: true,
        },
        {
          accessorKey: 'salesTaxRateTotal',
          header: 'Base Rate',
          enableSorting: true,
          cell: ({ row }: any) =>
            convertToFloat({
              value: row?.original?.salesTaxRateTotal,
              decimalPlaces: 3,
              postfix: '%',
            }),
        },
        {
          accessorKey: 'effectivedate',
          header: 'Effective Date',
          enableSorting: true,
          cell: ({ row }: any) => formatDate(row?.original?.effectivedate),
        },
        {
          accessorKey: 'salestaxstate',
          header: 'State',
          enableSorting: true,
        },
        {
          accessorKey: 'isactive',
          header: 'Status',
          enableSorting: true,
          cell: ({ row }: any) => (
            <StatusBadge status={row?.original?.isactive} />
          ),
        },
      ],
    },
    {
      pageLabel: 'Departments',
      formLabel: 'Department',
      navigatingRoute: 'departments',
      findApi: DEPARTMENT_API_ROUTES.ALL,
      deleteApi: DEPARTMENT_API_ROUTES.DELETE,
      addLabel: 'New Department',
      bindingKey: 'id',
      isAddButton: true,
      className: 'max-w-[25%]',
      defaultSort: [{ id: 'dept', desc: true }],
      columns: [
        {
          accessorKey: 'dept',
          header: 'Department',
          size: 130,
          enableSorting: true,
        },
        {
          accessorKey: 'deptDesc',
          header: 'Description',
          enableSorting: true,
        },
        {
          accessorKey: 'prtKitCompOnInv',
          header: 'Print Kit Components on Invoices',
          enableSorting: true,
          cell: ({ row }: any) => (
            <StatusBadge
              status={row.original.prtKitCompOnInv}
              activeText="Yes"
              inactiveText="No"
              className="w-14"
            />
          ),
        },
      ],
    },
    {
      pageLabel: 'Drivers',
      formLabel: 'Driver',
      navigatingRoute: 'drivers',
      findApi: DRIVERS_API_ROUTES.ALL,
      deleteApi: DRIVERS_API_ROUTES.DELETE,
      addLabel: 'New Driver',
      bindingKey: 'id',
      isAddButton: true,
      className: 'max-w-[25%]',
      defaultSort: [{ id: 'name', desc: true }],
      columns: [
        {
          accessorKey: 'name',
          header: 'Driver',
          size: 130,
          enableSorting: true,
        },
        {
          accessorKey: 'phone',
          header: 'Phone',
          size: 130,
          enableSorting: true,
          cell: (info: any) => {
            const phone = info.getValue() as string;
            return formatPhoneNumber(phone);
          },
        },
        {
          accessorKey: 'email',
          header: 'E-mail',
          size: 130,
          enableSorting: true,
        },
        {
          accessorKey: 'gpsName',
          header: 'GPS Device Name',
          size: 130,
          enableSorting: true,
        },
      ],
    },
    {
      pageLabel: 'E-sign Fields',
      formLabel: 'E-sign Field',
      navigatingRoute: 'e-sign-fields',
      findApi: E_SIGN_FILEDS_API_ROUTES.ALL,
      deleteApi: E_SIGN_FILEDS_API_ROUTES.DELETE,
      addLabel: 'New E-sign Field',
      bindingKey: 'id',
      isAddButton: true,
      className: 'max-w-[25%]',
      defaultSort: [{ id: 'formType', desc: true }],
      columns: [
        {
          accessorKey: 'formType',
          header: 'Form Type',
          size: 130,
          enableSorting: true,
        },
        {
          accessorKey: 'fieldName',
          header: 'Field Name',
          size: 130,
          enableSorting: true,
        },
      ],
    },
    {
      pageLabel: 'Equipment Types',
      formLabel: 'Equipment Type',
      navigatingRoute: 'equipment-types',
      addLabel: 'New Equipment Type',
      findApi: EQUIPMENT_TYPES_API_ROUTES.ALL,
      deleteApi: EQUIPMENT_TYPES_API_ROUTES.DELETE,
      bindingKey: 'id',
      isAddButton: true,
      className: 'max-w-[25%]',
      defaultSort: [{ id: 'equipmentType', desc: true }],
      columns: [
        {
          accessorKey: 'equipmentType',
          header: 'Code',
          size: 130,
          enableSorting: true,
        },
        {
          accessorKey: 'equipmentTypeDesc',
          header: 'Equipment Type',
          size: 130,
          enableSorting: true,
        },
      ],
    },
    {
      pageLabel: 'Event Types',
      formLabel: 'Event Type',
      navigatingRoute: 'event-types',
      addLabel: 'New Event Type',
      findApi: EVENT_TYPES_API_ROUTES.ALL,
      deleteApi: EVENT_TYPES_API_ROUTES.DELETE,
      bindingKey: 'id',
      isAddButton: true,
      className: 'max-w-[25%]',
      defaultSort: [{ id: 'eventType', desc: true }],
      columns: [
        {
          accessorKey: 'eventType',
          header: 'Code',
          size: 130,
          enableSorting: true,
        },
        {
          accessorKey: 'eventDesc',
          header: 'Event Type',
          size: 130,
          enableSorting: true,
        },
      ],
    },
    {
      pageLabel: 'Payment Terms',
      formLabel: 'Payment Term',
      navigatingRoute: 'payment-terms',
      findApi: PAYMENT_TERM_API_ROUTES.ALL,
      deleteApi: PAYMENT_TERM_API_ROUTES.DELETE,
      addLabel: 'New Payment Term',
      bindingKey: 'paymentterm_id',
      isAddButton: true,
      className: 'max-w-[25%]',
      defaultSort: [{ id: 'term', desc: true }],

      columns: [
        {
          accessorKey: 'term',
          header: 'Payment Terms',
          size: 130,
          enableSorting: true,
        },
      ],
    },
    {
      pageLabel: 'Payment Types',
      formLabel: 'Payment Type',
      navigatingRoute: 'payment-types',
      addLabel: 'New Payment Type',
      findApi: PAYMENT_TYPE_API_ROUTES.ALL,
      deleteApi: PAYMENT_TYPE_API_ROUTES.DELETE,
      bindingKey: 'id',
      className: 'max-w-[25%]',
      isDeleteButton: false,
      defaultSort: [{ id: 'paymentMethod', desc: true }],
      columns: [
        {
          accessorKey: 'paymentMethod',
          header: 'Payment Type',
          size: 130,
          enableSorting: true,
        },
        {
          accessorKey: 'bankAccount',
          header: 'Bank Account',
          size: 130,
          enableSorting: true,
        },
      ],
    },
    {
      pageLabel: 'Quality Types',
      formLabel: 'Quality Type',
      navigatingRoute: 'quality-types',
      addLabel: 'New Quality Type',
      findApi: QUALITY_TYPES_API_ROUTES.ALL,
      deleteApi: QUALITY_TYPES_API_ROUTES.DELETE,
      bindingKey: 'id',
      isAddButton: true,
      className: 'max-w-[30%]',
      defaultSort: [{ id: 'qualityType', desc: true }],
      columns: [
        {
          accessorKey: 'qualityType',
          header: 'Code',
          size: 130,
          enableSorting: true,
        },
        {
          accessorKey: 'qualityDesc',
          header: 'Quality Type',
          size: 160,
          enableSorting: true,
        },
      ],
    },
    {
      pageLabel: 'Referral Types',
      formLabel: 'Referral Type',
      navigatingRoute: 'referral-types',
      addLabel: 'New Referral Type',
      findApi: REFERRAL_TYPE_API_ROUTES.ALL,
      deleteApi: REFERRAL_TYPE_API_ROUTES.DELETE,
      bindingKey: 'reftype_id',
      isAddButton: true,
      className: 'max-w-[25%]',
      defaultSort: [{ id: 'code', desc: true }],
      columns: [
        {
          accessorKey: 'code',
          header: 'Code',
          size: 130,
          enableSorting: true,
        },
        {
          accessorKey: 'description',
          header: 'Referral Type',
          size: 130,
          enableSorting: true,
        },
      ],
    },
    {
      pageLabel: 'Setup / Takedown',
      navigatingRoute: 'setup-takedown',
      addLabel: 'New Setup / Takedown',
      findApi: SETUP_TAKEDOWN_API_ROUTES.ALL,
      deleteApi: SETUP_TAKEDOWN_API_ROUTES.DELETE,
      bindingKey: 'id',
      isAddButton: true,
      className: 'max-w-[30%]',
      defaultSort: [{ id: 'code', desc: true }],
      columns: [
        {
          accessorKey: 'code',
          header: 'Code',
          size: 130,
          enableSorting: true,
        },
        {
          accessorKey: 'type',
          header: 'Type',
          size: 130,
          enableSorting: true,
          cell: ({ row }: any) =>
            row.original.type === 1
              ? SETUP_TAKEDOWN_STRING.SETUP
              : SETUP_TAKEDOWN_STRING.TAKEDOWN,
        },
        {
          accessorKey: 'description',
          header: 'Description',
          size: 130,
          enableSorting: true,
        },
      ],
    },
    {
      pageLabel: 'Shipping Companies',
      formLabel: 'Shipping Company',
      navigatingRoute: 'shipping-companies',
      addLabel: 'New Shipping Company',
      findApi: SHIPPING_COMPAINES_API_ROUTES.ALL,
      deleteApi: SHIPPING_COMPAINES_API_ROUTES.DELETE,
      bindingKey: 'id',
      isAddButton: true,
      className: 'max-w-[30%]',
      defaultSort: [{ id: 'id', desc: true }],
      columns: [
        {
          accessorKey: 'id',
          header: 'Company #',
          size: 130,
          enableSorting: true,
        },
        {
          accessorKey: 'name',
          header: 'Shipping Company',
          size: 130,
          enableSorting: true,
        },
        {
          accessorKey: 'typeName',
          header: 'Type',
          size: 130,
          enableSorting: true,
        },
        {
          accessorKey: 'trackPage',
          header: 'Track Page',
          size: 130,
          enableSorting: true,
          cell: ({ row }: any) => {
            let websiteUrl = row.original.trackPage;

            // Check if the URL is missing the protocol (http:// or https://)
            if (websiteUrl && !/^https?:\/\//i.test(websiteUrl)) {
              websiteUrl = 'https://' + websiteUrl; // Add https:// by default
            } else if (websiteUrl && !/^http?:\/\//i.test(websiteUrl)) {
              websiteUrl = 'http://' + websiteUrl; // Add https:// by default
            }

            return (
              <a
                href={websiteUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="text-[#5f26c9] hover:text-[#4c1d9d] transition duration-200 ease-in-out"
              >
                {row.original.trackPage}
              </a>
            );
          },
        },
      ],
    },
    {
      pageLabel: 'Surge Rates',
      formLabel: 'Surge Rate',
      navigatingRoute: 'surge-rates',
      addLabel: 'New Surge Rate',
      findApi: SURGE_RATES_API_ROUTES.ALL,
      deleteApi: SURGE_RATES_API_ROUTES.DELETE,
      bindingKey: 'id',
      isAddButton: true,
      className: 'max-w-[30%]',
      defaultSort: [{ id: 'code', desc: true }],
      columns: [
        {
          accessorKey: 'code',
          header: 'Code',
          size: 130,
          enableSorting: true,
        },
        {
          accessorKey: 'description',
          header: 'Description',
          size: 130,
          enableSorting: true,
        },
        {
          accessorKey: 'rate',
          header: 'Rate',
          size: 130,
          enableSorting: true,
          cell: ({ row }: any) =>
            convertToFloat({
              value: row?.original?.rate,
              postfix: '%',
            }),
        },
      ],
    },
    {
      pageLabel: 'Trucks',
      formLabel: 'Truck',
      navigatingRoute: 'trucks',
      addLabel: 'New Truck',
      findApi: TRUCKS_API_ROUTES.ALL,
      deleteApi: TRUCKS_API_ROUTES.DELETE,
      bindingKey: 'id',
      isAddButton: true,
      className: 'max-w-[30%]',
      defaultSort: [{ id: 'truckName', desc: true }],
      columns: [
        {
          accessorKey: 'truckName',
          header: 'Truck',
          size: 130,
          enableSorting: true,
        },
        {
          accessorKey: 'location',
          header: 'Location',
          size: 130,
          enableSorting: true,
        },
        {
          accessorKey: 'defaultRouteDesc',
          header: 'Default Route Description',
          size: 130,
          enableSorting: true,
        },
        {
          accessorKey: 'allowedWeight',
          header: 'Allowed Weight (lbs)',
          size: 130,
          enableSorting: true,
          cell: ({ row }: any) => {
            return row?.original?.allowedWeight
              ? convertToFloat({
                  value: row?.original?.allowedWeight,
                })
              : '';
          },
        },
      ],
    },
    {
      pageLabel: 'Truck Equipment List',
      formLabel: 'Equipment',
      navigatingRoute: 'truck-equipment-list',
      addLabel: 'New Equipment',
      findApi: TRUCKS_EQUIPMENTS_API_ROUTES.ALL,
      deleteApi: TRUCKS_EQUIPMENTS_API_ROUTES.DELETE,
      bindingKey: 'id',
      isAddButton: true,
      className: 'max-w-[30%]',
      defaultSort: [{ id: 'equipmentTypeName', desc: true }],
      columns: [
        {
          accessorKey: 'equipmentTypeName',
          header: 'Equipment Type',
          size: 130,
          enableSorting: true,
        },
        {
          accessorKey: 'description',
          header: 'Description',
          size: 160,
          enableSorting: true,
        },
        {
          accessorKey: 'weight',
          header: 'Weight (lbs)',
          size: 130,
          enableSorting: true,
          cell: ({ row }: any) =>
            row?.original?.weight
              ? convertToFloat({
                  value: row?.original?.weight,
                })
              : '',
        },
      ],
    },
    {
      pageLabel: 'Local Zip Codes',
      formLabel: 'Local Zip Code',
      navigatingRoute: 'local-zip-codes',
      addLabel: 'New Local Zip Code',
      findApi: LOCAL_ZIP_CODES_API_ROUTES.ALL,
      deleteApi: LOCAL_ZIP_CODES_API_ROUTES.DELETE,
      bindingKey: 'id',
      isAddButton: true,
      className: 'max-w-[25%]',
      defaultSort: [{ id: 'zipFrom', desc: true }],
      columns: [
        {
          accessorKey: 'zipFrom',
          header: 'Zip Code From',
          size: 130,
          enableSorting: true,
        },
        {
          accessorKey: 'zipThru',
          header: 'Zip Code Thru',
          size: 160,
          enableSorting: true,
        },
      ],
    },
    {
      pageLabel: 'Packing List Departments',
      formLabel: 'Packing List Departments',
      navigatingRoute: 'packing-list-departments',
      addLabel: 'New Packing List Departments',
      findApi: PACKING_LIST_DEPARTMENTS_API_ROUTES.ALL,
      bindingKey: 'id',
      isDeleteButton: false,
      defaultSort: [{ id: 'name', desc: true }],
      columns: [
        {
          accessorKey: 'name',
          header: 'Form Type',
          size: 160,
          enableSorting: true,
        },
        {
          accessorKey: 'departments',
          header: 'Departments',
          size: 130,
          enableSorting: true,
        },
      ],
    },
  ];

  return { listingSetupConstants };
};

type StateTypes = {
  label: string;
  value: string;
};

// Base field type with common properties
type BaseField<T> = {
  name: Path<T>;
  label?: string;
  placeholder?: string;
  colSpan?: number;
};

// Input field type
type InputFieldType<T extends FieldValues> = BaseField<T> & {
  type: 'input';
  validation?: RegisterOptions<T, Path<T>>;
};

// Phone field type
type PhoneFieldType<T> = BaseField<T> & {
  type: 'phone';
};

// Select field type
type SelectFieldType<T> = BaseField<T> & {
  type: 'select';
  options: ListType[];
  isLoading?: boolean;
  isClearable?: boolean;
};

// Zipcode field type
type ZipcodeFieldType<T> = BaseField<T> & {
  type: 'zipcode';
  isUSA: boolean;
};

// Number field type
type NumberFieldType<T> = BaseField<T> & {
  type: 'number';
  prefix: string;
  maxLength: number;
};

// Checkbox field type
type CheckboxFieldType<T> = BaseField<T> & {
  type: 'checkbox';
};

// Textarea field type
type TextareaFieldType<T> = BaseField<T> & {
  type: 'textarea';
};

// Customer field type
type CustomerFieldType<T> = BaseField<T> & {
  type: 'customer';
};

// Union type of all field types
type FieldType<T extends FieldValues> =
  | InputFieldType<T>
  | PhoneFieldType<T>
  | SelectFieldType<T>
  | ZipcodeFieldType<T>
  | NumberFieldType<T>
  | CheckboxFieldType<T>
  | TextareaFieldType<T>
  | CustomerFieldType<T>;

// Type for the configuration function parameters
type DeliveryLocationFormFieldsConfig = {
  stateOptions: StateTypes[];
  countryOptions: ListType[];
  isFetchingStates: boolean;
  isUSA: boolean;
};

// Form Field Configuration
/**
 * Returns the configuration for the Delivery Location form fields.
 *
 * @param stateOptions - Array of state options for the "State" select field.
 * @param countryOptions - Array of country options for the "Country" select field.
 * @param isFetchingStates - Loading state for the states API call.
 * @param isUSA - Flag indicating if the selected country is USA.
 * @returns Array of form field configurations.
 */
export const getDeliveryLocationFormFields = ({
  stateOptions,
  countryOptions,
  isFetchingStates,
  isUSA,
}: DeliveryLocationFormFieldsConfig): FieldType<DeliveryLocationFormType>[] => [
  {
    type: 'input',
    name: 'location',
    label: 'Location',
    placeholder: 'Enter Location',
    validation: TEXT_VALIDATION_RULE,
    colSpan: 1,
  },
  {
    type: 'phone',
    name: 'phone',
    label: 'Phone',
    colSpan: 1,
  },
  {
    type: 'input',
    name: 'contact',
    label: 'Contact',
    placeholder: 'Enter Contact',
    colSpan: 1,
  },
  {
    type: 'input',
    name: 'contactEmail',
    label: 'Contact Email',
    placeholder: 'Enter Contact Email',
    validation: EMAIL_VALIDATION_RULEOptional,
    colSpan: 1,
  },
  {
    type: 'phone',
    name: 'contactPhone',
    label: 'Contact Phone',
    colSpan: 1,
  },
  {
    type: 'input',
    name: 'locationLine2',
    label: 'Location Desc 2',
    placeholder: 'Enter Location Desc 2',
    colSpan: 1,
  },
  {
    type: 'input',
    name: 'town',
    label: 'Town',
    placeholder: 'Enter Town',
    colSpan: 1,
  },
  {
    type: 'select',
    name: 'state',
    label: 'State',
    placeholder: 'Select State',
    isClearable: false,
    options: stateOptions,
    isLoading: isFetchingStates,
    colSpan: 1,
  },
  {
    type: 'zipcode',
    name: 'zipcode',
    label: 'Zip Code',
    isUSA,
    colSpan: 1,
  },
  {
    type: 'select',
    name: 'country',
    label: 'Country',
    placeholder: 'Select Country',
    options: countryOptions,
    isClearable: false,
    colSpan: 1,
  },
  {
    type: 'input',
    name: 'shipNote',
    label: 'Ship Note',
    placeholder: 'Enter Ship Note',
    colSpan: 1,
  },
  {
    type: 'number',
    name: 'deliveryFee',
    label: 'Delivery Fee',
    placeholder: 'Enter Delivery Fee',
    prefix: '$',
    maxLength: 9,
    colSpan: 1,
  },
  {
    type: 'customer',
    name: 'customerName',
    label: 'Customer',
    placeholder: 'Customer',
    colSpan: 2,
  },
  {
    type: 'checkbox',
    name: 'isDefault',
    label: 'This is the default location for this Customer',
    colSpan: 2,
  },
  {
    type: 'textarea',
    name: 'instructions',
    label: 'Directions',
    colSpan: 2,
  },
];

// Field Renderer

/**
 * Renders the appropriate form field component based on the field configuration.
 *
 * @param field - The form field configuration object.
 * @param form - The react-hook-form instance.
 * @param onCustomerSelect - Handler invoked when a customer is selected.
 * @param onCountryChange - Handler invoked when the country selection changes.
 * @returns JSX.Element representing the field.
 */
export const renderFormField = ({
  field,
  form,
  onCustomerSelect,
  onCountryChange,
}: {
  field: FieldType<DeliveryLocationFormType>;
  form: UseFormReturn<DeliveryLocationFormType>;
  onCustomerSelect: (customer: CustomerDetailTypes) => void;
  onCountryChange: (value: string) => void;
}) => {
  const commonProps = {
    form,
    name: field.name,
    label: field.label,
    placeholder: field.placeholder,
  };

  switch (field.type) {
    case 'input':
      return <InputField {...commonProps} validation={field.validation} />;
    case 'phone':
      return (
        <PhoneInputWidget {...commonProps} error={form.formState.errors} />
      );
    case 'select':
      return (
        <SelectWidget
          {...commonProps}
          form={form}
          optionsList={field.options}
          isLoading={field.isLoading}
          isClearable={field.isClearable}
          menuPosition="absolute"
          onSelectChange={
            field.name === 'country' ? onCountryChange : undefined
          }
        />
      );
    case 'zipcode':
      return <ZipCodeInput {...commonProps} isUSA={field.isUSA} />;
    case 'number':
      return (
        <NumberInputField
          {...commonProps}
          prefix={field.prefix}
          maxLength={field.maxLength}
          fixedDecimalScale
          thousandSeparator=","
        />
      );
    case 'checkbox':
      return <CheckboxField {...commonProps} control={form.control} />;
    case 'textarea':
      return <TextAreaField {...commonProps} />;
    case 'customer':
      return (
        <div className="col-span-2 grid grid-cols-2 gap-4 ">
          <InputField {...commonProps} className="w-full" disabled />
          <div className="flex gap-3 mt-8">
            <AppButton
              label="Clear"
              variant="neutral"
              onClick={() =>
                onCustomerSelect({
                  customer_id: 0,
                  first_name: '',
                  last_name: '',
                  state: '',
                  isactive: false,
                })
              }
              disabled={!form?.watch('customerId')}
            />
            <CustomerLookup handleOk={onCustomerSelect} className="w-fit" />
          </div>
        </div>
      );
    default:
      return null;
  }
};

export default useCommonSetupListingConstants;
