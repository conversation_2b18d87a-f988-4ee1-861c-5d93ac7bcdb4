import { TransactionType } from '@/types/accounting.types';

export const transactionTypeOptions = [
  { label: 'Debit', value: TransactionType.DEBIT },
  { label: 'Credit', value: TransactionType.CREDIT },
  { label: 'Beginning Balance', value: TransactionType.BEGINNING_BALANCE },
  { label: 'Refund', value: TransactionType.REFUND },
  { label: 'Overpayment', value: TransactionType.OVERPAYMENT },
  { label: 'Service Charge', value: TransactionType.SERVICE_CHARGE },
];
