import {
  invoiceOptionsBatchEmailMethod,
  DateOptions,
  DateOptionsSlips,
  InvoiceOptionsMethod,
  sendMethod,
  emailMethod,
  DateOptionsBatchEmail,
  PrintOptions,
  QuotesOptions,
  PrintEmailOptions,
  PrintPopupOptions,
} from '@/types/processes.types';

const invoiceOptionsMethods = [
  {
    label: 'Do Not Include Orders Already Invoiced',
    value: InvoiceOptionsMethod.IncludeOrders,
  },
  { label: 'Invoice All Orders', value: InvoiceOptionsMethod.InvoiceOrders },
  { label: 'Re-Invoice Orders', value: InvoiceOptionsMethod.ReInvoiceOrders },
];

const dateOptions = [
  {
    label: 'Delivery Date',
    value: DateOptions.DeliveryDate,
  },
  { label: 'Date of Use', value: DateOptions.DateOfUse },
];

const dateOptionsSlips = [
  {
    label: 'Delivery Date',
    value: DateOptionsSlips.DeliveryDate,
  },
  { label: 'Arrival Date', value: DateOptionsSlips.ArrivalDate },
];

const invoiceOptionsBatchEmail = [
  {
    label: 'Do Not Include Orders Already Invoiced',
    value: invoiceOptionsBatchEmailMethod.IncludeOrders,
  },
  {
    label: 'Invoice All Orders',
    value: invoiceOptionsBatchEmailMethod.InvoiceOrders,
  },
];

const sendBatchEmail = [
  {
    label: 'Invocies',
    value: sendMethod.Invocies,
  },
  { label: 'Order Confirmations', value: sendMethod.OrderConfirmations },
  {
    label: 'E-sign Documents',
    value: sendMethod.EsignDocuments,
    disabled: true,
  },
];

const emailBatchEmail = [
  {
    label: 'Order Contact E-mail',
    value: emailMethod.OrderContactEmail,
  },
  { label: 'Main Customer E-mail', value: emailMethod.MainCustomerEmail },
];

const dateOptionsBatchEmail = [
  {
    label: 'Delivery Date',
    value: DateOptionsBatchEmail.DeliveryDate,
  },
  { label: 'Date of Use', value: DateOptionsBatchEmail.DateOfUse },
  { label: 'Date Ordered', value: DateOptionsBatchEmail.DateOrdered },
];

const printOptions = [
  {
    label: 'Do Not Include Sub-Rentals Already Printed',
    value: PrintOptions.DoNotIncludeSubRentalsAlreadyPrinted,
  },
  { label: 'Print All Sub-Rentals', value: PrintOptions.PrintAllSubRentals },
];

const quotesOptions = [
  {
    label: 'Archive Quotes',
    value: QuotesOptions.ArchiveQuotes,
  },
  { label: 'Delete Quotes', value: QuotesOptions.DeleteQuotes },
];

const printEmailOptions = [
  {
    label: 'Previous Selection',
    value: PrintEmailOptions.PreviousSelection,
  },
  {
    label: 'Default Invoices & Packing Lists',
    value: PrintEmailOptions.DefaultInvoicesPackingLists,
  },
  { label: 'Other', value: PrintEmailOptions.Other, disabled: true },

  { label: 'Default Invoices', value: PrintEmailOptions.DefaultInvoices },
  { label: 'E-sign Documents', value: PrintEmailOptions.EsignDocuments },
  {
    label: 'Default Packing Lists',
    value: PrintEmailOptions.DefaultPackingLists,
  },
];

const printPopupOptions = [
  {
    label: 'Customer',
    value: PrintPopupOptions.Customer,
  },
  {
    label: 'Delivery Date',
    value: PrintPopupOptions.DeliveryDate,
  },
  {
    label: 'Pickup Date',
    value: PrintPopupOptions.PickupDate,
  },
  {
    label: 'Delivery Driver',
    value: PrintPopupOptions.DeliveryDriver,
  },
  {
    label: 'Pickup Driver',
    value: PrintPopupOptions.PickupDriver,
  },
  {
    label: 'City',
    value: PrintPopupOptions.City,
  },
];

export {
  invoiceOptionsMethods,
  dateOptions,
  dateOptionsSlips,
  invoiceOptionsBatchEmail,
  sendBatchEmail,
  emailBatchEmail,
  dateOptionsBatchEmail,
  printOptions,
  quotesOptions,
  printEmailOptions,
  printPopupOptions,
};
