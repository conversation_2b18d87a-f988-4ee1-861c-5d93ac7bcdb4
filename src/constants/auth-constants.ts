const EMAIL_VALIDATION_RULEs = {
  required: 'Required',
  pattern: {
    value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
    message: 'Please enter a valid Email Address',
  },
};

// without required
const EMAIL_VALIDATION_RULEOptional = {
  pattern: {
    value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
    message: 'Please enter a valid Email Address',
  },
};

const passwordValidationRules = {
  required: 'Required',
  minLength: {
    value: 8,
    message: 'Password must be at least 8 characters',
  },
};

export {
  EMAIL_VALIDATION_RULEOptional,
  EMAIL_VALIDATION_RULEs,
  passwordValidationRules,
};
