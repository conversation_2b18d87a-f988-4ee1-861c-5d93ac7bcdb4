import { lazy } from 'react';

import SuspenseWrapper from '../routes/SuspenseWrapper';
import { ResourceId } from '@/types/common.types';

const GeneralInformation = lazy(
  () => import('@/components/modules/system/user-options/GeneralInformation')
);
const OtherSettings = lazy(
  () => import('@/components/modules/system/user-options/OtherSettings')
);
const Security = lazy(
  () => import('@/components/modules/system/user-options/Security')
);

export const USER_OPTIONS_API = {
  GET: (id: ResourceId) => `/user/${id}`,
  UPDATE: (id: ResourceId) => `/user/${id}`,
};

export const LOGIN_PROFILE_API = {
  GET: (id: number) => `/user/loginprofile/${id}`,
};

export const IMAGES_API = {
  GET: (userId: ResourceId) => `s3file/get/userimage/${userId}`,
  UPLOAD: (userId: ResourceId) => `s3file/upload/userimage/${userId}`,
  DELETE: (userId: ResourceId) => `s3file/delete/userimage/${userId}`,
};

export const UserOptionsTabList = [
  {
    value: 'general-information',
    label: 'General Information',
    content: (
      <SuspenseWrapper>
        <GeneralInformation />
      </SuspenseWrapper>
    ),
  },
  {
    value: 'security',
    label: 'Security',
    content: (
      <SuspenseWrapper>
        <Security />
      </SuspenseWrapper>
    ),
  },
  {
    value: 'other-settings',
    label: 'Other Settings',
    content: (
      <SuspenseWrapper>
        <OtherSettings />
      </SuspenseWrapper>
    ),
  },
];
