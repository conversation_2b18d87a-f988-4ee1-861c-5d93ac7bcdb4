// src/constants/storageKeys.ts
export const STORAGE_KEYS = {
  USER_ID: 'userId',
  FIRST_NAME: 'firstName',
  LAST_NAME: 'lastName',
  DEFAULT_LOCATION_ID: 'defaultLocationId',
  IDLE_LOGOUT_MINUTES: 'idleLogoutMinutes',
  PROFILE_IMAGE: 'profileImage',
  TOKEN_TYPE: 'tokenType',
  ACCESS_TOKEN: 'accessToken',
  REFRESH_TOKEN: 'refreshToken',
  USED_TOKEN: 'accessToken', // If this is the same as the access token
  EXPIRES_IN: 'expiresIn',
  IS_AUTHENTICATED: 'accessToken',
  R<PERSON><PERSON>: 'role',
  REMEMBER_ME: 'rememberMe',
  USER_NAME: 'userName',
  PERMISSIONS: 'permissions',
  IDLE_ORDER_MINUTES: 'idleOrderMinutes',
};
