export interface PrintList {
  form: string;
  copies: number;
  printer: string;
}

export interface OptionsTypes {
  form: string;
  includeSales: boolean;
}

export interface WarehouseCustomerPickupsReturnsListTypes {
  id: number;
  jobNo: number;
  orderid: number;
  revid: number;
  customer: string;
  dateOfUse: string;
  dp: string;
  deliveryDate: string;
  deliveryTime: string;
  pickupDate: string;
  pickupTime: string;
  status: string;
}

export interface CustomerPickupsReturnsForm {
  itemViewType?: 'itemSummary' | 'itemOrders';
  kitType?: 'kits' | 'kitComponents';
  print: PrintList[];
  downloadType?: string;
  includeSubRentals?: boolean;
  includeDeliveriesPickups?: boolean;
}

export interface WarehouseOrderDeliveriesPickupsListTypes {
  id: number;
  jobNo: number;
  orderid: number;
  revid: number;
  customer: string;
  dateOfUse: string;
  dp: string;
  deliveryDate: string;
  deliveryTime: string;
  pickupDate: string;
  pickupTime: string;
  status: string;
  deliveryItinerary: string;
  pickupItinerary: string;
}

export interface OrderDeliveriesPickupsForm {
  itemViewType?: 'itemSummary' | 'itemOrders';
  kitType?: 'kits' | 'kitComponents';
  totalWeight?: number;
  dateFrom?: Date | string;
  dateTo?: Date | string;
  truck?: string;
  print: PrintList[];
}

export interface WarehouseSubrentalPickupsReturnsListTypes {
  id: number;
  subRentalId: number;
  orderid: number;
  customerVendor: string;
  pr: string;
  pickupDate: string;
  pickupTime: string;
  returnDate: string;
  returnTime: string;
  status: string;
  pickupItinerary: string;
  returnItinerary: string;
}

export interface SubrentalPickupsReturnsForm {
  itemViewType?: 'itemSummary' | 'itemOrders';
  kitType?: 'kits' | 'kitComponents';
  totalWeight?: number;
  dateFrom?: Date | string;
  dateTo?: Date | string;
  truck?: string;
  print: PrintList[];
}

export interface WarehouseTruckItinerariesListTypes {
  id: number;
  truck: string;
  dateFrom: string;
  dateTo: string;
  driver: string;
}

export interface TruckItineraryForm {
  dateFrom?: Date | string;
  dateTo?: Date | string;
  truck?: string;
  print: PrintList[];
}

export type FilterFieldName =
  | 'storeLocationId'
  | 'dateOfUseFrom'
  | 'dateOfUseThru'
  | 'arrivalDate'
  | 'returnShipDate'
  | 'arrivalReturnDate';

export type FilterFieldNameOrderDeliveriesPickups =
  | 'storeLocationId'
  | 'type'
  | 'dateOfUseFrom'
  | 'dateOfUseThru'
  | 'arrivalDate'
  | 'returnShipDate'
  | 'arrivalReturnDate'
  | 'notDelivered'
  | 'scheduled'
  | 'loading'
  | 'deliveryOnRoute'
  | 'delivered'
  | 'unableToDeliver'
  | 'pickUpOnRoute'
  | 'pickUp'
  | 'unableToPickUp'
  | 'includeSales'
  | 'includeMissingEquipments';

export type FilterFieldNameSubrentalPickupsReturns =
  | 'storeLocationId'
  | 'dateOfUseFrom'
  | 'dateOfUseThru'
  | 'notPickedUp'
  | 'scheduled'
  | 'pickUpOnRoute'
  | 'pickUp'
  | 'unableToPickup'
  | 'returnOnRoute'
  | 'returned'
  | 'unableToReturn';

export type FilterFieldNameTruckItinerary =
  | 'dateOfUseFrom'
  | 'dateOfUseThru'
  | 'notPickedUp'
  | 'scheduled'
  | 'pickUpOnRoute'
  | 'pickUp'
  | 'unableToPickup'
  | 'returnOnRoute'
  | 'returned'
  | 'unableToReturn';

export const CheckBoxMap = [
  {
    name: 'arrivalDate',
    label: 'Arrival Date',
  },
  {
    name: 'returnShipDate',
    label: 'Return Ship Date',
  },
  {
    name: 'arrivalReturnDate',
    label: 'Arrival/Return Ship',
  },
];

export const StatusCheckBoxMap = [
  {
    name: 'notDelivered',
    label: 'Not Delivered',
  },
  {
    name: 'cpu',
    label: 'CPU',
  },
  {
    name: 'cr',
    label: 'CR',
  },
  {
    name: 'includeSales',
    label: 'Include Sales',
  },
];

export const StatusCheckBoxOrderDeliveriesPickupsMap = [
  {
    name: 'notDelivered',
    label: 'Not Delivered',
  },
  {
    name: 'scheduled',
    label: 'Scheduled',
  },
  {
    name: 'loading',
    label: 'Loading',
  },
  {
    name: 'deliveryOnRoute',
    label: 'Delivery On Route',
  },
  {
    name: 'delivered',
    label: 'Delivered',
  },
  {
    name: 'unableToDeliver',
    label: 'Unable To Deliver',
  },
  {
    name: 'pickUpOnRoute',
    label: 'Pick Up On Route',
  },
  {
    name: 'pickUp',
    label: 'Pick Up',
  },
  {
    name: 'unableToPickUp',
    label: 'Unable To Pick Up',
  },
  {
    name: 'includeSales',
    label: 'Include Sales',
  },
  {
    name: 'includeMissingEquipments',
    label: 'Include Missing Equipments',
  },
];

export const StatusCheckBoxSubrentalPickupsReturnsMap = [
  {
    name: 'notPickedUp',
    label: 'Not Picked Up',
  },
  {
    name: 'scheduled',
    label: 'Scheduled',
  },
  {
    name: 'pickUpOnRoute',
    label: 'Pick Up On Route',
  },
  {
    name: 'pickUp',
    label: 'Pick Up',
  },
  {
    name: 'unableToPickup',
    label: 'Unable To Pick Up',
  },
  {
    name: 'returnOnRoute',
    label: 'Return On Route',
  },
  {
    name: 'returned',
    label: 'Returned',
  },
  {
    name: 'unableToReturn',
    label: 'Unable To Return',
  },
];

export const CheckBoxMapSubrentalPickupsReturns = [
  {
    name: 'pickupDate',
    label: 'Pickup Date',
  },
  {
    name: 'returnDate',
    label: 'Return Date',
  },
];

export interface CheckedBoxType {
  name: FilterFieldName | string;
  label: string;
}

export const typesOfOderDeliveriesPickups = [
  {
    value: 'all',
    label: 'All Types',
  },
  {
    value: 'deliver',
    label: 'Deliver',
  },
  {
    value: 'ship',
    label: 'Ship',
  },
];

export interface OpenDialogType {
  state: boolean;
  action: string;
}

export enum WAREHOUSE_POPUPS {
  VIEW_ITEMS = 'view-items',
  NEW_ITINERARY = 'new-itinerary',
  TRUCK_ITINERARY = 'truck-itinerary',
  PRINT = 'print',
  OPTIONS = 'options',
  EXTRACT = 'extract',
}

export interface ItineraryItem {
  id: number;
  seqId: number;
  orderId: string;
  itineraryDescription: string;
  date: string;
  time: string;
  delPu: string;
  timeFrom: string;
  timeTo: string;
  startTime: string;
  travelTime: string;
  estDpTime: string;
  setupTime: string;
  actualDpTime: string;
  actualSetupTime: string;
}

export interface FormValues {
  itineraries: ItineraryItem[];
  dateFrom?: Date | string;
  dateTo?: Date | string;
  truck?: string;
  routeDescription?: string;
  driver?: string;
  allowedWeight?: number | string;
  truckWeight?: number | string;
  view?: string;
}

export enum TRUCK_WAREHOUSE_TYPE {
  ORDERS = 'orders',
  SUB_RENTALS = 'sub-rentals',
}

export const TRUCK_WAREHOUSE_TYPE_OPTIONS = [
  { label: 'Orders', value: TRUCK_WAREHOUSE_TYPE.ORDERS },
  { label: 'Sub-Rentals', value: TRUCK_WAREHOUSE_TYPE.SUB_RENTALS },
];
