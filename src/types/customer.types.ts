import { ApiResponseDto } from './common.types';

export interface TabsContentTypes {
  value: string;
  content: JSX.Element;
}

export interface CustomerDetailTypes {
  customer_id: number;
  first_name: string;
  last_name: string;
  address1?: string | null;
  address2?: string | null;
  city?: string;
  state: string;
  zipcode?: string;
  phone?: string;
  tel1?: string;
  notes?: string;
  full_name?: string;
  isactive: boolean;
  custtype_id?: number;
  // optional types
  country?: string;
  contact?: string;
  email?: string;
  tel2?: string;
  emailaddress?: string;
  salesPersonId?: number | string;
  salestaxcode_id?: number | string;
  exemptno?: number | string;
  licenseno?: number | string;
  paymentterm_id?: number | string;
  defstorelocationno?: number | string;
}

export interface ContactInfo {
  email: string;
  phone: string;
  website: string;
  fax: string;
}

export interface DeliveryDetails {
  salesperson: string;
  defaultDeliveryType: string;
  defaultDeliveryCharge: string;
  upsAccountNumber: string;
  freeShipping: string;
  corporate: boolean;
  priceOnInvoice: boolean;
  dropShip: boolean;
  chargeConvenienceFee: boolean;
  displayAlertOnOrder: boolean;
  onlinePayments: boolean;
}

export interface PaymentDetails {
  defaultSalesTaxCode: string;
  defaultPaymentType: string;
  creditStatus: string;
  creditLimit: string;
  creditMessage: string;
  rentStatus: string;
  paymentTerms: string;
}

export interface AccountDetails {
  baseDiscount: string;
  autoEmails: boolean;
  referralType: string;
  referralContactId: string;
  demageWaiver: string;
  demageWaiverPercent: string;
  blanketPO: string;
  exemptId: string;
  licenseNumber: string;
  limitedCustomerId: string;
  accountInfo: string;
  defaultSpecialInstructions: string;
  fuelSurchargePercent: string;
  alternateEmail: string;
  invoiceDelivery: {
    email: boolean;
    fax: boolean;
    print: boolean;
  };
  statementDelivery: {
    email: boolean;
    fax: boolean;
    print: boolean;
  };
}

export interface LinkedFileTypes {
  icon?: React.ReactNode;
  fileName: string;
  dateCreated: string;
  owner: string;
}

export interface QuickNotesTypes {
  noteName: string;
  dateCreated: string;
  owner: string;
}

export interface DiscountsTypes {
  category_id?: number;
  department?: string;
  category?: string;
  catdiscount?: number;
  custdiscount_id?: number;
  customer_id?: number;
  department_id?: number;
}

export interface DepartmentDiscountsTypes {
  dept: number;
  dept_desc: string;
  catdiscount?: string;
}

export interface SubTypesDataTypes {
  customerType: string;
}

export interface CustomPriceTypes {
  itemId: string;
  category: string;
  description: string;
  quantity: number;
  cost: string;
  unitPrice: string;
}

export interface FormData {
  first_name: string;
  last_name: string;
  address: string;
  address1: string;
  address2: string;
  city: string;
  state: string;
  zipcode: string;
  country: string;
  customerType: string;
  defstorelocation: string;
  contact: string;
  emailaddress: string;
  tel1: string;
  tel2: string;
  website: string;
  telfax: string;
  salesPersonId: string;
  deliverytype_id: string;
  defdelchg: number | null;
  upsacctnum: string;
  freeshipping: string;
  free_shipping: string;
  corporate: boolean;
  pricingoninvoice: boolean;
  dropship: boolean;
  defconvfeeflag: boolean;
  displayalert: boolean;
  onlinepmts: boolean;
  salestaxcode_id: string;
  paymenttype_id: string;
  credithold: string;
  creditlimit: number | null;
  creditmessage: string;
  rentstatus: string;
  paymentterm_id: string;
  discountpercent?: string | null | number;
  autoemails: boolean;
  reftype_id: string;
  referralcommpct?: string | null | number;
  damwaiv: boolean | string;
  damwaivpct?: string | null | number;
  blanketpo: string;
  exemptno: string;
  licenseno: string;
  linkedcustno: string;
  acctinfo: string;
  defspecialinstr: string;
  fuelchgpct?: string | null | number;
  altemailaddress: string;
  invoicecomm: InvoiceDeliveryMethod;
  statementcomm: InvoiceDeliveryMethod;
  custtype: any;
  customer?: string;
  customer_id: number;
  custstatus: string;
  custtype_id: number;
  isactive: boolean;
  creditstatus: string;
  defstorelocationno?: number;
  country_id: number;
}

export type CreateCustomerResponseDto = ApiResponseDto<FormData>;

export interface NewContactInfoTypes {
  phoneNumber: string;
  type: string;
}

// Customer Type List Interface
export interface CustomerType {
  custtype_id: number;
  code: string | null;
  description: string | null;
  delcharge: number;
  pt_customers: string[];
}

export type CustomerTypeDto = ApiResponseDto<CustomerType>;

// Payment Term List Interface
export interface PaymentTermType {
  paymentterm_id: number;
  term: string;
}

// Payment Type List Interface
export interface PaymentType {
  paymenttype_id: number;
  code: string;
  pmttype: string;
}

// Sales Tax Code Interface
export interface SalesTaxCodeType {
  salestaxcode_id: number;
  salestaxcode: string;
  salestaxdesc: string;
  salestaxstate: string;
  selectable: boolean;
  effectivedate: string;
  taxdel: boolean;
  taxlabor: boolean;
  taxdamagewaiver: boolean;
  taxfuelsurcharge: boolean;
  transaction: boolean;
  updateqb: boolean;
  taxconvfee: boolean;
  convfeetaxsalesonly: boolean;
  taxexpeditedfee: boolean;
  taxproductionfee: boolean;
}

// Referal Type Interface
export interface ReferralType {
  reftype_id: number;
  code: string;
  description: string;
}

// CategoryType Interface
export interface CategoryType {
  category_id: number;
  catno: number;
  catdesc: string;
  department_id: number;
  incomeacct: string;
}

// Delivery Type Interface
export interface DeliveryType {
  deliverytype_id: number;
  name: string;
  description: string;
  presun: number;
  premon: number;
  pretue: number;
  prewed: number;
  prethur: number;
  prefri: number;
  presat: number;
  postsun: number;
  postmon: number;
  posttue: number;
  postwed: number;
  postthur: number;
  postfri: number;
  postsat: number;
  shiptransitdays: number;
}

// Enum for invoice delivery methods
export enum InvoiceDeliveryMethod {
  Email = 'Email',
  Fax = 'Fax',
  Print = 'Print',
}

export interface CountryType {
  country_id: number;
  name: string;
}

export type CountryListDto = ApiResponseDto<CountryType>;

export interface TimeZoneType {
  id: number;
  displayName: string;
}

export interface StateType {
  state_id: number;
  country_id: number;
  name: string;
  code: string;
}

export interface CustomerSubTypeDto {
  custTypeId: number;
  isSelected: boolean;
  description?: string;
}

export type PhoneNumbersDto = {
  phone_id?: number;
  phoneno: string;
  phonetype_id: number | null | string;
};

export interface AdditionalContactDto {
  contact_id?: number;
  contacttype_id?: number;
  linkid?: number;
  email?: string;
  isactive?: boolean;
  first_name?: string;
  last_name?: string;
  title?: string;
  greeting?: string;
  isprimary?: boolean;
  batch?: string;
  batch_id?: number;
  phones?: PhoneNumbersDto[];
}

export interface StoreLocationsDto {
  config_id: number;
  storename: string;
  location: string;
}

interface FilterDto {
  field: string;
  value: string;
  operator: string;
}

export interface CustomerRequestDto {
  pageNumber: number;
  pageSize: number;
  sortBy: string;
  sortAscending: boolean;
  filters: FilterDto[];
}

export interface CustomerNotesDto {
  customernote_id: number;
  customer_id: number;
  user_id: number;
  isactive: boolean;
  note: string;
  createdOn: string;
}

export interface CustomerFilesDto {
  id: number;
  fileName: string;
  fileSizeText: string;
  date: string;
  owner: string;
  createdAt: string;
}

export interface ZipCodesType {
  zipCodeFrom: string | null;
  zipCodeThru: string | null;
  charge: number | null;
}
