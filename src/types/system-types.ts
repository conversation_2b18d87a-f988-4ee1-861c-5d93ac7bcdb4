export interface User {
  id?: number;
  userId: string;
  firstName: string;
  lastName: string;
  isActive: boolean;
  salesCommission: number;
  emailHome: string;
  emailWork: string;
  defaultLocationId?: number;
  emailUser: string;
  isEmailConfirmed: boolean;
}

export interface UserTabs {
  information: InformationSystemTypes;
  'users-options': UsersOptionsSystemTypes;
  'profile-options': ProfileOptionsSystemTypes;
}

export interface InformationSystemTypes {
  userId: string;
  firstName: string;
  lastName: string;
  salesCommission?: string;
  emailWork: string;
  emailHome?: string;
  isActive: boolean;
  permissions: Record<number, boolean>;
}

export interface UsersOptionsSystemTypes {
  phoneWork?: string;
  phoneCell?: string;
  fax?: string;
  permissions: Record<string, any>;
}

export interface ProfileOptionsSystemTypes {
  permissions: Record<number, boolean>;
  actionAccess: Permission[];
  featureAccess: Permission[];
}

export interface Permission {
  permissionId: number;
  permission: string;
  parentPermissionId: number | null;
  parentPermission: string | null;
  enabled: boolean;
  description: string;
}

export type PermissionForm = {
  permissions: Record<string, boolean>;
  actionAccess: Permission[];
  featureAccess: Permission[];
};
