import { Path } from 'react-hook-form';

export enum ItemType {
  RENTAL_ITEM = 'RENTAL_ITEM',
  KIT_ITEM = 'KIT_ITEM',
  SALE_ITEM = 'SALE_ITEM',
}

export enum ItemTabs {
  INFORMATION = 'information',
  LINKED_FILES = 'linked-files',
  OPTIONS = 'options',
  INVENTORY = 'inventory',
  QUANTITY = 'quantity',
}

export interface ItemsListingTypes {
  id: number;
  itemId: string;
  description: string;
  itemLocation: string | null;
  categoryId: number;
  vendorId: number;
  itemType: string;
  itemTypeDesc: string;
  unitPrice: number;
  replacementCharge: number;
  quantity: number;
  latestCost: number;
  averageCost: number;
  latestDate: string;
  quantityDate: string;
  replacementChargeDate: string;
  unitPriceDate: string;
  isActive: boolean;
}

export interface ItemInformationTypes {
  id: number;
  itemId: string;
  description: string;
  location: string | null;
  categoryId: number;
  vendorId: number;
  itemType: string;
  isActive: string;
  unitPrice: number;
  quantity: number;
  latestCost: number;
  averageCost: number;
  latestCostUpdatedOn: string;
  quantityUpdatedOn: string;
  replacementChargeUpdatedOn: string;
  unitPriceUpdatedOn: string;
  replacementCharge: number;
  vendor: string;
}

export interface LinkedFilesTypes {
  id: number;
  fileName: string;
  createdAt: string;
  fileSizeText: string;
  sequence: number;
  itemId: string;
  url: string;
  unitPrice: number;
  replacementCharge: number;
  isActive: string;
  vendorId: number;
  cleanUpDays: string;
  weight: number;
  cube: number;
  packageDiscount: number;
  washCycle: number;
  webSku: number;
}

export interface UploadLinkedFilesFormData {
  file: string;
}

export interface OptionsTypes {
  id: number;
  cleanupDays: number;
  sequenceNo: number;
  weight: number;
  cube: number;
  includeItemId: string;
  includeQuantity: number;
  isInventoryItem: boolean;
  isPackageItem: boolean;
  packageDiscount: number;
  webSku: string;
  warningMessage: string;
  itemInfo: string;
  isCommissionable: boolean;
  isTaxable: boolean;
  isDiscountable: boolean;
  isDamageWaiver: boolean;
  isCalculateFuelCharge: boolean;
  isCalculateProdFee: boolean;
  isPrintInWarehouseMgr: boolean;
  isPrintComponentWhenZero: boolean;
  isTentTop: boolean;
  isRfid: boolean;
  isTagged: boolean;
  isPrintRfidLabel: boolean;
  isItemInSwatch: boolean;
  isItemOnWeb: boolean;
  isItemPictureOnWeb: boolean;
  isPriceLocked: boolean;
  washCycle: string;
  isTumbleDry: string;
  replacementCharge: number;
  unitPrice: number;
  isActive: string;
}

export interface ItemOptionTypes {
  itemOption: OptionsTypes;
}

export interface AllTabTypeMap {
  information: ItemInformationTypes;
  'linked-files': LinkedFilesTypes;
  options: OptionsTypes;
}

export interface ItemTabTypes {
  id: number;
  itemId: string;
  description: string;
  location: string | null;
  categoryId: number;
  vendorId: number;
  itemType: string;
  isActive: string;
  unitPrice: number;
  quantity: number;
  latestCost: number;
  averageCost: number;
  latestCostUpdatedOn: string;
  quantityUpdatedOn: string;
  replacementCostUpdatedOn: string;
  unitPriceUpdatedOn: string;
  replacementCharge: number;
  itemOption: OptionsTypes;
}

// Array of SwitchField configurations with TypeScript types
export type SwitchFieldConfig<T> = {
  label: string;
  name: Path<T>;
  disabled?: boolean;
  description?: string;
};

// Copy Items
export interface CopyItemsType {
  id: number;
  newItemId: string;
}

// Item Lookup Dropdown
export interface ItemLookupDropdownType {
  id: number;
  itemId: string;
}

export interface ItemLookupTypes {
  id: number;
  itemId: string;
  quantity: number;
  description: string;
  unitPrice?: number | string;
}

// Kit Items
export interface KitItemsType {
  id: number | null;
  itemId: string;
  quantity: number | string;
  description: string;
  parentItemId?: number;
  childItemId?: number;
}

// Package Items
export interface PackageItemsType {
  id: number | null;
  itemId: string;
  quantity: number | string;
  description: string;
  parentItemId?: number;
  childItemId?: number;
}

export interface PackageItemsFormType {
  id: number | null;
  itemId: string;
  quantity: number;
  description: string;
  parentItemId?: number;
  childItemId?: number;
}

export interface BulkItemList {
  id: number;
  itemId: string;
  bulkId: number;

  description: string;
  location: string;
  sequenceNo: number | string;
  quantity: number | string;
  unitPrice: number | string;
  replacementCharge: number | string;
  latestCost: number | string;

  // optional
  category?: string;
  department?: string;
  itemTypeDesc?: string;
  itemType?: string;
  vendorId?: number;
  vendorName?: string;
  webSku?: string;
  isActive?: boolean;
}
// Bulk Edit Items form type
export interface BulkEditItemType {
  quantity: string;
  costUpdate: string;
  category?: string[];
  items: BulkItemList[];
}

// Item Brief Types
export interface ItemBriefType {
  id: number;
  itemId: string;
  description: string;
  unitPrice: number;
  quantity: number;
}

// item filter type
export interface ItemFiltersType {
  label: string;
  value: string;
  name: string;
  operator: string;
}

/**
 * Inventory Types
 */

export interface InventoryListItemTypes {
  id: number | null;
  quantity: number;
  storeLocationId: string | null;
  serialNo: string | null;
  qualityId: string | number | null;
  purchaseDate: string | null;
  sequenceNo: number | null;
  purchaseDateSequenceVal?: any;
}

export interface InventoryListPayloadTypes {
  id: number | null;
  quantity: number;
  storeLocationId: string | null;
  serialNo: string | null;
  qualityId: number | null;
  purchaseDate: string | null;
  sequenceNo: number | null;
}

export interface InventoryFormDataTypes {
  id?: number | null;
  itemId?: number | null;
  storeLocationId: string | null;
  serialNo: string | null;
  qualityId: string | number | null;
  quantity: string;
  purchaseDateSequenceVal: PurchaseDataSequenceItemsTypes | null;
}

export interface InventoryListTypes {
  inventory: InventoryFormDataTypes[];
}

export interface PurchaseDataSequenceItemsTypes {
  label: string;
  value: string;
  purchaseDate: string | null;
  sequenceNumber: number | null;
}

/**
 * Quantity Types
 */

export interface QuantityListDataTypes {
  id: number;
  dateChanged: string;
  purchaseDate: string;
  sequenceNumber: string;
  quantity: number;
  totalQuantity: number;
  description: string;
  unitCost: number;
  totalCost: number;
  vendorId: number | null;
  changedBy: string;
  vendorName: string | null;
  storeLocationId: number;
  itemId: number;
  isAdded: boolean;
  isRemoved: boolean;
  isAdjusted: boolean;
}
export interface QuantityListItemTypes {
  totalQuantity: number;
  globalQuantity: number;
  itemQuantities: QuantityListDataTypes[];
}

export interface AddQuantityFormDataTypes {
  purchaseDate: Date | string;
  quantity: number | string;
  description: string;
  unitCost: number | string;
  id: null;
  vendorId: null;
  storeLocationId: number | null;
  itemid: number;
}

export interface AdjustQuantityFormDataTypes {
  itemId: number;
  quantity: number | null | string;
  description: string;
  storeLocationId: number | null;
}

export interface RemoveQuantityFormDataTypes {
  id: number;
  quantity: number | string;
  description: string;
  storeLocationId: number | null;
  itemId: number;
}

export interface QuantityDetailsTypes {
  id: number | null;
  unitCost: number | null;
  itemId?: number;
}
