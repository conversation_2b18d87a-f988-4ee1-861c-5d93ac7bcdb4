import { Path } from 'react-hook-form';

export interface StoreOptionsTypes {
  id: number;
  locationName: string;
  address1?: string | null;
  address2?: string | null;
  city?: string;
  stateName: string;
  countryName?: string;
  phone?: string;
  website?: string;
  zipCode?: string;
  storeCopyingOrder?: string;
  storeDateCalculation?: string;
  storeDeliveryCharges?: string;
  storeEmailSetting?: string;
  storeForm?: string;
  storeManagerApproval?: string;
  storeMiscCustomerSetting?: string;
  storeMiscOrderSetting?: string;
}

export interface StoreInformationTypes {
  businessName: string;
  locationName: string;
  address1: string;
  address2: string;
  city: string;
  stateId: string;
  stateName: string | null;
  zipCode: string;
  countryId: string;
  countryName: string | null;
  phone: string;
  website: string;
  id?: string | null;
  createdAt?: string | null;
  updatedAt?: string | null;
}

export interface StoreCopyingOrdersTypes {
  kitsToUse: string;
  priceToUse: string;
  timeFrameLimit: string;
  refetFuelCharge: boolean;
  storeLocationId?: number;
  id?: number;
  createdAt?: string | null;
  updatedAt?: string | null;
}

export interface StoreDateCalculationTypes {
  allowManuallyEnterDate: boolean;
  allowClearExistingDate: boolean;
  shipDatesCalculation: string;
  allowArrivalDate: boolean;
  allowAffectPickupDate: boolean;
  maxDaysBeforeDelivery: number;
  allowUpdateDate: boolean;
  allowEditingShipDate: boolean;
  storeLocationId?: number;
  storeLocationName?: string | null;
  createdAt?: string | null;
  updatedAt?: string | null;
  id?: number;
}

export interface StoreDeliveryChargesTypes {
  chargeCalculation: string;
  defaultCharges: number;
  defaultWebRateShip: string | null;
  defaultWebRateReturn: string;
  calculateDcCharge: boolean;
  useNoDeliveryCharge: boolean;
  displayDcWarning: boolean;
  dcIncreasePercent: number;
  requireDcConfirmation: boolean;
  storeLocationId?: number;
  storeLocationName?: string | null;
  createdAt?: string | null;
  updatedAt?: string | null;
  id?: number;
}

export interface StoreEmailSettingsTypes {
  emailFrom: string | null;
  faxService: string | null;
  allowInvoicePackagingList: boolean;
  addEventDescription: boolean;
  addSmtp: boolean;
  sendEsignDocs: boolean;
  storeLocationId?: number;
  storeLocationName?: string | null;
  createdAt?: string | null;
  updatedAt?: string | null;
  id?: number;
}

export interface StoreFormsTypes {
  purchaseOrder: string | null;
  faxPurchaseOrder: string | null;
  poRequest: string | null;
  faxPoRequest: string | null;
  equipmentTransfer: string | null;
  faxEquipmentTransfer: string | null;
  arStatement: string | null;
  faxArStatement: string | null;
  arAdjustment: string | null;
  faxArAdjustment: string | null;
  arInquiry: string | null;
  deliveryLocationDirections: string | null;
  creditCardReceipt: string | null;
  htmlConfirmation: string | null;
  htmlInvoice: string | null;
  htmlStatements: string | null;
  storeLocationId?: number;
  storeLocationName?: string;
  createdAt?: string;
  updatedAt?: string;
  id?: number;
}

export interface StoreManagerApprovalTypes {
  deliveryCharges: boolean;
  discountCharges: boolean;
  priceChanges: boolean;
  taxExempt: boolean;
  paymentTerms: boolean;
  storeLocationId?: number;
  storeLocationName?: string | null;
  createdAt?: string | null;
  updatedAt?: string | null;
  id?: number;
}

export interface StoreMultiLocationTypes {
  defaultStoreLocation: 'string';
  checkInventoryFor: 'string';
  selectSerializedItemsAcrossLocations: true;
  selectTaxCodeByOrderLocation: true;
}

export interface StoreOrderItemSettingsTypes {
  itemDeliveryLocations: boolean;
  includeQuotesInInventoryChecks: boolean;
  editItemInfo: boolean;
  showItemTypeColumn: boolean;
  showSubRentalColumn: boolean;
  zeroOutQty: boolean;
  combineLikeItems: boolean;
  packageComponentsShowPrice: boolean;
  retrieveSerialToFlip: boolean;
  includeSerialWhenOpeningOverBookedItem: boolean;
  surgePricing: boolean;
  storeLocationId: number;
  showSerialNumberColumn: boolean;
}

export interface StorePasswordsTypes {
  deleteOrder: string;
  unDeleteOrder: string;
  deletePayment: string;
  employee: string;
  orderComp: string;
  approvalShipment: string;
  reports: string;
  unpost: string;
  viewCreditCard: string;
  rfidTag: string;
  timecard: string;
  dataProcesses: string;
}

export interface StorePaymentsTypes {
  paymentType: string;
  validateCreditCard: boolean;
  declinePartialCreditCardApprovals: boolean;
  itemDeliveryLocations: boolean;
  creditCardConvenienceFees: string | number;
  debitCardConvenienceFees: string | number;
  achConvenienceFees: string | number;
  allowRefundingConvenienceFees: string;
  monthsBeforeOnlinePaymentLinkExpire: string;
  allowNonProcessedCreditCardPayments: boolean;
  storeLocationId: number;
}

export interface StorePrintSettingsTypes {
  allowLocalPackingLists: boolean;
  defaultPrintingToPreviousSelection: boolean;
  optionallyInvoiceOrdersPrintedToNonInvoicePrinters: boolean;
  printPackagingList: boolean;
  includeRevisionNumberInPDF: boolean;
  openPDFAfterCreated: boolean;
  itemPrintSequence: string;
  kitComponentPrintSeq: string;
  batchInvoicePrintOptions: string;
  itemPicsOnForms: boolean;
  allowPrintingToIncludeLockedOrders: boolean;
  maxDaysBeforeInvoicingWarning: number;
  processesPrintsortingSelection: boolean;
  storeLocationId: number;
}
export interface StoreTotalExpeditedFeeType {
  daysFrom: string;
  daysTo: string;
  fee: string;
}

// Define the error shape for dynamic keys
export interface ExpeditedFeeErrorShape {
  [key: string]: { message: string };
}

export interface ExpeditedFeeType {
  expeditedFee: StoreTotalExpeditedFeeType[];
  onOkClick: (value: StoreTotalExpeditedFeeType[]) => void;
  onCancel: () => void;
}

export interface StoreTotalsTypes {
  fuelSurchargeStartDate: string;
  productionFeeStartDate: string;
  expeditedFeeStartDate: string;

  defaultDamageWaiver: number;
  damageWaiverIncrease: number;
  itemFuelSurcharge: number;
  deliveryFuelSurcharge: number;
  minimumFuelSurcharge: number;
  productionFee: number;
  requestedDeposit: number;

  autoApplyDamageWaiverCharges: boolean;
  prodFeesAppliesToCall: boolean;
  customerLevelDM: boolean;
  customerLevelFuelSurcharge: boolean;
  requestedDepositItemSubTotal: boolean;
  taxSalesMissingEquipments: boolean;
  storeLocationId: number;
  storeExpeditedFees: StoreTotalExpeditedFeeType[] | [];
  allowedProdFeeCategories: number[];
}

export interface StoreWarehouse {
  shippingCompanies: string;
  returnCompany: string;
  shippingManager: boolean;
  showLastPickedOrders: boolean;
  showTruckNames: boolean;
  showOrderListDefault: string;
  splitOrders: boolean;
  usingWareHouseStatus: boolean;
  removeItineraryOrders: boolean;
  storeLocationId: number;
}

export interface CustomCleanupDaysType {
  sunday: string;
  monday: string;
  tuesday: string;
  wednesday: string;
  thursday: string;
  friday: string;
  saturday: string;
}

export interface StoreMiscSettings {
  serviceChargeAnnualPercentage: string;
  payRollStartDay: string;
  inventoryAdjustmentType: string;
  networkPath: string;
  quickBooks: boolean;
  customCleanUpdays: boolean;
  showQtyAvailable: boolean;
  salesTaxReportByCompletedDate: boolean;
  salesTaxFinalization: boolean;
  refreshCustomerList: boolean;
  refreshOrderList: boolean;
  customerNameSearchDefault: string;
  locationSearchDefault: string;
  sortCategoryListByName: boolean;
  reportSecurity: string;
  listOnlinePayments: boolean;
  itemOnWebDefault: boolean;
  showDriverNames: boolean;
  startDateOfUse: string;
  storeCleanupDaysConfig?: CustomCleanupDaysType | null;
}

export interface StoreMiscOrderSettings {
  defaultOrderType: string | null;
  warnOverdueCustomers: boolean;
  jobInfoAlert: boolean;
  allowCustDiscToEqOrd: boolean;
  updateRevision: boolean;
  newCustContactInfo: boolean;
  defaultDeliveryType: string | null;
  defaultDeliveryTypeCall: string | null;
  defaultSalesTaxCode: string | null;
  rate: string;
  defaultTaxCodeForCallOrder: boolean;
  creditLimitWarning: boolean;
  associateSaveShipInfo: boolean;
  copyDeliveryTypeInfo: boolean;
  promptOnSerialId?: boolean;
  lockSalePersonField: boolean;
  storeLocationId?: number;
  storeLocationName?: string | null;
  createdAt?: Date | null;
  updatedAt?: Date | null;
  id?: number;
}

export interface StoreMiscCustomerSetting {
  saveCustomPriceBy: string | null;
  usePricing: boolean;
  creditHoldMessage: string | null;
  storeLocationId?: number;
  storeLocationName?: string | null;
  createdAt?: string | null;
  updatedAt?: string | null;
  id?: number;
}

export interface StoreRfidSettings {
  rfid: boolean;
  rfidUpdatesPickupDate: boolean;
  rfidReturnAlerts: boolean;
  id?: number;
}

// Interface representing the mapping of store options tabs for both add and edit operations.
export interface AllTabTypeMap {
  information: StoreInformationTypes;
  'copying-orders': StoreCopyingOrdersTypes;
  'date-calculation': StoreDateCalculationTypes;
  'delivery-charges': StoreDeliveryChargesTypes;
  'email-settings': StoreEmailSettingsTypes;
  forms: StoreFormsTypes;
  'manager-approval': StoreManagerApprovalTypes;
  'multi-location': StoreMultiLocationTypes;
  'order-item-settings': StoreOrderItemSettingsTypes;
  passwords: StorePasswordsTypes;
  payments: StorePaymentsTypes;
  'print-settings': StorePrintSettingsTypes;
  totals: StoreTotalsTypes;
  warehouse: StoreWarehouse;
  'misc-settings': StoreMiscSettings;
  'misc-order-settings': StoreMiscOrderSettings;
  'misc-customer-settings': StoreMiscCustomerSetting;
}

// Array of SwitchField configurations with TypeScript types
export type SwitchFieldConfig<T> = {
  label: string;
  name: Path<T>;
  disabled?: boolean;
};
