export interface ProfileSecurity {
  userId: number;
  oldPassword: string;
  newPassword: string;
  confirmPassword: string;
}
export interface ProfileGeneralInformation {
  id: string;
  timezoneId: number | null;
  isActive: boolean;
  code: string;
  firstName: string;
  lastName: string;
  salesCommPct: number;
  phoneWork: string;
  // phoneHome: string;
  phoneCell: string;
  fax: string;
  emailWork: string;
  emailHome: string;
  startPage: string;
  defLocation: string;
  defaultLocationId: number | null;
  locSelection: number;
  profileImage: string;
  updateImage: boolean;
  idleLogoutMinutes: number | null;
  locationSelection: string | null;
  idleOrderMinutes: string;
}

export interface LoginProfileDetails {
  id?: string;
  isActive?: boolean;
  firstName: string;
  lastName: string;
  defLocation: string | null;
  defaultLocationId: number | null;
  startPage: string | null;
  idleLogoutMinutes: number | null;
  profileImage: string;
  updateImage: boolean;
}
