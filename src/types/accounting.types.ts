import { OptionsListTypes } from './common.types';
import { OptionTypes } from './purchase-order.types';
import { StorePaymentsTypes } from './store.types';

export interface AccountingTypes {
  accountingName: string;
  lastUpdated: string;
  updatedBy: string;
  to: string;
}

export interface AdjustmentListingTypes {
  id: number;
  adjustmentNo: number;
  customerName: string;
  adjustmentDate: string;
  total: number;
  adjustmenttype: string;
  orderdate: string;
}

export interface AdjustmentsTypes {
  id?: number | null;
  adjustmentNo?: number;
  adjustmentDate: string | Date;
  customerId: number;
  status?: string;
  adjustmentType: string;
  phone: string | null;
  taxableTotal: number | null;
  nonTaxableTotal: number | null;
  convenienceFees: number;
  salesTaxId: string;
  salesTaxRate?: number;
  tax?: number;
  total?: number;
  customerName?: string;
  isBadDebt: boolean;
  notes: string;
}

export interface AdjustmentDistributionTypes {
  adjustmentId: number;
  amount: number;
}

export interface OrderDistributionTypes {
  orderId: number;
  amount: number;
}

export interface SaveDistributionPayloadTypes {
  parentAdjustmentId: number;
  adjustmentDistribution: AdjustmentDistributionTypes[];
  orderDistribution: OrderDistributionTypes[];
}

export interface DistibutionTypes {
  orderNo: string;
  orderId: number;
  amount: number;
  isAdjustment: boolean;
}

export interface LoadAdjustmentDistributionTypes {
  id: number;
  adjustmentNo: string;
  adjustmentId: number;
  adjustmentAmount: number;
  distributions: DistibutionTypes[];
}

export interface AdjustmentsFormDataTypes
  extends Omit<AdjustmentsTypes, 'customerId' | 'phone'> {
  customerId: OptionTypes;
  phone: OptionTypes;
  adjustmentDistribution: AdjustmentDistributionTypes[];
  orderDistribution: OrderDistributionTypes[];
}

export interface AdjustmentAmountForm {
  id: number;
  adjustmentNo: number;
  orderNo: number;
  dateOfUse: string;
  total: number;
  balance: number;
  amountOrder: number;
  status: string | null;
  adjustmentId: number | null;
  orderId: number | null;
}

export interface CalculateAdjustmentForm {
  amount: number;
  saveDistributionLater: boolean;
  payments: AdjustmentAmountForm[];
}

export enum TransactionType {
  DEBIT = 'DEBIT',
  CREDIT = 'CREDIT',
  BEGINNING_BALANCE = 'BEGINNING_BALANCE',
  REFUND = 'REFUND',
  OVERPAYMENT = 'OVERPAYMENT',
  SERVICE_CHARGE = 'SERVICE_CHARGE',
}
export interface AllAccoutingType {
  name: string;
  toggle?: (value?: any) => void;
}

export interface inquiryPaymentDetailsType {
  id: number;
  paid: string;
  date: string;
  invoiceNo: string;
  type: string;
  amount: string;
  discount: number;
  payments: number;
  balance: number;
  isDeleted: string;
  userId: string;
}

export interface InquiryFormTypes {
  customerId: string | number;
  customer: string | any;
  customerFullName: string;
  phone: string | any;
  fax: string;
  creditHold: string;
  baseDiscount: number;
  balance: number;
  deposits: number;
  discount: number;
  agedCurrent: number;
  agedOver30: number;
  agedOver60: number;
  agedOver90: number;
  displayEnum: string;
  balanceTransactionEnum: string;
  balanceTransactionEnumVal: string;
  //inquiry payment details
  inquiryPaymentDetails: inquiryPaymentDetailsType[];
}

export interface PaymentInfoTyps {
  invoiceNo: string;
  payment: string;
  discount: string;
}
export interface InvoiceInfoTypes {
  date: string;
  amount: string;
  cardType: string;
  reference: string;
  totalPayment: string;
}

export interface ApplyCreditAmountForm {
  orderId: number;
  adjustmentNo: number;
  orderNo: number;
  total: number;
  balance: number;
  amountOrder: string | number;
}

export interface ApplyCreditForm {
  amount: number;
  payments: ApplyCreditAmountForm[];
}

export interface CCProcessingTypes {
  customer: string;
  orderNo: number;
  dateOfUse: string;
  total: number;
  balanceDue: number;
  paymentType: string;
  paymentAmount: number;
  paymentDiscount: number;
  paymentReference: string;
  creditCard: string;
  user: string;
  id: number;
}

export interface DeletePaymentFormTypes {
  id?: number;
  date: string;
  type: string;
  amount: number;
  originalAccount: string;
  emailReceipt?: string;
}

// accounting payment Process With Credit Card/ACH
export interface PaymentRequestListType {
  orderId: number | null;
  adjustmentId: number | null;
  amount: number;
}
export interface PaymentProcessWithCreditCardTypes {
  date: string;
  paymentTypeId: string;
  amount: number;
  billingName: string;
  billingAddress: string;
  billingCity: string;
  billingZipCode: string;
  email: string;
  stateId: number;
  country: string;
  updateCustomer?: boolean;
  reuseCC?: boolean;
  reuseOption?: string | null;
  bankAccountId: number | null;
  cardExpiry?: string;
  customerCardDetailId: number | null;
  creditAmount: number | null;
  paymentRequestList: PaymentRequestListType[];
}

export interface NewPaymentType {
  date: string;
  type: string;
  amount: number;
  charge: number;
  reference: string;
  bankAccountId: number | null;
  customerId: string;
  creditAmount: number;
  paymentRequestList: PaymentRequestListType[];
  distributionId: number | null;
  convFee: number;
  convFeeTax: number;
}

interface ProcessWithCreditCardTypes {
  state: boolean;
  action: string;
}

export interface PaymentProcessWithCreditCardProps {
  setOpen: React.Dispatch<React.SetStateAction<ProcessWithCreditCardTypes>>;
  data: NewPaymentType;
  toggleToNewPayment: () => void;
  refetchPaymentDetails: () => void;
  paymentTypeList: OptionsListTypes[];
}

export interface CalculateConvFeeTaxAllPayloadTypes {
  orderId: number;
  amount: number;
}

export interface CalculateConvFeeTaxAllTypes {
  orderId: number;
  amount: number;
  convFee: number;
  convFeeTax: number;
  total: number;
  chargableAmout: number;
}

export interface UnappliedAmountTypes {
  amount: number;
  payments: number;
  applyCredit: number;
  creditAmount: number;
}

export enum PaymentTypeEnum {
  ACH = 'ACH',
  CREDIT_CARD = 'Credit Card',
  DEBIT_CARD = 'Debit Card',
  ZERO_SUM = 'Zero Sum',
}

interface AppliedCreditChildrenTypes {
  orderId: number;
  creditAmount: number;
}
export interface PaymentsItemTypes {
  orderId: number | null;
  adjustmentId: number | null;
  adjustmentNo: number | null;
  orderNo: string | null;
  dateOfUse: string;
  total: string;
  balance: string;
  payment: number | string;
  amount: number;
  creditAmount: string | number;
  ccConvFee: number | string;
  tax: number | string;
  discount: string | number;
  stickyId?: string;
  isNonPosted?: boolean;
  adjustmentType?: string;
  appliedCreditChildren?: AppliedCreditChildrenTypes[];
  orderConvFee: number;
  refundConvFee: boolean;
}
export interface PaymentFormTypes {
  date: string;
  type: number;
  paymentTypeLabel: string;
  amount: number;
  referenceNo: string;
  bankAccountId: number;
  unapplied: number;
  payments: PaymentsItemTypes[];
  creditAmount: number | null;
  breakdown: {
    charge: number;
    convenienceFee: number;
    tax: number;
    totalCharge: number;
  };
  totalPayment: number;
  totalconvenienceFee: number;
  totalTax: number;
  totalAppliedCredit: number;
  distributionId: number | null;
  storePaymentConfig: StorePaymentsTypes;
}

export type ModalState = {
  withoutGravityPayment: boolean;
  checkCardDetails: boolean;
  isFullPayment: boolean;
  processCreditCardACH: boolean;
  applyCredit: boolean;
  overPayment: boolean;
  loadDistribution: boolean;
  isDistributionPresent: boolean;
  adjustmentOverBalance: boolean;
  refundConvFee: boolean;
  refundWithGravity: boolean;
};

export type ModalKeys = keyof ModalState;

export interface CreditCardDetailsTypes {
  scheme: string;
  type: string;
  brand: string;
  country: {
    numeric: string;
    alpha2: string;
    name: string;
    currency: string;
    latitude: number;
    longitude: number;
  };
  bank: {
    name: string;
  };
}

export enum CardTypeEnum {
  CREDIT = 'credit',
  DEBIT = 'debit',
}

export interface RefundConvFeeTypes {
  orderId: number | null;
  orderNo: string;
  refundAmountType: string;
  refundIncludingConvFee: number;
  refundOriginalAmount: number;
}
