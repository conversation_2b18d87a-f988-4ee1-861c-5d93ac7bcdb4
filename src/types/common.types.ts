import { ReactNode } from 'react';
import { FieldValues, Path } from 'react-hook-form';

// Type for resource identifiers used in API endpoints.
export type ResourceId = string | number | null | boolean;

export type OptionsListTypes = {
  label: string;
  value: string | boolean;
};

export interface CheckboxListType<T extends FieldValues> {
  name: Path<T>;
  label: string;
  value?: string;
}

export interface PaginationType {
  pageIndex: number;
  pageSize: number;
}

export interface ErrorResponse {
  data: {
    type: string;
    title: string;
    status: number;
    detail?: string;
    message?: string;
  };
  status?: number;
}

export interface MapItems {
  data: any;
  labelKey: string;
  labelKey2?: string;
  valueKey: string;
}

export interface PaginationStateTyp {
  pageIndex: number;
  pageSize: number;
}

export interface SortingStateType {
  id: string;
  desc: boolean;
}
export interface PaginationFilterType {
  field: string;
  value: string | number | boolean;
  operator?: string;
}
export interface PaginationOptionsType {
  pagination: PaginationStateTyp;
  sorting: SortingStateType[];
  filters?: PaginationFilterType[];
}
export interface ApiResponseDto<T> {
  pagination?: {
    totalCount: number;
    pageNumber: number;
    pageSize: number;
  };
  success: boolean;
  message: string;
  statusCode: number;
  data: T;
}

export enum RolesEnum {
  SUPER_ADMIN = 'SuperAdmin',
  ADMIN = 'Admin',
  NORMAL_USER = 'User',
}

export interface FilterItem {
  label: string;
  value: string;
  name: string;
  tagValue: string;
  operator: string;
}

export interface ZipCodePatterns {
  US: {
    basic: RegExp;
    extended: RegExp;
    any: RegExp;
  };
  CA: RegExp;
}

export interface DropdownMenuListType {
  label?: string | ReactNode;
  onClick?: () => void;
  icon?: ReactNode;
  className?: string;
  subMenu?: DropdownMenuListType[];
  disabled?: boolean;
}

/**
 * Payload for a POST request with pagination and filtering
 */
export interface PaginationFilterPayload {
  pageNumber: number;
  pageSize: number;
  sortBy: string;
  sortAscending: boolean;
  filters: PaginationFilterType[];
}

/**
 * Filter criteria for POST request
 */
export interface FilterDTO {
  field: string;
  value: string;
  operator: string;
}

/**
 * Breadcrumb type
 * @label: string
 * @value: string
 */
export interface BreadcrumbType {
  label: string;
  value: string;
}

// CalendarView date slot type
export interface AvailabilityEntry {
  date: string | Date;
  [key: string]: string | number | Date | undefined;
  className?: string;
  labelClassName?: string;
  selectedClassName?: string;
}

//  Common filter operators used for comparison in search inputs.
export enum OperatorType {
  Contains = 'Contains',
  Equals = 'equals',
  StartsWith = 'startswith',
}

export type ReuploadTypes = {
  id: number | null;
  state: boolean;
};
