export interface ItemType {
  label?: string;
  value?: string;
}
export interface AvailabiltyCalendarInfoTyps {
  orderId: number;
  itemId: ItemType;
  storeLocationId: string;
  date: string;
  includeQuotes: string | boolean;
  includeSubRentAndPO: boolean;
  includePastOrders: boolean;
  description: string;
  cleanupDays: string;
  unitPrice: string;
  owned: string;
  availableToday: string;
  availabilities: any[];
  deliveryDate: string;
  pickupDate: string;
}
