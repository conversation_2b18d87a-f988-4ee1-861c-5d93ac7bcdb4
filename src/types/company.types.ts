export interface CompanyFormData {
  id?: number;
  companyName?: string;
  useMultiloc?: boolean;
  itinerarynDist?: number;
  isIdleLogout?: boolean;
  idleLogoutMinutes?: number | null;
  smtpServer?: string;
  smtpPort?: number;
  smtpUsername?: string;
  smtpPassword?: string;
  emailFrom?: string;
  emailCc?: string;
  enableSsl?: boolean;
  readReceipt?: boolean;
  emailLimit?: number;
  body?: string;
  stBody?: string;
  emailFromItins?: string;
  ccProcessingType?: string | number;
  paymentGateway?: string;
  gravityOid?: string;
  gravityAuthToken?: string;
  gravityHppUsername?: string;
  gravityHppPassword?: string;
  gravityQueueId?: string;
  gravityUseDevices?: boolean;
  gravityLevel2Data?: boolean;
  esignType?: string | number;
  ptSignApiKey?: string;
  useFedEx?: boolean;
  fedExUserKey?: string;
  fedExPassword?: string;
  fedExAcctNum?: string;
  fedExMeter?: string;
  modifiedOn?: string;
  isActive?: boolean;
}

interface CreditCard {
  ccProcessingType: string | number;
  gravityOid?: string;
  gravityAuthToken?: string;
  gravityHppUsername?: string;
  gravityHppPassword?: string;
  gravityQueueId?: string;
  gravityUseDevices?: boolean;
  gravityLevel2Data?: boolean;
}

interface WebRate {
  useFedEx?: boolean;
  fedExUserKey?: string;
  fedExPassword?: string;
  fedExAcctNum?: string;
  fedExMeter?: string;
}
interface Esign {
  esignType?: string | number;
  ptSignApiKey?: string;
}

export interface CompanyData {
  id?: number;
  companyName?: string;
  useMultiloc?: boolean;
  itinerarynDist?: number;
  isIdleLogout?: boolean;
  idleLogoutMinutes?: number | null;
  creditCard: CreditCard;
  esign: Esign;
  webRate: WebRate;
}

// Enum for Gravity Checkbox Options
export enum GravityCheckboxOption {
  USE_DEVICES = 'gravityUseDevices',
  LEVEL_2_DATA = 'gravityLevel2Data',
}

// Enum for Esign Types
export enum EsignType {
  NONE = '0',
  PTSIGN = '1',
}

// Enum for FedEx Options
export enum FedExOption {
  YES = 'yes',
  NO = 'no',
}

// Enum for CCProcessingType Types
export enum CCProcessingType {
  NONE = '0',
  GRAViTY_PAYMENTS = '1',
}

// Enum for Tabs Name
export enum CompanyTabsEnum {
  COMPANY_DETAILS = 'company-details',
  CREDIT_CARD_PROCESSING = 'credit-card-processing',
  WEB_RATE_SERVICES = 'web-rate-services',
  E_SIGN = 'e-sign',
}
