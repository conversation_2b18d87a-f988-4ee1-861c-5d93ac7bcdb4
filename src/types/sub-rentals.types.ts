import { OptionTypes } from './purchase-order.types';

export interface SubRentalInformationTypes {
  id: number | null;
  orderStatus: string;
  orderDate: string;
  pickupReturnStatus: string;
  pickupDate: string;
  pickupInfo: string;
  returnDate: string;
  returnInfo: string;
  resvNo: string;
  name: string; // name as contact
  storeLocationId: number;
  enteredById: number;
  orderId: { label: string; value: string | number };
  isDeleted: boolean;
  deletedBy: string;
  deletedOn: string;
  deletedReason: string;

  // RENTED FROM details
  rentedFromId: { label: string; value: string | number };
  phone: string;
  fax: string;
  address: string;
  city: string;
  state: string;
  zipcode: string;
  country: string;
  specInst1: string;
  contactType: number;
  contactTypeText: string;
  customerVendorDetail: CustomerVendorLookupTypes;
}

export interface SubRentalItemListTypes {
  id?: number | null;
  orderSubrentId?: number | string;
  itemId?: number;
  itemIdLabel?: string;
  itemDescription?: string;
  quantity: number | string | null;
  unitPrice?: number | string;
  rowNo?: number;
  createdAt?: string;
  updatedAt?: string;
  isDeleted?: boolean;
  total?: string | number;
  rowIndex?: number;
  isRemove?: boolean;
  listId?: number | string | null;
  description?: string;
}

export interface SubRentalItemDetailsFormDataTypes
  extends Omit<SubRentalItemListTypes, 'itemId'> {
  itemId: OptionTypes | null;
  disabledCheckBox?: boolean;
}
export interface SubRentalItemDetailsTypes {
  items: SubRentalItemDetailsFormDataTypes[];
}

export interface SubRentalTabs {
  information: SubRentalInformationTypes;
  'item-details': SubRentalItemDetailsTypes;
}

export interface CustomerVendorLookupTypes {
  srNo: number;
  id: number;
  fullName: string;
  phone: string;
  fax: string;
  address1: string;
  address2: string;
  city: string;
  state: string;
  zipcode: string;
  country: string;
  specialInstructions: string;
  contactType: number;
  isActive: boolean;
}

export interface GetDefaultSubRentalValuesParams {
  data: SubRentalInformationTypes;
  currentDate: string;
  enteredById: string | number;
  storeLocationId: string | number;
}
