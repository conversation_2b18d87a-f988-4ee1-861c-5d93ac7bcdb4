import { ProfileGeneralInformation } from './user-options.types';

export interface LoginRequest {
  email: string;
  password: string;
  rememberMe: boolean;
}

export interface LoginResponse {
  token: string;
  role: string;
  rememberMe: string;
  // userId: string;
  user: ProfileGeneralInformation;
}

export interface User {
  id: string;
  isActive: boolean;
  firstName: string;
  lastName: string;
  defLocation: string | null;
  defaultLocationId: number | null;
  startPage: string | null;
  idleLogoutMinutes: number | null;
  profileImage: string;
  timeZoneOffset?: string;
  idleOrderMinutes: string;
}

// Define the response type for the refresh token API call
export interface RefreshResponse {
  token: string;
  accessToken: string;
  expiresIn: number;
  refreshToken: string;
}

export interface ForgotPasswordRequest {
  email: string;
}

export interface ForgotPasswordResponse {
  success: boolean;
  message: string;
}

export interface CommonResponseType<T> {
  success: boolean;
  statusCode: number;
  message: string;
  data: T;
}
