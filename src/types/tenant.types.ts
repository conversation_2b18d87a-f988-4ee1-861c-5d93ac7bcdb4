export interface TenantType {
  id: string | number | undefined;
  clientName: string;
  timezoneId: string;
  isActive: string;
  location: string;
  contactName: string;
  contactEmail: string;
  contactPhone: string;
  website: string;
  addressLine1: string;
  addressLine2?: string | null;
  city: string;
  state: string;
  zipcode: string;
  schemaName: string;
  password: string;
  token: string;
  confirmPassword: string;
  country: string;
}

export interface TenantListType {
  id: number;
  clientName: string;
  schema_name: string;
  timezoneId: number;
  isActive: boolean;
  password: string;
  location: string;
  contactName: string;
  contactEmail: string;
  contactPhone: string;
  website: string;
  addressLine1: string;
  addressLine2?: string | null;
  city: string;
  state: string;
  zipcode: string;
  isverified: boolean;
}

export interface ClientDTO {
  clientName: string;
  timezoneId: string;
  isActive: string;
  location: string;
  contactName: string;
  contactEmail: string;
  contactPhone: string;
  website: string;
  addressLine1: string;
  addressLine2?: string | null;
  city: string;
  state: string;
  zipcode: string;
  country: string;
  schemaName: string;
  password: string;
  token: string;
  confirmPassword: string;
}
