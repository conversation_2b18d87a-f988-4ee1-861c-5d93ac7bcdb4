import { FC } from 'react';

export interface MenuItem {
  icon?: FC<{ iconColor?: string }>;
  label: string;
  to: string;
  children?: MenuItem[];
}

export interface SidebarItemProps extends MenuItem {
  isCollapsed?: boolean;
  onItemClick?: () => void;
  children?: any;
  icon?: FC<{ iconColor?: string }>;
}

export interface SidebarProps {
  isCollapsed?: boolean;
  setOpenMenu?: (open: boolean) => void;
  setIsCollapsed?: React.Dispatch<React.SetStateAction<boolean>>;
}
