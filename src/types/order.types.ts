import { Path } from 'react-hook-form';
import { StoreDateCalculationTypes } from './store.types';

interface OptionTypes {
  label: string;
  value: string;
}

export interface billToTypes {
  customerId: OptionTypes | null;
  name: string;
  customerPhone: OptionTypes | null;
  orderedBy: OptionTypes | null;
  orderEmail: string;
  orderPhone: OptionTypes | null;
  orderFax: string;
  address1: string;
  address2: string;
  city: string;
  state: string;
  country: string;
  zipCode: string;
  customerInstructions: string;

  stateId?: number | string;
  countryId?: number | string;
}
export interface shipToType {
  shipLocation: OptionTypes | null;
  phone: OptionTypes | null;

  contact: string;
  contactEmail: string;
  contactPhone: string;
  address1: string;
  address2: string;
  city: string;
  zipCode: string;
  additionalInstructions: string;
  state: string;
  stateId?: number | string;
  countryId?: number | string;
}

export interface DeliveryOrderType {
  id: number;
  deliveryDate: string;
  deliveryDay: string;
  deliveryInfo: string;
  orderSetupId: string;
  deliveryTimeIn: string;
  deliveryTimeOut: string;
  pickupDate: string;
  pickupDay: string;
  pickupInfo: string;
  takedownId: string;
  pickupTimeIn: string;
  pickupTimeOut: string;

  arrivalDate: string;
  arrivalDay: string;
  returnShipDate: string;
  returnShipDay: string;
}

export interface ShipOrderType {
  id: number;
  shipDate: string;
  shipDay: string;
  shipInfo: string;
  orderSetupId: string;
  shipTimeIn: string;
  shipTimeOut: string;
  returnArrivalDate: string;
  returnArrivalDay: string;
  returnInfo: string;
  takedownId: string;
  returnTimeIn: string;
  returnTimeOut: string;

  arrivalDate: string;
  arrivalDay: string;
  returnShipDate: string;
  returnShipDay: string;
}

export interface WillCallOrderType {
  id: number;
  pickupDate: string;
  pickupDay: string;
  pickupInfo: string;
  orderSetupId: string;
  pickupTimeIn: string;
  pickupTimeOut: string;
  returnDate: string;
  returnDay: string;
  returnInfo: string;
  takedownId: string;
  returnTimeIn: string;
  returnTimeOut: string;

  arrivalDate: string;
  arrivalDay: string;
  returnShipDate: string;
  returnShipDay: string;
}

// user default store info
interface UserDefaultStoreInfoTypes {
  id: number;
  defaultDeliveryType: number;
  defaultDeliveryTypeCall: number;
  showSerialNumberColumn: boolean;
  storeCountryId: number;
}

export interface OrderInformationTypes {
  id: number;
  orderNo: number;
  revision: number;
  orderType: string;
  storeLocationId: string;

  dateOfUseFrom: string;
  dayOfUseFrom: string;
  timeOfUseFrom: string;

  dateOfUseThru: string;
  timeOfUseThru: string;
  dayOfUseThru: string;

  deliveryTypeId: number;
  eventDescription: string;
  eventTypeId: number;
  eventFrequency: string;
  rentDays?: number;
  eventPurchase?: number;
  updatedAt: string;
  isDeleted: boolean;
  originType: string;
  deletedBy: string;
  takenBy: string;
  reasonOfDeletion: string;
  // Missing equipment info
  missingInfo: string;

  // Order Type
  selectedOrderType: string;

  deliveryOrder: DeliveryOrderType;
  shipOrder: ShipOrderType;
  willCallOrder: WillCallOrderType;

  // Billing & Shipping Info
  billTo: billToTypes;
  shipTo: shipToType;
  storeDateCalculation: StoreDateCalculationTypes;
  updateData: boolean;

  // user default store info
  userDefaultStoreInfo: UserDefaultStoreInfoTypes;
  recalculateDate: boolean;
  surgeRateId: number | null;
  surgePricing: boolean;
  itemPrintSequence: string;
  kitComponentPrintSeq: string | null;
  shippingManager: boolean;
  status?: string;
  orderEntryReadOnly: boolean;
}

export interface AllTabTypeMap {
  information: OrderInformationTypes;
}
export interface TotalPaymentsTypes {
  orderId: string;
  total: string;
  discount: number;
  subTotal: string;
  deliveryCharge: string;
  labour: string;
  damageWaiver: string;
  fuelSurcharge: string;
  productionFee: string;
  expeditedFee: string;
  ccConvenienceFee: string;
  tax: number;
  grandTotal: number;
  dwPercent: number;
  dwCalculation: number;
  requiredDeposit: string;
  isTaxExempted: boolean;
  chargeConvFee: boolean;
  paymentType: string;
  paymentTerms: PaymentTerm[];
  taxCode: TaxCode;
  orderTaxBreakDown: TaxBreakdown[];
  compedOrder: string;
  writeOffDiscount: string;
  amountPaid: string;
  balanceDue: string;
  isDwEdited: boolean;
  isProdFeeEdited: boolean;
  isExpeditedFeeEdited: boolean;
  isFuelChargeEdited: boolean;
  salesTaxCodeId: string;
  discountPercentage: string;
  discountAmount: number;
  amountSubjectToDiscount: string;
}

export interface PaymentTerm {
  id: number;
  term: string;
}

export interface TaxCode {
  code: string;
  rate: number;
  exempt: string;
}

export interface TaxBreakdown {
  rateCode: string;
  state: string;
  baseRate: number;
  amount: number;
}

export interface TotalPaymentsDeliveryCharge {
  id: number;
  standardRates: string;
  discountAmount: number;
  shipChargeBasedOn: string;
  discountPercentage: string;
  shipTransitTime: string;
  returnDeliveryChargeOption: string;
  returnChargeBasedOn: string;
  returnDeliveryCharge: string;
  returnTransitTime: string;
  deliveryChargeOption: string;
  chargeBasedOn: string;
  standardRate: string;
  transitTime: string;
  ship: string;
  return: string;
  shipDeliveryChargeOption: string;
  shipCharge: string;
  returnCharge: string;
}

type StatusInfoTypes = {
  vehicle: string;
  driver: string;
  routeDescription: string;
  driverPhone: string;
  startTime: string;
  travelTime: string;
  comment: string;
  estimatedDelivery: string;
  setupTime: string;
  actualDelivery: string;
  actualSetup: string;
};

export interface StatusTypes {
  deliverPickupStatus: string | null;
  status: string;
  dateOrdered: string;
  deliveryOption: string;
  enteredBy: string | null;
  signed: boolean | null;
  inventoryQuality: string;
  approvedForShipment: boolean;
  deliveryStatus: StatusInfoTypes;
  pickupStatus: StatusInfoTypes;
}

export interface AdditionalInfoTypes {
  customerid: number | null;
  orderId: string | number | null;
  jobNo: number;
  jobTitle: string;
  jobManager: string;
  insuranceValue: number;
  siteInfo: string;
  asInstall: string;
  asTeardown: string;
  description: string;
  installLeader: number;
  leadtype: string;
  buildingPermit: boolean;
  buildingPermitDate: string | Date | null;
  cadDrawing: boolean;
  cadDrawingFileLocation: string;
  tentLocation: string;
  undergroundLocate: boolean;
  undergroundDate: string | Date | null;
  undergroundNo: string;
  install: boolean;
  installStartDate: string | Date | null;
  installEndDate: string | Date | null;
  dismantleLeader: number;
  dismantleStartDate: string | Date | null;
  dismantleEndDate: string | Date | null;
  totallabourcost: number | null;

  // For Ship Tab
  arrivalDate: string | Date;
  returnShipDate: string | Date;

  // For Delivery Tab
  // deliveryDate: string | Date;
  // pickupDate: string | Date; // pickupDate is Same for both Delivery & Will Call Tab

  // For Will Call Tab
  // returnDate: string | Date;

  id: number;
  isDeleted: boolean;
  buildingPermits: string;
  undergroundLocationDesc: string;
  dismantle: boolean;

  assignedJobNo: number;
  orderInfo?: {
    data: AdditionalInfoOrderInfoTypes[];
    totalAmount: number;
  };
  subRentInfo?: {
    data: AdditionalInfoSubRentalInfoTypes[];
    totalAmount: number;
  };
}

export interface SalesReferralsCustomerTypes {
  customer_id: number | string | null;
  first_name: string;
  last_name: string;
  address1: string | null;
  address2: string | null;
  city: string | null;
  state: string | null;
  zipcode: string | null;
  country: string | null;
  tel1: string | null;
  reftype_id: number;
  code: string;
  description: string;
  referralcommpct: number;
}

export interface SalesReferralsType {
  id?: number;
  orderId: number;
  orderCommissionTotal: number;
  custId: number | string | null;
  custReferredBy: string | null;
  custPaymentReference: string | null;
  custPaymentDate: string | null;
  custReferralCommissionPercentage: number | null;
  custReferralCommissionAmount: number | null;
  salespersonId: string | null;
  salesPaymentReference: string | null;
  salesPaymentDate: string | null;
  salesCommissionPercentage: number;
  salesCommissionAmount: number | null;
  isApplyDiscountAsReferral: boolean;
  lockSalesPersonField: boolean;
  customerReferralDTO: SalesReferralsCustomerTypes;
  applyDiscountReferral: boolean;
  customerSearch?: string;
  status?: string;
  searchField?: string;
}

export interface RfidTagsType {
  id: number;
  itemID: string;
  description: string;
  location: string;
  quantity: string;
  tagID: string;
  returned: string;
  status: string;
  lastOrder: string;
}

export interface OrderNotesType {
  id: number;
  note: string;
  createdAt: string;
}
export interface LinkedFilesType {
  id: number;
  fileName: string;
  createdAt: string;
}
export interface ChecklistType {
  id: number;
  icon: string;
  description: string;
  dateCreated: string;
  actions: string;
}

// Array of SwitchField configurations with TypeScript types
export type SwitchFieldConfig<T> = {
  label: string;
  name: Path<T>;
  disabled?: boolean;
  description?: string;
};

// Order Type Tab ENUM
export enum ORDERT_TYPE_TAB {
  DELIVER = 'DeliveryOrder',
  SHIP = 'SHIP',
  WILL_CALL = 'WILL_CALL',
}

// Status Info Tab ENUM
export enum STATUS_INFO_TAB {
  DELIVER = 'DeliveryStatusInfo',
  PICKUP = 'PickupStatusInfo',
}

// Quick Discounts
export enum QuickDiscounts {
  Zero = '0',
  Five = '5',
  Ten = '10',
  Fifteen = '15',
  Twenty = '20',
  TwentyFive = '25',
  Fifty = '50',
  Hundred = '100',
}

// Order Type  types
export type TabType =
  | ORDERT_TYPE_TAB.DELIVER
  | ORDERT_TYPE_TAB.SHIP
  | ORDERT_TYPE_TAB.WILL_CALL
  | STATUS_INFO_TAB.DELIVER
  | STATUS_INFO_TAB.PICKUP;

// Item Types
export interface OrderItemListTypes {
  itemId: number;
  orderId?: number;
  serialNumber: string | null;
  quantity: number | string | null;
  description: string;
  type?: string;
  subRental: boolean;
  price: number;
  total?: number;
  itemIdString?: string;
  orderNumber?: number;
  parentId?: number | null;
  id: number | null;
  isOverbooked: boolean;
}

export interface OrderItemTypes {
  orderItemList: OrderItemListTypes[];
  totalCube: number;
  totalWeight: number;
}

export type ItemIdTypes = {
  label: string;
  value: string;
};

export interface ItemFormDataTypes {
  orderId?: number | string;
  serialNumber: string | null;
  quantity: number | string | null;
  description: string;
  type?: string;
  subRental: string;
  price: number | string;
  total: string | number;
  orderNumber?: number;
  parentId: number | null | string;
  listId: number | string | null;
  itemId: ItemIdTypes | null;
  // isModified: boolean;
  disabledCheckBox?: boolean;
  itemIdString?: string;
}

// Item List Types
export interface ItemListTypes {
  items: ItemFormDataTypes[];
  totalCube: number;
  totalWeight: number;
}

export interface OrderItemLookupTypes {
  id: number;
  itemId: string;
  description: string;
  quantity: string;
  price?: number | string;
  itemType: string;
}
export interface OrderItemLookupFormTypes {
  id: number | null;
  itemId: {
    label: string;
    value: string;
  };
  quantity: number;
  description: string;
  childItemId?: number;
  price?: number | string;
  total?: number;
  itemType: string;
}

export interface CheckListItemsType {
  id: number;
  checkListId: number;
  description: string;
  notes: string;
  checkedAt: string;
  isChecked: boolean;
  user: string;
}
// check list item type list
export interface CheckListTypes {
  items: CheckListItemsType[];
}

// Missing Equipment ENUM
export enum MISSING_EQUIPMENT {
  CREATE = 'createNew',
  UPDATE = 'updateExisting',
  UPDATE_FOR_CUSTOMER = 'updateExistingForCustomer',
}
export type MISSING_EQUIPMENT_TABS =
  | MISSING_EQUIPMENT.UPDATE
  | MISSING_EQUIPMENT.UPDATE_FOR_CUSTOMER;

export enum MISSING_EQUIPMENT {
  CREATE_MISSING_QUIPMENT = 'createMissingEquipment',
  MISSING_ITEMS = 'missingItems',
}
// missing equipment breadcrumb
export type MISSING_EQUIPMENT_BREADCRUMB =
  | MISSING_EQUIPMENT.CREATE_MISSING_QUIPMENT
  | MISSING_EQUIPMENT.MISSING_ITEMS;

// SubRenting Dialog Enum
export enum SubRentingTabEnum {
  SUB_RENTING = 'sub-renting',
  NEW_SUB_RENT = 'new-sub-rent',
  CUSTOMER_LOOKUP = 'customer-lookup',
}
// Missing Equipment -> Missing Items types
export interface MissingItemsFormTyps {
  id: number;
  itemId: number;
  itemIdString: string;
  orderId: number;
  serialNumber: number;
  quantity: number;
  description: string;
  missingQty: number;
  replacementCharge: number;
  total: number;
  returnQty?: string;
}

export interface MissingItemsTyp {
  items: MissingItemsFormTyps[];
}

// Deletion info type
export interface DeleteOrderType {
  id: string;
  deletedBy: string;
  takenBy: string;
  updatedAt: string;
  reasonOfDeletion: string;
  isDeleted: boolean;
  originType: string;
}

export interface OrderDirectionsTyps {
  deliveryDefaultLocation: string;
  directionText: string;
  orderEntryReadOnly: boolean;
}
// SubRenting BreadCrumb Enum
export enum SubRentingBreadCrumbEnum {
  SUB_RENTING = 'Sub Renting',
  NEW_SUB_RENT = 'New Sub Rent',
  CUSTOMER_LOOKUP = 'Customer Lookup',
}
export enum OrderNoteTabEnum {
  ORDER_NOTES = 'order-notes',
  ADD_NOTE = 'add-note',
}

// Cash Sale Types
export interface CashSaleTypes {
  customerId?: number;
}

// order module , order type Deliver / Ship / Will Ca;;
export interface OrderTypeTabs {
  setupList: OptionTypes[];
  takedownList: OptionTypes[];
  loading: boolean;
}

export interface OverbookingItemInfoType {
  id: number;
  itemID: string;
  serialNumber: string;
  location: string;
  itemDescription: string;
  deliveryDate: string;
  pickupDate: string;
  display: string;
  orderType: string;
  cleanUpDays: string;
  unitPrice: string;
  subRented: string;
  owned: string;
  ordered: string;
  rented: string;
  available: string;
}

// Kit Components
export enum ItemTypeEnum {
  SUB_RENTAL = 'sub-rental',
  RETURN_QTY = 'return-qty',
  OVERBOOKED_ITEM = 'overbooked-item',
  AVAILABILITY_INFO = 'availability-info',
  KIT_ITEM = 'kit-item',
  ADDITIONAL_ITEM_INFO = 'additional-item-info',
  CHANGE_ITEM = 'change-item',
  CHANGE_SORTING = 'change-sorting',
}

// Overbooked Item Info
export interface OverBookedItemInfoTypes {
  itemId: { label: string; value: string };
  description: string;
  cleanupDays: number;
  unitPrice: string;
  owned: number;
  rented: number;
  subRented: number;
  ordered: number;
  available: number;
  serialNo: number;
  displayType: string;
  orderType: string;
  storeLocationId: string;
  deliveryDate: string;
  pickupDate: string;
  includeQuotes: boolean;
}

// Additional Info (Assign Jobs)
export interface AssignJobData {
  jobNo: number;
  description: string;
  installationDate: string;
  dismantleDate: string;
  customerid?: number;
}

// Additional Info (Assign Orders)
export interface OrderInfo {
  orderNo: number | string;
  dateOfUseFrom: string;
  eventDescription: string | null;
}

// Additional Info (breakdown)
export interface BreakdownInfo {
  employee: string;
  date: string;
  cost: number;
}

// Additional Info (Ship Info)
export interface ShipItemDetails {
  id: number;
  qtyShipped: number;
  isDeleted: boolean;
  orderId: number;
  splitNo: number;
  rowNo: number;
  item: string;
  // itemId: number;
  orderItemId: number;

  itemDescription: string;
  qtyOrdered: number;
  shipBoxNo: number;
  qtyTobeShipped: number;
  qtyTobeReturned: number;
  qtyReturn: number;
  returnBoxNo: number;
  qtyDamaged: number;
  details?: number;

  // rows?: {
  //   id: number;
  //   item: string;
  //   // itemId: number;
  //   orderItemId: number;
  //   orderId: number;
  //   qtyOrdered: number;
  //   splitNo: number;
  //   qtyTobeShipped: number;
  //   qtyTobeReturned: number;
  //   qtyShipped: number;
  //   shipBoxNo: number;
  //   qtyReturn: number;
  //   returnBoxNo: number;
  //   qtyDamaged: number;
  //   details?: number;
  //   qtyReturned?: number;
  //   status?: string;
  //   price?: number;
  //   notes?: string;
  // }[];
}

export interface ShipmentBoxInfo {
  id: number;
  isDeleted: boolean;
  orderId: number;
  boxNo: number;
  boxType: string;
  shipCompanyId: number;
  shipCompany: string;
  itemsWeight: number;
  boxWeight: number;
  date: string;
  printLabel: string;
  trackingNo: string;
  usNo: string;
  packer: string;
  listShipCharg: string;
  actualShipCharg: string;
  flag?: boolean;
  void?: boolean;
}

export interface ShipInfoFormTypes {
  info: ShipItemDetails[];
  shipStatus: string;
  isCreatingME: boolean;
}

export interface ShipBoxFormTypes {
  box: ShipmentBoxInfo[];
}

export interface ShipmentDetails {
  shipStatus: string;
  itemDetails: ShipItemDetails;
  boxInfo: ShipmentBoxInfo;
}

// filter option type
export interface FilterOptionTypes {
  field: string;
  value: string | boolean;
  operator: string;
}

// Sales & Referrals customer search lookup dialog
export interface CustomerSearchDataTypes {
  customer_id?: string;
  name: string;
  type: string;
  phone: string;
  address1: string;
  city: string;
  state: string;
  zipcode: string;
  isactive: boolean;
  referralcommpct?: string;
}

export interface Customer {
  customer_id?: string;
  full_name?: string;
  custtype?: string;
  tel1?: string;
  address1?: string;
  city?: string;
  state?: string;
  zipcode?: string;
  isactive?: boolean | string;
}
// userDefaultStoreInfo
export interface StoreSettings {
  defaultOrderType: string;
  warnOverdueCustomers: boolean;
  jobInfoAlert: boolean;
  allowCustDiscToEqOrd: boolean;
  updateRevision: boolean;
  newCustContactInfo: boolean;
  defaultDeliveryType: number;
  defaultDeliveryTypeCall: number;
  defaultSalesTaxCode: string | null;
  rate: number;
  defaultTaxCodeForCallOrder: boolean;
  creditLimitWarning: boolean;
  associateSaveShipInfo: boolean;
  copyDeliveryTypeInfo: boolean;
  promptOnSerialId: boolean;
  lockSalePersonField: boolean;
  storeLocationId: number;
  storeLocationName: string | null;
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
  id: number;
  isDeleted: boolean;
  itemDeliveryLocations: boolean;
  includeQuotesInInventoryChecks: boolean;
  editItemInfo: boolean;
  showItemTypeColumn: boolean;
  showSubRentalColumn: boolean;
  zeroOutQty: boolean;
  combineLikeItems: boolean;
  packageComponentsShowPrice: boolean;
  retrieveSerialToFlip: boolean;
  includeSerialWhenOpeningOverBookedItem: boolean;
  surgePricing: boolean;
  showSerialNumberColumn: boolean;
}
export interface OrderDiscountsTypes {
  categoryId?: number;
  department?: string;
  category?: string;
  discount?: number;
  id?: number;
  departmentId?: number;
  orderId?: number;
  catdesc?: string;
  deptdesc?: string;
}

// Additional info (Order Info Table)
export interface AdditionalInfoOrderInfoTypes {
  orderId: number;
  amount: number;
}

// Additional info (Sub-Rental Info Table)
export interface AdditionalInfoSubRentalInfoTypes {
  id: number;
  amount: number;
}

// Additional info (Crew Details)
export interface CrewDetailsListsTypes {
  id: number;
  date: string;
  weekDay: string;
  people?: number;
  hours?: number;
  isDeleted?: boolean;
  listId?: number;
  lead: string | number;
  departmentId: number;
  orderId: number;
}

// Additional info (Crew Details)

export interface CrewDetailsTypes {
  rows: CrewDetailsListsTypes[];
  department?: string;
}

// Additional info (Crew Details)
export interface CrewDetailsProps {
  onClose: () => void;
}

// New Payment Types
export interface NewPaymentDTO {
  date: string;
  type: string;
  amount: number;
  tax: number;
  reference: string;
  bankAccountId: number | null;
  charge?: string;
  name: string;
  email: string;
  address: string;
  city: string;
  state: number;
  zipcode: string;
  customerId: string;
  // creditCardId?: string;
  // expirationDate?: string;
  // cardCode?: string;
  // isChecked: boolean;
  refundOrderId?: number;
  refundAmountType?: 'refundIncludingConvFee' | 'refundOriginalAmount';
  cardNumber?: string;
  paymentId?: number;
  refundConvFee?: boolean;
  convFee: number;
  convFeeTax: number;
}

export interface BreakdownTypes {
  charge: string;
  convenienceFree: string;
  tax: string;
  totalCharge: string;
}

// export interface PaymentSelectionTypes {
//   percentage: string;
//   percentageAmount: string;
//   requiredDeposite: string;
//   requiredDepositeAmount: string;
//   balanceDue: string;
//   balanceDueAmount: string;
//   discountPercentage: string;
// }

export type PaymentSelectionTypes = {
  paymentType: 'percentage' | 'requiredDeposite' | 'balanceDue';
  discountPercentage: string;
  percentageAmount?: number;
  requiredDepositeAmount?: number;
  balanceDueAmount?: number;
};

export interface CreditCardsTypes {
  firstSixDigits: string;
  scheme: string;
  type: string;
  brand: string;
  bank: string;
}

// Saved Payment Types
export interface SavedPaymentDTO {
  paymentType: string;
  paymentAmount: string;
  paymentDiscount: string;
  paymentReference: string;
  bankAccount: string;
  creditCardId: string;
  transactionId: string;
  user: string;
  level2Data: string;
}

// Extended Payment Info Types
export interface ExtendedPaymentInfoDTO {
  id?: number;
  date: string;
  cardType: string;
  amount: number;
  discount: number | null;
  reference: string | null;
  bankAccount: string;
  cardNumber: string;
  authCode?: string;
  transactionId?: string | null;
  user?: string | null;
  level2PoNo?: string | null;
  level2Tax?: number;
  level2Freight?: number | null;
  convFee?: number;
  originalCharge?: number;
  isDeleted?: boolean;
  isOnlinePayment?: boolean;
  transactionOperation?: string;
  batchId?: number;
  paymentDate: string;
  email?: string;
}

export interface OnlinePaymentDTOForm {
  laststatuschange: string;
  field: string;
  operator: string;
  value: string;
  statusvalue: string;
}

export interface OnlinePaymentDTOTable {
  id: number;
  customer: string;
  salesperson: string;
  lastStatusChange: string;
  amount: string;
  status: string;
}

export interface Permission {
  permission: string;
  enabled: boolean;
}

// DTO of Process With Credit Card/ACH
export interface ProcessWithCreditCardDTO {
  date: string;
  type: string;
  amount: number;
  name: string;
  email: string;
  address: string;
  city: string;
  state: number;
  country: string;
  zipcode: string;
  updateCustomer?: boolean;
  reuseCC?: boolean;
  reuseOption?: string | null;
  bankAccountId: number | null;
  cardExpiry?: string;
  convFee: number;
  convFeeTax: number;
}

// DTO of Process With Refund
export interface ProcessWithRefundDTO {
  date: string;
  type: string;
  amount: number;
  name: string;
  email: string;
  address: string;
  city: string;
  state: number;
  country: string;
  zipcode: string;
  cardNumber: string;
  updateCustomer?: boolean;
  bankAccountId: number | null;
  cardExpiry?: string;
}

export interface PaymentCallbackType {
  sentPaymentId: number;
  processor: string;
  status: boolean;
  resultmsg: string;
  transid: string;
  authcode: string;
  card: string;
  expdate: string;
  amount: number;
  partial: boolean;
  type: string;
  billingname: string;
}

export interface AddPaymentType {
  orderId: number;
  date: string;
  paymentTypeId: number;
  amount: number;
  reference?: string;
  bankAccountId: number | null;
  updateCustomer?: boolean;
  customerId: number;
  convFee?: number;
  convFeeTax?: number;
}

export interface RefundPaymentType {
  orderId: number;
  date: string;
  paymentTypeId: number;
  amount: number;
  billingName: string;
  billingAddress: string;
  billingCity: string;
  billingZipCode: string;
  email: string;
  updateCustomer: boolean;
  stateId: number;
  bankAccountId: number;
  refundConvFee: boolean;
  paymentId: number;
}

export enum LEAD_TYPE_VALUES {
  INSTALLATION_LEAD = 'installation',
  DISMANTLE_LEAD = 'dismantle',
}

export interface RequestAdditionalInfoTypes {
  body: AdditionalInfoTypes;
  orderId: string;
}

export type TrackEquipmentStatus = {
  shipping: boolean;
  return: boolean;
};

export enum STATUS_SHIP_INFO {
  SHIPPED = 'SHIPPED',
  NOT_SHIPPED = 'NOT_SHIPPED',
  PARTIALLY_SHIPPED = 'PARTIALLY_SHIPPED',
  NOT_RETURN = 'NOT_RETURN',
  PARTIALLY_RETURN = 'PARTIALLY_RETURN',
  RETURNED = 'RETURNED',
}

export type dataCalculationTypes = {
  grandTotal?: number;
  subTotal?: number;
  balanceDueAmount?: number;
  requestedDepositPercentageAmt?: number;
};

export interface VoidDeletePaymentFormTypes {
  id: number;
  date: string;
  type: string;
  amount: string;
  originalAccount: string;
  emailReceipt: string;
}
