export interface ListTypes {
  listName: string;
  lastUpdated: string;
  updatedBy: string;
  to: string;
}

export interface AllListsType {
  name: string;
  toggle?: (value?: any) => void;
}

export interface RentDto {
  rateAdjId?: number;
  id: number | null;
  rentDaysFrom: string;
  rentDaysTo: string;
  rateAdj: string;
}
export interface CategoryFormType {
  id: number;
  catNo: string;
  catDesc: string;
  departmentId: number;
  incomeAcct: string;
  isActive?: boolean;
  rateAdjs: RentDto[];
}
export interface CustomerTypesDto {
  id: number;
  code: string;
  description: string;
  delcharge: string;
  isactive?: boolean;
  custtype_id: number;
}

export interface DeliveryFormType {
  deliverytype_id: number;
  name: string;
  description: string;
  isactive: boolean;
  presun: number;
  premon: number;
  pretue: number;
  prewed: number;
  prethur: number;
  prefri: number;
  presat: number;
  postsun: number;
  postmon: number;
  posttue: number;
  postwed: number;
  postthur: number;
  postfri: number;
  postsat: number;
  shiptransitdays: string;
}

export interface DepartmentFormType {
  id: number;
  deptDesc: string;
  dept: string;
  prtKitCompOnInv: Boolean;
}

export interface PaymentTypeFormType {
  id: number;
  bankAccountId: number;
  paymentMethod: string;
  bankAccount: string;
}
export interface PaymentTermsFormType {
  paymentterm_id: number;
  term: string;
}

export interface ReferralTypeFromType {
  reftype_id: number;
  code: string;
  description: string;
}

export interface SalesTaxRatesTypes {
  addnl_salestaxcode_id: number;
  salestaxcode_id: number;
  salestaxcode: string;
  salestaxrate: string;
}
export interface SalesTaxCodeFormType {
  salestaxcode_id: number;
  salestaxcode: string;
  salestaxdesc: string;
  salestaxstate: string;
  selectable: boolean;
  effectivedate: string;
  taxdel: boolean;
  taxlabor: boolean;
  taxdamagewaiver: boolean;
  taxfuelsurcharge: boolean;
  transaction: boolean;
  updateqb: boolean;
  taxconvfee: boolean;
  convfeetaxsalesonly: boolean;
  taxexpeditedfee: boolean;
  taxproductionfee: boolean;
  isactive: boolean;
  salestaxrate: number;
  taxrateid: number | string;
  salesTaxRates: SalesTaxRatesTypes[];
}

export interface InviteClient {
  email: string;
  invitedDateTime: string;
  expiredBy: string;
  id: number;
  isExpired: boolean;
  companyName: string;
  createdOn: string;
  status: string;
}

export interface BankAccountsFormType {
  id: number;
  account: number;
}

export interface CheckListItemsFormType {
  id: number;
  seqNo: number;
  description: string;
}

export interface DriversFormType {
  drivers_id: number;
  name: string;
  phone: string;
  email: string;
  gpsName: string;
}

export interface ESignFieldsFormType {
  id: number;
  formType: string;
  fieldName: string;
}

export interface EmployeesType {
  employee_id: number;
  name: string;
  employeeNo: number;
  rate: number;
  halfRate: number;
  doubleRate: number;
}

export interface EquipmentFormType {
  equipment_type_id: number;
  equipmentType: string;
  equipmentTypeDesc: string;
}

export interface EventFormType {
  event_type_id: number;
  eventType: string;
  eventDesc: string;
}

export interface DeliveryChargesZipCodesType {
  id: number;
  // town: string;
  // state: string;
  zipCodeFrom: string;
  zipCodeThru: string;
  charge: number;
}
export interface DeliveryChargesType {
  id: number;
  town: string;
  state: string;
  department_id: number;
  delCharge: string;
  salesTaxCodeId?: boolean;
  zipCodes: DeliveryChargesZipCodesType[];
}
export interface TrucksType {
  truckName: string;
  location: string;
  defaultRouteDesc: string;
  allowedWeight: number;
  locationNo?: number;
}

export interface TrucksEquipmentsType {
  equipmentTypeId: number;
  description: string;
  weight: string;
}

export interface SurgeRatesType {
  code: string;
  description: string;
  rate: number;
}

export interface ShippingCompaniesType {
  id?: string;
  name: string;
  type: number;
  trackPage: string;
}

export interface SetupTakedownType {
  code: string;
  type: string;
  description: string;
  isActive?: boolean;
  isDefault?: boolean;
  id?: number;
}

export const enum SetupTakedown {
  SETUP = 1,
  TAKEDOWN = 2,
}

export const enum SETUP_TAKEDOWN_STRING {
  SETUP = 'Setup',
  TAKEDOWN = 'Takedown',
}

// List of available delivery methods for invoices/statements
export const setupTakedown = [
  { label: 'Setup', value: SetupTakedown.SETUP?.toString() },
  { label: 'Takedown', value: SetupTakedown.TAKEDOWN?.toString() },
];

export interface QualityType {
  qualityType: string;
  qualityDesc: string;
}

export interface LocalZipCodesType {
  id: string;
  zipFrom: number;
  zipThru: string;
}

export interface BankAccountPaymentType {
  paymentterm_id: number;
  account: string;
}

export interface DeliveryLocationFormType {
  id: number;
  location: string;
  phone: string;
  contact: string;
  contactEmail: string;
  contactPhone: string;
  locationLine2: string;
  town: string;
  state: string;
  zipcode: string;
  country: string;
  shipNote: string;
  customerId: number;
  deliveryFee: number;
  deliveryType: string;
  isDefault: boolean;
  instructions: string;
  customerName?: string;
  stateId?: string;
  countryId?: string;
}

export interface PackingListDepartmentsFormType {
  packing_id: number;
  name: string;
  deparments: string;
}
