import {
  billToTypes,
  ItemIdTypes,
  ShipOrderType,
  shipToType,
} from './order.types';

export interface ItemInquiryItemsType {
  id: number;
  itemInquiryId: number;
  itemId: string;
  serialNumber: number;
  description: string;
  quantity: number | string;
  unitPrice: number | string;
  price: number | string;
  itemType: string;
  subRental: boolean;
  total: number;
  isActive: boolean;
}

export interface ItemInquiryFormType {
  itemId: ItemIdTypes;
  category: string;
  description: string;

  storeLocationId: number;
  orderType: string;
  dateOfUseFrom: string;
  eventFrequency: string;
  shipOrder: ShipOrderType;
  billTo: billToTypes;
  shipTo: shipToType;
  rentDays: number;

  items: ItemInquiryItemsType[];
  storeItems: ItemInquiryItemsType[];
}

export interface RequireShippingInfoTypes {
  contactPhone: string;
  address1: string;
  stateId: string;
  city: string;
  zipCode: string;
}

// Location  shipping info types
export interface ShippingInfoProps {
  info: shipToType;
  onChange: (value: RequireShippingInfoTypes) => void;
  onCancel: () => void;
}

export interface LocationTyps {
  contactPhone: string;
  location: string;
  stateId: string;
  town: string;
  zipcode: string;
}
