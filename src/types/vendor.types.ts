import { FieldValues } from 'react-hook-form';

export interface VendorListTypes {
  id: number;
  vendorName: string;
  address1: string;
  address2: string;
  city: string;
  state: string;
  zipCode: string;
  tel1: string;
  email: string;
  country?: string | number;
  telfax?: string;
}

export interface BusinessInfoTypes {
  id: number;
  vendorName: string;
  address1: string;
  address2: string;
  city: string;
  state: string;
  zipCode: string;
  country: string | number;
  isactive: boolean | string;

  // contact info
  contact: string;
  tel1: string;
  tel2: string;
  telfax: string;
  emailaddress: string;
}

export interface ContactTyps {
  phone_id: number;
  contact_id: number;
  phoneno: string;
  phonetype_id: string;
}
export interface AdditionalContactInfoTypes {
  contact_id: number;
  first_name: string;
  last_name: string;
  linkid: number;
  title: string;
  email: string;
  batch: string;
  phones: ContactTyps[];
  phoneno: string;
  batchname: string;
}

export interface DeleteModalState {
  isOpen: boolean;
  itemId: string | number | null;
  isTableRow: boolean;
}

// Interface representing the mapping of vendors tabs for both add and edit operations.
type CompatibleFieldValues<T> = {
  [K in keyof T]: T[K] | null | undefined;
};

export enum VENDOR_TABS {
  BUSINESS_INFO = 'business-info',
  CONTACT_INFO = 'contact-info',
  ADDITIONAL_CONTACT_INFO = 'additional-contact-info',
}

export interface AllVendorsTabTypeMap
  extends CompatibleFieldValues<FieldValues> {
  [VENDOR_TABS.BUSINESS_INFO]: BusinessInfoTypes;
  [VENDOR_TABS.CONTACT_INFO]: BusinessInfoTypes;
  [VENDOR_TABS.ADDITIONAL_CONTACT_INFO]?: AdditionalContactInfoTypes | null;
}

export enum SearchByFilter {
  Vendor = 'vendorName',
  Address = 'address1',
  City = 'city',
  State = 'state',
  Phone = 'tel1',
  ZipCode = 'zipCode',
  email = 'emailaddress',
}

export const searchByFilterOptions = [
  { value: SearchByFilter.Vendor, label: 'Vendor' },
  { value: SearchByFilter.Address, label: 'Address' },
  { value: SearchByFilter.City, label: 'City' },
  { value: SearchByFilter.State, label: 'State' },
  { value: SearchByFilter.Phone, label: 'Phone' },
  { value: SearchByFilter.ZipCode, label: 'Zip Code' },
  { value: SearchByFilter.email, label: 'E-mail' },
];
