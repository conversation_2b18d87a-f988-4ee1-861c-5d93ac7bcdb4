import { RowSelectionState } from '@tanstack/react-table';
import {
  FieldArrayWithId,
  SubmitHandler,
  UseFieldArrayAppend,
  UseFormReturn,
} from 'react-hook-form';
import { AvailabiltyCalendarInfoTyps } from '../availabilty-calendar-info.types';
import { SortingStateType } from '../common.types';
import {
  ItemIdTypes,
  OrderItemLookupFormTypes,
  OverBookedItemInfoTypes,
} from '../order.types';

// Interface for Email form data
export interface EmailFormDataTypes {
  listId?: string;
  to: boolean;
  cc: boolean;
  bcc: boolean;
  name: string;
  email: string;
}

export interface AdditionalEmailInfoTypes {
  emails: EmailFormDataTypes[];
}

export interface AdditionalEmailInfoPayloadTypes {
  emailRecipients: EmailFormDataTypes[];
  subject: string;
  body: string;
}

export interface EmailListTypes {
  contact_id: number;
  contacttype_id: number;
  linkid: number;
  name: string;
  email: string;
  primary: boolean;
}

// Interface for Item change details
export interface ChangeItemTypes {
  itemId: string;
  description: string;
  price: string;
  items: ItemDetailsTypes;
}

// Interface for item details under ChangeItem
interface ItemDetailsTypes {
  description: string;
  serialNo: string;
  quality: string;
  location: string;
  price: number;
}

export interface CustomerVendorLookupTypes {
  srNo: number;
  id: number;
  fullName: string;
  phone: string;
  fax: string;
  address1: string;
  address2: string;
  city: string;
  state: string;
  zipcode: string;
  country: string;
  specialInstructions: string;
  contactType: number;
  isActive: boolean;
}

export interface SubRentItem {
  id: number;
  SubRentItem: string;
  itemDescription: string;
  quantityAddCurrentTotal: string;
  costReplaceCurrentCost: string;
  currentSubRentQty: string;
  currentSubRentCost: string;
}

export interface NewSubRent {
  id?: number;
  subRental?: string;
  rentedFrom?: string;
  custContact: string;
  resvNo: string;
  orderId: string;
  pickupDate: string;
  specInst1: string;
  returnDate: string;
  specInst2: string;
  customerId: number;
  contactTypeValue: number;
}

export interface OrderSubRentsTypes {
  id: number;
  pickupDate: string;
  returnDate: string;
  ordStatus: string;
  firstName: string;
  lastName: string;
  contactTypeValue: number;
  contactTypeText: string;
  resvNo: string;
  ordBy: string;
  custContact: string;
  pickupReturnStatus: string;
  specInst1: string;
  specInst2: string;
  customerId: number;
}

export interface SubRentPayload {
  id?: number;
  orderId: number;
  pickupDate: string | null;
  returnDate: string | null;
  rentedFromId: number;
  contactType: number;
  resvNo: string;
  custContact: string;
  specInst1: string;
  specInst2: string;
  OrderSubrentItems: {
    orderSubrentId: number;
    orderItemId: number;
    itemId: number;
    itemDescription: string;
    quantity: number;
    unitPrice: number;
    rowNo: number;
  }[];
}

export interface SubRentItemListTypes {
  id: number;
  orderId: number;
  orderItemId: number;
  itemId: number;
  itemName: string;
  itemDescription: string;
  quantity: number;
  unitPrice?: number | string;
  currentSubRentedQty: number;
  currentSubRentedCost: number;
  orderSubrents: OrderSubRentsTypes[];
}

export interface SubRentDetailTypes {
  orderId: number;
  pickupDate: string;
  returnDate: string;
}

export interface AdditionalItemInfoTypes {
  itemId: number;
  orderId: number;
  description: string;
  itemlocation: string | null;
  officelocation: string;
  qualitydetails: string;
  replacementcharge: number;
  itemInfo: string;
}

export interface ChangeSortingTypes {
  orderId: number;
  ItemPrintSequence: string;
  KitComponentPrintSeq: string | null;
}

export interface KitItemListTypes {
  itemId: number;
  orderId: number;
  serialNumber: string | null;
  quantity: number;
  description: string;
  type: string;
  subRental: boolean;
  price: number;
  total: number;
  itemIdString: string;
  orderNumber: number;
  parentId: number;
  isOverbooked: boolean;
  id: number;
}

export interface KitItemTypes {
  orderId?: number | string;
  serialNumber: string | null;
  quantity: number | string | null;
  description: string;
  type?: string;
  subRental: string;
  price: number | string;
  total: string | number;
  orderNumber?: number;
  parentId: number | null | string;
  listId: number | string | null;
  itemId: ItemIdTypes | null;
  disabledCheckBox?: boolean;
}
export interface KitItemsFormDataTypes {
  items: KitItemTypes[];
}

export interface GenerateKitItemsTreeProps {
  //  Form Instances
  kitForm: UseFormReturn<KitItemsFormDataTypes>;
  additionalItemInfoForm: UseFormReturn<AdditionalEmailInfoTypes>;
  availabilityForm: UseFormReturn<AvailabiltyCalendarInfoTyps>;
  subRentingForm: UseFormReturn<SubRentItemListTypes>;
  overBookedForm: UseFormReturn<OverBookedItemInfoTypes>;

  // Data and State
  orderId: string | number;
  customerData: CustomerVendorLookupTypes | null;
  setCustomerData: (data: CustomerVendorLookupTypes | null) => void;
  serialData: any;
  serialList: any;
  serialLoading: boolean;
  orderSubRents?: OrderSubRentsTypes[];
  orderItemId: string | number | null;

  // Form Array Management
  fields: FieldArrayWithId<KitItemsFormDataTypes, 'items', 'id'>[];
  append: UseFieldArrayAppend<KitItemsFormDataTypes, 'items'>;
  remove: (index?: number | number[]) => void;

  // Selection and Pagination
  rowSelection: Record<string, boolean>;
  setRowSelection: React.Dispatch<
    React.SetStateAction<Record<string, boolean>>
  >;
  subRentalRowSelection: RowSelectionState;
  setSubRentalRowSelection: React.Dispatch<
    React.SetStateAction<RowSelectionState>
  >;
  sorting: SortingStateType[];
  setSorting: React.Dispatch<React.SetStateAction<SortingStateType[]>>;

  // Item Management
  kitItemId: number;
  setKitItemId: React.Dispatch<React.SetStateAction<number | null>>;
  selectedItem?: KitItemTypes;
  selectedSubRents: OrderSubRentsTypes[];
  setSelectedSubRents: React.Dispatch<
    React.SetStateAction<OrderSubRentsTypes[]>
  >;

  // UI State
  activeInfoTab: string;
  isLoading: boolean;
  isSubRentLoading: boolean;
  isUpdating: boolean;

  // Callbacks
  onOpenChange: () => void;
  setActiveTab: (tab: string) => void;
  handleAddItemLookupData: (value: OrderItemLookupFormTypes[]) => void;
  handleTabChange: (tab: string) => void;
  handleUpdateSubRent: SubmitHandler<SubRentItemListTypes>;
  handleNewSubRental: () => Promise<void>;
}

export interface ChangeSerializedItemTypes {
  itemId: number;
  storeLocationId: number;
  storeLocationName: string;
  rowNo: null;
  serialNo: string | null;
  qualityId: number;
  qualitydesc: string;
  quantity: number;
  purchaseDate: string;
  purchaseDateSequenceVal: string;
  sequenceNo: 1;
  itemDescription: string;
  price: number | null;
  itemIdString: string;
  id: number;
}

export interface ChangeSerializedItemPayloadTypes {
  orderItemId: number;
  filterBy?: 'ITEM' | 'CATEGORY';
  filter: {
    availInvForLocation?: boolean;
    availInvForAllLocation?: boolean;
    allInvForTheLocation?: boolean;
    allInvForAllLocation?: boolean;
  };
  categoryIds: string[];
}
