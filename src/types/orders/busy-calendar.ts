export interface BusyCalendarTypes {
  month: string;
  date: string;
  //
  pickups: boolean;
  cpu_cr: boolean;
  quotes: boolean;

  selectedDate: string;
  orderId: number;
  deliveries: string;
  orderPickups: string;
  itineraries: string;
  tents: string;
  otherItems: string;
  revenue: string;
  availabilities: any[];
  orderNo: string;
  pickupDate: string;
  deliveryDate: string;
  orderType: string;
  orderTotal: string;
  customer: string;
  category: string;
  city: string;
}

export interface BusyCalendarOrderTypes {
  cpu_cr: boolean;
  quotes: boolean;
  orderId: number;
  dateEnable?: string;
  deliveries: string;
  pickupsDel: string;
  itineraries: string;
  tentItems: string;
  otherItems: string;
  revenue: string;
  availabilities: any[];
  orderNo: string;
  pickupDate: string;
  deliveryDate: string;
  orderType: string;
  orderTotal: string;
  customer: string;
  category: string;
  city: string;
}

export interface BusyCalReviewLevelsFormType {
  id: number;
  totalDeliveries: number;
  totalPickups: number;
  totalItineraries: number;
  totalTentItems: number;
  totalOtherItems: number;
  totalRevenue: number;
  csvCategoryIds: number[];
}

export interface BusyDateDataTypes {
  date: string;
  busy: boolean;
  review: boolean;
}
