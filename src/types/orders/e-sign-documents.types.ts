export interface ESignDocumentsTypes {
  id: number;
  orderId: number;
  revNo: number;
  description: string;
  agreementId: string;
  status: string;
  statusText: string;
  downloaded: boolean;
  lastUpdate: string;
  lastChecked: string;
  linkId: string;
  flag: string;
  expiry: number;
  isDeleted: boolean;
  createdBy: string;
  updatedBy: string;
  customerFullName: string;
}

export interface ESignDocumentsPayloadTypes {
  orderId: number;
  status: string;
  linkId: string;
  flag: string;
}
