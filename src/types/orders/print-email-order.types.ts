export interface AutoselectionOptionsTypes {
  autoSelectOptionVal: string;
  autoSelectOptionText: string;
}

export interface PrintEmailListTypes {
  id: number;
  storeId: number;
  seq: number;
  isChecked: boolean;
  type: string;
  description: string;
  form: string;
  faxForm: string;
  copies: number;
  printer: string;
  faxPrinter: string;
  defPrinter: string;
  isDeleted: boolean;
  createdBy: string;
  updatedBy: string;
}
