export interface InventoryManagerFormType {
  storeLocationId: string;
  dateFrom: string;
  dateTo: string;
  hideResolvedOverbooking: boolean;
  showNonOverbookedOrders: boolean;
  includeQuotes: boolean;
  includeSalesItemsOnRentals: boolean;
  checkAcrossAllLocation: boolean;
  sortByDeliveryDate: boolean;
  displayByCategory: boolean;
  departments: string;
  categories: string;
}

export interface OptionformTyps {
  includeQuotes: boolean;
  includeSalesItemsOnRentals: boolean;
  checkAcrossAllLocation: boolean;
  sortByDeliveryDate: boolean;
  displayByCategory: boolean;
  categories: string;
  departments: string;
}

export interface OptionListType {
  label: string;
  name:
    | 'includeQuotes'
    | 'includeSalesItemsOnRentals'
    | 'checkAcrossAllLocation'
    | 'sortByDeliveryDate'
    | 'displayByCategory';
  disable?: boolean;
}

export interface TransferInventoryTypes {
  itemId: string;
  qtyShort: string;
  transferTo: string;
  leftTo: string;
  items: {
    location: string;
    serial: number;
    quality: string;
    owned: number;
    available: number;
    toTransfer: string;
  }[];
}

export interface InventoryManagerChildRowTypes {
  orderNo: string;
  customer: string;
  rented: string;
  deliveryDate: string;
  pickupDate: string;
  cleanupDays: number | string;
  availableDate: string;
  shortBy: string;
  flipped: string;
  note: string;
}
