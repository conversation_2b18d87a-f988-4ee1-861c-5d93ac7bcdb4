export interface OptionTypes {
  label: string;
  value: string;
}

// Base interface for form data
export interface POInformationTypes {
  id: number;
  type: string;
  status: string;
  orderDate: string | Date;
  anticipatedDeliveryDate: string | Date;
  job: string;
  contact: string;
  shipVia: string;
  fob: string;
  terms: string;
  orderBy: string;
  isDeleted: boolean;

  // Ship
  shipLocation: string;
  shipPhone: string;
  shipContact: string;
  shipAddress1: string;
  shipAddress2: string;
  shipCity: string;
  shipStateId: number | string;
  shipCountryId: number | string;
  shipZipCode: string;
  customDescription: string;
  customCharge: number;
  freight: number;
  subTotal: number;
  total: number;

  // Delete PO
  cancelBy: string | null;
  cancelDate: string | null;
  cancelReason: string | null;
  completeNote1: string | null;
  completeNote2: string | null;
  completeNote3: string | null;
  completeDate: string | null;

  // Vendor
  vendorId: number;
  vendorName: string;
  vendorPhone: string;
  vendorFax: string;
  vendorAddress1: string;
  vendorAddress2: string;
  vendorCity: string;
  vendorState: string;
  vendorZipCode: string;
  vendorCountry: string;
  vendorStateId: number | null;
  vendorCountryId: number;
}

export interface POInformationFormDataTypes
  extends Omit<POInformationTypes, 'vendorId' | 'vendorPhone' | 'orderBy'> {
  vendorId: OptionTypes;
  vendorPhone: OptionTypes;
  orderBy: OptionTypes | string;
}

// Form data type mapping
export interface FormDataTypeMap {
  information: POInformationTypes;
  'item-details': POItemDetailsList;
  'received-items': ReceivedItemsList;
}

// API route configuration type
export interface RouteConfig {
  CREATE?: string;
  UPDATE: string | ((id: string) => string);
  GET?: string | ((id: string) => string);
}

// API routes type mapping
export interface ApiRoutesTypeMap {
  information: RouteConfig;
  'received-items': RouteConfig;
}

export type ActiveTabType = keyof FormDataTypeMap;

export interface PurchaseOrderListingTypes {
  id: number;
  purchaseOrderId: string;
  jobId: string;
  purchaseOrderType: string;
  purchaseOrderStatus: string;
  vendor: string;
  orderDate: string;
  total: string;
  isActive: boolean;
  isDeleted: boolean;
  cancelBy: string | null;
  cancelDate: string | null;
  cancelReason: string | null;
  completeNote1: string | null;
  completeNote2: string | null;
  completeNote3: string | null;
}

export const purchaseOrderTypeList = [
  {
    label: 'Purchase Order',
    value: '1',
  },
  {
    label: 'Request For Quotation',
    value: '2',
  },
];

export const purchaseOrderStatusList = [
  {
    label: 'Ordered',
    value: '1',
  },
  {
    label: 'Received',
    value: '2',
  },
  {
    label: 'Paid',
    value: '3',
  },
  {
    label: 'Deleted',
    value: '4',
  },
];

export interface DeleteDialogState {
  open: boolean;
  id: number;
}

export interface POItemDetailsTypes {
  purchaseOrderId?: number | null;
  itemId: number;
  description: string;
  quantity: number | string | null;
  price?: number | string;
  total?: number;
  totalQuantityReceived?: number | null;
  quantityReceived?: number | string | null;
  itemIdLabel?: string;
  id?: number | null;
  storeLocationId?: number | null;
}

export interface POItemDetailsFormDataTypes
  extends Omit<POItemDetailsTypes, 'itemId'> {
  itemId: OptionTypes | null;
  listId?: number | string | null;
  disabledCheckBox?: boolean;
}

export interface POReceivedItemsFormDataTypes
  extends Omit<POItemDetailsTypes, 'itemId'> {
  itemId: number | null;
}

export interface KitPurchaseOrderForReceivedItems {
  id: number | null | string;
  itemId: {
    label: string;
    value: string;
  };
  quantity?: number | string | null;
  description: string;

  location: string;
  qtyRcvd: string;
  totalQtyRcvd: string;
}

export interface POItemDetailsList {
  items: POItemDetailsFormDataTypes[];
}
export interface ReceivedItemsList {
  items: POReceivedItemsFormDataTypes[];
}

export type PurchaseOrderFilter = {
  label: string;
  value: string;
  name: string;
  tagValue: string;
  operator: string;
};

export interface DeletePurchaseOrderType {
  id: string;
  isDeleted: boolean;
  cancelBy: string | null;
  cancelDate: string | null;
  cancelReason: string | null;
}

export interface FilterItemDTO {
  field: string | null;
  label: string;
  operator: string;
  name: string;
  tagValue: string | null;
  value: string;
}

export interface RawFilterItem {
  label: string;
  value: string;
  name: string;
  operator: string;
  tagValue?: string;
  field?: string;
}

export interface CompletionInfoType {
  completeNote1: string | null;
  completeNote2: string | null;
  completeNote3: string | null;
}
