export type ProcessDialogAction =
  | 'POSTING'
  | 'SERVICE_CHARGES'
  | 'UNDELETE_ORDER'
  | 'INVOICES__DELIVERIES_AND_PACKING_LISTS'
  | 'INVOICES'
  | 'DELIVERY_SLIPS'
  | 'PACKING_LISTS'
  | 'PICKUP_SLIPS'
  | 'BATCH_E_MAIL'
  | 'SUB_RENTALS'
  | 'TIME_CARD_BY_JOB'
  | 'ARCHIVE_ORDERS_BY_DATE'
  | 'ARCHIVE_A_R_BY_DATE'
  // | 'BACKUP_DATABASE'
  // | 'CHANGE_CUSTOMER_NAME'
  // | 'COMBINE_CUSTOMERS'
  | 'FLAG_CUSTOMERS_INACTIVE'
  | 'PURGE_CUSTOMERS'
  | 'PURGE_DELETED_ORDERS_BY_DATE'
  | 'PURGE_QUOTES_BY_DATE';

export interface ProcessesType {
  processId: string;
  icon?: String;
  type: string;
  processName: string;
}

export interface PostingListType {
  orderNo: number;
  dateofUse: string;
  customerName: string;
  total: string;
}
export interface PostingType {
  locationid: number;
  date: string;
  excludeFuturePickups: boolean;
}

export interface UnpostType {
  orderId: string;
}

export interface ServiceChargesListType {
  id: number;
  serviceChargeId?: number;
  customer: string;
  totalBalance: number;
  overdueBalance: number;
  serviceCharge: number;
  amountApplied: number | undefined;
}
export interface ServiceChargesType {
  date: string;
  days: string;
  items: ServiceChargesListType[];
}

export interface UndeleteOrderType {
  orderId: string;
}

export enum InvoiceOptionsMethod {
  IncludeOrders = 'IncludeOrders',
  InvoiceOrders = 'InvoiceOrders',
  ReInvoiceOrders = 'ReInvoiceOrders',
}

export enum DateOptions {
  DeliveryDate = 'DeliveryDate',
  DateOfUse = 'DateOfUse',
}

export interface InvoicesDeliveriesPackingListsType {
  location: string;
  invoiceOptions: InvoiceOptionsMethod;
  dateOptions: DateOptions;
  deliveryDateFrom: string;
  deliveryDateThru: string;
  dateOfUseFrom: string;
  dateOfUseThru: string;
  doNotPrintInvoices?: string;
  printEmailOptions: PrintEmailOptions;
}

export enum DateOptionsSlips {
  DeliveryDate = 'DeliveryDate',
  ArrivalDate = 'ArrivalDate',
}
export interface DeliverySlipsType {
  location?: string;
  dateOptionsSlips?: DateOptionsSlips;
  deliveryDateFrom?: string;
  deliveryDateThru?: string;
  ordersBeingDelivered?: boolean;
  ordersBeingPickedByCustomers?: boolean;
}

export interface PackingListsType {
  location?: string;
  deliveryDateFrom?: string;
  deliveryDateThru?: string;
  ordersBeingDelivered?: boolean;
  ordersBeingPickedByCustomers?: boolean;
}

export interface PickupSlipsType {
  location?: string;
  pickupDateFrom?: string;
  pickupDateThru?: string;
  ordersPickedUp?: boolean;
  ordersBeingReturnedByCustomers?: boolean;
}

export interface PrintDialogType {
  printOptions: PrintPopupOptions;
}

export enum invoiceOptionsBatchEmailMethod {
  IncludeOrders = 'IncludeOrders',
  InvoiceOrders = 'InvoiceOrders',
}

export enum sendMethod {
  Invocies = 'Invocies',
  OrderConfirmations = 'OrderConfirmations',
  EsignDocuments = 'EsignDocuments',
}

export enum emailMethod {
  OrderContactEmail = 'OrderContactEmail',
  MainCustomerEmail = 'MainCustomerEmail',
}

export enum DateOptionsBatchEmail {
  DeliveryDate = 'DeliveryDate',
  DateOfUse = 'DateOfUse',
  DateOrdered = 'DateOrdered',
}

export enum PrintEmailOptions {
  PreviousSelection = 'PreviousSelection',
  Other = 'Other',
  EsignDocuments = 'EsignDocuments',
  DefaultInvoicesPackingLists = 'DefaultInvoicesPackingLists',
  DefaultInvoices = 'DefaultInvoices',
  DefaultPackingLists = 'DefaultPackingLists',
}

export enum PrintPopupOptions {
  Customer = 'Customer',
  DeliveryDate = 'DeliveryDate',
  PickupDate = 'PickupDate',
  DeliveryDriver = 'DeliveryDriver',
  PickupDriver = 'PickupDriver',
  City = 'City',
}

export interface BatchEmailType {
  location?: string;
  invoiceOptions: invoiceOptionsBatchEmailMethod;
  dateOptions: DateOptionsBatchEmail;
  deliveryDateFrom?: string;
  deliveryDateThru?: string;
  dateOfUseFrom?: string;
  dateOfUseThru?: string;
  dateOrderedFrom?: string;
  dateOrderedThru?: string;
  send: sendMethod;
  email: emailMethod;
  printEmailOptions: PrintEmailOptions;
}

export interface SettingsEmailType {
  server: string;
  port: string;
  username: string;
  password: string;
  enableSSL: boolean;
  sendReadReceipt: boolean;
  messageLimit: string;
  emailFrom: string;
  cc: string;
  emailBody: string;
  emailType: string;
}

export enum PrintOptions {
  DoNotIncludeSubRentalsAlreadyPrinted = 'DoNotIncludeSubRentalsAlreadyPrinted',
  PrintAllSubRentals = 'PrintAllSubRentals',
}

export interface SubRentalsType {
  printOptions: PrintOptions;
  pickupDateFrom: string;
  pickupDateThru: string;
}

export interface TimeCardJobType {
  employee: string;
  totalWeek: string;
  date: string;
  hours: string;
}

export interface archiveARType {
  archiveARThru: string;
}

export interface archiveOrdersDateType {
  archiveOrdersThru: string;
}

export interface flatCustomersInactiveType {
  noOrdersSince: string;
}

export interface purgeDeletedOrdersType {
  purgeDeletedOrdersThru: string;
}

export enum QuotesOptions {
  ArchiveQuotes = 'ArchiveQuotes',
  DeleteQuotes = 'DeleteQuotes',
}

export interface purgeQuotesType {
  quotes: QuotesOptions;
  purgeQuotesThru: string;
}
