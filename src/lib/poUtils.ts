import {
  OptionTypes,
  POInformationFormDataTypes,
  POItemDetailsFormDataTypes,
  POReceivedItemsFormDataTypes,
} from '@/types/purchase-order.types';
import { DEFAULT_FORMAT, formatDate } from './utils';

export const transformPurchaseOrderPayload = (
  formData: POInformationFormDataTypes,
  id?: string
) => {
  return {
    id: id ? Number(id) : null,
    type: formData?.type,
    status: formData?.status,
    orderDate: formatDate(formData?.orderDate, DEFAULT_FORMAT),
    anticipatedDeliveryDate: formatDate(
      formData?.anticipatedDeliveryDate,
      DEFAULT_FORMAT
    ),
    orderBy: (formData?.orderBy as OptionTypes)?.value ?? '',
    shipLocation: formData?.shipLocation ?? null,
    shipPhone: formData?.shipPhone ?? null,
    shipContact: formData?.shipContact ?? null,
    shipAddress1: formData?.shipAddress1 ?? null,
    shipAddress2: formData?.shipAddress2 ?? null,
    shipZipCode: formData?.shipZipCode ?? null,
    shipCity: formData?.shipCity ?? null,
    shipCountryId: formData?.shipCountryId ?? null,
    vendorId: formData?.vendorId?.value
      ? Number(formData?.vendorId?.value)
      : null,
    freight: formData?.freight ? Number(formData?.freight) : 0,
    subTotal: Number(formData?.subTotal),
    total: Number(formData?.total),
    job: formData?.job ?? null,
    contact: formData?.contact ?? null,
    shipVia: formData?.shipVia ?? null,
    fob: formData?.fob ?? null,
    terms: formData?.terms ?? null,
    shipStateId: formData?.shipStateId ?? null,
    customDescription: formData?.customDescription ?? null,
    customCharge: Number(formData?.customCharge) ?? null,
    completeNote1: formData?.completeNote1,
    completeNote2: formData?.completeNote2,
    completeNote3: formData?.completeNote3,
  };
};

export const transformItemPayload = (items: POItemDetailsFormDataTypes[]) =>
  items.map(({ listId, itemId, description, price, quantity, total }) => ({
    itemId: Number(itemId?.value),
    description,
    quantity: Number(quantity ?? 0),
    price: Number(price ?? 0),
    total: Number(total ?? 0),
    id: typeof listId === 'number' ? listId : null,
  }));

export const transformReceivedPayload = (
  items: POReceivedItemsFormDataTypes[]
) =>
  items.map(({ id, quantityReceived, storeLocationId }) => ({
    id,
    quantityReceived: Number(quantityReceived),
    storeLocationId: Number(storeLocationId),
  }));
