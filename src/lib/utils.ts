import { ZIP_CODE_PATTERNS } from '@/constants/validation-constants';
import { MapItems, PaginationOptionsType } from '@/types/common.types';
import { CustomerRequestDto } from '@/types/customer.types';
import { clsx, type ClassValue } from 'clsx';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import { twMerge } from 'tailwind-merge';

// Default format for the utility
export const DEFAULT_FORMAT = 'MM/DD/YYYY';
export const DATE_FORMAT_YYYYMMDD = 'YYYY-MM-DD';
export const DATE_TIME_FORMAT = `${DEFAULT_FORMAT} hh:mm A`;

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const formatKey = (key: string) => {
  return key
    .replace(/([A-Z])/g, ' $1')
    .replace(/^./, (str) => str.toUpperCase());
};

/**
 * Generates an array of label-value pairs from the provided data array.
 *
 * @param data - The array of objects to map over
 * @param labelKey - The key to use for the label in each pair
 * @param valueKey - The key to use for the value in each pair
 *
 * @returns An array of objects, each containing a 'label' and 'value' pair extracted from the data array.
 */

export const generateLabelValuePairs = ({
  data,
  labelKey,
  labelKey2,
  valueKey,
}: MapItems): { label: string; labelKey2?: string; value: string }[] => {
  // Ensure 'data' is an array before using map
  if (!Array.isArray(data)) {
    return []; // Return an empty array if 'data' is not an array
  }

  return data.map((item: any) => ({
    label: [item[labelKey], labelKey2 ? item[labelKey2] : null]
      .filter((val) => Boolean(val))
      .join(' '),
    value: item[valueKey] as string,
  }));
};

export const getLabelFromId = <T>(
  data: T[],
  id: number,
  idKey: keyof T,
  labelKey: keyof T
): string => {
  const item = data?.find((item) => item[idKey] === id);
  return item ? (item[labelKey] as unknown as string) : '';
};

//  Updates or removes a query parameter in the URL.
export const updateQueryParam = (
  value: string | number | null,
  keyName: string = 'id'
) => {
  const params = new URLSearchParams(window?.location?.search);

  if (value === null) {
    params?.delete(keyName);
  } else {
    params.set(keyName, value?.toString());
  }

  // Construct the new URL
  const newUrl = params.toString()
    ? `${window?.location?.pathname}?${params?.toString()}`
    : window?.location?.pathname;

  window?.history?.pushState({}, '', newUrl);
};

//  Gets the value of a specific query parameter from the URL.
export const getQueryParam = (key: string): string | null => {
  const params = new URLSearchParams(window?.location?.search);
  return params?.get(key);
};

// error message by name of the filed
export const getErrorMessage = (error: any, name: string | undefined) => {
  return name && error[name] && error[name]?.message;
};

/**
 * Utility function to remove country code prefix (+1) from the phone number.
 *
 * @param phoneNumber - The phone number to process.
 * @returns - The phone number with the country code removed, if it exists.
 */
export const removeCountryCodePrefix = (phoneNumber: string): string => {
  // Regular expression to match the phone number starting with +1 (or any other country code)
  const countryCodePattern = /^\+1/;

  // Remove the country code if it exists and return the rest of the phone number
  const newPhoneNumber = phoneNumber.replace(countryCodePattern, '');

  return `(${newPhoneNumber.slice(0, 3)})-${newPhoneNumber.slice(3, 6)}-${newPhoneNumber.slice(6)}`;
};

export const searchPayload = (props: CustomerRequestDto) => {
  const { pageSize, pageNumber, sortAscending, sortBy, filters } = props;

  // Prepare the request body with required fields and conditional filters
  const reqBody: any = {
    pageSize: pageSize || 10,
    pageNumber: pageNumber || 0,
    sortAscending: sortAscending,
    sortBy: sortBy,
    filters: filters || [],
  };

  return reqBody;
};

export function getPaginationObject({
  pagination,
  sorting,
  filters,
}: PaginationOptionsType) {
  return {
    pageNumber: pagination?.pageIndex + 1,
    pageSize: pagination?.pageSize || 10, // Default page size
    sortBy: sorting?.[0]?.id || '',
    sortAscending: sorting?.[0]?.desc,
    filters: filters?.filter(({ value }) => value) || [],
  };
}

// Format date to a specific format
export const formatDate = (
  date: string | Date,
  format: string = DEFAULT_FORMAT
): string => {
  return date ? dayjs(date)?.format(format) : '';
};

// Function to convert a value to float with customizable decimal places, and optional prefix/postfix symbols
export const convertToFloat = ({
  value,
  decimalPlaces = 2,
  prefix = '',
  postfix = '',
  alignDecimals = false, // Optional feature flag
}: {
  value: string | number;
  decimalPlaces?: number;
  prefix?: string;
  postfix?: string;
  alignDecimals?: boolean;
}): string => {
  const floatValue = parseFloat(value?.toString()) || 0;
  const isNegative = floatValue < 0;
  const absoluteValue = Math.abs(floatValue).toFixed(decimalPlaces);

  // Add a leading space if positive and alignment is desired
  const signPrefix = isNegative ? '-' : alignDecimals ? ' ' : '';

  return `${signPrefix}${prefix}${absoluteValue}${postfix}`;
};

// Add necessary plugins
dayjs.extend(utc);
dayjs.extend(timezone);

export function dayJsFormat(
  date: Date | string | undefined,
  format: string = 'YYYY-MM-DDTHH:mm:ss.SSSSSS',
  timezone?: string
): string {
  // Return empty string if the date is undefined or invalid
  if (!date || !dayjs(date).isValid()) {
    return '';
  }

  const parsedDate = dayjs(date);

  // If timezone is provided, use UTC and convert to the specified timezone
  if (timezone) {
    return parsedDate
      .utc()
      .tz(timezone ?? dayjs.tz.guess())
      .format(format);
  }

  // If no timezone, return the formatted date directly
  return parsedDate.format(format);
}

/**
 * Utility function to format a date into 'MM/DD/YYYY' format.
 *
 * @param {string | Date} value - The input value, which can either be a date string or a Date object.
 * @returns {string} - The formatted date as a string in 'MM/DD/YYYY' format.
 */
export const getDateInMMDDYYYY = (value: string | Date) => {
  return dayjs(value).format('MM/DD/YYYY');
};

export const stripCountryCode = (
  phoneNo?: string,
  countryCode: string = '+1'
): string => {
  const escapedCountryCode = countryCode?.replace('+', '\\+'); // Escape the "+" symbol
  return phoneNo?.replace(new RegExp(`^${escapedCountryCode}`), '') || '';
};

// Utility function to format a phone number into the (XXX) XXX-XXXX format
// export const formatPhoneNumber = (phoneNo: string): string => {
//   // Ensure phoneNo is a string and trim any spaces
//   if (typeof phoneNo !== 'string') {
//     phoneNo = String(phoneNo); // Convert to string if it's a number
//   }

//   phoneNo = phoneNo?.trim(); // Remove leading/trailing spaces

//   // Check if phoneNo is valid (at least 10 digits)
//   if (!/^\+?\d{10,}$/.test(phoneNo)) {
//     return ''; // Return empty if invalid
//   }

//   // Remove "+1" if present
//   phoneNo = stripCountryCode(phoneNo);

//   // Format if exactly 10 digits
//   return phoneNo?.length === 10
//     ? phoneNo.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3')
//     : phoneNo;
// };

// Formats a phone number as (XXX) XXX-XXXX and appends extension as x##### if present.
export const formatPhoneNumber = (value: string): string => {
  if (!value) return '';

  const [numberPart, extPart] = value?.split(/x/i);

  // Remove everything except digits and "+" from number part, then keep last 10 digits
  const phoneNumber = numberPart?.replace(/[^\d]/g, '')?.slice(-10);

  if (phoneNumber?.length !== 10) return '';

  // Apply US phone number formatting
  const maskedNumber = phoneNumber?.replace(
    /(\d{3})(\d{3})(\d{4})/,
    '($1) $2-$3'
  );

  // Clean extension
  const cleanedExt = extPart?.replace(/\D/g, '');

  return cleanedExt ? `${maskedNumber} x${cleanedExt}` : maskedNumber;
};

// Utiltiy function to get storage Value
export const getStorageValue = (key: string): string | null =>
  localStorage.getItem(key) || sessionStorage.getItem(key) || '';

/**
 * Formats a ZIP code based on its country and length.
 * For US ZIP codes, formats as 5-digit or ZIP+4.
 * For Canadian postal codes, formats as A1B 2C3.
 *
 * @param input - The input ZIP/postal code.
 * @param isUSA - Flag indicating whether the country is the USA (true) or Canada (false).
 * @returns Formatted ZIP code.
 */
export const formatZipCode = (input: string, isUSA: boolean): string => {
  if (!input) return ''; // Return empty string if input is empty

  // Clean input by removing non-ALPHA_NUMERIC_VALIDATION_RULE characters and making it uppercase.
  let cleaned = input
    .replace(/[^\d\w]/g, '')
    .toUpperCase()
    .slice(0, isUSA ? 9 : 6);

  if (isUSA) {
    // If it's a US ZIP code, format as either 5 digits or 5+4 digits (ZIP+4)
    return cleaned.length > 5
      ? `${cleaned.slice(0, 5)}-${cleaned.slice(5)}`
      : cleaned.slice(0, 5);
  }

  // If it's a Canadian postal code, format as A1B 2C3 (with a space)
  return cleaned.length > 3
    ? `${cleaned.slice(0, 3)} ${cleaned.slice(3)}`
    : cleaned;
};

/**
 * Validates a ZIP code based on country and format.
 * Checks whether the value matches the expected format for either the USA or Canada.
 *
 * @param value - The ZIP code to validate.
 * @param isUSA - Flag indicating whether the country is the USA (true) or Canada (false).
 * @returns Error message if invalid, or true if valid.
 */
export const validateZipCode = (value: string, isUSA: boolean) => {
  if (!value) return; // Return early if no value is provided

  // Remove any spaces to normalize the input
  const cleanedValue = value.replace(/\s/g, '').toUpperCase();

  // US ZIP code validation
  if (isUSA) {
    // Check if the input matches either 5-digit or 9-digit ZIP code (ZIP+4)
    if (!ZIP_CODE_PATTERNS.US.any.test(cleanedValue)) {
      return 'Please enter a valid zip code (e.g. 12345 or 12345-6789)';
    }
  } else {
    // Canadian postal code validation
    if (!ZIP_CODE_PATTERNS.CA.test(cleanedValue)) {
      return 'Please enter a valid zip code (e.g. A1B 2C3)';
    }
  }

  return true; // If all checks pass, the value is valid
};

export const removeStorageValue = (key: string) => localStorage.removeItem(key);

export function filterFormData<T extends Record<string, any>>(
  formData: T,
  valueKeys: (keyof T)[]
): { [K in keyof T]?: T[K] | null } {
  const filteredData: { [K in keyof T]?: T[K] | null } = {};

  for (const key of valueKeys) {
    const value = formData[key];

    // If the key exists and the value is an empty string, set it to null
    if (value === '') {
      filteredData[key] = null;
    } else if (key in formData) {
      filteredData[key] = value;
    }
    // If the key does not exist in formData, set it to null
    else {
      filteredData[key] = null;
    }
  }
  return filteredData;
}

// Checks if the value matches compareValue (defaults to "true").
export const isValueMatching = ({
  value,
  compareValue = 'true',
}: {
  value: string | null | undefined;
  compareValue?: string;
}): boolean => {
  return value === compareValue;
};

export const booleanToString = (value: boolean | undefined | null): string => {
  return value ? 'true' : 'false';
};

/**
 * Opens a new browser window centered on the screen with specified dimensions.
 *
 * @param {string} url - The URL to open in the new window.
 */
export const openCenteredWindow = (url: string) => {
  // Define the dimensions for the new window
  const width = 900;
  const height = 700;

  // Calculate the position to center the window
  const left = window.screen.width / 2 - width / 2;
  const top = window.screen.height / 2 - height / 2;

  // Define the features for the new window
  const windowFeatures = `width=${width},height=${height},left=${left},top=${top},toolbar=no,location=no,status=no,menubar=no,scrollbars=no,resizable=no`;

  // Open the new window with the specified URL and features
  window.open(url, '_blank', windowFeatures);
};

type ValidationRule = {
  min: number;
  max: number;
  allowedFirstDigits: string[];
};

const DATE_RULES: Record<'month' | 'day', ValidationRule> = {
  month: {
    min: 1,
    max: 12,
    allowedFirstDigits: ['0', '1'],
  },
  day: {
    min: 1,
    max: 31,
    allowedFirstDigits: ['0', '1', '2', '3'],
  },
};

/**
 * Validates a date input string (MM/DD/YYYY) digit-by-digit as it's being typed.
 *
 * @param formattedValue - The formatted date string (e.g., "MM/DD/YYYY")
 * @returns {boolean} - True if the input is valid at the current stage
 */
export const validateDateInput = (formattedValue: string): boolean => {
  // Extract digits only
  const digits = formattedValue.replace(/\D/g, '');

  // Early return if no digits
  if (!digits.length) return true;

  // Validate month (positions 0-1)
  if (digits.length >= 1) {
    if (!DATE_RULES.month.allowedFirstDigits.includes(digits[0])) {
      return false;
    }
  }

  if (digits.length >= 2) {
    const month = parseInt(digits.slice(0, 2), 10);
    if (month < DATE_RULES.month.min || month > DATE_RULES.month.max) {
      return false;
    }
  }

  // Validate day (positions 2-3)
  if (digits.length >= 3) {
    if (!DATE_RULES.day.allowedFirstDigits.includes(digits[2])) {
      return false;
    }
  }

  if (digits.length >= 4) {
    const day = parseInt(digits.slice(2, 4), 10);
    if (day < DATE_RULES.day.min || day > DATE_RULES.day.max) {
      return false;
    }
  }

  return true;
};
// get the day of the initial
export const getDayInitial = ({
  date,
  weekday = 'long',
  locale,
}: {
  date?: Date | string;
  locale?: string;
  weekday?: 'long' | 'short' | 'narrow';
}): string => {
  if (!date) {
    return '';
  }
  const parsedDate = new Date(date);
  if (isNaN(parsedDate.getTime())) {
    return '';
  }

  return new Date(date).toLocaleString(locale, { weekday });
};

// get the timein HH:mm:ss format

export const formatTime = (time?: string): string => {
  if (!time) return '00:00:00';

  // Check if the time is already in HH:mm:ss format
  if (/^\d{2}:\d{2}(:\d{2})?$/.test(time)) {
    return time.length === 5 ? `${time}:00` : time; // Ensure HH:mm:ss format
  }

  // Parse time using Day.js
  const parsedTime = dayjs(time, ['HH:mm:ss', 'HH:mm', 'h:mm A'], true);

  // If parsing fails, return default time
  if (!parsedTime.isValid()) {
    return '00:00:00';
  }

  return parsedTime.format('HH:mm:ss');
};

// Formats a phone number and contact name into a standardized display format.

export const formatPhoneWithContact = ({
  phone,
  name,
}: {
  phone?: string;
  name?: string;
}): string => {
  const formattedPhone = phone ? formatPhoneNumber(phone) : '';
  const displayName = name?.trim() || 'Unknown';

  // If no phone and name is "Unknown", return empty string
  return formattedPhone || displayName !== 'Unknown'
    ? `${formattedPhone} (${displayName})`
    : '';
};

export const toCapitalize = (value: string | undefined): string => {
  if (!value || value?.trim() === '') {
    return '';
  }

  // return value
  //   ?.toLowerCase()
  //   ?.split('_')
  //   ?.map((word) => word?.charAt(0)?.toUpperCase() + word?.slice(1))
  //   ?.join(' ');
  return value
    ?.toLowerCase()
    ?.replace(/[-_]+/g, ' ') // Replace - and _ with a space
    ?.split(' ') // Split into words
    ?.map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    ?.join(' ');
};

/**
 * Converts an empty string or undefined to null.
 */
export function emptyToNull<T>(value: T | '' | undefined): T | null {
  return value === '' || value === undefined ? null : value;
}

export const normalizeToNegative = (value: number) => -Math.abs(value);
export const normalizeToPositive = (value: number) => Math.abs(value);

//Utility function to normalize and format a phone number input with optional extension.

export const normalizePhoneWithExtension = (input: string) => {
  // Clean the input by removing any non-numeric characters, 'x', or 'ext'
  const cleaned = input?.replace(/[^\dextx]/gi, '');

  // Match the phone number and optional extension
  const phoneMatch = cleaned?.match(/^(\d{0,10})(?:\s*(ext|x)?\s*(\d{0,5}))?/i);

  if (!phoneMatch) return cleaned;

  const phone = phoneMatch[1] ?? ''; // Extract phone number
  const ext = phoneMatch[3]; // Extract extension

  // Format the phone number (XXX) XXX-XXXX style
  let formatted = phone;
  if (phone?.length >= 6) {
    formatted = `(${phone.slice(0, 3)}) ${phone.slice(3, 6)}${phone.length > 6 ? `-${phone.slice(6)}` : ''}`;
  } else if (phone?.length >= 3) {
    formatted = `(${phone.slice(0, 3)}) ${phone.slice(3)}`;
  }

  // If an extension is provided, add it after the phone number (e.g., x123)
  return ext ? `${formatted} x${ext}` : formatted;
};
