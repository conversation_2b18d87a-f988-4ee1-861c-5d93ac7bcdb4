import {
  PaymentsItemTypes,
  PaymentTypeEnum,
  RefundConvFeeTypes,
  UnappliedAmountTypes,
} from '@/types/accounting.types';
import { StorePaymentsTypes } from '@/types/store.types';
import { normalizeToNegative, normalizeToPositive } from './utils';

export const getUnappliedAmount = ({
  amount,
  payments,
  applyCredit,
  creditAmount,
}: UnappliedAmountTypes) => {
  return amount - creditAmount - (payments + applyCredit);
};

export const calculateConvenienceFeeByType = (
  paymentType: string,
  value: number,
  storePaymentConfig: StorePaymentsTypes
): number => {
  const feeKeyMap: Record<string, keyof StorePaymentsTypes> = {
    [PaymentTypeEnum.ACH]: 'achConvenienceFees',
    [PaymentTypeEnum.CREDIT_CARD]: 'creditCardConvenienceFees',
    [PaymentTypeEnum.DEBIT_CARD]: 'debitCardConvenienceFees',
  };

  const feeKey = feeKeyMap[paymentType];

  const rawPercentage = feeKey ? storePaymentConfig?.[feeKey] : 0;
  const feePercentage =
    typeof rawPercentage === 'number'
      ? rawPercentage
      : Number(rawPercentage) || 0;

  const fee = (value * feePercentage) / 100;
  return Number(fee.toFixed(2));
};

export const mapRefundFormValues = (
  data: PaymentsItemTypes
): RefundConvFeeTypes => {
  const originalAmount = Number(data?.payment || 0);
  const convFee = Number(data?.orderConvFee || 0);
  const refundIncludingConvFee = normalizeToPositive(originalAmount) + convFee;

  return {
    orderId: data?.orderId,
    orderNo: data.orderNo ?? '',
    refundAmountType: 'refundIncludingConvFee',
    refundIncludingConvFee: normalizeToNegative(refundIncludingConvFee),
    refundOriginalAmount: originalAmount,
  };
};
