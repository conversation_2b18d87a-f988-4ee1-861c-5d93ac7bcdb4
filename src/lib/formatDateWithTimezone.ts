import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { DATE_FORMAT_YYYYMMDD, getStorageValue } from './utils';

dayjs.extend(utc);
dayjs.extend(timezone);

/**
 * Returns a formatted date string in the specified timezone.
 *
 * @param format - Desired output format (e.g., 'YYYY-MM-DD')
 * @param tz - Timezone string (default: 'UTC')
 * @param date - Optional date input (default: now)
 * @returns Formatted date string
 */
export const formatDateWithTimezone = ({
  date,
  format = DATE_FORMAT_YYYYMMDD,
  tz = '+00:00',
}: {
  date?: string | Date;
  format?: string;
  tz?: string;
} = {}): string => {
  const timeZoneoffset = getStorageValue('timeZoneOffset') || '';

  return dayjs(date)
    .tz(tz || timeZoneoffset)
    .format(format);
};
