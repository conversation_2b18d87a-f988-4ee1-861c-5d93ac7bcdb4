import isEqual from 'lodash/isEqual';

/**
 * Returns only the modified (or new) items by deep comparing two arrays.
 *
 * @template T - The type of items in the arrays.
 * @param currentItems - The current array of items.
 * @param defaultItems - The default (original) array of items.
 * @returns An array of items that are either new or have been modified.
 */
export function getModifiedItems<T>(
  currentItems: T[] = [],
  defaultItems: T[] = []
): T[] {
  return currentItems.filter((currentItem, index) => {
    // If there's no corresponding default item at this index, it's a new item.
    if (!defaultItems[index]) {
      return true;
    }
    // Otherwise, if the current item is not deeply equal to the default item, it's modified.
    return !isEqual(currentItem, defaultItems[index]);
  });
}
