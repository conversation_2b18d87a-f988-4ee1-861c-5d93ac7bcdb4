export interface UserPermission {
  permission: string;
  enabled: boolean;
}

/**
 * Normalizes a permission string for case-insensitive comparison.
 * Trims and converts to uppercase.
 */
const normalizePermission = (value: string): string =>
  value.trim().toUpperCase();

/**
 * Determines whether the current user has at least one of the specified permissions enabled.
 *
 * @param requiredPermissions - A single permission or list of permissions to validate.
 * @returns true if the user has any of the required permissions enabled.
 */
export const hasEnabledPermission = (
  requiredPermissions: string | string[]
): boolean => {
  const targetPermissions = Array.isArray(requiredPermissions)
    ? requiredPermissions.map(normalizePermission)
    : [normalizePermission(requiredPermissions)];

  const storedPermissionsRaw = localStorage.getItem('permissions') ?? '[]';
  const storedPermissions: UserPermission[] = JSON.parse(storedPermissionsRaw);

  return storedPermissions.some(
    (perm) =>
      perm.enabled &&
      targetPermissions?.includes(normalizePermission(perm.permission))
  );
};
