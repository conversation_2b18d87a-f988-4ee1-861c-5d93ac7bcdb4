import { adjustmentsApi } from '@/redux/features/accounting/adjustments.api';
import { accountingInquiryApi } from '@/redux/features/accounting/Inquiry.api';
import { paymentsApi } from '@/redux/features/accounting/payments.api';
import { authApi } from '@/redux/features/auth/authApi';
import { commonApi } from '@/redux/features/common-api/common.api';
import { companyApi } from '@/redux/features/company/company.api';
import { countyList } from '@/redux/features/country/country.api';
import { customerTypeApi } from '@/redux/features/customer-type/customer-type.api';
import { choicesApi } from '@/redux/features/customers/choices.api';
import { contactsApi } from '@/redux/features/customers/contacts.api';
import { customerApi } from '@/redux/features/customers/customer.api';
import { discountApi } from '@/redux/features/customers/discount.api';
import { filesApi } from '@/redux/features/customers/files';
import { notesApi } from '@/redux/features/customers/notes.api';
import { deliveryTypeApi } from '@/redux/features/delivery-type/delivery-type.api';
import { esignApi } from '@/redux/features/e-sign/e-sign.api';
import { enumsApi } from '@/redux/features/enums-api/enums-api';
import { inventoryManagerApi } from '@/redux/features/inventory-manager/inventory-manager.api';
import { itemApi } from '@/redux/features/items/item.api';
import { linkedFilesApi } from '@/redux/features/items/linkedFiles.api';
import { listApi } from '@/redux/features/list/category/list.api';
import { onlinePaymentApi } from '@/redux/features/online-payment/online-payment.api';
import { additionalInfoApi } from '@/redux/features/orders/additional-info.api';
import { additionalItemInfoApi } from '@/redux/features/orders/additional-item-info.api';
import { itemDetailsApi } from '@/redux/features/orders/item-details.api';
import { orderApi } from '@/redux/features/orders/order.api';
import { paymentTermApi } from '@/redux/features/payment-term/payement-term.api';
import { paymentTypeApi } from '@/redux/features/payment-type/payment-type.api';
import { processesApi } from '@/redux/features/processes/processes.api';
import { purchaseOrderApi } from '@/redux/features/purchase-order/purchaseOrder.api';
import { referralTypeApi } from '@/redux/features/referral-type/referral-type.api';
import { salesTaxCodeApi } from '@/redux/features/sales-tax-code/sales-tax-code.api';
import { stateList } from '@/redux/features/state/state.api';
import { storeApi } from '@/redux/features/store/store.api';
import { subRentalApi } from '@/redux/features/sub-rental/subRental.api';
import { customerSubTypes } from '@/redux/features/sub-types/sub-types.api';
import { systemUserApi } from '@/redux/features/system/users.api';
import { accountAPI } from '@/redux/features/tenant/tenant.api';
import { timezoneList } from '@/redux/features/timezone/timezone.api';
import { imagesApi } from '@/redux/features/user-options/images.api';
import { loginProfileApi } from '@/redux/features/user-options/login-profile-api';
import { profileDetailsApi } from '@/redux/features/user-options/profile-details.api';
import { vendorsApi } from '@/redux/features/vendors-api/vendors.api';
import { truckItinerariesApi } from '@/redux/features/warehouse/truckItineraries.api';
import { warehouseCustomPickupReturnApi } from '@/redux/features/warehouse/warehouseCustomPickupReturn.api';
import { warehouseOrderDeliveriesPickupsApi } from '@/redux/features/warehouse/warehouseOrderDeliveriesPickups.api';
import { warehouseSubrentalPickupsReturnsApi } from '@/redux/features/warehouse/warehouseSubrentalPickupsReturns.api';

export const allApis = [
  authApi,
  customerApi,
  itemApi,
  customerTypeApi,
  paymentTermApi,
  paymentTypeApi,
  salesTaxCodeApi,
  referralTypeApi,
  deliveryTypeApi,
  listApi,
  countyList,
  stateList,
  timezoneList,
  accountAPI,
  customerSubTypes,
  notesApi,
  discountApi,
  choicesApi,
  commonApi,
  contactsApi,
  filesApi,
  profileDetailsApi,
  companyApi,
  imagesApi,
  enumsApi,
  loginProfileApi,
  storeApi,
  linkedFilesApi,
  adjustmentsApi,
  vendorsApi,
  systemUserApi,
  orderApi,
  purchaseOrderApi,
  itemDetailsApi,
  additionalItemInfoApi,
  additionalInfoApi,
  inventoryManagerApi,
  onlinePaymentApi,
  subRentalApi,
  esignApi,
  warehouseCustomPickupReturnApi,
  warehouseOrderDeliveriesPickupsApi,
  processesApi,
  accountingInquiryApi,
  warehouseSubrentalPickupsReturnsApi,
  truckItinerariesApi,
  paymentsApi,
];
