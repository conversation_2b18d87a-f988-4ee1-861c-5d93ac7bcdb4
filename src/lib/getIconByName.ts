// iconUtils.ts
import { FileBadge, FileSpreadsheet, Printer, SaveAll } from 'lucide-react';

const iconMap: Record<string, React.FC<React.SVGProps<SVGSVGElement>>> = {
  SaveAll,
  FileBadge,
  FileSpreadsheet,
  Printer,
};

/**
 * Returns the icon component based on the provided icon name.
 * Falls back to a default icon if not found.
 *
 * @param iconName - The name of the icon string (e.g., "SaveAll")
 * @returns A React icon component (not rendered yet)
 */
export function getIconByName(
  iconName: string
): React.FC<React.SVGProps<SVGSVGElement>> {
  return iconMap[iconName] || Printer;
}
