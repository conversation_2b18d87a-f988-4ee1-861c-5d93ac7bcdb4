import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import { Provider } from 'react-redux';
import App from './App.tsx';
import { ToastContainer } from './components/ui/toast/ToastContainer.tsx';
import './index.css';
import { store } from './redux/store.ts';
// const App = lazy(() => import('./App.tsx'));

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <Provider store={store}>
      {/* <SuspenseWrapper> */}
      <App />
      {/* </SuspenseWrapper> */}
      <ToastContainer />
    </Provider>
  </StrictMode>
);
