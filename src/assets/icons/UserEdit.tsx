const ClientEditIcon = () => {
  return (
    <svg
      width="48"
      height="48"
      viewBox="0 0 48 48"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M17.999 31H14.999C12.2079 31 10.8123 31 9.67674 31.3445C7.11993 32.1201 5.1191 34.1209 4.3435 36.6777C3.99902 37.8133 3.99902 39.2089 3.99902 42M28.999 15C28.999 19.9706 24.9696 24 19.999 24C15.0285 24 10.999 19.9706 10.999 15C10.999 10.0294 15.0285 6 19.999 6C24.9696 6 28.999 10.0294 28.999 15ZM21.999 42L28.2017 40.2278C28.4988 40.1429 28.6473 40.1005 28.7858 40.0369C28.9088 39.9804 29.0257 39.9116 29.1348 39.8315C29.2577 39.7413 29.3669 39.6321 29.5854 39.4137L42.4991 26.5001C43.8799 25.1193 43.8799 22.8807 42.4991 21.4999C41.1183 20.1193 38.8798 20.1193 37.4991 21.5L24.5854 34.4137C24.3669 34.6321 24.2577 34.7413 24.1675 34.8642C24.0874 34.9733 24.0186 35.0902 23.9621 35.2132C23.8985 35.3518 23.8561 35.5003 23.7712 35.7973L21.999 42Z"
        stroke="#F3F3F3"
        strokeWidth="4.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default ClientEditIcon;
