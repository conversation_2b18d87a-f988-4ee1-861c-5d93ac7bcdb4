const SystemIcon = ({ iconColor = '#F3F3F3' }: { iconColor?: string }) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12.9165 9.58333H12.0832L10.8332 12.0833L9.1665 7.08333L7.9165 9.58333H7.08317M9.99413 4.27985C8.328 2.332 5.54963 1.80804 3.46208 3.59168C1.37454 5.37532 1.08064 8.35748 2.72 10.467C3.95825 12.0604 7.47591 15.2591 9.12318 16.7291C9.42599 16.9993 9.5774 17.1344 9.7547 17.1876C9.90861 17.2338 10.0796 17.2338 10.2336 17.1876C10.4109 17.1344 10.5623 16.9993 10.8651 16.7291C12.5123 15.2591 16.03 12.0604 17.2683 10.467C18.9076 8.35748 18.6496 5.35656 16.5262 3.59168C14.4028 1.8268 11.6603 2.332 9.99413 4.27985Z"
        stroke={iconColor}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default SystemIcon;
