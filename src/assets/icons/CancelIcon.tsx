interface CancelIconProps {
  className?: string;
  onClick?: () => void;
  iconColor?: string;
}

const CancelIcon = ({
  className,
  onClick,
  iconColor = '#1E1E1E',
}: CancelIconProps) => {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      className={className}
      onClick={onClick}
      xmlns="http://www.w3.org/2000/svg"
      aria-label="cancel icon"
    >
      <path
        d="M12 4L4 12M4 4L12 12"
        stroke={iconColor}
        strokeWidth="1.6"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default CancelIcon;
