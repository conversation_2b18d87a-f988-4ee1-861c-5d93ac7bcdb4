const SettingIcon = () => {
  return (
    <svg
      width="36"
      height="36"
      viewBox="0 0 36 36"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M0.5 18C0.5 8.33502 8.33502 0.5 18 0.5C27.665 0.5 35.5 8.33502 35.5 18C35.5 27.665 27.665 35.5 18 35.5C8.33502 35.5 0.5 27.665 0.5 18Z"
        fill="#04AAAF"
      />
      <path
        d="M0.5 18C0.5 8.33502 8.33502 0.5 18 0.5C27.665 0.5 35.5 8.33502 35.5 18C35.5 27.665 27.665 35.5 18 35.5C8.33502 35.5 0.5 27.665 0.5 18Z"
        stroke="#04BCC2"
        strokeLinecap="round"
      />
      <path
        d="M24.9583 15.4358L24.6531 15.266C24.6057 15.2396 24.5824 15.2264 24.5595 15.2127C24.332 15.0764 24.1401 14.888 24.0002 14.6627C23.9861 14.64 23.9728 14.6162 23.9457 14.5692C23.9186 14.5223 23.9048 14.4985 23.8922 14.475C23.7666 14.2406 23.6987 13.9793 23.6947 13.7134C23.6943 13.6867 23.6943 13.6593 23.6953 13.605L23.7012 13.2507C23.7108 12.6835 23.7156 12.3991 23.6359 12.1438C23.5651 11.9171 23.4466 11.7083 23.2885 11.531C23.1097 11.3307 22.8623 11.1878 22.3669 10.9023L21.9554 10.6652C21.4613 10.3805 21.2142 10.2381 20.9519 10.1838C20.7199 10.1358 20.4804 10.138 20.2492 10.1899C19.9883 10.2485 19.7443 10.3946 19.2566 10.6866L19.2538 10.688L18.9589 10.8645C18.9123 10.8924 18.8887 10.9065 18.8653 10.9195C18.6334 11.0484 18.3746 11.1197 18.1094 11.1282C18.0826 11.1291 18.0555 11.1291 18.0011 11.1291C17.9471 11.1291 17.9187 11.1291 17.892 11.1282C17.6263 11.1197 17.3669 11.048 17.1346 10.9185C17.1112 10.9055 17.088 10.8913 17.0413 10.8633L16.7446 10.6851C16.2535 10.3903 16.0076 10.2427 15.7452 10.1838C15.5131 10.1317 15.2728 10.1303 15.0399 10.1789C14.777 10.2338 14.5298 10.3773 14.0355 10.6642L14.0333 10.6652L13.6269 10.901L13.6224 10.9038C13.1325 11.1881 12.887 11.3306 12.7097 11.5301C12.5524 11.7072 12.4349 11.9157 12.3645 12.1418C12.285 12.3974 12.2892 12.6825 12.2988 13.2522L12.3047 13.6061C12.3056 13.6597 12.3072 13.6863 12.3068 13.7127C12.3029 13.9792 12.234 14.2409 12.1081 14.4758C12.0957 14.499 12.0822 14.5223 12.0555 14.5686C12.0286 14.6151 12.0156 14.6382 12.0017 14.6606C11.8611 14.8871 11.6684 15.0766 11.4394 15.2131C11.4168 15.2266 11.3929 15.2396 11.346 15.2656L11.0447 15.4326C10.5434 15.7104 10.2928 15.8494 10.1105 16.0472C9.94915 16.2223 9.82729 16.4299 9.75291 16.656C9.66882 16.9116 9.66889 17.1982 9.6702 17.7713L9.67126 18.2397C9.67255 18.809 9.67433 19.0935 9.7586 19.3474C9.83316 19.5719 9.95412 19.7783 10.1145 19.9523C10.2958 20.1489 10.5439 20.2871 11.0415 20.5639L11.3401 20.73C11.3909 20.7582 11.4165 20.7722 11.441 20.787C11.6679 20.9236 11.8591 21.1125 11.9985 21.3378C12.0135 21.3621 12.028 21.3874 12.0569 21.4379C12.0855 21.4878 12.1001 21.5127 12.1133 21.5377C12.2355 21.7691 12.3009 22.0262 12.3054 22.2879C12.3059 22.3162 12.3055 22.3448 12.3045 22.4022L12.2988 22.7418C12.2891 23.3136 12.2849 23.5998 12.3649 23.8561C12.4357 24.0828 12.554 24.2916 12.7122 24.4689C12.891 24.6692 13.1388 24.8121 13.6342 25.0975L14.0456 25.3346C14.5397 25.6193 14.7866 25.7615 15.0489 25.8158C15.2809 25.8638 15.5205 25.862 15.7517 25.8101C16.0131 25.7514 16.2579 25.6048 16.747 25.3119L17.0419 25.1354C17.0885 25.1075 17.1121 25.0934 17.1355 25.0805C17.3674 24.9515 17.626 24.8799 17.8912 24.8713C17.918 24.8705 17.9451 24.8705 17.9995 24.8705C18.054 24.8705 18.0812 24.8705 18.1079 24.8714C18.3737 24.8799 18.6339 24.9518 18.8662 25.0813C18.8866 25.0927 18.9071 25.105 18.943 25.1266L19.2565 25.3147C19.7476 25.6096 19.993 25.7568 20.2554 25.8157C20.4875 25.8677 20.728 25.8699 20.9609 25.8212C21.2238 25.7663 21.4714 25.6226 21.9655 25.3358L22.378 25.0964C22.8682 24.8119 23.1139 24.6693 23.2913 24.4697C23.4486 24.2926 23.5663 24.0842 23.6367 23.8581C23.7156 23.6043 23.7109 23.3215 23.7014 22.7599L23.6953 22.3937C23.6944 22.3401 23.6943 22.3134 23.6946 22.2871C23.6986 22.0206 23.7663 21.7587 23.8922 21.5238C23.9046 21.5006 23.9182 21.4772 23.9449 21.431C23.9717 21.3845 23.9856 21.3614 23.9995 21.339C24.1401 21.1125 24.333 20.9228 24.562 20.7863C24.5843 20.7729 24.6074 20.7602 24.6532 20.7348L24.6547 20.7341L24.956 20.5671C25.4573 20.2893 25.7084 20.1501 25.8908 19.9523C26.0521 19.7773 26.1738 19.5699 26.2482 19.3438C26.3317 19.0898 26.3311 18.8048 26.3298 18.2384L26.3287 17.7599C26.3274 17.1906 26.3267 16.9062 26.2425 16.6523C26.1679 16.4277 26.0463 16.2213 25.8859 16.0474C25.7047 15.8509 25.4563 15.7127 24.9597 15.4365L24.9583 15.4358Z"
        stroke="white"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M14.6669 18C14.6669 19.8409 16.1593 21.3333 18.0003 21.3333C19.8412 21.3333 21.3336 19.8409 21.3336 18C21.3336 16.159 19.8412 14.6666 18.0003 14.6666C16.1593 14.6666 14.6669 16.159 14.6669 18Z"
        stroke="white"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default SettingIcon;
