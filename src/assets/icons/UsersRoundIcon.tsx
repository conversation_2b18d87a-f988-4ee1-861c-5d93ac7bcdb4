const UsersRoundIcon = ({ iconColor = '#F3F3F3' }: { iconColor?: string }) => {
  return (
    <svg
      width="18"
      height="15"
      viewBox="0 0 18 15"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M13.1667 13.6665C13.1667 12.2858 11.3012 11.1665 9 11.1665C6.69881 11.1665 4.83333 12.2858 4.83333 13.6665M16.5 11.1668C16.5 10.1417 15.4716 9.26061 14 8.87484M1.5 11.1668C1.5 10.1417 2.52841 9.26061 4 8.87484M14 5.52993C14.5115 5.07216 14.8333 4.40692 14.8333 3.6665C14.8333 2.28579 13.714 1.1665 12.3333 1.1665C11.693 1.1665 11.109 1.40722 10.6667 1.80308M4 5.52993C3.48854 5.07216 3.16667 4.40692 3.16667 3.6665C3.16667 2.28579 4.28595 1.1665 5.66667 1.1665C6.30696 1.1665 6.89104 1.40722 7.33333 1.80308M9 8.6665C7.61929 8.6665 6.5 7.54722 6.5 6.1665C6.5 4.78579 7.61929 3.6665 9 3.6665C10.3807 3.6665 11.5 4.78579 11.5 6.1665C11.5 7.54722 10.3807 8.6665 9 8.6665Z"
        stroke={iconColor}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default UsersRoundIcon;
