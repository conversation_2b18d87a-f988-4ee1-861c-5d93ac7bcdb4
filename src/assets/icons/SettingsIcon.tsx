import { cn } from '@/lib/utils';

const SettingsIcon = ({ className }: { className?: string }) => {
  return (
    <svg
      width="19"
      height="18"
      viewBox="0 0 19 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={cn('text-[#767676] [.active_&]:text-[#491D97]', className)}
    >
      <g clipPath="url(#clip0_866_12634)">
        <path
          d="M16.0486 6.69268L15.7739 6.53989C15.7313 6.51616 15.7103 6.50426 15.6897 6.49192C15.4849 6.36923 15.3123 6.19971 15.1863 5.99691C15.1736 5.9765 15.1617 5.9551 15.1372 5.91281C15.1128 5.87057 15.1005 5.84916 15.0891 5.82797C14.9761 5.61698 14.915 5.38185 14.9113 5.14253C14.911 5.11848 14.911 5.09389 14.9119 5.04502L14.9172 4.72607C14.9258 4.21568 14.9301 3.95969 14.8584 3.72995C14.7947 3.52589 14.6881 3.33793 14.5458 3.17843C14.3849 2.99813 14.1622 2.8695 13.7163 2.61256L13.346 2.39914C12.9013 2.14292 12.6789 2.01477 12.4429 1.96591C12.234 1.92269 12.0185 1.92469 11.8104 1.97141C11.5756 2.02413 11.356 2.15562 10.917 2.41846L10.9145 2.41965L10.6492 2.57854C10.6072 2.60367 10.586 2.61633 10.5649 2.62803C10.3562 2.74407 10.1232 2.80823 9.88456 2.81589C9.86051 2.81666 9.83604 2.81666 9.78711 2.81666C9.73849 2.81666 9.71297 2.81666 9.68897 2.81589C9.44978 2.8082 9.2163 2.74368 9.00727 2.62716C8.9862 2.61542 8.96536 2.60266 8.92331 2.57741L8.65625 2.41709C8.2143 2.15176 7.99299 2.0189 7.75684 1.96591C7.54791 1.91904 7.33166 1.91774 7.12206 1.96152C6.8854 2.01095 6.66296 2.14005 6.21807 2.39826L6.21609 2.39914L5.85034 2.61142L5.8463 2.61389C5.40541 2.86978 5.18444 2.99803 5.02491 3.17759C4.88333 3.33694 4.77752 3.5246 4.71418 3.72812C4.64259 3.95817 4.6464 4.21471 4.65503 4.72751L4.66039 5.04601C4.6612 5.09424 4.66261 5.1182 4.66226 5.14192C4.65871 5.38173 4.59678 5.61734 4.48344 5.82871C4.47223 5.84961 4.46016 5.87052 4.43604 5.91227C4.4119 5.95405 4.40021 5.97483 4.3877 5.99499C4.26116 6.19888 4.08774 6.36938 3.88163 6.49228C3.86124 6.50444 3.83976 6.51613 3.79754 6.53952L3.52637 6.6898C3.0752 6.93982 2.84966 7.06495 2.68555 7.24301C2.54037 7.40053 2.4307 7.58738 2.36375 7.79087C2.28807 8.02089 2.28814 8.27883 2.28931 8.79465L2.29027 9.21624C2.29143 9.72862 2.29303 9.98464 2.36887 10.2131C2.43597 10.4152 2.54485 10.601 2.68921 10.7575C2.85239 10.9345 3.07569 11.0589 3.52344 11.308L3.7922 11.4575C3.83793 11.4829 3.86095 11.4955 3.88301 11.5088C4.08724 11.6317 4.2593 11.8018 4.38477 12.0045C4.39832 12.0264 4.41134 12.0491 4.43735 12.0946C4.46305 12.1395 4.4762 12.1619 4.48809 12.1844C4.59809 12.3927 4.65699 12.6241 4.661 12.8596C4.66144 12.885 4.66107 12.9108 4.66019 12.9625L4.65503 13.2681C4.64635 13.7827 4.64256 14.0403 4.71457 14.271C4.77828 14.475 4.88476 14.663 5.0271 14.8225C5.188 15.0028 5.41105 15.1313 5.85694 15.3883L6.22722 15.6017C6.67185 15.8579 6.89409 15.9859 7.13015 16.0347C7.33898 16.078 7.55462 16.0763 7.7627 16.0296C7.99788 15.9768 8.21824 15.8448 8.65845 15.5812L8.92382 15.4223C8.9658 15.3972 8.98706 15.3846 9.00809 15.3729C9.2168 15.2569 9.44956 15.1924 9.68824 15.1847C9.71229 15.1839 9.73676 15.1839 9.78569 15.1839C9.83473 15.1839 9.85917 15.1839 9.88328 15.1847C10.1225 15.1924 10.3566 15.2571 10.5657 15.3736C10.5841 15.3839 10.6025 15.395 10.6349 15.4144L10.917 15.5838C11.359 15.8491 11.5798 15.9816 11.816 16.0346C12.0249 16.0815 12.2413 16.0834 12.4509 16.0396C12.6875 15.9902 12.9104 15.8608 13.3551 15.6027L13.7263 15.3873C14.1675 15.1312 14.3887 15.0028 14.5483 14.8232C14.6898 14.6639 14.7958 14.4763 14.8591 14.2728C14.9302 14.0444 14.9259 13.7898 14.9174 13.2844L14.9119 12.9548C14.9111 12.9066 14.911 12.8826 14.9113 12.8589C14.9149 12.6191 14.9758 12.3833 15.0891 12.1719C15.1003 12.151 15.1125 12.13 15.1365 12.0884C15.1606 12.0466 15.1731 12.0257 15.1857 12.0056C15.3122 11.8017 15.4858 11.631 15.6919 11.5082C15.712 11.4961 15.7328 11.4847 15.774 11.4618L15.7754 11.4612L16.0466 11.3109C16.4977 11.0609 16.7237 10.9356 16.8878 10.7575C17.033 10.6 17.1425 10.4134 17.2095 10.2099C17.2847 9.98127 17.2841 9.72483 17.283 9.21504L17.282 8.7844C17.2808 8.27202 17.2802 8.01604 17.2043 7.78757C17.1372 7.58545 17.0278 7.3997 16.8834 7.24312C16.7204 7.06632 16.4968 6.94193 16.0499 6.69332L16.0486 6.69268Z"
          className="stroke-current"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M6.78638 9.00046C6.78638 10.6573 8.12953 12.0005 9.78638 12.0005C11.4432 12.0005 12.7864 10.6573 12.7864 9.00046C12.7864 7.34361 11.4432 6.00046 9.78638 6.00046C8.12953 6.00046 6.78638 7.34361 6.78638 9.00046Z"
          className="stroke-current"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_866_12634">
          <rect
            width="18"
            height="18"
            fill="white"
            transform="translate(0.786133)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default SettingsIcon;
