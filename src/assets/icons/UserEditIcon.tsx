import { cn } from '@/lib/utils';

const UserEditIcon = ({ className }: { className?: string }) => {
  return (
    <svg
      width="19"
      height="18"
      viewBox="0 0 19 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={cn('text-[#767676] [.active_&]:text-[#491D97]', className)}
    >
      <g clipPath="url(#clip0_866_11963)">
        <path
          d="M6.82129 11.625H5.69629C4.64962 11.625 4.12628 11.625 3.70043 11.7542C2.74163 12.045 1.99132 12.7953 1.70047 13.7541C1.57129 14.18 1.57129 14.7033 1.57129 15.75M10.9463 5.625C10.9463 7.48896 9.43525 9 7.57129 9C5.70733 9 4.19629 7.48896 4.19629 5.625C4.19629 3.76104 5.70733 2.25 7.57129 2.25C9.43525 2.25 10.9463 3.76104 10.9463 5.625ZM8.32129 15.75L10.6473 15.0854C10.7587 15.0536 10.8144 15.0377 10.8663 15.0138C10.9125 14.9927 10.9563 14.9669 10.9972 14.9368C11.0433 14.903 11.0843 14.862 11.1662 14.7801L16.0088 9.93753C16.5266 9.41975 16.5266 8.58025 16.0088 8.06248C15.491 7.54472 14.6516 7.54473 14.1338 8.0625L9.29117 12.9051C9.20925 12.987 9.16829 13.028 9.13447 13.0741C9.10443 13.115 9.07863 13.1588 9.05745 13.205C9.0336 13.2569 9.01769 13.3126 8.98586 13.424L8.32129 15.75Z"
          className="stroke-current"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_866_11963">
          <rect
            width="18"
            height="18"
            fill="white"
            transform="translate(0.0712891)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default UserEditIcon;
