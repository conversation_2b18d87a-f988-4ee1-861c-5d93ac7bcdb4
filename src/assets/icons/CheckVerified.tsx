const CheckVerified = () => {
  return (
    <svg
      width="17"
      height="16"
      viewBox="0 0 17 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6.49967 7.99967L7.83301 9.33301L10.833 6.33301M12.4338 3.33201C12.5711 3.66403 12.8346 3.92794 13.1664 4.06572L14.3298 4.54766C14.6619 4.6852 14.9257 4.949 15.0632 5.28105C15.2007 5.61309 15.2007 5.98617 15.0632 6.31821L14.5816 7.48088C14.444 7.81307 14.4438 8.18652 14.5821 8.51855L15.0628 9.68087C15.131 9.84533 15.1661 10.0216 15.1661 10.1996C15.1661 10.3777 15.1311 10.554 15.063 10.7184C14.9948 10.8829 14.895 11.0324 14.7691 11.1582C14.6432 11.2841 14.4937 11.3839 14.3292 11.452L13.1666 11.9336C12.8346 12.0709 12.5707 12.3344 12.4329 12.6662L11.951 13.8297C11.8134 14.1617 11.5496 14.4255 11.2176 14.5631C10.8856 14.7006 10.5125 14.7006 10.1805 14.5631L9.01786 14.0815C8.68582 13.9443 8.31289 13.9446 7.98106 14.0823L6.8176 14.5635C6.48576 14.7007 6.11301 14.7006 5.78125 14.5632C5.44949 14.4258 5.18584 14.1623 5.04822 13.8306L4.56615 12.6667C4.42888 12.3347 4.16537 12.0708 3.83357 11.933L2.67012 11.4511C2.33823 11.3136 2.07451 11.05 1.93693 10.7181C1.79935 10.3863 1.79916 10.0134 1.93641 9.68137L2.41798 8.51871C2.55518 8.18666 2.5549 7.81371 2.4172 7.48186L1.93632 6.31753C1.86814 6.15307 1.83304 5.97679 1.83301 5.79876C1.83298 5.62073 1.86803 5.44444 1.93616 5.27996C2.00428 5.11548 2.10415 4.96604 2.23005 4.84018C2.35596 4.71431 2.50542 4.61449 2.66992 4.54641L3.83254 4.06482C4.16426 3.92766 4.428 3.66447 4.56588 3.33305L5.0478 2.16955C5.18533 1.8375 5.44913 1.5737 5.78116 1.43616C6.11319 1.29862 6.48625 1.29862 6.81828 1.43616L7.98091 1.91775C8.31295 2.05495 8.68588 2.05467 9.01771 1.91697L10.1817 1.43691C10.5136 1.29945 10.8866 1.29948 11.2186 1.43699C11.5506 1.5745 11.8143 1.83823 11.9519 2.17018L12.4339 3.33403L12.4338 3.33201Z"
        stroke="white"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default CheckVerified;
