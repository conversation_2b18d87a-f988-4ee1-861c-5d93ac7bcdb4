const WareHouseIcon = ({ iconColor = '#F3F3F3' }: { iconColor?: string }) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M11.6665 5.8335H13.6142C13.818 5.8335 13.92 5.8335 14.0159 5.85652C14.1009 5.87693 14.1822 5.91061 14.2567 5.9563C14.3408 6.00783 14.4129 6.07989 14.557 6.22402L17.9426 9.60964C18.0868 9.75376 18.1588 9.82583 18.2104 9.90992C18.2561 9.98448 18.2897 10.0658 18.3101 10.1508C18.3332 10.2467 18.3332 10.3486 18.3332 10.5524V12.9168C18.3332 13.3051 18.3332 13.4993 18.2697 13.6524C18.1852 13.8566 18.0229 14.0188 17.8187 14.1034C17.6656 14.1668 17.4715 14.1668 17.0832 14.1668M12.9165 14.1668H11.6665M11.6665 14.1668V6.00016C11.6665 5.06674 11.6665 4.60003 11.4848 4.24351C11.3251 3.92991 11.0701 3.67494 10.7565 3.51515C10.4 3.3335 9.93326 3.3335 8.99984 3.3335H4.33317C3.39975 3.3335 2.93304 3.3335 2.57652 3.51515C2.26292 3.67494 2.00795 3.92991 1.84816 4.24351C1.6665 4.60003 1.6665 5.06674 1.6665 6.00016V12.5002C1.6665 13.4206 2.4127 14.1668 3.33317 14.1668M11.6665 14.1668H8.33317M8.33317 14.1668C8.33317 15.5475 7.21388 16.6668 5.83317 16.6668C4.45246 16.6668 3.33317 15.5475 3.33317 14.1668M8.33317 14.1668C8.33317 12.7861 7.21388 11.6668 5.83317 11.6668C4.45246 11.6668 3.33317 12.7861 3.33317 14.1668M17.0832 14.5835C17.0832 15.7341 16.1504 16.6668 14.9998 16.6668C13.8492 16.6668 12.9165 15.7341 12.9165 14.5835C12.9165 13.4329 13.8492 12.5002 14.9998 12.5002C16.1504 12.5002 17.0832 13.4329 17.0832 14.5835Z"
        stroke={iconColor}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default WareHouseIcon;
