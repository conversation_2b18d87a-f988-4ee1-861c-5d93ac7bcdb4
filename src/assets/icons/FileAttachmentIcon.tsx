import { cn } from '@/lib/utils';

const FileAttachmentIcon = ({ className }: { className?: string }) => {
  return (
    <svg
      width="17"
      height="16"
      viewBox="0 0 17 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={cn('text-[#1E1E1E] [.active_&]:text-[#f5f5f5]', className)}
    >
      <path
        d="M13.8332 4.66634V4.53301C13.8332 3.4129 13.8332 2.85285 13.6152 2.42503C13.4234 2.0487 13.1175 1.74274 12.7412 1.55099C12.3133 1.33301 11.7533 1.33301 10.6332 1.33301H6.3665C5.2464 1.33301 4.68635 1.33301 4.25852 1.55099C3.8822 1.74274 3.57624 2.0487 3.38449 2.42503C3.1665 2.85285 3.1665 3.4129 3.1665 4.53301V11.4663C3.1665 12.5864 3.1665 13.1465 3.38449 13.5743C3.57624 13.9506 3.8822 14.2566 4.25852 14.4484C4.68635 14.6663 5.2464 14.6663 6.3665 14.6663H8.83317M8.83317 7.33301H5.83317M8.1665 9.99967H5.83317M11.1665 4.66634H5.83317M12.4998 11.9997V8.33301C12.4998 7.78072 12.9476 7.33301 13.4998 7.33301C14.0521 7.33301 14.4998 7.78072 14.4998 8.33301V11.9997C14.4998 13.1042 13.6044 13.9997 12.4998 13.9997C11.3953 13.9997 10.4998 13.1042 10.4998 11.9997V9.33301"
        className="stroke-current"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default FileAttachmentIcon;
