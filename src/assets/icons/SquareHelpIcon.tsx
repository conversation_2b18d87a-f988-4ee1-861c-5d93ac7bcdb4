const SquareHelpIcon = () => {
  return (
    <svg
      width="18"
      height="18"
      viewBox="0 0 15 15"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6.16667 4.33482C6.28413 4.00091 6.51598 3.71934 6.82116 3.53998C7.12633 3.36063 7.48513 3.29507 7.83401 3.35491C8.18289 3.41475 8.49934 3.59614 8.7273 3.86694C8.95526 4.13774 9.08002 4.48048 9.07949 4.83445C9.07949 5.83371 7.58061 6.33333 7.58061 6.33333M7.59994 8.33333H7.6066M4.16667 11V12.557C4.16667 12.9122 4.16667 13.0898 4.23949 13.1811C4.30282 13.2604 4.39885 13.3066 4.50036 13.3065C4.61708 13.3063 4.75578 13.1954 5.03317 12.9735L6.62348 11.7012C6.94834 11.4413 7.11078 11.3114 7.29166 11.219C7.45213 11.137 7.62295 11.0771 7.79948 11.0408C7.99845 11 8.20646 11 8.6225 11H10.3C11.4201 11 11.9802 11 12.408 10.782C12.7843 10.5903 13.0903 10.2843 13.282 9.90798C13.5 9.48016 13.5 8.9201 13.5 7.8V4.2C13.5 3.07989 13.5 2.51984 13.282 2.09202C13.0903 1.71569 12.7843 1.40973 12.408 1.21799C11.9802 1 11.4201 1 10.3 1H4.7C3.5799 1 3.01984 1 2.59202 1.21799C2.21569 1.40973 1.90973 1.71569 1.71799 2.09202C1.5 2.51984 1.5 3.07989 1.5 4.2V8.33333C1.5 8.95331 1.5 9.2633 1.56815 9.51764C1.75308 10.2078 2.29218 10.7469 2.98236 10.9319C3.2367 11 3.54669 11 4.16667 11Z"
        stroke="#1E1E1E"
        className="stroke:#1E1E1E;stroke:color(display-p3 0.1176 0.1176 0.1176);stroke-opacity:1;"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default SquareHelpIcon;
