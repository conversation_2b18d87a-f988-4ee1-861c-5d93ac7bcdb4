const ClientsIcon = ({ iconColor = '#F3F3F3' }: { iconColor?: string }) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M18.3307 17.5V15.8333C18.3307 14.2801 17.2684 12.9751 15.8307 12.605M12.9141 2.7423C14.1357 3.23679 14.9974 4.43443 14.9974 5.83333C14.9974 7.23224 14.1357 8.42988 12.9141 8.92437M14.1641 17.5C14.1641 15.9469 14.1641 15.1703 13.9103 14.5577C13.572 13.741 12.9231 13.092 12.1063 12.7537C11.4938 12.5 10.7172 12.5 9.16406 12.5H6.66406C5.11092 12.5 4.33435 12.5 3.72178 12.7537C2.90502 13.092 2.25611 13.741 1.9178 14.5577C1.66406 15.1703 1.66406 15.9469 1.66406 17.5M11.2474 5.83333C11.2474 7.67428 9.75501 9.16667 7.91406 9.16667C6.07311 9.16667 4.58073 7.67428 4.58073 5.83333C4.58073 3.99238 6.07311 2.5 7.91406 2.5C9.75501 2.5 11.2474 3.99238 11.2474 5.83333Z"
        stroke={iconColor}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default ClientsIcon;
