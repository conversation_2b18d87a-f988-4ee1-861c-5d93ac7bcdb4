const CopyIcon = ({ className }: { className?: string }) => {
  return (
    <svg
      width="17"
      height="16"
      viewBox="0 0 17 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M6.5 6V4.13346C6.5 3.38673 6.5 3.01308 6.64532 2.72786C6.77316 2.47698 6.97698 2.27316 7.22786 2.14532C7.51308 2 7.88673 2 8.63346 2H12.3668C13.1135 2 13.4867 2 13.7719 2.14532C14.0228 2.27316 14.227 2.47698 14.3548 2.72786C14.5001 3.01308 14.5001 3.38645 14.5001 4.13319V7.86654C14.5001 8.61327 14.5001 8.98664 14.3548 9.27186C14.227 9.52274 14.0226 9.72699 13.7717 9.85482C13.4868 10 13.114 10 12.3687 10H10.5M6.5 6H4.63346C3.88673 6 3.51308 6 3.22786 6.14532C2.97698 6.27316 2.77316 6.47698 2.64532 6.72786C2.5 7.01308 2.5 7.38673 2.5 8.13346V11.8668C2.5 12.6135 2.5 12.9867 2.64532 13.2719C2.77316 13.5228 2.97698 13.727 3.22786 13.8548C3.5128 14 3.886 14 4.63127 14H8.36903C9.11431 14 9.48698 14 9.77192 13.8548C10.0228 13.727 10.227 13.5226 10.3548 13.2717C10.5 12.9868 10.5 12.614 10.5 11.8687V10M6.5 6H8.3668C9.11353 6 9.48671 6 9.77192 6.14532C10.0228 6.27316 10.227 6.47698 10.3548 6.72786C10.5 7.0128 10.5 7.386 10.5 8.1313L10.5 10"
        stroke="#1E1E1E"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default CopyIcon;
