const BankNoteIcon = () => {
  return (
    <svg
      width="19"
      height="18"
      viewBox="0 0 19 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="text-[#767676] [.active_&]:text-[#491D97]"
    >
      <g clipPath="url(#clip0_866_11862)">
        <path
          d="M11.3809 6.75H9.50586C8.88454 6.75 8.38086 7.25368 8.38086 7.875C8.38086 8.49632 8.88454 9 9.50586 9H10.2559C10.8772 9 11.3809 9.50368 11.3809 10.125C11.3809 10.7463 10.8772 11.25 10.2559 11.25H8.38086M9.88086 6V6.75M9.88086 11.25V12M14.3809 9H14.3884M5.38086 9H5.38836M2.38086 6.15L2.38086 11.85C2.38086 12.6901 2.38086 13.1101 2.54435 13.431C2.68816 13.7132 2.91763 13.9427 3.19987 14.0865C3.52074 14.25 3.94078 14.25 4.78086 14.25L14.9809 14.25C15.8209 14.25 16.241 14.25 16.5618 14.0865C16.8441 13.9427 17.0736 13.7132 17.2174 13.431C17.3809 13.1101 17.3809 12.6901 17.3809 11.85V6.15C17.3809 5.30992 17.3809 4.88988 17.2174 4.56902C17.0736 4.28677 16.8441 4.0573 16.5618 3.91349C16.241 3.75 15.8209 3.75 14.9809 3.75L4.78086 3.75C3.94078 3.75 3.52074 3.75 3.19987 3.91349C2.91763 4.0573 2.68816 4.28677 2.54435 4.56901C2.38086 4.88988 2.38086 5.30992 2.38086 6.15ZM14.7559 9C14.7559 9.20711 14.588 9.375 14.3809 9.375C14.1738 9.375 14.0059 9.20711 14.0059 9C14.0059 8.79289 14.1738 8.625 14.3809 8.625C14.588 8.625 14.7559 8.79289 14.7559 9ZM5.75586 9C5.75586 9.20711 5.58797 9.375 5.38086 9.375C5.17375 9.375 5.00586 9.20711 5.00586 9C5.00586 8.79289 5.17375 8.625 5.38086 8.625C5.58797 8.625 5.75586 8.79289 5.75586 9Z"
          className="stroke-current"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_866_11862">
          <rect
            width="18"
            height="18"
            fill="white"
            transform="translate(0.880859)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default BankNoteIcon;
