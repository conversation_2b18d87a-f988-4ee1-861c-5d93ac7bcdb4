const CircleDollarSignIcon = ({
  iconColor = '#F3F3F3',
}: {
  iconColor?: string;
}) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_483_6810)">
        <path
          d="M7.08317 12.2223C7.08317 13.2962 7.95373 14.1667 9.02761 14.1667H10.8332C11.9838 14.1667 12.9165 13.234 12.9165 12.0834C12.9165 10.9328 11.9838 10.0001 10.8332 10.0001H9.1665C8.01591 10.0001 7.08317 9.06734 7.08317 7.91675C7.08317 6.76615 8.01591 5.83341 9.1665 5.83341H10.9721C12.0459 5.83341 12.9165 6.70397 12.9165 7.77786M9.99984 4.58341V5.83341M9.99984 14.1667V15.4167M18.3332 10.0001C18.3332 14.6025 14.6022 18.3334 9.99984 18.3334C5.39746 18.3334 1.6665 14.6025 1.6665 10.0001C1.6665 5.39771 5.39746 1.66675 9.99984 1.66675C14.6022 1.66675 18.3332 5.39771 18.3332 10.0001Z"
          stroke={iconColor}
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_483_6810">
          <rect width="20" height="20" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default CircleDollarSignIcon;
