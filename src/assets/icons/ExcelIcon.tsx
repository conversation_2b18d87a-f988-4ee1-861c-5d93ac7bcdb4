const ExcelIcon = () => {
  return (
    <svg
      width="20"
      height="18"
      viewBox="0 0 20 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="5"
        y="0.25"
        width="15"
        height="17.5"
        rx="1.5"
        fill="#2FB776"
        className="fill:#2FB776;fill:color(display-p3 0.1843 0.7176 0.4627);fill-opacity:1;"
      />
      <path
        d="M5 13.375H20V16.25C20 17.0784 19.3284 17.75 18.5 17.75H6.5C5.67157 17.75 5 17.0784 5 16.25V13.375Z"
        fill="url(#paint0_linear_4442_1662)"
      />
      <rect
        x="12.5"
        y="9"
        width="7.5"
        height="4.375"
        fill="#229C5B"
        className="fill:#229C5B;fill:color(display-p3 0.1333 0.6118 0.3569);fill-opacity:1;"
      />
      <rect
        x="12.5"
        y="4.625"
        width="7.5"
        height="4.375"
        fill="#27AE68"
        className="fill:#27AE68;fill:color(display-p3 0.1529 0.6824 0.4078);fill-opacity:1;"
      />
      <path
        d="M5 1.75C5 0.921573 5.67157 0.25 6.5 0.25H12.5V4.625H5V1.75Z"
        fill="#1D854F"
        className="fill:#1D854F;fill:color(display-p3 0.1137 0.5216 0.3098);fill-opacity:1;"
      />
      <rect
        x="5"
        y="4.625"
        width="7.5"
        height="4.375"
        fill="#197B43"
        className="fill:#197B43;fill:color(display-p3 0.0980 0.4824 0.2627);fill-opacity:1;"
      />
      <rect
        x="5"
        y="9"
        width="7.5"
        height="4.375"
        fill="#1B5B38"
        className="fill:#1B5B38;fill:color(display-p3 0.1059 0.3569 0.2196);fill-opacity:1;"
      />
      <path
        d="M5 6.875C5 5.63236 6.00736 4.625 7.25 4.625H10.25C11.4926 4.625 12.5 5.63236 12.5 6.875V13.625C12.5 14.8676 11.4926 15.875 10.25 15.875H5V6.875Z"
        fill="black"
        fill-opacity="0.3"
        className="fill:black;fill-opacity:0.3;"
      />
      <rect
        y="3.375"
        width="11.25"
        height="11.25"
        rx="1.5"
        fill="url(#paint1_linear_4442_1662)"
      />
      <path
        d="M8.125 12.125L6.36383 8.9375L8.04768 5.875H6.67311L5.63359 7.83036L4.61125 5.875H3.19373L4.88617 8.9375L3.125 12.125H4.49957L5.60782 10.0536L6.70747 12.125H8.125Z"
        fill="white"
        className="fill:white;fill-opacity:1;"
      />
      <defs>
        <linearGradient
          id="paint0_linear_4442_1662"
          x1="5"
          y1="15.5625"
          x2="20"
          y2="15.5625"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            stopColor="#163C27"
            className="stopColor:#163C27;stopColor:color(display-p3 0.0863 0.2353 0.1529);stop-opacity:1;"
          />
          <stop
            offset="1"
            stopColor="#2A6043"
            className="stopColor:#2A6043;stopColor:color(display-p3 0.1647 0.3765 0.2627);stop-opacity:1;"
          />
        </linearGradient>
        <linearGradient
          id="paint1_linear_4442_1662"
          x1="0"
          y1="9"
          x2="11.25"
          y2="9"
          gradientUnits="userSpaceOnUse"
        >
          <stop
            stopColor="#185A30"
            className="stopColor:#185A30;stopColor:color(display-p3 0.0941 0.3529 0.1882);stop-opacity:1;"
          />
          <stop
            offset="1"
            stopColor="#176F3D"
            className="stopColor:#176F3D;stopColor:color(display-p3 0.0902 0.4353 0.2392);stop-opacity:1;"
          />
        </linearGradient>
      </defs>
    </svg>
  );
};
export default ExcelIcon;
