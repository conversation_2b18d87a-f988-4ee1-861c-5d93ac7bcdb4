const ReportsIcon = ({ iconColor = '#F3F3F3' }: { iconColor?: string }) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9.99984 13.3333V17.5M14.9998 17.5L11.707 14.756C11.0997 14.2499 10.796 13.9968 10.4571 13.9001C10.1582 13.8149 9.84147 13.8149 9.54262 13.9001C9.20365 13.9968 8.89999 14.2499 8.29268 14.756L4.99984 17.5M6.6665 9.16667V10M9.99984 7.5V10M13.3332 5.83333V10M18.3332 2.5H1.6665M2.49984 2.5H17.4998V9.33333C17.4998 10.7335 17.4998 11.4335 17.2274 11.9683C16.9877 12.4387 16.6052 12.8212 16.1348 13.0608C15.6 13.3333 14.9 13.3333 13.4998 13.3333H6.49984C5.09971 13.3333 4.39964 13.3333 3.86486 13.0608C3.39446 12.8212 3.012 12.4387 2.77232 11.9683C2.49984 11.4335 2.49984 10.7335 2.49984 9.33333V2.5Z"
        stroke={iconColor}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default ReportsIcon;
