const PurchaseOrderIcon = ({
  iconColor = '#F3F3F3',
}: {
  iconColor?: string;
}) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M4.6 2.19984L3.3 3.93317C3.04251 4.27648 2.91377 4.44814 2.91676 4.59183C2.91936 4.71687 2.97799 4.83413 3.07646 4.91123C3.18962 4.99984 3.40419 4.99984 3.83333 4.99984H16.1667C16.5958 4.99984 16.8104 4.99984 16.9235 4.91123C17.022 4.83413 17.0806 4.71687 17.0832 4.59183C17.0862 4.44814 16.9575 4.27648 16.7 3.93317L15.4 2.19984M4.6 2.19984C4.74667 2.00428 4.82 1.9065 4.91294 1.83598C4.99525 1.77352 5.08846 1.72692 5.18782 1.69854C5.3 1.6665 5.42222 1.6665 5.66667 1.6665H14.3333C14.5778 1.6665 14.7 1.6665 14.8122 1.69854C14.9115 1.72692 15.0047 1.77352 15.0871 1.83598C15.18 1.9065 15.2533 2.00428 15.4 2.19984M4.6 2.19984L3.03333 4.28872C2.83545 4.55257 2.73651 4.68449 2.66625 4.82977C2.6039 4.95869 2.55843 5.0951 2.53096 5.23564C2.5 5.39401 2.5 5.55892 2.5 5.88872L2.5 15.6665C2.5 16.5999 2.5 17.0666 2.68166 17.4232C2.84144 17.7368 3.09641 17.9917 3.41002 18.1515C3.76654 18.3332 4.23325 18.3332 5.16667 18.3332L14.8333 18.3332C15.7668 18.3332 16.2335 18.3332 16.59 18.1515C16.9036 17.9917 17.1586 17.7368 17.3183 17.4232C17.5 17.0666 17.5 16.5999 17.5 15.6665V5.88873C17.5 5.55892 17.5 5.39402 17.469 5.23564C17.4416 5.0951 17.3961 4.95869 17.3338 4.82977C17.2635 4.68449 17.1646 4.55257 16.9667 4.28873L15.4 2.19984M13.3333 8.33317C13.3333 9.21722 12.9821 10.0651 12.357 10.6902C11.7319 11.3153 10.8841 11.6665 10 11.6665C9.11594 11.6665 8.2681 11.3153 7.64298 10.6902C7.01786 10.0651 6.66667 9.21722 6.66667 8.33317"
        stroke={iconColor}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default PurchaseOrderIcon;
