const PathIcon = () => {
  return (
    <svg
      width="19"
      height="18"
      viewBox="0 0 19 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="text-[#767676] [.active_&]:text-[#491D97]"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.66723 5.09488C5.33879 4.76644 5.33879 4.23394 5.66723 3.90551C5.99566 3.57707 6.52816 3.57707 6.8566 3.90551C7.18504 4.23394 7.18504 4.76644 6.8566 5.09488C6.70491 5.24658 6.511 5.32808 6.31189 5.33975C6.29538 5.33866 6.27873 5.33811 6.26194 5.33811C6.24514 5.33811 6.22846 5.33866 6.21193 5.33975C6.01283 5.32808 5.81892 5.24658 5.66723 5.09488ZM4.60657 6.15554C4.86999 6.41897 5.18109 6.60654 5.51191 6.71817V9.00026V11.2824C5.18109 11.394 4.86999 11.5816 4.60657 11.845C3.69234 12.7592 3.69234 14.2415 4.60657 15.1557C5.52079 16.0699 7.00304 16.0699 7.91726 15.1557C8.83148 14.2415 8.83148 12.7592 7.91726 11.845C7.65383 11.5816 7.34273 11.394 7.01191 11.2824L7.01191 9.75026H10.262C11.7808 9.75026 13.012 8.51905 13.012 7.00026V6.62214C13.8857 6.31318 14.5117 5.47983 14.5117 4.50026C14.5117 3.25762 13.5044 2.25026 12.2617 2.25026C11.0191 2.25026 10.0117 3.25762 10.0117 4.50026C10.0117 5.48003 10.638 6.31353 11.512 6.62233V7.00026C11.512 7.69062 10.9523 8.25026 10.262 8.25026H7.01191L7.01191 6.71817C7.34273 6.60654 7.65383 6.41897 7.91726 6.15554C8.83148 5.24132 8.83148 3.75907 7.91726 2.84485C7.00304 1.93062 5.52079 1.93062 4.60657 2.84485C3.69234 3.75907 3.69234 5.24132 4.60657 6.15554ZM5.66723 14.095C5.33879 13.7666 5.33879 13.2341 5.66723 12.9056C5.83159 12.7413 6.04552 12.6593 6.26191 12.6593C6.47831 12.6593 6.69224 12.7413 6.8566 12.9056C7.18504 13.2341 7.18504 13.7666 6.8566 14.095C6.52816 14.4235 5.99566 14.4235 5.66723 14.095ZM11.5117 4.50026C11.5117 4.08605 11.8475 3.75026 12.2617 3.75026C12.6759 3.75026 13.0117 4.08605 13.0117 4.50026C13.0117 4.91448 12.6759 5.25026 12.2617 5.25026C11.8475 5.25026 11.5117 4.91448 11.5117 4.50026Z"
        className="fill-current"
      />
    </svg>
  );
};

export default PathIcon;
