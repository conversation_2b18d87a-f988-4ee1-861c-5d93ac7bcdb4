const UploadCompleteIcon = () => {
  return (
    <svg
      width="54"
      height="54"
      viewBox="0 0 54 54"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M19 31.2422C17.794 30.435 17 29.0602 17 27.5C17 25.1564 18.7915 23.2313 21.0797 23.0194C21.5478 20.1721 24.0202 18 27 18C29.9798 18 32.4522 20.1721 32.9203 23.0194C35.2085 23.2313 37 25.1564 37 27.5C37 29.0602 36.206 30.435 35 31.2422M23 31L27 27M27 27L31 31M27 27V36"
        stroke="#04BCC2"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M54 27C54 41.9117 41.9117 54 27 54C12.0883 54 0 41.9117 0 27C0 12.0883 12.0883 0 27 0C41.9117 0 54 12.0883 54 27ZM8.1 27C8.1 37.4382 16.5618 45.9 27 45.9C37.4382 45.9 45.9 37.4382 45.9 27C45.9 16.5618 37.4382 8.1 27 8.1C16.5618 8.1 8.1 16.5618 8.1 27Z"
        fill="#D4F1F2"
      />
      <path
        d="M27 54C22.5598 54 18.1882 52.905 14.2723 50.8119C10.3564 48.7188 7.01715 45.6923 4.55032 42.0004C2.08349 38.3085 0.565226 34.0653 0.130012 29.6465C-0.305201 25.2277 0.35607 20.7697 2.05525 16.6675C3.75443 12.5654 6.43908 8.94554 9.87138 6.12872C13.3037 3.3119 17.3777 1.38503 21.7326 0.518798C26.0874 -0.347438 30.5887 -0.126305 34.8377 1.16261C39.0867 2.45152 42.9522 4.76844 46.0919 7.90811L40.3643 13.6357C38.1665 11.4379 35.4607 9.81607 32.4864 8.91383C29.5121 8.01159 26.3612 7.85679 23.3128 8.46316C20.2644 9.06952 17.4126 10.4183 15.01 12.3901C12.6074 14.3619 10.7281 16.8958 9.53868 19.7673C8.34925 22.6388 7.88636 25.7594 8.19101 28.8525C8.49566 31.9457 9.55844 34.916 11.2852 37.5003C13.012 40.0846 15.3495 42.2032 18.0906 43.6683C20.8317 45.1335 23.8919 45.9 27 45.9L27 54Z"
        fill="#04BCC2"
      />
    </svg>
  );
};

export default UploadCompleteIcon;
