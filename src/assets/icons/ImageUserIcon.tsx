const ImageUserIcon = () => {
  return (
    <svg
      width="19"
      height="18"
      viewBox="0 0 19 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="text-[#767676] [.active_&]:text-[#491D97]"
    >
      <path
        d="M14.7266 6V1.5M12.4766 3.75H16.9766M16.9766 9V12.9C16.9766 14.1601 16.9766 14.7902 16.7313 15.2715C16.5156 15.6948 16.1714 16.039 15.748 16.2548C15.2667 16.5 14.6367 16.5 13.3766 16.5H5.57656C4.31644 16.5 3.68639 16.5 3.20508 16.2548C2.78172 16.039 2.43751 15.6948 2.2218 15.2715C1.97656 14.7902 1.97656 14.1601 1.97656 12.9V5.1C1.97656 3.83988 1.97656 3.20982 2.2218 2.72852C2.43751 2.30516 2.78172 1.96095 3.20508 1.74524C3.68639 1.5 4.31644 1.5 5.57656 1.5H9.47656M2.08587 14.9447C2.43772 13.6789 3.59866 12.75 4.97656 12.75H10.2266C10.9235 12.75 11.272 12.75 11.5618 12.8076C12.7519 13.0444 13.6822 13.9747 13.9189 15.1647C13.9766 15.4545 13.9766 15.803 13.9766 16.5M10.9766 7.125C10.9766 8.78185 9.63342 10.125 7.97656 10.125C6.31971 10.125 4.97656 8.78185 4.97656 7.125C4.97656 5.46815 6.31971 4.125 7.97656 4.125C9.63342 4.125 10.9766 5.46815 10.9766 7.125Z"
        className="stroke-current"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default ImageUserIcon;
