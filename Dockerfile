# Step 1: Use an official Node.js image as the base image
FROM node:18-alpine AS build

# Step 2: Set the working directory inside the container
WORKDIR /app

# Step 3: Copy the package.json and package-lock.json/yarn.lock files to the working directory
COPY package*.json ./

# Step 4: Install the project dependencies
RUN npm ci

# Step 5: Copy the rest of the project files to the working directory
COPY . .

# Step 6: Build the project for production
RUN npm run build

# Stage 2, based on Nginx, to have only the compiled app, ready for production with Nginx
FROM nginx:1.14.1-alpine

# Remove the default nginx configuration file
RUN rm -rf /etc/nginx/conf.d/default.conf

# Remove contents of /usr/share/nginx/html (if any)
RUN rm -rf /usr/share/nginx/html/*

# Set permissions for /usr/share/nginx/html directory
RUN chmod -R 755 /usr/share/nginx/html

# Set working directory to nginx asset directory
WORKDIR /usr/share/nginx/html

# Copy static assets from builder stage
COPY --from=build /app/dist /usr/share/nginx/html

# Copy custom Nginx configuration file
COPY ./nginx-custom.conf /etc/nginx/conf.d/default.conf

# Expose port 80
EXPOSE 80

# Containers run nginx with global directives and daemon off
CMD ["nginx", "-g", "daemon off;"]

